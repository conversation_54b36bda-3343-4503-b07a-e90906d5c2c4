// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/module/types/types.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_proto_2fmodule_2ftypes_2ftypes_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_proto_2fmodule_2ftypes_2ftypes_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_proto_2fmodule_2ftypes_2ftypes_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_proto_2fmodule_2ftypes_2ftypes_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[3]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2fmodule_2ftypes_2ftypes_2eproto;
namespace carbon {
namespace module {
namespace types {
class Empty;
struct EmptyDefaultTypeInternal;
extern EmptyDefaultTypeInternal _Empty_default_instance_;
class ModuleIPs;
struct ModuleIPsDefaultTypeInternal;
extern ModuleIPsDefaultTypeInternal _ModuleIPs_default_instance_;
class ModuleIdentity;
struct ModuleIdentityDefaultTypeInternal;
extern ModuleIdentityDefaultTypeInternal _ModuleIdentity_default_instance_;
}  // namespace types
}  // namespace module
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::module::types::Empty* Arena::CreateMaybeMessage<::carbon::module::types::Empty>(Arena*);
template<> ::carbon::module::types::ModuleIPs* Arena::CreateMaybeMessage<::carbon::module::types::ModuleIPs>(Arena*);
template<> ::carbon::module::types::ModuleIdentity* Arena::CreateMaybeMessage<::carbon::module::types::ModuleIdentity>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace module {
namespace types {

// ===================================================================

class Empty final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.module.types.Empty) */ {
 public:
  inline Empty() : Empty(nullptr) {}
  explicit constexpr Empty(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Empty(const Empty& from);
  Empty(Empty&& from) noexcept
    : Empty() {
    *this = ::std::move(from);
  }

  inline Empty& operator=(const Empty& from) {
    CopyFrom(from);
    return *this;
  }
  inline Empty& operator=(Empty&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Empty& default_instance() {
    return *internal_default_instance();
  }
  static inline const Empty* internal_default_instance() {
    return reinterpret_cast<const Empty*>(
               &_Empty_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Empty& a, Empty& b) {
    a.Swap(&b);
  }
  inline void Swap(Empty* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Empty* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Empty* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Empty>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const Empty& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const Empty& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.module.types.Empty";
  }
  protected:
  explicit Empty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.module.types.Empty)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmodule_2ftypes_2ftypes_2eproto;
};
// -------------------------------------------------------------------

class ModuleIdentity final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.module.types.ModuleIdentity) */ {
 public:
  inline ModuleIdentity() : ModuleIdentity(nullptr) {}
  ~ModuleIdentity() override;
  explicit constexpr ModuleIdentity(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModuleIdentity(const ModuleIdentity& from);
  ModuleIdentity(ModuleIdentity&& from) noexcept
    : ModuleIdentity() {
    *this = ::std::move(from);
  }

  inline ModuleIdentity& operator=(const ModuleIdentity& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModuleIdentity& operator=(ModuleIdentity&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModuleIdentity& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModuleIdentity* internal_default_instance() {
    return reinterpret_cast<const ModuleIdentity*>(
               &_ModuleIdentity_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ModuleIdentity& a, ModuleIdentity& b) {
    a.Swap(&b);
  }
  inline void Swap(ModuleIdentity* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModuleIdentity* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModuleIdentity* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModuleIdentity>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModuleIdentity& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ModuleIdentity& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModuleIdentity* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.module.types.ModuleIdentity";
  }
  protected:
  explicit ModuleIdentity(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSerialFieldNumber = 2,
    kIdFieldNumber = 1,
  };
  // string serial = 2;
  void clear_serial();
  const std::string& serial() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_serial(ArgT0&& arg0, ArgT... args);
  std::string* mutable_serial();
  PROTOBUF_NODISCARD std::string* release_serial();
  void set_allocated_serial(std::string* serial);
  private:
  const std::string& _internal_serial() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_serial(const std::string& value);
  std::string* _internal_mutable_serial();
  public:

  // uint32 id = 1;
  void clear_id();
  uint32_t id() const;
  void set_id(uint32_t value);
  private:
  uint32_t _internal_id() const;
  void _internal_set_id(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.module.types.ModuleIdentity)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr serial_;
  uint32_t id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmodule_2ftypes_2ftypes_2eproto;
};
// -------------------------------------------------------------------

class ModuleIPs final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.module.types.ModuleIPs) */ {
 public:
  inline ModuleIPs() : ModuleIPs(nullptr) {}
  ~ModuleIPs() override;
  explicit constexpr ModuleIPs(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModuleIPs(const ModuleIPs& from);
  ModuleIPs(ModuleIPs&& from) noexcept
    : ModuleIPs() {
    *this = ::std::move(from);
  }

  inline ModuleIPs& operator=(const ModuleIPs& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModuleIPs& operator=(ModuleIPs&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModuleIPs& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModuleIPs* internal_default_instance() {
    return reinterpret_cast<const ModuleIPs*>(
               &_ModuleIPs_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ModuleIPs& a, ModuleIPs& b) {
    a.Swap(&b);
  }
  inline void Swap(ModuleIPs* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModuleIPs* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModuleIPs* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModuleIPs>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModuleIPs& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ModuleIPs& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModuleIPs* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.module.types.ModuleIPs";
  }
  protected:
  explicit ModuleIPs(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMcbIpFieldNumber = 1,
    kPcIpFieldNumber = 2,
    kIpmiIpFieldNumber = 3,
  };
  // string mcb_ip = 1;
  void clear_mcb_ip();
  const std::string& mcb_ip() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_mcb_ip(ArgT0&& arg0, ArgT... args);
  std::string* mutable_mcb_ip();
  PROTOBUF_NODISCARD std::string* release_mcb_ip();
  void set_allocated_mcb_ip(std::string* mcb_ip);
  private:
  const std::string& _internal_mcb_ip() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_mcb_ip(const std::string& value);
  std::string* _internal_mutable_mcb_ip();
  public:

  // string pc_ip = 2;
  void clear_pc_ip();
  const std::string& pc_ip() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_pc_ip(ArgT0&& arg0, ArgT... args);
  std::string* mutable_pc_ip();
  PROTOBUF_NODISCARD std::string* release_pc_ip();
  void set_allocated_pc_ip(std::string* pc_ip);
  private:
  const std::string& _internal_pc_ip() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_pc_ip(const std::string& value);
  std::string* _internal_mutable_pc_ip();
  public:

  // string ipmi_ip = 3;
  void clear_ipmi_ip();
  const std::string& ipmi_ip() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_ipmi_ip(ArgT0&& arg0, ArgT... args);
  std::string* mutable_ipmi_ip();
  PROTOBUF_NODISCARD std::string* release_ipmi_ip();
  void set_allocated_ipmi_ip(std::string* ipmi_ip);
  private:
  const std::string& _internal_ipmi_ip() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_ipmi_ip(const std::string& value);
  std::string* _internal_mutable_ipmi_ip();
  public:

  // @@protoc_insertion_point(class_scope:carbon.module.types.ModuleIPs)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr mcb_ip_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr pc_ip_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr ipmi_ip_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmodule_2ftypes_2ftypes_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Empty

// -------------------------------------------------------------------

// ModuleIdentity

// uint32 id = 1;
inline void ModuleIdentity::clear_id() {
  id_ = 0u;
}
inline uint32_t ModuleIdentity::_internal_id() const {
  return id_;
}
inline uint32_t ModuleIdentity::id() const {
  // @@protoc_insertion_point(field_get:carbon.module.types.ModuleIdentity.id)
  return _internal_id();
}
inline void ModuleIdentity::_internal_set_id(uint32_t value) {
  
  id_ = value;
}
inline void ModuleIdentity::set_id(uint32_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:carbon.module.types.ModuleIdentity.id)
}

// string serial = 2;
inline void ModuleIdentity::clear_serial() {
  serial_.ClearToEmpty();
}
inline const std::string& ModuleIdentity::serial() const {
  // @@protoc_insertion_point(field_get:carbon.module.types.ModuleIdentity.serial)
  return _internal_serial();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModuleIdentity::set_serial(ArgT0&& arg0, ArgT... args) {
 
 serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.module.types.ModuleIdentity.serial)
}
inline std::string* ModuleIdentity::mutable_serial() {
  std::string* _s = _internal_mutable_serial();
  // @@protoc_insertion_point(field_mutable:carbon.module.types.ModuleIdentity.serial)
  return _s;
}
inline const std::string& ModuleIdentity::_internal_serial() const {
  return serial_.Get();
}
inline void ModuleIdentity::_internal_set_serial(const std::string& value) {
  
  serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModuleIdentity::_internal_mutable_serial() {
  
  return serial_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModuleIdentity::release_serial() {
  // @@protoc_insertion_point(field_release:carbon.module.types.ModuleIdentity.serial)
  return serial_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModuleIdentity::set_allocated_serial(std::string* serial) {
  if (serial != nullptr) {
    
  } else {
    
  }
  serial_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), serial,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (serial_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.module.types.ModuleIdentity.serial)
}

// -------------------------------------------------------------------

// ModuleIPs

// string mcb_ip = 1;
inline void ModuleIPs::clear_mcb_ip() {
  mcb_ip_.ClearToEmpty();
}
inline const std::string& ModuleIPs::mcb_ip() const {
  // @@protoc_insertion_point(field_get:carbon.module.types.ModuleIPs.mcb_ip)
  return _internal_mcb_ip();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModuleIPs::set_mcb_ip(ArgT0&& arg0, ArgT... args) {
 
 mcb_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.module.types.ModuleIPs.mcb_ip)
}
inline std::string* ModuleIPs::mutable_mcb_ip() {
  std::string* _s = _internal_mutable_mcb_ip();
  // @@protoc_insertion_point(field_mutable:carbon.module.types.ModuleIPs.mcb_ip)
  return _s;
}
inline const std::string& ModuleIPs::_internal_mcb_ip() const {
  return mcb_ip_.Get();
}
inline void ModuleIPs::_internal_set_mcb_ip(const std::string& value) {
  
  mcb_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModuleIPs::_internal_mutable_mcb_ip() {
  
  return mcb_ip_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModuleIPs::release_mcb_ip() {
  // @@protoc_insertion_point(field_release:carbon.module.types.ModuleIPs.mcb_ip)
  return mcb_ip_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModuleIPs::set_allocated_mcb_ip(std::string* mcb_ip) {
  if (mcb_ip != nullptr) {
    
  } else {
    
  }
  mcb_ip_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), mcb_ip,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (mcb_ip_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    mcb_ip_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.module.types.ModuleIPs.mcb_ip)
}

// string pc_ip = 2;
inline void ModuleIPs::clear_pc_ip() {
  pc_ip_.ClearToEmpty();
}
inline const std::string& ModuleIPs::pc_ip() const {
  // @@protoc_insertion_point(field_get:carbon.module.types.ModuleIPs.pc_ip)
  return _internal_pc_ip();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModuleIPs::set_pc_ip(ArgT0&& arg0, ArgT... args) {
 
 pc_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.module.types.ModuleIPs.pc_ip)
}
inline std::string* ModuleIPs::mutable_pc_ip() {
  std::string* _s = _internal_mutable_pc_ip();
  // @@protoc_insertion_point(field_mutable:carbon.module.types.ModuleIPs.pc_ip)
  return _s;
}
inline const std::string& ModuleIPs::_internal_pc_ip() const {
  return pc_ip_.Get();
}
inline void ModuleIPs::_internal_set_pc_ip(const std::string& value) {
  
  pc_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModuleIPs::_internal_mutable_pc_ip() {
  
  return pc_ip_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModuleIPs::release_pc_ip() {
  // @@protoc_insertion_point(field_release:carbon.module.types.ModuleIPs.pc_ip)
  return pc_ip_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModuleIPs::set_allocated_pc_ip(std::string* pc_ip) {
  if (pc_ip != nullptr) {
    
  } else {
    
  }
  pc_ip_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), pc_ip,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (pc_ip_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    pc_ip_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.module.types.ModuleIPs.pc_ip)
}

// string ipmi_ip = 3;
inline void ModuleIPs::clear_ipmi_ip() {
  ipmi_ip_.ClearToEmpty();
}
inline const std::string& ModuleIPs::ipmi_ip() const {
  // @@protoc_insertion_point(field_get:carbon.module.types.ModuleIPs.ipmi_ip)
  return _internal_ipmi_ip();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModuleIPs::set_ipmi_ip(ArgT0&& arg0, ArgT... args) {
 
 ipmi_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.module.types.ModuleIPs.ipmi_ip)
}
inline std::string* ModuleIPs::mutable_ipmi_ip() {
  std::string* _s = _internal_mutable_ipmi_ip();
  // @@protoc_insertion_point(field_mutable:carbon.module.types.ModuleIPs.ipmi_ip)
  return _s;
}
inline const std::string& ModuleIPs::_internal_ipmi_ip() const {
  return ipmi_ip_.Get();
}
inline void ModuleIPs::_internal_set_ipmi_ip(const std::string& value) {
  
  ipmi_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModuleIPs::_internal_mutable_ipmi_ip() {
  
  return ipmi_ip_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModuleIPs::release_ipmi_ip() {
  // @@protoc_insertion_point(field_release:carbon.module.types.ModuleIPs.ipmi_ip)
  return ipmi_ip_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModuleIPs::set_allocated_ipmi_ip(std::string* ipmi_ip) {
  if (ipmi_ip != nullptr) {
    
  } else {
    
  }
  ipmi_ip_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ipmi_ip,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (ipmi_ip_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    ipmi_ip_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.module.types.ModuleIPs.ipmi_ip)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace types
}  // namespace module
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_proto_2fmodule_2ftypes_2ftypes_2eproto

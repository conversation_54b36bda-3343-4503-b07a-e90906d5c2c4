# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.proto.module.orchestrator import orchestrator_pb2 as proto_dot_module_dot_orchestrator_dot_orchestrator__pb2
from generated.proto.module.types import types_pb2 as proto_dot_module_dot_types_dot_types__pb2


class ModuleOrchestratorServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Heartbeat = channel.unary_unary(
                '/carbon.module.orchestrator.ModuleOrchestratorService/Heartbeat',
                request_serializer=proto_dot_module_dot_orchestrator_dot_orchestrator__pb2.HeartbeatRequest.SerializeToString,
                response_deserializer=proto_dot_module_dot_types_dot_types__pb2.Empty.FromString,
                )


class ModuleOrchestratorServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Heartbeat(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ModuleOrchestratorServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Heartbeat': grpc.unary_unary_rpc_method_handler(
                    servicer.Heartbeat,
                    request_deserializer=proto_dot_module_dot_orchestrator_dot_orchestrator__pb2.HeartbeatRequest.FromString,
                    response_serializer=proto_dot_module_dot_types_dot_types__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.module.orchestrator.ModuleOrchestratorService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class ModuleOrchestratorService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Heartbeat(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.module.orchestrator.ModuleOrchestratorService/Heartbeat',
            proto_dot_module_dot_orchestrator_dot_orchestrator__pb2.HeartbeatRequest.SerializeToString,
            proto_dot_module_dot_types_dot_types__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

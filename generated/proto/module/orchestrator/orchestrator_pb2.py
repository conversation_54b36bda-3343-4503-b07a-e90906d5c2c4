# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/module/orchestrator/orchestrator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.proto.module.types import types_pb2 as proto_dot_module_dot_types_dot_types__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/module/orchestrator/orchestrator.proto',
  package='carbon.module.orchestrator',
  syntax='proto3',
  serialized_options=b'Z\031proto/module/orchestrator',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n,proto/module/orchestrator/orchestrator.proto\x12\x1a\x63\x61rbon.module.orchestrator\x1a\x1eproto/module/types/types.proto\"}\n\x10HeartbeatRequest\x12\x35\n\x08identity\x18\x01 \x01(\x0b\x32#.carbon.module.types.ModuleIdentity\x12\x32\n\nmodule_ips\x18\x02 \x01(\x0b\x32\x1e.carbon.module.types.ModuleIPs2r\n\x19ModuleOrchestratorService\x12U\n\tHeartbeat\x12,.carbon.module.orchestrator.HeartbeatRequest\x1a\x1a.carbon.module.types.EmptyB\x1bZ\x19proto/module/orchestratorb\x06proto3'
  ,
  dependencies=[proto_dot_module_dot_types_dot_types__pb2.DESCRIPTOR,])




_HEARTBEATREQUEST = _descriptor.Descriptor(
  name='HeartbeatRequest',
  full_name='carbon.module.orchestrator.HeartbeatRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='identity', full_name='carbon.module.orchestrator.HeartbeatRequest.identity', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='module_ips', full_name='carbon.module.orchestrator.HeartbeatRequest.module_ips', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=108,
  serialized_end=233,
)

_HEARTBEATREQUEST.fields_by_name['identity'].message_type = proto_dot_module_dot_types_dot_types__pb2._MODULEIDENTITY
_HEARTBEATREQUEST.fields_by_name['module_ips'].message_type = proto_dot_module_dot_types_dot_types__pb2._MODULEIPS
DESCRIPTOR.message_types_by_name['HeartbeatRequest'] = _HEARTBEATREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

HeartbeatRequest = _reflection.GeneratedProtocolMessageType('HeartbeatRequest', (_message.Message,), {
  'DESCRIPTOR' : _HEARTBEATREQUEST,
  '__module__' : 'proto.module.orchestrator.orchestrator_pb2'
  # @@protoc_insertion_point(class_scope:carbon.module.orchestrator.HeartbeatRequest)
  })
_sym_db.RegisterMessage(HeartbeatRequest)


DESCRIPTOR._options = None

_MODULEORCHESTRATORSERVICE = _descriptor.ServiceDescriptor(
  name='ModuleOrchestratorService',
  full_name='carbon.module.orchestrator.ModuleOrchestratorService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=235,
  serialized_end=349,
  methods=[
  _descriptor.MethodDescriptor(
    name='Heartbeat',
    full_name='carbon.module.orchestrator.ModuleOrchestratorService.Heartbeat',
    index=0,
    containing_service=None,
    input_type=_HEARTBEATREQUEST,
    output_type=proto_dot_module_dot_types_dot_types__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_MODULEORCHESTRATORSERVICE)

DESCRIPTOR.services_by_name['ModuleOrchestratorService'] = _MODULEORCHESTRATORSERVICE

# @@protoc_insertion_point(module_scope)

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from generated.proto.module.types.types_pb2 import (
    ModuleIPs as proto___module___types___types_pb2___ModuleIPs,
    ModuleIdentity as proto___module___types___types_pb2___ModuleIdentity,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class HeartbeatRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def identity(self) -> proto___module___types___types_pb2___ModuleIdentity: ...

    @property
    def module_ips(self) -> proto___module___types___types_pb2___ModuleIPs: ...

    def __init__(self,
        *,
        identity : typing___Optional[proto___module___types___types_pb2___ModuleIdentity] = None,
        module_ips : typing___Optional[proto___module___types___types_pb2___ModuleIPs] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"identity",b"identity",u"module_ips",b"module_ips"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"identity",b"identity",u"module_ips",b"module_ips"]) -> None: ...
type___HeartbeatRequest = HeartbeatRequest

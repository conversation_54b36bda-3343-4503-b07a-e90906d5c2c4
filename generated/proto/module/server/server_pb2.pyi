"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from generated.proto.module.types.types_pb2 import (
    ModuleIdentity as proto___module___types___types_pb2___ModuleIdentity,
)

from typing import (
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class GetModuleIdentityResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def identity(self) -> proto___module___types___types_pb2___ModuleIdentity: ...

    def __init__(self,
        *,
        identity : typing___Optional[proto___module___types___types_pb2___ModuleIdentity] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"identity",b"identity"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"identity",b"identity"]) -> None: ...
type___GetModuleIdentityResponse = GetModuleIdentityResponse

class SetModuleSerialNumberRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    serial_number: typing___Text = ...
    force: builtin___bool = ...

    def __init__(self,
        *,
        serial_number : typing___Optional[typing___Text] = None,
        force : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"force",b"force",u"serial_number",b"serial_number"]) -> None: ...
type___SetModuleSerialNumberRequest = SetModuleSerialNumberRequest

class SetModuleIdentityRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def identity(self) -> proto___module___types___types_pb2___ModuleIdentity: ...

    def __init__(self,
        *,
        identity : typing___Optional[proto___module___types___types_pb2___ModuleIdentity] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"identity",b"identity"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"identity",b"identity"]) -> None: ...
type___SetModuleIdentityRequest = SetModuleIdentityRequest

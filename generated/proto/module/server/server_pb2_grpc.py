# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.hardware_manager.proto import hardware_manager_service_pb2 as hardware__manager_dot_proto_dot_hardware__manager__service__pb2
from generated.proto.module.server import server_pb2 as proto_dot_module_dot_server_dot_server__pb2
from generated.proto.module.types import types_pb2 as proto_dot_module_dot_types_dot_types__pb2


class ModuleServerServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetModuleIdentity = channel.unary_unary(
                '/carbon.module.server.ModuleServerService/GetModuleIdentity',
                request_serializer=proto_dot_module_dot_types_dot_types__pb2.Empty.SerializeToString,
                response_deserializer=proto_dot_module_dot_server_dot_server__pb2.GetModuleIdentityResponse.FromString,
                )
        self.SetModuleSerialNumber = channel.unary_unary(
                '/carbon.module.server.ModuleServerService/SetModuleSerialNumber',
                request_serializer=proto_dot_module_dot_server_dot_server__pb2.SetModuleSerialNumberRequest.SerializeToString,
                response_deserializer=proto_dot_module_dot_types_dot_types__pb2.Empty.FromString,
                )
        self.SetModuleIdentity = channel.unary_unary(
                '/carbon.module.server.ModuleServerService/SetModuleIdentity',
                request_serializer=proto_dot_module_dot_server_dot_server__pb2.SetModuleIdentityRequest.SerializeToString,
                response_deserializer=proto_dot_module_dot_types_dot_types__pb2.Empty.FromString,
                )
        self.GetModuleSensors = channel.unary_unary(
                '/carbon.module.server.ModuleServerService/GetModuleSensors',
                request_serializer=proto_dot_module_dot_types_dot_types__pb2.Empty.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.ReaperPcSensorData.FromString,
                )


class ModuleServerServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetModuleIdentity(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetModuleSerialNumber(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetModuleIdentity(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetModuleSensors(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ModuleServerServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetModuleIdentity': grpc.unary_unary_rpc_method_handler(
                    servicer.GetModuleIdentity,
                    request_deserializer=proto_dot_module_dot_types_dot_types__pb2.Empty.FromString,
                    response_serializer=proto_dot_module_dot_server_dot_server__pb2.GetModuleIdentityResponse.SerializeToString,
            ),
            'SetModuleSerialNumber': grpc.unary_unary_rpc_method_handler(
                    servicer.SetModuleSerialNumber,
                    request_deserializer=proto_dot_module_dot_server_dot_server__pb2.SetModuleSerialNumberRequest.FromString,
                    response_serializer=proto_dot_module_dot_types_dot_types__pb2.Empty.SerializeToString,
            ),
            'SetModuleIdentity': grpc.unary_unary_rpc_method_handler(
                    servicer.SetModuleIdentity,
                    request_deserializer=proto_dot_module_dot_server_dot_server__pb2.SetModuleIdentityRequest.FromString,
                    response_serializer=proto_dot_module_dot_types_dot_types__pb2.Empty.SerializeToString,
            ),
            'GetModuleSensors': grpc.unary_unary_rpc_method_handler(
                    servicer.GetModuleSensors,
                    request_deserializer=proto_dot_module_dot_types_dot_types__pb2.Empty.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.ReaperPcSensorData.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.module.server.ModuleServerService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class ModuleServerService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetModuleIdentity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.module.server.ModuleServerService/GetModuleIdentity',
            proto_dot_module_dot_types_dot_types__pb2.Empty.SerializeToString,
            proto_dot_module_dot_server_dot_server__pb2.GetModuleIdentityResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetModuleSerialNumber(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.module.server.ModuleServerService/SetModuleSerialNumber',
            proto_dot_module_dot_server_dot_server__pb2.SetModuleSerialNumberRequest.SerializeToString,
            proto_dot_module_dot_types_dot_types__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetModuleIdentity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.module.server.ModuleServerService/SetModuleIdentity',
            proto_dot_module_dot_server_dot_server__pb2.SetModuleIdentityRequest.SerializeToString,
            proto_dot_module_dot_types_dot_types__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetModuleSensors(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.module.server.ModuleServerService/GetModuleSensors',
            proto_dot_module_dot_types_dot_types__pb2.Empty.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.ReaperPcSensorData.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

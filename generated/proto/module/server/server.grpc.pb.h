// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: proto/module/server/server.proto
#ifndef GRPC_proto_2fmodule_2fserver_2fserver_2eproto__INCLUDED
#define GRPC_proto_2fmodule_2fserver_2fserver_2eproto__INCLUDED

#include "proto/module/server/server.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace module {
namespace server {

class ModuleServerService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.module.server.ModuleServerService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::carbon::module::server::GetModuleIdentityResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::module::server::GetModuleIdentityResponse>> AsyncGetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::module::server::GetModuleIdentityResponse>>(AsyncGetModuleIdentityRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::module::server::GetModuleIdentityResponse>> PrepareAsyncGetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::module::server::GetModuleIdentityResponse>>(PrepareAsyncGetModuleIdentityRaw(context, request, cq));
    }
    virtual ::grpc::Status SetModuleSerialNumber(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest& request, ::carbon::module::types::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::module::types::Empty>> AsyncSetModuleSerialNumber(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::module::types::Empty>>(AsyncSetModuleSerialNumberRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::module::types::Empty>> PrepareAsyncSetModuleSerialNumber(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::module::types::Empty>>(PrepareAsyncSetModuleSerialNumberRaw(context, request, cq));
    }
    virtual ::grpc::Status SetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleIdentityRequest& request, ::carbon::module::types::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::module::types::Empty>> AsyncSetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleIdentityRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::module::types::Empty>>(AsyncSetModuleIdentityRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::module::types::Empty>> PrepareAsyncSetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleIdentityRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::module::types::Empty>>(PrepareAsyncSetModuleIdentityRaw(context, request, cq));
    }
    virtual ::grpc::Status GetModuleSensors(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::hardware_manager::ReaperPcSensorData* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::hardware_manager::ReaperPcSensorData>> AsyncGetModuleSensors(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::hardware_manager::ReaperPcSensorData>>(AsyncGetModuleSensorsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::hardware_manager::ReaperPcSensorData>> PrepareAsyncGetModuleSensors(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::hardware_manager::ReaperPcSensorData>>(PrepareAsyncGetModuleSensorsRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::types::Empty* request, ::carbon::module::server::GetModuleIdentityResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::types::Empty* request, ::carbon::module::server::GetModuleIdentityResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetModuleSerialNumber(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest* request, ::carbon::module::types::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetModuleSerialNumber(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest* request, ::carbon::module::types::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleIdentityRequest* request, ::carbon::module::types::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleIdentityRequest* request, ::carbon::module::types::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetModuleSensors(::grpc::ClientContext* context, const ::carbon::module::types::Empty* request, ::hardware_manager::ReaperPcSensorData* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetModuleSensors(::grpc::ClientContext* context, const ::carbon::module::types::Empty* request, ::hardware_manager::ReaperPcSensorData* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::module::server::GetModuleIdentityResponse>* AsyncGetModuleIdentityRaw(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::module::server::GetModuleIdentityResponse>* PrepareAsyncGetModuleIdentityRaw(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::module::types::Empty>* AsyncSetModuleSerialNumberRaw(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::module::types::Empty>* PrepareAsyncSetModuleSerialNumberRaw(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::module::types::Empty>* AsyncSetModuleIdentityRaw(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleIdentityRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::module::types::Empty>* PrepareAsyncSetModuleIdentityRaw(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleIdentityRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::hardware_manager::ReaperPcSensorData>* AsyncGetModuleSensorsRaw(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::hardware_manager::ReaperPcSensorData>* PrepareAsyncGetModuleSensorsRaw(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::carbon::module::server::GetModuleIdentityResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::module::server::GetModuleIdentityResponse>> AsyncGetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::module::server::GetModuleIdentityResponse>>(AsyncGetModuleIdentityRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::module::server::GetModuleIdentityResponse>> PrepareAsyncGetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::module::server::GetModuleIdentityResponse>>(PrepareAsyncGetModuleIdentityRaw(context, request, cq));
    }
    ::grpc::Status SetModuleSerialNumber(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest& request, ::carbon::module::types::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::module::types::Empty>> AsyncSetModuleSerialNumber(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::module::types::Empty>>(AsyncSetModuleSerialNumberRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::module::types::Empty>> PrepareAsyncSetModuleSerialNumber(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::module::types::Empty>>(PrepareAsyncSetModuleSerialNumberRaw(context, request, cq));
    }
    ::grpc::Status SetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleIdentityRequest& request, ::carbon::module::types::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::module::types::Empty>> AsyncSetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleIdentityRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::module::types::Empty>>(AsyncSetModuleIdentityRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::module::types::Empty>> PrepareAsyncSetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleIdentityRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::module::types::Empty>>(PrepareAsyncSetModuleIdentityRaw(context, request, cq));
    }
    ::grpc::Status GetModuleSensors(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::hardware_manager::ReaperPcSensorData* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::hardware_manager::ReaperPcSensorData>> AsyncGetModuleSensors(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::hardware_manager::ReaperPcSensorData>>(AsyncGetModuleSensorsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::hardware_manager::ReaperPcSensorData>> PrepareAsyncGetModuleSensors(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::hardware_manager::ReaperPcSensorData>>(PrepareAsyncGetModuleSensorsRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::types::Empty* request, ::carbon::module::server::GetModuleIdentityResponse* response, std::function<void(::grpc::Status)>) override;
      void GetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::types::Empty* request, ::carbon::module::server::GetModuleIdentityResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetModuleSerialNumber(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest* request, ::carbon::module::types::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetModuleSerialNumber(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest* request, ::carbon::module::types::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleIdentityRequest* request, ::carbon::module::types::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleIdentityRequest* request, ::carbon::module::types::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetModuleSensors(::grpc::ClientContext* context, const ::carbon::module::types::Empty* request, ::hardware_manager::ReaperPcSensorData* response, std::function<void(::grpc::Status)>) override;
      void GetModuleSensors(::grpc::ClientContext* context, const ::carbon::module::types::Empty* request, ::hardware_manager::ReaperPcSensorData* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::module::server::GetModuleIdentityResponse>* AsyncGetModuleIdentityRaw(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::module::server::GetModuleIdentityResponse>* PrepareAsyncGetModuleIdentityRaw(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::module::types::Empty>* AsyncSetModuleSerialNumberRaw(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::module::types::Empty>* PrepareAsyncSetModuleSerialNumberRaw(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::module::types::Empty>* AsyncSetModuleIdentityRaw(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleIdentityRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::module::types::Empty>* PrepareAsyncSetModuleIdentityRaw(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleIdentityRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::hardware_manager::ReaperPcSensorData>* AsyncGetModuleSensorsRaw(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::hardware_manager::ReaperPcSensorData>* PrepareAsyncGetModuleSensorsRaw(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetModuleIdentity_;
    const ::grpc::internal::RpcMethod rpcmethod_SetModuleSerialNumber_;
    const ::grpc::internal::RpcMethod rpcmethod_SetModuleIdentity_;
    const ::grpc::internal::RpcMethod rpcmethod_GetModuleSensors_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetModuleIdentity(::grpc::ServerContext* context, const ::carbon::module::types::Empty* request, ::carbon::module::server::GetModuleIdentityResponse* response);
    virtual ::grpc::Status SetModuleSerialNumber(::grpc::ServerContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest* request, ::carbon::module::types::Empty* response);
    virtual ::grpc::Status SetModuleIdentity(::grpc::ServerContext* context, const ::carbon::module::server::SetModuleIdentityRequest* request, ::carbon::module::types::Empty* response);
    virtual ::grpc::Status GetModuleSensors(::grpc::ServerContext* context, const ::carbon::module::types::Empty* request, ::hardware_manager::ReaperPcSensorData* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetModuleIdentity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetModuleIdentity() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetModuleIdentity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModuleIdentity(::grpc::ServerContext* /*context*/, const ::carbon::module::types::Empty* /*request*/, ::carbon::module::server::GetModuleIdentityResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetModuleIdentity(::grpc::ServerContext* context, ::carbon::module::types::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::module::server::GetModuleIdentityResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetModuleSerialNumber : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetModuleSerialNumber() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_SetModuleSerialNumber() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModuleSerialNumber(::grpc::ServerContext* /*context*/, const ::carbon::module::server::SetModuleSerialNumberRequest* /*request*/, ::carbon::module::types::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetModuleSerialNumber(::grpc::ServerContext* context, ::carbon::module::server::SetModuleSerialNumberRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::module::types::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetModuleIdentity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetModuleIdentity() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_SetModuleIdentity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModuleIdentity(::grpc::ServerContext* /*context*/, const ::carbon::module::server::SetModuleIdentityRequest* /*request*/, ::carbon::module::types::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetModuleIdentity(::grpc::ServerContext* context, ::carbon::module::server::SetModuleIdentityRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::module::types::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetModuleSensors : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetModuleSensors() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_GetModuleSensors() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModuleSensors(::grpc::ServerContext* /*context*/, const ::carbon::module::types::Empty* /*request*/, ::hardware_manager::ReaperPcSensorData* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetModuleSensors(::grpc::ServerContext* context, ::carbon::module::types::Empty* request, ::grpc::ServerAsyncResponseWriter< ::hardware_manager::ReaperPcSensorData>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetModuleIdentity<WithAsyncMethod_SetModuleSerialNumber<WithAsyncMethod_SetModuleIdentity<WithAsyncMethod_GetModuleSensors<Service > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetModuleIdentity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetModuleIdentity() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::module::types::Empty, ::carbon::module::server::GetModuleIdentityResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::module::types::Empty* request, ::carbon::module::server::GetModuleIdentityResponse* response) { return this->GetModuleIdentity(context, request, response); }));}
    void SetMessageAllocatorFor_GetModuleIdentity(
        ::grpc::MessageAllocator< ::carbon::module::types::Empty, ::carbon::module::server::GetModuleIdentityResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::module::types::Empty, ::carbon::module::server::GetModuleIdentityResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetModuleIdentity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModuleIdentity(::grpc::ServerContext* /*context*/, const ::carbon::module::types::Empty* /*request*/, ::carbon::module::server::GetModuleIdentityResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetModuleIdentity(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::module::types::Empty* /*request*/, ::carbon::module::server::GetModuleIdentityResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetModuleSerialNumber : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetModuleSerialNumber() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::module::server::SetModuleSerialNumberRequest, ::carbon::module::types::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest* request, ::carbon::module::types::Empty* response) { return this->SetModuleSerialNumber(context, request, response); }));}
    void SetMessageAllocatorFor_SetModuleSerialNumber(
        ::grpc::MessageAllocator< ::carbon::module::server::SetModuleSerialNumberRequest, ::carbon::module::types::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::module::server::SetModuleSerialNumberRequest, ::carbon::module::types::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetModuleSerialNumber() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModuleSerialNumber(::grpc::ServerContext* /*context*/, const ::carbon::module::server::SetModuleSerialNumberRequest* /*request*/, ::carbon::module::types::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetModuleSerialNumber(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::module::server::SetModuleSerialNumberRequest* /*request*/, ::carbon::module::types::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetModuleIdentity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetModuleIdentity() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::module::server::SetModuleIdentityRequest, ::carbon::module::types::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::module::server::SetModuleIdentityRequest* request, ::carbon::module::types::Empty* response) { return this->SetModuleIdentity(context, request, response); }));}
    void SetMessageAllocatorFor_SetModuleIdentity(
        ::grpc::MessageAllocator< ::carbon::module::server::SetModuleIdentityRequest, ::carbon::module::types::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::module::server::SetModuleIdentityRequest, ::carbon::module::types::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetModuleIdentity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModuleIdentity(::grpc::ServerContext* /*context*/, const ::carbon::module::server::SetModuleIdentityRequest* /*request*/, ::carbon::module::types::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetModuleIdentity(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::module::server::SetModuleIdentityRequest* /*request*/, ::carbon::module::types::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetModuleSensors : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetModuleSensors() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::module::types::Empty, ::hardware_manager::ReaperPcSensorData>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::module::types::Empty* request, ::hardware_manager::ReaperPcSensorData* response) { return this->GetModuleSensors(context, request, response); }));}
    void SetMessageAllocatorFor_GetModuleSensors(
        ::grpc::MessageAllocator< ::carbon::module::types::Empty, ::hardware_manager::ReaperPcSensorData>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::module::types::Empty, ::hardware_manager::ReaperPcSensorData>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetModuleSensors() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModuleSensors(::grpc::ServerContext* /*context*/, const ::carbon::module::types::Empty* /*request*/, ::hardware_manager::ReaperPcSensorData* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetModuleSensors(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::module::types::Empty* /*request*/, ::hardware_manager::ReaperPcSensorData* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetModuleIdentity<WithCallbackMethod_SetModuleSerialNumber<WithCallbackMethod_SetModuleIdentity<WithCallbackMethod_GetModuleSensors<Service > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetModuleIdentity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetModuleIdentity() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetModuleIdentity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModuleIdentity(::grpc::ServerContext* /*context*/, const ::carbon::module::types::Empty* /*request*/, ::carbon::module::server::GetModuleIdentityResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetModuleSerialNumber : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetModuleSerialNumber() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_SetModuleSerialNumber() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModuleSerialNumber(::grpc::ServerContext* /*context*/, const ::carbon::module::server::SetModuleSerialNumberRequest* /*request*/, ::carbon::module::types::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetModuleIdentity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetModuleIdentity() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_SetModuleIdentity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModuleIdentity(::grpc::ServerContext* /*context*/, const ::carbon::module::server::SetModuleIdentityRequest* /*request*/, ::carbon::module::types::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetModuleSensors : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetModuleSensors() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_GetModuleSensors() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModuleSensors(::grpc::ServerContext* /*context*/, const ::carbon::module::types::Empty* /*request*/, ::hardware_manager::ReaperPcSensorData* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetModuleIdentity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetModuleIdentity() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetModuleIdentity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModuleIdentity(::grpc::ServerContext* /*context*/, const ::carbon::module::types::Empty* /*request*/, ::carbon::module::server::GetModuleIdentityResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetModuleIdentity(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetModuleSerialNumber : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetModuleSerialNumber() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_SetModuleSerialNumber() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModuleSerialNumber(::grpc::ServerContext* /*context*/, const ::carbon::module::server::SetModuleSerialNumberRequest* /*request*/, ::carbon::module::types::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetModuleSerialNumber(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetModuleIdentity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetModuleIdentity() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_SetModuleIdentity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModuleIdentity(::grpc::ServerContext* /*context*/, const ::carbon::module::server::SetModuleIdentityRequest* /*request*/, ::carbon::module::types::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetModuleIdentity(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetModuleSensors : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetModuleSensors() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_GetModuleSensors() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModuleSensors(::grpc::ServerContext* /*context*/, const ::carbon::module::types::Empty* /*request*/, ::hardware_manager::ReaperPcSensorData* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetModuleSensors(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetModuleIdentity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetModuleIdentity() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetModuleIdentity(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetModuleIdentity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModuleIdentity(::grpc::ServerContext* /*context*/, const ::carbon::module::types::Empty* /*request*/, ::carbon::module::server::GetModuleIdentityResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetModuleIdentity(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetModuleSerialNumber : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetModuleSerialNumber() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetModuleSerialNumber(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetModuleSerialNumber() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModuleSerialNumber(::grpc::ServerContext* /*context*/, const ::carbon::module::server::SetModuleSerialNumberRequest* /*request*/, ::carbon::module::types::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetModuleSerialNumber(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetModuleIdentity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetModuleIdentity() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetModuleIdentity(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetModuleIdentity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModuleIdentity(::grpc::ServerContext* /*context*/, const ::carbon::module::server::SetModuleIdentityRequest* /*request*/, ::carbon::module::types::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetModuleIdentity(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetModuleSensors : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetModuleSensors() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetModuleSensors(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetModuleSensors() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModuleSensors(::grpc::ServerContext* /*context*/, const ::carbon::module::types::Empty* /*request*/, ::hardware_manager::ReaperPcSensorData* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetModuleSensors(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetModuleIdentity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetModuleIdentity() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::module::types::Empty, ::carbon::module::server::GetModuleIdentityResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::module::types::Empty, ::carbon::module::server::GetModuleIdentityResponse>* streamer) {
                       return this->StreamedGetModuleIdentity(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetModuleIdentity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetModuleIdentity(::grpc::ServerContext* /*context*/, const ::carbon::module::types::Empty* /*request*/, ::carbon::module::server::GetModuleIdentityResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetModuleIdentity(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::module::types::Empty,::carbon::module::server::GetModuleIdentityResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetModuleSerialNumber : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetModuleSerialNumber() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::module::server::SetModuleSerialNumberRequest, ::carbon::module::types::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::module::server::SetModuleSerialNumberRequest, ::carbon::module::types::Empty>* streamer) {
                       return this->StreamedSetModuleSerialNumber(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetModuleSerialNumber() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetModuleSerialNumber(::grpc::ServerContext* /*context*/, const ::carbon::module::server::SetModuleSerialNumberRequest* /*request*/, ::carbon::module::types::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetModuleSerialNumber(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::module::server::SetModuleSerialNumberRequest,::carbon::module::types::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetModuleIdentity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetModuleIdentity() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::module::server::SetModuleIdentityRequest, ::carbon::module::types::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::module::server::SetModuleIdentityRequest, ::carbon::module::types::Empty>* streamer) {
                       return this->StreamedSetModuleIdentity(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetModuleIdentity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetModuleIdentity(::grpc::ServerContext* /*context*/, const ::carbon::module::server::SetModuleIdentityRequest* /*request*/, ::carbon::module::types::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetModuleIdentity(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::module::server::SetModuleIdentityRequest,::carbon::module::types::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetModuleSensors : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetModuleSensors() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::module::types::Empty, ::hardware_manager::ReaperPcSensorData>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::module::types::Empty, ::hardware_manager::ReaperPcSensorData>* streamer) {
                       return this->StreamedGetModuleSensors(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetModuleSensors() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetModuleSensors(::grpc::ServerContext* /*context*/, const ::carbon::module::types::Empty* /*request*/, ::hardware_manager::ReaperPcSensorData* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetModuleSensors(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::module::types::Empty,::hardware_manager::ReaperPcSensorData>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetModuleIdentity<WithStreamedUnaryMethod_SetModuleSerialNumber<WithStreamedUnaryMethod_SetModuleIdentity<WithStreamedUnaryMethod_GetModuleSensors<Service > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetModuleIdentity<WithStreamedUnaryMethod_SetModuleSerialNumber<WithStreamedUnaryMethod_SetModuleIdentity<WithStreamedUnaryMethod_GetModuleSensors<Service > > > > StreamedService;
};

}  // namespace server
}  // namespace module
}  // namespace carbon


#endif  // GRPC_proto_2fmodule_2fserver_2fserver_2eproto__INCLUDED

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/module/server/server.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_proto_2fmodule_2fserver_2fserver_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_proto_2fmodule_2fserver_2fserver_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "proto/module/types/types.pb.h"
#include "hardware_manager/proto/hardware_manager_service.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_proto_2fmodule_2fserver_2fserver_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_proto_2fmodule_2fserver_2fserver_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[3]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2fmodule_2fserver_2fserver_2eproto;
namespace carbon {
namespace module {
namespace server {
class GetModuleIdentityResponse;
struct GetModuleIdentityResponseDefaultTypeInternal;
extern GetModuleIdentityResponseDefaultTypeInternal _GetModuleIdentityResponse_default_instance_;
class SetModuleIdentityRequest;
struct SetModuleIdentityRequestDefaultTypeInternal;
extern SetModuleIdentityRequestDefaultTypeInternal _SetModuleIdentityRequest_default_instance_;
class SetModuleSerialNumberRequest;
struct SetModuleSerialNumberRequestDefaultTypeInternal;
extern SetModuleSerialNumberRequestDefaultTypeInternal _SetModuleSerialNumberRequest_default_instance_;
}  // namespace server
}  // namespace module
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::module::server::GetModuleIdentityResponse* Arena::CreateMaybeMessage<::carbon::module::server::GetModuleIdentityResponse>(Arena*);
template<> ::carbon::module::server::SetModuleIdentityRequest* Arena::CreateMaybeMessage<::carbon::module::server::SetModuleIdentityRequest>(Arena*);
template<> ::carbon::module::server::SetModuleSerialNumberRequest* Arena::CreateMaybeMessage<::carbon::module::server::SetModuleSerialNumberRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace module {
namespace server {

// ===================================================================

class GetModuleIdentityResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.module.server.GetModuleIdentityResponse) */ {
 public:
  inline GetModuleIdentityResponse() : GetModuleIdentityResponse(nullptr) {}
  ~GetModuleIdentityResponse() override;
  explicit constexpr GetModuleIdentityResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetModuleIdentityResponse(const GetModuleIdentityResponse& from);
  GetModuleIdentityResponse(GetModuleIdentityResponse&& from) noexcept
    : GetModuleIdentityResponse() {
    *this = ::std::move(from);
  }

  inline GetModuleIdentityResponse& operator=(const GetModuleIdentityResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetModuleIdentityResponse& operator=(GetModuleIdentityResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetModuleIdentityResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetModuleIdentityResponse* internal_default_instance() {
    return reinterpret_cast<const GetModuleIdentityResponse*>(
               &_GetModuleIdentityResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GetModuleIdentityResponse& a, GetModuleIdentityResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetModuleIdentityResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetModuleIdentityResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetModuleIdentityResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetModuleIdentityResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetModuleIdentityResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetModuleIdentityResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetModuleIdentityResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.module.server.GetModuleIdentityResponse";
  }
  protected:
  explicit GetModuleIdentityResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdentityFieldNumber = 1,
  };
  // .carbon.module.types.ModuleIdentity identity = 1;
  bool has_identity() const;
  private:
  bool _internal_has_identity() const;
  public:
  void clear_identity();
  const ::carbon::module::types::ModuleIdentity& identity() const;
  PROTOBUF_NODISCARD ::carbon::module::types::ModuleIdentity* release_identity();
  ::carbon::module::types::ModuleIdentity* mutable_identity();
  void set_allocated_identity(::carbon::module::types::ModuleIdentity* identity);
  private:
  const ::carbon::module::types::ModuleIdentity& _internal_identity() const;
  ::carbon::module::types::ModuleIdentity* _internal_mutable_identity();
  public:
  void unsafe_arena_set_allocated_identity(
      ::carbon::module::types::ModuleIdentity* identity);
  ::carbon::module::types::ModuleIdentity* unsafe_arena_release_identity();

  // @@protoc_insertion_point(class_scope:carbon.module.server.GetModuleIdentityResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::module::types::ModuleIdentity* identity_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmodule_2fserver_2fserver_2eproto;
};
// -------------------------------------------------------------------

class SetModuleSerialNumberRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.module.server.SetModuleSerialNumberRequest) */ {
 public:
  inline SetModuleSerialNumberRequest() : SetModuleSerialNumberRequest(nullptr) {}
  ~SetModuleSerialNumberRequest() override;
  explicit constexpr SetModuleSerialNumberRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetModuleSerialNumberRequest(const SetModuleSerialNumberRequest& from);
  SetModuleSerialNumberRequest(SetModuleSerialNumberRequest&& from) noexcept
    : SetModuleSerialNumberRequest() {
    *this = ::std::move(from);
  }

  inline SetModuleSerialNumberRequest& operator=(const SetModuleSerialNumberRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetModuleSerialNumberRequest& operator=(SetModuleSerialNumberRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetModuleSerialNumberRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetModuleSerialNumberRequest* internal_default_instance() {
    return reinterpret_cast<const SetModuleSerialNumberRequest*>(
               &_SetModuleSerialNumberRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(SetModuleSerialNumberRequest& a, SetModuleSerialNumberRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetModuleSerialNumberRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetModuleSerialNumberRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetModuleSerialNumberRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetModuleSerialNumberRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetModuleSerialNumberRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetModuleSerialNumberRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetModuleSerialNumberRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.module.server.SetModuleSerialNumberRequest";
  }
  protected:
  explicit SetModuleSerialNumberRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSerialNumberFieldNumber = 1,
    kForceFieldNumber = 2,
  };
  // string serial_number = 1;
  void clear_serial_number();
  const std::string& serial_number() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_serial_number(ArgT0&& arg0, ArgT... args);
  std::string* mutable_serial_number();
  PROTOBUF_NODISCARD std::string* release_serial_number();
  void set_allocated_serial_number(std::string* serial_number);
  private:
  const std::string& _internal_serial_number() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_serial_number(const std::string& value);
  std::string* _internal_mutable_serial_number();
  public:

  // bool force = 2;
  void clear_force();
  bool force() const;
  void set_force(bool value);
  private:
  bool _internal_force() const;
  void _internal_set_force(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.module.server.SetModuleSerialNumberRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr serial_number_;
  bool force_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmodule_2fserver_2fserver_2eproto;
};
// -------------------------------------------------------------------

class SetModuleIdentityRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.module.server.SetModuleIdentityRequest) */ {
 public:
  inline SetModuleIdentityRequest() : SetModuleIdentityRequest(nullptr) {}
  ~SetModuleIdentityRequest() override;
  explicit constexpr SetModuleIdentityRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetModuleIdentityRequest(const SetModuleIdentityRequest& from);
  SetModuleIdentityRequest(SetModuleIdentityRequest&& from) noexcept
    : SetModuleIdentityRequest() {
    *this = ::std::move(from);
  }

  inline SetModuleIdentityRequest& operator=(const SetModuleIdentityRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetModuleIdentityRequest& operator=(SetModuleIdentityRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetModuleIdentityRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetModuleIdentityRequest* internal_default_instance() {
    return reinterpret_cast<const SetModuleIdentityRequest*>(
               &_SetModuleIdentityRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(SetModuleIdentityRequest& a, SetModuleIdentityRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetModuleIdentityRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetModuleIdentityRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetModuleIdentityRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetModuleIdentityRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetModuleIdentityRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetModuleIdentityRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetModuleIdentityRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.module.server.SetModuleIdentityRequest";
  }
  protected:
  explicit SetModuleIdentityRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdentityFieldNumber = 1,
  };
  // .carbon.module.types.ModuleIdentity identity = 1;
  bool has_identity() const;
  private:
  bool _internal_has_identity() const;
  public:
  void clear_identity();
  const ::carbon::module::types::ModuleIdentity& identity() const;
  PROTOBUF_NODISCARD ::carbon::module::types::ModuleIdentity* release_identity();
  ::carbon::module::types::ModuleIdentity* mutable_identity();
  void set_allocated_identity(::carbon::module::types::ModuleIdentity* identity);
  private:
  const ::carbon::module::types::ModuleIdentity& _internal_identity() const;
  ::carbon::module::types::ModuleIdentity* _internal_mutable_identity();
  public:
  void unsafe_arena_set_allocated_identity(
      ::carbon::module::types::ModuleIdentity* identity);
  ::carbon::module::types::ModuleIdentity* unsafe_arena_release_identity();

  // @@protoc_insertion_point(class_scope:carbon.module.server.SetModuleIdentityRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::module::types::ModuleIdentity* identity_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmodule_2fserver_2fserver_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GetModuleIdentityResponse

// .carbon.module.types.ModuleIdentity identity = 1;
inline bool GetModuleIdentityResponse::_internal_has_identity() const {
  return this != internal_default_instance() && identity_ != nullptr;
}
inline bool GetModuleIdentityResponse::has_identity() const {
  return _internal_has_identity();
}
inline const ::carbon::module::types::ModuleIdentity& GetModuleIdentityResponse::_internal_identity() const {
  const ::carbon::module::types::ModuleIdentity* p = identity_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::module::types::ModuleIdentity&>(
      ::carbon::module::types::_ModuleIdentity_default_instance_);
}
inline const ::carbon::module::types::ModuleIdentity& GetModuleIdentityResponse::identity() const {
  // @@protoc_insertion_point(field_get:carbon.module.server.GetModuleIdentityResponse.identity)
  return _internal_identity();
}
inline void GetModuleIdentityResponse::unsafe_arena_set_allocated_identity(
    ::carbon::module::types::ModuleIdentity* identity) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(identity_);
  }
  identity_ = identity;
  if (identity) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.module.server.GetModuleIdentityResponse.identity)
}
inline ::carbon::module::types::ModuleIdentity* GetModuleIdentityResponse::release_identity() {
  
  ::carbon::module::types::ModuleIdentity* temp = identity_;
  identity_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::module::types::ModuleIdentity* GetModuleIdentityResponse::unsafe_arena_release_identity() {
  // @@protoc_insertion_point(field_release:carbon.module.server.GetModuleIdentityResponse.identity)
  
  ::carbon::module::types::ModuleIdentity* temp = identity_;
  identity_ = nullptr;
  return temp;
}
inline ::carbon::module::types::ModuleIdentity* GetModuleIdentityResponse::_internal_mutable_identity() {
  
  if (identity_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::module::types::ModuleIdentity>(GetArenaForAllocation());
    identity_ = p;
  }
  return identity_;
}
inline ::carbon::module::types::ModuleIdentity* GetModuleIdentityResponse::mutable_identity() {
  ::carbon::module::types::ModuleIdentity* _msg = _internal_mutable_identity();
  // @@protoc_insertion_point(field_mutable:carbon.module.server.GetModuleIdentityResponse.identity)
  return _msg;
}
inline void GetModuleIdentityResponse::set_allocated_identity(::carbon::module::types::ModuleIdentity* identity) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(identity_);
  }
  if (identity) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(identity));
    if (message_arena != submessage_arena) {
      identity = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, identity, submessage_arena);
    }
    
  } else {
    
  }
  identity_ = identity;
  // @@protoc_insertion_point(field_set_allocated:carbon.module.server.GetModuleIdentityResponse.identity)
}

// -------------------------------------------------------------------

// SetModuleSerialNumberRequest

// string serial_number = 1;
inline void SetModuleSerialNumberRequest::clear_serial_number() {
  serial_number_.ClearToEmpty();
}
inline const std::string& SetModuleSerialNumberRequest::serial_number() const {
  // @@protoc_insertion_point(field_get:carbon.module.server.SetModuleSerialNumberRequest.serial_number)
  return _internal_serial_number();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetModuleSerialNumberRequest::set_serial_number(ArgT0&& arg0, ArgT... args) {
 
 serial_number_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.module.server.SetModuleSerialNumberRequest.serial_number)
}
inline std::string* SetModuleSerialNumberRequest::mutable_serial_number() {
  std::string* _s = _internal_mutable_serial_number();
  // @@protoc_insertion_point(field_mutable:carbon.module.server.SetModuleSerialNumberRequest.serial_number)
  return _s;
}
inline const std::string& SetModuleSerialNumberRequest::_internal_serial_number() const {
  return serial_number_.Get();
}
inline void SetModuleSerialNumberRequest::_internal_set_serial_number(const std::string& value) {
  
  serial_number_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetModuleSerialNumberRequest::_internal_mutable_serial_number() {
  
  return serial_number_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetModuleSerialNumberRequest::release_serial_number() {
  // @@protoc_insertion_point(field_release:carbon.module.server.SetModuleSerialNumberRequest.serial_number)
  return serial_number_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetModuleSerialNumberRequest::set_allocated_serial_number(std::string* serial_number) {
  if (serial_number != nullptr) {
    
  } else {
    
  }
  serial_number_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), serial_number,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (serial_number_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    serial_number_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.module.server.SetModuleSerialNumberRequest.serial_number)
}

// bool force = 2;
inline void SetModuleSerialNumberRequest::clear_force() {
  force_ = false;
}
inline bool SetModuleSerialNumberRequest::_internal_force() const {
  return force_;
}
inline bool SetModuleSerialNumberRequest::force() const {
  // @@protoc_insertion_point(field_get:carbon.module.server.SetModuleSerialNumberRequest.force)
  return _internal_force();
}
inline void SetModuleSerialNumberRequest::_internal_set_force(bool value) {
  
  force_ = value;
}
inline void SetModuleSerialNumberRequest::set_force(bool value) {
  _internal_set_force(value);
  // @@protoc_insertion_point(field_set:carbon.module.server.SetModuleSerialNumberRequest.force)
}

// -------------------------------------------------------------------

// SetModuleIdentityRequest

// .carbon.module.types.ModuleIdentity identity = 1;
inline bool SetModuleIdentityRequest::_internal_has_identity() const {
  return this != internal_default_instance() && identity_ != nullptr;
}
inline bool SetModuleIdentityRequest::has_identity() const {
  return _internal_has_identity();
}
inline const ::carbon::module::types::ModuleIdentity& SetModuleIdentityRequest::_internal_identity() const {
  const ::carbon::module::types::ModuleIdentity* p = identity_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::module::types::ModuleIdentity&>(
      ::carbon::module::types::_ModuleIdentity_default_instance_);
}
inline const ::carbon::module::types::ModuleIdentity& SetModuleIdentityRequest::identity() const {
  // @@protoc_insertion_point(field_get:carbon.module.server.SetModuleIdentityRequest.identity)
  return _internal_identity();
}
inline void SetModuleIdentityRequest::unsafe_arena_set_allocated_identity(
    ::carbon::module::types::ModuleIdentity* identity) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(identity_);
  }
  identity_ = identity;
  if (identity) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.module.server.SetModuleIdentityRequest.identity)
}
inline ::carbon::module::types::ModuleIdentity* SetModuleIdentityRequest::release_identity() {
  
  ::carbon::module::types::ModuleIdentity* temp = identity_;
  identity_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::module::types::ModuleIdentity* SetModuleIdentityRequest::unsafe_arena_release_identity() {
  // @@protoc_insertion_point(field_release:carbon.module.server.SetModuleIdentityRequest.identity)
  
  ::carbon::module::types::ModuleIdentity* temp = identity_;
  identity_ = nullptr;
  return temp;
}
inline ::carbon::module::types::ModuleIdentity* SetModuleIdentityRequest::_internal_mutable_identity() {
  
  if (identity_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::module::types::ModuleIdentity>(GetArenaForAllocation());
    identity_ = p;
  }
  return identity_;
}
inline ::carbon::module::types::ModuleIdentity* SetModuleIdentityRequest::mutable_identity() {
  ::carbon::module::types::ModuleIdentity* _msg = _internal_mutable_identity();
  // @@protoc_insertion_point(field_mutable:carbon.module.server.SetModuleIdentityRequest.identity)
  return _msg;
}
inline void SetModuleIdentityRequest::set_allocated_identity(::carbon::module::types::ModuleIdentity* identity) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(identity_);
  }
  if (identity) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(identity));
    if (message_arena != submessage_arena) {
      identity = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, identity, submessage_arena);
    }
    
  } else {
    
  }
  identity_ = identity;
  // @@protoc_insertion_point(field_set_allocated:carbon.module.server.SetModuleIdentityRequest.identity)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace server
}  // namespace module
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_proto_2fmodule_2fserver_2fserver_2eproto

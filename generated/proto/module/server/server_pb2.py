# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/module/server/server.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.proto.module.types import types_pb2 as proto_dot_module_dot_types_dot_types__pb2
from generated.hardware_manager.proto import hardware_manager_service_pb2 as hardware__manager_dot_proto_dot_hardware__manager__service__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/module/server/server.proto',
  package='carbon.module.server',
  syntax='proto3',
  serialized_options=b'Z\023proto/module/server',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n proto/module/server/server.proto\x12\x14\x63\x61rbon.module.server\x1a\x1eproto/module/types/types.proto\x1a\x35hardware_manager/proto/hardware_manager_service.proto\"R\n\x19GetModuleIdentityResponse\x12\x35\n\x08identity\x18\x01 \x01(\x0b\x32#.carbon.module.types.ModuleIdentity\"D\n\x1cSetModuleSerialNumberRequest\x12\x15\n\rserial_number\x18\x01 \x01(\t\x12\r\n\x05\x66orce\x18\x02 \x01(\x08\"Q\n\x18SetModuleIdentityRequest\x12\x35\n\x08identity\x18\x01 \x01(\x0b\x32#.carbon.module.types.ModuleIdentity2\x97\x03\n\x13ModuleServerService\x12`\n\x11GetModuleIdentity\x12\x1a.carbon.module.types.Empty\x1a/.carbon.module.server.GetModuleIdentityResponse\x12g\n\x15SetModuleSerialNumber\x12\x32.carbon.module.server.SetModuleSerialNumberRequest\x1a\x1a.carbon.module.types.Empty\x12_\n\x11SetModuleIdentity\x12..carbon.module.server.SetModuleIdentityRequest\x1a\x1a.carbon.module.types.Empty\x12T\n\x10GetModuleSensors\x12\x1a.carbon.module.types.Empty\x1a$.hardware_manager.ReaperPcSensorDataB\x15Z\x13proto/module/serverb\x06proto3'
  ,
  dependencies=[proto_dot_module_dot_types_dot_types__pb2.DESCRIPTOR,hardware__manager_dot_proto_dot_hardware__manager__service__pb2.DESCRIPTOR,])




_GETMODULEIDENTITYRESPONSE = _descriptor.Descriptor(
  name='GetModuleIdentityResponse',
  full_name='carbon.module.server.GetModuleIdentityResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='identity', full_name='carbon.module.server.GetModuleIdentityResponse.identity', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=145,
  serialized_end=227,
)


_SETMODULESERIALNUMBERREQUEST = _descriptor.Descriptor(
  name='SetModuleSerialNumberRequest',
  full_name='carbon.module.server.SetModuleSerialNumberRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='serial_number', full_name='carbon.module.server.SetModuleSerialNumberRequest.serial_number', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='force', full_name='carbon.module.server.SetModuleSerialNumberRequest.force', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=229,
  serialized_end=297,
)


_SETMODULEIDENTITYREQUEST = _descriptor.Descriptor(
  name='SetModuleIdentityRequest',
  full_name='carbon.module.server.SetModuleIdentityRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='identity', full_name='carbon.module.server.SetModuleIdentityRequest.identity', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=299,
  serialized_end=380,
)

_GETMODULEIDENTITYRESPONSE.fields_by_name['identity'].message_type = proto_dot_module_dot_types_dot_types__pb2._MODULEIDENTITY
_SETMODULEIDENTITYREQUEST.fields_by_name['identity'].message_type = proto_dot_module_dot_types_dot_types__pb2._MODULEIDENTITY
DESCRIPTOR.message_types_by_name['GetModuleIdentityResponse'] = _GETMODULEIDENTITYRESPONSE
DESCRIPTOR.message_types_by_name['SetModuleSerialNumberRequest'] = _SETMODULESERIALNUMBERREQUEST
DESCRIPTOR.message_types_by_name['SetModuleIdentityRequest'] = _SETMODULEIDENTITYREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetModuleIdentityResponse = _reflection.GeneratedProtocolMessageType('GetModuleIdentityResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETMODULEIDENTITYRESPONSE,
  '__module__' : 'proto.module.server.server_pb2'
  # @@protoc_insertion_point(class_scope:carbon.module.server.GetModuleIdentityResponse)
  })
_sym_db.RegisterMessage(GetModuleIdentityResponse)

SetModuleSerialNumberRequest = _reflection.GeneratedProtocolMessageType('SetModuleSerialNumberRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETMODULESERIALNUMBERREQUEST,
  '__module__' : 'proto.module.server.server_pb2'
  # @@protoc_insertion_point(class_scope:carbon.module.server.SetModuleSerialNumberRequest)
  })
_sym_db.RegisterMessage(SetModuleSerialNumberRequest)

SetModuleIdentityRequest = _reflection.GeneratedProtocolMessageType('SetModuleIdentityRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETMODULEIDENTITYREQUEST,
  '__module__' : 'proto.module.server.server_pb2'
  # @@protoc_insertion_point(class_scope:carbon.module.server.SetModuleIdentityRequest)
  })
_sym_db.RegisterMessage(SetModuleIdentityRequest)


DESCRIPTOR._options = None

_MODULESERVERSERVICE = _descriptor.ServiceDescriptor(
  name='ModuleServerService',
  full_name='carbon.module.server.ModuleServerService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=383,
  serialized_end=790,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetModuleIdentity',
    full_name='carbon.module.server.ModuleServerService.GetModuleIdentity',
    index=0,
    containing_service=None,
    input_type=proto_dot_module_dot_types_dot_types__pb2._EMPTY,
    output_type=_GETMODULEIDENTITYRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetModuleSerialNumber',
    full_name='carbon.module.server.ModuleServerService.SetModuleSerialNumber',
    index=1,
    containing_service=None,
    input_type=_SETMODULESERIALNUMBERREQUEST,
    output_type=proto_dot_module_dot_types_dot_types__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetModuleIdentity',
    full_name='carbon.module.server.ModuleServerService.SetModuleIdentity',
    index=2,
    containing_service=None,
    input_type=_SETMODULEIDENTITYREQUEST,
    output_type=proto_dot_module_dot_types_dot_types__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetModuleSensors',
    full_name='carbon.module.server.ModuleServerService.GetModuleSensors',
    index=3,
    containing_service=None,
    input_type=proto_dot_module_dot_types_dot_types__pb2._EMPTY,
    output_type=hardware__manager_dot_proto_dot_hardware__manager__service__pb2._REAPERPCSENSORDATA,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_MODULESERVERSERVICE)

DESCRIPTOR.services_by_name['ModuleServerService'] = _MODULESERVERSERVICE

# @@protoc_insertion_point(module_scope)

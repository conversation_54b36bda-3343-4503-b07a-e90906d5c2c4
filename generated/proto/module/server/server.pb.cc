// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/module/server/server.proto

#include "proto/module/server/server.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace module {
namespace server {
constexpr GetModuleIdentityResponse::GetModuleIdentityResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : identity_(nullptr){}
struct GetModuleIdentityResponseDefaultTypeInternal {
  constexpr GetModuleIdentityResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetModuleIdentityResponseDefaultTypeInternal() {}
  union {
    GetModuleIdentityResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetModuleIdentityResponseDefaultTypeInternal _GetModuleIdentityResponse_default_instance_;
constexpr SetModuleSerialNumberRequest::SetModuleSerialNumberRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : serial_number_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , force_(false){}
struct SetModuleSerialNumberRequestDefaultTypeInternal {
  constexpr SetModuleSerialNumberRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetModuleSerialNumberRequestDefaultTypeInternal() {}
  union {
    SetModuleSerialNumberRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetModuleSerialNumberRequestDefaultTypeInternal _SetModuleSerialNumberRequest_default_instance_;
constexpr SetModuleIdentityRequest::SetModuleIdentityRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : identity_(nullptr){}
struct SetModuleIdentityRequestDefaultTypeInternal {
  constexpr SetModuleIdentityRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetModuleIdentityRequestDefaultTypeInternal() {}
  union {
    SetModuleIdentityRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetModuleIdentityRequestDefaultTypeInternal _SetModuleIdentityRequest_default_instance_;
}  // namespace server
}  // namespace module
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_proto_2fmodule_2fserver_2fserver_2eproto[3];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_proto_2fmodule_2fserver_2fserver_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_2fmodule_2fserver_2fserver_2eproto = nullptr;

const uint32_t TableStruct_proto_2fmodule_2fserver_2fserver_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::module::server::GetModuleIdentityResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::module::server::GetModuleIdentityResponse, identity_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::module::server::SetModuleSerialNumberRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::module::server::SetModuleSerialNumberRequest, serial_number_),
  PROTOBUF_FIELD_OFFSET(::carbon::module::server::SetModuleSerialNumberRequest, force_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::module::server::SetModuleIdentityRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::module::server::SetModuleIdentityRequest, identity_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::module::server::GetModuleIdentityResponse)},
  { 7, -1, -1, sizeof(::carbon::module::server::SetModuleSerialNumberRequest)},
  { 15, -1, -1, sizeof(::carbon::module::server::SetModuleIdentityRequest)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::module::server::_GetModuleIdentityResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::module::server::_SetModuleSerialNumberRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::module::server::_SetModuleIdentityRequest_default_instance_),
};

const char descriptor_table_protodef_proto_2fmodule_2fserver_2fserver_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n proto/module/server/server.proto\022\024carb"
  "on.module.server\032\036proto/module/types/typ"
  "es.proto\0325hardware_manager/proto/hardwar"
  "e_manager_service.proto\"R\n\031GetModuleIden"
  "tityResponse\0225\n\010identity\030\001 \001(\0132#.carbon."
  "module.types.ModuleIdentity\"D\n\034SetModule"
  "SerialNumberRequest\022\025\n\rserial_number\030\001 \001"
  "(\t\022\r\n\005force\030\002 \001(\010\"Q\n\030SetModuleIdentityRe"
  "quest\0225\n\010identity\030\001 \001(\0132#.carbon.module."
  "types.ModuleIdentity2\227\003\n\023ModuleServerSer"
  "vice\022`\n\021GetModuleIdentity\022\032.carbon.modul"
  "e.types.Empty\032/.carbon.module.server.Get"
  "ModuleIdentityResponse\022g\n\025SetModuleSeria"
  "lNumber\0222.carbon.module.server.SetModule"
  "SerialNumberRequest\032\032.carbon.module.type"
  "s.Empty\022_\n\021SetModuleIdentity\022..carbon.mo"
  "dule.server.SetModuleIdentityRequest\032\032.c"
  "arbon.module.types.Empty\022T\n\020GetModuleSen"
  "sors\022\032.carbon.module.types.Empty\032$.hardw"
  "are_manager.ReaperPcSensorDataB\025Z\023proto/"
  "module/serverb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_proto_2fmodule_2fserver_2fserver_2eproto_deps[2] = {
  &::descriptor_table_hardware_5fmanager_2fproto_2fhardware_5fmanager_5fservice_2eproto,
  &::descriptor_table_proto_2fmodule_2ftypes_2ftypes_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_2fmodule_2fserver_2fserver_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2fmodule_2fserver_2fserver_2eproto = {
  false, false, 821, descriptor_table_protodef_proto_2fmodule_2fserver_2fserver_2eproto, "proto/module/server/server.proto", 
  &descriptor_table_proto_2fmodule_2fserver_2fserver_2eproto_once, descriptor_table_proto_2fmodule_2fserver_2fserver_2eproto_deps, 2, 3,
  schemas, file_default_instances, TableStruct_proto_2fmodule_2fserver_2fserver_2eproto::offsets,
  file_level_metadata_proto_2fmodule_2fserver_2fserver_2eproto, file_level_enum_descriptors_proto_2fmodule_2fserver_2fserver_2eproto, file_level_service_descriptors_proto_2fmodule_2fserver_2fserver_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_proto_2fmodule_2fserver_2fserver_2eproto_getter() {
  return &descriptor_table_proto_2fmodule_2fserver_2fserver_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_proto_2fmodule_2fserver_2fserver_2eproto(&descriptor_table_proto_2fmodule_2fserver_2fserver_2eproto);
namespace carbon {
namespace module {
namespace server {

// ===================================================================

class GetModuleIdentityResponse::_Internal {
 public:
  static const ::carbon::module::types::ModuleIdentity& identity(const GetModuleIdentityResponse* msg);
};

const ::carbon::module::types::ModuleIdentity&
GetModuleIdentityResponse::_Internal::identity(const GetModuleIdentityResponse* msg) {
  return *msg->identity_;
}
void GetModuleIdentityResponse::clear_identity() {
  if (GetArenaForAllocation() == nullptr && identity_ != nullptr) {
    delete identity_;
  }
  identity_ = nullptr;
}
GetModuleIdentityResponse::GetModuleIdentityResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.module.server.GetModuleIdentityResponse)
}
GetModuleIdentityResponse::GetModuleIdentityResponse(const GetModuleIdentityResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_identity()) {
    identity_ = new ::carbon::module::types::ModuleIdentity(*from.identity_);
  } else {
    identity_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.module.server.GetModuleIdentityResponse)
}

inline void GetModuleIdentityResponse::SharedCtor() {
identity_ = nullptr;
}

GetModuleIdentityResponse::~GetModuleIdentityResponse() {
  // @@protoc_insertion_point(destructor:carbon.module.server.GetModuleIdentityResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetModuleIdentityResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete identity_;
}

void GetModuleIdentityResponse::ArenaDtor(void* object) {
  GetModuleIdentityResponse* _this = reinterpret_cast< GetModuleIdentityResponse* >(object);
  (void)_this;
}
void GetModuleIdentityResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetModuleIdentityResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetModuleIdentityResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.module.server.GetModuleIdentityResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && identity_ != nullptr) {
    delete identity_;
  }
  identity_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetModuleIdentityResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.module.types.ModuleIdentity identity = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_identity(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetModuleIdentityResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.module.server.GetModuleIdentityResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.module.types.ModuleIdentity identity = 1;
  if (this->_internal_has_identity()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::identity(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.module.server.GetModuleIdentityResponse)
  return target;
}

size_t GetModuleIdentityResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.module.server.GetModuleIdentityResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.module.types.ModuleIdentity identity = 1;
  if (this->_internal_has_identity()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *identity_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetModuleIdentityResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetModuleIdentityResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetModuleIdentityResponse::GetClassData() const { return &_class_data_; }

void GetModuleIdentityResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetModuleIdentityResponse *>(to)->MergeFrom(
      static_cast<const GetModuleIdentityResponse &>(from));
}


void GetModuleIdentityResponse::MergeFrom(const GetModuleIdentityResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.module.server.GetModuleIdentityResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_identity()) {
    _internal_mutable_identity()->::carbon::module::types::ModuleIdentity::MergeFrom(from._internal_identity());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetModuleIdentityResponse::CopyFrom(const GetModuleIdentityResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.module.server.GetModuleIdentityResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetModuleIdentityResponse::IsInitialized() const {
  return true;
}

void GetModuleIdentityResponse::InternalSwap(GetModuleIdentityResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(identity_, other->identity_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetModuleIdentityResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmodule_2fserver_2fserver_2eproto_getter, &descriptor_table_proto_2fmodule_2fserver_2fserver_2eproto_once,
      file_level_metadata_proto_2fmodule_2fserver_2fserver_2eproto[0]);
}

// ===================================================================

class SetModuleSerialNumberRequest::_Internal {
 public:
};

SetModuleSerialNumberRequest::SetModuleSerialNumberRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.module.server.SetModuleSerialNumberRequest)
}
SetModuleSerialNumberRequest::SetModuleSerialNumberRequest(const SetModuleSerialNumberRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  serial_number_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    serial_number_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_serial_number().empty()) {
    serial_number_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_serial_number(), 
      GetArenaForAllocation());
  }
  force_ = from.force_;
  // @@protoc_insertion_point(copy_constructor:carbon.module.server.SetModuleSerialNumberRequest)
}

inline void SetModuleSerialNumberRequest::SharedCtor() {
serial_number_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  serial_number_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
force_ = false;
}

SetModuleSerialNumberRequest::~SetModuleSerialNumberRequest() {
  // @@protoc_insertion_point(destructor:carbon.module.server.SetModuleSerialNumberRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetModuleSerialNumberRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  serial_number_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SetModuleSerialNumberRequest::ArenaDtor(void* object) {
  SetModuleSerialNumberRequest* _this = reinterpret_cast< SetModuleSerialNumberRequest* >(object);
  (void)_this;
}
void SetModuleSerialNumberRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetModuleSerialNumberRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetModuleSerialNumberRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.module.server.SetModuleSerialNumberRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  serial_number_.ClearToEmpty();
  force_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetModuleSerialNumberRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string serial_number = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_serial_number();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.module.server.SetModuleSerialNumberRequest.serial_number"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool force = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          force_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetModuleSerialNumberRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.module.server.SetModuleSerialNumberRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string serial_number = 1;
  if (!this->_internal_serial_number().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_serial_number().data(), static_cast<int>(this->_internal_serial_number().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.module.server.SetModuleSerialNumberRequest.serial_number");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_serial_number(), target);
  }

  // bool force = 2;
  if (this->_internal_force() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_force(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.module.server.SetModuleSerialNumberRequest)
  return target;
}

size_t SetModuleSerialNumberRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.module.server.SetModuleSerialNumberRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string serial_number = 1;
  if (!this->_internal_serial_number().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_serial_number());
  }

  // bool force = 2;
  if (this->_internal_force() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetModuleSerialNumberRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetModuleSerialNumberRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetModuleSerialNumberRequest::GetClassData() const { return &_class_data_; }

void SetModuleSerialNumberRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetModuleSerialNumberRequest *>(to)->MergeFrom(
      static_cast<const SetModuleSerialNumberRequest &>(from));
}


void SetModuleSerialNumberRequest::MergeFrom(const SetModuleSerialNumberRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.module.server.SetModuleSerialNumberRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_serial_number().empty()) {
    _internal_set_serial_number(from._internal_serial_number());
  }
  if (from._internal_force() != 0) {
    _internal_set_force(from._internal_force());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetModuleSerialNumberRequest::CopyFrom(const SetModuleSerialNumberRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.module.server.SetModuleSerialNumberRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetModuleSerialNumberRequest::IsInitialized() const {
  return true;
}

void SetModuleSerialNumberRequest::InternalSwap(SetModuleSerialNumberRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &serial_number_, lhs_arena,
      &other->serial_number_, rhs_arena
  );
  swap(force_, other->force_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetModuleSerialNumberRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmodule_2fserver_2fserver_2eproto_getter, &descriptor_table_proto_2fmodule_2fserver_2fserver_2eproto_once,
      file_level_metadata_proto_2fmodule_2fserver_2fserver_2eproto[1]);
}

// ===================================================================

class SetModuleIdentityRequest::_Internal {
 public:
  static const ::carbon::module::types::ModuleIdentity& identity(const SetModuleIdentityRequest* msg);
};

const ::carbon::module::types::ModuleIdentity&
SetModuleIdentityRequest::_Internal::identity(const SetModuleIdentityRequest* msg) {
  return *msg->identity_;
}
void SetModuleIdentityRequest::clear_identity() {
  if (GetArenaForAllocation() == nullptr && identity_ != nullptr) {
    delete identity_;
  }
  identity_ = nullptr;
}
SetModuleIdentityRequest::SetModuleIdentityRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.module.server.SetModuleIdentityRequest)
}
SetModuleIdentityRequest::SetModuleIdentityRequest(const SetModuleIdentityRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_identity()) {
    identity_ = new ::carbon::module::types::ModuleIdentity(*from.identity_);
  } else {
    identity_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.module.server.SetModuleIdentityRequest)
}

inline void SetModuleIdentityRequest::SharedCtor() {
identity_ = nullptr;
}

SetModuleIdentityRequest::~SetModuleIdentityRequest() {
  // @@protoc_insertion_point(destructor:carbon.module.server.SetModuleIdentityRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetModuleIdentityRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete identity_;
}

void SetModuleIdentityRequest::ArenaDtor(void* object) {
  SetModuleIdentityRequest* _this = reinterpret_cast< SetModuleIdentityRequest* >(object);
  (void)_this;
}
void SetModuleIdentityRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetModuleIdentityRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetModuleIdentityRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.module.server.SetModuleIdentityRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && identity_ != nullptr) {
    delete identity_;
  }
  identity_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetModuleIdentityRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.module.types.ModuleIdentity identity = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_identity(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetModuleIdentityRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.module.server.SetModuleIdentityRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.module.types.ModuleIdentity identity = 1;
  if (this->_internal_has_identity()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::identity(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.module.server.SetModuleIdentityRequest)
  return target;
}

size_t SetModuleIdentityRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.module.server.SetModuleIdentityRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.module.types.ModuleIdentity identity = 1;
  if (this->_internal_has_identity()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *identity_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetModuleIdentityRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetModuleIdentityRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetModuleIdentityRequest::GetClassData() const { return &_class_data_; }

void SetModuleIdentityRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetModuleIdentityRequest *>(to)->MergeFrom(
      static_cast<const SetModuleIdentityRequest &>(from));
}


void SetModuleIdentityRequest::MergeFrom(const SetModuleIdentityRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.module.server.SetModuleIdentityRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_identity()) {
    _internal_mutable_identity()->::carbon::module::types::ModuleIdentity::MergeFrom(from._internal_identity());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetModuleIdentityRequest::CopyFrom(const SetModuleIdentityRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.module.server.SetModuleIdentityRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetModuleIdentityRequest::IsInitialized() const {
  return true;
}

void SetModuleIdentityRequest::InternalSwap(SetModuleIdentityRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(identity_, other->identity_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetModuleIdentityRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmodule_2fserver_2fserver_2eproto_getter, &descriptor_table_proto_2fmodule_2fserver_2fserver_2eproto_once,
      file_level_metadata_proto_2fmodule_2fserver_2fserver_2eproto[2]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace server
}  // namespace module
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::module::server::GetModuleIdentityResponse* Arena::CreateMaybeMessage< ::carbon::module::server::GetModuleIdentityResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::module::server::GetModuleIdentityResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::module::server::SetModuleSerialNumberRequest* Arena::CreateMaybeMessage< ::carbon::module::server::SetModuleSerialNumberRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::module::server::SetModuleSerialNumberRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::module::server::SetModuleIdentityRequest* Arena::CreateMaybeMessage< ::carbon::module::server::SetModuleIdentityRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::module::server::SetModuleIdentityRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

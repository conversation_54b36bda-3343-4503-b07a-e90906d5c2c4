// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: proto/module/server/server.proto

#include "proto/module/server/server.pb.h"
#include "proto/module/server/server.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace module {
namespace server {

static const char* ModuleServerService_method_names[] = {
  "/carbon.module.server.ModuleServerService/GetModuleIdentity",
  "/carbon.module.server.ModuleServerService/SetModuleSerialNumber",
  "/carbon.module.server.ModuleServerService/SetModuleIdentity",
  "/carbon.module.server.ModuleServerService/GetModuleSensors",
};

std::unique_ptr< ModuleServerService::Stub> ModuleServerService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< ModuleServerService::Stub> stub(new ModuleServerService::Stub(channel, options));
  return stub;
}

ModuleServerService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetModuleIdentity_(ModuleServerService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetModuleSerialNumber_(ModuleServerService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetModuleIdentity_(ModuleServerService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetModuleSensors_(ModuleServerService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status ModuleServerService::Stub::GetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::carbon::module::server::GetModuleIdentityResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::module::types::Empty, ::carbon::module::server::GetModuleIdentityResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetModuleIdentity_, context, request, response);
}

void ModuleServerService::Stub::async::GetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::types::Empty* request, ::carbon::module::server::GetModuleIdentityResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::module::types::Empty, ::carbon::module::server::GetModuleIdentityResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetModuleIdentity_, context, request, response, std::move(f));
}

void ModuleServerService::Stub::async::GetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::types::Empty* request, ::carbon::module::server::GetModuleIdentityResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetModuleIdentity_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::module::server::GetModuleIdentityResponse>* ModuleServerService::Stub::PrepareAsyncGetModuleIdentityRaw(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::module::server::GetModuleIdentityResponse, ::carbon::module::types::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetModuleIdentity_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::module::server::GetModuleIdentityResponse>* ModuleServerService::Stub::AsyncGetModuleIdentityRaw(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetModuleIdentityRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModuleServerService::Stub::SetModuleSerialNumber(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest& request, ::carbon::module::types::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::module::server::SetModuleSerialNumberRequest, ::carbon::module::types::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetModuleSerialNumber_, context, request, response);
}

void ModuleServerService::Stub::async::SetModuleSerialNumber(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest* request, ::carbon::module::types::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::module::server::SetModuleSerialNumberRequest, ::carbon::module::types::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetModuleSerialNumber_, context, request, response, std::move(f));
}

void ModuleServerService::Stub::async::SetModuleSerialNumber(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest* request, ::carbon::module::types::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetModuleSerialNumber_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::module::types::Empty>* ModuleServerService::Stub::PrepareAsyncSetModuleSerialNumberRaw(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::module::types::Empty, ::carbon::module::server::SetModuleSerialNumberRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetModuleSerialNumber_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::module::types::Empty>* ModuleServerService::Stub::AsyncSetModuleSerialNumberRaw(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetModuleSerialNumberRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModuleServerService::Stub::SetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleIdentityRequest& request, ::carbon::module::types::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::module::server::SetModuleIdentityRequest, ::carbon::module::types::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetModuleIdentity_, context, request, response);
}

void ModuleServerService::Stub::async::SetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleIdentityRequest* request, ::carbon::module::types::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::module::server::SetModuleIdentityRequest, ::carbon::module::types::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetModuleIdentity_, context, request, response, std::move(f));
}

void ModuleServerService::Stub::async::SetModuleIdentity(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleIdentityRequest* request, ::carbon::module::types::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetModuleIdentity_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::module::types::Empty>* ModuleServerService::Stub::PrepareAsyncSetModuleIdentityRaw(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleIdentityRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::module::types::Empty, ::carbon::module::server::SetModuleIdentityRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetModuleIdentity_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::module::types::Empty>* ModuleServerService::Stub::AsyncSetModuleIdentityRaw(::grpc::ClientContext* context, const ::carbon::module::server::SetModuleIdentityRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetModuleIdentityRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModuleServerService::Stub::GetModuleSensors(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::hardware_manager::ReaperPcSensorData* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::module::types::Empty, ::hardware_manager::ReaperPcSensorData, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetModuleSensors_, context, request, response);
}

void ModuleServerService::Stub::async::GetModuleSensors(::grpc::ClientContext* context, const ::carbon::module::types::Empty* request, ::hardware_manager::ReaperPcSensorData* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::module::types::Empty, ::hardware_manager::ReaperPcSensorData, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetModuleSensors_, context, request, response, std::move(f));
}

void ModuleServerService::Stub::async::GetModuleSensors(::grpc::ClientContext* context, const ::carbon::module::types::Empty* request, ::hardware_manager::ReaperPcSensorData* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetModuleSensors_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::ReaperPcSensorData>* ModuleServerService::Stub::PrepareAsyncGetModuleSensorsRaw(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::ReaperPcSensorData, ::carbon::module::types::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetModuleSensors_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::ReaperPcSensorData>* ModuleServerService::Stub::AsyncGetModuleSensorsRaw(::grpc::ClientContext* context, const ::carbon::module::types::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetModuleSensorsRaw(context, request, cq);
  result->StartCall();
  return result;
}

ModuleServerService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModuleServerService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModuleServerService::Service, ::carbon::module::types::Empty, ::carbon::module::server::GetModuleIdentityResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModuleServerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::module::types::Empty* req,
             ::carbon::module::server::GetModuleIdentityResponse* resp) {
               return service->GetModuleIdentity(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModuleServerService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModuleServerService::Service, ::carbon::module::server::SetModuleSerialNumberRequest, ::carbon::module::types::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModuleServerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::module::server::SetModuleSerialNumberRequest* req,
             ::carbon::module::types::Empty* resp) {
               return service->SetModuleSerialNumber(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModuleServerService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModuleServerService::Service, ::carbon::module::server::SetModuleIdentityRequest, ::carbon::module::types::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModuleServerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::module::server::SetModuleIdentityRequest* req,
             ::carbon::module::types::Empty* resp) {
               return service->SetModuleIdentity(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModuleServerService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModuleServerService::Service, ::carbon::module::types::Empty, ::hardware_manager::ReaperPcSensorData, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModuleServerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::module::types::Empty* req,
             ::hardware_manager::ReaperPcSensorData* resp) {
               return service->GetModuleSensors(ctx, req, resp);
             }, this)));
}

ModuleServerService::Service::~Service() {
}

::grpc::Status ModuleServerService::Service::GetModuleIdentity(::grpc::ServerContext* context, const ::carbon::module::types::Empty* request, ::carbon::module::server::GetModuleIdentityResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModuleServerService::Service::SetModuleSerialNumber(::grpc::ServerContext* context, const ::carbon::module::server::SetModuleSerialNumberRequest* request, ::carbon::module::types::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModuleServerService::Service::SetModuleIdentity(::grpc::ServerContext* context, const ::carbon::module::server::SetModuleIdentityRequest* request, ::carbon::module::types::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModuleServerService::Service::GetModuleSensors(::grpc::ServerContext* context, const ::carbon::module::types::Empty* request, ::hardware_manager::ReaperPcSensorData* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace module
}  // namespace server


// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/almanac/almanac.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_proto_2falmanac_2falmanac_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_proto_2falmanac_2falmanac_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_proto_2falmanac_2falmanac_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_proto_2falmanac_2falmanac_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[10]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2falmanac_2falmanac_2eproto;
namespace carbon {
namespace aimbot {
namespace almanac {
class AlmanacConfig;
struct AlmanacConfigDefaultTypeInternal;
extern AlmanacConfigDefaultTypeInternal _AlmanacConfig_default_instance_;
class AlmanacTypeCategory;
struct AlmanacTypeCategoryDefaultTypeInternal;
extern AlmanacTypeCategoryDefaultTypeInternal _AlmanacTypeCategory_default_instance_;
class DiscriminatorConfig;
struct DiscriminatorConfigDefaultTypeInternal;
extern DiscriminatorConfigDefaultTypeInternal _DiscriminatorConfig_default_instance_;
class DiscriminatorTypeCategory;
struct DiscriminatorTypeCategoryDefaultTypeInternal;
extern DiscriminatorTypeCategoryDefaultTypeInternal _DiscriminatorTypeCategory_default_instance_;
class Formula;
struct FormulaDefaultTypeInternal;
extern FormulaDefaultTypeInternal _Formula_default_instance_;
class ModelTrust;
struct ModelTrustDefaultTypeInternal;
extern ModelTrustDefaultTypeInternal _ModelTrust_default_instance_;
class ModelinatorConfig;
struct ModelinatorConfigDefaultTypeInternal;
extern ModelinatorConfigDefaultTypeInternal _ModelinatorConfig_default_instance_;
class ModelinatorTypeCategory;
struct ModelinatorTypeCategoryDefaultTypeInternal;
extern ModelinatorTypeCategoryDefaultTypeInternal _ModelinatorTypeCategory_default_instance_;
class Trust;
struct TrustDefaultTypeInternal;
extern TrustDefaultTypeInternal _Trust_default_instance_;
class TypeCategory;
struct TypeCategoryDefaultTypeInternal;
extern TypeCategoryDefaultTypeInternal _TypeCategory_default_instance_;
}  // namespace almanac
}  // namespace aimbot
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::aimbot::almanac::AlmanacConfig* Arena::CreateMaybeMessage<::carbon::aimbot::almanac::AlmanacConfig>(Arena*);
template<> ::carbon::aimbot::almanac::AlmanacTypeCategory* Arena::CreateMaybeMessage<::carbon::aimbot::almanac::AlmanacTypeCategory>(Arena*);
template<> ::carbon::aimbot::almanac::DiscriminatorConfig* Arena::CreateMaybeMessage<::carbon::aimbot::almanac::DiscriminatorConfig>(Arena*);
template<> ::carbon::aimbot::almanac::DiscriminatorTypeCategory* Arena::CreateMaybeMessage<::carbon::aimbot::almanac::DiscriminatorTypeCategory>(Arena*);
template<> ::carbon::aimbot::almanac::Formula* Arena::CreateMaybeMessage<::carbon::aimbot::almanac::Formula>(Arena*);
template<> ::carbon::aimbot::almanac::ModelTrust* Arena::CreateMaybeMessage<::carbon::aimbot::almanac::ModelTrust>(Arena*);
template<> ::carbon::aimbot::almanac::ModelinatorConfig* Arena::CreateMaybeMessage<::carbon::aimbot::almanac::ModelinatorConfig>(Arena*);
template<> ::carbon::aimbot::almanac::ModelinatorTypeCategory* Arena::CreateMaybeMessage<::carbon::aimbot::almanac::ModelinatorTypeCategory>(Arena*);
template<> ::carbon::aimbot::almanac::Trust* Arena::CreateMaybeMessage<::carbon::aimbot::almanac::Trust>(Arena*);
template<> ::carbon::aimbot::almanac::TypeCategory* Arena::CreateMaybeMessage<::carbon::aimbot::almanac::TypeCategory>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace aimbot {
namespace almanac {

enum CategoryClassification : int {
  CATEGORY_WEED = 0,
  CATEGORY_CROP = 1,
  CategoryClassification_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  CategoryClassification_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool CategoryClassification_IsValid(int value);
constexpr CategoryClassification CategoryClassification_MIN = CATEGORY_WEED;
constexpr CategoryClassification CategoryClassification_MAX = CATEGORY_CROP;
constexpr int CategoryClassification_ARRAYSIZE = CategoryClassification_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CategoryClassification_descriptor();
template<typename T>
inline const std::string& CategoryClassification_Name(T enum_t_value) {
  static_assert(::std::is_same<T, CategoryClassification>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function CategoryClassification_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    CategoryClassification_descriptor(), enum_t_value);
}
inline bool CategoryClassification_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, CategoryClassification* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<CategoryClassification>(
    CategoryClassification_descriptor(), name, value);
}
// ===================================================================

class Formula final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.aimbot.almanac.Formula) */ {
 public:
  inline Formula() : Formula(nullptr) {}
  ~Formula() override;
  explicit constexpr Formula(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Formula(const Formula& from);
  Formula(Formula&& from) noexcept
    : Formula() {
    *this = ::std::move(from);
  }

  inline Formula& operator=(const Formula& from) {
    CopyFrom(from);
    return *this;
  }
  inline Formula& operator=(Formula&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Formula& default_instance() {
    return *internal_default_instance();
  }
  static inline const Formula* internal_default_instance() {
    return reinterpret_cast<const Formula*>(
               &_Formula_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Formula& a, Formula& b) {
    a.Swap(&b);
  }
  inline void Swap(Formula* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Formula* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Formula* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Formula>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Formula& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Formula& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Formula* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.aimbot.almanac.Formula";
  }
  protected:
  explicit Formula(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMultiplierFieldNumber = 1,
    kOffsetFieldNumber = 2,
    kExponentFieldNumber = 3,
    kFineTuneMultiplierFieldNumber = 4,
    kMaxTimeFieldNumber = 5,
    kFineTuneMultiplierValFieldNumber = 6,
  };
  // float multiplier = 1;
  void clear_multiplier();
  float multiplier() const;
  void set_multiplier(float value);
  private:
  float _internal_multiplier() const;
  void _internal_set_multiplier(float value);
  public:

  // float offset = 2;
  void clear_offset();
  float offset() const;
  void set_offset(float value);
  private:
  float _internal_offset() const;
  void _internal_set_offset(float value);
  public:

  // float exponent = 3;
  void clear_exponent();
  float exponent() const;
  void set_exponent(float value);
  private:
  float _internal_exponent() const;
  void _internal_set_exponent(float value);
  public:

  // uint32 fine_tune_multiplier = 4;
  void clear_fine_tune_multiplier();
  uint32_t fine_tune_multiplier() const;
  void set_fine_tune_multiplier(uint32_t value);
  private:
  uint32_t _internal_fine_tune_multiplier() const;
  void _internal_set_fine_tune_multiplier(uint32_t value);
  public:

  // uint32 max_time = 5;
  void clear_max_time();
  uint32_t max_time() const;
  void set_max_time(uint32_t value);
  private:
  uint32_t _internal_max_time() const;
  void _internal_set_max_time(uint32_t value);
  public:

  // float fine_tune_multiplier_val = 6;
  void clear_fine_tune_multiplier_val();
  float fine_tune_multiplier_val() const;
  void set_fine_tune_multiplier_val(float value);
  private:
  float _internal_fine_tune_multiplier_val() const;
  void _internal_set_fine_tune_multiplier_val(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.Formula)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float multiplier_;
  float offset_;
  float exponent_;
  uint32_t fine_tune_multiplier_;
  uint32_t max_time_;
  float fine_tune_multiplier_val_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2falmanac_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class Trust final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.aimbot.almanac.Trust) */ {
 public:
  inline Trust() : Trust(nullptr) {}
  ~Trust() override;
  explicit constexpr Trust(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Trust(const Trust& from);
  Trust(Trust&& from) noexcept
    : Trust() {
    *this = ::std::move(from);
  }

  inline Trust& operator=(const Trust& from) {
    CopyFrom(from);
    return *this;
  }
  inline Trust& operator=(Trust&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Trust& default_instance() {
    return *internal_default_instance();
  }
  static inline const Trust* internal_default_instance() {
    return reinterpret_cast<const Trust*>(
               &_Trust_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Trust& a, Trust& b) {
    a.Swap(&b);
  }
  inline void Swap(Trust* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Trust* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Trust* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Trust>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Trust& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Trust& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Trust* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.aimbot.almanac.Trust";
  }
  protected:
  explicit Trust(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIgnorableFieldNumber = 1,
    kAvoidFieldNumber = 2,
  };
  // bool ignorable = 1;
  void clear_ignorable();
  bool ignorable() const;
  void set_ignorable(bool value);
  private:
  bool _internal_ignorable() const;
  void _internal_set_ignorable(bool value);
  public:

  // bool avoid = 2;
  void clear_avoid();
  bool avoid() const;
  void set_avoid(bool value);
  private:
  bool _internal_avoid() const;
  void _internal_set_avoid(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.Trust)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool ignorable_;
  bool avoid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2falmanac_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class ModelTrust final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.aimbot.almanac.ModelTrust) */ {
 public:
  inline ModelTrust() : ModelTrust(nullptr) {}
  ~ModelTrust() override;
  explicit constexpr ModelTrust(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModelTrust(const ModelTrust& from);
  ModelTrust(ModelTrust&& from) noexcept
    : ModelTrust() {
    *this = ::std::move(from);
  }

  inline ModelTrust& operator=(const ModelTrust& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelTrust& operator=(ModelTrust&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModelTrust& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModelTrust* internal_default_instance() {
    return reinterpret_cast<const ModelTrust*>(
               &_ModelTrust_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ModelTrust& a, ModelTrust& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelTrust* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelTrust* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModelTrust* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModelTrust>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModelTrust& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ModelTrust& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelTrust* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.aimbot.almanac.ModelTrust";
  }
  protected:
  explicit ModelTrust(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMinDooFieldNumber = 1,
    kWeedingThresholdFieldNumber = 2,
    kThinningThresholdFieldNumber = 3,
    kBandingThresholdFieldNumber = 4,
  };
  // float min_doo = 1;
  void clear_min_doo();
  float min_doo() const;
  void set_min_doo(float value);
  private:
  float _internal_min_doo() const;
  void _internal_set_min_doo(float value);
  public:

  // float weeding_threshold = 2;
  void clear_weeding_threshold();
  float weeding_threshold() const;
  void set_weeding_threshold(float value);
  private:
  float _internal_weeding_threshold() const;
  void _internal_set_weeding_threshold(float value);
  public:

  // float thinning_threshold = 3;
  void clear_thinning_threshold();
  float thinning_threshold() const;
  void set_thinning_threshold(float value);
  private:
  float _internal_thinning_threshold() const;
  void _internal_set_thinning_threshold(float value);
  public:

  // float banding_threshold = 4;
  void clear_banding_threshold();
  float banding_threshold() const;
  void set_banding_threshold(float value);
  private:
  float _internal_banding_threshold() const;
  void _internal_set_banding_threshold(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.ModelTrust)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float min_doo_;
  float weeding_threshold_;
  float thinning_threshold_;
  float banding_threshold_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2falmanac_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class TypeCategory final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.aimbot.almanac.TypeCategory) */ {
 public:
  inline TypeCategory() : TypeCategory(nullptr) {}
  ~TypeCategory() override;
  explicit constexpr TypeCategory(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TypeCategory(const TypeCategory& from);
  TypeCategory(TypeCategory&& from) noexcept
    : TypeCategory() {
    *this = ::std::move(from);
  }

  inline TypeCategory& operator=(const TypeCategory& from) {
    CopyFrom(from);
    return *this;
  }
  inline TypeCategory& operator=(TypeCategory&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TypeCategory& default_instance() {
    return *internal_default_instance();
  }
  static inline const TypeCategory* internal_default_instance() {
    return reinterpret_cast<const TypeCategory*>(
               &_TypeCategory_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(TypeCategory& a, TypeCategory& b) {
    a.Swap(&b);
  }
  inline void Swap(TypeCategory* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TypeCategory* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TypeCategory* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TypeCategory>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TypeCategory& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TypeCategory& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TypeCategory* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.aimbot.almanac.TypeCategory";
  }
  protected:
  explicit TypeCategory(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCategoryFieldNumber = 1,
    kClassificationFieldNumber = 2,
  };
  // string category = 1;
  void clear_category();
  const std::string& category() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_category(ArgT0&& arg0, ArgT... args);
  std::string* mutable_category();
  PROTOBUF_NODISCARD std::string* release_category();
  void set_allocated_category(std::string* category);
  private:
  const std::string& _internal_category() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_category(const std::string& value);
  std::string* _internal_mutable_category();
  public:

  // .carbon.aimbot.almanac.CategoryClassification classification = 2;
  void clear_classification();
  ::carbon::aimbot::almanac::CategoryClassification classification() const;
  void set_classification(::carbon::aimbot::almanac::CategoryClassification value);
  private:
  ::carbon::aimbot::almanac::CategoryClassification _internal_classification() const;
  void _internal_set_classification(::carbon::aimbot::almanac::CategoryClassification value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.TypeCategory)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr category_;
  int classification_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2falmanac_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class AlmanacTypeCategory final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.aimbot.almanac.AlmanacTypeCategory) */ {
 public:
  inline AlmanacTypeCategory() : AlmanacTypeCategory(nullptr) {}
  ~AlmanacTypeCategory() override;
  explicit constexpr AlmanacTypeCategory(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AlmanacTypeCategory(const AlmanacTypeCategory& from);
  AlmanacTypeCategory(AlmanacTypeCategory&& from) noexcept
    : AlmanacTypeCategory() {
    *this = ::std::move(from);
  }

  inline AlmanacTypeCategory& operator=(const AlmanacTypeCategory& from) {
    CopyFrom(from);
    return *this;
  }
  inline AlmanacTypeCategory& operator=(AlmanacTypeCategory&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AlmanacTypeCategory& default_instance() {
    return *internal_default_instance();
  }
  static inline const AlmanacTypeCategory* internal_default_instance() {
    return reinterpret_cast<const AlmanacTypeCategory*>(
               &_AlmanacTypeCategory_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(AlmanacTypeCategory& a, AlmanacTypeCategory& b) {
    a.Swap(&b);
  }
  inline void Swap(AlmanacTypeCategory* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AlmanacTypeCategory* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AlmanacTypeCategory* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AlmanacTypeCategory>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AlmanacTypeCategory& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AlmanacTypeCategory& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AlmanacTypeCategory* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.aimbot.almanac.AlmanacTypeCategory";
  }
  protected:
  explicit AlmanacTypeCategory(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSizesFieldNumber = 2,
    kFormulasFieldNumber = 3,
    kTypeFieldNumber = 1,
  };
  // repeated float sizes = 2;
  int sizes_size() const;
  private:
  int _internal_sizes_size() const;
  public:
  void clear_sizes();
  private:
  float _internal_sizes(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_sizes() const;
  void _internal_add_sizes(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_sizes();
  public:
  float sizes(int index) const;
  void set_sizes(int index, float value);
  void add_sizes(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      sizes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_sizes();

  // repeated .carbon.aimbot.almanac.Formula formulas = 3;
  int formulas_size() const;
  private:
  int _internal_formulas_size() const;
  public:
  void clear_formulas();
  ::carbon::aimbot::almanac::Formula* mutable_formulas(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::Formula >*
      mutable_formulas();
  private:
  const ::carbon::aimbot::almanac::Formula& _internal_formulas(int index) const;
  ::carbon::aimbot::almanac::Formula* _internal_add_formulas();
  public:
  const ::carbon::aimbot::almanac::Formula& formulas(int index) const;
  ::carbon::aimbot::almanac::Formula* add_formulas();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::Formula >&
      formulas() const;

  // .carbon.aimbot.almanac.TypeCategory type = 1;
  bool has_type() const;
  private:
  bool _internal_has_type() const;
  public:
  void clear_type();
  const ::carbon::aimbot::almanac::TypeCategory& type() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::TypeCategory* release_type();
  ::carbon::aimbot::almanac::TypeCategory* mutable_type();
  void set_allocated_type(::carbon::aimbot::almanac::TypeCategory* type);
  private:
  const ::carbon::aimbot::almanac::TypeCategory& _internal_type() const;
  ::carbon::aimbot::almanac::TypeCategory* _internal_mutable_type();
  public:
  void unsafe_arena_set_allocated_type(
      ::carbon::aimbot::almanac::TypeCategory* type);
  ::carbon::aimbot::almanac::TypeCategory* unsafe_arena_release_type();

  // @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.AlmanacTypeCategory)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > sizes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::Formula > formulas_;
  ::carbon::aimbot::almanac::TypeCategory* type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2falmanac_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class DiscriminatorTypeCategory final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.aimbot.almanac.DiscriminatorTypeCategory) */ {
 public:
  inline DiscriminatorTypeCategory() : DiscriminatorTypeCategory(nullptr) {}
  ~DiscriminatorTypeCategory() override;
  explicit constexpr DiscriminatorTypeCategory(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DiscriminatorTypeCategory(const DiscriminatorTypeCategory& from);
  DiscriminatorTypeCategory(DiscriminatorTypeCategory&& from) noexcept
    : DiscriminatorTypeCategory() {
    *this = ::std::move(from);
  }

  inline DiscriminatorTypeCategory& operator=(const DiscriminatorTypeCategory& from) {
    CopyFrom(from);
    return *this;
  }
  inline DiscriminatorTypeCategory& operator=(DiscriminatorTypeCategory&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DiscriminatorTypeCategory& default_instance() {
    return *internal_default_instance();
  }
  static inline const DiscriminatorTypeCategory* internal_default_instance() {
    return reinterpret_cast<const DiscriminatorTypeCategory*>(
               &_DiscriminatorTypeCategory_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(DiscriminatorTypeCategory& a, DiscriminatorTypeCategory& b) {
    a.Swap(&b);
  }
  inline void Swap(DiscriminatorTypeCategory* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DiscriminatorTypeCategory* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DiscriminatorTypeCategory* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DiscriminatorTypeCategory>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DiscriminatorTypeCategory& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DiscriminatorTypeCategory& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DiscriminatorTypeCategory* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.aimbot.almanac.DiscriminatorTypeCategory";
  }
  protected:
  explicit DiscriminatorTypeCategory(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTrustsFieldNumber = 2,
    kTypeFieldNumber = 1,
  };
  // repeated .carbon.aimbot.almanac.Trust trusts = 2;
  int trusts_size() const;
  private:
  int _internal_trusts_size() const;
  public:
  void clear_trusts();
  ::carbon::aimbot::almanac::Trust* mutable_trusts(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::Trust >*
      mutable_trusts();
  private:
  const ::carbon::aimbot::almanac::Trust& _internal_trusts(int index) const;
  ::carbon::aimbot::almanac::Trust* _internal_add_trusts();
  public:
  const ::carbon::aimbot::almanac::Trust& trusts(int index) const;
  ::carbon::aimbot::almanac::Trust* add_trusts();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::Trust >&
      trusts() const;

  // .carbon.aimbot.almanac.TypeCategory type = 1;
  bool has_type() const;
  private:
  bool _internal_has_type() const;
  public:
  void clear_type();
  const ::carbon::aimbot::almanac::TypeCategory& type() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::TypeCategory* release_type();
  ::carbon::aimbot::almanac::TypeCategory* mutable_type();
  void set_allocated_type(::carbon::aimbot::almanac::TypeCategory* type);
  private:
  const ::carbon::aimbot::almanac::TypeCategory& _internal_type() const;
  ::carbon::aimbot::almanac::TypeCategory* _internal_mutable_type();
  public:
  void unsafe_arena_set_allocated_type(
      ::carbon::aimbot::almanac::TypeCategory* type);
  ::carbon::aimbot::almanac::TypeCategory* unsafe_arena_release_type();

  // @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.DiscriminatorTypeCategory)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::Trust > trusts_;
  ::carbon::aimbot::almanac::TypeCategory* type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2falmanac_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class ModelinatorTypeCategory final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.aimbot.almanac.ModelinatorTypeCategory) */ {
 public:
  inline ModelinatorTypeCategory() : ModelinatorTypeCategory(nullptr) {}
  ~ModelinatorTypeCategory() override;
  explicit constexpr ModelinatorTypeCategory(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModelinatorTypeCategory(const ModelinatorTypeCategory& from);
  ModelinatorTypeCategory(ModelinatorTypeCategory&& from) noexcept
    : ModelinatorTypeCategory() {
    *this = ::std::move(from);
  }

  inline ModelinatorTypeCategory& operator=(const ModelinatorTypeCategory& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelinatorTypeCategory& operator=(ModelinatorTypeCategory&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModelinatorTypeCategory& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModelinatorTypeCategory* internal_default_instance() {
    return reinterpret_cast<const ModelinatorTypeCategory*>(
               &_ModelinatorTypeCategory_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(ModelinatorTypeCategory& a, ModelinatorTypeCategory& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelinatorTypeCategory* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelinatorTypeCategory* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModelinatorTypeCategory* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModelinatorTypeCategory>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModelinatorTypeCategory& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ModelinatorTypeCategory& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelinatorTypeCategory* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.aimbot.almanac.ModelinatorTypeCategory";
  }
  protected:
  explicit ModelinatorTypeCategory(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTrustsFieldNumber = 2,
    kTypeFieldNumber = 1,
  };
  // repeated .carbon.aimbot.almanac.ModelTrust trusts = 2;
  int trusts_size() const;
  private:
  int _internal_trusts_size() const;
  public:
  void clear_trusts();
  ::carbon::aimbot::almanac::ModelTrust* mutable_trusts(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::ModelTrust >*
      mutable_trusts();
  private:
  const ::carbon::aimbot::almanac::ModelTrust& _internal_trusts(int index) const;
  ::carbon::aimbot::almanac::ModelTrust* _internal_add_trusts();
  public:
  const ::carbon::aimbot::almanac::ModelTrust& trusts(int index) const;
  ::carbon::aimbot::almanac::ModelTrust* add_trusts();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::ModelTrust >&
      trusts() const;

  // .carbon.aimbot.almanac.TypeCategory type = 1;
  bool has_type() const;
  private:
  bool _internal_has_type() const;
  public:
  void clear_type();
  const ::carbon::aimbot::almanac::TypeCategory& type() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::TypeCategory* release_type();
  ::carbon::aimbot::almanac::TypeCategory* mutable_type();
  void set_allocated_type(::carbon::aimbot::almanac::TypeCategory* type);
  private:
  const ::carbon::aimbot::almanac::TypeCategory& _internal_type() const;
  ::carbon::aimbot::almanac::TypeCategory* _internal_mutable_type();
  public:
  void unsafe_arena_set_allocated_type(
      ::carbon::aimbot::almanac::TypeCategory* type);
  ::carbon::aimbot::almanac::TypeCategory* unsafe_arena_release_type();

  // @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.ModelinatorTypeCategory)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::ModelTrust > trusts_;
  ::carbon::aimbot::almanac::TypeCategory* type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2falmanac_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class AlmanacConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.aimbot.almanac.AlmanacConfig) */ {
 public:
  inline AlmanacConfig() : AlmanacConfig(nullptr) {}
  ~AlmanacConfig() override;
  explicit constexpr AlmanacConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AlmanacConfig(const AlmanacConfig& from);
  AlmanacConfig(AlmanacConfig&& from) noexcept
    : AlmanacConfig() {
    *this = ::std::move(from);
  }

  inline AlmanacConfig& operator=(const AlmanacConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline AlmanacConfig& operator=(AlmanacConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AlmanacConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const AlmanacConfig* internal_default_instance() {
    return reinterpret_cast<const AlmanacConfig*>(
               &_AlmanacConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(AlmanacConfig& a, AlmanacConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(AlmanacConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AlmanacConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AlmanacConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AlmanacConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AlmanacConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AlmanacConfig& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AlmanacConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.aimbot.almanac.AlmanacConfig";
  }
  protected:
  explicit AlmanacConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCategoriesFieldNumber = 5,
    kIdFieldNumber = 1,
    kNameFieldNumber = 2,
    kUpdatedTsFieldNumber = 4,
    kProtectedFieldNumber = 3,
  };
  // repeated .carbon.aimbot.almanac.AlmanacTypeCategory categories = 5;
  int categories_size() const;
  private:
  int _internal_categories_size() const;
  public:
  void clear_categories();
  ::carbon::aimbot::almanac::AlmanacTypeCategory* mutable_categories(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::AlmanacTypeCategory >*
      mutable_categories();
  private:
  const ::carbon::aimbot::almanac::AlmanacTypeCategory& _internal_categories(int index) const;
  ::carbon::aimbot::almanac::AlmanacTypeCategory* _internal_add_categories();
  public:
  const ::carbon::aimbot::almanac::AlmanacTypeCategory& categories(int index) const;
  ::carbon::aimbot::almanac::AlmanacTypeCategory* add_categories();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::AlmanacTypeCategory >&
      categories() const;

  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // uint64 updated_ts = 4;
  void clear_updated_ts();
  uint64_t updated_ts() const;
  void set_updated_ts(uint64_t value);
  private:
  uint64_t _internal_updated_ts() const;
  void _internal_set_updated_ts(uint64_t value);
  public:

  // bool protected = 3;
  void clear_protected_();
  bool protected_() const;
  void set_protected_(bool value);
  private:
  bool _internal_protected_() const;
  void _internal_set_protected_(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.AlmanacConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::AlmanacTypeCategory > categories_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  uint64_t updated_ts_;
  bool protected__;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2falmanac_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class DiscriminatorConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.aimbot.almanac.DiscriminatorConfig) */ {
 public:
  inline DiscriminatorConfig() : DiscriminatorConfig(nullptr) {}
  ~DiscriminatorConfig() override;
  explicit constexpr DiscriminatorConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DiscriminatorConfig(const DiscriminatorConfig& from);
  DiscriminatorConfig(DiscriminatorConfig&& from) noexcept
    : DiscriminatorConfig() {
    *this = ::std::move(from);
  }

  inline DiscriminatorConfig& operator=(const DiscriminatorConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline DiscriminatorConfig& operator=(DiscriminatorConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DiscriminatorConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const DiscriminatorConfig* internal_default_instance() {
    return reinterpret_cast<const DiscriminatorConfig*>(
               &_DiscriminatorConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(DiscriminatorConfig& a, DiscriminatorConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(DiscriminatorConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DiscriminatorConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DiscriminatorConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DiscriminatorConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DiscriminatorConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DiscriminatorConfig& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DiscriminatorConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.aimbot.almanac.DiscriminatorConfig";
  }
  protected:
  explicit DiscriminatorConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCategoriesFieldNumber = 5,
    kIdFieldNumber = 1,
    kNameFieldNumber = 2,
    kUpdatedTsFieldNumber = 4,
    kProtectedFieldNumber = 3,
  };
  // repeated .carbon.aimbot.almanac.DiscriminatorTypeCategory categories = 5;
  int categories_size() const;
  private:
  int _internal_categories_size() const;
  public:
  void clear_categories();
  ::carbon::aimbot::almanac::DiscriminatorTypeCategory* mutable_categories(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::DiscriminatorTypeCategory >*
      mutable_categories();
  private:
  const ::carbon::aimbot::almanac::DiscriminatorTypeCategory& _internal_categories(int index) const;
  ::carbon::aimbot::almanac::DiscriminatorTypeCategory* _internal_add_categories();
  public:
  const ::carbon::aimbot::almanac::DiscriminatorTypeCategory& categories(int index) const;
  ::carbon::aimbot::almanac::DiscriminatorTypeCategory* add_categories();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::DiscriminatorTypeCategory >&
      categories() const;

  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // uint64 updated_ts = 4;
  void clear_updated_ts();
  uint64_t updated_ts() const;
  void set_updated_ts(uint64_t value);
  private:
  uint64_t _internal_updated_ts() const;
  void _internal_set_updated_ts(uint64_t value);
  public:

  // bool protected = 3;
  void clear_protected_();
  bool protected_() const;
  void set_protected_(bool value);
  private:
  bool _internal_protected_() const;
  void _internal_set_protected_(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.DiscriminatorConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::DiscriminatorTypeCategory > categories_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  uint64_t updated_ts_;
  bool protected__;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2falmanac_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class ModelinatorConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.aimbot.almanac.ModelinatorConfig) */ {
 public:
  inline ModelinatorConfig() : ModelinatorConfig(nullptr) {}
  ~ModelinatorConfig() override;
  explicit constexpr ModelinatorConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModelinatorConfig(const ModelinatorConfig& from);
  ModelinatorConfig(ModelinatorConfig&& from) noexcept
    : ModelinatorConfig() {
    *this = ::std::move(from);
  }

  inline ModelinatorConfig& operator=(const ModelinatorConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelinatorConfig& operator=(ModelinatorConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModelinatorConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModelinatorConfig* internal_default_instance() {
    return reinterpret_cast<const ModelinatorConfig*>(
               &_ModelinatorConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(ModelinatorConfig& a, ModelinatorConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelinatorConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelinatorConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModelinatorConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModelinatorConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModelinatorConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ModelinatorConfig& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelinatorConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.aimbot.almanac.ModelinatorConfig";
  }
  protected:
  explicit ModelinatorConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCategoriesFieldNumber = 4,
    kModelIdFieldNumber = 1,
    kCropIdFieldNumber = 2,
    kModifiedFieldNumber = 3,
  };
  // repeated .carbon.aimbot.almanac.ModelinatorTypeCategory categories = 4;
  int categories_size() const;
  private:
  int _internal_categories_size() const;
  public:
  void clear_categories();
  ::carbon::aimbot::almanac::ModelinatorTypeCategory* mutable_categories(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::ModelinatorTypeCategory >*
      mutable_categories();
  private:
  const ::carbon::aimbot::almanac::ModelinatorTypeCategory& _internal_categories(int index) const;
  ::carbon::aimbot::almanac::ModelinatorTypeCategory* _internal_add_categories();
  public:
  const ::carbon::aimbot::almanac::ModelinatorTypeCategory& categories(int index) const;
  ::carbon::aimbot::almanac::ModelinatorTypeCategory* add_categories();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::ModelinatorTypeCategory >&
      categories() const;

  // string model_id = 1;
  void clear_model_id();
  const std::string& model_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_id();
  PROTOBUF_NODISCARD std::string* release_model_id();
  void set_allocated_model_id(std::string* model_id);
  private:
  const std::string& _internal_model_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_id(const std::string& value);
  std::string* _internal_mutable_model_id();
  public:

  // string crop_id = 2;
  void clear_crop_id();
  const std::string& crop_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_id();
  PROTOBUF_NODISCARD std::string* release_crop_id();
  void set_allocated_crop_id(std::string* crop_id);
  private:
  const std::string& _internal_crop_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_id(const std::string& value);
  std::string* _internal_mutable_crop_id();
  public:

  // bool modified = 3;
  void clear_modified();
  bool modified() const;
  void set_modified(bool value);
  private:
  bool _internal_modified() const;
  void _internal_set_modified(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.ModelinatorConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::ModelinatorTypeCategory > categories_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_id_;
  bool modified_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2falmanac_2falmanac_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Formula

// float multiplier = 1;
inline void Formula::clear_multiplier() {
  multiplier_ = 0;
}
inline float Formula::_internal_multiplier() const {
  return multiplier_;
}
inline float Formula::multiplier() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.Formula.multiplier)
  return _internal_multiplier();
}
inline void Formula::_internal_set_multiplier(float value) {
  
  multiplier_ = value;
}
inline void Formula::set_multiplier(float value) {
  _internal_set_multiplier(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.Formula.multiplier)
}

// float offset = 2;
inline void Formula::clear_offset() {
  offset_ = 0;
}
inline float Formula::_internal_offset() const {
  return offset_;
}
inline float Formula::offset() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.Formula.offset)
  return _internal_offset();
}
inline void Formula::_internal_set_offset(float value) {
  
  offset_ = value;
}
inline void Formula::set_offset(float value) {
  _internal_set_offset(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.Formula.offset)
}

// float exponent = 3;
inline void Formula::clear_exponent() {
  exponent_ = 0;
}
inline float Formula::_internal_exponent() const {
  return exponent_;
}
inline float Formula::exponent() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.Formula.exponent)
  return _internal_exponent();
}
inline void Formula::_internal_set_exponent(float value) {
  
  exponent_ = value;
}
inline void Formula::set_exponent(float value) {
  _internal_set_exponent(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.Formula.exponent)
}

// uint32 fine_tune_multiplier = 4;
inline void Formula::clear_fine_tune_multiplier() {
  fine_tune_multiplier_ = 0u;
}
inline uint32_t Formula::_internal_fine_tune_multiplier() const {
  return fine_tune_multiplier_;
}
inline uint32_t Formula::fine_tune_multiplier() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.Formula.fine_tune_multiplier)
  return _internal_fine_tune_multiplier();
}
inline void Formula::_internal_set_fine_tune_multiplier(uint32_t value) {
  
  fine_tune_multiplier_ = value;
}
inline void Formula::set_fine_tune_multiplier(uint32_t value) {
  _internal_set_fine_tune_multiplier(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.Formula.fine_tune_multiplier)
}

// uint32 max_time = 5;
inline void Formula::clear_max_time() {
  max_time_ = 0u;
}
inline uint32_t Formula::_internal_max_time() const {
  return max_time_;
}
inline uint32_t Formula::max_time() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.Formula.max_time)
  return _internal_max_time();
}
inline void Formula::_internal_set_max_time(uint32_t value) {
  
  max_time_ = value;
}
inline void Formula::set_max_time(uint32_t value) {
  _internal_set_max_time(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.Formula.max_time)
}

// float fine_tune_multiplier_val = 6;
inline void Formula::clear_fine_tune_multiplier_val() {
  fine_tune_multiplier_val_ = 0;
}
inline float Formula::_internal_fine_tune_multiplier_val() const {
  return fine_tune_multiplier_val_;
}
inline float Formula::fine_tune_multiplier_val() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.Formula.fine_tune_multiplier_val)
  return _internal_fine_tune_multiplier_val();
}
inline void Formula::_internal_set_fine_tune_multiplier_val(float value) {
  
  fine_tune_multiplier_val_ = value;
}
inline void Formula::set_fine_tune_multiplier_val(float value) {
  _internal_set_fine_tune_multiplier_val(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.Formula.fine_tune_multiplier_val)
}

// -------------------------------------------------------------------

// Trust

// bool ignorable = 1;
inline void Trust::clear_ignorable() {
  ignorable_ = false;
}
inline bool Trust::_internal_ignorable() const {
  return ignorable_;
}
inline bool Trust::ignorable() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.Trust.ignorable)
  return _internal_ignorable();
}
inline void Trust::_internal_set_ignorable(bool value) {
  
  ignorable_ = value;
}
inline void Trust::set_ignorable(bool value) {
  _internal_set_ignorable(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.Trust.ignorable)
}

// bool avoid = 2;
inline void Trust::clear_avoid() {
  avoid_ = false;
}
inline bool Trust::_internal_avoid() const {
  return avoid_;
}
inline bool Trust::avoid() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.Trust.avoid)
  return _internal_avoid();
}
inline void Trust::_internal_set_avoid(bool value) {
  
  avoid_ = value;
}
inline void Trust::set_avoid(bool value) {
  _internal_set_avoid(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.Trust.avoid)
}

// -------------------------------------------------------------------

// ModelTrust

// float min_doo = 1;
inline void ModelTrust::clear_min_doo() {
  min_doo_ = 0;
}
inline float ModelTrust::_internal_min_doo() const {
  return min_doo_;
}
inline float ModelTrust::min_doo() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.ModelTrust.min_doo)
  return _internal_min_doo();
}
inline void ModelTrust::_internal_set_min_doo(float value) {
  
  min_doo_ = value;
}
inline void ModelTrust::set_min_doo(float value) {
  _internal_set_min_doo(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.ModelTrust.min_doo)
}

// float weeding_threshold = 2;
inline void ModelTrust::clear_weeding_threshold() {
  weeding_threshold_ = 0;
}
inline float ModelTrust::_internal_weeding_threshold() const {
  return weeding_threshold_;
}
inline float ModelTrust::weeding_threshold() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.ModelTrust.weeding_threshold)
  return _internal_weeding_threshold();
}
inline void ModelTrust::_internal_set_weeding_threshold(float value) {
  
  weeding_threshold_ = value;
}
inline void ModelTrust::set_weeding_threshold(float value) {
  _internal_set_weeding_threshold(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.ModelTrust.weeding_threshold)
}

// float thinning_threshold = 3;
inline void ModelTrust::clear_thinning_threshold() {
  thinning_threshold_ = 0;
}
inline float ModelTrust::_internal_thinning_threshold() const {
  return thinning_threshold_;
}
inline float ModelTrust::thinning_threshold() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.ModelTrust.thinning_threshold)
  return _internal_thinning_threshold();
}
inline void ModelTrust::_internal_set_thinning_threshold(float value) {
  
  thinning_threshold_ = value;
}
inline void ModelTrust::set_thinning_threshold(float value) {
  _internal_set_thinning_threshold(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.ModelTrust.thinning_threshold)
}

// float banding_threshold = 4;
inline void ModelTrust::clear_banding_threshold() {
  banding_threshold_ = 0;
}
inline float ModelTrust::_internal_banding_threshold() const {
  return banding_threshold_;
}
inline float ModelTrust::banding_threshold() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.ModelTrust.banding_threshold)
  return _internal_banding_threshold();
}
inline void ModelTrust::_internal_set_banding_threshold(float value) {
  
  banding_threshold_ = value;
}
inline void ModelTrust::set_banding_threshold(float value) {
  _internal_set_banding_threshold(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.ModelTrust.banding_threshold)
}

// -------------------------------------------------------------------

// TypeCategory

// string category = 1;
inline void TypeCategory::clear_category() {
  category_.ClearToEmpty();
}
inline const std::string& TypeCategory::category() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.TypeCategory.category)
  return _internal_category();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TypeCategory::set_category(ArgT0&& arg0, ArgT... args) {
 
 category_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.TypeCategory.category)
}
inline std::string* TypeCategory::mutable_category() {
  std::string* _s = _internal_mutable_category();
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.almanac.TypeCategory.category)
  return _s;
}
inline const std::string& TypeCategory::_internal_category() const {
  return category_.Get();
}
inline void TypeCategory::_internal_set_category(const std::string& value) {
  
  category_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TypeCategory::_internal_mutable_category() {
  
  return category_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TypeCategory::release_category() {
  // @@protoc_insertion_point(field_release:carbon.aimbot.almanac.TypeCategory.category)
  return category_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void TypeCategory::set_allocated_category(std::string* category) {
  if (category != nullptr) {
    
  } else {
    
  }
  category_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), category,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (category_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    category_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.aimbot.almanac.TypeCategory.category)
}

// .carbon.aimbot.almanac.CategoryClassification classification = 2;
inline void TypeCategory::clear_classification() {
  classification_ = 0;
}
inline ::carbon::aimbot::almanac::CategoryClassification TypeCategory::_internal_classification() const {
  return static_cast< ::carbon::aimbot::almanac::CategoryClassification >(classification_);
}
inline ::carbon::aimbot::almanac::CategoryClassification TypeCategory::classification() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.TypeCategory.classification)
  return _internal_classification();
}
inline void TypeCategory::_internal_set_classification(::carbon::aimbot::almanac::CategoryClassification value) {
  
  classification_ = value;
}
inline void TypeCategory::set_classification(::carbon::aimbot::almanac::CategoryClassification value) {
  _internal_set_classification(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.TypeCategory.classification)
}

// -------------------------------------------------------------------

// AlmanacTypeCategory

// .carbon.aimbot.almanac.TypeCategory type = 1;
inline bool AlmanacTypeCategory::_internal_has_type() const {
  return this != internal_default_instance() && type_ != nullptr;
}
inline bool AlmanacTypeCategory::has_type() const {
  return _internal_has_type();
}
inline void AlmanacTypeCategory::clear_type() {
  if (GetArenaForAllocation() == nullptr && type_ != nullptr) {
    delete type_;
  }
  type_ = nullptr;
}
inline const ::carbon::aimbot::almanac::TypeCategory& AlmanacTypeCategory::_internal_type() const {
  const ::carbon::aimbot::almanac::TypeCategory* p = type_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::almanac::TypeCategory&>(
      ::carbon::aimbot::almanac::_TypeCategory_default_instance_);
}
inline const ::carbon::aimbot::almanac::TypeCategory& AlmanacTypeCategory::type() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.AlmanacTypeCategory.type)
  return _internal_type();
}
inline void AlmanacTypeCategory::unsafe_arena_set_allocated_type(
    ::carbon::aimbot::almanac::TypeCategory* type) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(type_);
  }
  type_ = type;
  if (type) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.aimbot.almanac.AlmanacTypeCategory.type)
}
inline ::carbon::aimbot::almanac::TypeCategory* AlmanacTypeCategory::release_type() {
  
  ::carbon::aimbot::almanac::TypeCategory* temp = type_;
  type_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::almanac::TypeCategory* AlmanacTypeCategory::unsafe_arena_release_type() {
  // @@protoc_insertion_point(field_release:carbon.aimbot.almanac.AlmanacTypeCategory.type)
  
  ::carbon::aimbot::almanac::TypeCategory* temp = type_;
  type_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::almanac::TypeCategory* AlmanacTypeCategory::_internal_mutable_type() {
  
  if (type_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::almanac::TypeCategory>(GetArenaForAllocation());
    type_ = p;
  }
  return type_;
}
inline ::carbon::aimbot::almanac::TypeCategory* AlmanacTypeCategory::mutable_type() {
  ::carbon::aimbot::almanac::TypeCategory* _msg = _internal_mutable_type();
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.almanac.AlmanacTypeCategory.type)
  return _msg;
}
inline void AlmanacTypeCategory::set_allocated_type(::carbon::aimbot::almanac::TypeCategory* type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete type_;
  }
  if (type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::aimbot::almanac::TypeCategory>::GetOwningArena(type);
    if (message_arena != submessage_arena) {
      type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, type, submessage_arena);
    }
    
  } else {
    
  }
  type_ = type;
  // @@protoc_insertion_point(field_set_allocated:carbon.aimbot.almanac.AlmanacTypeCategory.type)
}

// repeated float sizes = 2;
inline int AlmanacTypeCategory::_internal_sizes_size() const {
  return sizes_.size();
}
inline int AlmanacTypeCategory::sizes_size() const {
  return _internal_sizes_size();
}
inline void AlmanacTypeCategory::clear_sizes() {
  sizes_.Clear();
}
inline float AlmanacTypeCategory::_internal_sizes(int index) const {
  return sizes_.Get(index);
}
inline float AlmanacTypeCategory::sizes(int index) const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.AlmanacTypeCategory.sizes)
  return _internal_sizes(index);
}
inline void AlmanacTypeCategory::set_sizes(int index, float value) {
  sizes_.Set(index, value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.AlmanacTypeCategory.sizes)
}
inline void AlmanacTypeCategory::_internal_add_sizes(float value) {
  sizes_.Add(value);
}
inline void AlmanacTypeCategory::add_sizes(float value) {
  _internal_add_sizes(value);
  // @@protoc_insertion_point(field_add:carbon.aimbot.almanac.AlmanacTypeCategory.sizes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
AlmanacTypeCategory::_internal_sizes() const {
  return sizes_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
AlmanacTypeCategory::sizes() const {
  // @@protoc_insertion_point(field_list:carbon.aimbot.almanac.AlmanacTypeCategory.sizes)
  return _internal_sizes();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
AlmanacTypeCategory::_internal_mutable_sizes() {
  return &sizes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
AlmanacTypeCategory::mutable_sizes() {
  // @@protoc_insertion_point(field_mutable_list:carbon.aimbot.almanac.AlmanacTypeCategory.sizes)
  return _internal_mutable_sizes();
}

// repeated .carbon.aimbot.almanac.Formula formulas = 3;
inline int AlmanacTypeCategory::_internal_formulas_size() const {
  return formulas_.size();
}
inline int AlmanacTypeCategory::formulas_size() const {
  return _internal_formulas_size();
}
inline void AlmanacTypeCategory::clear_formulas() {
  formulas_.Clear();
}
inline ::carbon::aimbot::almanac::Formula* AlmanacTypeCategory::mutable_formulas(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.almanac.AlmanacTypeCategory.formulas)
  return formulas_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::Formula >*
AlmanacTypeCategory::mutable_formulas() {
  // @@protoc_insertion_point(field_mutable_list:carbon.aimbot.almanac.AlmanacTypeCategory.formulas)
  return &formulas_;
}
inline const ::carbon::aimbot::almanac::Formula& AlmanacTypeCategory::_internal_formulas(int index) const {
  return formulas_.Get(index);
}
inline const ::carbon::aimbot::almanac::Formula& AlmanacTypeCategory::formulas(int index) const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.AlmanacTypeCategory.formulas)
  return _internal_formulas(index);
}
inline ::carbon::aimbot::almanac::Formula* AlmanacTypeCategory::_internal_add_formulas() {
  return formulas_.Add();
}
inline ::carbon::aimbot::almanac::Formula* AlmanacTypeCategory::add_formulas() {
  ::carbon::aimbot::almanac::Formula* _add = _internal_add_formulas();
  // @@protoc_insertion_point(field_add:carbon.aimbot.almanac.AlmanacTypeCategory.formulas)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::Formula >&
AlmanacTypeCategory::formulas() const {
  // @@protoc_insertion_point(field_list:carbon.aimbot.almanac.AlmanacTypeCategory.formulas)
  return formulas_;
}

// -------------------------------------------------------------------

// DiscriminatorTypeCategory

// .carbon.aimbot.almanac.TypeCategory type = 1;
inline bool DiscriminatorTypeCategory::_internal_has_type() const {
  return this != internal_default_instance() && type_ != nullptr;
}
inline bool DiscriminatorTypeCategory::has_type() const {
  return _internal_has_type();
}
inline void DiscriminatorTypeCategory::clear_type() {
  if (GetArenaForAllocation() == nullptr && type_ != nullptr) {
    delete type_;
  }
  type_ = nullptr;
}
inline const ::carbon::aimbot::almanac::TypeCategory& DiscriminatorTypeCategory::_internal_type() const {
  const ::carbon::aimbot::almanac::TypeCategory* p = type_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::almanac::TypeCategory&>(
      ::carbon::aimbot::almanac::_TypeCategory_default_instance_);
}
inline const ::carbon::aimbot::almanac::TypeCategory& DiscriminatorTypeCategory::type() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.DiscriminatorTypeCategory.type)
  return _internal_type();
}
inline void DiscriminatorTypeCategory::unsafe_arena_set_allocated_type(
    ::carbon::aimbot::almanac::TypeCategory* type) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(type_);
  }
  type_ = type;
  if (type) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.aimbot.almanac.DiscriminatorTypeCategory.type)
}
inline ::carbon::aimbot::almanac::TypeCategory* DiscriminatorTypeCategory::release_type() {
  
  ::carbon::aimbot::almanac::TypeCategory* temp = type_;
  type_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::almanac::TypeCategory* DiscriminatorTypeCategory::unsafe_arena_release_type() {
  // @@protoc_insertion_point(field_release:carbon.aimbot.almanac.DiscriminatorTypeCategory.type)
  
  ::carbon::aimbot::almanac::TypeCategory* temp = type_;
  type_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::almanac::TypeCategory* DiscriminatorTypeCategory::_internal_mutable_type() {
  
  if (type_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::almanac::TypeCategory>(GetArenaForAllocation());
    type_ = p;
  }
  return type_;
}
inline ::carbon::aimbot::almanac::TypeCategory* DiscriminatorTypeCategory::mutable_type() {
  ::carbon::aimbot::almanac::TypeCategory* _msg = _internal_mutable_type();
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.almanac.DiscriminatorTypeCategory.type)
  return _msg;
}
inline void DiscriminatorTypeCategory::set_allocated_type(::carbon::aimbot::almanac::TypeCategory* type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete type_;
  }
  if (type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::aimbot::almanac::TypeCategory>::GetOwningArena(type);
    if (message_arena != submessage_arena) {
      type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, type, submessage_arena);
    }
    
  } else {
    
  }
  type_ = type;
  // @@protoc_insertion_point(field_set_allocated:carbon.aimbot.almanac.DiscriminatorTypeCategory.type)
}

// repeated .carbon.aimbot.almanac.Trust trusts = 2;
inline int DiscriminatorTypeCategory::_internal_trusts_size() const {
  return trusts_.size();
}
inline int DiscriminatorTypeCategory::trusts_size() const {
  return _internal_trusts_size();
}
inline void DiscriminatorTypeCategory::clear_trusts() {
  trusts_.Clear();
}
inline ::carbon::aimbot::almanac::Trust* DiscriminatorTypeCategory::mutable_trusts(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.almanac.DiscriminatorTypeCategory.trusts)
  return trusts_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::Trust >*
DiscriminatorTypeCategory::mutable_trusts() {
  // @@protoc_insertion_point(field_mutable_list:carbon.aimbot.almanac.DiscriminatorTypeCategory.trusts)
  return &trusts_;
}
inline const ::carbon::aimbot::almanac::Trust& DiscriminatorTypeCategory::_internal_trusts(int index) const {
  return trusts_.Get(index);
}
inline const ::carbon::aimbot::almanac::Trust& DiscriminatorTypeCategory::trusts(int index) const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.DiscriminatorTypeCategory.trusts)
  return _internal_trusts(index);
}
inline ::carbon::aimbot::almanac::Trust* DiscriminatorTypeCategory::_internal_add_trusts() {
  return trusts_.Add();
}
inline ::carbon::aimbot::almanac::Trust* DiscriminatorTypeCategory::add_trusts() {
  ::carbon::aimbot::almanac::Trust* _add = _internal_add_trusts();
  // @@protoc_insertion_point(field_add:carbon.aimbot.almanac.DiscriminatorTypeCategory.trusts)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::Trust >&
DiscriminatorTypeCategory::trusts() const {
  // @@protoc_insertion_point(field_list:carbon.aimbot.almanac.DiscriminatorTypeCategory.trusts)
  return trusts_;
}

// -------------------------------------------------------------------

// ModelinatorTypeCategory

// .carbon.aimbot.almanac.TypeCategory type = 1;
inline bool ModelinatorTypeCategory::_internal_has_type() const {
  return this != internal_default_instance() && type_ != nullptr;
}
inline bool ModelinatorTypeCategory::has_type() const {
  return _internal_has_type();
}
inline void ModelinatorTypeCategory::clear_type() {
  if (GetArenaForAllocation() == nullptr && type_ != nullptr) {
    delete type_;
  }
  type_ = nullptr;
}
inline const ::carbon::aimbot::almanac::TypeCategory& ModelinatorTypeCategory::_internal_type() const {
  const ::carbon::aimbot::almanac::TypeCategory* p = type_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::almanac::TypeCategory&>(
      ::carbon::aimbot::almanac::_TypeCategory_default_instance_);
}
inline const ::carbon::aimbot::almanac::TypeCategory& ModelinatorTypeCategory::type() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.ModelinatorTypeCategory.type)
  return _internal_type();
}
inline void ModelinatorTypeCategory::unsafe_arena_set_allocated_type(
    ::carbon::aimbot::almanac::TypeCategory* type) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(type_);
  }
  type_ = type;
  if (type) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.aimbot.almanac.ModelinatorTypeCategory.type)
}
inline ::carbon::aimbot::almanac::TypeCategory* ModelinatorTypeCategory::release_type() {
  
  ::carbon::aimbot::almanac::TypeCategory* temp = type_;
  type_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::almanac::TypeCategory* ModelinatorTypeCategory::unsafe_arena_release_type() {
  // @@protoc_insertion_point(field_release:carbon.aimbot.almanac.ModelinatorTypeCategory.type)
  
  ::carbon::aimbot::almanac::TypeCategory* temp = type_;
  type_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::almanac::TypeCategory* ModelinatorTypeCategory::_internal_mutable_type() {
  
  if (type_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::almanac::TypeCategory>(GetArenaForAllocation());
    type_ = p;
  }
  return type_;
}
inline ::carbon::aimbot::almanac::TypeCategory* ModelinatorTypeCategory::mutable_type() {
  ::carbon::aimbot::almanac::TypeCategory* _msg = _internal_mutable_type();
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.almanac.ModelinatorTypeCategory.type)
  return _msg;
}
inline void ModelinatorTypeCategory::set_allocated_type(::carbon::aimbot::almanac::TypeCategory* type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete type_;
  }
  if (type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::aimbot::almanac::TypeCategory>::GetOwningArena(type);
    if (message_arena != submessage_arena) {
      type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, type, submessage_arena);
    }
    
  } else {
    
  }
  type_ = type;
  // @@protoc_insertion_point(field_set_allocated:carbon.aimbot.almanac.ModelinatorTypeCategory.type)
}

// repeated .carbon.aimbot.almanac.ModelTrust trusts = 2;
inline int ModelinatorTypeCategory::_internal_trusts_size() const {
  return trusts_.size();
}
inline int ModelinatorTypeCategory::trusts_size() const {
  return _internal_trusts_size();
}
inline void ModelinatorTypeCategory::clear_trusts() {
  trusts_.Clear();
}
inline ::carbon::aimbot::almanac::ModelTrust* ModelinatorTypeCategory::mutable_trusts(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.almanac.ModelinatorTypeCategory.trusts)
  return trusts_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::ModelTrust >*
ModelinatorTypeCategory::mutable_trusts() {
  // @@protoc_insertion_point(field_mutable_list:carbon.aimbot.almanac.ModelinatorTypeCategory.trusts)
  return &trusts_;
}
inline const ::carbon::aimbot::almanac::ModelTrust& ModelinatorTypeCategory::_internal_trusts(int index) const {
  return trusts_.Get(index);
}
inline const ::carbon::aimbot::almanac::ModelTrust& ModelinatorTypeCategory::trusts(int index) const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.ModelinatorTypeCategory.trusts)
  return _internal_trusts(index);
}
inline ::carbon::aimbot::almanac::ModelTrust* ModelinatorTypeCategory::_internal_add_trusts() {
  return trusts_.Add();
}
inline ::carbon::aimbot::almanac::ModelTrust* ModelinatorTypeCategory::add_trusts() {
  ::carbon::aimbot::almanac::ModelTrust* _add = _internal_add_trusts();
  // @@protoc_insertion_point(field_add:carbon.aimbot.almanac.ModelinatorTypeCategory.trusts)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::ModelTrust >&
ModelinatorTypeCategory::trusts() const {
  // @@protoc_insertion_point(field_list:carbon.aimbot.almanac.ModelinatorTypeCategory.trusts)
  return trusts_;
}

// -------------------------------------------------------------------

// AlmanacConfig

// string id = 1;
inline void AlmanacConfig::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& AlmanacConfig::id() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.AlmanacConfig.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AlmanacConfig::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.AlmanacConfig.id)
}
inline std::string* AlmanacConfig::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.almanac.AlmanacConfig.id)
  return _s;
}
inline const std::string& AlmanacConfig::_internal_id() const {
  return id_.Get();
}
inline void AlmanacConfig::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AlmanacConfig::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AlmanacConfig::release_id() {
  // @@protoc_insertion_point(field_release:carbon.aimbot.almanac.AlmanacConfig.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AlmanacConfig::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.aimbot.almanac.AlmanacConfig.id)
}

// string name = 2;
inline void AlmanacConfig::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& AlmanacConfig::name() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.AlmanacConfig.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AlmanacConfig::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.AlmanacConfig.name)
}
inline std::string* AlmanacConfig::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.almanac.AlmanacConfig.name)
  return _s;
}
inline const std::string& AlmanacConfig::_internal_name() const {
  return name_.Get();
}
inline void AlmanacConfig::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AlmanacConfig::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AlmanacConfig::release_name() {
  // @@protoc_insertion_point(field_release:carbon.aimbot.almanac.AlmanacConfig.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AlmanacConfig::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.aimbot.almanac.AlmanacConfig.name)
}

// bool protected = 3;
inline void AlmanacConfig::clear_protected_() {
  protected__ = false;
}
inline bool AlmanacConfig::_internal_protected_() const {
  return protected__;
}
inline bool AlmanacConfig::protected_() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.AlmanacConfig.protected)
  return _internal_protected_();
}
inline void AlmanacConfig::_internal_set_protected_(bool value) {
  
  protected__ = value;
}
inline void AlmanacConfig::set_protected_(bool value) {
  _internal_set_protected_(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.AlmanacConfig.protected)
}

// uint64 updated_ts = 4;
inline void AlmanacConfig::clear_updated_ts() {
  updated_ts_ = uint64_t{0u};
}
inline uint64_t AlmanacConfig::_internal_updated_ts() const {
  return updated_ts_;
}
inline uint64_t AlmanacConfig::updated_ts() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.AlmanacConfig.updated_ts)
  return _internal_updated_ts();
}
inline void AlmanacConfig::_internal_set_updated_ts(uint64_t value) {
  
  updated_ts_ = value;
}
inline void AlmanacConfig::set_updated_ts(uint64_t value) {
  _internal_set_updated_ts(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.AlmanacConfig.updated_ts)
}

// repeated .carbon.aimbot.almanac.AlmanacTypeCategory categories = 5;
inline int AlmanacConfig::_internal_categories_size() const {
  return categories_.size();
}
inline int AlmanacConfig::categories_size() const {
  return _internal_categories_size();
}
inline void AlmanacConfig::clear_categories() {
  categories_.Clear();
}
inline ::carbon::aimbot::almanac::AlmanacTypeCategory* AlmanacConfig::mutable_categories(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.almanac.AlmanacConfig.categories)
  return categories_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::AlmanacTypeCategory >*
AlmanacConfig::mutable_categories() {
  // @@protoc_insertion_point(field_mutable_list:carbon.aimbot.almanac.AlmanacConfig.categories)
  return &categories_;
}
inline const ::carbon::aimbot::almanac::AlmanacTypeCategory& AlmanacConfig::_internal_categories(int index) const {
  return categories_.Get(index);
}
inline const ::carbon::aimbot::almanac::AlmanacTypeCategory& AlmanacConfig::categories(int index) const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.AlmanacConfig.categories)
  return _internal_categories(index);
}
inline ::carbon::aimbot::almanac::AlmanacTypeCategory* AlmanacConfig::_internal_add_categories() {
  return categories_.Add();
}
inline ::carbon::aimbot::almanac::AlmanacTypeCategory* AlmanacConfig::add_categories() {
  ::carbon::aimbot::almanac::AlmanacTypeCategory* _add = _internal_add_categories();
  // @@protoc_insertion_point(field_add:carbon.aimbot.almanac.AlmanacConfig.categories)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::AlmanacTypeCategory >&
AlmanacConfig::categories() const {
  // @@protoc_insertion_point(field_list:carbon.aimbot.almanac.AlmanacConfig.categories)
  return categories_;
}

// -------------------------------------------------------------------

// DiscriminatorConfig

// string id = 1;
inline void DiscriminatorConfig::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& DiscriminatorConfig::id() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.DiscriminatorConfig.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DiscriminatorConfig::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.DiscriminatorConfig.id)
}
inline std::string* DiscriminatorConfig::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.almanac.DiscriminatorConfig.id)
  return _s;
}
inline const std::string& DiscriminatorConfig::_internal_id() const {
  return id_.Get();
}
inline void DiscriminatorConfig::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DiscriminatorConfig::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DiscriminatorConfig::release_id() {
  // @@protoc_insertion_point(field_release:carbon.aimbot.almanac.DiscriminatorConfig.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DiscriminatorConfig::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.aimbot.almanac.DiscriminatorConfig.id)
}

// string name = 2;
inline void DiscriminatorConfig::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& DiscriminatorConfig::name() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.DiscriminatorConfig.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DiscriminatorConfig::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.DiscriminatorConfig.name)
}
inline std::string* DiscriminatorConfig::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.almanac.DiscriminatorConfig.name)
  return _s;
}
inline const std::string& DiscriminatorConfig::_internal_name() const {
  return name_.Get();
}
inline void DiscriminatorConfig::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DiscriminatorConfig::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DiscriminatorConfig::release_name() {
  // @@protoc_insertion_point(field_release:carbon.aimbot.almanac.DiscriminatorConfig.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DiscriminatorConfig::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.aimbot.almanac.DiscriminatorConfig.name)
}

// bool protected = 3;
inline void DiscriminatorConfig::clear_protected_() {
  protected__ = false;
}
inline bool DiscriminatorConfig::_internal_protected_() const {
  return protected__;
}
inline bool DiscriminatorConfig::protected_() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.DiscriminatorConfig.protected)
  return _internal_protected_();
}
inline void DiscriminatorConfig::_internal_set_protected_(bool value) {
  
  protected__ = value;
}
inline void DiscriminatorConfig::set_protected_(bool value) {
  _internal_set_protected_(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.DiscriminatorConfig.protected)
}

// uint64 updated_ts = 4;
inline void DiscriminatorConfig::clear_updated_ts() {
  updated_ts_ = uint64_t{0u};
}
inline uint64_t DiscriminatorConfig::_internal_updated_ts() const {
  return updated_ts_;
}
inline uint64_t DiscriminatorConfig::updated_ts() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.DiscriminatorConfig.updated_ts)
  return _internal_updated_ts();
}
inline void DiscriminatorConfig::_internal_set_updated_ts(uint64_t value) {
  
  updated_ts_ = value;
}
inline void DiscriminatorConfig::set_updated_ts(uint64_t value) {
  _internal_set_updated_ts(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.DiscriminatorConfig.updated_ts)
}

// repeated .carbon.aimbot.almanac.DiscriminatorTypeCategory categories = 5;
inline int DiscriminatorConfig::_internal_categories_size() const {
  return categories_.size();
}
inline int DiscriminatorConfig::categories_size() const {
  return _internal_categories_size();
}
inline void DiscriminatorConfig::clear_categories() {
  categories_.Clear();
}
inline ::carbon::aimbot::almanac::DiscriminatorTypeCategory* DiscriminatorConfig::mutable_categories(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.almanac.DiscriminatorConfig.categories)
  return categories_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::DiscriminatorTypeCategory >*
DiscriminatorConfig::mutable_categories() {
  // @@protoc_insertion_point(field_mutable_list:carbon.aimbot.almanac.DiscriminatorConfig.categories)
  return &categories_;
}
inline const ::carbon::aimbot::almanac::DiscriminatorTypeCategory& DiscriminatorConfig::_internal_categories(int index) const {
  return categories_.Get(index);
}
inline const ::carbon::aimbot::almanac::DiscriminatorTypeCategory& DiscriminatorConfig::categories(int index) const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.DiscriminatorConfig.categories)
  return _internal_categories(index);
}
inline ::carbon::aimbot::almanac::DiscriminatorTypeCategory* DiscriminatorConfig::_internal_add_categories() {
  return categories_.Add();
}
inline ::carbon::aimbot::almanac::DiscriminatorTypeCategory* DiscriminatorConfig::add_categories() {
  ::carbon::aimbot::almanac::DiscriminatorTypeCategory* _add = _internal_add_categories();
  // @@protoc_insertion_point(field_add:carbon.aimbot.almanac.DiscriminatorConfig.categories)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::DiscriminatorTypeCategory >&
DiscriminatorConfig::categories() const {
  // @@protoc_insertion_point(field_list:carbon.aimbot.almanac.DiscriminatorConfig.categories)
  return categories_;
}

// -------------------------------------------------------------------

// ModelinatorConfig

// string model_id = 1;
inline void ModelinatorConfig::clear_model_id() {
  model_id_.ClearToEmpty();
}
inline const std::string& ModelinatorConfig::model_id() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.ModelinatorConfig.model_id)
  return _internal_model_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelinatorConfig::set_model_id(ArgT0&& arg0, ArgT... args) {
 
 model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.ModelinatorConfig.model_id)
}
inline std::string* ModelinatorConfig::mutable_model_id() {
  std::string* _s = _internal_mutable_model_id();
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.almanac.ModelinatorConfig.model_id)
  return _s;
}
inline const std::string& ModelinatorConfig::_internal_model_id() const {
  return model_id_.Get();
}
inline void ModelinatorConfig::_internal_set_model_id(const std::string& value) {
  
  model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelinatorConfig::_internal_mutable_model_id() {
  
  return model_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelinatorConfig::release_model_id() {
  // @@protoc_insertion_point(field_release:carbon.aimbot.almanac.ModelinatorConfig.model_id)
  return model_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelinatorConfig::set_allocated_model_id(std::string* model_id) {
  if (model_id != nullptr) {
    
  } else {
    
  }
  model_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.aimbot.almanac.ModelinatorConfig.model_id)
}

// string crop_id = 2;
inline void ModelinatorConfig::clear_crop_id() {
  crop_id_.ClearToEmpty();
}
inline const std::string& ModelinatorConfig::crop_id() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.ModelinatorConfig.crop_id)
  return _internal_crop_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelinatorConfig::set_crop_id(ArgT0&& arg0, ArgT... args) {
 
 crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.ModelinatorConfig.crop_id)
}
inline std::string* ModelinatorConfig::mutable_crop_id() {
  std::string* _s = _internal_mutable_crop_id();
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.almanac.ModelinatorConfig.crop_id)
  return _s;
}
inline const std::string& ModelinatorConfig::_internal_crop_id() const {
  return crop_id_.Get();
}
inline void ModelinatorConfig::_internal_set_crop_id(const std::string& value) {
  
  crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelinatorConfig::_internal_mutable_crop_id() {
  
  return crop_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelinatorConfig::release_crop_id() {
  // @@protoc_insertion_point(field_release:carbon.aimbot.almanac.ModelinatorConfig.crop_id)
  return crop_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelinatorConfig::set_allocated_crop_id(std::string* crop_id) {
  if (crop_id != nullptr) {
    
  } else {
    
  }
  crop_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.aimbot.almanac.ModelinatorConfig.crop_id)
}

// bool modified = 3;
inline void ModelinatorConfig::clear_modified() {
  modified_ = false;
}
inline bool ModelinatorConfig::_internal_modified() const {
  return modified_;
}
inline bool ModelinatorConfig::modified() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.ModelinatorConfig.modified)
  return _internal_modified();
}
inline void ModelinatorConfig::_internal_set_modified(bool value) {
  
  modified_ = value;
}
inline void ModelinatorConfig::set_modified(bool value) {
  _internal_set_modified(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.almanac.ModelinatorConfig.modified)
}

// repeated .carbon.aimbot.almanac.ModelinatorTypeCategory categories = 4;
inline int ModelinatorConfig::_internal_categories_size() const {
  return categories_.size();
}
inline int ModelinatorConfig::categories_size() const {
  return _internal_categories_size();
}
inline void ModelinatorConfig::clear_categories() {
  categories_.Clear();
}
inline ::carbon::aimbot::almanac::ModelinatorTypeCategory* ModelinatorConfig::mutable_categories(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.almanac.ModelinatorConfig.categories)
  return categories_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::ModelinatorTypeCategory >*
ModelinatorConfig::mutable_categories() {
  // @@protoc_insertion_point(field_mutable_list:carbon.aimbot.almanac.ModelinatorConfig.categories)
  return &categories_;
}
inline const ::carbon::aimbot::almanac::ModelinatorTypeCategory& ModelinatorConfig::_internal_categories(int index) const {
  return categories_.Get(index);
}
inline const ::carbon::aimbot::almanac::ModelinatorTypeCategory& ModelinatorConfig::categories(int index) const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.almanac.ModelinatorConfig.categories)
  return _internal_categories(index);
}
inline ::carbon::aimbot::almanac::ModelinatorTypeCategory* ModelinatorConfig::_internal_add_categories() {
  return categories_.Add();
}
inline ::carbon::aimbot::almanac::ModelinatorTypeCategory* ModelinatorConfig::add_categories() {
  ::carbon::aimbot::almanac::ModelinatorTypeCategory* _add = _internal_add_categories();
  // @@protoc_insertion_point(field_add:carbon.aimbot.almanac.ModelinatorConfig.categories)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::almanac::ModelinatorTypeCategory >&
ModelinatorConfig::categories() const {
  // @@protoc_insertion_point(field_list:carbon.aimbot.almanac.ModelinatorConfig.categories)
  return categories_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace almanac
}  // namespace aimbot
}  // namespace carbon

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::carbon::aimbot::almanac::CategoryClassification> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::aimbot::almanac::CategoryClassification>() {
  return ::carbon::aimbot::almanac::CategoryClassification_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_proto_2falmanac_2falmanac_2eproto

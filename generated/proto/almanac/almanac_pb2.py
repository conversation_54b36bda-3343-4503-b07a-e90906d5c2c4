# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/almanac/almanac.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/almanac/almanac.proto',
  package='carbon.aimbot.almanac',
  syntax='proto3',
  serialized_options=b'Z\rproto/almanac',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1bproto/almanac/almanac.proto\x12\x15\x63\x61rbon.aimbot.almanac\"\x91\x01\n\x07\x46ormula\x12\x12\n\nmultiplier\x18\x01 \x01(\x02\x12\x0e\n\x06offset\x18\x02 \x01(\x02\x12\x10\n\x08\x65xponent\x18\x03 \x01(\x02\x12\x1c\n\x14\x66ine_tune_multiplier\x18\x04 \x01(\r\x12\x10\n\x08max_time\x18\x05 \x01(\r\x12 \n\x18\x66ine_tune_multiplier_val\x18\x06 \x01(\x02\")\n\x05Trust\x12\x11\n\tignorable\x18\x01 \x01(\x08\x12\r\n\x05\x61void\x18\x02 \x01(\x08\"o\n\nModelTrust\x12\x0f\n\x07min_doo\x18\x01 \x01(\x02\x12\x19\n\x11weeding_threshold\x18\x02 \x01(\x02\x12\x1a\n\x12thinning_threshold\x18\x03 \x01(\x02\x12\x19\n\x11\x62\x61nding_threshold\x18\x04 \x01(\x02\"g\n\x0cTypeCategory\x12\x10\n\x08\x63\x61tegory\x18\x01 \x01(\t\x12\x45\n\x0e\x63lassification\x18\x02 \x01(\x0e\x32-.carbon.aimbot.almanac.CategoryClassification\"\x89\x01\n\x13\x41lmanacTypeCategory\x12\x31\n\x04type\x18\x01 \x01(\x0b\x32#.carbon.aimbot.almanac.TypeCategory\x12\r\n\x05sizes\x18\x02 \x03(\x02\x12\x30\n\x08\x66ormulas\x18\x03 \x03(\x0b\x32\x1e.carbon.aimbot.almanac.Formula\"|\n\x19\x44iscriminatorTypeCategory\x12\x31\n\x04type\x18\x01 \x01(\x0b\x32#.carbon.aimbot.almanac.TypeCategory\x12,\n\x06trusts\x18\x02 \x03(\x0b\x32\x1c.carbon.aimbot.almanac.Trust\"\x7f\n\x17ModelinatorTypeCategory\x12\x31\n\x04type\x18\x01 \x01(\x0b\x32#.carbon.aimbot.almanac.TypeCategory\x12\x31\n\x06trusts\x18\x02 \x03(\x0b\x32!.carbon.aimbot.almanac.ModelTrust\"\x90\x01\n\rAlmanacConfig\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x11\n\tprotected\x18\x03 \x01(\x08\x12\x12\n\nupdated_ts\x18\x04 \x01(\x04\x12>\n\ncategories\x18\x05 \x03(\x0b\x32*.carbon.aimbot.almanac.AlmanacTypeCategory\"\x9c\x01\n\x13\x44iscriminatorConfig\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x11\n\tprotected\x18\x03 \x01(\x08\x12\x12\n\nupdated_ts\x18\x04 \x01(\x04\x12\x44\n\ncategories\x18\x05 \x03(\x0b\x32\x30.carbon.aimbot.almanac.DiscriminatorTypeCategory\"\x8c\x01\n\x11ModelinatorConfig\x12\x10\n\x08model_id\x18\x01 \x01(\t\x12\x0f\n\x07\x63rop_id\x18\x02 \x01(\t\x12\x10\n\x08modified\x18\x03 \x01(\x08\x12\x42\n\ncategories\x18\x04 \x03(\x0b\x32..carbon.aimbot.almanac.ModelinatorTypeCategory*>\n\x16\x43\x61tegoryClassification\x12\x11\n\rCATEGORY_WEED\x10\x00\x12\x11\n\rCATEGORY_CROP\x10\x01\x42\x0fZ\rproto/almanacb\x06proto3'
)

_CATEGORYCLASSIFICATION = _descriptor.EnumDescriptor(
  name='CategoryClassification',
  full_name='carbon.aimbot.almanac.CategoryClassification',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CATEGORY_WEED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CATEGORY_CROP', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1307,
  serialized_end=1369,
)
_sym_db.RegisterEnumDescriptor(_CATEGORYCLASSIFICATION)

CategoryClassification = enum_type_wrapper.EnumTypeWrapper(_CATEGORYCLASSIFICATION)
CATEGORY_WEED = 0
CATEGORY_CROP = 1



_FORMULA = _descriptor.Descriptor(
  name='Formula',
  full_name='carbon.aimbot.almanac.Formula',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='multiplier', full_name='carbon.aimbot.almanac.Formula.multiplier', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='offset', full_name='carbon.aimbot.almanac.Formula.offset', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='exponent', full_name='carbon.aimbot.almanac.Formula.exponent', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fine_tune_multiplier', full_name='carbon.aimbot.almanac.Formula.fine_tune_multiplier', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_time', full_name='carbon.aimbot.almanac.Formula.max_time', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fine_tune_multiplier_val', full_name='carbon.aimbot.almanac.Formula.fine_tune_multiplier_val', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=55,
  serialized_end=200,
)


_TRUST = _descriptor.Descriptor(
  name='Trust',
  full_name='carbon.aimbot.almanac.Trust',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ignorable', full_name='carbon.aimbot.almanac.Trust.ignorable', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='avoid', full_name='carbon.aimbot.almanac.Trust.avoid', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=202,
  serialized_end=243,
)


_MODELTRUST = _descriptor.Descriptor(
  name='ModelTrust',
  full_name='carbon.aimbot.almanac.ModelTrust',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='min_doo', full_name='carbon.aimbot.almanac.ModelTrust.min_doo', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weeding_threshold', full_name='carbon.aimbot.almanac.ModelTrust.weeding_threshold', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thinning_threshold', full_name='carbon.aimbot.almanac.ModelTrust.thinning_threshold', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='banding_threshold', full_name='carbon.aimbot.almanac.ModelTrust.banding_threshold', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=245,
  serialized_end=356,
)


_TYPECATEGORY = _descriptor.Descriptor(
  name='TypeCategory',
  full_name='carbon.aimbot.almanac.TypeCategory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='category', full_name='carbon.aimbot.almanac.TypeCategory.category', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='classification', full_name='carbon.aimbot.almanac.TypeCategory.classification', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=358,
  serialized_end=461,
)


_ALMANACTYPECATEGORY = _descriptor.Descriptor(
  name='AlmanacTypeCategory',
  full_name='carbon.aimbot.almanac.AlmanacTypeCategory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='carbon.aimbot.almanac.AlmanacTypeCategory.type', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sizes', full_name='carbon.aimbot.almanac.AlmanacTypeCategory.sizes', index=1,
      number=2, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='formulas', full_name='carbon.aimbot.almanac.AlmanacTypeCategory.formulas', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=464,
  serialized_end=601,
)


_DISCRIMINATORTYPECATEGORY = _descriptor.Descriptor(
  name='DiscriminatorTypeCategory',
  full_name='carbon.aimbot.almanac.DiscriminatorTypeCategory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='carbon.aimbot.almanac.DiscriminatorTypeCategory.type', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='trusts', full_name='carbon.aimbot.almanac.DiscriminatorTypeCategory.trusts', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=603,
  serialized_end=727,
)


_MODELINATORTYPECATEGORY = _descriptor.Descriptor(
  name='ModelinatorTypeCategory',
  full_name='carbon.aimbot.almanac.ModelinatorTypeCategory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='carbon.aimbot.almanac.ModelinatorTypeCategory.type', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='trusts', full_name='carbon.aimbot.almanac.ModelinatorTypeCategory.trusts', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=729,
  serialized_end=856,
)


_ALMANACCONFIG = _descriptor.Descriptor(
  name='AlmanacConfig',
  full_name='carbon.aimbot.almanac.AlmanacConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.aimbot.almanac.AlmanacConfig.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.aimbot.almanac.AlmanacConfig.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='protected', full_name='carbon.aimbot.almanac.AlmanacConfig.protected', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='updated_ts', full_name='carbon.aimbot.almanac.AlmanacConfig.updated_ts', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='categories', full_name='carbon.aimbot.almanac.AlmanacConfig.categories', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=859,
  serialized_end=1003,
)


_DISCRIMINATORCONFIG = _descriptor.Descriptor(
  name='DiscriminatorConfig',
  full_name='carbon.aimbot.almanac.DiscriminatorConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.aimbot.almanac.DiscriminatorConfig.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.aimbot.almanac.DiscriminatorConfig.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='protected', full_name='carbon.aimbot.almanac.DiscriminatorConfig.protected', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='updated_ts', full_name='carbon.aimbot.almanac.DiscriminatorConfig.updated_ts', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='categories', full_name='carbon.aimbot.almanac.DiscriminatorConfig.categories', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1006,
  serialized_end=1162,
)


_MODELINATORCONFIG = _descriptor.Descriptor(
  name='ModelinatorConfig',
  full_name='carbon.aimbot.almanac.ModelinatorConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='model_id', full_name='carbon.aimbot.almanac.ModelinatorConfig.model_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_id', full_name='carbon.aimbot.almanac.ModelinatorConfig.crop_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='modified', full_name='carbon.aimbot.almanac.ModelinatorConfig.modified', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='categories', full_name='carbon.aimbot.almanac.ModelinatorConfig.categories', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1165,
  serialized_end=1305,
)

_TYPECATEGORY.fields_by_name['classification'].enum_type = _CATEGORYCLASSIFICATION
_ALMANACTYPECATEGORY.fields_by_name['type'].message_type = _TYPECATEGORY
_ALMANACTYPECATEGORY.fields_by_name['formulas'].message_type = _FORMULA
_DISCRIMINATORTYPECATEGORY.fields_by_name['type'].message_type = _TYPECATEGORY
_DISCRIMINATORTYPECATEGORY.fields_by_name['trusts'].message_type = _TRUST
_MODELINATORTYPECATEGORY.fields_by_name['type'].message_type = _TYPECATEGORY
_MODELINATORTYPECATEGORY.fields_by_name['trusts'].message_type = _MODELTRUST
_ALMANACCONFIG.fields_by_name['categories'].message_type = _ALMANACTYPECATEGORY
_DISCRIMINATORCONFIG.fields_by_name['categories'].message_type = _DISCRIMINATORTYPECATEGORY
_MODELINATORCONFIG.fields_by_name['categories'].message_type = _MODELINATORTYPECATEGORY
DESCRIPTOR.message_types_by_name['Formula'] = _FORMULA
DESCRIPTOR.message_types_by_name['Trust'] = _TRUST
DESCRIPTOR.message_types_by_name['ModelTrust'] = _MODELTRUST
DESCRIPTOR.message_types_by_name['TypeCategory'] = _TYPECATEGORY
DESCRIPTOR.message_types_by_name['AlmanacTypeCategory'] = _ALMANACTYPECATEGORY
DESCRIPTOR.message_types_by_name['DiscriminatorTypeCategory'] = _DISCRIMINATORTYPECATEGORY
DESCRIPTOR.message_types_by_name['ModelinatorTypeCategory'] = _MODELINATORTYPECATEGORY
DESCRIPTOR.message_types_by_name['AlmanacConfig'] = _ALMANACCONFIG
DESCRIPTOR.message_types_by_name['DiscriminatorConfig'] = _DISCRIMINATORCONFIG
DESCRIPTOR.message_types_by_name['ModelinatorConfig'] = _MODELINATORCONFIG
DESCRIPTOR.enum_types_by_name['CategoryClassification'] = _CATEGORYCLASSIFICATION
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Formula = _reflection.GeneratedProtocolMessageType('Formula', (_message.Message,), {
  'DESCRIPTOR' : _FORMULA,
  '__module__' : 'proto.almanac.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.Formula)
  })
_sym_db.RegisterMessage(Formula)

Trust = _reflection.GeneratedProtocolMessageType('Trust', (_message.Message,), {
  'DESCRIPTOR' : _TRUST,
  '__module__' : 'proto.almanac.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.Trust)
  })
_sym_db.RegisterMessage(Trust)

ModelTrust = _reflection.GeneratedProtocolMessageType('ModelTrust', (_message.Message,), {
  'DESCRIPTOR' : _MODELTRUST,
  '__module__' : 'proto.almanac.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.ModelTrust)
  })
_sym_db.RegisterMessage(ModelTrust)

TypeCategory = _reflection.GeneratedProtocolMessageType('TypeCategory', (_message.Message,), {
  'DESCRIPTOR' : _TYPECATEGORY,
  '__module__' : 'proto.almanac.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.TypeCategory)
  })
_sym_db.RegisterMessage(TypeCategory)

AlmanacTypeCategory = _reflection.GeneratedProtocolMessageType('AlmanacTypeCategory', (_message.Message,), {
  'DESCRIPTOR' : _ALMANACTYPECATEGORY,
  '__module__' : 'proto.almanac.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.AlmanacTypeCategory)
  })
_sym_db.RegisterMessage(AlmanacTypeCategory)

DiscriminatorTypeCategory = _reflection.GeneratedProtocolMessageType('DiscriminatorTypeCategory', (_message.Message,), {
  'DESCRIPTOR' : _DISCRIMINATORTYPECATEGORY,
  '__module__' : 'proto.almanac.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.DiscriminatorTypeCategory)
  })
_sym_db.RegisterMessage(DiscriminatorTypeCategory)

ModelinatorTypeCategory = _reflection.GeneratedProtocolMessageType('ModelinatorTypeCategory', (_message.Message,), {
  'DESCRIPTOR' : _MODELINATORTYPECATEGORY,
  '__module__' : 'proto.almanac.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.ModelinatorTypeCategory)
  })
_sym_db.RegisterMessage(ModelinatorTypeCategory)

AlmanacConfig = _reflection.GeneratedProtocolMessageType('AlmanacConfig', (_message.Message,), {
  'DESCRIPTOR' : _ALMANACCONFIG,
  '__module__' : 'proto.almanac.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.AlmanacConfig)
  })
_sym_db.RegisterMessage(AlmanacConfig)

DiscriminatorConfig = _reflection.GeneratedProtocolMessageType('DiscriminatorConfig', (_message.Message,), {
  'DESCRIPTOR' : _DISCRIMINATORCONFIG,
  '__module__' : 'proto.almanac.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.DiscriminatorConfig)
  })
_sym_db.RegisterMessage(DiscriminatorConfig)

ModelinatorConfig = _reflection.GeneratedProtocolMessageType('ModelinatorConfig', (_message.Message,), {
  'DESCRIPTOR' : _MODELINATORCONFIG,
  '__module__' : 'proto.almanac.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.aimbot.almanac.ModelinatorConfig)
  })
_sym_db.RegisterMessage(ModelinatorConfig)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

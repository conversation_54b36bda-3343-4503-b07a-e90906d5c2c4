"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

CategoryClassificationValue = typing___NewType('CategoryClassificationValue', builtin___int)
type___CategoryClassificationValue = CategoryClassificationValue
CategoryClassification: _CategoryClassification
class _CategoryClassification(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[CategoryClassificationValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    CATEGORY_WEED = typing___cast(CategoryClassificationValue, 0)
    CATEGORY_CROP = typing___cast(CategoryClassificationValue, 1)
CATEGORY_WEED = typing___cast(CategoryClassificationValue, 0)
CATEGORY_CROP = typing___cast(CategoryClassificationValue, 1)

class Formula(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    multiplier: builtin___float = ...
    offset: builtin___float = ...
    exponent: builtin___float = ...
    fine_tune_multiplier: builtin___int = ...
    max_time: builtin___int = ...
    fine_tune_multiplier_val: builtin___float = ...

    def __init__(self,
        *,
        multiplier : typing___Optional[builtin___float] = None,
        offset : typing___Optional[builtin___float] = None,
        exponent : typing___Optional[builtin___float] = None,
        fine_tune_multiplier : typing___Optional[builtin___int] = None,
        max_time : typing___Optional[builtin___int] = None,
        fine_tune_multiplier_val : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"exponent",b"exponent",u"fine_tune_multiplier",b"fine_tune_multiplier",u"fine_tune_multiplier_val",b"fine_tune_multiplier_val",u"max_time",b"max_time",u"multiplier",b"multiplier",u"offset",b"offset"]) -> None: ...
type___Formula = Formula

class Trust(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    ignorable: builtin___bool = ...
    avoid: builtin___bool = ...

    def __init__(self,
        *,
        ignorable : typing___Optional[builtin___bool] = None,
        avoid : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"avoid",b"avoid",u"ignorable",b"ignorable"]) -> None: ...
type___Trust = Trust

class ModelTrust(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    min_doo: builtin___float = ...
    weeding_threshold: builtin___float = ...
    thinning_threshold: builtin___float = ...
    banding_threshold: builtin___float = ...

    def __init__(self,
        *,
        min_doo : typing___Optional[builtin___float] = None,
        weeding_threshold : typing___Optional[builtin___float] = None,
        thinning_threshold : typing___Optional[builtin___float] = None,
        banding_threshold : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"banding_threshold",b"banding_threshold",u"min_doo",b"min_doo",u"thinning_threshold",b"thinning_threshold",u"weeding_threshold",b"weeding_threshold"]) -> None: ...
type___ModelTrust = ModelTrust

class TypeCategory(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    category: typing___Text = ...
    classification: type___CategoryClassificationValue = ...

    def __init__(self,
        *,
        category : typing___Optional[typing___Text] = None,
        classification : typing___Optional[type___CategoryClassificationValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"category",b"category",u"classification",b"classification"]) -> None: ...
type___TypeCategory = TypeCategory

class AlmanacTypeCategory(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    sizes: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...

    @property
    def type(self) -> type___TypeCategory: ...

    @property
    def formulas(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Formula]: ...

    def __init__(self,
        *,
        type : typing___Optional[type___TypeCategory] = None,
        sizes : typing___Optional[typing___Iterable[builtin___float]] = None,
        formulas : typing___Optional[typing___Iterable[type___Formula]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"type",b"type"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"formulas",b"formulas",u"sizes",b"sizes",u"type",b"type"]) -> None: ...
type___AlmanacTypeCategory = AlmanacTypeCategory

class DiscriminatorTypeCategory(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def type(self) -> type___TypeCategory: ...

    @property
    def trusts(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Trust]: ...

    def __init__(self,
        *,
        type : typing___Optional[type___TypeCategory] = None,
        trusts : typing___Optional[typing___Iterable[type___Trust]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"type",b"type"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"trusts",b"trusts",u"type",b"type"]) -> None: ...
type___DiscriminatorTypeCategory = DiscriminatorTypeCategory

class ModelinatorTypeCategory(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def type(self) -> type___TypeCategory: ...

    @property
    def trusts(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ModelTrust]: ...

    def __init__(self,
        *,
        type : typing___Optional[type___TypeCategory] = None,
        trusts : typing___Optional[typing___Iterable[type___ModelTrust]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"type",b"type"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"trusts",b"trusts",u"type",b"type"]) -> None: ...
type___ModelinatorTypeCategory = ModelinatorTypeCategory

class AlmanacConfig(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...
    name: typing___Text = ...
    protected: builtin___bool = ...
    updated_ts: builtin___int = ...

    @property
    def categories(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___AlmanacTypeCategory]: ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        name : typing___Optional[typing___Text] = None,
        protected : typing___Optional[builtin___bool] = None,
        updated_ts : typing___Optional[builtin___int] = None,
        categories : typing___Optional[typing___Iterable[type___AlmanacTypeCategory]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"categories",b"categories",u"id",b"id",u"name",b"name",u"protected",b"protected",u"updated_ts",b"updated_ts"]) -> None: ...
type___AlmanacConfig = AlmanacConfig

class DiscriminatorConfig(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...
    name: typing___Text = ...
    protected: builtin___bool = ...
    updated_ts: builtin___int = ...

    @property
    def categories(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___DiscriminatorTypeCategory]: ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        name : typing___Optional[typing___Text] = None,
        protected : typing___Optional[builtin___bool] = None,
        updated_ts : typing___Optional[builtin___int] = None,
        categories : typing___Optional[typing___Iterable[type___DiscriminatorTypeCategory]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"categories",b"categories",u"id",b"id",u"name",b"name",u"protected",b"protected",u"updated_ts",b"updated_ts"]) -> None: ...
type___DiscriminatorConfig = DiscriminatorConfig

class ModelinatorConfig(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    model_id: typing___Text = ...
    crop_id: typing___Text = ...
    modified: builtin___bool = ...

    @property
    def categories(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ModelinatorTypeCategory]: ...

    def __init__(self,
        *,
        model_id : typing___Optional[typing___Text] = None,
        crop_id : typing___Optional[typing___Text] = None,
        modified : typing___Optional[builtin___bool] = None,
        categories : typing___Optional[typing___Iterable[type___ModelinatorTypeCategory]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"categories",b"categories",u"crop_id",b"crop_id",u"model_id",b"model_id",u"modified",b"modified"]) -> None: ...
type___ModelinatorConfig = ModelinatorConfig

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/almanac/almanac.proto

#include "proto/almanac/almanac.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace aimbot {
namespace almanac {
constexpr Formula::Formula(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : multiplier_(0)
  , offset_(0)
  , exponent_(0)
  , fine_tune_multiplier_(0u)
  , max_time_(0u)
  , fine_tune_multiplier_val_(0){}
struct FormulaDefaultTypeInternal {
  constexpr FormulaDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FormulaDefaultTypeInternal() {}
  union {
    Formula _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FormulaDefaultTypeInternal _Formula_default_instance_;
constexpr Trust::Trust(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ignorable_(false)
  , avoid_(false){}
struct TrustDefaultTypeInternal {
  constexpr TrustDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TrustDefaultTypeInternal() {}
  union {
    Trust _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TrustDefaultTypeInternal _Trust_default_instance_;
constexpr ModelTrust::ModelTrust(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : min_doo_(0)
  , weeding_threshold_(0)
  , thinning_threshold_(0)
  , banding_threshold_(0){}
struct ModelTrustDefaultTypeInternal {
  constexpr ModelTrustDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ModelTrustDefaultTypeInternal() {}
  union {
    ModelTrust _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ModelTrustDefaultTypeInternal _ModelTrust_default_instance_;
constexpr TypeCategory::TypeCategory(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : category_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , classification_(0)
{}
struct TypeCategoryDefaultTypeInternal {
  constexpr TypeCategoryDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TypeCategoryDefaultTypeInternal() {}
  union {
    TypeCategory _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TypeCategoryDefaultTypeInternal _TypeCategory_default_instance_;
constexpr AlmanacTypeCategory::AlmanacTypeCategory(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : sizes_()
  , formulas_()
  , type_(nullptr){}
struct AlmanacTypeCategoryDefaultTypeInternal {
  constexpr AlmanacTypeCategoryDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AlmanacTypeCategoryDefaultTypeInternal() {}
  union {
    AlmanacTypeCategory _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AlmanacTypeCategoryDefaultTypeInternal _AlmanacTypeCategory_default_instance_;
constexpr DiscriminatorTypeCategory::DiscriminatorTypeCategory(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : trusts_()
  , type_(nullptr){}
struct DiscriminatorTypeCategoryDefaultTypeInternal {
  constexpr DiscriminatorTypeCategoryDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DiscriminatorTypeCategoryDefaultTypeInternal() {}
  union {
    DiscriminatorTypeCategory _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DiscriminatorTypeCategoryDefaultTypeInternal _DiscriminatorTypeCategory_default_instance_;
constexpr ModelinatorTypeCategory::ModelinatorTypeCategory(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : trusts_()
  , type_(nullptr){}
struct ModelinatorTypeCategoryDefaultTypeInternal {
  constexpr ModelinatorTypeCategoryDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ModelinatorTypeCategoryDefaultTypeInternal() {}
  union {
    ModelinatorTypeCategory _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ModelinatorTypeCategoryDefaultTypeInternal _ModelinatorTypeCategory_default_instance_;
constexpr AlmanacConfig::AlmanacConfig(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : categories_()
  , id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , updated_ts_(uint64_t{0u})
  , protected__(false){}
struct AlmanacConfigDefaultTypeInternal {
  constexpr AlmanacConfigDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AlmanacConfigDefaultTypeInternal() {}
  union {
    AlmanacConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AlmanacConfigDefaultTypeInternal _AlmanacConfig_default_instance_;
constexpr DiscriminatorConfig::DiscriminatorConfig(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : categories_()
  , id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , updated_ts_(uint64_t{0u})
  , protected__(false){}
struct DiscriminatorConfigDefaultTypeInternal {
  constexpr DiscriminatorConfigDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DiscriminatorConfigDefaultTypeInternal() {}
  union {
    DiscriminatorConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DiscriminatorConfigDefaultTypeInternal _DiscriminatorConfig_default_instance_;
constexpr ModelinatorConfig::ModelinatorConfig(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : categories_()
  , model_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , modified_(false){}
struct ModelinatorConfigDefaultTypeInternal {
  constexpr ModelinatorConfigDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ModelinatorConfigDefaultTypeInternal() {}
  union {
    ModelinatorConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ModelinatorConfigDefaultTypeInternal _ModelinatorConfig_default_instance_;
}  // namespace almanac
}  // namespace aimbot
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_proto_2falmanac_2falmanac_2eproto[10];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_proto_2falmanac_2falmanac_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_2falmanac_2falmanac_2eproto = nullptr;

const uint32_t TableStruct_proto_2falmanac_2falmanac_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::Formula, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::Formula, multiplier_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::Formula, offset_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::Formula, exponent_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::Formula, fine_tune_multiplier_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::Formula, max_time_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::Formula, fine_tune_multiplier_val_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::Trust, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::Trust, ignorable_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::Trust, avoid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::ModelTrust, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::ModelTrust, min_doo_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::ModelTrust, weeding_threshold_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::ModelTrust, thinning_threshold_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::ModelTrust, banding_threshold_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::TypeCategory, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::TypeCategory, category_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::TypeCategory, classification_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::AlmanacTypeCategory, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::AlmanacTypeCategory, type_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::AlmanacTypeCategory, sizes_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::AlmanacTypeCategory, formulas_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::DiscriminatorTypeCategory, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::DiscriminatorTypeCategory, type_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::DiscriminatorTypeCategory, trusts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::ModelinatorTypeCategory, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::ModelinatorTypeCategory, type_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::ModelinatorTypeCategory, trusts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::AlmanacConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::AlmanacConfig, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::AlmanacConfig, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::AlmanacConfig, protected__),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::AlmanacConfig, updated_ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::AlmanacConfig, categories_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::DiscriminatorConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::DiscriminatorConfig, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::DiscriminatorConfig, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::DiscriminatorConfig, protected__),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::DiscriminatorConfig, updated_ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::DiscriminatorConfig, categories_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::ModelinatorConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::ModelinatorConfig, model_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::ModelinatorConfig, crop_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::ModelinatorConfig, modified_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::almanac::ModelinatorConfig, categories_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::aimbot::almanac::Formula)},
  { 12, -1, -1, sizeof(::carbon::aimbot::almanac::Trust)},
  { 20, -1, -1, sizeof(::carbon::aimbot::almanac::ModelTrust)},
  { 30, -1, -1, sizeof(::carbon::aimbot::almanac::TypeCategory)},
  { 38, -1, -1, sizeof(::carbon::aimbot::almanac::AlmanacTypeCategory)},
  { 47, -1, -1, sizeof(::carbon::aimbot::almanac::DiscriminatorTypeCategory)},
  { 55, -1, -1, sizeof(::carbon::aimbot::almanac::ModelinatorTypeCategory)},
  { 63, -1, -1, sizeof(::carbon::aimbot::almanac::AlmanacConfig)},
  { 74, -1, -1, sizeof(::carbon::aimbot::almanac::DiscriminatorConfig)},
  { 85, -1, -1, sizeof(::carbon::aimbot::almanac::ModelinatorConfig)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::aimbot::almanac::_Formula_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::aimbot::almanac::_Trust_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::aimbot::almanac::_ModelTrust_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::aimbot::almanac::_TypeCategory_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::aimbot::almanac::_AlmanacTypeCategory_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::aimbot::almanac::_DiscriminatorTypeCategory_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::aimbot::almanac::_ModelinatorTypeCategory_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::aimbot::almanac::_AlmanacConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::aimbot::almanac::_DiscriminatorConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::aimbot::almanac::_ModelinatorConfig_default_instance_),
};

const char descriptor_table_protodef_proto_2falmanac_2falmanac_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\033proto/almanac/almanac.proto\022\025carbon.ai"
  "mbot.almanac\"\221\001\n\007Formula\022\022\n\nmultiplier\030\001"
  " \001(\002\022\016\n\006offset\030\002 \001(\002\022\020\n\010exponent\030\003 \001(\002\022\034"
  "\n\024fine_tune_multiplier\030\004 \001(\r\022\020\n\010max_time"
  "\030\005 \001(\r\022 \n\030fine_tune_multiplier_val\030\006 \001(\002"
  "\")\n\005Trust\022\021\n\tignorable\030\001 \001(\010\022\r\n\005avoid\030\002 "
  "\001(\010\"o\n\nModelTrust\022\017\n\007min_doo\030\001 \001(\002\022\031\n\021we"
  "eding_threshold\030\002 \001(\002\022\032\n\022thinning_thresh"
  "old\030\003 \001(\002\022\031\n\021banding_threshold\030\004 \001(\002\"g\n\014"
  "TypeCategory\022\020\n\010category\030\001 \001(\t\022E\n\016classi"
  "fication\030\002 \001(\0162-.carbon.aimbot.almanac.C"
  "ategoryClassification\"\211\001\n\023AlmanacTypeCat"
  "egory\0221\n\004type\030\001 \001(\0132#.carbon.aimbot.alma"
  "nac.TypeCategory\022\r\n\005sizes\030\002 \003(\002\0220\n\010formu"
  "las\030\003 \003(\0132\036.carbon.aimbot.almanac.Formul"
  "a\"|\n\031DiscriminatorTypeCategory\0221\n\004type\030\001"
  " \001(\0132#.carbon.aimbot.almanac.TypeCategor"
  "y\022,\n\006trusts\030\002 \003(\0132\034.carbon.aimbot.almana"
  "c.Trust\"\177\n\027ModelinatorTypeCategory\0221\n\004ty"
  "pe\030\001 \001(\0132#.carbon.aimbot.almanac.TypeCat"
  "egory\0221\n\006trusts\030\002 \003(\0132!.carbon.aimbot.al"
  "manac.ModelTrust\"\220\001\n\rAlmanacConfig\022\n\n\002id"
  "\030\001 \001(\t\022\014\n\004name\030\002 \001(\t\022\021\n\tprotected\030\003 \001(\010\022"
  "\022\n\nupdated_ts\030\004 \001(\004\022>\n\ncategories\030\005 \003(\0132"
  "*.carbon.aimbot.almanac.AlmanacTypeCateg"
  "ory\"\234\001\n\023DiscriminatorConfig\022\n\n\002id\030\001 \001(\t\022"
  "\014\n\004name\030\002 \001(\t\022\021\n\tprotected\030\003 \001(\010\022\022\n\nupda"
  "ted_ts\030\004 \001(\004\022D\n\ncategories\030\005 \003(\01320.carbo"
  "n.aimbot.almanac.DiscriminatorTypeCatego"
  "ry\"\214\001\n\021ModelinatorConfig\022\020\n\010model_id\030\001 \001"
  "(\t\022\017\n\007crop_id\030\002 \001(\t\022\020\n\010modified\030\003 \001(\010\022B\n"
  "\ncategories\030\004 \003(\0132..carbon.aimbot.almana"
  "c.ModelinatorTypeCategory*>\n\026CategoryCla"
  "ssification\022\021\n\rCATEGORY_WEED\020\000\022\021\n\rCATEGO"
  "RY_CROP\020\001B\017Z\rproto/almanacb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_2falmanac_2falmanac_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2falmanac_2falmanac_2eproto = {
  false, false, 1394, descriptor_table_protodef_proto_2falmanac_2falmanac_2eproto, "proto/almanac/almanac.proto", 
  &descriptor_table_proto_2falmanac_2falmanac_2eproto_once, nullptr, 0, 10,
  schemas, file_default_instances, TableStruct_proto_2falmanac_2falmanac_2eproto::offsets,
  file_level_metadata_proto_2falmanac_2falmanac_2eproto, file_level_enum_descriptors_proto_2falmanac_2falmanac_2eproto, file_level_service_descriptors_proto_2falmanac_2falmanac_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_proto_2falmanac_2falmanac_2eproto_getter() {
  return &descriptor_table_proto_2falmanac_2falmanac_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_proto_2falmanac_2falmanac_2eproto(&descriptor_table_proto_2falmanac_2falmanac_2eproto);
namespace carbon {
namespace aimbot {
namespace almanac {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CategoryClassification_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_proto_2falmanac_2falmanac_2eproto);
  return file_level_enum_descriptors_proto_2falmanac_2falmanac_2eproto[0];
}
bool CategoryClassification_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class Formula::_Internal {
 public:
};

Formula::Formula(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.aimbot.almanac.Formula)
}
Formula::Formula(const Formula& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&multiplier_, &from.multiplier_,
    static_cast<size_t>(reinterpret_cast<char*>(&fine_tune_multiplier_val_) -
    reinterpret_cast<char*>(&multiplier_)) + sizeof(fine_tune_multiplier_val_));
  // @@protoc_insertion_point(copy_constructor:carbon.aimbot.almanac.Formula)
}

inline void Formula::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&multiplier_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&fine_tune_multiplier_val_) -
    reinterpret_cast<char*>(&multiplier_)) + sizeof(fine_tune_multiplier_val_));
}

Formula::~Formula() {
  // @@protoc_insertion_point(destructor:carbon.aimbot.almanac.Formula)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Formula::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Formula::ArenaDtor(void* object) {
  Formula* _this = reinterpret_cast< Formula* >(object);
  (void)_this;
}
void Formula::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Formula::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Formula::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.aimbot.almanac.Formula)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&multiplier_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&fine_tune_multiplier_val_) -
      reinterpret_cast<char*>(&multiplier_)) + sizeof(fine_tune_multiplier_val_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Formula::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float multiplier = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          multiplier_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float offset = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          offset_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float exponent = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          exponent_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // uint32 fine_tune_multiplier = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          fine_tune_multiplier_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 max_time = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          max_time_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float fine_tune_multiplier_val = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 53)) {
          fine_tune_multiplier_val_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Formula::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.aimbot.almanac.Formula)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float multiplier = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_multiplier = this->_internal_multiplier();
  uint32_t raw_multiplier;
  memcpy(&raw_multiplier, &tmp_multiplier, sizeof(tmp_multiplier));
  if (raw_multiplier != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_multiplier(), target);
  }

  // float offset = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_offset = this->_internal_offset();
  uint32_t raw_offset;
  memcpy(&raw_offset, &tmp_offset, sizeof(tmp_offset));
  if (raw_offset != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_offset(), target);
  }

  // float exponent = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_exponent = this->_internal_exponent();
  uint32_t raw_exponent;
  memcpy(&raw_exponent, &tmp_exponent, sizeof(tmp_exponent));
  if (raw_exponent != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_exponent(), target);
  }

  // uint32 fine_tune_multiplier = 4;
  if (this->_internal_fine_tune_multiplier() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_fine_tune_multiplier(), target);
  }

  // uint32 max_time = 5;
  if (this->_internal_max_time() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_max_time(), target);
  }

  // float fine_tune_multiplier_val = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_fine_tune_multiplier_val = this->_internal_fine_tune_multiplier_val();
  uint32_t raw_fine_tune_multiplier_val;
  memcpy(&raw_fine_tune_multiplier_val, &tmp_fine_tune_multiplier_val, sizeof(tmp_fine_tune_multiplier_val));
  if (raw_fine_tune_multiplier_val != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_fine_tune_multiplier_val(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.aimbot.almanac.Formula)
  return target;
}

size_t Formula::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.aimbot.almanac.Formula)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float multiplier = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_multiplier = this->_internal_multiplier();
  uint32_t raw_multiplier;
  memcpy(&raw_multiplier, &tmp_multiplier, sizeof(tmp_multiplier));
  if (raw_multiplier != 0) {
    total_size += 1 + 4;
  }

  // float offset = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_offset = this->_internal_offset();
  uint32_t raw_offset;
  memcpy(&raw_offset, &tmp_offset, sizeof(tmp_offset));
  if (raw_offset != 0) {
    total_size += 1 + 4;
  }

  // float exponent = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_exponent = this->_internal_exponent();
  uint32_t raw_exponent;
  memcpy(&raw_exponent, &tmp_exponent, sizeof(tmp_exponent));
  if (raw_exponent != 0) {
    total_size += 1 + 4;
  }

  // uint32 fine_tune_multiplier = 4;
  if (this->_internal_fine_tune_multiplier() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_fine_tune_multiplier());
  }

  // uint32 max_time = 5;
  if (this->_internal_max_time() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_max_time());
  }

  // float fine_tune_multiplier_val = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_fine_tune_multiplier_val = this->_internal_fine_tune_multiplier_val();
  uint32_t raw_fine_tune_multiplier_val;
  memcpy(&raw_fine_tune_multiplier_val, &tmp_fine_tune_multiplier_val, sizeof(tmp_fine_tune_multiplier_val));
  if (raw_fine_tune_multiplier_val != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Formula::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Formula::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Formula::GetClassData() const { return &_class_data_; }

void Formula::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Formula *>(to)->MergeFrom(
      static_cast<const Formula &>(from));
}


void Formula::MergeFrom(const Formula& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.aimbot.almanac.Formula)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_multiplier = from._internal_multiplier();
  uint32_t raw_multiplier;
  memcpy(&raw_multiplier, &tmp_multiplier, sizeof(tmp_multiplier));
  if (raw_multiplier != 0) {
    _internal_set_multiplier(from._internal_multiplier());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_offset = from._internal_offset();
  uint32_t raw_offset;
  memcpy(&raw_offset, &tmp_offset, sizeof(tmp_offset));
  if (raw_offset != 0) {
    _internal_set_offset(from._internal_offset());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_exponent = from._internal_exponent();
  uint32_t raw_exponent;
  memcpy(&raw_exponent, &tmp_exponent, sizeof(tmp_exponent));
  if (raw_exponent != 0) {
    _internal_set_exponent(from._internal_exponent());
  }
  if (from._internal_fine_tune_multiplier() != 0) {
    _internal_set_fine_tune_multiplier(from._internal_fine_tune_multiplier());
  }
  if (from._internal_max_time() != 0) {
    _internal_set_max_time(from._internal_max_time());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_fine_tune_multiplier_val = from._internal_fine_tune_multiplier_val();
  uint32_t raw_fine_tune_multiplier_val;
  memcpy(&raw_fine_tune_multiplier_val, &tmp_fine_tune_multiplier_val, sizeof(tmp_fine_tune_multiplier_val));
  if (raw_fine_tune_multiplier_val != 0) {
    _internal_set_fine_tune_multiplier_val(from._internal_fine_tune_multiplier_val());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Formula::CopyFrom(const Formula& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.aimbot.almanac.Formula)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Formula::IsInitialized() const {
  return true;
}

void Formula::InternalSwap(Formula* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Formula, fine_tune_multiplier_val_)
      + sizeof(Formula::fine_tune_multiplier_val_)
      - PROTOBUF_FIELD_OFFSET(Formula, multiplier_)>(
          reinterpret_cast<char*>(&multiplier_),
          reinterpret_cast<char*>(&other->multiplier_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Formula::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2falmanac_2falmanac_2eproto_getter, &descriptor_table_proto_2falmanac_2falmanac_2eproto_once,
      file_level_metadata_proto_2falmanac_2falmanac_2eproto[0]);
}

// ===================================================================

class Trust::_Internal {
 public:
};

Trust::Trust(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.aimbot.almanac.Trust)
}
Trust::Trust(const Trust& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&ignorable_, &from.ignorable_,
    static_cast<size_t>(reinterpret_cast<char*>(&avoid_) -
    reinterpret_cast<char*>(&ignorable_)) + sizeof(avoid_));
  // @@protoc_insertion_point(copy_constructor:carbon.aimbot.almanac.Trust)
}

inline void Trust::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ignorable_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&avoid_) -
    reinterpret_cast<char*>(&ignorable_)) + sizeof(avoid_));
}

Trust::~Trust() {
  // @@protoc_insertion_point(destructor:carbon.aimbot.almanac.Trust)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Trust::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Trust::ArenaDtor(void* object) {
  Trust* _this = reinterpret_cast< Trust* >(object);
  (void)_this;
}
void Trust::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Trust::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Trust::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.aimbot.almanac.Trust)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&ignorable_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&avoid_) -
      reinterpret_cast<char*>(&ignorable_)) + sizeof(avoid_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Trust::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool ignorable = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          ignorable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool avoid = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          avoid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Trust::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.aimbot.almanac.Trust)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool ignorable = 1;
  if (this->_internal_ignorable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_ignorable(), target);
  }

  // bool avoid = 2;
  if (this->_internal_avoid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_avoid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.aimbot.almanac.Trust)
  return target;
}

size_t Trust::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.aimbot.almanac.Trust)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool ignorable = 1;
  if (this->_internal_ignorable() != 0) {
    total_size += 1 + 1;
  }

  // bool avoid = 2;
  if (this->_internal_avoid() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Trust::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Trust::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Trust::GetClassData() const { return &_class_data_; }

void Trust::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Trust *>(to)->MergeFrom(
      static_cast<const Trust &>(from));
}


void Trust::MergeFrom(const Trust& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.aimbot.almanac.Trust)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_ignorable() != 0) {
    _internal_set_ignorable(from._internal_ignorable());
  }
  if (from._internal_avoid() != 0) {
    _internal_set_avoid(from._internal_avoid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Trust::CopyFrom(const Trust& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.aimbot.almanac.Trust)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Trust::IsInitialized() const {
  return true;
}

void Trust::InternalSwap(Trust* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Trust, avoid_)
      + sizeof(Trust::avoid_)
      - PROTOBUF_FIELD_OFFSET(Trust, ignorable_)>(
          reinterpret_cast<char*>(&ignorable_),
          reinterpret_cast<char*>(&other->ignorable_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Trust::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2falmanac_2falmanac_2eproto_getter, &descriptor_table_proto_2falmanac_2falmanac_2eproto_once,
      file_level_metadata_proto_2falmanac_2falmanac_2eproto[1]);
}

// ===================================================================

class ModelTrust::_Internal {
 public:
};

ModelTrust::ModelTrust(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.aimbot.almanac.ModelTrust)
}
ModelTrust::ModelTrust(const ModelTrust& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&min_doo_, &from.min_doo_,
    static_cast<size_t>(reinterpret_cast<char*>(&banding_threshold_) -
    reinterpret_cast<char*>(&min_doo_)) + sizeof(banding_threshold_));
  // @@protoc_insertion_point(copy_constructor:carbon.aimbot.almanac.ModelTrust)
}

inline void ModelTrust::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&min_doo_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&banding_threshold_) -
    reinterpret_cast<char*>(&min_doo_)) + sizeof(banding_threshold_));
}

ModelTrust::~ModelTrust() {
  // @@protoc_insertion_point(destructor:carbon.aimbot.almanac.ModelTrust)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ModelTrust::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ModelTrust::ArenaDtor(void* object) {
  ModelTrust* _this = reinterpret_cast< ModelTrust* >(object);
  (void)_this;
}
void ModelTrust::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ModelTrust::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ModelTrust::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.aimbot.almanac.ModelTrust)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&min_doo_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&banding_threshold_) -
      reinterpret_cast<char*>(&min_doo_)) + sizeof(banding_threshold_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ModelTrust::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float min_doo = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          min_doo_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float weeding_threshold = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          weeding_threshold_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float thinning_threshold = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          thinning_threshold_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float banding_threshold = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          banding_threshold_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ModelTrust::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.aimbot.almanac.ModelTrust)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float min_doo = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_doo = this->_internal_min_doo();
  uint32_t raw_min_doo;
  memcpy(&raw_min_doo, &tmp_min_doo, sizeof(tmp_min_doo));
  if (raw_min_doo != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_min_doo(), target);
  }

  // float weeding_threshold = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_weeding_threshold = this->_internal_weeding_threshold();
  uint32_t raw_weeding_threshold;
  memcpy(&raw_weeding_threshold, &tmp_weeding_threshold, sizeof(tmp_weeding_threshold));
  if (raw_weeding_threshold != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_weeding_threshold(), target);
  }

  // float thinning_threshold = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_thinning_threshold = this->_internal_thinning_threshold();
  uint32_t raw_thinning_threshold;
  memcpy(&raw_thinning_threshold, &tmp_thinning_threshold, sizeof(tmp_thinning_threshold));
  if (raw_thinning_threshold != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_thinning_threshold(), target);
  }

  // float banding_threshold = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_banding_threshold = this->_internal_banding_threshold();
  uint32_t raw_banding_threshold;
  memcpy(&raw_banding_threshold, &tmp_banding_threshold, sizeof(tmp_banding_threshold));
  if (raw_banding_threshold != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_banding_threshold(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.aimbot.almanac.ModelTrust)
  return target;
}

size_t ModelTrust::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.aimbot.almanac.ModelTrust)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float min_doo = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_doo = this->_internal_min_doo();
  uint32_t raw_min_doo;
  memcpy(&raw_min_doo, &tmp_min_doo, sizeof(tmp_min_doo));
  if (raw_min_doo != 0) {
    total_size += 1 + 4;
  }

  // float weeding_threshold = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_weeding_threshold = this->_internal_weeding_threshold();
  uint32_t raw_weeding_threshold;
  memcpy(&raw_weeding_threshold, &tmp_weeding_threshold, sizeof(tmp_weeding_threshold));
  if (raw_weeding_threshold != 0) {
    total_size += 1 + 4;
  }

  // float thinning_threshold = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_thinning_threshold = this->_internal_thinning_threshold();
  uint32_t raw_thinning_threshold;
  memcpy(&raw_thinning_threshold, &tmp_thinning_threshold, sizeof(tmp_thinning_threshold));
  if (raw_thinning_threshold != 0) {
    total_size += 1 + 4;
  }

  // float banding_threshold = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_banding_threshold = this->_internal_banding_threshold();
  uint32_t raw_banding_threshold;
  memcpy(&raw_banding_threshold, &tmp_banding_threshold, sizeof(tmp_banding_threshold));
  if (raw_banding_threshold != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ModelTrust::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ModelTrust::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ModelTrust::GetClassData() const { return &_class_data_; }

void ModelTrust::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ModelTrust *>(to)->MergeFrom(
      static_cast<const ModelTrust &>(from));
}


void ModelTrust::MergeFrom(const ModelTrust& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.aimbot.almanac.ModelTrust)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_doo = from._internal_min_doo();
  uint32_t raw_min_doo;
  memcpy(&raw_min_doo, &tmp_min_doo, sizeof(tmp_min_doo));
  if (raw_min_doo != 0) {
    _internal_set_min_doo(from._internal_min_doo());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_weeding_threshold = from._internal_weeding_threshold();
  uint32_t raw_weeding_threshold;
  memcpy(&raw_weeding_threshold, &tmp_weeding_threshold, sizeof(tmp_weeding_threshold));
  if (raw_weeding_threshold != 0) {
    _internal_set_weeding_threshold(from._internal_weeding_threshold());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_thinning_threshold = from._internal_thinning_threshold();
  uint32_t raw_thinning_threshold;
  memcpy(&raw_thinning_threshold, &tmp_thinning_threshold, sizeof(tmp_thinning_threshold));
  if (raw_thinning_threshold != 0) {
    _internal_set_thinning_threshold(from._internal_thinning_threshold());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_banding_threshold = from._internal_banding_threshold();
  uint32_t raw_banding_threshold;
  memcpy(&raw_banding_threshold, &tmp_banding_threshold, sizeof(tmp_banding_threshold));
  if (raw_banding_threshold != 0) {
    _internal_set_banding_threshold(from._internal_banding_threshold());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ModelTrust::CopyFrom(const ModelTrust& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.aimbot.almanac.ModelTrust)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ModelTrust::IsInitialized() const {
  return true;
}

void ModelTrust::InternalSwap(ModelTrust* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ModelTrust, banding_threshold_)
      + sizeof(ModelTrust::banding_threshold_)
      - PROTOBUF_FIELD_OFFSET(ModelTrust, min_doo_)>(
          reinterpret_cast<char*>(&min_doo_),
          reinterpret_cast<char*>(&other->min_doo_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ModelTrust::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2falmanac_2falmanac_2eproto_getter, &descriptor_table_proto_2falmanac_2falmanac_2eproto_once,
      file_level_metadata_proto_2falmanac_2falmanac_2eproto[2]);
}

// ===================================================================

class TypeCategory::_Internal {
 public:
};

TypeCategory::TypeCategory(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.aimbot.almanac.TypeCategory)
}
TypeCategory::TypeCategory(const TypeCategory& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  category_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    category_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_category().empty()) {
    category_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_category(), 
      GetArenaForAllocation());
  }
  classification_ = from.classification_;
  // @@protoc_insertion_point(copy_constructor:carbon.aimbot.almanac.TypeCategory)
}

inline void TypeCategory::SharedCtor() {
category_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  category_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
classification_ = 0;
}

TypeCategory::~TypeCategory() {
  // @@protoc_insertion_point(destructor:carbon.aimbot.almanac.TypeCategory)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TypeCategory::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  category_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void TypeCategory::ArenaDtor(void* object) {
  TypeCategory* _this = reinterpret_cast< TypeCategory* >(object);
  (void)_this;
}
void TypeCategory::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TypeCategory::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TypeCategory::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.aimbot.almanac.TypeCategory)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  category_.ClearToEmpty();
  classification_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TypeCategory::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string category = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_category();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.aimbot.almanac.TypeCategory.category"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.almanac.CategoryClassification classification = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_classification(static_cast<::carbon::aimbot::almanac::CategoryClassification>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TypeCategory::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.aimbot.almanac.TypeCategory)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string category = 1;
  if (!this->_internal_category().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_category().data(), static_cast<int>(this->_internal_category().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.aimbot.almanac.TypeCategory.category");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_category(), target);
  }

  // .carbon.aimbot.almanac.CategoryClassification classification = 2;
  if (this->_internal_classification() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_classification(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.aimbot.almanac.TypeCategory)
  return target;
}

size_t TypeCategory::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.aimbot.almanac.TypeCategory)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string category = 1;
  if (!this->_internal_category().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_category());
  }

  // .carbon.aimbot.almanac.CategoryClassification classification = 2;
  if (this->_internal_classification() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_classification());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TypeCategory::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TypeCategory::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TypeCategory::GetClassData() const { return &_class_data_; }

void TypeCategory::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TypeCategory *>(to)->MergeFrom(
      static_cast<const TypeCategory &>(from));
}


void TypeCategory::MergeFrom(const TypeCategory& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.aimbot.almanac.TypeCategory)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_category().empty()) {
    _internal_set_category(from._internal_category());
  }
  if (from._internal_classification() != 0) {
    _internal_set_classification(from._internal_classification());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TypeCategory::CopyFrom(const TypeCategory& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.aimbot.almanac.TypeCategory)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TypeCategory::IsInitialized() const {
  return true;
}

void TypeCategory::InternalSwap(TypeCategory* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &category_, lhs_arena,
      &other->category_, rhs_arena
  );
  swap(classification_, other->classification_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TypeCategory::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2falmanac_2falmanac_2eproto_getter, &descriptor_table_proto_2falmanac_2falmanac_2eproto_once,
      file_level_metadata_proto_2falmanac_2falmanac_2eproto[3]);
}

// ===================================================================

class AlmanacTypeCategory::_Internal {
 public:
  static const ::carbon::aimbot::almanac::TypeCategory& type(const AlmanacTypeCategory* msg);
};

const ::carbon::aimbot::almanac::TypeCategory&
AlmanacTypeCategory::_Internal::type(const AlmanacTypeCategory* msg) {
  return *msg->type_;
}
AlmanacTypeCategory::AlmanacTypeCategory(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  sizes_(arena),
  formulas_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.aimbot.almanac.AlmanacTypeCategory)
}
AlmanacTypeCategory::AlmanacTypeCategory(const AlmanacTypeCategory& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      sizes_(from.sizes_),
      formulas_(from.formulas_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_type()) {
    type_ = new ::carbon::aimbot::almanac::TypeCategory(*from.type_);
  } else {
    type_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.aimbot.almanac.AlmanacTypeCategory)
}

inline void AlmanacTypeCategory::SharedCtor() {
type_ = nullptr;
}

AlmanacTypeCategory::~AlmanacTypeCategory() {
  // @@protoc_insertion_point(destructor:carbon.aimbot.almanac.AlmanacTypeCategory)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AlmanacTypeCategory::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete type_;
}

void AlmanacTypeCategory::ArenaDtor(void* object) {
  AlmanacTypeCategory* _this = reinterpret_cast< AlmanacTypeCategory* >(object);
  (void)_this;
}
void AlmanacTypeCategory::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AlmanacTypeCategory::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AlmanacTypeCategory::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.aimbot.almanac.AlmanacTypeCategory)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  sizes_.Clear();
  formulas_.Clear();
  if (GetArenaForAllocation() == nullptr && type_ != nullptr) {
    delete type_;
  }
  type_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AlmanacTypeCategory::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.aimbot.almanac.TypeCategory type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_type(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated float sizes = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedFloatParser(_internal_mutable_sizes(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 21) {
          _internal_add_sizes(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr));
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.aimbot.almanac.Formula formulas = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_formulas(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AlmanacTypeCategory::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.aimbot.almanac.AlmanacTypeCategory)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.TypeCategory type = 1;
  if (this->_internal_has_type()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::type(this), target, stream);
  }

  // repeated float sizes = 2;
  if (this->_internal_sizes_size() > 0) {
    target = stream->WriteFixedPacked(2, _internal_sizes(), target);
  }

  // repeated .carbon.aimbot.almanac.Formula formulas = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_formulas_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_formulas(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.aimbot.almanac.AlmanacTypeCategory)
  return target;
}

size_t AlmanacTypeCategory::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.aimbot.almanac.AlmanacTypeCategory)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated float sizes = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_sizes_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // repeated .carbon.aimbot.almanac.Formula formulas = 3;
  total_size += 1UL * this->_internal_formulas_size();
  for (const auto& msg : this->formulas_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.aimbot.almanac.TypeCategory type = 1;
  if (this->_internal_has_type()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *type_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AlmanacTypeCategory::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AlmanacTypeCategory::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AlmanacTypeCategory::GetClassData() const { return &_class_data_; }

void AlmanacTypeCategory::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<AlmanacTypeCategory *>(to)->MergeFrom(
      static_cast<const AlmanacTypeCategory &>(from));
}


void AlmanacTypeCategory::MergeFrom(const AlmanacTypeCategory& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.aimbot.almanac.AlmanacTypeCategory)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  sizes_.MergeFrom(from.sizes_);
  formulas_.MergeFrom(from.formulas_);
  if (from._internal_has_type()) {
    _internal_mutable_type()->::carbon::aimbot::almanac::TypeCategory::MergeFrom(from._internal_type());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AlmanacTypeCategory::CopyFrom(const AlmanacTypeCategory& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.aimbot.almanac.AlmanacTypeCategory)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AlmanacTypeCategory::IsInitialized() const {
  return true;
}

void AlmanacTypeCategory::InternalSwap(AlmanacTypeCategory* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  sizes_.InternalSwap(&other->sizes_);
  formulas_.InternalSwap(&other->formulas_);
  swap(type_, other->type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata AlmanacTypeCategory::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2falmanac_2falmanac_2eproto_getter, &descriptor_table_proto_2falmanac_2falmanac_2eproto_once,
      file_level_metadata_proto_2falmanac_2falmanac_2eproto[4]);
}

// ===================================================================

class DiscriminatorTypeCategory::_Internal {
 public:
  static const ::carbon::aimbot::almanac::TypeCategory& type(const DiscriminatorTypeCategory* msg);
};

const ::carbon::aimbot::almanac::TypeCategory&
DiscriminatorTypeCategory::_Internal::type(const DiscriminatorTypeCategory* msg) {
  return *msg->type_;
}
DiscriminatorTypeCategory::DiscriminatorTypeCategory(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  trusts_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.aimbot.almanac.DiscriminatorTypeCategory)
}
DiscriminatorTypeCategory::DiscriminatorTypeCategory(const DiscriminatorTypeCategory& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      trusts_(from.trusts_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_type()) {
    type_ = new ::carbon::aimbot::almanac::TypeCategory(*from.type_);
  } else {
    type_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.aimbot.almanac.DiscriminatorTypeCategory)
}

inline void DiscriminatorTypeCategory::SharedCtor() {
type_ = nullptr;
}

DiscriminatorTypeCategory::~DiscriminatorTypeCategory() {
  // @@protoc_insertion_point(destructor:carbon.aimbot.almanac.DiscriminatorTypeCategory)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DiscriminatorTypeCategory::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete type_;
}

void DiscriminatorTypeCategory::ArenaDtor(void* object) {
  DiscriminatorTypeCategory* _this = reinterpret_cast< DiscriminatorTypeCategory* >(object);
  (void)_this;
}
void DiscriminatorTypeCategory::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DiscriminatorTypeCategory::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DiscriminatorTypeCategory::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.aimbot.almanac.DiscriminatorTypeCategory)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  trusts_.Clear();
  if (GetArenaForAllocation() == nullptr && type_ != nullptr) {
    delete type_;
  }
  type_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DiscriminatorTypeCategory::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.aimbot.almanac.TypeCategory type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_type(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.aimbot.almanac.Trust trusts = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_trusts(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DiscriminatorTypeCategory::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.aimbot.almanac.DiscriminatorTypeCategory)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.TypeCategory type = 1;
  if (this->_internal_has_type()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::type(this), target, stream);
  }

  // repeated .carbon.aimbot.almanac.Trust trusts = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_trusts_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_trusts(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.aimbot.almanac.DiscriminatorTypeCategory)
  return target;
}

size_t DiscriminatorTypeCategory::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.aimbot.almanac.DiscriminatorTypeCategory)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.aimbot.almanac.Trust trusts = 2;
  total_size += 1UL * this->_internal_trusts_size();
  for (const auto& msg : this->trusts_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.aimbot.almanac.TypeCategory type = 1;
  if (this->_internal_has_type()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *type_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DiscriminatorTypeCategory::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DiscriminatorTypeCategory::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DiscriminatorTypeCategory::GetClassData() const { return &_class_data_; }

void DiscriminatorTypeCategory::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DiscriminatorTypeCategory *>(to)->MergeFrom(
      static_cast<const DiscriminatorTypeCategory &>(from));
}


void DiscriminatorTypeCategory::MergeFrom(const DiscriminatorTypeCategory& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.aimbot.almanac.DiscriminatorTypeCategory)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  trusts_.MergeFrom(from.trusts_);
  if (from._internal_has_type()) {
    _internal_mutable_type()->::carbon::aimbot::almanac::TypeCategory::MergeFrom(from._internal_type());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DiscriminatorTypeCategory::CopyFrom(const DiscriminatorTypeCategory& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.aimbot.almanac.DiscriminatorTypeCategory)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DiscriminatorTypeCategory::IsInitialized() const {
  return true;
}

void DiscriminatorTypeCategory::InternalSwap(DiscriminatorTypeCategory* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  trusts_.InternalSwap(&other->trusts_);
  swap(type_, other->type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DiscriminatorTypeCategory::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2falmanac_2falmanac_2eproto_getter, &descriptor_table_proto_2falmanac_2falmanac_2eproto_once,
      file_level_metadata_proto_2falmanac_2falmanac_2eproto[5]);
}

// ===================================================================

class ModelinatorTypeCategory::_Internal {
 public:
  static const ::carbon::aimbot::almanac::TypeCategory& type(const ModelinatorTypeCategory* msg);
};

const ::carbon::aimbot::almanac::TypeCategory&
ModelinatorTypeCategory::_Internal::type(const ModelinatorTypeCategory* msg) {
  return *msg->type_;
}
ModelinatorTypeCategory::ModelinatorTypeCategory(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  trusts_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.aimbot.almanac.ModelinatorTypeCategory)
}
ModelinatorTypeCategory::ModelinatorTypeCategory(const ModelinatorTypeCategory& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      trusts_(from.trusts_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_type()) {
    type_ = new ::carbon::aimbot::almanac::TypeCategory(*from.type_);
  } else {
    type_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.aimbot.almanac.ModelinatorTypeCategory)
}

inline void ModelinatorTypeCategory::SharedCtor() {
type_ = nullptr;
}

ModelinatorTypeCategory::~ModelinatorTypeCategory() {
  // @@protoc_insertion_point(destructor:carbon.aimbot.almanac.ModelinatorTypeCategory)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ModelinatorTypeCategory::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete type_;
}

void ModelinatorTypeCategory::ArenaDtor(void* object) {
  ModelinatorTypeCategory* _this = reinterpret_cast< ModelinatorTypeCategory* >(object);
  (void)_this;
}
void ModelinatorTypeCategory::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ModelinatorTypeCategory::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ModelinatorTypeCategory::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.aimbot.almanac.ModelinatorTypeCategory)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  trusts_.Clear();
  if (GetArenaForAllocation() == nullptr && type_ != nullptr) {
    delete type_;
  }
  type_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ModelinatorTypeCategory::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.aimbot.almanac.TypeCategory type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_type(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.aimbot.almanac.ModelTrust trusts = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_trusts(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ModelinatorTypeCategory::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.aimbot.almanac.ModelinatorTypeCategory)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.TypeCategory type = 1;
  if (this->_internal_has_type()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::type(this), target, stream);
  }

  // repeated .carbon.aimbot.almanac.ModelTrust trusts = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_trusts_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_trusts(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.aimbot.almanac.ModelinatorTypeCategory)
  return target;
}

size_t ModelinatorTypeCategory::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.aimbot.almanac.ModelinatorTypeCategory)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.aimbot.almanac.ModelTrust trusts = 2;
  total_size += 1UL * this->_internal_trusts_size();
  for (const auto& msg : this->trusts_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.aimbot.almanac.TypeCategory type = 1;
  if (this->_internal_has_type()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *type_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ModelinatorTypeCategory::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ModelinatorTypeCategory::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ModelinatorTypeCategory::GetClassData() const { return &_class_data_; }

void ModelinatorTypeCategory::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ModelinatorTypeCategory *>(to)->MergeFrom(
      static_cast<const ModelinatorTypeCategory &>(from));
}


void ModelinatorTypeCategory::MergeFrom(const ModelinatorTypeCategory& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.aimbot.almanac.ModelinatorTypeCategory)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  trusts_.MergeFrom(from.trusts_);
  if (from._internal_has_type()) {
    _internal_mutable_type()->::carbon::aimbot::almanac::TypeCategory::MergeFrom(from._internal_type());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ModelinatorTypeCategory::CopyFrom(const ModelinatorTypeCategory& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.aimbot.almanac.ModelinatorTypeCategory)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ModelinatorTypeCategory::IsInitialized() const {
  return true;
}

void ModelinatorTypeCategory::InternalSwap(ModelinatorTypeCategory* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  trusts_.InternalSwap(&other->trusts_);
  swap(type_, other->type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ModelinatorTypeCategory::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2falmanac_2falmanac_2eproto_getter, &descriptor_table_proto_2falmanac_2falmanac_2eproto_once,
      file_level_metadata_proto_2falmanac_2falmanac_2eproto[6]);
}

// ===================================================================

class AlmanacConfig::_Internal {
 public:
};

AlmanacConfig::AlmanacConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  categories_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.aimbot.almanac.AlmanacConfig)
}
AlmanacConfig::AlmanacConfig(const AlmanacConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      categories_(from.categories_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  ::memcpy(&updated_ts_, &from.updated_ts_,
    static_cast<size_t>(reinterpret_cast<char*>(&protected__) -
    reinterpret_cast<char*>(&updated_ts_)) + sizeof(protected__));
  // @@protoc_insertion_point(copy_constructor:carbon.aimbot.almanac.AlmanacConfig)
}

inline void AlmanacConfig::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&updated_ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&protected__) -
    reinterpret_cast<char*>(&updated_ts_)) + sizeof(protected__));
}

AlmanacConfig::~AlmanacConfig() {
  // @@protoc_insertion_point(destructor:carbon.aimbot.almanac.AlmanacConfig)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AlmanacConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void AlmanacConfig::ArenaDtor(void* object) {
  AlmanacConfig* _this = reinterpret_cast< AlmanacConfig* >(object);
  (void)_this;
}
void AlmanacConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AlmanacConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AlmanacConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.aimbot.almanac.AlmanacConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  categories_.Clear();
  id_.ClearToEmpty();
  name_.ClearToEmpty();
  ::memset(&updated_ts_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&protected__) -
      reinterpret_cast<char*>(&updated_ts_)) + sizeof(protected__));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AlmanacConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.aimbot.almanac.AlmanacConfig.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.aimbot.almanac.AlmanacConfig.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool protected = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          protected__ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 updated_ts = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          updated_ts_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.aimbot.almanac.AlmanacTypeCategory categories = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_categories(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AlmanacConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.aimbot.almanac.AlmanacConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.aimbot.almanac.AlmanacConfig.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.aimbot.almanac.AlmanacConfig.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  // bool protected = 3;
  if (this->_internal_protected_() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_protected_(), target);
  }

  // uint64 updated_ts = 4;
  if (this->_internal_updated_ts() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(4, this->_internal_updated_ts(), target);
  }

  // repeated .carbon.aimbot.almanac.AlmanacTypeCategory categories = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_categories_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, this->_internal_categories(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.aimbot.almanac.AlmanacConfig)
  return target;
}

size_t AlmanacConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.aimbot.almanac.AlmanacConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.aimbot.almanac.AlmanacTypeCategory categories = 5;
  total_size += 1UL * this->_internal_categories_size();
  for (const auto& msg : this->categories_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // uint64 updated_ts = 4;
  if (this->_internal_updated_ts() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_updated_ts());
  }

  // bool protected = 3;
  if (this->_internal_protected_() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AlmanacConfig::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AlmanacConfig::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AlmanacConfig::GetClassData() const { return &_class_data_; }

void AlmanacConfig::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<AlmanacConfig *>(to)->MergeFrom(
      static_cast<const AlmanacConfig &>(from));
}


void AlmanacConfig::MergeFrom(const AlmanacConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.aimbot.almanac.AlmanacConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  categories_.MergeFrom(from.categories_);
  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (from._internal_updated_ts() != 0) {
    _internal_set_updated_ts(from._internal_updated_ts());
  }
  if (from._internal_protected_() != 0) {
    _internal_set_protected_(from._internal_protected_());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AlmanacConfig::CopyFrom(const AlmanacConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.aimbot.almanac.AlmanacConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AlmanacConfig::IsInitialized() const {
  return true;
}

void AlmanacConfig::InternalSwap(AlmanacConfig* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  categories_.InternalSwap(&other->categories_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(AlmanacConfig, protected__)
      + sizeof(AlmanacConfig::protected__)
      - PROTOBUF_FIELD_OFFSET(AlmanacConfig, updated_ts_)>(
          reinterpret_cast<char*>(&updated_ts_),
          reinterpret_cast<char*>(&other->updated_ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata AlmanacConfig::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2falmanac_2falmanac_2eproto_getter, &descriptor_table_proto_2falmanac_2falmanac_2eproto_once,
      file_level_metadata_proto_2falmanac_2falmanac_2eproto[7]);
}

// ===================================================================

class DiscriminatorConfig::_Internal {
 public:
};

DiscriminatorConfig::DiscriminatorConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  categories_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.aimbot.almanac.DiscriminatorConfig)
}
DiscriminatorConfig::DiscriminatorConfig(const DiscriminatorConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      categories_(from.categories_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  ::memcpy(&updated_ts_, &from.updated_ts_,
    static_cast<size_t>(reinterpret_cast<char*>(&protected__) -
    reinterpret_cast<char*>(&updated_ts_)) + sizeof(protected__));
  // @@protoc_insertion_point(copy_constructor:carbon.aimbot.almanac.DiscriminatorConfig)
}

inline void DiscriminatorConfig::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&updated_ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&protected__) -
    reinterpret_cast<char*>(&updated_ts_)) + sizeof(protected__));
}

DiscriminatorConfig::~DiscriminatorConfig() {
  // @@protoc_insertion_point(destructor:carbon.aimbot.almanac.DiscriminatorConfig)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DiscriminatorConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DiscriminatorConfig::ArenaDtor(void* object) {
  DiscriminatorConfig* _this = reinterpret_cast< DiscriminatorConfig* >(object);
  (void)_this;
}
void DiscriminatorConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DiscriminatorConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DiscriminatorConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.aimbot.almanac.DiscriminatorConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  categories_.Clear();
  id_.ClearToEmpty();
  name_.ClearToEmpty();
  ::memset(&updated_ts_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&protected__) -
      reinterpret_cast<char*>(&updated_ts_)) + sizeof(protected__));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DiscriminatorConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.aimbot.almanac.DiscriminatorConfig.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.aimbot.almanac.DiscriminatorConfig.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool protected = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          protected__ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 updated_ts = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          updated_ts_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.aimbot.almanac.DiscriminatorTypeCategory categories = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_categories(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DiscriminatorConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.aimbot.almanac.DiscriminatorConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.aimbot.almanac.DiscriminatorConfig.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.aimbot.almanac.DiscriminatorConfig.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  // bool protected = 3;
  if (this->_internal_protected_() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_protected_(), target);
  }

  // uint64 updated_ts = 4;
  if (this->_internal_updated_ts() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(4, this->_internal_updated_ts(), target);
  }

  // repeated .carbon.aimbot.almanac.DiscriminatorTypeCategory categories = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_categories_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, this->_internal_categories(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.aimbot.almanac.DiscriminatorConfig)
  return target;
}

size_t DiscriminatorConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.aimbot.almanac.DiscriminatorConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.aimbot.almanac.DiscriminatorTypeCategory categories = 5;
  total_size += 1UL * this->_internal_categories_size();
  for (const auto& msg : this->categories_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // uint64 updated_ts = 4;
  if (this->_internal_updated_ts() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_updated_ts());
  }

  // bool protected = 3;
  if (this->_internal_protected_() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DiscriminatorConfig::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DiscriminatorConfig::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DiscriminatorConfig::GetClassData() const { return &_class_data_; }

void DiscriminatorConfig::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DiscriminatorConfig *>(to)->MergeFrom(
      static_cast<const DiscriminatorConfig &>(from));
}


void DiscriminatorConfig::MergeFrom(const DiscriminatorConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.aimbot.almanac.DiscriminatorConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  categories_.MergeFrom(from.categories_);
  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (from._internal_updated_ts() != 0) {
    _internal_set_updated_ts(from._internal_updated_ts());
  }
  if (from._internal_protected_() != 0) {
    _internal_set_protected_(from._internal_protected_());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DiscriminatorConfig::CopyFrom(const DiscriminatorConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.aimbot.almanac.DiscriminatorConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DiscriminatorConfig::IsInitialized() const {
  return true;
}

void DiscriminatorConfig::InternalSwap(DiscriminatorConfig* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  categories_.InternalSwap(&other->categories_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DiscriminatorConfig, protected__)
      + sizeof(DiscriminatorConfig::protected__)
      - PROTOBUF_FIELD_OFFSET(DiscriminatorConfig, updated_ts_)>(
          reinterpret_cast<char*>(&updated_ts_),
          reinterpret_cast<char*>(&other->updated_ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DiscriminatorConfig::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2falmanac_2falmanac_2eproto_getter, &descriptor_table_proto_2falmanac_2falmanac_2eproto_once,
      file_level_metadata_proto_2falmanac_2falmanac_2eproto[8]);
}

// ===================================================================

class ModelinatorConfig::_Internal {
 public:
};

ModelinatorConfig::ModelinatorConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  categories_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.aimbot.almanac.ModelinatorConfig)
}
ModelinatorConfig::ModelinatorConfig(const ModelinatorConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      categories_(from.categories_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_id().empty()) {
    model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_id(), 
      GetArenaForAllocation());
  }
  crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop_id().empty()) {
    crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_id(), 
      GetArenaForAllocation());
  }
  modified_ = from.modified_;
  // @@protoc_insertion_point(copy_constructor:carbon.aimbot.almanac.ModelinatorConfig)
}

inline void ModelinatorConfig::SharedCtor() {
model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
modified_ = false;
}

ModelinatorConfig::~ModelinatorConfig() {
  // @@protoc_insertion_point(destructor:carbon.aimbot.almanac.ModelinatorConfig)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ModelinatorConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  model_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ModelinatorConfig::ArenaDtor(void* object) {
  ModelinatorConfig* _this = reinterpret_cast< ModelinatorConfig* >(object);
  (void)_this;
}
void ModelinatorConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ModelinatorConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ModelinatorConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.aimbot.almanac.ModelinatorConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  categories_.Clear();
  model_id_.ClearToEmpty();
  crop_id_.ClearToEmpty();
  modified_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ModelinatorConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string model_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_model_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.aimbot.almanac.ModelinatorConfig.model_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_crop_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.aimbot.almanac.ModelinatorConfig.crop_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool modified = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          modified_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.aimbot.almanac.ModelinatorTypeCategory categories = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_categories(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ModelinatorConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.aimbot.almanac.ModelinatorConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string model_id = 1;
  if (!this->_internal_model_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_id().data(), static_cast<int>(this->_internal_model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.aimbot.almanac.ModelinatorConfig.model_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_model_id(), target);
  }

  // string crop_id = 2;
  if (!this->_internal_crop_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_id().data(), static_cast<int>(this->_internal_crop_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.aimbot.almanac.ModelinatorConfig.crop_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_crop_id(), target);
  }

  // bool modified = 3;
  if (this->_internal_modified() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_modified(), target);
  }

  // repeated .carbon.aimbot.almanac.ModelinatorTypeCategory categories = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_categories_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, this->_internal_categories(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.aimbot.almanac.ModelinatorConfig)
  return target;
}

size_t ModelinatorConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.aimbot.almanac.ModelinatorConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.aimbot.almanac.ModelinatorTypeCategory categories = 4;
  total_size += 1UL * this->_internal_categories_size();
  for (const auto& msg : this->categories_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string model_id = 1;
  if (!this->_internal_model_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_id());
  }

  // string crop_id = 2;
  if (!this->_internal_crop_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_id());
  }

  // bool modified = 3;
  if (this->_internal_modified() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ModelinatorConfig::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ModelinatorConfig::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ModelinatorConfig::GetClassData() const { return &_class_data_; }

void ModelinatorConfig::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ModelinatorConfig *>(to)->MergeFrom(
      static_cast<const ModelinatorConfig &>(from));
}


void ModelinatorConfig::MergeFrom(const ModelinatorConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.aimbot.almanac.ModelinatorConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  categories_.MergeFrom(from.categories_);
  if (!from._internal_model_id().empty()) {
    _internal_set_model_id(from._internal_model_id());
  }
  if (!from._internal_crop_id().empty()) {
    _internal_set_crop_id(from._internal_crop_id());
  }
  if (from._internal_modified() != 0) {
    _internal_set_modified(from._internal_modified());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ModelinatorConfig::CopyFrom(const ModelinatorConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.aimbot.almanac.ModelinatorConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ModelinatorConfig::IsInitialized() const {
  return true;
}

void ModelinatorConfig::InternalSwap(ModelinatorConfig* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  categories_.InternalSwap(&other->categories_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_id_, lhs_arena,
      &other->model_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_id_, lhs_arena,
      &other->crop_id_, rhs_arena
  );
  swap(modified_, other->modified_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ModelinatorConfig::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2falmanac_2falmanac_2eproto_getter, &descriptor_table_proto_2falmanac_2falmanac_2eproto_once,
      file_level_metadata_proto_2falmanac_2falmanac_2eproto[9]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace almanac
}  // namespace aimbot
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::aimbot::almanac::Formula* Arena::CreateMaybeMessage< ::carbon::aimbot::almanac::Formula >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::aimbot::almanac::Formula >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::aimbot::almanac::Trust* Arena::CreateMaybeMessage< ::carbon::aimbot::almanac::Trust >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::aimbot::almanac::Trust >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::aimbot::almanac::ModelTrust* Arena::CreateMaybeMessage< ::carbon::aimbot::almanac::ModelTrust >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::aimbot::almanac::ModelTrust >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::aimbot::almanac::TypeCategory* Arena::CreateMaybeMessage< ::carbon::aimbot::almanac::TypeCategory >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::aimbot::almanac::TypeCategory >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::aimbot::almanac::AlmanacTypeCategory* Arena::CreateMaybeMessage< ::carbon::aimbot::almanac::AlmanacTypeCategory >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::aimbot::almanac::AlmanacTypeCategory >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::aimbot::almanac::DiscriminatorTypeCategory* Arena::CreateMaybeMessage< ::carbon::aimbot::almanac::DiscriminatorTypeCategory >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::aimbot::almanac::DiscriminatorTypeCategory >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::aimbot::almanac::ModelinatorTypeCategory* Arena::CreateMaybeMessage< ::carbon::aimbot::almanac::ModelinatorTypeCategory >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::aimbot::almanac::ModelinatorTypeCategory >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::aimbot::almanac::AlmanacConfig* Arena::CreateMaybeMessage< ::carbon::aimbot::almanac::AlmanacConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::aimbot::almanac::AlmanacConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::aimbot::almanac::DiscriminatorConfig* Arena::CreateMaybeMessage< ::carbon::aimbot::almanac::DiscriminatorConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::aimbot::almanac::DiscriminatorConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::aimbot::almanac::ModelinatorConfig* Arena::CreateMaybeMessage< ::carbon::aimbot::almanac::ModelinatorConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::aimbot::almanac::ModelinatorConfig >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

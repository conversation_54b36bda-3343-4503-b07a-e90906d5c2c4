# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.proto.webrtc import webrtc_pb2 as proto_dot_webrtc_dot_webrtc__pb2


class WebrtcServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateConnection = channel.unary_unary(
                '/carbon.webrtc.WebrtcService/CreateConnection',
                request_serializer=proto_dot_webrtc_dot_webrtc__pb2.CreateConnectionRequest.SerializeToString,
                response_deserializer=proto_dot_webrtc_dot_webrtc__pb2.CreateConnectionResponse.FromString,
                )
        self.SetRemoteSdp = channel.unary_unary(
                '/carbon.webrtc.WebrtcService/SetRemoteSdp',
                request_serializer=proto_dot_webrtc_dot_webrtc__pb2.SetRemoteSdpRequest.SerializeToString,
                response_deserializer=proto_dot_webrtc_dot_webrtc__pb2.SetRemoteSdpResponse.FromString,
                )


class WebrtcServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def CreateConnection(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetRemoteSdp(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_WebrtcServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateConnection': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateConnection,
                    request_deserializer=proto_dot_webrtc_dot_webrtc__pb2.CreateConnectionRequest.FromString,
                    response_serializer=proto_dot_webrtc_dot_webrtc__pb2.CreateConnectionResponse.SerializeToString,
            ),
            'SetRemoteSdp': grpc.unary_unary_rpc_method_handler(
                    servicer.SetRemoteSdp,
                    request_deserializer=proto_dot_webrtc_dot_webrtc__pb2.SetRemoteSdpRequest.FromString,
                    response_serializer=proto_dot_webrtc_dot_webrtc__pb2.SetRemoteSdpResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.webrtc.WebrtcService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class WebrtcService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def CreateConnection(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.webrtc.WebrtcService/CreateConnection',
            proto_dot_webrtc_dot_webrtc__pb2.CreateConnectionRequest.SerializeToString,
            proto_dot_webrtc_dot_webrtc__pb2.CreateConnectionResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetRemoteSdp(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.webrtc.WebrtcService/SetRemoteSdp',
            proto_dot_webrtc_dot_webrtc__pb2.SetRemoteSdpRequest.SerializeToString,
            proto_dot_webrtc_dot_webrtc__pb2.SetRemoteSdpResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/webrtc/webrtc.proto

#include "proto/webrtc/webrtc.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/io/zero_copy_stream_impl_lite.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace webrtc {
constexpr CreateConnectionRequest::CreateConnectionRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct CreateConnectionRequestDefaultTypeInternal {
  constexpr CreateConnectionRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CreateConnectionRequestDefaultTypeInternal() {}
  union {
    CreateConnectionRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CreateConnectionRequestDefaultTypeInternal _CreateConnectionRequest_default_instance_;
constexpr CreateConnectionResponse::CreateConnectionResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : sdp_json_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct CreateConnectionResponseDefaultTypeInternal {
  constexpr CreateConnectionResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CreateConnectionResponseDefaultTypeInternal() {}
  union {
    CreateConnectionResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CreateConnectionResponseDefaultTypeInternal _CreateConnectionResponse_default_instance_;
constexpr SetRemoteSdpRequest::SetRemoteSdpRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , sdp_json_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct SetRemoteSdpRequestDefaultTypeInternal {
  constexpr SetRemoteSdpRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetRemoteSdpRequestDefaultTypeInternal() {}
  union {
    SetRemoteSdpRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetRemoteSdpRequestDefaultTypeInternal _SetRemoteSdpRequest_default_instance_;
constexpr SetRemoteSdpResponse::SetRemoteSdpResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct SetRemoteSdpResponseDefaultTypeInternal {
  constexpr SetRemoteSdpResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetRemoteSdpResponseDefaultTypeInternal() {}
  union {
    SetRemoteSdpResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetRemoteSdpResponseDefaultTypeInternal _SetRemoteSdpResponse_default_instance_;
}  // namespace webrtc
}  // namespace carbon
namespace carbon {
namespace webrtc {

// ===================================================================

class CreateConnectionRequest::_Internal {
 public:
};

CreateConnectionRequest::CreateConnectionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.webrtc.CreateConnectionRequest)
}
CreateConnectionRequest::CreateConnectionRequest(const CreateConnectionRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite() {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.webrtc.CreateConnectionRequest)
}

inline void CreateConnectionRequest::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

CreateConnectionRequest::~CreateConnectionRequest() {
  // @@protoc_insertion_point(destructor:carbon.webrtc.CreateConnectionRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<std::string>();
}

inline void CreateConnectionRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CreateConnectionRequest::ArenaDtor(void* object) {
  CreateConnectionRequest* _this = reinterpret_cast< CreateConnectionRequest* >(object);
  (void)_this;
}
void CreateConnectionRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CreateConnectionRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CreateConnectionRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.webrtc.CreateConnectionRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  _internal_metadata_.Clear<std::string>();
}

const char* CreateConnectionRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, nullptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CreateConnectionRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.webrtc.CreateConnectionRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.webrtc.CreateConnectionRequest.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.webrtc.CreateConnectionRequest)
  return target;
}

size_t CreateConnectionRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.webrtc.CreateConnectionRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CreateConnectionRequest::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::PROTOBUF_NAMESPACE_ID::internal::DownCast<const CreateConnectionRequest*>(
      &from));
}

void CreateConnectionRequest::MergeFrom(const CreateConnectionRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.webrtc.CreateConnectionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void CreateConnectionRequest::CopyFrom(const CreateConnectionRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.webrtc.CreateConnectionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CreateConnectionRequest::IsInitialized() const {
  return true;
}

void CreateConnectionRequest::InternalSwap(CreateConnectionRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
}

std::string CreateConnectionRequest::GetTypeName() const {
  return "carbon.webrtc.CreateConnectionRequest";
}


// ===================================================================

class CreateConnectionResponse::_Internal {
 public:
};

CreateConnectionResponse::CreateConnectionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.webrtc.CreateConnectionResponse)
}
CreateConnectionResponse::CreateConnectionResponse(const CreateConnectionResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite() {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  sdp_json_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    sdp_json_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_sdp_json().empty()) {
    sdp_json_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_sdp_json(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.webrtc.CreateConnectionResponse)
}

inline void CreateConnectionResponse::SharedCtor() {
sdp_json_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  sdp_json_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

CreateConnectionResponse::~CreateConnectionResponse() {
  // @@protoc_insertion_point(destructor:carbon.webrtc.CreateConnectionResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<std::string>();
}

inline void CreateConnectionResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  sdp_json_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CreateConnectionResponse::ArenaDtor(void* object) {
  CreateConnectionResponse* _this = reinterpret_cast< CreateConnectionResponse* >(object);
  (void)_this;
}
void CreateConnectionResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CreateConnectionResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CreateConnectionResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.webrtc.CreateConnectionResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  sdp_json_.ClearToEmpty();
  _internal_metadata_.Clear<std::string>();
}

const char* CreateConnectionResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string sdp_json = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_sdp_json();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, nullptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CreateConnectionResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.webrtc.CreateConnectionResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string sdp_json = 1;
  if (!this->_internal_sdp_json().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_sdp_json().data(), static_cast<int>(this->_internal_sdp_json().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.webrtc.CreateConnectionResponse.sdp_json");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_sdp_json(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.webrtc.CreateConnectionResponse)
  return target;
}

size_t CreateConnectionResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.webrtc.CreateConnectionResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string sdp_json = 1;
  if (!this->_internal_sdp_json().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_sdp_json());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CreateConnectionResponse::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::PROTOBUF_NAMESPACE_ID::internal::DownCast<const CreateConnectionResponse*>(
      &from));
}

void CreateConnectionResponse::MergeFrom(const CreateConnectionResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.webrtc.CreateConnectionResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_sdp_json().empty()) {
    _internal_set_sdp_json(from._internal_sdp_json());
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void CreateConnectionResponse::CopyFrom(const CreateConnectionResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.webrtc.CreateConnectionResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CreateConnectionResponse::IsInitialized() const {
  return true;
}

void CreateConnectionResponse::InternalSwap(CreateConnectionResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &sdp_json_, lhs_arena,
      &other->sdp_json_, rhs_arena
  );
}

std::string CreateConnectionResponse::GetTypeName() const {
  return "carbon.webrtc.CreateConnectionResponse";
}


// ===================================================================

class SetRemoteSdpRequest::_Internal {
 public:
};

SetRemoteSdpRequest::SetRemoteSdpRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.webrtc.SetRemoteSdpRequest)
}
SetRemoteSdpRequest::SetRemoteSdpRequest(const SetRemoteSdpRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite() {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  sdp_json_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    sdp_json_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_sdp_json().empty()) {
    sdp_json_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_sdp_json(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.webrtc.SetRemoteSdpRequest)
}

inline void SetRemoteSdpRequest::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
sdp_json_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  sdp_json_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SetRemoteSdpRequest::~SetRemoteSdpRequest() {
  // @@protoc_insertion_point(destructor:carbon.webrtc.SetRemoteSdpRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<std::string>();
}

inline void SetRemoteSdpRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  sdp_json_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SetRemoteSdpRequest::ArenaDtor(void* object) {
  SetRemoteSdpRequest* _this = reinterpret_cast< SetRemoteSdpRequest* >(object);
  (void)_this;
}
void SetRemoteSdpRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetRemoteSdpRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetRemoteSdpRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.webrtc.SetRemoteSdpRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  sdp_json_.ClearToEmpty();
  _internal_metadata_.Clear<std::string>();
}

const char* SetRemoteSdpRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, nullptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string sdp_json = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_sdp_json();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, nullptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetRemoteSdpRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.webrtc.SetRemoteSdpRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.webrtc.SetRemoteSdpRequest.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  // string sdp_json = 2;
  if (!this->_internal_sdp_json().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_sdp_json().data(), static_cast<int>(this->_internal_sdp_json().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.webrtc.SetRemoteSdpRequest.sdp_json");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_sdp_json(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.webrtc.SetRemoteSdpRequest)
  return target;
}

size_t SetRemoteSdpRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.webrtc.SetRemoteSdpRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // string sdp_json = 2;
  if (!this->_internal_sdp_json().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_sdp_json());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SetRemoteSdpRequest::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::PROTOBUF_NAMESPACE_ID::internal::DownCast<const SetRemoteSdpRequest*>(
      &from));
}

void SetRemoteSdpRequest::MergeFrom(const SetRemoteSdpRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.webrtc.SetRemoteSdpRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (!from._internal_sdp_json().empty()) {
    _internal_set_sdp_json(from._internal_sdp_json());
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void SetRemoteSdpRequest::CopyFrom(const SetRemoteSdpRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.webrtc.SetRemoteSdpRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetRemoteSdpRequest::IsInitialized() const {
  return true;
}

void SetRemoteSdpRequest::InternalSwap(SetRemoteSdpRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &sdp_json_, lhs_arena,
      &other->sdp_json_, rhs_arena
  );
}

std::string SetRemoteSdpRequest::GetTypeName() const {
  return "carbon.webrtc.SetRemoteSdpRequest";
}


// ===================================================================

class SetRemoteSdpResponse::_Internal {
 public:
};

SetRemoteSdpResponse::SetRemoteSdpResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.webrtc.SetRemoteSdpResponse)
}
SetRemoteSdpResponse::SetRemoteSdpResponse(const SetRemoteSdpResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite() {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.webrtc.SetRemoteSdpResponse)
}

inline void SetRemoteSdpResponse::SharedCtor() {
}

SetRemoteSdpResponse::~SetRemoteSdpResponse() {
  // @@protoc_insertion_point(destructor:carbon.webrtc.SetRemoteSdpResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<std::string>();
}

inline void SetRemoteSdpResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SetRemoteSdpResponse::ArenaDtor(void* object) {
  SetRemoteSdpResponse* _this = reinterpret_cast< SetRemoteSdpResponse* >(object);
  (void)_this;
}
void SetRemoteSdpResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetRemoteSdpResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetRemoteSdpResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.webrtc.SetRemoteSdpResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear<std::string>();
}

const char* SetRemoteSdpResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetRemoteSdpResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.webrtc.SetRemoteSdpResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.webrtc.SetRemoteSdpResponse)
  return target;
}

size_t SetRemoteSdpResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.webrtc.SetRemoteSdpResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SetRemoteSdpResponse::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::PROTOBUF_NAMESPACE_ID::internal::DownCast<const SetRemoteSdpResponse*>(
      &from));
}

void SetRemoteSdpResponse::MergeFrom(const SetRemoteSdpResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.webrtc.SetRemoteSdpResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void SetRemoteSdpResponse::CopyFrom(const SetRemoteSdpResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.webrtc.SetRemoteSdpResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetRemoteSdpResponse::IsInitialized() const {
  return true;
}

void SetRemoteSdpResponse::InternalSwap(SetRemoteSdpResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
}

std::string SetRemoteSdpResponse::GetTypeName() const {
  return "carbon.webrtc.SetRemoteSdpResponse";
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace webrtc
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::webrtc::CreateConnectionRequest* Arena::CreateMaybeMessage< ::carbon::webrtc::CreateConnectionRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::webrtc::CreateConnectionRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::webrtc::CreateConnectionResponse* Arena::CreateMaybeMessage< ::carbon::webrtc::CreateConnectionResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::webrtc::CreateConnectionResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::webrtc::SetRemoteSdpRequest* Arena::CreateMaybeMessage< ::carbon::webrtc::SetRemoteSdpRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::webrtc::SetRemoteSdpRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::webrtc::SetRemoteSdpResponse* Arena::CreateMaybeMessage< ::carbon::webrtc::SetRemoteSdpResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::webrtc::SetRemoteSdpResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

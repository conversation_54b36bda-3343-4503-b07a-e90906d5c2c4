// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/webrtc/webrtc.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_proto_2fwebrtc_2fwebrtc_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_proto_2fwebrtc_2fwebrtc_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/message_lite.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_proto_2fwebrtc_2fwebrtc_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_proto_2fwebrtc_2fwebrtc_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[4]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
namespace carbon {
namespace webrtc {
class CreateConnectionRequest;
struct CreateConnectionRequestDefaultTypeInternal;
extern CreateConnectionRequestDefaultTypeInternal _CreateConnectionRequest_default_instance_;
class CreateConnectionResponse;
struct CreateConnectionResponseDefaultTypeInternal;
extern CreateConnectionResponseDefaultTypeInternal _CreateConnectionResponse_default_instance_;
class SetRemoteSdpRequest;
struct SetRemoteSdpRequestDefaultTypeInternal;
extern SetRemoteSdpRequestDefaultTypeInternal _SetRemoteSdpRequest_default_instance_;
class SetRemoteSdpResponse;
struct SetRemoteSdpResponseDefaultTypeInternal;
extern SetRemoteSdpResponseDefaultTypeInternal _SetRemoteSdpResponse_default_instance_;
}  // namespace webrtc
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::webrtc::CreateConnectionRequest* Arena::CreateMaybeMessage<::carbon::webrtc::CreateConnectionRequest>(Arena*);
template<> ::carbon::webrtc::CreateConnectionResponse* Arena::CreateMaybeMessage<::carbon::webrtc::CreateConnectionResponse>(Arena*);
template<> ::carbon::webrtc::SetRemoteSdpRequest* Arena::CreateMaybeMessage<::carbon::webrtc::SetRemoteSdpRequest>(Arena*);
template<> ::carbon::webrtc::SetRemoteSdpResponse* Arena::CreateMaybeMessage<::carbon::webrtc::SetRemoteSdpResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace webrtc {

// ===================================================================

class CreateConnectionRequest final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:carbon.webrtc.CreateConnectionRequest) */ {
 public:
  inline CreateConnectionRequest() : CreateConnectionRequest(nullptr) {}
  ~CreateConnectionRequest() override;
  explicit constexpr CreateConnectionRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CreateConnectionRequest(const CreateConnectionRequest& from);
  CreateConnectionRequest(CreateConnectionRequest&& from) noexcept
    : CreateConnectionRequest() {
    *this = ::std::move(from);
  }

  inline CreateConnectionRequest& operator=(const CreateConnectionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CreateConnectionRequest& operator=(CreateConnectionRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const CreateConnectionRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const CreateConnectionRequest* internal_default_instance() {
    return reinterpret_cast<const CreateConnectionRequest*>(
               &_CreateConnectionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CreateConnectionRequest& a, CreateConnectionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CreateConnectionRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CreateConnectionRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CreateConnectionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CreateConnectionRequest>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const CreateConnectionRequest& from);
  void MergeFrom(const CreateConnectionRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(CreateConnectionRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.webrtc.CreateConnectionRequest";
  }
  protected:
  explicit CreateConnectionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.webrtc.CreateConnectionRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fwebrtc_2fwebrtc_2eproto;
};
// -------------------------------------------------------------------

class CreateConnectionResponse final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:carbon.webrtc.CreateConnectionResponse) */ {
 public:
  inline CreateConnectionResponse() : CreateConnectionResponse(nullptr) {}
  ~CreateConnectionResponse() override;
  explicit constexpr CreateConnectionResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CreateConnectionResponse(const CreateConnectionResponse& from);
  CreateConnectionResponse(CreateConnectionResponse&& from) noexcept
    : CreateConnectionResponse() {
    *this = ::std::move(from);
  }

  inline CreateConnectionResponse& operator=(const CreateConnectionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CreateConnectionResponse& operator=(CreateConnectionResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const CreateConnectionResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const CreateConnectionResponse* internal_default_instance() {
    return reinterpret_cast<const CreateConnectionResponse*>(
               &_CreateConnectionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(CreateConnectionResponse& a, CreateConnectionResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CreateConnectionResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CreateConnectionResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CreateConnectionResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CreateConnectionResponse>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const CreateConnectionResponse& from);
  void MergeFrom(const CreateConnectionResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(CreateConnectionResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.webrtc.CreateConnectionResponse";
  }
  protected:
  explicit CreateConnectionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSdpJsonFieldNumber = 1,
  };
  // string sdp_json = 1;
  void clear_sdp_json();
  const std::string& sdp_json() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_sdp_json(ArgT0&& arg0, ArgT... args);
  std::string* mutable_sdp_json();
  PROTOBUF_NODISCARD std::string* release_sdp_json();
  void set_allocated_sdp_json(std::string* sdp_json);
  private:
  const std::string& _internal_sdp_json() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_sdp_json(const std::string& value);
  std::string* _internal_mutable_sdp_json();
  public:

  // @@protoc_insertion_point(class_scope:carbon.webrtc.CreateConnectionResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr sdp_json_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fwebrtc_2fwebrtc_2eproto;
};
// -------------------------------------------------------------------

class SetRemoteSdpRequest final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:carbon.webrtc.SetRemoteSdpRequest) */ {
 public:
  inline SetRemoteSdpRequest() : SetRemoteSdpRequest(nullptr) {}
  ~SetRemoteSdpRequest() override;
  explicit constexpr SetRemoteSdpRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetRemoteSdpRequest(const SetRemoteSdpRequest& from);
  SetRemoteSdpRequest(SetRemoteSdpRequest&& from) noexcept
    : SetRemoteSdpRequest() {
    *this = ::std::move(from);
  }

  inline SetRemoteSdpRequest& operator=(const SetRemoteSdpRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetRemoteSdpRequest& operator=(SetRemoteSdpRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const SetRemoteSdpRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetRemoteSdpRequest* internal_default_instance() {
    return reinterpret_cast<const SetRemoteSdpRequest*>(
               &_SetRemoteSdpRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(SetRemoteSdpRequest& a, SetRemoteSdpRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetRemoteSdpRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetRemoteSdpRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetRemoteSdpRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetRemoteSdpRequest>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const SetRemoteSdpRequest& from);
  void MergeFrom(const SetRemoteSdpRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(SetRemoteSdpRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.webrtc.SetRemoteSdpRequest";
  }
  protected:
  explicit SetRemoteSdpRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kSdpJsonFieldNumber = 2,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // string sdp_json = 2;
  void clear_sdp_json();
  const std::string& sdp_json() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_sdp_json(ArgT0&& arg0, ArgT... args);
  std::string* mutable_sdp_json();
  PROTOBUF_NODISCARD std::string* release_sdp_json();
  void set_allocated_sdp_json(std::string* sdp_json);
  private:
  const std::string& _internal_sdp_json() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_sdp_json(const std::string& value);
  std::string* _internal_mutable_sdp_json();
  public:

  // @@protoc_insertion_point(class_scope:carbon.webrtc.SetRemoteSdpRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr sdp_json_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fwebrtc_2fwebrtc_2eproto;
};
// -------------------------------------------------------------------

class SetRemoteSdpResponse final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:carbon.webrtc.SetRemoteSdpResponse) */ {
 public:
  inline SetRemoteSdpResponse() : SetRemoteSdpResponse(nullptr) {}
  ~SetRemoteSdpResponse() override;
  explicit constexpr SetRemoteSdpResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetRemoteSdpResponse(const SetRemoteSdpResponse& from);
  SetRemoteSdpResponse(SetRemoteSdpResponse&& from) noexcept
    : SetRemoteSdpResponse() {
    *this = ::std::move(from);
  }

  inline SetRemoteSdpResponse& operator=(const SetRemoteSdpResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetRemoteSdpResponse& operator=(SetRemoteSdpResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const SetRemoteSdpResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetRemoteSdpResponse* internal_default_instance() {
    return reinterpret_cast<const SetRemoteSdpResponse*>(
               &_SetRemoteSdpResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(SetRemoteSdpResponse& a, SetRemoteSdpResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(SetRemoteSdpResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetRemoteSdpResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetRemoteSdpResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetRemoteSdpResponse>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const SetRemoteSdpResponse& from);
  void MergeFrom(const SetRemoteSdpResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(SetRemoteSdpResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.webrtc.SetRemoteSdpResponse";
  }
  protected:
  explicit SetRemoteSdpResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.webrtc.SetRemoteSdpResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fwebrtc_2fwebrtc_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CreateConnectionRequest

// string id = 1;
inline void CreateConnectionRequest::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& CreateConnectionRequest::id() const {
  // @@protoc_insertion_point(field_get:carbon.webrtc.CreateConnectionRequest.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CreateConnectionRequest::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.webrtc.CreateConnectionRequest.id)
}
inline std::string* CreateConnectionRequest::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.webrtc.CreateConnectionRequest.id)
  return _s;
}
inline const std::string& CreateConnectionRequest::_internal_id() const {
  return id_.Get();
}
inline void CreateConnectionRequest::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CreateConnectionRequest::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CreateConnectionRequest::release_id() {
  // @@protoc_insertion_point(field_release:carbon.webrtc.CreateConnectionRequest.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CreateConnectionRequest::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.webrtc.CreateConnectionRequest.id)
}

// -------------------------------------------------------------------

// CreateConnectionResponse

// string sdp_json = 1;
inline void CreateConnectionResponse::clear_sdp_json() {
  sdp_json_.ClearToEmpty();
}
inline const std::string& CreateConnectionResponse::sdp_json() const {
  // @@protoc_insertion_point(field_get:carbon.webrtc.CreateConnectionResponse.sdp_json)
  return _internal_sdp_json();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CreateConnectionResponse::set_sdp_json(ArgT0&& arg0, ArgT... args) {
 
 sdp_json_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.webrtc.CreateConnectionResponse.sdp_json)
}
inline std::string* CreateConnectionResponse::mutable_sdp_json() {
  std::string* _s = _internal_mutable_sdp_json();
  // @@protoc_insertion_point(field_mutable:carbon.webrtc.CreateConnectionResponse.sdp_json)
  return _s;
}
inline const std::string& CreateConnectionResponse::_internal_sdp_json() const {
  return sdp_json_.Get();
}
inline void CreateConnectionResponse::_internal_set_sdp_json(const std::string& value) {
  
  sdp_json_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CreateConnectionResponse::_internal_mutable_sdp_json() {
  
  return sdp_json_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CreateConnectionResponse::release_sdp_json() {
  // @@protoc_insertion_point(field_release:carbon.webrtc.CreateConnectionResponse.sdp_json)
  return sdp_json_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CreateConnectionResponse::set_allocated_sdp_json(std::string* sdp_json) {
  if (sdp_json != nullptr) {
    
  } else {
    
  }
  sdp_json_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), sdp_json,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (sdp_json_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    sdp_json_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.webrtc.CreateConnectionResponse.sdp_json)
}

// -------------------------------------------------------------------

// SetRemoteSdpRequest

// string id = 1;
inline void SetRemoteSdpRequest::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& SetRemoteSdpRequest::id() const {
  // @@protoc_insertion_point(field_get:carbon.webrtc.SetRemoteSdpRequest.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetRemoteSdpRequest::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.webrtc.SetRemoteSdpRequest.id)
}
inline std::string* SetRemoteSdpRequest::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.webrtc.SetRemoteSdpRequest.id)
  return _s;
}
inline const std::string& SetRemoteSdpRequest::_internal_id() const {
  return id_.Get();
}
inline void SetRemoteSdpRequest::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetRemoteSdpRequest::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetRemoteSdpRequest::release_id() {
  // @@protoc_insertion_point(field_release:carbon.webrtc.SetRemoteSdpRequest.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetRemoteSdpRequest::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.webrtc.SetRemoteSdpRequest.id)
}

// string sdp_json = 2;
inline void SetRemoteSdpRequest::clear_sdp_json() {
  sdp_json_.ClearToEmpty();
}
inline const std::string& SetRemoteSdpRequest::sdp_json() const {
  // @@protoc_insertion_point(field_get:carbon.webrtc.SetRemoteSdpRequest.sdp_json)
  return _internal_sdp_json();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetRemoteSdpRequest::set_sdp_json(ArgT0&& arg0, ArgT... args) {
 
 sdp_json_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.webrtc.SetRemoteSdpRequest.sdp_json)
}
inline std::string* SetRemoteSdpRequest::mutable_sdp_json() {
  std::string* _s = _internal_mutable_sdp_json();
  // @@protoc_insertion_point(field_mutable:carbon.webrtc.SetRemoteSdpRequest.sdp_json)
  return _s;
}
inline const std::string& SetRemoteSdpRequest::_internal_sdp_json() const {
  return sdp_json_.Get();
}
inline void SetRemoteSdpRequest::_internal_set_sdp_json(const std::string& value) {
  
  sdp_json_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetRemoteSdpRequest::_internal_mutable_sdp_json() {
  
  return sdp_json_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetRemoteSdpRequest::release_sdp_json() {
  // @@protoc_insertion_point(field_release:carbon.webrtc.SetRemoteSdpRequest.sdp_json)
  return sdp_json_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetRemoteSdpRequest::set_allocated_sdp_json(std::string* sdp_json) {
  if (sdp_json != nullptr) {
    
  } else {
    
  }
  sdp_json_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), sdp_json,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (sdp_json_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    sdp_json_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.webrtc.SetRemoteSdpRequest.sdp_json)
}

// -------------------------------------------------------------------

// SetRemoteSdpResponse

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace webrtc
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_proto_2fwebrtc_2fwebrtc_2eproto

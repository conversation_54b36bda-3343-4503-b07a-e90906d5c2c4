# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/webrtc/webrtc.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/webrtc/webrtc.proto',
  package='carbon.webrtc',
  syntax='proto3',
  serialized_options=b'H\003Z\014proto/webrtc',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x19proto/webrtc/webrtc.proto\x12\rcarbon.webrtc\"%\n\x17\x43reateConnectionRequest\x12\n\n\x02id\x18\x01 \x01(\t\",\n\x18\x43reateConnectionResponse\x12\x10\n\x08sdp_json\x18\x01 \x01(\t\"3\n\x13SetRemoteSdpRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x10\n\x08sdp_json\x18\x02 \x01(\t\"\x16\n\x14SetRemoteSdpResponse2\xcd\x01\n\rWebrtcService\x12\x63\n\x10\x43reateConnection\x12&.carbon.webrtc.CreateConnectionRequest\x1a\'.carbon.webrtc.CreateConnectionResponse\x12W\n\x0cSetRemoteSdp\x12\".carbon.webrtc.SetRemoteSdpRequest\x1a#.carbon.webrtc.SetRemoteSdpResponseB\x10H\x03Z\x0cproto/webrtcb\x06proto3'
)




_CREATECONNECTIONREQUEST = _descriptor.Descriptor(
  name='CreateConnectionRequest',
  full_name='carbon.webrtc.CreateConnectionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.webrtc.CreateConnectionRequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=44,
  serialized_end=81,
)


_CREATECONNECTIONRESPONSE = _descriptor.Descriptor(
  name='CreateConnectionResponse',
  full_name='carbon.webrtc.CreateConnectionResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='sdp_json', full_name='carbon.webrtc.CreateConnectionResponse.sdp_json', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=83,
  serialized_end=127,
)


_SETREMOTESDPREQUEST = _descriptor.Descriptor(
  name='SetRemoteSdpRequest',
  full_name='carbon.webrtc.SetRemoteSdpRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.webrtc.SetRemoteSdpRequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sdp_json', full_name='carbon.webrtc.SetRemoteSdpRequest.sdp_json', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=129,
  serialized_end=180,
)


_SETREMOTESDPRESPONSE = _descriptor.Descriptor(
  name='SetRemoteSdpResponse',
  full_name='carbon.webrtc.SetRemoteSdpResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=182,
  serialized_end=204,
)

DESCRIPTOR.message_types_by_name['CreateConnectionRequest'] = _CREATECONNECTIONREQUEST
DESCRIPTOR.message_types_by_name['CreateConnectionResponse'] = _CREATECONNECTIONRESPONSE
DESCRIPTOR.message_types_by_name['SetRemoteSdpRequest'] = _SETREMOTESDPREQUEST
DESCRIPTOR.message_types_by_name['SetRemoteSdpResponse'] = _SETREMOTESDPRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CreateConnectionRequest = _reflection.GeneratedProtocolMessageType('CreateConnectionRequest', (_message.Message,), {
  'DESCRIPTOR' : _CREATECONNECTIONREQUEST,
  '__module__' : 'proto.webrtc.webrtc_pb2'
  # @@protoc_insertion_point(class_scope:carbon.webrtc.CreateConnectionRequest)
  })
_sym_db.RegisterMessage(CreateConnectionRequest)

CreateConnectionResponse = _reflection.GeneratedProtocolMessageType('CreateConnectionResponse', (_message.Message,), {
  'DESCRIPTOR' : _CREATECONNECTIONRESPONSE,
  '__module__' : 'proto.webrtc.webrtc_pb2'
  # @@protoc_insertion_point(class_scope:carbon.webrtc.CreateConnectionResponse)
  })
_sym_db.RegisterMessage(CreateConnectionResponse)

SetRemoteSdpRequest = _reflection.GeneratedProtocolMessageType('SetRemoteSdpRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETREMOTESDPREQUEST,
  '__module__' : 'proto.webrtc.webrtc_pb2'
  # @@protoc_insertion_point(class_scope:carbon.webrtc.SetRemoteSdpRequest)
  })
_sym_db.RegisterMessage(SetRemoteSdpRequest)

SetRemoteSdpResponse = _reflection.GeneratedProtocolMessageType('SetRemoteSdpResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETREMOTESDPRESPONSE,
  '__module__' : 'proto.webrtc.webrtc_pb2'
  # @@protoc_insertion_point(class_scope:carbon.webrtc.SetRemoteSdpResponse)
  })
_sym_db.RegisterMessage(SetRemoteSdpResponse)


DESCRIPTOR._options = None

_WEBRTCSERVICE = _descriptor.ServiceDescriptor(
  name='WebrtcService',
  full_name='carbon.webrtc.WebrtcService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=207,
  serialized_end=412,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateConnection',
    full_name='carbon.webrtc.WebrtcService.CreateConnection',
    index=0,
    containing_service=None,
    input_type=_CREATECONNECTIONREQUEST,
    output_type=_CREATECONNECTIONRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetRemoteSdp',
    full_name='carbon.webrtc.WebrtcService.SetRemoteSdp',
    index=1,
    containing_service=None,
    input_type=_SETREMOTESDPREQUEST,
    output_type=_SETREMOTESDPRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_WEBRTCSERVICE)

DESCRIPTOR.services_by_name['WebrtcService'] = _WEBRTCSERVICE

# @@protoc_insertion_point(module_scope)

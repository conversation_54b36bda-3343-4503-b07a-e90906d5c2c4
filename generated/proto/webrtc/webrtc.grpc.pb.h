// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: proto/webrtc/webrtc.proto
#ifndef GRPC_proto_2fwebrtc_2fwebrtc_2eproto__INCLUDED
#define GRPC_proto_2fwebrtc_2fwebrtc_2eproto__INCLUDED

#include "proto/webrtc/webrtc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace webrtc {

class WebrtcService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.webrtc.WebrtcService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status CreateConnection(::grpc::ClientContext* context, const ::carbon::webrtc::CreateConnectionRequest& request, ::carbon::webrtc::CreateConnectionResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::webrtc::CreateConnectionResponse>> AsyncCreateConnection(::grpc::ClientContext* context, const ::carbon::webrtc::CreateConnectionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::webrtc::CreateConnectionResponse>>(AsyncCreateConnectionRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::webrtc::CreateConnectionResponse>> PrepareAsyncCreateConnection(::grpc::ClientContext* context, const ::carbon::webrtc::CreateConnectionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::webrtc::CreateConnectionResponse>>(PrepareAsyncCreateConnectionRaw(context, request, cq));
    }
    virtual ::grpc::Status SetRemoteSdp(::grpc::ClientContext* context, const ::carbon::webrtc::SetRemoteSdpRequest& request, ::carbon::webrtc::SetRemoteSdpResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::webrtc::SetRemoteSdpResponse>> AsyncSetRemoteSdp(::grpc::ClientContext* context, const ::carbon::webrtc::SetRemoteSdpRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::webrtc::SetRemoteSdpResponse>>(AsyncSetRemoteSdpRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::webrtc::SetRemoteSdpResponse>> PrepareAsyncSetRemoteSdp(::grpc::ClientContext* context, const ::carbon::webrtc::SetRemoteSdpRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::webrtc::SetRemoteSdpResponse>>(PrepareAsyncSetRemoteSdpRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void CreateConnection(::grpc::ClientContext* context, const ::carbon::webrtc::CreateConnectionRequest* request, ::carbon::webrtc::CreateConnectionResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void CreateConnection(::grpc::ClientContext* context, const ::carbon::webrtc::CreateConnectionRequest* request, ::carbon::webrtc::CreateConnectionResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetRemoteSdp(::grpc::ClientContext* context, const ::carbon::webrtc::SetRemoteSdpRequest* request, ::carbon::webrtc::SetRemoteSdpResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetRemoteSdp(::grpc::ClientContext* context, const ::carbon::webrtc::SetRemoteSdpRequest* request, ::carbon::webrtc::SetRemoteSdpResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::webrtc::CreateConnectionResponse>* AsyncCreateConnectionRaw(::grpc::ClientContext* context, const ::carbon::webrtc::CreateConnectionRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::webrtc::CreateConnectionResponse>* PrepareAsyncCreateConnectionRaw(::grpc::ClientContext* context, const ::carbon::webrtc::CreateConnectionRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::webrtc::SetRemoteSdpResponse>* AsyncSetRemoteSdpRaw(::grpc::ClientContext* context, const ::carbon::webrtc::SetRemoteSdpRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::webrtc::SetRemoteSdpResponse>* PrepareAsyncSetRemoteSdpRaw(::grpc::ClientContext* context, const ::carbon::webrtc::SetRemoteSdpRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status CreateConnection(::grpc::ClientContext* context, const ::carbon::webrtc::CreateConnectionRequest& request, ::carbon::webrtc::CreateConnectionResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::webrtc::CreateConnectionResponse>> AsyncCreateConnection(::grpc::ClientContext* context, const ::carbon::webrtc::CreateConnectionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::webrtc::CreateConnectionResponse>>(AsyncCreateConnectionRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::webrtc::CreateConnectionResponse>> PrepareAsyncCreateConnection(::grpc::ClientContext* context, const ::carbon::webrtc::CreateConnectionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::webrtc::CreateConnectionResponse>>(PrepareAsyncCreateConnectionRaw(context, request, cq));
    }
    ::grpc::Status SetRemoteSdp(::grpc::ClientContext* context, const ::carbon::webrtc::SetRemoteSdpRequest& request, ::carbon::webrtc::SetRemoteSdpResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::webrtc::SetRemoteSdpResponse>> AsyncSetRemoteSdp(::grpc::ClientContext* context, const ::carbon::webrtc::SetRemoteSdpRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::webrtc::SetRemoteSdpResponse>>(AsyncSetRemoteSdpRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::webrtc::SetRemoteSdpResponse>> PrepareAsyncSetRemoteSdp(::grpc::ClientContext* context, const ::carbon::webrtc::SetRemoteSdpRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::webrtc::SetRemoteSdpResponse>>(PrepareAsyncSetRemoteSdpRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void CreateConnection(::grpc::ClientContext* context, const ::carbon::webrtc::CreateConnectionRequest* request, ::carbon::webrtc::CreateConnectionResponse* response, std::function<void(::grpc::Status)>) override;
      void CreateConnection(::grpc::ClientContext* context, const ::carbon::webrtc::CreateConnectionRequest* request, ::carbon::webrtc::CreateConnectionResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetRemoteSdp(::grpc::ClientContext* context, const ::carbon::webrtc::SetRemoteSdpRequest* request, ::carbon::webrtc::SetRemoteSdpResponse* response, std::function<void(::grpc::Status)>) override;
      void SetRemoteSdp(::grpc::ClientContext* context, const ::carbon::webrtc::SetRemoteSdpRequest* request, ::carbon::webrtc::SetRemoteSdpResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::webrtc::CreateConnectionResponse>* AsyncCreateConnectionRaw(::grpc::ClientContext* context, const ::carbon::webrtc::CreateConnectionRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::webrtc::CreateConnectionResponse>* PrepareAsyncCreateConnectionRaw(::grpc::ClientContext* context, const ::carbon::webrtc::CreateConnectionRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::webrtc::SetRemoteSdpResponse>* AsyncSetRemoteSdpRaw(::grpc::ClientContext* context, const ::carbon::webrtc::SetRemoteSdpRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::webrtc::SetRemoteSdpResponse>* PrepareAsyncSetRemoteSdpRaw(::grpc::ClientContext* context, const ::carbon::webrtc::SetRemoteSdpRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_CreateConnection_;
    const ::grpc::internal::RpcMethod rpcmethod_SetRemoteSdp_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status CreateConnection(::grpc::ServerContext* context, const ::carbon::webrtc::CreateConnectionRequest* request, ::carbon::webrtc::CreateConnectionResponse* response);
    virtual ::grpc::Status SetRemoteSdp(::grpc::ServerContext* context, const ::carbon::webrtc::SetRemoteSdpRequest* request, ::carbon::webrtc::SetRemoteSdpResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_CreateConnection : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_CreateConnection() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_CreateConnection() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateConnection(::grpc::ServerContext* /*context*/, const ::carbon::webrtc::CreateConnectionRequest* /*request*/, ::carbon::webrtc::CreateConnectionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCreateConnection(::grpc::ServerContext* context, ::carbon::webrtc::CreateConnectionRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::webrtc::CreateConnectionResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetRemoteSdp : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetRemoteSdp() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_SetRemoteSdp() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRemoteSdp(::grpc::ServerContext* /*context*/, const ::carbon::webrtc::SetRemoteSdpRequest* /*request*/, ::carbon::webrtc::SetRemoteSdpResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetRemoteSdp(::grpc::ServerContext* context, ::carbon::webrtc::SetRemoteSdpRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::webrtc::SetRemoteSdpResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_CreateConnection<WithAsyncMethod_SetRemoteSdp<Service > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_CreateConnection : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_CreateConnection() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::webrtc::CreateConnectionRequest, ::carbon::webrtc::CreateConnectionResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::webrtc::CreateConnectionRequest* request, ::carbon::webrtc::CreateConnectionResponse* response) { return this->CreateConnection(context, request, response); }));}
    void SetMessageAllocatorFor_CreateConnection(
        ::grpc::MessageAllocator< ::carbon::webrtc::CreateConnectionRequest, ::carbon::webrtc::CreateConnectionResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::webrtc::CreateConnectionRequest, ::carbon::webrtc::CreateConnectionResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_CreateConnection() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateConnection(::grpc::ServerContext* /*context*/, const ::carbon::webrtc::CreateConnectionRequest* /*request*/, ::carbon::webrtc::CreateConnectionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CreateConnection(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::webrtc::CreateConnectionRequest* /*request*/, ::carbon::webrtc::CreateConnectionResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetRemoteSdp : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetRemoteSdp() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::webrtc::SetRemoteSdpRequest, ::carbon::webrtc::SetRemoteSdpResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::webrtc::SetRemoteSdpRequest* request, ::carbon::webrtc::SetRemoteSdpResponse* response) { return this->SetRemoteSdp(context, request, response); }));}
    void SetMessageAllocatorFor_SetRemoteSdp(
        ::grpc::MessageAllocator< ::carbon::webrtc::SetRemoteSdpRequest, ::carbon::webrtc::SetRemoteSdpResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::webrtc::SetRemoteSdpRequest, ::carbon::webrtc::SetRemoteSdpResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetRemoteSdp() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRemoteSdp(::grpc::ServerContext* /*context*/, const ::carbon::webrtc::SetRemoteSdpRequest* /*request*/, ::carbon::webrtc::SetRemoteSdpResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetRemoteSdp(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::webrtc::SetRemoteSdpRequest* /*request*/, ::carbon::webrtc::SetRemoteSdpResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_CreateConnection<WithCallbackMethod_SetRemoteSdp<Service > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_CreateConnection : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_CreateConnection() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_CreateConnection() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateConnection(::grpc::ServerContext* /*context*/, const ::carbon::webrtc::CreateConnectionRequest* /*request*/, ::carbon::webrtc::CreateConnectionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetRemoteSdp : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetRemoteSdp() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_SetRemoteSdp() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRemoteSdp(::grpc::ServerContext* /*context*/, const ::carbon::webrtc::SetRemoteSdpRequest* /*request*/, ::carbon::webrtc::SetRemoteSdpResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_CreateConnection : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_CreateConnection() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_CreateConnection() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateConnection(::grpc::ServerContext* /*context*/, const ::carbon::webrtc::CreateConnectionRequest* /*request*/, ::carbon::webrtc::CreateConnectionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCreateConnection(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetRemoteSdp : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetRemoteSdp() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_SetRemoteSdp() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRemoteSdp(::grpc::ServerContext* /*context*/, const ::carbon::webrtc::SetRemoteSdpRequest* /*request*/, ::carbon::webrtc::SetRemoteSdpResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetRemoteSdp(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_CreateConnection : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_CreateConnection() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->CreateConnection(context, request, response); }));
    }
    ~WithRawCallbackMethod_CreateConnection() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateConnection(::grpc::ServerContext* /*context*/, const ::carbon::webrtc::CreateConnectionRequest* /*request*/, ::carbon::webrtc::CreateConnectionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CreateConnection(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetRemoteSdp : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetRemoteSdp() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetRemoteSdp(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetRemoteSdp() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRemoteSdp(::grpc::ServerContext* /*context*/, const ::carbon::webrtc::SetRemoteSdpRequest* /*request*/, ::carbon::webrtc::SetRemoteSdpResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetRemoteSdp(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_CreateConnection : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_CreateConnection() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::webrtc::CreateConnectionRequest, ::carbon::webrtc::CreateConnectionResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::webrtc::CreateConnectionRequest, ::carbon::webrtc::CreateConnectionResponse>* streamer) {
                       return this->StreamedCreateConnection(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_CreateConnection() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status CreateConnection(::grpc::ServerContext* /*context*/, const ::carbon::webrtc::CreateConnectionRequest* /*request*/, ::carbon::webrtc::CreateConnectionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedCreateConnection(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::webrtc::CreateConnectionRequest,::carbon::webrtc::CreateConnectionResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetRemoteSdp : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetRemoteSdp() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::webrtc::SetRemoteSdpRequest, ::carbon::webrtc::SetRemoteSdpResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::webrtc::SetRemoteSdpRequest, ::carbon::webrtc::SetRemoteSdpResponse>* streamer) {
                       return this->StreamedSetRemoteSdp(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetRemoteSdp() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetRemoteSdp(::grpc::ServerContext* /*context*/, const ::carbon::webrtc::SetRemoteSdpRequest* /*request*/, ::carbon::webrtc::SetRemoteSdpResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetRemoteSdp(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::webrtc::SetRemoteSdpRequest,::carbon::webrtc::SetRemoteSdpResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_CreateConnection<WithStreamedUnaryMethod_SetRemoteSdp<Service > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_CreateConnection<WithStreamedUnaryMethod_SetRemoteSdp<Service > > StreamedService;
};

}  // namespace webrtc
}  // namespace carbon


#endif  // GRPC_proto_2fwebrtc_2fwebrtc_2eproto__INCLUDED

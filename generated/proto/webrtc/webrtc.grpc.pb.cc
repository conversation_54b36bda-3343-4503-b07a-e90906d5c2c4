// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: proto/webrtc/webrtc.proto

#include "proto/webrtc/webrtc.pb.h"
#include "proto/webrtc/webrtc.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace webrtc {

static const char* WebrtcService_method_names[] = {
  "/carbon.webrtc.WebrtcService/CreateConnection",
  "/carbon.webrtc.WebrtcService/SetRemoteSdp",
};

std::unique_ptr< WebrtcService::Stub> WebrtcService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< WebrtcService::Stub> stub(new WebrtcService::Stub(channel, options));
  return stub;
}

WebrtcService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_CreateConnection_(WebrtcService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetRemoteSdp_(WebrtcService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status WebrtcService::Stub::CreateConnection(::grpc::ClientContext* context, const ::carbon::webrtc::CreateConnectionRequest& request, ::carbon::webrtc::CreateConnectionResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::webrtc::CreateConnectionRequest, ::carbon::webrtc::CreateConnectionResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_CreateConnection_, context, request, response);
}

void WebrtcService::Stub::async::CreateConnection(::grpc::ClientContext* context, const ::carbon::webrtc::CreateConnectionRequest* request, ::carbon::webrtc::CreateConnectionResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::webrtc::CreateConnectionRequest, ::carbon::webrtc::CreateConnectionResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CreateConnection_, context, request, response, std::move(f));
}

void WebrtcService::Stub::async::CreateConnection(::grpc::ClientContext* context, const ::carbon::webrtc::CreateConnectionRequest* request, ::carbon::webrtc::CreateConnectionResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CreateConnection_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::webrtc::CreateConnectionResponse>* WebrtcService::Stub::PrepareAsyncCreateConnectionRaw(::grpc::ClientContext* context, const ::carbon::webrtc::CreateConnectionRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::webrtc::CreateConnectionResponse, ::carbon::webrtc::CreateConnectionRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_CreateConnection_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::webrtc::CreateConnectionResponse>* WebrtcService::Stub::AsyncCreateConnectionRaw(::grpc::ClientContext* context, const ::carbon::webrtc::CreateConnectionRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncCreateConnectionRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WebrtcService::Stub::SetRemoteSdp(::grpc::ClientContext* context, const ::carbon::webrtc::SetRemoteSdpRequest& request, ::carbon::webrtc::SetRemoteSdpResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::webrtc::SetRemoteSdpRequest, ::carbon::webrtc::SetRemoteSdpResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetRemoteSdp_, context, request, response);
}

void WebrtcService::Stub::async::SetRemoteSdp(::grpc::ClientContext* context, const ::carbon::webrtc::SetRemoteSdpRequest* request, ::carbon::webrtc::SetRemoteSdpResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::webrtc::SetRemoteSdpRequest, ::carbon::webrtc::SetRemoteSdpResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetRemoteSdp_, context, request, response, std::move(f));
}

void WebrtcService::Stub::async::SetRemoteSdp(::grpc::ClientContext* context, const ::carbon::webrtc::SetRemoteSdpRequest* request, ::carbon::webrtc::SetRemoteSdpResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetRemoteSdp_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::webrtc::SetRemoteSdpResponse>* WebrtcService::Stub::PrepareAsyncSetRemoteSdpRaw(::grpc::ClientContext* context, const ::carbon::webrtc::SetRemoteSdpRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::webrtc::SetRemoteSdpResponse, ::carbon::webrtc::SetRemoteSdpRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetRemoteSdp_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::webrtc::SetRemoteSdpResponse>* WebrtcService::Stub::AsyncSetRemoteSdpRaw(::grpc::ClientContext* context, const ::carbon::webrtc::SetRemoteSdpRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetRemoteSdpRaw(context, request, cq);
  result->StartCall();
  return result;
}

WebrtcService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WebrtcService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WebrtcService::Service, ::carbon::webrtc::CreateConnectionRequest, ::carbon::webrtc::CreateConnectionResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WebrtcService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::webrtc::CreateConnectionRequest* req,
             ::carbon::webrtc::CreateConnectionResponse* resp) {
               return service->CreateConnection(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WebrtcService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WebrtcService::Service, ::carbon::webrtc::SetRemoteSdpRequest, ::carbon::webrtc::SetRemoteSdpResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WebrtcService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::webrtc::SetRemoteSdpRequest* req,
             ::carbon::webrtc::SetRemoteSdpResponse* resp) {
               return service->SetRemoteSdp(ctx, req, resp);
             }, this)));
}

WebrtcService::Service::~Service() {
}

::grpc::Status WebrtcService::Service::CreateConnection(::grpc::ServerContext* context, const ::carbon::webrtc::CreateConnectionRequest* request, ::carbon::webrtc::CreateConnectionResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WebrtcService::Service::SetRemoteSdp(::grpc::ServerContext* context, const ::carbon::webrtc::SetRemoteSdpRequest* request, ::carbon::webrtc::SetRemoteSdpResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace webrtc


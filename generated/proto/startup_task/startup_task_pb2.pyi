"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

TaskStateValue = typing___NewType('TaskStateValue', builtin___int)
type___TaskStateValue = TaskStateValue
TaskState: _TaskState
class _TaskState(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[TaskStateValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    QUEUED = typing___cast(TaskStateValue, 0)
    IN_PROGRESS = typing___cast(TaskStateValue, 1)
    COMPLETE = typing___cast(TaskStateValue, 2)
    ERROR = typing___cast(TaskStateValue, 3)
    UNKNOWN = typing___cast(TaskStateValue, 4)
QUEUED = typing___cast(TaskStateValue, 0)
IN_PROGRESS = typing___cast(TaskStateValue, 1)
COMPLETE = typing___cast(TaskStateValue, 2)
ERROR = typing___cast(TaskStateValue, 3)
UNKNOWN = typing___cast(TaskStateValue, 4)

class CrosshairCalibrationTask(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    row_number: builtin___int = ...
    laser_id: builtin___int = ...

    def __init__(self,
        *,
        row_number : typing___Optional[builtin___int] = None,
        laser_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"laser_id",b"laser_id",u"row_number",b"row_number"]) -> None: ...
type___CrosshairCalibrationTask = CrosshairCalibrationTask

class Task(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...
    label: typing___Text = ...
    description: typing___Text = ...
    state: type___TaskStateValue = ...

    @property
    def crosshair_cal_task(self) -> type___CrosshairCalibrationTask: ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        label : typing___Optional[typing___Text] = None,
        description : typing___Optional[typing___Text] = None,
        state : typing___Optional[type___TaskStateValue] = None,
        crosshair_cal_task : typing___Optional[type___CrosshairCalibrationTask] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"crosshair_cal_task",b"crosshair_cal_task",u"task_details",b"task_details"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crosshair_cal_task",b"crosshair_cal_task",u"description",b"description",u"id",b"id",u"label",b"label",u"state",b"state",u"task_details",b"task_details"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"task_details",b"task_details"]) -> typing_extensions___Literal["crosshair_cal_task"]: ...
type___Task = Task

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/startup_task/startup_task.proto

#include "proto/startup_task/startup_task.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace startup_task {
constexpr CrosshairCalibrationTask::CrosshairCalibrationTask(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : row_number_(0u)
  , laser_id_(0u){}
struct CrosshairCalibrationTaskDefaultTypeInternal {
  constexpr CrosshairCalibrationTaskDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CrosshairCalibrationTaskDefaultTypeInternal() {}
  union {
    CrosshairCalibrationTask _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CrosshairCalibrationTaskDefaultTypeInternal _CrosshairCalibrationTask_default_instance_;
constexpr Task::Task(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , label_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , description_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , state_(0)

  , _oneof_case_{}{}
struct TaskDefaultTypeInternal {
  constexpr TaskDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TaskDefaultTypeInternal() {}
  union {
    Task _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TaskDefaultTypeInternal _Task_default_instance_;
}  // namespace startup_task
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto[2];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto = nullptr;

const uint32_t TableStruct_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::startup_task::CrosshairCalibrationTask, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::startup_task::CrosshairCalibrationTask, row_number_),
  PROTOBUF_FIELD_OFFSET(::carbon::startup_task::CrosshairCalibrationTask, laser_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::startup_task::Task, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::startup_task::Task, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::startup_task::Task, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::startup_task::Task, label_),
  PROTOBUF_FIELD_OFFSET(::carbon::startup_task::Task, description_),
  PROTOBUF_FIELD_OFFSET(::carbon::startup_task::Task, state_),
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::startup_task::Task, task_details_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::startup_task::CrosshairCalibrationTask)},
  { 8, -1, -1, sizeof(::carbon::startup_task::Task)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::startup_task::_CrosshairCalibrationTask_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::startup_task::_Task_default_instance_),
};

const char descriptor_table_protodef_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n%proto/startup_task/startup_task.proto\022"
  "\023carbon.startup_task\"@\n\030CrosshairCalibra"
  "tionTask\022\022\n\nrow_number\030\001 \001(\r\022\020\n\010laser_id"
  "\030\002 \001(\r\"\302\001\n\004Task\022\n\n\002id\030\001 \001(\t\022\r\n\005label\030\002 \001"
  "(\t\022\023\n\013description\030\003 \001(\t\022-\n\005state\030\004 \001(\0162\036"
  ".carbon.startup_task.TaskState\022K\n\022crossh"
  "air_cal_task\030\005 \001(\0132-.carbon.startup_task"
  ".CrosshairCalibrationTaskH\000B\016\n\014task_deta"
  "ils*N\n\tTaskState\022\n\n\006QUEUED\020\000\022\017\n\013IN_PROGR"
  "ESS\020\001\022\014\n\010COMPLETE\020\002\022\t\n\005ERROR\020\003\022\013\n\007UNKNOW"
  "N\020\004B\024Z\022proto/startup_taskb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto = {
  false, false, 433, descriptor_table_protodef_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto, "proto/startup_task/startup_task.proto", 
  &descriptor_table_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto_once, nullptr, 0, 2,
  schemas, file_default_instances, TableStruct_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto::offsets,
  file_level_metadata_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto, file_level_enum_descriptors_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto, file_level_service_descriptors_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto_getter() {
  return &descriptor_table_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto(&descriptor_table_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto);
namespace carbon {
namespace startup_task {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TaskState_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto);
  return file_level_enum_descriptors_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto[0];
}
bool TaskState_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class CrosshairCalibrationTask::_Internal {
 public:
};

CrosshairCalibrationTask::CrosshairCalibrationTask(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.startup_task.CrosshairCalibrationTask)
}
CrosshairCalibrationTask::CrosshairCalibrationTask(const CrosshairCalibrationTask& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&row_number_, &from.row_number_,
    static_cast<size_t>(reinterpret_cast<char*>(&laser_id_) -
    reinterpret_cast<char*>(&row_number_)) + sizeof(laser_id_));
  // @@protoc_insertion_point(copy_constructor:carbon.startup_task.CrosshairCalibrationTask)
}

inline void CrosshairCalibrationTask::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&row_number_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&laser_id_) -
    reinterpret_cast<char*>(&row_number_)) + sizeof(laser_id_));
}

CrosshairCalibrationTask::~CrosshairCalibrationTask() {
  // @@protoc_insertion_point(destructor:carbon.startup_task.CrosshairCalibrationTask)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CrosshairCalibrationTask::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void CrosshairCalibrationTask::ArenaDtor(void* object) {
  CrosshairCalibrationTask* _this = reinterpret_cast< CrosshairCalibrationTask* >(object);
  (void)_this;
}
void CrosshairCalibrationTask::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CrosshairCalibrationTask::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CrosshairCalibrationTask::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.startup_task.CrosshairCalibrationTask)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&row_number_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&laser_id_) -
      reinterpret_cast<char*>(&row_number_)) + sizeof(laser_id_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CrosshairCalibrationTask::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 row_number = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          row_number_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 laser_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          laser_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CrosshairCalibrationTask::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.startup_task.CrosshairCalibrationTask)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 row_number = 1;
  if (this->_internal_row_number() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_row_number(), target);
  }

  // uint32 laser_id = 2;
  if (this->_internal_laser_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_laser_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.startup_task.CrosshairCalibrationTask)
  return target;
}

size_t CrosshairCalibrationTask::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.startup_task.CrosshairCalibrationTask)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 row_number = 1;
  if (this->_internal_row_number() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_row_number());
  }

  // uint32 laser_id = 2;
  if (this->_internal_laser_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_laser_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CrosshairCalibrationTask::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CrosshairCalibrationTask::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CrosshairCalibrationTask::GetClassData() const { return &_class_data_; }

void CrosshairCalibrationTask::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CrosshairCalibrationTask *>(to)->MergeFrom(
      static_cast<const CrosshairCalibrationTask &>(from));
}


void CrosshairCalibrationTask::MergeFrom(const CrosshairCalibrationTask& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.startup_task.CrosshairCalibrationTask)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_row_number() != 0) {
    _internal_set_row_number(from._internal_row_number());
  }
  if (from._internal_laser_id() != 0) {
    _internal_set_laser_id(from._internal_laser_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CrosshairCalibrationTask::CopyFrom(const CrosshairCalibrationTask& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.startup_task.CrosshairCalibrationTask)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CrosshairCalibrationTask::IsInitialized() const {
  return true;
}

void CrosshairCalibrationTask::InternalSwap(CrosshairCalibrationTask* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CrosshairCalibrationTask, laser_id_)
      + sizeof(CrosshairCalibrationTask::laser_id_)
      - PROTOBUF_FIELD_OFFSET(CrosshairCalibrationTask, row_number_)>(
          reinterpret_cast<char*>(&row_number_),
          reinterpret_cast<char*>(&other->row_number_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CrosshairCalibrationTask::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto_getter, &descriptor_table_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto_once,
      file_level_metadata_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto[0]);
}

// ===================================================================

class Task::_Internal {
 public:
  static const ::carbon::startup_task::CrosshairCalibrationTask& crosshair_cal_task(const Task* msg);
};

const ::carbon::startup_task::CrosshairCalibrationTask&
Task::_Internal::crosshair_cal_task(const Task* msg) {
  return *msg->task_details_.crosshair_cal_task_;
}
void Task::set_allocated_crosshair_cal_task(::carbon::startup_task::CrosshairCalibrationTask* crosshair_cal_task) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_task_details();
  if (crosshair_cal_task) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::startup_task::CrosshairCalibrationTask>::GetOwningArena(crosshair_cal_task);
    if (message_arena != submessage_arena) {
      crosshair_cal_task = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, crosshair_cal_task, submessage_arena);
    }
    set_has_crosshair_cal_task();
    task_details_.crosshair_cal_task_ = crosshair_cal_task;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.startup_task.Task.crosshair_cal_task)
}
Task::Task(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.startup_task.Task)
}
Task::Task(const Task& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  label_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    label_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_label().empty()) {
    label_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_label(), 
      GetArenaForAllocation());
  }
  description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_description().empty()) {
    description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_description(), 
      GetArenaForAllocation());
  }
  state_ = from.state_;
  clear_has_task_details();
  switch (from.task_details_case()) {
    case kCrosshairCalTask: {
      _internal_mutable_crosshair_cal_task()->::carbon::startup_task::CrosshairCalibrationTask::MergeFrom(from._internal_crosshair_cal_task());
      break;
    }
    case TASK_DETAILS_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.startup_task.Task)
}

inline void Task::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
label_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  label_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
state_ = 0;
clear_has_task_details();
}

Task::~Task() {
  // @@protoc_insertion_point(destructor:carbon.startup_task.Task)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Task::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  label_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  description_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (has_task_details()) {
    clear_task_details();
  }
}

void Task::ArenaDtor(void* object) {
  Task* _this = reinterpret_cast< Task* >(object);
  (void)_this;
}
void Task::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Task::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Task::clear_task_details() {
// @@protoc_insertion_point(one_of_clear_start:carbon.startup_task.Task)
  switch (task_details_case()) {
    case kCrosshairCalTask: {
      if (GetArenaForAllocation() == nullptr) {
        delete task_details_.crosshair_cal_task_;
      }
      break;
    }
    case TASK_DETAILS_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = TASK_DETAILS_NOT_SET;
}


void Task::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.startup_task.Task)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  label_.ClearToEmpty();
  description_.ClearToEmpty();
  state_ = 0;
  clear_task_details();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Task::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.startup_task.Task.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string label = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_label();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.startup_task.Task.label"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string description = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_description();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.startup_task.Task.description"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.startup_task.TaskState state = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_state(static_cast<::carbon::startup_task::TaskState>(val));
        } else
          goto handle_unusual;
        continue;
      // .carbon.startup_task.CrosshairCalibrationTask crosshair_cal_task = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_crosshair_cal_task(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Task::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.startup_task.Task)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.startup_task.Task.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  // string label = 2;
  if (!this->_internal_label().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_label().data(), static_cast<int>(this->_internal_label().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.startup_task.Task.label");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_label(), target);
  }

  // string description = 3;
  if (!this->_internal_description().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_description().data(), static_cast<int>(this->_internal_description().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.startup_task.Task.description");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_description(), target);
  }

  // .carbon.startup_task.TaskState state = 4;
  if (this->_internal_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      4, this->_internal_state(), target);
  }

  // .carbon.startup_task.CrosshairCalibrationTask crosshair_cal_task = 5;
  if (_internal_has_crosshair_cal_task()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::crosshair_cal_task(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.startup_task.Task)
  return target;
}

size_t Task::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.startup_task.Task)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // string label = 2;
  if (!this->_internal_label().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_label());
  }

  // string description = 3;
  if (!this->_internal_description().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_description());
  }

  // .carbon.startup_task.TaskState state = 4;
  if (this->_internal_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());
  }

  switch (task_details_case()) {
    // .carbon.startup_task.CrosshairCalibrationTask crosshair_cal_task = 5;
    case kCrosshairCalTask: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *task_details_.crosshair_cal_task_);
      break;
    }
    case TASK_DETAILS_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Task::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Task::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Task::GetClassData() const { return &_class_data_; }

void Task::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Task *>(to)->MergeFrom(
      static_cast<const Task &>(from));
}


void Task::MergeFrom(const Task& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.startup_task.Task)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (!from._internal_label().empty()) {
    _internal_set_label(from._internal_label());
  }
  if (!from._internal_description().empty()) {
    _internal_set_description(from._internal_description());
  }
  if (from._internal_state() != 0) {
    _internal_set_state(from._internal_state());
  }
  switch (from.task_details_case()) {
    case kCrosshairCalTask: {
      _internal_mutable_crosshair_cal_task()->::carbon::startup_task::CrosshairCalibrationTask::MergeFrom(from._internal_crosshair_cal_task());
      break;
    }
    case TASK_DETAILS_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Task::CopyFrom(const Task& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.startup_task.Task)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Task::IsInitialized() const {
  return true;
}

void Task::InternalSwap(Task* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &label_, lhs_arena,
      &other->label_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &description_, lhs_arena,
      &other->description_, rhs_arena
  );
  swap(state_, other->state_);
  swap(task_details_, other->task_details_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata Task::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto_getter, &descriptor_table_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto_once,
      file_level_metadata_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto[1]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace startup_task
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::startup_task::CrosshairCalibrationTask* Arena::CreateMaybeMessage< ::carbon::startup_task::CrosshairCalibrationTask >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::startup_task::CrosshairCalibrationTask >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::startup_task::Task* Arena::CreateMaybeMessage< ::carbon::startup_task::Task >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::startup_task::Task >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

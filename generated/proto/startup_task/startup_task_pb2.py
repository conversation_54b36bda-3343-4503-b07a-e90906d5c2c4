# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/startup_task/startup_task.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/startup_task/startup_task.proto',
  package='carbon.startup_task',
  syntax='proto3',
  serialized_options=b'Z\022proto/startup_task',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n%proto/startup_task/startup_task.proto\x12\x13\x63\x61rbon.startup_task\"@\n\x18\x43rosshairCalibrationTask\x12\x12\n\nrow_number\x18\x01 \x01(\r\x12\x10\n\x08laser_id\x18\x02 \x01(\r\"\xc2\x01\n\x04Task\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05label\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12-\n\x05state\x18\x04 \x01(\x0e\x32\x1e.carbon.startup_task.TaskState\x12K\n\x12\x63rosshair_cal_task\x18\x05 \x01(\x0b\x32-.carbon.startup_task.CrosshairCalibrationTaskH\x00\x42\x0e\n\x0ctask_details*N\n\tTaskState\x12\n\n\x06QUEUED\x10\x00\x12\x0f\n\x0bIN_PROGRESS\x10\x01\x12\x0c\n\x08\x43OMPLETE\x10\x02\x12\t\n\x05\x45RROR\x10\x03\x12\x0b\n\x07UNKNOWN\x10\x04\x42\x14Z\x12proto/startup_taskb\x06proto3'
)

_TASKSTATE = _descriptor.EnumDescriptor(
  name='TaskState',
  full_name='carbon.startup_task.TaskState',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='QUEUED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='IN_PROGRESS', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='COMPLETE', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ERROR', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='UNKNOWN', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=325,
  serialized_end=403,
)
_sym_db.RegisterEnumDescriptor(_TASKSTATE)

TaskState = enum_type_wrapper.EnumTypeWrapper(_TASKSTATE)
QUEUED = 0
IN_PROGRESS = 1
COMPLETE = 2
ERROR = 3
UNKNOWN = 4



_CROSSHAIRCALIBRATIONTASK = _descriptor.Descriptor(
  name='CrosshairCalibrationTask',
  full_name='carbon.startup_task.CrosshairCalibrationTask',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_number', full_name='carbon.startup_task.CrosshairCalibrationTask.row_number', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_id', full_name='carbon.startup_task.CrosshairCalibrationTask.laser_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=62,
  serialized_end=126,
)


_TASK = _descriptor.Descriptor(
  name='Task',
  full_name='carbon.startup_task.Task',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.startup_task.Task.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='label', full_name='carbon.startup_task.Task.label', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='description', full_name='carbon.startup_task.Task.description', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='state', full_name='carbon.startup_task.Task.state', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crosshair_cal_task', full_name='carbon.startup_task.Task.crosshair_cal_task', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='task_details', full_name='carbon.startup_task.Task.task_details',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=129,
  serialized_end=323,
)

_TASK.fields_by_name['state'].enum_type = _TASKSTATE
_TASK.fields_by_name['crosshair_cal_task'].message_type = _CROSSHAIRCALIBRATIONTASK
_TASK.oneofs_by_name['task_details'].fields.append(
  _TASK.fields_by_name['crosshair_cal_task'])
_TASK.fields_by_name['crosshair_cal_task'].containing_oneof = _TASK.oneofs_by_name['task_details']
DESCRIPTOR.message_types_by_name['CrosshairCalibrationTask'] = _CROSSHAIRCALIBRATIONTASK
DESCRIPTOR.message_types_by_name['Task'] = _TASK
DESCRIPTOR.enum_types_by_name['TaskState'] = _TASKSTATE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CrosshairCalibrationTask = _reflection.GeneratedProtocolMessageType('CrosshairCalibrationTask', (_message.Message,), {
  'DESCRIPTOR' : _CROSSHAIRCALIBRATIONTASK,
  '__module__' : 'proto.startup_task.startup_task_pb2'
  # @@protoc_insertion_point(class_scope:carbon.startup_task.CrosshairCalibrationTask)
  })
_sym_db.RegisterMessage(CrosshairCalibrationTask)

Task = _reflection.GeneratedProtocolMessageType('Task', (_message.Message,), {
  'DESCRIPTOR' : _TASK,
  '__module__' : 'proto.startup_task.startup_task_pb2'
  # @@protoc_insertion_point(class_scope:carbon.startup_task.Task)
  })
_sym_db.RegisterMessage(Task)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/startup_task/startup_task.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[2]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto;
namespace carbon {
namespace startup_task {
class CrosshairCalibrationTask;
struct CrosshairCalibrationTaskDefaultTypeInternal;
extern CrosshairCalibrationTaskDefaultTypeInternal _CrosshairCalibrationTask_default_instance_;
class Task;
struct TaskDefaultTypeInternal;
extern TaskDefaultTypeInternal _Task_default_instance_;
}  // namespace startup_task
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::startup_task::CrosshairCalibrationTask* Arena::CreateMaybeMessage<::carbon::startup_task::CrosshairCalibrationTask>(Arena*);
template<> ::carbon::startup_task::Task* Arena::CreateMaybeMessage<::carbon::startup_task::Task>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace startup_task {

enum TaskState : int {
  QUEUED = 0,
  IN_PROGRESS = 1,
  COMPLETE = 2,
  ERROR = 3,
  UNKNOWN = 4,
  TaskState_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  TaskState_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool TaskState_IsValid(int value);
constexpr TaskState TaskState_MIN = QUEUED;
constexpr TaskState TaskState_MAX = UNKNOWN;
constexpr int TaskState_ARRAYSIZE = TaskState_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TaskState_descriptor();
template<typename T>
inline const std::string& TaskState_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TaskState>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TaskState_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TaskState_descriptor(), enum_t_value);
}
inline bool TaskState_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, TaskState* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TaskState>(
    TaskState_descriptor(), name, value);
}
// ===================================================================

class CrosshairCalibrationTask final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.startup_task.CrosshairCalibrationTask) */ {
 public:
  inline CrosshairCalibrationTask() : CrosshairCalibrationTask(nullptr) {}
  ~CrosshairCalibrationTask() override;
  explicit constexpr CrosshairCalibrationTask(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CrosshairCalibrationTask(const CrosshairCalibrationTask& from);
  CrosshairCalibrationTask(CrosshairCalibrationTask&& from) noexcept
    : CrosshairCalibrationTask() {
    *this = ::std::move(from);
  }

  inline CrosshairCalibrationTask& operator=(const CrosshairCalibrationTask& from) {
    CopyFrom(from);
    return *this;
  }
  inline CrosshairCalibrationTask& operator=(CrosshairCalibrationTask&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CrosshairCalibrationTask& default_instance() {
    return *internal_default_instance();
  }
  static inline const CrosshairCalibrationTask* internal_default_instance() {
    return reinterpret_cast<const CrosshairCalibrationTask*>(
               &_CrosshairCalibrationTask_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CrosshairCalibrationTask& a, CrosshairCalibrationTask& b) {
    a.Swap(&b);
  }
  inline void Swap(CrosshairCalibrationTask* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CrosshairCalibrationTask* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CrosshairCalibrationTask* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CrosshairCalibrationTask>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CrosshairCalibrationTask& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CrosshairCalibrationTask& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CrosshairCalibrationTask* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.startup_task.CrosshairCalibrationTask";
  }
  protected:
  explicit CrosshairCalibrationTask(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRowNumberFieldNumber = 1,
    kLaserIdFieldNumber = 2,
  };
  // uint32 row_number = 1;
  void clear_row_number();
  uint32_t row_number() const;
  void set_row_number(uint32_t value);
  private:
  uint32_t _internal_row_number() const;
  void _internal_set_row_number(uint32_t value);
  public:

  // uint32 laser_id = 2;
  void clear_laser_id();
  uint32_t laser_id() const;
  void set_laser_id(uint32_t value);
  private:
  uint32_t _internal_laser_id() const;
  void _internal_set_laser_id(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.startup_task.CrosshairCalibrationTask)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t row_number_;
  uint32_t laser_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto;
};
// -------------------------------------------------------------------

class Task final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.startup_task.Task) */ {
 public:
  inline Task() : Task(nullptr) {}
  ~Task() override;
  explicit constexpr Task(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Task(const Task& from);
  Task(Task&& from) noexcept
    : Task() {
    *this = ::std::move(from);
  }

  inline Task& operator=(const Task& from) {
    CopyFrom(from);
    return *this;
  }
  inline Task& operator=(Task&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Task& default_instance() {
    return *internal_default_instance();
  }
  enum TaskDetailsCase {
    kCrosshairCalTask = 5,
    TASK_DETAILS_NOT_SET = 0,
  };

  static inline const Task* internal_default_instance() {
    return reinterpret_cast<const Task*>(
               &_Task_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Task& a, Task& b) {
    a.Swap(&b);
  }
  inline void Swap(Task* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Task* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Task* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Task>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Task& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Task& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Task* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.startup_task.Task";
  }
  protected:
  explicit Task(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kLabelFieldNumber = 2,
    kDescriptionFieldNumber = 3,
    kStateFieldNumber = 4,
    kCrosshairCalTaskFieldNumber = 5,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // string label = 2;
  void clear_label();
  const std::string& label() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_label(ArgT0&& arg0, ArgT... args);
  std::string* mutable_label();
  PROTOBUF_NODISCARD std::string* release_label();
  void set_allocated_label(std::string* label);
  private:
  const std::string& _internal_label() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_label(const std::string& value);
  std::string* _internal_mutable_label();
  public:

  // string description = 3;
  void clear_description();
  const std::string& description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description();
  PROTOBUF_NODISCARD std::string* release_description();
  void set_allocated_description(std::string* description);
  private:
  const std::string& _internal_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description(const std::string& value);
  std::string* _internal_mutable_description();
  public:

  // .carbon.startup_task.TaskState state = 4;
  void clear_state();
  ::carbon::startup_task::TaskState state() const;
  void set_state(::carbon::startup_task::TaskState value);
  private:
  ::carbon::startup_task::TaskState _internal_state() const;
  void _internal_set_state(::carbon::startup_task::TaskState value);
  public:

  // .carbon.startup_task.CrosshairCalibrationTask crosshair_cal_task = 5;
  bool has_crosshair_cal_task() const;
  private:
  bool _internal_has_crosshair_cal_task() const;
  public:
  void clear_crosshair_cal_task();
  const ::carbon::startup_task::CrosshairCalibrationTask& crosshair_cal_task() const;
  PROTOBUF_NODISCARD ::carbon::startup_task::CrosshairCalibrationTask* release_crosshair_cal_task();
  ::carbon::startup_task::CrosshairCalibrationTask* mutable_crosshair_cal_task();
  void set_allocated_crosshair_cal_task(::carbon::startup_task::CrosshairCalibrationTask* crosshair_cal_task);
  private:
  const ::carbon::startup_task::CrosshairCalibrationTask& _internal_crosshair_cal_task() const;
  ::carbon::startup_task::CrosshairCalibrationTask* _internal_mutable_crosshair_cal_task();
  public:
  void unsafe_arena_set_allocated_crosshair_cal_task(
      ::carbon::startup_task::CrosshairCalibrationTask* crosshair_cal_task);
  ::carbon::startup_task::CrosshairCalibrationTask* unsafe_arena_release_crosshair_cal_task();

  void clear_task_details();
  TaskDetailsCase task_details_case() const;
  // @@protoc_insertion_point(class_scope:carbon.startup_task.Task)
 private:
  class _Internal;
  void set_has_crosshair_cal_task();

  inline bool has_task_details() const;
  inline void clear_has_task_details();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr label_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
  int state_;
  union TaskDetailsUnion {
    constexpr TaskDetailsUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    ::carbon::startup_task::CrosshairCalibrationTask* crosshair_cal_task_;
  } task_details_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CrosshairCalibrationTask

// uint32 row_number = 1;
inline void CrosshairCalibrationTask::clear_row_number() {
  row_number_ = 0u;
}
inline uint32_t CrosshairCalibrationTask::_internal_row_number() const {
  return row_number_;
}
inline uint32_t CrosshairCalibrationTask::row_number() const {
  // @@protoc_insertion_point(field_get:carbon.startup_task.CrosshairCalibrationTask.row_number)
  return _internal_row_number();
}
inline void CrosshairCalibrationTask::_internal_set_row_number(uint32_t value) {
  
  row_number_ = value;
}
inline void CrosshairCalibrationTask::set_row_number(uint32_t value) {
  _internal_set_row_number(value);
  // @@protoc_insertion_point(field_set:carbon.startup_task.CrosshairCalibrationTask.row_number)
}

// uint32 laser_id = 2;
inline void CrosshairCalibrationTask::clear_laser_id() {
  laser_id_ = 0u;
}
inline uint32_t CrosshairCalibrationTask::_internal_laser_id() const {
  return laser_id_;
}
inline uint32_t CrosshairCalibrationTask::laser_id() const {
  // @@protoc_insertion_point(field_get:carbon.startup_task.CrosshairCalibrationTask.laser_id)
  return _internal_laser_id();
}
inline void CrosshairCalibrationTask::_internal_set_laser_id(uint32_t value) {
  
  laser_id_ = value;
}
inline void CrosshairCalibrationTask::set_laser_id(uint32_t value) {
  _internal_set_laser_id(value);
  // @@protoc_insertion_point(field_set:carbon.startup_task.CrosshairCalibrationTask.laser_id)
}

// -------------------------------------------------------------------

// Task

// string id = 1;
inline void Task::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& Task::id() const {
  // @@protoc_insertion_point(field_get:carbon.startup_task.Task.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Task::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.startup_task.Task.id)
}
inline std::string* Task::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.startup_task.Task.id)
  return _s;
}
inline const std::string& Task::_internal_id() const {
  return id_.Get();
}
inline void Task::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Task::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Task::release_id() {
  // @@protoc_insertion_point(field_release:carbon.startup_task.Task.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Task::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.startup_task.Task.id)
}

// string label = 2;
inline void Task::clear_label() {
  label_.ClearToEmpty();
}
inline const std::string& Task::label() const {
  // @@protoc_insertion_point(field_get:carbon.startup_task.Task.label)
  return _internal_label();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Task::set_label(ArgT0&& arg0, ArgT... args) {
 
 label_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.startup_task.Task.label)
}
inline std::string* Task::mutable_label() {
  std::string* _s = _internal_mutable_label();
  // @@protoc_insertion_point(field_mutable:carbon.startup_task.Task.label)
  return _s;
}
inline const std::string& Task::_internal_label() const {
  return label_.Get();
}
inline void Task::_internal_set_label(const std::string& value) {
  
  label_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Task::_internal_mutable_label() {
  
  return label_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Task::release_label() {
  // @@protoc_insertion_point(field_release:carbon.startup_task.Task.label)
  return label_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Task::set_allocated_label(std::string* label) {
  if (label != nullptr) {
    
  } else {
    
  }
  label_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), label,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (label_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    label_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.startup_task.Task.label)
}

// string description = 3;
inline void Task::clear_description() {
  description_.ClearToEmpty();
}
inline const std::string& Task::description() const {
  // @@protoc_insertion_point(field_get:carbon.startup_task.Task.description)
  return _internal_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Task::set_description(ArgT0&& arg0, ArgT... args) {
 
 description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.startup_task.Task.description)
}
inline std::string* Task::mutable_description() {
  std::string* _s = _internal_mutable_description();
  // @@protoc_insertion_point(field_mutable:carbon.startup_task.Task.description)
  return _s;
}
inline const std::string& Task::_internal_description() const {
  return description_.Get();
}
inline void Task::_internal_set_description(const std::string& value) {
  
  description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Task::_internal_mutable_description() {
  
  return description_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Task::release_description() {
  // @@protoc_insertion_point(field_release:carbon.startup_task.Task.description)
  return description_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Task::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  description_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), description,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (description_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.startup_task.Task.description)
}

// .carbon.startup_task.TaskState state = 4;
inline void Task::clear_state() {
  state_ = 0;
}
inline ::carbon::startup_task::TaskState Task::_internal_state() const {
  return static_cast< ::carbon::startup_task::TaskState >(state_);
}
inline ::carbon::startup_task::TaskState Task::state() const {
  // @@protoc_insertion_point(field_get:carbon.startup_task.Task.state)
  return _internal_state();
}
inline void Task::_internal_set_state(::carbon::startup_task::TaskState value) {
  
  state_ = value;
}
inline void Task::set_state(::carbon::startup_task::TaskState value) {
  _internal_set_state(value);
  // @@protoc_insertion_point(field_set:carbon.startup_task.Task.state)
}

// .carbon.startup_task.CrosshairCalibrationTask crosshair_cal_task = 5;
inline bool Task::_internal_has_crosshair_cal_task() const {
  return task_details_case() == kCrosshairCalTask;
}
inline bool Task::has_crosshair_cal_task() const {
  return _internal_has_crosshair_cal_task();
}
inline void Task::set_has_crosshair_cal_task() {
  _oneof_case_[0] = kCrosshairCalTask;
}
inline void Task::clear_crosshair_cal_task() {
  if (_internal_has_crosshair_cal_task()) {
    if (GetArenaForAllocation() == nullptr) {
      delete task_details_.crosshair_cal_task_;
    }
    clear_has_task_details();
  }
}
inline ::carbon::startup_task::CrosshairCalibrationTask* Task::release_crosshair_cal_task() {
  // @@protoc_insertion_point(field_release:carbon.startup_task.Task.crosshair_cal_task)
  if (_internal_has_crosshair_cal_task()) {
    clear_has_task_details();
      ::carbon::startup_task::CrosshairCalibrationTask* temp = task_details_.crosshair_cal_task_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    task_details_.crosshair_cal_task_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::startup_task::CrosshairCalibrationTask& Task::_internal_crosshair_cal_task() const {
  return _internal_has_crosshair_cal_task()
      ? *task_details_.crosshair_cal_task_
      : reinterpret_cast< ::carbon::startup_task::CrosshairCalibrationTask&>(::carbon::startup_task::_CrosshairCalibrationTask_default_instance_);
}
inline const ::carbon::startup_task::CrosshairCalibrationTask& Task::crosshair_cal_task() const {
  // @@protoc_insertion_point(field_get:carbon.startup_task.Task.crosshair_cal_task)
  return _internal_crosshair_cal_task();
}
inline ::carbon::startup_task::CrosshairCalibrationTask* Task::unsafe_arena_release_crosshair_cal_task() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.startup_task.Task.crosshair_cal_task)
  if (_internal_has_crosshair_cal_task()) {
    clear_has_task_details();
    ::carbon::startup_task::CrosshairCalibrationTask* temp = task_details_.crosshair_cal_task_;
    task_details_.crosshair_cal_task_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Task::unsafe_arena_set_allocated_crosshair_cal_task(::carbon::startup_task::CrosshairCalibrationTask* crosshair_cal_task) {
  clear_task_details();
  if (crosshair_cal_task) {
    set_has_crosshair_cal_task();
    task_details_.crosshair_cal_task_ = crosshair_cal_task;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.startup_task.Task.crosshair_cal_task)
}
inline ::carbon::startup_task::CrosshairCalibrationTask* Task::_internal_mutable_crosshair_cal_task() {
  if (!_internal_has_crosshair_cal_task()) {
    clear_task_details();
    set_has_crosshair_cal_task();
    task_details_.crosshair_cal_task_ = CreateMaybeMessage< ::carbon::startup_task::CrosshairCalibrationTask >(GetArenaForAllocation());
  }
  return task_details_.crosshair_cal_task_;
}
inline ::carbon::startup_task::CrosshairCalibrationTask* Task::mutable_crosshair_cal_task() {
  ::carbon::startup_task::CrosshairCalibrationTask* _msg = _internal_mutable_crosshair_cal_task();
  // @@protoc_insertion_point(field_mutable:carbon.startup_task.Task.crosshair_cal_task)
  return _msg;
}

inline bool Task::has_task_details() const {
  return task_details_case() != TASK_DETAILS_NOT_SET;
}
inline void Task::clear_has_task_details() {
  _oneof_case_[0] = TASK_DETAILS_NOT_SET;
}
inline Task::TaskDetailsCase Task::task_details_case() const {
  return Task::TaskDetailsCase(_oneof_case_[0]);
}
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace startup_task
}  // namespace carbon

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::carbon::startup_task::TaskState> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::startup_task::TaskState>() {
  return ::carbon::startup_task::TaskState_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto

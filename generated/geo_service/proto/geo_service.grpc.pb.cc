// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: geo_service/proto/geo_service.proto

#include "geo_service/proto/geo_service.pb.h"
#include "geo_service/proto/geo_service.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace geo_service {

static const char* GeoService_method_names[] = {
  "/geo_service.GeoService/GeodFwd",
};

std::unique_ptr< GeoService::Stub> GeoService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< GeoService::Stub> stub(new GeoService::Stub(channel, options));
  return stub;
}

GeoService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GeodFwd_(GeoService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status GeoService::Stub::GeodFwd(::grpc::ClientContext* context, const ::geo_service::GeodFwdRequest& request, ::geo_service::GeodFwdResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::geo_service::GeodFwdRequest, ::geo_service::GeodFwdResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GeodFwd_, context, request, response);
}

void GeoService::Stub::async::GeodFwd(::grpc::ClientContext* context, const ::geo_service::GeodFwdRequest* request, ::geo_service::GeodFwdResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::geo_service::GeodFwdRequest, ::geo_service::GeodFwdResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GeodFwd_, context, request, response, std::move(f));
}

void GeoService::Stub::async::GeodFwd(::grpc::ClientContext* context, const ::geo_service::GeodFwdRequest* request, ::geo_service::GeodFwdResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GeodFwd_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::geo_service::GeodFwdResponse>* GeoService::Stub::PrepareAsyncGeodFwdRaw(::grpc::ClientContext* context, const ::geo_service::GeodFwdRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::geo_service::GeodFwdResponse, ::geo_service::GeodFwdRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GeodFwd_, context, request);
}

::grpc::ClientAsyncResponseReader< ::geo_service::GeodFwdResponse>* GeoService::Stub::AsyncGeodFwdRaw(::grpc::ClientContext* context, const ::geo_service::GeodFwdRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGeodFwdRaw(context, request, cq);
  result->StartCall();
  return result;
}

GeoService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      GeoService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< GeoService::Service, ::geo_service::GeodFwdRequest, ::geo_service::GeodFwdResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](GeoService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::geo_service::GeodFwdRequest* req,
             ::geo_service::GeodFwdResponse* resp) {
               return service->GeodFwd(ctx, req, resp);
             }, this)));
}

GeoService::Service::~Service() {
}

::grpc::Status GeoService::Service::GeodFwd(::grpc::ServerContext* context, const ::geo_service::GeodFwdRequest* request, ::geo_service::GeodFwdResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace geo_service


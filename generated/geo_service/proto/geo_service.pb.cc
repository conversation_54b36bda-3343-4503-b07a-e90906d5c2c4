// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: geo_service/proto/geo_service.proto

#include "geo_service/proto/geo_service.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace geo_service {
constexpr GeoLL::GeoLL(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : lat_(0)
  , lng_(0){}
struct GeoLLDefaultTypeInternal {
  constexpr GeoLLDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GeoLLDefaultTypeInternal() {}
  union {
    GeoLL _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GeoLLDefaultTypeInternal _GeoLL_default_instance_;
constexpr GeodFwdRequest::GeodFwdRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : start_(nullptr)
  , dist_meters_(0)
  , azimuth_deg_(0){}
struct GeodFwdRequestDefaultTypeInternal {
  constexpr GeodFwdRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GeodFwdRequestDefaultTypeInternal() {}
  union {
    GeodFwdRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GeodFwdRequestDefaultTypeInternal _GeodFwdRequest_default_instance_;
constexpr GeodFwdResponse::GeodFwdResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : pos_(nullptr){}
struct GeodFwdResponseDefaultTypeInternal {
  constexpr GeodFwdResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GeodFwdResponseDefaultTypeInternal() {}
  union {
    GeodFwdResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GeodFwdResponseDefaultTypeInternal _GeodFwdResponse_default_instance_;
}  // namespace geo_service
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_geo_5fservice_2fproto_2fgeo_5fservice_2eproto[3];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_geo_5fservice_2fproto_2fgeo_5fservice_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_geo_5fservice_2fproto_2fgeo_5fservice_2eproto = nullptr;

const uint32_t TableStruct_geo_5fservice_2fproto_2fgeo_5fservice_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::geo_service::GeoLL, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::geo_service::GeoLL, lat_),
  PROTOBUF_FIELD_OFFSET(::geo_service::GeoLL, lng_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::geo_service::GeodFwdRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::geo_service::GeodFwdRequest, start_),
  PROTOBUF_FIELD_OFFSET(::geo_service::GeodFwdRequest, dist_meters_),
  PROTOBUF_FIELD_OFFSET(::geo_service::GeodFwdRequest, azimuth_deg_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::geo_service::GeodFwdResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::geo_service::GeodFwdResponse, pos_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::geo_service::GeoLL)},
  { 8, -1, -1, sizeof(::geo_service::GeodFwdRequest)},
  { 17, -1, -1, sizeof(::geo_service::GeodFwdResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::geo_service::_GeoLL_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::geo_service::_GeodFwdRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::geo_service::_GeodFwdResponse_default_instance_),
};

const char descriptor_table_protodef_geo_5fservice_2fproto_2fgeo_5fservice_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n#geo_service/proto/geo_service.proto\022\013g"
  "eo_service\"!\n\005GeoLL\022\013\n\003lat\030\001 \001(\001\022\013\n\003lng\030"
  "\002 \001(\001\"]\n\016GeodFwdRequest\022!\n\005start\030\001 \001(\0132\022"
  ".geo_service.GeoLL\022\023\n\013dist_meters\030\002 \001(\001\022"
  "\023\n\013azimuth_deg\030\003 \001(\001\"2\n\017GeodFwdResponse\022"
  "\037\n\003pos\030\001 \001(\0132\022.geo_service.GeoLL2T\n\nGeoS"
  "ervice\022F\n\007GeodFwd\022\033.geo_service.GeodFwdR"
  "equest\032\034.geo_service.GeodFwdResponse\"\000B\023"
  "Z\021proto/geo_serviceb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_geo_5fservice_2fproto_2fgeo_5fservice_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_geo_5fservice_2fproto_2fgeo_5fservice_2eproto = {
  false, false, 347, descriptor_table_protodef_geo_5fservice_2fproto_2fgeo_5fservice_2eproto, "geo_service/proto/geo_service.proto", 
  &descriptor_table_geo_5fservice_2fproto_2fgeo_5fservice_2eproto_once, nullptr, 0, 3,
  schemas, file_default_instances, TableStruct_geo_5fservice_2fproto_2fgeo_5fservice_2eproto::offsets,
  file_level_metadata_geo_5fservice_2fproto_2fgeo_5fservice_2eproto, file_level_enum_descriptors_geo_5fservice_2fproto_2fgeo_5fservice_2eproto, file_level_service_descriptors_geo_5fservice_2fproto_2fgeo_5fservice_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_geo_5fservice_2fproto_2fgeo_5fservice_2eproto_getter() {
  return &descriptor_table_geo_5fservice_2fproto_2fgeo_5fservice_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_geo_5fservice_2fproto_2fgeo_5fservice_2eproto(&descriptor_table_geo_5fservice_2fproto_2fgeo_5fservice_2eproto);
namespace geo_service {

// ===================================================================

class GeoLL::_Internal {
 public:
};

GeoLL::GeoLL(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:geo_service.GeoLL)
}
GeoLL::GeoLL(const GeoLL& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&lat_, &from.lat_,
    static_cast<size_t>(reinterpret_cast<char*>(&lng_) -
    reinterpret_cast<char*>(&lat_)) + sizeof(lng_));
  // @@protoc_insertion_point(copy_constructor:geo_service.GeoLL)
}

inline void GeoLL::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&lat_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&lng_) -
    reinterpret_cast<char*>(&lat_)) + sizeof(lng_));
}

GeoLL::~GeoLL() {
  // @@protoc_insertion_point(destructor:geo_service.GeoLL)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GeoLL::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GeoLL::ArenaDtor(void* object) {
  GeoLL* _this = reinterpret_cast< GeoLL* >(object);
  (void)_this;
}
void GeoLL::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GeoLL::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GeoLL::Clear() {
// @@protoc_insertion_point(message_clear_start:geo_service.GeoLL)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&lat_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&lng_) -
      reinterpret_cast<char*>(&lat_)) + sizeof(lng_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GeoLL::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double lat = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          lat_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double lng = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          lng_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GeoLL::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:geo_service.GeoLL)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double lat = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_lat = this->_internal_lat();
  uint64_t raw_lat;
  memcpy(&raw_lat, &tmp_lat, sizeof(tmp_lat));
  if (raw_lat != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_lat(), target);
  }

  // double lng = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_lng = this->_internal_lng();
  uint64_t raw_lng;
  memcpy(&raw_lng, &tmp_lng, sizeof(tmp_lng));
  if (raw_lng != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_lng(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:geo_service.GeoLL)
  return target;
}

size_t GeoLL::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:geo_service.GeoLL)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double lat = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_lat = this->_internal_lat();
  uint64_t raw_lat;
  memcpy(&raw_lat, &tmp_lat, sizeof(tmp_lat));
  if (raw_lat != 0) {
    total_size += 1 + 8;
  }

  // double lng = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_lng = this->_internal_lng();
  uint64_t raw_lng;
  memcpy(&raw_lng, &tmp_lng, sizeof(tmp_lng));
  if (raw_lng != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GeoLL::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GeoLL::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GeoLL::GetClassData() const { return &_class_data_; }

void GeoLL::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GeoLL *>(to)->MergeFrom(
      static_cast<const GeoLL &>(from));
}


void GeoLL::MergeFrom(const GeoLL& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:geo_service.GeoLL)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_lat = from._internal_lat();
  uint64_t raw_lat;
  memcpy(&raw_lat, &tmp_lat, sizeof(tmp_lat));
  if (raw_lat != 0) {
    _internal_set_lat(from._internal_lat());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_lng = from._internal_lng();
  uint64_t raw_lng;
  memcpy(&raw_lng, &tmp_lng, sizeof(tmp_lng));
  if (raw_lng != 0) {
    _internal_set_lng(from._internal_lng());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GeoLL::CopyFrom(const GeoLL& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:geo_service.GeoLL)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GeoLL::IsInitialized() const {
  return true;
}

void GeoLL::InternalSwap(GeoLL* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GeoLL, lng_)
      + sizeof(GeoLL::lng_)
      - PROTOBUF_FIELD_OFFSET(GeoLL, lat_)>(
          reinterpret_cast<char*>(&lat_),
          reinterpret_cast<char*>(&other->lat_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GeoLL::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_geo_5fservice_2fproto_2fgeo_5fservice_2eproto_getter, &descriptor_table_geo_5fservice_2fproto_2fgeo_5fservice_2eproto_once,
      file_level_metadata_geo_5fservice_2fproto_2fgeo_5fservice_2eproto[0]);
}

// ===================================================================

class GeodFwdRequest::_Internal {
 public:
  static const ::geo_service::GeoLL& start(const GeodFwdRequest* msg);
};

const ::geo_service::GeoLL&
GeodFwdRequest::_Internal::start(const GeodFwdRequest* msg) {
  return *msg->start_;
}
GeodFwdRequest::GeodFwdRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:geo_service.GeodFwdRequest)
}
GeodFwdRequest::GeodFwdRequest(const GeodFwdRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_start()) {
    start_ = new ::geo_service::GeoLL(*from.start_);
  } else {
    start_ = nullptr;
  }
  ::memcpy(&dist_meters_, &from.dist_meters_,
    static_cast<size_t>(reinterpret_cast<char*>(&azimuth_deg_) -
    reinterpret_cast<char*>(&dist_meters_)) + sizeof(azimuth_deg_));
  // @@protoc_insertion_point(copy_constructor:geo_service.GeodFwdRequest)
}

inline void GeodFwdRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&start_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&azimuth_deg_) -
    reinterpret_cast<char*>(&start_)) + sizeof(azimuth_deg_));
}

GeodFwdRequest::~GeodFwdRequest() {
  // @@protoc_insertion_point(destructor:geo_service.GeodFwdRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GeodFwdRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete start_;
}

void GeodFwdRequest::ArenaDtor(void* object) {
  GeodFwdRequest* _this = reinterpret_cast< GeodFwdRequest* >(object);
  (void)_this;
}
void GeodFwdRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GeodFwdRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GeodFwdRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:geo_service.GeodFwdRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && start_ != nullptr) {
    delete start_;
  }
  start_ = nullptr;
  ::memset(&dist_meters_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&azimuth_deg_) -
      reinterpret_cast<char*>(&dist_meters_)) + sizeof(azimuth_deg_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GeodFwdRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .geo_service.GeoLL start = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_start(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double dist_meters = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          dist_meters_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double azimuth_deg = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 25)) {
          azimuth_deg_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GeodFwdRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:geo_service.GeodFwdRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .geo_service.GeoLL start = 1;
  if (this->_internal_has_start()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::start(this), target, stream);
  }

  // double dist_meters = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_dist_meters = this->_internal_dist_meters();
  uint64_t raw_dist_meters;
  memcpy(&raw_dist_meters, &tmp_dist_meters, sizeof(tmp_dist_meters));
  if (raw_dist_meters != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_dist_meters(), target);
  }

  // double azimuth_deg = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_azimuth_deg = this->_internal_azimuth_deg();
  uint64_t raw_azimuth_deg;
  memcpy(&raw_azimuth_deg, &tmp_azimuth_deg, sizeof(tmp_azimuth_deg));
  if (raw_azimuth_deg != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_azimuth_deg(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:geo_service.GeodFwdRequest)
  return target;
}

size_t GeodFwdRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:geo_service.GeodFwdRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .geo_service.GeoLL start = 1;
  if (this->_internal_has_start()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *start_);
  }

  // double dist_meters = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_dist_meters = this->_internal_dist_meters();
  uint64_t raw_dist_meters;
  memcpy(&raw_dist_meters, &tmp_dist_meters, sizeof(tmp_dist_meters));
  if (raw_dist_meters != 0) {
    total_size += 1 + 8;
  }

  // double azimuth_deg = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_azimuth_deg = this->_internal_azimuth_deg();
  uint64_t raw_azimuth_deg;
  memcpy(&raw_azimuth_deg, &tmp_azimuth_deg, sizeof(tmp_azimuth_deg));
  if (raw_azimuth_deg != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GeodFwdRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GeodFwdRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GeodFwdRequest::GetClassData() const { return &_class_data_; }

void GeodFwdRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GeodFwdRequest *>(to)->MergeFrom(
      static_cast<const GeodFwdRequest &>(from));
}


void GeodFwdRequest::MergeFrom(const GeodFwdRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:geo_service.GeodFwdRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_start()) {
    _internal_mutable_start()->::geo_service::GeoLL::MergeFrom(from._internal_start());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_dist_meters = from._internal_dist_meters();
  uint64_t raw_dist_meters;
  memcpy(&raw_dist_meters, &tmp_dist_meters, sizeof(tmp_dist_meters));
  if (raw_dist_meters != 0) {
    _internal_set_dist_meters(from._internal_dist_meters());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_azimuth_deg = from._internal_azimuth_deg();
  uint64_t raw_azimuth_deg;
  memcpy(&raw_azimuth_deg, &tmp_azimuth_deg, sizeof(tmp_azimuth_deg));
  if (raw_azimuth_deg != 0) {
    _internal_set_azimuth_deg(from._internal_azimuth_deg());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GeodFwdRequest::CopyFrom(const GeodFwdRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:geo_service.GeodFwdRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GeodFwdRequest::IsInitialized() const {
  return true;
}

void GeodFwdRequest::InternalSwap(GeodFwdRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GeodFwdRequest, azimuth_deg_)
      + sizeof(GeodFwdRequest::azimuth_deg_)
      - PROTOBUF_FIELD_OFFSET(GeodFwdRequest, start_)>(
          reinterpret_cast<char*>(&start_),
          reinterpret_cast<char*>(&other->start_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GeodFwdRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_geo_5fservice_2fproto_2fgeo_5fservice_2eproto_getter, &descriptor_table_geo_5fservice_2fproto_2fgeo_5fservice_2eproto_once,
      file_level_metadata_geo_5fservice_2fproto_2fgeo_5fservice_2eproto[1]);
}

// ===================================================================

class GeodFwdResponse::_Internal {
 public:
  static const ::geo_service::GeoLL& pos(const GeodFwdResponse* msg);
};

const ::geo_service::GeoLL&
GeodFwdResponse::_Internal::pos(const GeodFwdResponse* msg) {
  return *msg->pos_;
}
GeodFwdResponse::GeodFwdResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:geo_service.GeodFwdResponse)
}
GeodFwdResponse::GeodFwdResponse(const GeodFwdResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_pos()) {
    pos_ = new ::geo_service::GeoLL(*from.pos_);
  } else {
    pos_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:geo_service.GeodFwdResponse)
}

inline void GeodFwdResponse::SharedCtor() {
pos_ = nullptr;
}

GeodFwdResponse::~GeodFwdResponse() {
  // @@protoc_insertion_point(destructor:geo_service.GeodFwdResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GeodFwdResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete pos_;
}

void GeodFwdResponse::ArenaDtor(void* object) {
  GeodFwdResponse* _this = reinterpret_cast< GeodFwdResponse* >(object);
  (void)_this;
}
void GeodFwdResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GeodFwdResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GeodFwdResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:geo_service.GeodFwdResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && pos_ != nullptr) {
    delete pos_;
  }
  pos_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GeodFwdResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .geo_service.GeoLL pos = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_pos(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GeodFwdResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:geo_service.GeodFwdResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .geo_service.GeoLL pos = 1;
  if (this->_internal_has_pos()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::pos(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:geo_service.GeodFwdResponse)
  return target;
}

size_t GeodFwdResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:geo_service.GeodFwdResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .geo_service.GeoLL pos = 1;
  if (this->_internal_has_pos()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *pos_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GeodFwdResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GeodFwdResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GeodFwdResponse::GetClassData() const { return &_class_data_; }

void GeodFwdResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GeodFwdResponse *>(to)->MergeFrom(
      static_cast<const GeodFwdResponse &>(from));
}


void GeodFwdResponse::MergeFrom(const GeodFwdResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:geo_service.GeodFwdResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_pos()) {
    _internal_mutable_pos()->::geo_service::GeoLL::MergeFrom(from._internal_pos());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GeodFwdResponse::CopyFrom(const GeodFwdResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:geo_service.GeodFwdResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GeodFwdResponse::IsInitialized() const {
  return true;
}

void GeodFwdResponse::InternalSwap(GeodFwdResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(pos_, other->pos_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GeodFwdResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_geo_5fservice_2fproto_2fgeo_5fservice_2eproto_getter, &descriptor_table_geo_5fservice_2fproto_2fgeo_5fservice_2eproto_once,
      file_level_metadata_geo_5fservice_2fproto_2fgeo_5fservice_2eproto[2]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace geo_service
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::geo_service::GeoLL* Arena::CreateMaybeMessage< ::geo_service::GeoLL >(Arena* arena) {
  return Arena::CreateMessageInternal< ::geo_service::GeoLL >(arena);
}
template<> PROTOBUF_NOINLINE ::geo_service::GeodFwdRequest* Arena::CreateMaybeMessage< ::geo_service::GeodFwdRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::geo_service::GeodFwdRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::geo_service::GeodFwdResponse* Arena::CreateMaybeMessage< ::geo_service::GeodFwdResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::geo_service::GeodFwdResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

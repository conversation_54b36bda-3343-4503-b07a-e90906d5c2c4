// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: geo_service/proto/geo_service.proto
#ifndef GRPC_geo_5fservice_2fproto_2fgeo_5fservice_2eproto__INCLUDED
#define GRPC_geo_5fservice_2fproto_2fgeo_5fservice_2eproto__INCLUDED

#include "geo_service/proto/geo_service.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace geo_service {

class GeoService final {
 public:
  static constexpr char const* service_full_name() {
    return "geo_service.GeoService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GeodFwd(::grpc::ClientContext* context, const ::geo_service::GeodFwdRequest& request, ::geo_service::GeodFwdResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::geo_service::GeodFwdResponse>> AsyncGeodFwd(::grpc::ClientContext* context, const ::geo_service::GeodFwdRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::geo_service::GeodFwdResponse>>(AsyncGeodFwdRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::geo_service::GeodFwdResponse>> PrepareAsyncGeodFwd(::grpc::ClientContext* context, const ::geo_service::GeodFwdRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::geo_service::GeodFwdResponse>>(PrepareAsyncGeodFwdRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GeodFwd(::grpc::ClientContext* context, const ::geo_service::GeodFwdRequest* request, ::geo_service::GeodFwdResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GeodFwd(::grpc::ClientContext* context, const ::geo_service::GeodFwdRequest* request, ::geo_service::GeodFwdResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::geo_service::GeodFwdResponse>* AsyncGeodFwdRaw(::grpc::ClientContext* context, const ::geo_service::GeodFwdRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::geo_service::GeodFwdResponse>* PrepareAsyncGeodFwdRaw(::grpc::ClientContext* context, const ::geo_service::GeodFwdRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GeodFwd(::grpc::ClientContext* context, const ::geo_service::GeodFwdRequest& request, ::geo_service::GeodFwdResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::geo_service::GeodFwdResponse>> AsyncGeodFwd(::grpc::ClientContext* context, const ::geo_service::GeodFwdRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::geo_service::GeodFwdResponse>>(AsyncGeodFwdRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::geo_service::GeodFwdResponse>> PrepareAsyncGeodFwd(::grpc::ClientContext* context, const ::geo_service::GeodFwdRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::geo_service::GeodFwdResponse>>(PrepareAsyncGeodFwdRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GeodFwd(::grpc::ClientContext* context, const ::geo_service::GeodFwdRequest* request, ::geo_service::GeodFwdResponse* response, std::function<void(::grpc::Status)>) override;
      void GeodFwd(::grpc::ClientContext* context, const ::geo_service::GeodFwdRequest* request, ::geo_service::GeodFwdResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::geo_service::GeodFwdResponse>* AsyncGeodFwdRaw(::grpc::ClientContext* context, const ::geo_service::GeodFwdRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::geo_service::GeodFwdResponse>* PrepareAsyncGeodFwdRaw(::grpc::ClientContext* context, const ::geo_service::GeodFwdRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GeodFwd_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GeodFwd(::grpc::ServerContext* context, const ::geo_service::GeodFwdRequest* request, ::geo_service::GeodFwdResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GeodFwd : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GeodFwd() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GeodFwd() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GeodFwd(::grpc::ServerContext* /*context*/, const ::geo_service::GeodFwdRequest* /*request*/, ::geo_service::GeodFwdResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGeodFwd(::grpc::ServerContext* context, ::geo_service::GeodFwdRequest* request, ::grpc::ServerAsyncResponseWriter< ::geo_service::GeodFwdResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GeodFwd<Service > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GeodFwd : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GeodFwd() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::geo_service::GeodFwdRequest, ::geo_service::GeodFwdResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::geo_service::GeodFwdRequest* request, ::geo_service::GeodFwdResponse* response) { return this->GeodFwd(context, request, response); }));}
    void SetMessageAllocatorFor_GeodFwd(
        ::grpc::MessageAllocator< ::geo_service::GeodFwdRequest, ::geo_service::GeodFwdResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::geo_service::GeodFwdRequest, ::geo_service::GeodFwdResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GeodFwd() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GeodFwd(::grpc::ServerContext* /*context*/, const ::geo_service::GeodFwdRequest* /*request*/, ::geo_service::GeodFwdResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GeodFwd(
      ::grpc::CallbackServerContext* /*context*/, const ::geo_service::GeodFwdRequest* /*request*/, ::geo_service::GeodFwdResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GeodFwd<Service > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GeodFwd : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GeodFwd() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GeodFwd() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GeodFwd(::grpc::ServerContext* /*context*/, const ::geo_service::GeodFwdRequest* /*request*/, ::geo_service::GeodFwdResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GeodFwd : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GeodFwd() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GeodFwd() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GeodFwd(::grpc::ServerContext* /*context*/, const ::geo_service::GeodFwdRequest* /*request*/, ::geo_service::GeodFwdResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGeodFwd(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GeodFwd : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GeodFwd() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GeodFwd(context, request, response); }));
    }
    ~WithRawCallbackMethod_GeodFwd() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GeodFwd(::grpc::ServerContext* /*context*/, const ::geo_service::GeodFwdRequest* /*request*/, ::geo_service::GeodFwdResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GeodFwd(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GeodFwd : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GeodFwd() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::geo_service::GeodFwdRequest, ::geo_service::GeodFwdResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::geo_service::GeodFwdRequest, ::geo_service::GeodFwdResponse>* streamer) {
                       return this->StreamedGeodFwd(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GeodFwd() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GeodFwd(::grpc::ServerContext* /*context*/, const ::geo_service::GeodFwdRequest* /*request*/, ::geo_service::GeodFwdResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGeodFwd(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::geo_service::GeodFwdRequest,::geo_service::GeodFwdResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GeodFwd<Service > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GeodFwd<Service > StreamedService;
};

}  // namespace geo_service


#endif  // GRPC_geo_5fservice_2fproto_2fgeo_5fservice_2eproto__INCLUDED

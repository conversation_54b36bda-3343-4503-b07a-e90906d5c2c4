# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.geo_service.proto import geo_service_pb2 as geo__service_dot_proto_dot_geo__service__pb2


class GeoServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GeodFwd = channel.unary_unary(
                '/geo_service.GeoService/GeodFwd',
                request_serializer=geo__service_dot_proto_dot_geo__service__pb2.GeodFwdRequest.SerializeToString,
                response_deserializer=geo__service_dot_proto_dot_geo__service__pb2.GeodFwdResponse.FromString,
                )


class GeoServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GeodFwd(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_GeoServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GeodFwd': grpc.unary_unary_rpc_method_handler(
                    servicer.GeodFwd,
                    request_deserializer=geo__service_dot_proto_dot_geo__service__pb2.GeodFwdRequest.FromString,
                    response_serializer=geo__service_dot_proto_dot_geo__service__pb2.GeodFwdResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'geo_service.GeoService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class GeoService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GeodFwd(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/geo_service.GeoService/GeodFwd',
            geo__service_dot_proto_dot_geo__service__pb2.GeodFwdRequest.SerializeToString,
            geo__service_dot_proto_dot_geo__service__pb2.GeodFwdResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

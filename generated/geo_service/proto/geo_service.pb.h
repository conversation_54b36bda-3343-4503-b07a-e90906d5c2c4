// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: geo_service/proto/geo_service.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_geo_5fservice_2fproto_2fgeo_5fservice_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_geo_5fservice_2fproto_2fgeo_5fservice_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_geo_5fservice_2fproto_2fgeo_5fservice_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_geo_5fservice_2fproto_2fgeo_5fservice_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[3]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_geo_5fservice_2fproto_2fgeo_5fservice_2eproto;
namespace geo_service {
class GeoLL;
struct GeoLLDefaultTypeInternal;
extern GeoLLDefaultTypeInternal _GeoLL_default_instance_;
class GeodFwdRequest;
struct GeodFwdRequestDefaultTypeInternal;
extern GeodFwdRequestDefaultTypeInternal _GeodFwdRequest_default_instance_;
class GeodFwdResponse;
struct GeodFwdResponseDefaultTypeInternal;
extern GeodFwdResponseDefaultTypeInternal _GeodFwdResponse_default_instance_;
}  // namespace geo_service
PROTOBUF_NAMESPACE_OPEN
template<> ::geo_service::GeoLL* Arena::CreateMaybeMessage<::geo_service::GeoLL>(Arena*);
template<> ::geo_service::GeodFwdRequest* Arena::CreateMaybeMessage<::geo_service::GeodFwdRequest>(Arena*);
template<> ::geo_service::GeodFwdResponse* Arena::CreateMaybeMessage<::geo_service::GeodFwdResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace geo_service {

// ===================================================================

class GeoLL final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:geo_service.GeoLL) */ {
 public:
  inline GeoLL() : GeoLL(nullptr) {}
  ~GeoLL() override;
  explicit constexpr GeoLL(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GeoLL(const GeoLL& from);
  GeoLL(GeoLL&& from) noexcept
    : GeoLL() {
    *this = ::std::move(from);
  }

  inline GeoLL& operator=(const GeoLL& from) {
    CopyFrom(from);
    return *this;
  }
  inline GeoLL& operator=(GeoLL&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GeoLL& default_instance() {
    return *internal_default_instance();
  }
  static inline const GeoLL* internal_default_instance() {
    return reinterpret_cast<const GeoLL*>(
               &_GeoLL_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GeoLL& a, GeoLL& b) {
    a.Swap(&b);
  }
  inline void Swap(GeoLL* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GeoLL* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GeoLL* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GeoLL>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GeoLL& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GeoLL& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GeoLL* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "geo_service.GeoLL";
  }
  protected:
  explicit GeoLL(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLatFieldNumber = 1,
    kLngFieldNumber = 2,
  };
  // double lat = 1;
  void clear_lat();
  double lat() const;
  void set_lat(double value);
  private:
  double _internal_lat() const;
  void _internal_set_lat(double value);
  public:

  // double lng = 2;
  void clear_lng();
  double lng() const;
  void set_lng(double value);
  private:
  double _internal_lng() const;
  void _internal_set_lng(double value);
  public:

  // @@protoc_insertion_point(class_scope:geo_service.GeoLL)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double lat_;
  double lng_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_geo_5fservice_2fproto_2fgeo_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GeodFwdRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:geo_service.GeodFwdRequest) */ {
 public:
  inline GeodFwdRequest() : GeodFwdRequest(nullptr) {}
  ~GeodFwdRequest() override;
  explicit constexpr GeodFwdRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GeodFwdRequest(const GeodFwdRequest& from);
  GeodFwdRequest(GeodFwdRequest&& from) noexcept
    : GeodFwdRequest() {
    *this = ::std::move(from);
  }

  inline GeodFwdRequest& operator=(const GeodFwdRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GeodFwdRequest& operator=(GeodFwdRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GeodFwdRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GeodFwdRequest* internal_default_instance() {
    return reinterpret_cast<const GeodFwdRequest*>(
               &_GeodFwdRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GeodFwdRequest& a, GeodFwdRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GeodFwdRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GeodFwdRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GeodFwdRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GeodFwdRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GeodFwdRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GeodFwdRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GeodFwdRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "geo_service.GeodFwdRequest";
  }
  protected:
  explicit GeodFwdRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStartFieldNumber = 1,
    kDistMetersFieldNumber = 2,
    kAzimuthDegFieldNumber = 3,
  };
  // .geo_service.GeoLL start = 1;
  bool has_start() const;
  private:
  bool _internal_has_start() const;
  public:
  void clear_start();
  const ::geo_service::GeoLL& start() const;
  PROTOBUF_NODISCARD ::geo_service::GeoLL* release_start();
  ::geo_service::GeoLL* mutable_start();
  void set_allocated_start(::geo_service::GeoLL* start);
  private:
  const ::geo_service::GeoLL& _internal_start() const;
  ::geo_service::GeoLL* _internal_mutable_start();
  public:
  void unsafe_arena_set_allocated_start(
      ::geo_service::GeoLL* start);
  ::geo_service::GeoLL* unsafe_arena_release_start();

  // double dist_meters = 2;
  void clear_dist_meters();
  double dist_meters() const;
  void set_dist_meters(double value);
  private:
  double _internal_dist_meters() const;
  void _internal_set_dist_meters(double value);
  public:

  // double azimuth_deg = 3;
  void clear_azimuth_deg();
  double azimuth_deg() const;
  void set_azimuth_deg(double value);
  private:
  double _internal_azimuth_deg() const;
  void _internal_set_azimuth_deg(double value);
  public:

  // @@protoc_insertion_point(class_scope:geo_service.GeodFwdRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::geo_service::GeoLL* start_;
  double dist_meters_;
  double azimuth_deg_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_geo_5fservice_2fproto_2fgeo_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GeodFwdResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:geo_service.GeodFwdResponse) */ {
 public:
  inline GeodFwdResponse() : GeodFwdResponse(nullptr) {}
  ~GeodFwdResponse() override;
  explicit constexpr GeodFwdResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GeodFwdResponse(const GeodFwdResponse& from);
  GeodFwdResponse(GeodFwdResponse&& from) noexcept
    : GeodFwdResponse() {
    *this = ::std::move(from);
  }

  inline GeodFwdResponse& operator=(const GeodFwdResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GeodFwdResponse& operator=(GeodFwdResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GeodFwdResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GeodFwdResponse* internal_default_instance() {
    return reinterpret_cast<const GeodFwdResponse*>(
               &_GeodFwdResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(GeodFwdResponse& a, GeodFwdResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GeodFwdResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GeodFwdResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GeodFwdResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GeodFwdResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GeodFwdResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GeodFwdResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GeodFwdResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "geo_service.GeodFwdResponse";
  }
  protected:
  explicit GeodFwdResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPosFieldNumber = 1,
  };
  // .geo_service.GeoLL pos = 1;
  bool has_pos() const;
  private:
  bool _internal_has_pos() const;
  public:
  void clear_pos();
  const ::geo_service::GeoLL& pos() const;
  PROTOBUF_NODISCARD ::geo_service::GeoLL* release_pos();
  ::geo_service::GeoLL* mutable_pos();
  void set_allocated_pos(::geo_service::GeoLL* pos);
  private:
  const ::geo_service::GeoLL& _internal_pos() const;
  ::geo_service::GeoLL* _internal_mutable_pos();
  public:
  void unsafe_arena_set_allocated_pos(
      ::geo_service::GeoLL* pos);
  ::geo_service::GeoLL* unsafe_arena_release_pos();

  // @@protoc_insertion_point(class_scope:geo_service.GeodFwdResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::geo_service::GeoLL* pos_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_geo_5fservice_2fproto_2fgeo_5fservice_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GeoLL

// double lat = 1;
inline void GeoLL::clear_lat() {
  lat_ = 0;
}
inline double GeoLL::_internal_lat() const {
  return lat_;
}
inline double GeoLL::lat() const {
  // @@protoc_insertion_point(field_get:geo_service.GeoLL.lat)
  return _internal_lat();
}
inline void GeoLL::_internal_set_lat(double value) {
  
  lat_ = value;
}
inline void GeoLL::set_lat(double value) {
  _internal_set_lat(value);
  // @@protoc_insertion_point(field_set:geo_service.GeoLL.lat)
}

// double lng = 2;
inline void GeoLL::clear_lng() {
  lng_ = 0;
}
inline double GeoLL::_internal_lng() const {
  return lng_;
}
inline double GeoLL::lng() const {
  // @@protoc_insertion_point(field_get:geo_service.GeoLL.lng)
  return _internal_lng();
}
inline void GeoLL::_internal_set_lng(double value) {
  
  lng_ = value;
}
inline void GeoLL::set_lng(double value) {
  _internal_set_lng(value);
  // @@protoc_insertion_point(field_set:geo_service.GeoLL.lng)
}

// -------------------------------------------------------------------

// GeodFwdRequest

// .geo_service.GeoLL start = 1;
inline bool GeodFwdRequest::_internal_has_start() const {
  return this != internal_default_instance() && start_ != nullptr;
}
inline bool GeodFwdRequest::has_start() const {
  return _internal_has_start();
}
inline void GeodFwdRequest::clear_start() {
  if (GetArenaForAllocation() == nullptr && start_ != nullptr) {
    delete start_;
  }
  start_ = nullptr;
}
inline const ::geo_service::GeoLL& GeodFwdRequest::_internal_start() const {
  const ::geo_service::GeoLL* p = start_;
  return p != nullptr ? *p : reinterpret_cast<const ::geo_service::GeoLL&>(
      ::geo_service::_GeoLL_default_instance_);
}
inline const ::geo_service::GeoLL& GeodFwdRequest::start() const {
  // @@protoc_insertion_point(field_get:geo_service.GeodFwdRequest.start)
  return _internal_start();
}
inline void GeodFwdRequest::unsafe_arena_set_allocated_start(
    ::geo_service::GeoLL* start) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_);
  }
  start_ = start;
  if (start) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:geo_service.GeodFwdRequest.start)
}
inline ::geo_service::GeoLL* GeodFwdRequest::release_start() {
  
  ::geo_service::GeoLL* temp = start_;
  start_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::geo_service::GeoLL* GeodFwdRequest::unsafe_arena_release_start() {
  // @@protoc_insertion_point(field_release:geo_service.GeodFwdRequest.start)
  
  ::geo_service::GeoLL* temp = start_;
  start_ = nullptr;
  return temp;
}
inline ::geo_service::GeoLL* GeodFwdRequest::_internal_mutable_start() {
  
  if (start_ == nullptr) {
    auto* p = CreateMaybeMessage<::geo_service::GeoLL>(GetArenaForAllocation());
    start_ = p;
  }
  return start_;
}
inline ::geo_service::GeoLL* GeodFwdRequest::mutable_start() {
  ::geo_service::GeoLL* _msg = _internal_mutable_start();
  // @@protoc_insertion_point(field_mutable:geo_service.GeodFwdRequest.start)
  return _msg;
}
inline void GeodFwdRequest::set_allocated_start(::geo_service::GeoLL* start) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete start_;
  }
  if (start) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::geo_service::GeoLL>::GetOwningArena(start);
    if (message_arena != submessage_arena) {
      start = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, start, submessage_arena);
    }
    
  } else {
    
  }
  start_ = start;
  // @@protoc_insertion_point(field_set_allocated:geo_service.GeodFwdRequest.start)
}

// double dist_meters = 2;
inline void GeodFwdRequest::clear_dist_meters() {
  dist_meters_ = 0;
}
inline double GeodFwdRequest::_internal_dist_meters() const {
  return dist_meters_;
}
inline double GeodFwdRequest::dist_meters() const {
  // @@protoc_insertion_point(field_get:geo_service.GeodFwdRequest.dist_meters)
  return _internal_dist_meters();
}
inline void GeodFwdRequest::_internal_set_dist_meters(double value) {
  
  dist_meters_ = value;
}
inline void GeodFwdRequest::set_dist_meters(double value) {
  _internal_set_dist_meters(value);
  // @@protoc_insertion_point(field_set:geo_service.GeodFwdRequest.dist_meters)
}

// double azimuth_deg = 3;
inline void GeodFwdRequest::clear_azimuth_deg() {
  azimuth_deg_ = 0;
}
inline double GeodFwdRequest::_internal_azimuth_deg() const {
  return azimuth_deg_;
}
inline double GeodFwdRequest::azimuth_deg() const {
  // @@protoc_insertion_point(field_get:geo_service.GeodFwdRequest.azimuth_deg)
  return _internal_azimuth_deg();
}
inline void GeodFwdRequest::_internal_set_azimuth_deg(double value) {
  
  azimuth_deg_ = value;
}
inline void GeodFwdRequest::set_azimuth_deg(double value) {
  _internal_set_azimuth_deg(value);
  // @@protoc_insertion_point(field_set:geo_service.GeodFwdRequest.azimuth_deg)
}

// -------------------------------------------------------------------

// GeodFwdResponse

// .geo_service.GeoLL pos = 1;
inline bool GeodFwdResponse::_internal_has_pos() const {
  return this != internal_default_instance() && pos_ != nullptr;
}
inline bool GeodFwdResponse::has_pos() const {
  return _internal_has_pos();
}
inline void GeodFwdResponse::clear_pos() {
  if (GetArenaForAllocation() == nullptr && pos_ != nullptr) {
    delete pos_;
  }
  pos_ = nullptr;
}
inline const ::geo_service::GeoLL& GeodFwdResponse::_internal_pos() const {
  const ::geo_service::GeoLL* p = pos_;
  return p != nullptr ? *p : reinterpret_cast<const ::geo_service::GeoLL&>(
      ::geo_service::_GeoLL_default_instance_);
}
inline const ::geo_service::GeoLL& GeodFwdResponse::pos() const {
  // @@protoc_insertion_point(field_get:geo_service.GeodFwdResponse.pos)
  return _internal_pos();
}
inline void GeodFwdResponse::unsafe_arena_set_allocated_pos(
    ::geo_service::GeoLL* pos) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(pos_);
  }
  pos_ = pos;
  if (pos) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:geo_service.GeodFwdResponse.pos)
}
inline ::geo_service::GeoLL* GeodFwdResponse::release_pos() {
  
  ::geo_service::GeoLL* temp = pos_;
  pos_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::geo_service::GeoLL* GeodFwdResponse::unsafe_arena_release_pos() {
  // @@protoc_insertion_point(field_release:geo_service.GeodFwdResponse.pos)
  
  ::geo_service::GeoLL* temp = pos_;
  pos_ = nullptr;
  return temp;
}
inline ::geo_service::GeoLL* GeodFwdResponse::_internal_mutable_pos() {
  
  if (pos_ == nullptr) {
    auto* p = CreateMaybeMessage<::geo_service::GeoLL>(GetArenaForAllocation());
    pos_ = p;
  }
  return pos_;
}
inline ::geo_service::GeoLL* GeodFwdResponse::mutable_pos() {
  ::geo_service::GeoLL* _msg = _internal_mutable_pos();
  // @@protoc_insertion_point(field_mutable:geo_service.GeodFwdResponse.pos)
  return _msg;
}
inline void GeodFwdResponse::set_allocated_pos(::geo_service::GeoLL* pos) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete pos_;
  }
  if (pos) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::geo_service::GeoLL>::GetOwningArena(pos);
    if (message_arena != submessage_arena) {
      pos = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, pos, submessage_arena);
    }
    
  } else {
    
  }
  pos_ = pos;
  // @@protoc_insertion_point(field_set_allocated:geo_service.GeodFwdResponse.pos)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace geo_service

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_geo_5fservice_2fproto_2fgeo_5fservice_2eproto

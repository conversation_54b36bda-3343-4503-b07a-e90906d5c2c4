# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: geo_service/proto/geo_service.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='geo_service/proto/geo_service.proto',
  package='geo_service',
  syntax='proto3',
  serialized_options=b'Z\021proto/geo_service',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n#geo_service/proto/geo_service.proto\x12\x0bgeo_service\"!\n\x05GeoLL\x12\x0b\n\x03lat\x18\x01 \x01(\x01\x12\x0b\n\x03lng\x18\x02 \x01(\x01\"]\n\x0eGeodFwdRequest\x12!\n\x05start\x18\x01 \x01(\x0b\x32\x12.geo_service.GeoLL\x12\x13\n\x0b\x64ist_meters\x18\x02 \x01(\x01\x12\x13\n\x0b\x61zimuth_deg\x18\x03 \x01(\x01\"2\n\x0fGeodFwdResponse\x12\x1f\n\x03pos\x18\x01 \x01(\x0b\x32\x12.geo_service.GeoLL2T\n\nGeoService\x12\x46\n\x07GeodFwd\x12\x1b.geo_service.GeodFwdRequest\x1a\x1c.geo_service.GeodFwdResponse\"\x00\x42\x13Z\x11proto/geo_serviceb\x06proto3'
)




_GEOLL = _descriptor.Descriptor(
  name='GeoLL',
  full_name='geo_service.GeoLL',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lat', full_name='geo_service.GeoLL.lat', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lng', full_name='geo_service.GeoLL.lng', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=52,
  serialized_end=85,
)


_GEODFWDREQUEST = _descriptor.Descriptor(
  name='GeodFwdRequest',
  full_name='geo_service.GeodFwdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='start', full_name='geo_service.GeodFwdRequest.start', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='dist_meters', full_name='geo_service.GeodFwdRequest.dist_meters', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='azimuth_deg', full_name='geo_service.GeodFwdRequest.azimuth_deg', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=87,
  serialized_end=180,
)


_GEODFWDRESPONSE = _descriptor.Descriptor(
  name='GeodFwdResponse',
  full_name='geo_service.GeodFwdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pos', full_name='geo_service.GeodFwdResponse.pos', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=182,
  serialized_end=232,
)

_GEODFWDREQUEST.fields_by_name['start'].message_type = _GEOLL
_GEODFWDRESPONSE.fields_by_name['pos'].message_type = _GEOLL
DESCRIPTOR.message_types_by_name['GeoLL'] = _GEOLL
DESCRIPTOR.message_types_by_name['GeodFwdRequest'] = _GEODFWDREQUEST
DESCRIPTOR.message_types_by_name['GeodFwdResponse'] = _GEODFWDRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GeoLL = _reflection.GeneratedProtocolMessageType('GeoLL', (_message.Message,), {
  'DESCRIPTOR' : _GEOLL,
  '__module__' : 'geo_service.proto.geo_service_pb2'
  # @@protoc_insertion_point(class_scope:geo_service.GeoLL)
  })
_sym_db.RegisterMessage(GeoLL)

GeodFwdRequest = _reflection.GeneratedProtocolMessageType('GeodFwdRequest', (_message.Message,), {
  'DESCRIPTOR' : _GEODFWDREQUEST,
  '__module__' : 'geo_service.proto.geo_service_pb2'
  # @@protoc_insertion_point(class_scope:geo_service.GeodFwdRequest)
  })
_sym_db.RegisterMessage(GeodFwdRequest)

GeodFwdResponse = _reflection.GeneratedProtocolMessageType('GeodFwdResponse', (_message.Message,), {
  'DESCRIPTOR' : _GEODFWDRESPONSE,
  '__module__' : 'geo_service.proto.geo_service_pb2'
  # @@protoc_insertion_point(class_scope:geo_service.GeodFwdResponse)
  })
_sym_db.RegisterMessage(GeodFwdResponse)


DESCRIPTOR._options = None

_GEOSERVICE = _descriptor.ServiceDescriptor(
  name='GeoService',
  full_name='geo_service.GeoService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=234,
  serialized_end=318,
  methods=[
  _descriptor.MethodDescriptor(
    name='GeodFwd',
    full_name='geo_service.GeoService.GeodFwd',
    index=0,
    containing_service=None,
    input_type=_GEODFWDREQUEST,
    output_type=_GEODFWDRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_GEOSERVICE)

DESCRIPTOR.services_by_name['GeoService'] = _GEOSERVICE

# @@protoc_insertion_point(module_scope)

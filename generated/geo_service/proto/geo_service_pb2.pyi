"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class GeoLL(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    lat: builtin___float = ...
    lng: builtin___float = ...

    def __init__(self,
        *,
        lat : typing___Optional[builtin___float] = None,
        lng : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"lat",b"lat",u"lng",b"lng"]) -> None: ...
type___GeoLL = GeoLL

class GeodFwdRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    dist_meters: builtin___float = ...
    azimuth_deg: builtin___float = ...

    @property
    def start(self) -> type___GeoLL: ...

    def __init__(self,
        *,
        start : typing___Optional[type___GeoLL] = None,
        dist_meters : typing___Optional[builtin___float] = None,
        azimuth_deg : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"start",b"start"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"azimuth_deg",b"azimuth_deg",u"dist_meters",b"dist_meters",u"start",b"start"]) -> None: ...
type___GeodFwdRequest = GeodFwdRequest

class GeodFwdResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def pos(self) -> type___GeoLL: ...

    def __init__(self,
        *,
        pos : typing___Optional[type___GeoLL] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"pos",b"pos"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pos",b"pos"]) -> None: ...
type___GeodFwdResponse = GeodFwdResponse

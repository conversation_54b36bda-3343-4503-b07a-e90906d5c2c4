/* ========================================
 *
 * Copyright Carbon Autonomous Robotic Systems, 2021
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "generated/lib/drivers/nanopb/proto/rotary_encoder.pb.h"
#include "project.h"

#include "history_list.h"

/* Sentinal values for "off the beginning" and "off the end" */
#define PAST_INDEX -1
#define FUTURE_INDEX -2

static encoders_snapshot null_snapshot;

/* Timestamp helpers */
int64_t ts_compare(time_Timestamp proto_ts, M_Timestamp_t system_ts) {
  int64_t pts = TS2INT64(proto_ts);
  int64_t sts = TS2INT64(system_ts);
  int64_t ret = pts - sts;
  return ret;
}

void init_history(history *h) {
  h->last = 0;
  int allocation_request = sizeof(encoders_snapshot) * NUM_HISTORY;
  /* h->snapshots = (encoders_snapshot*)malloc(allocation_request); <-- only works for small allocations */
  h->snapshots = (encoders_snapshot *)pvPortMalloc(allocation_request);
  memset(h->snapshots, 0, sizeof(encoders_snapshot) * NUM_HISTORY);
  memset(&null_snapshot, 0, sizeof(encoders_snapshot));
  h->sem = xSemaphoreCreateMutex();
}

int history_lock(history *h) { return xSemaphoreTake(h->sem, portMAX_DELAY); }

void history_release(history *h) { xSemaphoreGive(h->sem); }

void add_snapshot(history *h, uint64_t usec, ticks_t front_left_ticks, ticks_t front_right_ticks,
                  ticks_t back_left_ticks, ticks_t back_right_ticks) {
  if (h->last == NUM_HISTORY - 1) {
    h->last = 0;
  } else {
    h->last++;
  }
  encoders_snapshot *ss = &h->snapshots[h->last];
  ss->usec = usec;
  ss->front_left_ticks = front_left_ticks;
  ss->front_right_ticks = front_right_ticks;
  ss->back_left_ticks = back_left_ticks;
  ss->back_right_ticks = back_right_ticks;
}

void get_latest(history *h, encoders_snapshot *pout) {
  memcpy(pout, &h->snapshots[h->last], sizeof(encoders_snapshot));
}

/* searching for best index */

int relative_index(history *h, int index) {
  int start = (h->last + 1) % NUM_HISTORY;
  int ret = (index + start) % NUM_HISTORY;
  return ret;
}

int matched(history *h, int index, uint64_t usec) {
  int64_t curr_diff = usec - h->snapshots[index].usec;
  int64_t next_diff = usec - h->snapshots[(index + 1) % NUM_HISTORY].usec;
  if (next_diff == 0 && curr_diff == 0) {
    /* Special case where the list hasn't been filled in yet */
    return 1;
  }
  return curr_diff >= 0 && next_diff < 0;
}

int best_index(history *h, uint64_t usec) {
  /* binary search for the index i->[ ts ) */
  int start = 0;
  int end = NUM_HISTORY - 1;
  /* Check for before or after the whole list */
  if (usec < h->snapshots[relative_index(h, start)].usec) {
    return PAST_INDEX;
  }
  if (usec >= h->snapshots[relative_index(h, end)].usec) {
    return relative_index(h, end);
  }

  /* Do the bin search */
  int64_t cmp;
  int pivot, rel;
  while (1) {
    pivot = (start + end) / 2;      // pivot in the middle
    rel = relative_index(h, pivot); // convert to array offset
    if (matched(h, rel, usec))      // look for ts in the gap
    {
      return rel;
    }
    cmp = usec - h->snapshots[rel].usec;
    if (cmp > 0) {
      start = pivot + 1;
    } else {
      end = pivot - 1;
    }
  }
  return 0;
}

int next_index(history *h, int index) {
  int start = (h->last + 1) % NUM_HISTORY;
  if (index >= 0) {
    index = (index + 1) % NUM_HISTORY;
    /* Off the end */
    if (index == start) {
      return FUTURE_INDEX;
    }
    return index;
  }
  /* Was before the list, now moving to the beginning */
  if (index == PAST_INDEX) {
    return start;
  }
  if (index == FUTURE_INDEX) {
    return FUTURE_INDEX;
  }
  return 0; // This should never happen
}

encoders_snapshot *get_at_index(history *h, int index) {
  if (index >= 0) {
    return &h->snapshots[index];
  }
  return &null_snapshot;
}

void history_list_verify(history *h, uint32_t *pooo_elements, uint32_t *pshort_time_elements,
                         uint32_t *plong_time_elements) {
  int failures = 0;
  int matches = 0;
  *pshort_time_elements = 0;
  *plong_time_elements = 0;
  uint64_t last_time = 0;
  for (int i = 0; i < NUM_HISTORY; i++) {
    encoders_snapshot *pss = &h->snapshots[relative_index(h, i)];
    if (last_time > pss->usec) {
      failures++;
    }
    if (last_time == pss->usec) {
      matches++;
    }
    if (i > 0) {
      uint64_t diff = pss->usec - last_time;
      if (diff > 10000) {
        (*plong_time_elements)++;
      }
      if (diff < 10) {
        (*pshort_time_elements)++;
      }
    }
    last_time = pss->usec;
  }
  *pooo_elements = failures + matches;
}

#include <assert.h>
#include <stdio.h>

#include "../history_list.h"
#include "generated/lib/drivers/nanopb/proto/rotary_encoder.pb.h"
#include "project.h"

#define END_OF_TIME 99999

history history_list;

const int samples = 1071;

void print_ss(encoders_snapshot *p) {
  printf("ts: %d:%d, rl: %d, fr:%d, bl: %d, br: %d\n", p->ts.seconds, p->ts.micros, p->front_left_ticks,
         p->front_right_ticks, p->back_left_ticks, p->back_right_ticks);
}

int main(int argc, char *argv[]) {
  printf("history list test\n");

  init_history(&history_list);

  for (int i = 1; i < samples + 1; i++) {
    M_Timestamp_t ts;
    ts.seconds = i * 10;
    ts.micros = i * 10;
    add_snapshot(&history_list, ts, i, -i, i, -i);
  }

  printf("last: %d\n", history_list.last);
  for (int i = 0; i < NUM_HISTORY; i++) {
    encoders_snapshot *p;
    p = &history_list.snapshots[i];
    printf("index: %d, ", i);
    print_ss(p);
  }

  encoders_snapshot ss;
  printf("latest:\n");
  get_latest(&history_list, &ss);
  print_ss(&ss);

  time_Timestamp ts;
  ts.seconds = ts.micros = 0;
  int index = index = best_index(&history_list, ts);
  ts.seconds = ts.micros = END_OF_TIME;
  int end_index = best_index(&history_list, ts);
  printf("first index: %d, end index %d\n", index, end_index);
  /* We wrap around so end + 1 should be first */
  assert(end_index == next_index(&history_list, index) - 1);

  int test_times[9] = {0, END_OF_TIME, 1, samples * 10, samples * 10 - 1, 445, 775, 1005, 555};
  for (int i = 0; i < 9; i++) {
    ts.seconds = test_times[i];
    ts.micros = ts.seconds * 10;
    printf("----------------------------------------------------\n");
    int64_t t = TS2INT64(ts);
    printf("Testing: %d:%d (%lld)\n", ts.seconds, ts.micros, (long long)t);
    index = best_index(&history_list, ts);
    encoders_snapshot *p = get_at_index(&history_list, index);
    printf("index best: %d, ", index);
    print_ss(p);
    int64_t t1 = TS2INT64(p->ts);
    index = next_index(&history_list, index);
    p = get_at_index(&history_list, index);
    printf("index next: %d, ", index);
    print_ss(p);
    int64_t t2 = TS2INT64(p->ts);
    printf("[%lld\n %lld\n %lld]\n", (long long)t1, (long long)t, (long long)t2);
    /* for the time validation assert below we modify the sentinal t2 to be past the end of time + 1*/
    if (index == -2) {
      t2 = 99999999990 + 1;
    }
    assert(t >= t1 && t < t2);
  }

  return 0;
}

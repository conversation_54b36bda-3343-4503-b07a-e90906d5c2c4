/* ========================================
 *
 * Copyright Maka Autonomous Robotic Systems, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka Autonomous Robotic Systems.
 *
 * ========================================
 */

#include "spi_ops.h"
#include "FreeRTOS.h"
#include "project.h"
#include "semphr.h"

SemaphoreHandle_t spi_sem = NULL;

void SPIBoot() {
  spi_sem = xSemaphoreCreateMutex();
  SPI_Start();
}

int SPIAcquire() { return xSemaphoreTake(spi_sem, pdMS_TO_TICKS(100)); }

void SPIRelease() { xSemaphoreGive(spi_sem); }

void SPIWaitTx() {
  uint8_t bits;
  bits = SPI_ReadTxStatus();
  while (!(bits & SPI_STS_SPI_DONE))
    bits = SPI_ReadTxStatus();
}

void SPIWaitRx() {
  uint8_t size;
  size = SPI_GetRxBufferSize();
  while (size == 0)
    size = SPI_GetRxBufferSize();
}

void SPISelect(write_sig_t f) { f(0); }

void SPIDeselect(write_sig_t f) { f(1); }

void SPIWrite(write_sig_t f, uint8_t command) {
  SPIDeselect(f);
  SPI_ClearTxBuffer();
  SPISelect(f);
  SPI_WriteTxData(command);
  SPIWaitTx();
  SPIDeselect(f);
}

void SPIWriteOp(write_sig_t f, uint8_t op, uint8_t data) {
  SPIDeselect(f);
  SPISelect(f);

  SPI_ClearTxBuffer();
  SPI_WriteTxData(op);
  SPIWaitTx();
  SPI_WriteTxData(data);
  SPIWaitTx();

  SPIDeselect(f);
}

uint8_t SPIRead1(write_sig_t f, uint8_t command) {
  uint8_t value;
  SPIDeselect(f);
  SPISelect(f);

  SPI_ClearTxBuffer();
  SPI_WriteTxData(command);
  SPIWaitTx();

  SPI_ClearRxBuffer();
  SPI_WriteTxData(0x0);
  SPIWaitRx();
  value = SPI_ReadRxData();

  SPIDeselect(f);
  return value;
}

uint8_t SPIReadWrite1_nosel(uint8_t data) {
  uint8_t value;

  SPI_ClearTxBuffer();
  SPI_ClearRxBuffer();
  SPI_WriteTxData(data);
  SPIWaitTx();
  SPIWaitRx();
  value = SPI_ReadRxData();

  return value;
}

int32_t SPIRead4(write_sig_t f, uint8_t command) {
  int32_t value;
  uint8_t *p = (uint8_t *)&value;

  SPIDeselect(f);
  SPISelect(f);

  SPI_ClearTxBuffer();
  SPI_WriteTxData(command);
  SPIWaitTx();

  for (int i = 0; i < 4; i++) {
    SPI_ClearRxBuffer();
    SPI_WriteTxData(0x0);
    SPIWaitTx();
    SPIWaitRx();
    p[3 - i] = SPI_ReadRxData();
  }

  SPIDeselect(f);
  return value;
}

/* [] END OF FILE */

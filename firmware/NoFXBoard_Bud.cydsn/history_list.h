/* ========================================
 *
 * Copyright Carbon Autonomous Robotic Systems, 2021
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "semphr.h"

#define NUM_ENCODERS 4
#define NUM_HISTORY 1000 // We need 1 second's worth at 1ms per

#define TS2INT64(x) ((uint64_t)x.seconds * (uint64_t)(1000 * 1000) + x.micros)
#define INT642sec(x) (x / (1000 * 1000))
#define INT642usec(x) (x % (1000 * 1000))

typedef int32_t ticks_t;
typedef struct encoders_snapshot {
  ticks_t front_left_ticks;
  ticks_t front_right_ticks;
  ticks_t back_left_ticks;
  ticks_t back_right_ticks;
  uint64_t usec;
} encoders_snapshot;

typedef struct history {
  int last;
  encoders_snapshot *snapshots;
  SemaphoreHandle_t sem;
} history;

void init_history(history *h);
void add_snapshot(history *h, uint64_t usec, ticks_t front_left_ticks, ticks_t front_right_ticks,
                  ticks_t back_left_ticks, ticks_t back_right_ticks);
void get_latest(history *h, encoders_snapshot *pout);
int best_index(history *h, uint64_t usec);
int next_index(history *h, int index);
int history_lock(history *h);
void history_release(history *h);
encoders_snapshot *get_at_index(history *h, int index);
void history_list_verify(history *h, uint32_t *pooo_elements, uint32_t *pshort_time_elements,
                         uint32_t *plong_time_elements);

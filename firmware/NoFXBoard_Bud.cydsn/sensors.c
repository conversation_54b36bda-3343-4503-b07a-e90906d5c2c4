/* ========================================
 *
 * Copyright Maka Autonomous Robotic Systems, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka Autonomous Robotic Systems.
 *
 * ========================================
 */

#include "FreeRTOS.h"
#include "semphr.h"
#include "spi_ops.h"
#include "task.h"

SemaphoreHandle_t eoc_sem = NULL;

CY_ISR(EOC_Isr_Handler) {
  BaseType_t xHigherPriorityTaskWoken = pdFALSE;
  xSemaphoreGiveFromISR(eoc_sem, &xHigherPriorityTaskWoken);
  portEND_SWITCHING_ISR(xHigherPriorityTaskWoken);
}

void Sensors_Boot() {
  // make our binary sem and initialize it to 1
  eoc_sem = xSemaphoreCreateBinary();

  // set up the isr
  EOC_Isr_Start();
  EOC_Isr_SetVector(EOC_Isr_Handler);

  // https://datasheets.maximintegrated.com/en/ds/MAX11638-MAX11643.pdf pg 15
  // Clock Modes:
  //     00 - CNVSTn (40ns), wait for EOCn, then read your values
  //     01 - CNVSTn (40ns), wait for EOCn for each conversion, then read your values
  //     10 - Write Conversion Register, Wait EOCn, read your values
  //     11 - Write Conversion Register, Wait 16 SCLK cycles and read for each of your values

  // Clock use 00 - single CNVSTn then wait for EOC, Ref we use 10 internal ref always on
  // Setup Register: 01 10 10 10 [Setup Reg][ClockMode: 00][Ref Mode: 10][10]
  while (!SPIAcquire()) {
    taskYIELD();
  }

  SPIWrite(Sensors_SSn_Write, 0x4a);

  // Convert first 2 values - 1 0001 000
  SPIWrite(Sensors_SSn_Write, 0x88);
  SPIRelease();
}

int Wait_EOC() { return xSemaphoreTake(eoc_sem, 100 / portTICK_PERIOD_MS); }

float Sensors_Fuel_Level() {
  float ret = 0.0;

  volatile uint8_t msb_bat;
  volatile uint8_t lsb_bat;
  volatile uint8_t msb_fuel;
  volatile uint8_t lsb_fuel;

  EOC_Isr_ClearPending();

  // Trigger the CNVSTn line for minimum of 40ns (in testing these 2 instructions product 290ns response on the scope)

  while (!SPIAcquire()) {
    taskYIELD();
  }

  Sensors_CNVSTn_Write(0);
  Sensors_CNVSTn_Write(1);

  // Wait for EOC - faster than pumping 16 SCLK
  if (!Wait_EOC()) {
    return ret;
  }

  // SSn the ADC and read 4 1byte values
  SPISelect(Sensors_SSn_Write);
  msb_bat = SPIReadWrite1_nosel(0x00);
  lsb_bat = SPIReadWrite1_nosel(0x00);
  msb_fuel = SPIReadWrite1_nosel(0x00);
  lsb_fuel = SPIReadWrite1_nosel(0x00);
  SPIDeselect(Sensors_SSn_Write);
  SPIRelease();

  // shift, compare, return
  ret = (float)((msb_fuel << 8) | lsb_fuel) / ((msb_bat << 8) | lsb_bat);

  return ret;
}

/* [] END OF FILE */

/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "drive_controller.h"

void Driver_Boot() { init_solenoid_controller(); }

void drive_forward(uint16_t speed) {
  drive_out_n(DRIVE_FORWARD_OUTPUT, speed);
  drive_out_n(DRIVE_BACKWARD_OUTPUT, 0);
}

void drive_backward(uint16_t speed) {
  drive_out_n(DRIVE_FORWARD_OUTPUT, 0);
  drive_out_n(DRIVE_BACKWARD_OUTPUT, speed);
}

void stop_drive() {
  drive_out_n(DRIVE_FORWARD_OUTPUT, 0);
  drive_out_n(DRIVE_BACKWARD_OUTPUT, 0);
}

void turn_right_wheel_cw(uint16_t speed) {
  drive_out_n(R_HELAC_CCW_OUTPUT, 0);
  drive_out_n(R_HELAC_CW_OUTPUT, speed);
}

void turn_left_wheel_cw(uint16_t speed) {
  drive_out_n(L_HELAC_CCW_OUTPUT, 0);
  drive_out_n(L_HELAC_CW_OUTPUT, speed);
}

void turn_right_wheel_ccw(uint16_t speed) {
  drive_out_n(R_HELAC_CCW_OUTPUT, speed);
  drive_out_n(R_HELAC_CW_OUTPUT, 0);
}

void turn_left_wheel_ccw(uint16_t speed) {
  drive_out_n(L_HELAC_CCW_OUTPUT, speed);
  drive_out_n(L_HELAC_CW_OUTPUT, 0);
}

void point_straight() {
  drive_out_n(R_HELAC_CCW_OUTPUT, 0);
  drive_out_n(R_HELAC_CW_OUTPUT, 0);

  drive_out_n(L_HELAC_CCW_OUTPUT, 0);
  drive_out_n(L_HELAC_CW_OUTPUT, 0);
}

void unidirectional_drive(uint8_t in1, uint8_t in2) {
  drive_out_n(in1, MAX_DRIVE_OUTPUT);
  drive_out_n(in2, 0);
}

void brake() { unidirectional_drive(BRAKE_IN_1, BRAKE_IN_2); }

void shift() { unidirectional_drive(SHIFT_IN_1, SHIFT_IN_2); }

void fan() { unidirectional_drive(FAN_IN_1, FAN_IN_2); }

void generator() { unidirectional_drive(GENERATOR_IN_1, GENERATOR_IN_2); }

void traction_control() { unidirectional_drive(TXN_CTL_IN_1, TXN_CTL_IN_2); }
/* [] END OF FILE */

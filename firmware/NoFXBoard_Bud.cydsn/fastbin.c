#include "fastbin.h"
#include "rotary_encoder.h"

static uint8_t buffer[sizeof(fastbin_Header) + sizeof(fastbin_Rotary_Snapshot_Reply)];

/* The contract here is that we won't get called again until the last outgoing buffer has been sent - meaning
 * we can return our static buffer as data */
uint8_t *handle_fastbin_message(M_Ethernet_W5500_msg_t *msg, int *size) {
  fastbin_Header *header = (fastbin_Header *)msg->data;
  fastbin_Header *resp_header = (fastbin_Header *)buffer;
  resp_header->request_id = header->request_id;

  uint8_t *pin = (uint8_t *)msg->data + sizeof(fastbin_Header);
  uint8_t *pout = (uint8_t *)buffer + sizeof(fastbin_Header);

  *size = 0;
  switch (header->opcode) {
  case ROTARY_SNAPSHOT_OPCODE: {
    resp_header->opcode = ROTARY_SNAPSHOT_REPLY_OPCODE;
    fastbin_Rotary_Snapshot_Reply *reply = (fastbin_Rotary_Snapshot_Reply *)pout;
    fastbin_Rotary_Snapshot_Request *req = (fastbin_Rotary_Snapshot_Request *)pin;

    GetFastbinSnapshot(req, reply);
    *size = sizeof(fastbin_Header) + sizeof(fastbin_Rotary_Snapshot_Reply);

    return buffer;
  } break;

  case ROTARY_TICKS_OPCODE: {
    resp_header->opcode = ROTARY_TICKS_REPLY_OPCODE;
    fastbin_Rotary_Reply *reply = (fastbin_Rotary_Reply *)pout;

    GetFastbinTicks(reply);
    *size = sizeof(fastbin_Header) + sizeof(fastbin_Rotary_Reply);

    return buffer;
  } break;

  default:
    break;
  }
  return NULL;
}

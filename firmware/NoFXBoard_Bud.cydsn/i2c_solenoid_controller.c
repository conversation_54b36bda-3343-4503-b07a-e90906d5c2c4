/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "i2c_solenoid_controller.h"

void _send_register(uint8_t reg, uint8_t data) {
  uint8_t res;
  res = I2C_MasterSendStart(I2C_SOLENOID_ADDR, 0);
  if (res != I2C_MSTR_NO_ERROR) {
    return;
  }
  res = I2C_MasterWriteByte(reg);
  if (res != I2C_MSTR_NO_ERROR) {
    return;
  }
  res = I2C_MasterWriteByte(data);
  if (res != I2C_MSTR_NO_ERROR) {
    return;
  }
  res = I2C_MasterSendStop();
  if (res != I2C_MSTR_NO_ERROR) {
    return;
  }
}

uint8_t _read_reg(uint8_t reg) {
  uint8_t res, val_to_read;
  res = I2C_MasterSendStart(I2C_SOLENOID_ADDR, 0);
  if (res != I2C_MSTR_NO_ERROR) {
    return -1;
  }
  res = I2C_MasterWriteByte(reg);
  if (res != I2C_MSTR_NO_ERROR) {
    return -1;
  }
  res = I2C_MasterSendStop();
  if (res != I2C_MSTR_NO_ERROR) {
    return -1;
  }
  res = I2C_MasterSendStart(I2C_SOLENOID_ADDR, 1);
  if (res != I2C_MSTR_NO_ERROR) {
    return -1;
  }
  val_to_read = I2C_MasterReadByte(0);
  res = I2C_MasterSendStop();
  if (res != I2C_MSTR_NO_ERROR) {
    return -1;
  }
  return val_to_read;
}

inline uint8_t output_n_reg_start(uint8_t out) { return PWM_OUT_REG_START + out * 4; }

inline uint8_t output_n_on_l(uint8_t out) { return output_n_reg_start(out); }

inline uint8_t output_n_on_h(uint8_t out) { return output_n_reg_start(out) + 1; }

inline uint8_t output_n_off_l(uint8_t out) { return output_n_reg_start(out) + 2; }

inline uint8_t output_n_off_h(uint8_t out) { return output_n_reg_start(out) + 3; }

void _drive_out_n(uint8_t n, uint16_t on_pwm, uint16_t off_pwm) {
  uint8_t reg = output_n_reg_start(n);
  uint8_t res = I2C_MasterSendStart(I2C_SOLENOID_ADDR, 0);
  if (res != I2C_MSTR_NO_ERROR) {
    return;
  }
  res = I2C_MasterWriteByte(reg);
  if (res != I2C_MSTR_NO_ERROR) {
    return;
  }

  res = I2C_MasterWriteByte((uint8_t)(on_pwm & 0xFF)); // ON LSB
  if (res != I2C_MSTR_NO_ERROR) {
    return;
  }

  res = I2C_MasterWriteByte((uint8_t)((on_pwm >> 8) & 0x0F)); // ON MSB
  if (res != I2C_MSTR_NO_ERROR) {
    return;
  }

  res = I2C_MasterWriteByte((uint8_t)(off_pwm & 0xFF)); // OFF LSB
  if (res != I2C_MSTR_NO_ERROR) {
    return;
  }

  res = I2C_MasterWriteByte((uint8_t)((off_pwm >> 8) & 0x0F)); // OFF MSB
  if (res != I2C_MSTR_NO_ERROR) {
    return;
  }

  res = I2C_MasterSendStop();
  if (res != I2C_MSTR_NO_ERROR) {
    return;
  }
}

void init_solenoid_controller() {
  uint8_t mode1_val, mode2_val; // , mode1_val_read;

  // mode1_val_read = _read_reg(MODE1_REG);

  mode1_val = MODE1_SLEEP;
  _send_register(MODE1_REG, mode1_val);

  // mode1_val_read = _read_reg(MODE1_REG);

  mode1_val = MODE1_EXTCLK | MODE1_AUTOINCREMENT;
  _send_register(MODE1_REG, mode1_val);

  // mode1_val_read = _read_reg(MODE1_REG);

  // mode1_val_read = _read_reg(MODE2_REG);

  mode2_val = MODE2_OUTDRV | MODE2_OUTNE_0;
  _send_register(MODE2_REG, mode2_val);

  // mode1_val_read = _read_reg(MODE2_REG);
}

void _read_out_n(uint8_t n) {
  _read_reg(output_n_on_l(n));
  _read_reg(output_n_on_h(n));
  _read_reg(output_n_off_l(n));
  _read_reg(output_n_off_h(n));
}

void drive_out_n(uint8_t n, uint16_t on_pwm) {
  _drive_out_n(n, 0, on_pwm);
  _read_out_n(n);
}
/* [] END OF FILE */

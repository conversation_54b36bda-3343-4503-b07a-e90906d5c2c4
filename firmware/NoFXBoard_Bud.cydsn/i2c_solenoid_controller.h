/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PR<PERSON><PERSON>ETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

/*
 * Device driver for the NXP pca9685 q900 118 i2c pwm io extender.
 */
#ifndef __I2C_SOLENOID_CONTROLLER__
#define __I2C_SOLENOID_CONTROLLER__

#include "I2C.h"
#include "I2C_PVT.h"

#define I2C_SOLENOID_ADDR 0b01000011

#define MODE1_REG 0x00
#define MODE1_RESTART 0x80
#define MODE1_EXTCLK 0x40
#define MODE1_AUTOINCREMENT 0x20
#define MODE1_SLEEP 0x10
#define MODE1_SUB1 0x08
#define MODE1_SUB2 0x04
#define MODE1_SUB3 0x02
#define MODE1_ALLCALL 0x01

#define MODE2_REG 0x01
#define MODE2_INVRT 0x10
#define MODE2_OCH 0x08
#define MODE2_OUTDRV                                                                                                   \
  0x04 // this bit True: totem pole output, False, open drain output; DRV8871 has internal pulldowns, so this should be
       // strong drive
#define MODE2_OUTNE_1 0x02
#define MODE2_OUTNE_0 0x01

#define SUBADR1_REG 0x02
#define SUBADR2_REG 0x03
#define SUBADR3_REG 0x04

#define ALLCALLADR_REG 0x05

#define MAX_DRIVE_OUTPUT 0x0FFF
/*
 * 16 outputs;
 * 2 configs / output; specify time on and time off;
 * ie. LEDn_ON, LEDn_OFF
 * 12 bit pwm, therefore, pwm value takes 2 bytes.
 * therefore 2 bytes per config; therfore 4 bytes per output
 */

#define PWM_OUT_REG_START 0x06

void init_solenoid_controller();

void drive_out_n(uint8_t n, uint16_t on_pwm);

#endif
/* [] END OF FILE */

/* ========================================
 *
 * Copyright Maka Autonomous Robotic Systems, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */
#include "fastbin.h"
#include "generated/lib/drivers/nanopb/proto/rotary_encoder.pb.h"
#include "m_time_request.h"
#include "stdint.h"

enum RE_TYPE { RE_QUAD, RE_NONE };
void RotaryEncoder_Boot(uint8_t priority, enum RE_TYPE *);
void GetRotaryEncoderSnapshot(rotary_encoder_RotaryEncoderSnapshot_Request *request,
                              rotary_encoder_RotaryEncoderSnapshot_Reply *reply);
void GetFastbinSnapshot(fastbin_Rotary_Snapshot_Request *request, fastbin_Rotary_Snapshot_Reply *reply);
void GetRotaryEncoderTicks(rotary_encoder_RotaryEncoder_Reply *);
void GetFastbinTicks(fastbin_Rotary_Reply *reply);
void GetRotaryHistoryFailures(uint32_t *ptime_warps, uint32_t *pooo_elements, uint32_t *pmax_usec_distance,
                              uint32_t *pepoch_resets, uint32_t *ppps_plus1, uint32_t *ppps_signal_missed,
                              uint32_t *ppps_signal_reject, uint32_t *num_short_time, uint32_t *num_long_time);

/* [] END OF FILE */

/* ========================================
 *
 * Copyright Maka Autonomous Robotic Systems, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka Autonomous Robotic Systems.
 *
 * ========================================
 */

#include "project.h"

typedef void (*write_sig_t)(uint8_t);

void SPIBoot();
int SPIAcquire();
void SPIRelease();
void SPIWaitTx();
void SPIWaitRx();
void SPISelect(write_sig_t f);
void SPIDeselect(write_sig_t f);
void SPIWrite(write_sig_t f, uint8_t command);
void SPIWriteOp(write_sig_t f, uint8_t op, uint8_t data);
uint8_t SPIRead1(write_sig_t f, uint8_t command);
int32_t SPIRead4(write_sig_t f, uint8_t command);
uint8_t SPIReadWrite1_nosel(uint8_t data);

/* [] END OF FILE */

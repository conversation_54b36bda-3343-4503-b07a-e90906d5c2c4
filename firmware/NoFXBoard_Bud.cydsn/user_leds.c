/* ========================================
 *
 * Copyright Carbon Robotics, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */
#include "project.h"

#define LEDS_I2C_SLAVE 0x61

void Send_Register(uint8_t reg, uint8_t data) {
  uint8_t res;
  res = I2C_MasterSendStart(LEDS_I2C_SLAVE, 0);
  if (res != I2C_MSTR_NO_ERROR) {
    PIN_LED_Write(1);
    return;
  }
  res = I2C_MasterWriteByte(reg);
  if (res != I2C_MSTR_NO_ERROR) {
    PIN_LED_Write(1);
    return;
  }
  res = I2C_MasterWriteByte(data);
  if (res != I2C_MSTR_NO_ERROR) {
    PIN_LED_Write(1);
    return;
  }
  res = I2C_MasterSendStop();
  if (res != I2C_MSTR_NO_ERROR) {
    PIN_LED_Write(1);
    return;
  }
}

#define PWM_REG 0xaa
#define FULL_ON 0x55
#define FULL_OFF 0x00
#define PWM_LOW 0x06
#define PWM_MED 0x40
#define PWM_HIGH 0xff
#define PWM_OFF 0x00
#define DELAY_LIGHTS 50

void User_Leds_Boot_Lights() {
  int vals[] = {PWM_LOW, PWM_MED, PWM_HIGH, PWM_OFF};
  int lights[] = {0x02, 0x03, 0x04, 0x05, 0x06, 0x07};

  Send_Register(0x00, 0x00);
  Send_Register(0x14, PWM_REG); // LED_OUT0
  Send_Register(0x15, PWM_REG); // LED_OUT1

  for (int j = 0; j < 6; j++)
    Send_Register(lights[j], PWM_OFF);

  for (int i = 0; i < 4; i++) {
    for (int j = 0; j < 6; j++) {
      Send_Register(lights[j], vals[i]); // pwm reg set
      CyDelay(DELAY_LIGHTS);
    }
  }
}

/* [] END OF FILE */
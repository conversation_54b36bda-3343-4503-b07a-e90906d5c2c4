/* ========================================
 *
 * Copyright Maka Autonomous Robotic Systems, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND <PERSON>OP<PERSON>ETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */
#include "rotary_encoder.h"
#include "FreeRTOS.h"
#include "fastbin.h"
#include "history_list.h"
#include "project.h"
#include "spi_ops.h"
#include "task.h"

/********************************************************************************
 * From datasheet:
 *******************************************************************************/
// Count modes
#define NQUAD 0x00   // non-quadrature mode
#define QUADRX1 0x01 // X1 quadrature mode
#define QUADRX2 0x02 // X2 quadrature mode
#define QUADRX4 0x03 // X4 quadrature mode
// Running modes
#define FREE_RUN 0x00
#define SINGE_CYCLE 0x04
#define RANGE_LIMIT 0x08
#define MODULO_N 0x0C
// Index modes
#define DISABLE_INDX 0x00 // index_disabled
#define INDX_LOADC 0x10   // index_load_CNTR
#define INDX_RESETC 0x20  // index_rest_CNTR
#define INDX_LOADO 0x30   // index_load_OL
#define ASYNCH_INDX 0x00  // asynchronous index
#define SYNCH_INDX 0x80   // synchronous index
// Clock filter modes
#define FILTER_1 0x00 // filter clock frequncy division factor 1
#define FILTER_2 0x80 // filter clock frequncy division factor 2
/* **MDR1 configuration data; any of these***
 ***data segments can be ORed together***/
// Flag modes
#define NO_FLAGS 0x00 // all flags disabled
#define IDX_FLAG 0x10 // IDX flag
#define CMP_FLAG 0x20 // CMP flag
#define BW_FLAG 0x40  // BW flag
#define CY_FLAG 0x80  // CY flag
// 1 to 4 bytes data-width
#define BYTE_4 0x00 // four byte mode
#define BYTE_3 0x01 // three byte mode
#define BYTE_2 0x02 // two byte mode
#define BYTE_1 0x03 // one byte mode
// Enable/disable counter
#define EN_CNTR 0x00  // counting enabled
#define DIS_CNTR 0x04 // counting disabled

/* LS7366R op-code list */
#define CLR_MDR0 0x08
#define CLR_MDR1 0x10
#define CLR_CNTR 0x20
#define CLR_STR 0x30
#define READ_MDR0 0x48
#define READ_MDR1 0x50
#define READ_CNTR 0x60
#define READ_OTR 0x68
#define READ_STR 0x70
#define WRITE_MDR1 0x90
#define WRITE_MDR0 0x88
#define WRITE_DTR 0x98
#define LOAD_CNTR 0xE0
#define LOAD_OTR 0xE4

/* STR Bits */
#define STR_CNT_EN 0x02

/* Config */
int booted = 0;
uint32_t poll_sleep_ms = 1;
volatile uint32_t g_time_failures = 0;
volatile uint32_t g_max_usec_distance = 0;
extern volatile uint32_t g_pps_epoch_resets;
extern volatile uint32_t g_pps_plus1_micros;
extern volatile uint32_t g_pps_signal_reject;
extern volatile uint32_t g_pps_signal_missed;

typedef struct quad_rotary {
  write_sig_t SSn;
} quad_rotary;

/* Wiring for each posible location */
quad_rotary rotaries_index[4] = {{FL_SSn_Write}, {FR_SSn_Write}, {BL_SSn_Write}, {BR_SSn_Write}};

/* Configured locations get set here (Max 2) */
quad_rotary *encoders[NUM_ENCODERS] = {NULL, NULL, NULL, NULL};

history history_list;

/*
 * handle read task for quadrature encoders
 *
 */
ticks_t get_quad_rotary(struct quad_rotary *quad) { return SPIRead4(quad->SSn, READ_CNTR); }

void RotaryEncoder_Poll(void *unused) {
  (void)unused;
  uint64_t last_time = 0;

  for (;;) {
    if (!SPIAcquire()) {
      taskYIELD();
      continue;
    }

    ticks_t flt = 0;
    ticks_t frt = 0;
    ticks_t blt = 0;
    ticks_t brt = 0;

    M_Timestamp_t timestamp;
    if (PPS_Time_Get_Timestamp(&timestamp) == M_ERROR_CODE_OK) {
      /*
       * If our time drifted backwards then log it and use the latest time.
       */
      uint64_t this_time = TS2INT64(timestamp);
      if (last_time >= this_time) {
        uint32_t distance = last_time - this_time;
        if (distance > g_max_usec_distance) {
          g_max_usec_distance = distance;
        }
        g_time_failures++;
        this_time = last_time;
      } else {
        last_time = this_time;
      }

      /* Get values for each configured encoder */
      if (encoders[0] != NULL) {
        flt = get_quad_rotary(encoders[0]);
      }
      if (encoders[1] != NULL) {
        frt = get_quad_rotary(encoders[1]);
      }
      if (encoders[2] != NULL) {
        blt = get_quad_rotary(encoders[2]);
      }
      if (encoders[3] != NULL) {
        brt = get_quad_rotary(encoders[3]);
      }

      SPIRelease();
      if (history_lock(&history_list) == pdTRUE) {
        add_snapshot(&history_list, TS2INT64(timestamp), flt, frt, blt, brt);
        history_release(&history_list);
      }
    } else {
      SPIRelease();
    }
    vTaskDelay(pdMS_TO_TICKS(poll_sleep_ms));
  }
}

void RotaryEncoder_Boot(uint8_t task_priority, enum RE_TYPE *encoder_types) {
  for (int i = 0; i < NUM_ENCODERS; i++) {
    if (encoder_types[i] == RE_QUAD) {
      encoders[i] = &rotaries_index[i];
    }
  }

  /* This locks out the spi and will also prevent an existing poller from doing work until we have (re)configured */
  while (!SPIAcquire()) {
    taskYIELD();
  }

  for (int i = 0; i < NUM_ENCODERS; i++) {
    if (encoders[i] == NULL)
      continue;
    struct quad_rotary *quad = encoders[i];
    SPIWrite(quad->SSn, CLR_CNTR);
    SPIWriteOp(quad->SSn, WRITE_MDR0, QUADRX4 | FREE_RUN | DISABLE_INDX | ASYNCH_INDX | FILTER_1);
    SPIWriteOp(quad->SSn, WRITE_MDR1, NO_FLAGS | BYTE_4 | EN_CNTR);
  }

  SPIRelease();

  if (!booted) {
    booted = 1;

    /* Only call this once. If in the future we need to adjust the size we have to free the old memory */
    init_history(&history_list);
    xTaskCreate(RotaryEncoder_Poll, "Rotary Encoders", configMINIMAL_STACK_SIZE + 1024, NULL, task_priority, NULL);
  }
}

void fill_reply_from_snapshot(rotary_encoder_RotaryEncoder_Reply *reply, encoders_snapshot *pss) {
  reply->timestamp.seconds = INT642sec(pss->usec);
  reply->timestamp.micros = INT642usec(pss->usec);
  reply->has_timestamp = true;
  reply->front_left_ticks = pss->front_left_ticks;
  reply->front_right_ticks = pss->front_right_ticks;
  reply->back_left_ticks = pss->back_left_ticks;
  reply->back_right_ticks = pss->back_right_ticks;
}

void GetRotaryEncoderSnapshot(rotary_encoder_RotaryEncoderSnapshot_Request *request,
                              rotary_encoder_RotaryEncoderSnapshot_Reply *reply) {
  if (!booted) {
    return;
  }
  int index;
  uint64_t first = TS2INT64(request->first);
  uint64_t last = TS2INT64(request->last);
  encoders_snapshot *p;

  if (history_lock(&history_list) == pdTRUE) {
    index = best_index(&history_list, first);
    p = get_at_index(&history_list, index);
    fill_reply_from_snapshot(&reply->first_before, p);
    reply->has_first_before = true;

    index = next_index(&history_list, index);
    p = get_at_index(&history_list, index);
    fill_reply_from_snapshot(&reply->first_after, p);
    reply->has_first_after = true;

    index = best_index(&history_list, last);
    p = get_at_index(&history_list, index);
    fill_reply_from_snapshot(&reply->last_before, p);
    reply->has_last_before = true;

    index = next_index(&history_list, index);
    p = get_at_index(&history_list, index);
    fill_reply_from_snapshot(&reply->last_after, p);
    reply->has_last_after = true;

    history_release(&history_list);

    reply->has_request = true;
    reply->request.first = request->first;
    reply->request.last = request->last;
    reply->request.has_first = true;
    reply->request.has_last = true;
  }
}

void fastbin_reply_from_snapshot(fastbin_Rotary_Reply *reply, encoders_snapshot *pss) {
  reply->usec = pss->usec;
  reply->front_left_ticks = pss->front_left_ticks;
  reply->front_right_ticks = pss->front_right_ticks;
  reply->back_left_ticks = pss->back_left_ticks;
  reply->back_right_ticks = pss->back_right_ticks;
}

void GetFastbinSnapshot(fastbin_Rotary_Snapshot_Request *request, fastbin_Rotary_Snapshot_Reply *reply) {
  if (!booted) {
    return;
  }
  int index;
  uint64_t first = request->first_us;
  uint64_t last = request->last_us;
  encoders_snapshot *p;

  if (history_lock(&history_list) == pdTRUE) {
    index = best_index(&history_list, first);
    p = get_at_index(&history_list, index);
    fastbin_reply_from_snapshot(&reply->first_before, p);

    index = next_index(&history_list, index);
    p = get_at_index(&history_list, index);
    fastbin_reply_from_snapshot(&reply->first_after, p);

    index = best_index(&history_list, last);
    p = get_at_index(&history_list, index);
    fastbin_reply_from_snapshot(&reply->last_before, p);

    index = next_index(&history_list, index);
    p = get_at_index(&history_list, index);
    fastbin_reply_from_snapshot(&reply->last_after, p);

    history_release(&history_list);

    reply->request.first_us = request->first_us;
    reply->request.last_us = request->last_us;
  }
}

void get_latest_ticks(encoders_snapshot *pss) {
  if (history_lock(&history_list) == pdTRUE) {
    get_latest(&history_list, pss);
    history_release(&history_list);
  }
}

void GetRotaryEncoderTicks(rotary_encoder_RotaryEncoder_Reply *reply) {
  if (!booted) {
    return;
  }

  encoders_snapshot ss;
  get_latest_ticks(&ss);
  fill_reply_from_snapshot(reply, &ss);
}

void GetFastbinTicks(fastbin_Rotary_Reply *reply) {
  if (!booted) {
    return;
  }

  encoders_snapshot ss;
  get_latest_ticks(&ss);
  fastbin_reply_from_snapshot(reply, &ss);
}

void GetRotaryHistoryFailures(uint32_t *ptime_warps, uint32_t *pooo_elements, uint32_t *pmax_usec_distance,
                              uint32_t *pepoch_resets, uint32_t *ppps_plus1, uint32_t *ppps_signal_missed,
                              uint32_t *ppps_signal_reject, uint32_t *num_short_time, uint32_t *num_long_time) {
  *ppps_signal_missed = g_pps_signal_missed;
  g_pps_signal_missed = 0;
  *ppps_signal_reject = g_pps_signal_reject;
  g_pps_signal_reject = 0;
  *ppps_plus1 = g_pps_plus1_micros;
  g_pps_plus1_micros = 0;
  *pepoch_resets = g_pps_epoch_resets;
  g_pps_epoch_resets = 0;
  *ptime_warps = g_time_failures;
  g_time_failures = 0;

  *pmax_usec_distance = g_max_usec_distance;
  g_max_usec_distance = 0;

  *pooo_elements = 0;
  if (history_lock(&history_list) == pdTRUE) {
    history_list_verify(&history_list, pooo_elements, num_short_time, num_long_time);
    history_release(&history_list);
  }
}
/* [] END OF FILE */

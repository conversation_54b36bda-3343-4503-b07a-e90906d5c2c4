/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#ifndef __DRIVE_CONTROLLER__
#define __DRIVE_CONTROLLER__

#include "i2c_solenoid_controller.h"

#define FAN_IN_1 0
#define FAN_IN_2 1

#define GENERATOR_IN_1 3
#define GENERATOR_IN_2 2

#define SHIFT_IN_1 5
#define SHIFT_IN_2 4

#define BRAKE_IN_1 7
#define BRAKE_IN_2 6

#define DRIVE_FORWARD_OUTPUT 8
#define DRIVE_BACKWARD_OUTPUT 9

#define R_HELAC_CCW_OUTPUT 10
#define R_HELAC_CW_OUTPUT 11

#define L_HELAC_CCW_OUTPUT 12
#define L_HELAC_CW_OUTPUT 13

#define TXN_CTL_IN_1 14
#define TXN_CTL_IN_2 15

void Driver_Boot();

void drive_forward(uint16_t speed);
void drive_backward(uint16_t speed);
void stop_drive();

void turn_right_wheel_ccw(uint16_t speed);
void turn_left_wheel_ccw(uint16_t speed);

void turn_right_wheel_cw(uint16_t speed);
void turn_left_wheel_cw(uint16_t speed);

void point_straight();

void unidirectional_drive(uint8_t in1, uint8_t in2);
void brake();
void shift();
void fan();
void generator();
void traction_control();

#endif
/* [] END OF FILE */

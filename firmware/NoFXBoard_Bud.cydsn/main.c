/* ========================================
 *
 * Copyright Maka Autonomous Robotic Systems, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
 */
#include "FreeRTOS.h"
#include "drive_controller.h"
#include "ethernet_config.h"
#include "fastbin.h"
#include "freertos_setup.h"
#include "nano_time.h"
#include "pb_decode.h"
#include "pb_encode.h"
#include "project.h"
#include "rotary_encoder.h"
#include "sensors.h"
#include "spi_ops.h"
#include "stdbool.h"
#include "task.h"
#include "user_leds.h"

#include "generated/lib/drivers/nanopb/proto/nofx_board.pb.h"
#include "generated/lib/drivers/nanopb/proto/park_brake.pb.h"
#include "generated/lib/drivers/nanopb/proto/rotary_encoder.pb.h"

#define NOFX_VERSION_MAJOR 1  // Increase this for major breaking changes
#define NOFX_VERSION_MINOR 15 // Increase this for minor non-breaking changes

#define FAKE_CONFIG 0

void Handle_Ping(diagnostic_Ping *req, diagnostic_Pong *resp) { resp->x = req->x; }

void Handle_Version(version_Version_Reply *resp) {
  resp->major = NOFX_VERSION_MAJOR;
  resp->minor = NOFX_VERSION_MINOR;
}

void Xlate_Config(enum RE_TYPE *out, rotary_encoder_RotaryEncodersConfig_Request_Type val) {
  if (val == rotary_encoder_RotaryEncodersConfig_Request_Type_QUAD)
    *out = RE_QUAD;
  else
    *out = RE_NONE;
}

void Handle_Rotary_Config(rotary_encoder_RotaryEncodersConfig_Request *config) {
  enum RE_TYPE types[4];

  // Configure Rotary Encoders, same priority as udp read because rotaries never blocks
  const uint8_t encoder_task_priority = 1;

  // map from proto enumerations to C types
  Xlate_Config(&types[0], config->FL_type);
  Xlate_Config(&types[1], config->FR_type);
  Xlate_Config(&types[2], config->BL_type);
  Xlate_Config(&types[3], config->BR_type);

  RotaryEncoder_Boot(encoder_task_priority, types);
}

void Handle_Rotary_History_Verify(rotary_encoder_RotaryEncoderHistoryVerify_Reply *reply) {
  GetRotaryHistoryFailures(&reply->num_time_warps, &reply->num_ooo_elements, &reply->max_usec_distance,
                           &reply->num_epoch_resets, &reply->num_plus1_usec, &reply->num_missed_signal,
                           &reply->num_reject_signal, &reply->num_short_time, &reply->num_long_time);
}

void Handle_Rotary_Snapshot(rotary_encoder_RotaryEncoderSnapshot_Request *request,
                            rotary_encoder_RotaryEncoderSnapshot_Reply *reply) {
  GetRotaryEncoderSnapshot(request, reply);
}

void Handle_Rotary(rotary_encoder_RotaryEncoder_Reply *reply) { GetRotaryEncoderTicks(reply); }

void Handle_Drive(drive_solenoids_Drive_Request *req) {
  if (req->dir == drive_solenoids_Drive_Request_Direction_forward) {
    drive_forward((uint16_t)(MAX_DRIVE_OUTPUT * req->duty_cycle));
  } else if (req->dir == drive_solenoids_Drive_Request_Direction_backward) {
    drive_backward((uint16_t)(MAX_DRIVE_OUTPUT * req->duty_cycle));
  } else if (req->dir == drive_solenoids_Drive_Request_Direction_stop) {
    stop_drive();
  }
}

void Handle_Turn(drive_solenoids_Turn_Request *req) {
  if (req->dir == drive_solenoids_Turn_Request_Direction_right) {
    turn_right_wheel_cw((uint16_t)(MAX_DRIVE_OUTPUT * req->duty_cycle));
    turn_left_wheel_cw((uint16_t)(MAX_DRIVE_OUTPUT * req->duty_cycle));
  } else if (req->dir == drive_solenoids_Turn_Request_Direction_left) {
    turn_right_wheel_ccw((uint16_t)(MAX_DRIVE_OUTPUT * req->duty_cycle));
    turn_left_wheel_ccw((uint16_t)(MAX_DRIVE_OUTPUT * req->duty_cycle));
  } else if (req->dir == drive_solenoids_Turn_Request_Direction_straight) {
    point_straight();
  }
}

void Handle_Park_Brake(park_brake_Request *req) {
  if (req->onoff == true) {
    Park_Brake_Write(0);
  } else {
    Park_Brake_Write(1);
  }
}

void Handle_Park_Brake_Query(park_brake_Query_Reply *reply) { reply->onoff = !Park_Brake_Read(); }

void Handle_FuelGauge(float *value) { *value = Sensors_Fuel_Level(); }

void Handle_Time_Request(time_Request *request, time_Reply *reply) {
  M_Time_Request_t time_request;
  M_Time_Reply_t time_reply;
  Request_Metadata_t metadata;
  Nano_Convert_Time_Request(request, &time_request, &metadata);
  PPS_Time_Handle_Request(&time_request, &time_reply);
  Nano_Convert_Time_Reply(&time_reply, reply);
}

#define BUFFER_SIZE 1024

void UDP_Task(void *unused) {
  (void)unused;

  for (;;) {
    M_Ethernet_W5500_msg_t msg = M_Ethernet_W5500_UdpRead();

    // Decode request
    if (msg.local_port == FASTBIN_PORT) {
      uint8_t *pdata;
      int size;
      pdata = handle_fastbin_message(&msg, &size);
      M_Ethernet_W5500_Release_Msg_Data(&msg);
      if (size > 0) {
        msg.data = pdata;
        msg.count = size;
        M_Ethernet_W5500_UdpWrite(msg);
      }
      continue;
    }

    nofx_board_Request req = nofx_board_Request_init_zero;
    pb_istream_t stream = pb_istream_from_buffer(msg.data, msg.count);
    bool status = pb_decode(&stream, nofx_board_Request_fields, &req);
    M_Ethernet_W5500_Release_Msg_Data(&msg);
    if (!status) {
      // Failed to decode
      continue;
    }

    nofx_board_Reply resp = nofx_board_Reply_init_zero;
    resp.has_header = true;
    resp.header.requestId = req.header.requestId;
    switch (req.which_request) {
    case nofx_board_Request_ping_tag:
      resp.which_reply = nofx_board_Reply_pong_tag;
      Handle_Ping(&req.request.ping, &resp.reply.pong);
      break;
    case nofx_board_Request_rotary_encoder_tag:
      resp.which_reply = nofx_board_Reply_rotary_encoder_tag;
      switch (req.request.rotary_encoder.which_request) {
      case rotary_encoder_Request_rotary_tag:
        resp.reply.rotary_encoder.which_reply = rotary_encoder_Reply_rotary_tag;
        Handle_Rotary(&resp.reply.rotary_encoder.reply.rotary);
        break;
      case rotary_encoder_Request_config_tag:
        resp.reply.rotary_encoder.which_reply = rotary_encoder_Reply_config_tag;
        Handle_Rotary_Config(&req.request.rotary_encoder.request.config);
        break;
      case rotary_encoder_Request_rotary_snapshot_tag:
        resp.reply.rotary_encoder.which_reply = rotary_encoder_Reply_rotary_snapshot_tag;
        Handle_Rotary_Snapshot(&req.request.rotary_encoder.request.rotary_snapshot,
                               &resp.reply.rotary_encoder.reply.rotary_snapshot);
        break;
      case rotary_encoder_Request_history_verify_tag:
        resp.reply.rotary_encoder.which_reply = rotary_encoder_Reply_history_verify_tag;
        Handle_Rotary_History_Verify(&resp.reply.rotary_encoder.reply.history_verify);
        break;
      default:
        // unknown
        break;
      }
      break;
    case nofx_board_Request_park_brake_tag:
      resp.which_reply = nofx_board_Reply_park_brake_tag;
      Handle_Park_Brake(&req.request.park_brake);
      break;
    case nofx_board_Request_park_brake_query_tag:
      resp.which_reply = nofx_board_Reply_park_brake_query_tag;
      Handle_Park_Brake_Query(&resp.reply.park_brake_query);
      break;
    case nofx_board_Request_version_tag:
      resp.which_reply = nofx_board_Reply_version_tag;
      Handle_Version(&resp.reply.version);
      break;
    case nofx_board_Request_reset_tag:
      CySoftwareReset();
      break;
    case nofx_board_Request_sensors_tag:
      resp.which_reply = nofx_board_Reply_sensors_tag;
      switch (req.request.sensors.which_request) {
      case sensors_Request_fuel_gauge_tag:
        resp.reply.sensors.which_reply = sensors_Reply_fuel_gauge_tag;
        Handle_FuelGauge(&resp.reply.sensors.reply.fuel_gauge.value);
        break;
      }
      break;
    case nofx_board_Request_drive_solenoids_tag:
      resp.which_reply = nofx_board_Reply_drive_solenoids_tag;
      switch (req.request.drive_solenoids.which_request) {
      case drive_solenoids_Request_drive_tag:
        resp.reply.drive_solenoids.which_reply = drive_solenoids_Reply_drive_tag;
        Handle_Drive(&req.request.drive_solenoids.request.drive);
        break;
      case drive_solenoids_Request_turn_tag:
        resp.reply.drive_solenoids.which_reply = drive_solenoids_Reply_turn_tag;
        Handle_Turn(&req.request.drive_solenoids.request.turn);
        break;
      }
    case nofx_board_Request_time_tag:
      resp.which_reply = nofx_board_Reply_time_tag;
      Handle_Time_Request(&req.request.time, &resp.reply.time);
      break;
    default:
      // unknown
      break;
    }

    // Encode response.
    uint8_t buffer[BUFFER_SIZE];
    {
      pb_ostream_t stream = pb_ostream_from_buffer(buffer, sizeof(buffer));
      bool status = pb_encode(&stream, nofx_board_Reply_fields, &resp);
      configASSERT(status); // encoding should not fail
      msg.data = buffer;
      msg.count = stream.bytes_written;
    }
    M_Ethernet_W5500_UdpWrite(msg);
  }
}

void Boot_Task(void *unused) {
  (void)unused;

  // Configure Ethernet.
  uint8_t mac[] = BOARD_MAC(NOFX_IP_INDEX, NOFX_IP_SUB_INDEX);
  uint8_t ip[] = BOARD_IP(NOFX_IP_INDEX, NOFX_IP_SUB_INDEX);
  uint8_t netmask[] = BOARD_NETMASK;
  uint8_t gateway[] = BOARD_GATEWAY;
  size_t read_queue_len = 100;
  M_Ethernet_W5500_Boot(mac, ip, netmask, gateway, read_queue_len);
  M_Ethernet_W5500_UdpOpen(BASE_PORT);
  M_Ethernet_W5500_UdpOpen(FASTBIN_PORT);

  PPS_Time_Boot_Component();
  I2C_Start();
  SPIBoot();
  User_Leds_Boot_Lights();
  Driver_Boot();
  Sensors_Boot();

  // Schedule UDP task.
  xTaskCreate(UDP_Task, "UDP", configMINIMAL_STACK_SIZE + 2048 + 1024, NULL, 1, NULL);

#if FAKE_CONFIG
  rotary_encoder_RotaryEncodersConfig_Request conf;
  conf.FL_type = rotary_encoder_RotaryEncodersConfig_Request_Type_QUAD;
  conf.FR_type = rotary_encoder_RotaryEncodersConfig_Request_Type_QUAD;
  conf.BL_type = rotary_encoder_RotaryEncodersConfig_Request_Type_NONE;
  conf.BR_type = rotary_encoder_RotaryEncodersConfig_Request_Type_NONE;
  Handle_Rotary_Config(&conf);
#endif

  // Finish boot sequence.
  vTaskDelete(NULL);
}

int main(void) {
  // Enable global interrupts.
  CyGlobalIntEnable;

  // Install FreeRTOS interrupts.
  setup_FreeRTOS_interrupts();

  // Schedule boot sequence.
  xTaskCreate(Boot_Task, "Boot", configMINIMAL_STACK_SIZE, NULL, 1, NULL);

  // Start the scheduler.
  vTaskStartScheduler();
}

/* [] END OF FILE */

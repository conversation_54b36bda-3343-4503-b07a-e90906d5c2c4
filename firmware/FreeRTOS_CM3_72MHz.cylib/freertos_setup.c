/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
 */

#include "stdint.h"

extern void xPortPendSVHandler(void);
extern void xPortSysTickHandler(void);
extern void vPortSVCHandler(void);
typedef void (*cyisraddress)(void);
extern cyisraddress CyRamVectors[];

void setup_FreeRTOS_interrupts() {
  /* Install the OS Interrupt Handlers. */
  CyRamVectors[11] = (cyisraddress)vPortSVCHandler;
  CyRamVectors[14] = (cyisraddress)xPortPendSVHandler;
  CyRamVectors[15] = (cyisraddress)xPortSysTickHandler;
}

/* [] END OF FILE */

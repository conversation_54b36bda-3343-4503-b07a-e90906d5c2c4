/* ========================================
 *
 * Copyright Maka Autonomous Robotic Systems, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "FreeRTOS.h"
#include "`$INSTANCE_NAME`_M_Nano_USB.h"
#include "`$INSTANCE_NAME``[USB]`M_USB.h"
#include "task.h"

bool `$INSTANCE_NAME`_Send_Reply(size_t size, const pb_msgdesc_t *fields, const void *reply) {
  `$INSTANCE_NAME``[USB]`Msg_t msg;
  pb_ostream_t ostream;
  bool status;

  msg = `$INSTANCE_NAME``[USB]`Create_Msg(size);
  ostream = pb_ostream_from_buffer(msg.data, msg.size);
  status = pb_encode(&ostream, fields, reply);

  if (!status || ostream.bytes_written == 0) {
    `$INSTANCE_NAME``[USB]`Delete_Msg(msg);
    return false;
  }

  msg.size = ostream.bytes_written;
  `$INSTANCE_NAME``[USB]`Push_To_Send_Queue(&msg);
  return true;
}

bool `$INSTANCE_NAME`_Receive_Request(const pb_msgdesc_t *fields, void *request) {
  static `$INSTANCE_NAME``[USB]`Msg_t msg;
  static pb_istream_t istream;
  static bool status;

  msg = `$INSTANCE_NAME``[USB]`Pop_From_Read_Queue();
  istream = pb_istream_from_buffer(msg.data, msg.size);
  status = pb_decode(&istream, fields, request);
  `$INSTANCE_NAME``[USB]`Delete_Msg(msg);
  return status;
}

inline bool `$INSTANCE_NAME`_Send_`$ReplyTypeName`(`$ReplyTypeName` *reply) {
  return `$INSTANCE_NAME`_Send_Reply(`$ReplyTypeName`_size, `$ReplyTypeName`_fields, reply);
}

void `$INSTANCE_NAME`_Handler() {
  bool status;
  `$RequestTypeName` request = `$RequestTypeName`_init_zero;
  status = `$INSTANCE_NAME`_Receive_Request(`$RequestTypeName`_fields, &request);

  if (status && request.has_header) {
    `$INSTANCE_NAME`_Dispatch(&request);
  }

#if PB_ENABLE_MALLOC
  pb_release(`$RequestTypeName`_fields, &request);
#endif
}

void `$INSTANCE_NAME`_Dispatcher_Task(void *unused) {
  (void)unused;

  for (;;) {
    `$INSTANCE_NAME`_Handler();
  }
}

void `$INSTANCE_NAME`_boot() {
  `$INSTANCE_NAME``[USB]`boot();
  xTaskCreate(`$INSTANCE_NAME`_Dispatcher_Task, "NANO",
              configMINIMAL_STACK_SIZE + (sizeof(`$RequestTypeName`) + sizeof(`$ReplyTypeName`)) * 4, 0, 1, 0);
}

/* [] END OF FILE */

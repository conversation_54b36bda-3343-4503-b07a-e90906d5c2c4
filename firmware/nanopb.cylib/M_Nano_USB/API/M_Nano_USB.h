/* ========================================
 *
 * Copyright Maka Autonomous Robotic Systems, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */
#ifndef `$INSTANCE_NAME`_H
#define `$INSTANCE_NAME`_H

#include "`$ProtoHeaderPath`"
#include "pb_decode.h"
#include "pb_encode.h"
#include "stdbool.h"
#include "stdlib.h"

bool `$INSTANCE_NAME`_Send_`$ReplyTypeName`(`$ReplyTypeName` *reply);
void `$INSTANCE_NAME`_boot();

// Callback must be Implemented by User
void `$INSTANCE_NAME`_Dispatch(`$RequestTypeName` *request);

// Compiler Flag Definition Required: PB_ENABLE_MALLOC

#endif

/* [] END OF FILE */

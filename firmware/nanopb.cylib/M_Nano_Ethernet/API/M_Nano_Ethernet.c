/* ========================================
 *
 * Copyright Maka Autonomous Robotic Systems, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "FreeRTOS.h"
#include "`$INSTANCE_NAME`_Eth_M_Ethernet_W5500.h"
#include "`$INSTANCE_NAME`_M_Nano_Ethernet.h"
#include "ethernet_config.h"
#include "project.h"
#include "queue.h"
#include "request_metadata.h"
#include "stdbool.h"
#include "task.h"
#include "timers.h"

#define QUEUE_LENGTH 8

QueueHandle_t `$INSTANCE_NAME`_send_msg_queue;

inline void `$INSTANCE_NAME`_Push_To_Send_Queue(`$INSTANCE_NAME`_Eth_msg_t *msg) {
  xQueueSendToBack(`$INSTANCE_NAME`_send_msg_queue, msg, portMAX_DELAY);
}

inline `$INSTANCE_NAME`_Eth_msg_t `$INSTANCE_NAME`_Pop_From_Send_Queue() {
  `$INSTANCE_NAME`_Eth_msg_t msg;
  xQueueReceive(`$INSTANCE_NAME`_send_msg_queue, &msg, portMAX_DELAY);
  return msg;
}

M_Error_Code_t `$INSTANCE_NAME`_Send_Reply(Request_Metadata_Ethernet_t *metadata, size_t size,
                                           const pb_msgdesc_t *fields, const void *reply) {
  `$INSTANCE_NAME`_Eth_msg_t msg;
  pb_ostream_t ostream;
  bool status;
  M_Error_Code_t ret = M_ERROR_CODE_OK;
  uint8_t *buffer = pvPortMalloc(size);

  ostream = pb_ostream_from_buffer(buffer, size);
  status = pb_encode(&ostream, fields, reply);

  if (status && ostream.bytes_written != 0) {
    memcpy(msg.ip, metadata->ip, 4);
    msg.port = metadata->port;
    msg.local_port = metadata->local_port;
    msg.data = buffer;
    msg.count = ostream.bytes_written;
    `$INSTANCE_NAME`_Push_To_Send_Queue(&msg);
  } else {
    ret = M_ERROR_CODE_ETH_NANOPB_ENCODE_FAIL;
    vPortFree(buffer);
  }

  return ret;
}

M_Error_Code_t `$INSTANCE_NAME`_Receive_Request(const pb_msgdesc_t *fields, void *request,
                                                Request_Metadata_Ethernet_t *metadata) {
  static `$INSTANCE_NAME`_Eth_msg_t msg;
  static pb_istream_t istream;
  static bool status;
  M_Error_Code_t ret = M_ERROR_CODE_OK;

  msg = `$INSTANCE_NAME`_Eth_UdpRead();
  istream = pb_istream_from_buffer(msg.data, msg.count);
  status = pb_decode(&istream, fields, request);
  `$INSTANCE_NAME`_Eth_Release_Msg_Data(&msg);

  if (!status) {
    ret = M_ERROR_CODE_ETH_NANOPB_DECODE_FAIL;
  }
  memcpy(metadata->ip, msg.ip, 4);
  metadata->port = msg.port;
  metadata->local_port = msg.local_port;
  return ret;
}

inline M_Error_Code_t `$INSTANCE_NAME`_Send_`$ReplyTypeName`(
    Request_Metadata_Ethernet_t *metadata, `$ReplyTypeName` *reply) {
  return `$INSTANCE_NAME`_Send_Reply(metadata, `$ReplyTypeName`_size, `$ReplyTypeName`_fields, reply);
}

void `$INSTANCE_NAME`_Handler() {
  `$RequestTypeName` request = `$RequestTypeName`_init_zero;
  Request_Metadata_Ethernet_t metadata;
  M_Error_Code_t ret = `$INSTANCE_NAME`_Receive_Request(`$RequestTypeName`_fields, &request, &metadata);

  if (ret == M_ERROR_CODE_OK && request.has_header) {

    `$INSTANCE_NAME`_Dispatch(&metadata, &request);
  }

#if PB_ENABLE_MALLOC
  pb_release(`$RequestTypeName`_fields, &request);
#endif
}

void `$INSTANCE_NAME`_Send_Task(void *unused) {
  (void)unused;
  `$INSTANCE_NAME`_Eth_msg_t msg;

  for (;;) {
    msg = `$INSTANCE_NAME`_Pop_From_Send_Queue();
    `$INSTANCE_NAME`_Eth_UdpWrite(msg);
    `$INSTANCE_NAME`_Eth_Release_Msg_Data(&msg);
  }
}

void `$INSTANCE_NAME`_Dispatcher_Task(void *unused) {
  (void)unused;

  for (;;) {
    `$INSTANCE_NAME`_Handler();
  }
}

void `$INSTANCE_NAME`_boot(uint8_t index, uint8_t subindex) {
  // Configure Ethernet.
  uint8_t mac[] = BOARD_MAC(index, subindex);
  uint8_t ip[] = BOARD_IP(index, subindex);
  uint8_t netmask[] = BOARD_NETMASK;
  uint8_t gateway[] = BOARD_GATEWAY;
  size_t read_queue_len = 10;
  `$INSTANCE_NAME`_Eth_Boot(mac, ip, netmask, gateway, read_queue_len);
  `$INSTANCE_NAME`_Eth_UdpOpen(BASE_PORT);

  `$INSTANCE_NAME`_send_msg_queue = xQueueCreate(QUEUE_LENGTH, sizeof(`$INSTANCE_NAME`_Eth_msg_t));

  xTaskCreate(`$INSTANCE_NAME`_Send_Task, "NANO-S", configMINIMAL_STACK_SIZE + 512, 0, 1, 0);
  xTaskCreate(`$INSTANCE_NAME`_Dispatcher_Task, "NANO-D",
              configMINIMAL_STACK_SIZE + 512 + (sizeof(`$RequestTypeName`) + sizeof(`$ReplyTypeName`)) * 4, 0, 1, 0);
}

/* [] END OF FILE */

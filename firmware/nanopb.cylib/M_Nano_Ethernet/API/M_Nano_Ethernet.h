/* ========================================
 *
 * Copyright Maka Autonomous Robotic Systems, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */
#ifndef `$INSTANCE_NAME`_H
#define `$INSTANCE_NAME`_H

#include "`$ProtoHeaderPath`"
#include "m_error_codes.h"
#include "pb_decode.h"
#include "pb_encode.h"
#include "request_metadata.h"
#include "stdbool.h"
#include "stdlib.h"

M_Error_Code_t `$INSTANCE_NAME`_Send_`$ReplyTypeName`(Request_Metadata_Ethernet_t *metadata, `$ReplyTypeName` *reply);
void `$INSTANCE_NAME`_boot(uint8_t index, uint8_t subindex);

// Callback must be Implemented by User
void `$INSTANCE_NAME`_Dispatch(Request_Metadata_Ethernet_t *metadata, `$RequestTypeName` *request);

// Compiler Flag Definition Required: PB_ENABLE_MALLOC

#endif

/* [] END OF FILE */

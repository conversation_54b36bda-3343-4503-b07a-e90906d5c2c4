/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "FreeRTOS.h"
#include "freertos_setup.h"
#include "generated/lib/drivers/nanopb/proto/experiment_board.pb.h"
#include "m_row_module_request.h"
#include "maka_endian.h"
#include "nano_dawg.h"
#include "nano_row_module.h"
#include "nano_scanner.h"
#include "project.h"
#include "queue.h"
#include "stdbool.h"
#include "stdlib.h"
#include "task.h"

void Handle_Ping(experiment_board_ExperimentRequest *request) {
  experiment_board_ExperimentReply reply = experiment_board_ExperimentReply_init_zero;
  reply.has_header = true;
  reply.header.requestId = request->header.requestId;
  reply.which_reply = experiment_board_ExperimentReply_pong_tag;
  reply.reply.pong.x = request->request.ping.x;

  configASSERT(Nano_Send_experiment_board_ExperimentReply(&reply));
}

void Handle_Items(experiment_board_ExperimentRequest *request) {
  experiment_board_ExperimentReply reply = experiment_board_ExperimentReply_init_zero;
  reply.has_header = true;
  reply.header.requestId = request->header.requestId;
  reply.which_reply = experiment_board_ExperimentReply_result_tag;
  reply.reply.result.sum = 0;

  for (int i = 0; i < request->request.items.items_count; i++) {
    reply.reply.result.sum += request->request.items.items[i].x;
  }

  configASSERT(Nano_Send_experiment_board_ExperimentReply(&reply));
}

void Row_Module_Reset() { CySoftwareReset(); }

void Row_Module_Clear_Config(M_Row_Module_Clear_Config_Request_t *request) {
  EPOS_MCAN_Clear_Node_Configuration();
  experiment_board_ExperimentReply reply = experiment_board_ExperimentReply_init_zero;
  reply.has_header = true;
  reply.header.requestId = request->request_id;
  reply.which_reply = experiment_board_ExperimentReply_row_module_tag;
  reply.reply.row_module.which_reply = row_module_Reply_ack_tag;

  configASSERT(Nano_Send_experiment_board_ExperimentReply(&reply));
}

void Row_Module_Handle_Scanner_Request(M_Row_Module_Scanner_Request_t *request) {
  switch (request->scanner_id) {
  case 1:
    configASSERT(Scanner_1_Send_Request(&request->scanner));
    break;
  default:
    break;
  }
}

void Row_Module_Handle_Request(M_Row_Module_Request_t *request) {
  switch (request->request_type) {
  case ROW_MODULE_REQUEST_TYPE_RESET:
    Row_Module_Reset();
    break;
  case ROW_MODULE_REQUEST_TYPE_CLEAR_CONFIG:
    Row_Module_Clear_Config(&request->request.clear);
    break;
  case ROW_MODULE_REQUEST_TYPE_DAWG:
    Dawg_Send_Request(&request->request.dawg);
    break;
  case ROW_MODULE_REQUEST_TYPE_SCANNER:
    Row_Module_Handle_Scanner_Request(&request->request.scanner);
    break;
  default:
    break;
  }
}

M_Row_Module_Request_t row_module_request;

void Nano_Dispatch(experiment_board_ExperimentRequest *request) {
  switch (request->which_request) {
  case experiment_board_ExperimentRequest_ping_tag:
    Handle_Ping(request);
    break;
  case experiment_board_ExperimentRequest_items_tag:
    Handle_Items(request);
    break;
  case experiment_board_ExperimentRequest_row_module_tag:
    Nano_Convert_Row_Module_Request(&request->request.row_module, &row_module_request, request->header.requestId);
    Row_Module_Handle_Request(&row_module_request);
    break;
  default:
    break;
  }
}

void Send_Scanner_Reply(M_Scanner_Reply_t *reply) {
  experiment_board_ExperimentReply nano_reply = experiment_board_ExperimentReply_init_zero;
  nano_reply.has_header = true;
  nano_reply.header.requestId = reply->request_id;
  nano_reply.which_reply = experiment_board_ExperimentReply_row_module_tag;
  nano_reply.reply.row_module.which_reply = row_module_Reply_scanner_tag;
  Nano_Convert_Scanner_Reply(reply, &nano_reply.reply.row_module.reply.scanner);
  Nano_Send_experiment_board_ExperimentReply(&nano_reply);
  configASSERT(reply->reply_type != SCANNER_REPLY_TYPE_ERROR);
}

void Send_Dawg_Reply(M_Dawg_Reply_t *reply) {
  experiment_board_ExperimentReply nano_reply = experiment_board_ExperimentReply_init_zero;
  nano_reply.has_header = true;
  nano_reply.header.requestId = reply->request_id;
  nano_reply.which_reply = experiment_board_ExperimentReply_row_module_tag;
  nano_reply.reply.row_module.which_reply = row_module_Reply_dawg_tag;
  Nano_Convert_Dawg_Reply(reply, &nano_reply.reply.row_module.reply.dawg);
  Nano_Send_experiment_board_ExperimentReply(&nano_reply);
}

void Main_Function() {
  EPOS_MCAN_Clear_Node_Configuration();
  row_module_Request nano_request;
  M_Row_Module_Request_t request;

  nano_request.which_request = row_module_Request_dawg_tag;
  nano_request.request.dawg.which_request = dawg_Request_config_tag;
  nano_request.request.dawg.request.config.timeout_ms = 1000;
  Nano_Convert_Row_Module_Request(&nano_request, &request, 0);
  Row_Module_Handle_Request(&request);

  nano_request.request.dawg.which_request = dawg_Request_pet_tag;
  Nano_Convert_Row_Module_Request(&nano_request, &request, 0);
  Row_Module_Handle_Request(&request);

  nano_request.request.dawg.which_request = dawg_Request_arm_tag;
  nano_request.request.dawg.request.arm.armed = true;
  Nano_Convert_Row_Module_Request(&nano_request, &request, 0);
  Row_Module_Handle_Request(&request);

  nano_request.which_request = row_module_Request_scanner_tag;
  nano_request.request.scanner.has_request = true;
  nano_request.request.scanner.scanner_id = 1;
  nano_request.request.scanner.request.which_request = scanner_Request_laser_tag;
  nano_request.request.scanner.request.request.laser.on = true;
  Nano_Convert_Row_Module_Request(&nano_request, &request, 0);
  Row_Module_Handle_Request(&request);

  vTaskDelay(2000);

  nano_request.which_request = row_module_Request_dawg_tag;
  nano_request.request.dawg.which_request = dawg_Request_pet_tag;
  Nano_Convert_Row_Module_Request(&nano_request, &request, 0);
  Row_Module_Handle_Request(&request);

  vTaskDelay(500);

  nano_request.request.dawg.which_request = dawg_Request_arm_tag;
  nano_request.request.dawg.request.arm.armed = false;
  Nano_Convert_Row_Module_Request(&nano_request, &request, 0);
  Row_Module_Handle_Request(&request);

  // Configure
  nano_request.which_request = row_module_Request_scanner_tag;
  nano_request.request.scanner.has_request = true;
  nano_request.request.scanner.scanner_id = 1;
  nano_request.request.scanner.request.which_request = scanner_Request_gimbal_tag;
  nano_request.request.scanner.request.request.gimbal.has_pan = true;
  nano_request.request.scanner.request.request.gimbal.pan.which_request = servo_Request_config_tag;
  nano_request.request.scanner.request.request.gimbal.pan.request.config.node_id = 1;
  nano_request.request.scanner.request.request.gimbal.pan.request.config.has_config = true;
  nano_request.request.scanner.request.request.gimbal.pan.request.config.config.max_profile_velocity =
      EPOS_DEFAULT_VELOCITY_MRPM;
  nano_request.request.scanner.request.request.gimbal.pan.request.config.config.settle_timeout =
      EPOS_DEFAULT_SETTLE_TIMEOUT_MS;
  nano_request.request.scanner.request.request.gimbal.pan.request.config.config.settle_window =
      EPOS_DEFAULT_SETTLE_WINDOW;
  nano_request.request.scanner.request.request.gimbal.has_tilt = true;
  nano_request.request.scanner.request.request.gimbal.tilt.which_request = servo_Request_config_tag;
  nano_request.request.scanner.request.request.gimbal.tilt.request.config.node_id = 2;
  nano_request.request.scanner.request.request.gimbal.tilt.request.config.has_config = true;
  nano_request.request.scanner.request.request.gimbal.tilt.request.config.config.max_profile_velocity =
      EPOS_DEFAULT_VELOCITY_MRPM;
  nano_request.request.scanner.request.request.gimbal.tilt.request.config.config.settle_timeout =
      EPOS_DEFAULT_SETTLE_TIMEOUT_MS;
  nano_request.request.scanner.request.request.gimbal.tilt.request.config.config.settle_window =
      EPOS_DEFAULT_SETTLE_WINDOW;
  Nano_Convert_Row_Module_Request(&nano_request, &request, 0);
  Row_Module_Handle_Request(&request);

  // Boot
  nano_request.request.scanner.request.which_request = scanner_Request_boot_tag;
  nano_request.request.scanner.request.request.boot.has_pan_params = true;
  nano_request.request.scanner.request.request.boot.pan_params.max_position = EPOS_DEFAULT_HOMING_MAX;
  nano_request.request.scanner.request.request.boot.pan_params.min_position = EPOS_DEFAULT_HOMING_MIN;
  nano_request.request.scanner.request.request.boot.pan_params.profile_velocity = EPOS_DEFAULT_VELOCITY_MRPM;
  nano_request.request.scanner.request.request.boot.pan_params.which_params = epos_Home_Params_hard_stop_tag;
  nano_request.request.scanner.request.request.boot.pan_params.params.hard_stop.offset = 1000;
  nano_request.request.scanner.request.request.boot.pan_params.params.hard_stop.step_size = 1000;
  nano_request.request.scanner.request.request.boot.has_tilt_params = true;
  nano_request.request.scanner.request.request.boot.tilt_params.max_position = EPOS_DEFAULT_HOMING_MAX;
  nano_request.request.scanner.request.request.boot.tilt_params.min_position = EPOS_DEFAULT_HOMING_MIN;
  nano_request.request.scanner.request.request.boot.tilt_params.profile_velocity = EPOS_DEFAULT_VELOCITY_MRPM;
  nano_request.request.scanner.request.request.boot.tilt_params.which_params = epos_Home_Params_hard_stop_tag;
  nano_request.request.scanner.request.request.boot.tilt_params.params.hard_stop.offset = 1000;
  nano_request.request.scanner.request.request.boot.tilt_params.params.hard_stop.step_size = 1000;
  Nano_Convert_Row_Module_Request(&nano_request, &request, 0);
  Row_Module_Handle_Request(&request);

  // Do Things
  nano_request.request.scanner.request.which_request = scanner_Request_gimbal_tag;
  nano_request.request.scanner.request.request.gimbal.has_pan = true;
  nano_request.request.scanner.request.request.gimbal.pan.which_request = servo_Request_go_to_tag;
  nano_request.request.scanner.request.request.gimbal.pan.request.go_to.position = 5000;
  nano_request.request.scanner.request.request.gimbal.pan.request.go_to.velocity = EPOS_DEFAULT_VELOCITY_MRPM;
  nano_request.request.scanner.request.request.gimbal.pan.request.go_to.await_settle = true;
  nano_request.request.scanner.request.request.gimbal.has_tilt = true;
  nano_request.request.scanner.request.request.gimbal.tilt.which_request = servo_Request_go_to_tag;
  nano_request.request.scanner.request.request.gimbal.tilt.request.go_to.position = 5000;
  nano_request.request.scanner.request.request.gimbal.tilt.request.go_to.velocity = EPOS_DEFAULT_VELOCITY_MRPM;
  nano_request.request.scanner.request.request.gimbal.tilt.request.go_to.await_settle = true;
  Nano_Convert_Row_Module_Request(&nano_request, &request, 0);
  Row_Module_Handle_Request(&request);

  // Stop
  nano_request.request.scanner.request.which_request = scanner_Request_stop_tag;
  Nano_Convert_Row_Module_Request(&nano_request, &request, 0);
  Row_Module_Handle_Request(&request);

  // Optional Reset
  nano_request.which_request = row_module_Request_reset_tag;
  Nano_Convert_Row_Module_Request(&nano_request, &request, 0);
  // Row_Module_Handle_Request(&request);
}

void Boot_Task(void *unused) {
  (void)unused;

  // Startup
  Nano_boot();
  EPOS_boot(1);

  EPOS_MCAN_Clear_Node_Configuration();

  Scanner_1_Boot_Component(Send_Scanner_Reply, 1);
  Dawg_Boot_Component(Send_Dawg_Reply, 1);

  for (int i = 0; i < 1; i++) {
    Main_Function();
    vTaskDelay(6000);
  }

  // Finish boot sequence.
  vTaskDelete(NULL);
}

int main(void) {
  // Enable global interrupts.
  CyGlobalIntEnable;

  // Install FreeRTOS interrupts.
  setup_FreeRTOS_interrupts();

  // Schedule boot sequence.
  xTaskCreate(Boot_Task, "Boot", configMINIMAL_STACK_SIZE + 400, 0, 1, 0);

  // Start the scheduler.
  vTaskStartScheduler();
}

/* [] END OF FILE */

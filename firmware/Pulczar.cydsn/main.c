/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */
#include "FreeRTOS.h"
#include "ethernet_config.h"
#include "freertos_setup.h"
#include "m_pulczar_request.h"
#include "m_time_request.h"
#include "nano_dawg.h"
#include "nano_gimbal.h"
#include "nano_laser.h"
#include "nano_lens.h"
#include "nano_pulczar.h"
#include "nano_time.h"
#include "project.h"
#include "queue.h"
#include "semphr.h"
#include "stdbool.h"
#include "stdlib.h"
#include "task.h"

#define PULCZAR_VERSION_MAJOR 1  // Increase this for major breaking changes
#define PULCZAR_VERSION_MINOR 22 // Increase this for minor non-breaking changes

M_Error_Code_t Handle_Ping(Request_Metadata_t *metadata, diagnostic_Ping *request) {
  if (metadata->request_type != REQUEST_METADATA_TYPE_ETHERNET) {
    return M_ERROR_CODE_INVALID_METADATA;
  }

  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = metadata->request_id;
  reply.which_reply = pulczar_board_Reply_pong_tag;
  reply.reply.pong.x = request->x;
  return Ethernet_Send_pulczar_board_Reply(&metadata->metadata.eth, &reply);
}

M_Error_Code_t Handle_Version(Request_Metadata_t *metadata) {
  if (metadata->request_type != REQUEST_METADATA_TYPE_ETHERNET) {
    return M_ERROR_CODE_INVALID_METADATA;
  }

  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = metadata->request_id;
  reply.which_reply = pulczar_board_Reply_version_tag;
  reply.reply.version.major = PULCZAR_VERSION_MAJOR;
  reply.reply.version.minor = PULCZAR_VERSION_MINOR;
  return Ethernet_Send_pulczar_board_Reply(&metadata->metadata.eth, &reply);
}

void Pulczar_Reset() {
  // This resets the software, so we cannot reply
  CySoftwareReset();
}

M_Error_Code_t Pulczar_Clear_Config(Request_Metadata_t *metadata) {
  if (metadata->request_type != REQUEST_METADATA_TYPE_ETHERNET) {
    return M_ERROR_CODE_INVALID_METADATA;
  }

  EPOS_MCAN_Clear_Node_Configuration();
  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = metadata->request_id;
  reply.which_reply = pulczar_board_Reply_pulczar_tag;
  reply.reply.pulczar.which_reply = pulczar_Reply_ack_tag;
  return Ethernet_Send_pulczar_board_Reply(&metadata->metadata.eth, &reply);
}

M_Error_Code_t Pulczar_Status(Request_Metadata_t *metadata) {
  if (metadata->request_type != REQUEST_METADATA_TYPE_ETHERNET) {
    return M_ERROR_CODE_INVALID_METADATA;
  }

  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = metadata->request_id;
  reply.which_reply = pulczar_board_Reply_pulczar_tag;
  reply.reply.pulczar.which_reply = pulczar_Reply_status_tag;
  reply.reply.pulczar.reply.status.status = Status_Read();
  return Ethernet_Send_pulczar_board_Reply(&metadata->metadata.eth, &reply);
}

M_Error_Code_t Pulczar_Override(M_Pulczar_Override_Request_t *request) {
  if (request->metadata.request_type != REQUEST_METADATA_TYPE_ETHERNET) {
    return M_ERROR_CODE_INVALID_METADATA;
  }

  Override_Reg_Write(~request->override);

  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = request->metadata.request_id;
  reply.which_reply = pulczar_board_Reply_pulczar_tag;
  reply.reply.pulczar.which_reply = pulczar_Reply_ack_tag;
  return Ethernet_Send_pulczar_board_Reply(&request->metadata.metadata.eth, &reply);
}

void Pulczar_Handle_Request(M_Pulczar_Request_t *request) {
  switch (request->request_type) {
  case PULCZAR_REQUEST_TYPE_RESET:
    Pulczar_Reset();
    break;
  case PULCZAR_REQUEST_TYPE_CLEAR_CONFIG:
    Pulczar_Clear_Config(&request->request.metadata);
    break;
  case PULCZAR_REQUEST_TYPE_STATUS:
    Pulczar_Status(&request->request.metadata);
    break;
  case PULCZAR_REQUEST_TYPE_DAWG:
    Dawg_Send_Request(&request->request.dawg);
    break;
  case PULCZAR_REQUEST_TYPE_LASER:
    Laser_Send_Request(&request->request.laser);
    break;
  case PULCZAR_REQUEST_TYPE_LENS:
    Lens_Send_Request(&request->request.lens);
    break;
  case PULCZAR_REQUEST_TYPE_GIMBAL:
    Gimbal_Send_Request(&request->request.gimbal);
    break;
  case PULCZAR_REQUEST_TYPE_OVERRIDE:
    Pulczar_Override(&request->request.override);
    break;
  default:
    break;
  }
}

M_Error_Code_t Send_Time_Reply(M_Time_Reply_t *reply) {
  if (reply->metadata.request_type != REQUEST_METADATA_TYPE_ETHERNET) {
    return M_ERROR_CODE_INVALID_METADATA;
  }

  pulczar_board_Reply nano_reply = pulczar_board_Reply_init_zero;
  nano_reply.has_header = true;
  nano_reply.header.requestId = reply->metadata.request_id;
  nano_reply.which_reply = pulczar_board_Reply_time_tag;
  Nano_Convert_Time_Reply(reply, &nano_reply.reply.time);
  return Ethernet_Send_pulczar_board_Reply(&reply->metadata.metadata.eth, &nano_reply);
}

void Ethernet_Dispatch(Request_Metadata_Ethernet_t *eth_metadata, pulczar_board_Request *request) {
  M_Pulczar_Request_t pulczar_request;
  Request_Metadata_t metadata;
  metadata.request_id = request->header.requestId;
  metadata.request_type = REQUEST_METADATA_TYPE_ETHERNET;
  metadata.metadata.eth = *eth_metadata;
  switch (request->which_request) {
  case pulczar_board_Request_ping_tag:
    Handle_Ping(&metadata, &request->request.ping);
    break;
  case pulczar_board_Request_version_tag:
    Handle_Version(&metadata);
    break;
  case pulczar_board_Request_pulczar_tag:
    Nano_Convert_Pulczar_Request(&request->request.pulczar, &pulczar_request, &metadata);
    Pulczar_Handle_Request(&pulczar_request);
    break;
  case pulczar_board_Request_time_tag: {
    M_Time_Request_t time_request;
    M_Time_Reply_t time_reply;
    Nano_Convert_Time_Request(&request->request.time, &time_request, &metadata);
    PPS_Time_Handle_Request(&time_request, &time_reply);
    Send_Time_Reply(&time_reply);
    break;
  }
  default:
    break;
  }
}

M_Error_Code_t Send_Gimbal_Reply(M_Gimbal_Reply_t *reply) {
  if (reply->metadata.request_type != REQUEST_METADATA_TYPE_ETHERNET) {
    return M_ERROR_CODE_INVALID_METADATA;
  }

  pulczar_board_Reply nano_reply = pulczar_board_Reply_init_zero;
  nano_reply.has_header = true;
  nano_reply.header.requestId = reply->metadata.request_id;
  nano_reply.which_reply = pulczar_board_Reply_pulczar_tag;
  nano_reply.reply.pulczar.which_reply = pulczar_Reply_gimbal_tag;
  Nano_Convert_Gimbal_Reply(reply, &nano_reply.reply.pulczar.reply.gimbal);
  return Ethernet_Send_pulczar_board_Reply(&reply->metadata.metadata.eth, &nano_reply);
}

M_Error_Code_t Send_Dawg_Reply(M_Dawg_Reply_t *reply) {
  if (reply->metadata.request_type != REQUEST_METADATA_TYPE_ETHERNET) {
    return M_ERROR_CODE_INVALID_METADATA;
  }

  pulczar_board_Reply nano_reply = pulczar_board_Reply_init_zero;
  nano_reply.has_header = true;
  nano_reply.header.requestId = reply->metadata.request_id;
  nano_reply.which_reply = pulczar_board_Reply_pulczar_tag;
  nano_reply.reply.pulczar.which_reply = pulczar_Reply_dawg_tag;
  Nano_Convert_Dawg_Reply(reply, &nano_reply.reply.pulczar.reply.dawg);
  return Ethernet_Send_pulczar_board_Reply(&reply->metadata.metadata.eth, &nano_reply);
}

M_Error_Code_t Send_Laser_Reply(M_Laser_Reply_t *reply) {
  if (reply->metadata.request_type != REQUEST_METADATA_TYPE_ETHERNET) {
    return M_ERROR_CODE_INVALID_METADATA;
  }

  pulczar_board_Reply nano_reply = pulczar_board_Reply_init_zero;
  nano_reply.has_header = true;
  nano_reply.header.requestId = reply->metadata.request_id;
  nano_reply.which_reply = pulczar_board_Reply_pulczar_tag;
  nano_reply.reply.pulczar.which_reply = pulczar_Reply_laser_tag;
  Nano_Convert_Laser_Reply(reply, &nano_reply.reply.pulczar.reply.laser);
  return Ethernet_Send_pulczar_board_Reply(&reply->metadata.metadata.eth, &nano_reply);
}

M_Error_Code_t Send_Lens_Reply(M_Lens_Reply_t *reply) {
  if (reply->metadata.request_type != REQUEST_METADATA_TYPE_ETHERNET) {
    return M_ERROR_CODE_INVALID_METADATA;
  }

  pulczar_board_Reply nano_reply = pulczar_board_Reply_init_zero;
  nano_reply.has_header = true;
  nano_reply.header.requestId = reply->metadata.request_id;
  nano_reply.which_reply = pulczar_board_Reply_pulczar_tag;
  nano_reply.reply.pulczar.which_reply = pulczar_Reply_lens_tag;
  Nano_Convert_Lens_Reply(reply, &nano_reply.reply.pulczar.reply.lens);
  return Ethernet_Send_pulczar_board_Reply(&reply->metadata.metadata.eth, &nano_reply);
}

void Boot_Task(void *unused) {
  (void)unused;

  // Startup
  uint8_t addr_offset = (ADDR2_Read() << 2) | (ADDR1_Read() << 1) | (ADDR0_Read());
  Ethernet_boot(PULCZAR_IP_INDEX, SUBINDEX_OFFSET(PULCZAR_IP_SUB_INDEX_BASE, addr_offset));
  EPOS_boot(1);

  EPOS_MCAN_Clear_Node_Configuration();

  PPS_Time_Boot_Component();
  Gimbal_Boot_Component(Send_Gimbal_Reply, PPS_Time_Get_Timestamp, 1);
  Dawg_Boot_Component(Send_Dawg_Reply, 1);
  Laser_Boot_Component(Send_Laser_Reply, 1);
  Lens_Boot_Component(Send_Lens_Reply, 1);

  // Finish boot sequence.
  vTaskDelete(NULL);
}

int main(void) {
  // Enable global interrupts.
  CyGlobalIntEnable;

  // Install FreeRTOS interrupts.
  setup_FreeRTOS_interrupts();

  // Schedule boot sequence.
  xTaskCreate(Boot_Task, "Boot", configMINIMAL_STACK_SIZE + 512, 0, 1, 0);

  // Start the scheduler.
  vTaskStartScheduler();
}

/* [] END OF FILE */

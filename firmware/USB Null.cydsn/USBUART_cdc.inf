;******************************************************************************
; File Name: USBUART_cdc.inf
; Version 3.0
;
; Description:
;  Windows USB CDC setup file for USBUART Device.
;
;******************************************************************************
; Copyright 2007-2015, Cypress Semiconductor Corporation.  All rights reserved.
; You may use this file only in accordance with the license, terms, conditions,
; disclaimers, and limitations in the end user license agreement accompanying
; the software package with which this file was provided.
;******************************************************************************

[Version]
Signature="$Windows NT$"
Class=Ports
ClassGuid={4D36E978-E325-11CE-BFC1-08002BE10318}
Provider=%PROVIDER%
LayoutFile=layout.inf
DriverVer=10/05/2015,2.0.0000.0
CatalogFile=USBUART_cdc.cat

[Manufacturer]
%MFGNAME%=DeviceList, NTx86, NTia64, NTamd64

[DestinationDirs]
DefaultDestDir=12

[SourceDisksFiles]

[SourceDisksNames]

[DeviceList.NTx86]
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_00
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_01
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_02
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_03
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_04
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_05
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_06

[DeviceList.NTia64]
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_00
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_01
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_02
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_03
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_04
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_05
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_06

[DeviceList.NTamd64]
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_00
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_01
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_02
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_03
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_04
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_05
%DESCRIPTION%=DriverInstall, USB\VID_04B4&PID_F232&MI_06

;------------------------------------------------------------------------------
;  32 bit section for Windows 2000/2003/XP/Vista
;------------------------------------------------------------------------------

[DriverInstall.NTx86]
include=mdmcpq.inf
CopyFiles=DriverCopyFiles
AddReg=DriverInstall.NTx86.AddReg

[DriverCopyFiles]
usbser.sys,,,0x20

[DriverInstall.NTx86.AddReg]
HKR,,DevLoader,,*ntkern
HKR,,NTMPDriver,,usbser.sys
HKR,,EnumPropPages32,,"MsPorts.dll,SerialPortPropPageProvider"

[DriverInstall.NTx86.Services]
AddService=usbser, 0x00000002, DriverService

;------------------------------------------------------------------------------
;  64 bit section for Intel Itanium based systems
;------------------------------------------------------------------------------

[DriverInstall.NTia64]
include=mdmcpq.inf
CopyFiles=DriverCopyFiles
AddReg=DriverInstall.NTia64.AddReg

[DriverCopyFiles]
usbser.sys,,,0x20

[DriverInstall.NTia64.AddReg]
HKR,,DevLoader,,*ntkern
HKR,,NTMPDriver,,usbser.sys
HKR,,EnumPropPages32,,"MsPorts.dll,SerialPortPropPageProvider"

[DriverInstall.NTia64.Services]
AddService=usbser, 0x00000002, DriverService

;------------------------------------------------------------------------------
;  64 bit section for AMD64 and Intel EM64T based systems
;------------------------------------------------------------------------------

[DriverInstall.NTamd64]
include=mdmcpq.inf
CopyFiles=DriverCopyFiles
AddReg=DriverInstall.NTamd64.AddReg

[DriverCopyFiles]
usbser.sys,,,0x20

[DriverInstall.NTamd64.AddReg]
HKR,,DevLoader,,*ntkern
HKR,,NTMPDriver,,usbser.sys
HKR,,EnumPropPages32,,"MsPorts.dll,SerialPortPropPageProvider"

[DriverInstall.NTamd64.Services]
AddService=usbser, 0x00000002, DriverService

;------------------------------------------------------------------------------
;
;------------------------------------------------------------------------------

[DriverService]
DisplayName=%SERVICE%
ServiceType=1
StartType=3
ErrorControl=1
ServiceBinary=%12%\usbser.sys

;------------------------------------------------------------------------------
;  String Definitions
;------------------------------------------------------------------------------

[Strings]
PROVIDER="Cypress"
MFGNAME="Cypress Semiconductor Corporation"
DESCRIPTION="Cypress USB UART"
SERVICE="USB UART"

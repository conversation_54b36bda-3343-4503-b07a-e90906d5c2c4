﻿<?xml version="1.0" encoding="utf-8"?>
<DesignWideResources xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cypress.com/xsd/cydwr">
  <Group key="Clock2">
    <Group key="DesigneWideClks" />
    <Group key="LocalClks" />
    <Group key="SourceClks" />
    <Group key="SystemClks">
      <Group key="1F7CF08C-1B36-4851-9441-035049A4211B">
        <Data key="check_tolerance" value="True" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="15" />
        <Data key="divider" value="0" />
        <Data key="domain" value="DIGITAL" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="Digital_Signal" />
        <Data key="netlist_name" value="Digital Signal" />
        <Data key="placement" value="GLOBAL" />
        <Data key="plus_tolerance" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="False" />
        <Data key="sync_with_bus_clk" value="False" />
        <Data key="user_set_domain" value="False" />
      </Group>
      <Group key="39D5E4C2-EBFC-44ab-AE3D-19F9BBFD674D">
        <Data key="check_tolerance" value="True" />
        <Data key="desired_freq" value="64" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="domain" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="PLL_OUT" />
        <Data key="netlist_name" value="PLL_OUT" />
        <Data key="placement" value="GLOBAL" />
        <Data key="plus_tolerance" value="0" />
        <Data key="src_clk_id" value="CEF43CFB-0213-49b9-B980-2FFAB81C5B47" />
        <Data key="src_clk_name" value="IMO" />
        <Data key="start_on_reset" value="True" />
        <Data key="sync_with_bus_clk" value="False" />
        <Data key="user_set_domain" value="False" />
      </Group>
      <Group key="46B167E3-1786-4598-8688-AACCF18668D4">
        <Data key="check_tolerance" value="True" />
        <Data key="desired_freq" value="33" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="domain" value="DIGITAL" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="XTAL" />
        <Data key="netlist_name" value="XTAL" />
        <Data key="placement" value="GLOBAL" />
        <Data key="plus_tolerance" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="False" />
        <Data key="sync_with_bus_clk" value="False" />
        <Data key="user_set_domain" value="False" />
        <Data key="xtal_enable_fault_recovery" value="False" />
        <Data key="xtal_minus_Crystal_acuracy_name" value="0" />
        <Data key="xtal_plus_crystal_acuracy_name" value="0" />
      </Group>
      <Group key="75C2148C-3656-4d8a-846D-0CAE99AB6FF7">
        <Data key="check_tolerance" value="True" />
        <Data key="clk_key_bus_usedivider" value="True" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="15" />
        <Data key="divider" value="1" />
        <Data key="domain" value="DIGITAL" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="BUS_CLK" />
        <Data key="netlist_name" value="BUS_CLK" />
        <Data key="placement" value="GLOBAL" />
        <Data key="plus_tolerance" value="0" />
        <Data key="src_clk_id" value="61737EF6-3B74-48f9-8B91-F7473A442AE7" />
        <Data key="src_clk_name" value="MASTER_CLK" />
        <Data key="start_on_reset" value="True" />
        <Data key="sync_with_bus_clk" value="True" />
        <Data key="user_set_domain" value="False" />
      </Group>
      <Group key="61737EF6-3B74-48f9-8B91-F7473A442AE7">
        <Data key="check_tolerance" value="True" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="15" />
        <Data key="divider" value="1" />
        <Data key="domain" value="DIGITAL" />
        <Data key="enabled" value="True" />
        <Data key="master_divider_used" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="MASTER_CLK" />
        <Data key="netlist_name" value="MASTER_CLK" />
        <Data key="placement" value="GLOBAL" />
        <Data key="plus_tolerance" value="0" />
        <Data key="src_clk_id" value="39D5E4C2-EBFC-44ab-AE3D-19F9BBFD674D" />
        <Data key="src_clk_name" value="PLL_OUT" />
        <Data key="start_on_reset" value="True" />
        <Data key="sync_with_bus_clk" value="True" />
        <Data key="user_set_domain" value="False" />
      </Group>
      <Group key="96816ED8-BCFA-4aad-B6AF-0E41C02E8C31">
        <Data key="check_tolerance" value="True" />
        <Data key="clk_key_usb_imox2" value="True" />
        <Data key="clk_key_usb_usedivider" value="False" />
        <Data key="desired_freq" value="48" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="1" />
        <Data key="domain" value="DIGITAL" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="USB_CLK" />
        <Data key="netlist_name" value="USB_CLK" />
        <Data key="placement" value="GLOBAL" />
        <Data key="plus_tolerance" value="0" />
        <Data key="src_clk_id" value="CEF43CFB-0213-49b9-B980-2FFAB81C5B47" />
        <Data key="src_clk_name" value="IMO" />
        <Data key="start_on_reset" value="False" />
        <Data key="sync_with_bus_clk" value="False" />
        <Data key="user_set_domain" value="False" />
      </Group>
      <Group key="315365C3-2E3E-4f04-84A2-BB564A173261">
        <Data key="check_tolerance" value="True" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="15" />
        <Data key="divider" value="0" />
        <Data key="domain" value="DIGITAL" />
        <Data key="enabled" value="True" />
        <Data key="ilo_oscillator" value="ILO_100kHz" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="ILO" />
        <Data key="netlist_name" value="ILO" />
        <Data key="placement" value="GLOBAL" />
        <Data key="plus_tolerance" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="True" />
        <Data key="sync_with_bus_clk" value="False" />
        <Data key="user_set_domain" value="False" />
      </Group>
      <Group key="CEF43CFB-0213-49b9-B980-2FFAB81C5B47">
        <Data key="check_tolerance" value="True" />
        <Data key="desired_freq" value="24" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="domain" value="DIGITAL" />
        <Data key="enabled" value="True" />
        <Data key="imo_doubler" value="False" />
        <Data key="imo_oscillator" value="IMO_24MHZ" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="IMO" />
        <Data key="netlist_name" value="IMO" />
        <Data key="placement" value="GLOBAL" />
        <Data key="plus_tolerance" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="True" />
        <Data key="sync_with_bus_clk" value="False" />
        <Data key="user_set_domain" value="False" />
      </Group>
      <Group key="EC9D9168-D68F-4573-AC21-F93D3BF005CD">
        <Data key="check_tolerance" value="True" />
        <Data key="desired_freq" value="32.768" />
        <Data key="desired_unit" value="3" />
        <Data key="divider" value="0" />
        <Data key="domain" value="DIGITAL" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="XTAL_32KHZ" />
        <Data key="netlist_name" value="XTAL 32kHz" />
        <Data key="placement" value="GLOBAL" />
        <Data key="plus_tolerance" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="False" />
        <Data key="sync_with_bus_clk" value="False" />
        <Data key="user_set_domain" value="False" />
        <Data key="xtal_minus_Crystal_acuracy_name" value="0" />
        <Data key="xtal_plus_crystal_acuracy_name" value="0" />
      </Group>
    </Group>
  </Group>
  <Group key="Component">
    <Group key="v1">
      <Data key="cy_boot" value="cy_boot_v5_60" />
      <Data key="cy_dmac" value="cy_dmac_v1_10" />
      <Data key="cy_lfclk" value="cy_lfclk_v1_20" />
      <Data key="Em_EEPROM_Dynamic" value="Em_EEPROM_Dynamic_v2_20" />
      <Data key="LIN_Dynamic" value="LIN_Dynamic_v4_0" />
    </Group>
  </Group>
  <Group key="DWRInstGuidMapping">
    <Group key="Pin">
      <Data key="48e09283-6e22-4c96-a30a-1443caf0447f" value="SW2" />
      <Data key="5484040d-da45-4556-9268-487cc7d82d55" value="TEST" />
      <Data key="ac8fb70c-7191-4547-91f8-16d96c1410fe/ed092b9b-d398-4703-be89-cebf998501f6" value="LCD_LCDPort" />
      <Data key="c39ef993-d787-4c0c-8ad6-c0c81f866442/8b77a6c4-10a0-4390-971c-672353e2a49c" value="USBUART_Dm" />
      <Data key="c39ef993-d787-4c0c-8ad6-c0c81f866442/605a40d2-572d-4c7a-818e-3bfd37f14d87" value="USBUART_VBUS" />
      <Data key="c39ef993-d787-4c0c-8ad6-c0c81f866442/618a72fc-5ddd-4df5-958f-a3d55102db42" value="USBUART_Dp" />
      <Data key="e851a3b9-efb8-48be-bbb8-b303b216c393" value="LED" />
      <Data key="ed49cdf9-38df-4d0d-bf88-cc8fc60fbfde" value="SW1" />
    </Group>
  </Group>
  <Group key="M0S8Clock">
    <Group key="SystemClks">
      <Group key="1EB9C57A-B1AC-449a-92B8-1EB82F7A8FE5">
        <Data key="accuracy_display_unit" value="1" />
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="24" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="EXTCLK" />
        <Data key="netlist_name" value="EXTCLK" />
        <Data key="plus_tolerance" value="0" />
        <Data key="port_number" value="0" />
        <Data key="port_offset" value="6" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="False" />
      </Group>
      <Group key="002F1065-3E80-4855-857A-F0028A50C622">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="32" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="Timer1 (WDT1)" />
        <Data key="netlist_name" value="Timer1 (WDT1)" />
        <Data key="operation_mode" value="1" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="9A908CA6-5BB3-4db0-B098-959E5D90882B" />
        <Data key="src_clk_name" value="LFCLK" />
        <Data key="start_on_reset" value="False" />
      </Group>
      <Group key="3CF048D2-8DE2-48ce-836E-C46FE3B10287">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="24" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="False" />
        <Data key="MANUAL" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="PLL1" />
        <Data key="netlist_name" value="PLL1" />
        <Data key="P" value="0" />
        <Data key="plus_tolerance" value="0" />
        <Data key="Q" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="BA1FF31D-570B-4ee4-8086-5A1087FF2AD2" />
        <Data key="src_clk_name" value="PLL1_Sel" />
        <Data key="start_on_reset" value="False" />
      </Group>
      <Group key="3E3A6273-3DC0-49b9-A765-A81596A23306">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="48" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="Routed1" />
        <Data key="netlist_name" value="Routed1" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0.25" />
        <Data key="sourceClk_plus_acuracy_name" value="0.25" />
        <Data key="src_clk_id" value="13A80296-70D5-4f89-B0D3-510602484A50" />
        <Data key="src_clk_name" value="Direct_Sel" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="4A9DE5A6-9EF0-42af-BC8C-37DA5E390898">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="24" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="False" />
        <Data key="MANUAL" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="PLL0" />
        <Data key="netlist_name" value="PLL0" />
        <Data key="P" value="0" />
        <Data key="plus_tolerance" value="0" />
        <Data key="Q" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="25052E45-97E1-4618-A621-47712D88CDFB" />
        <Data key="src_clk_name" value="PLL0_Sel" />
        <Data key="start_on_reset" value="False" />
      </Group>
      <Group key="5A84F528-323D-4eda-8141-58D504D70A4B">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="1" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="SYSCLK" />
        <Data key="netlist_name" value="SYSCLK" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0.25" />
        <Data key="sourceClk_plus_acuracy_name" value="0.25" />
        <Data key="src_clk_id" value="413DE2EF-D9F2-4233-A808-DFAF137FD877" />
        <Data key="src_clk_name" value="HFCLK" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="9A908CA6-5BB3-4db0-B098-959E5D90882B">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="LFCLK" />
        <Data key="netlist_name" value="LFCLK" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="60" />
        <Data key="sourceClk_plus_acuracy_name" value="60" />
        <Data key="src_clk_id" value="D231CDB6-1E83-48bf-9FBA-0584D07512BE" />
        <Data key="src_clk_name" value="ILO" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="13A80296-70D5-4f89-B0D3-510602484A50">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="48" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="Direct_Sel" />
        <Data key="netlist_name" value="Direct_Sel" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0.25" />
        <Data key="sourceClk_plus_acuracy_name" value="0.25" />
        <Data key="src_clk_id" value="A8C3204D-9BE8-4092-97CC-B0B247063044" />
        <Data key="src_clk_name" value="IMO" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="37D694F5-3CB0-4bfb-AEF0-963FED0CE2EC">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="DigSig4" />
        <Data key="netlist_name" value="DigSig4" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="False" />
      </Group>
      <Group key="223AFA12-A351-4b16-AC47-BAC268E7B826">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="48" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="Routed2" />
        <Data key="netlist_name" value="Routed2" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0.25" />
        <Data key="sourceClk_plus_acuracy_name" value="0.25" />
        <Data key="src_clk_id" value="BA1FF31D-570B-4ee4-8086-5A1087FF2AD2" />
        <Data key="src_clk_name" value="PLL1_Sel" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="238B4C77-851C-44d4-8040-C396F09E3440">
        <Data key="accuracy_display_unit" value="0" />
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="32768" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="WCO" />
        <Data key="netlist_name" value="WCO" />
        <Data key="plus_tolerance" value="0" />
        <Data key="power_mode" value="True" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0.015" />
        <Data key="sourceClk_plus_acuracy_name" value="0.015" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="False" />
      </Group>
      <Group key="413DE2EF-D9F2-4233-A808-DFAF137FD877">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="48" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="1" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="HFCLK" />
        <Data key="netlist_name" value="HFCLK" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0.25" />
        <Data key="sourceClk_plus_acuracy_name" value="0.25" />
        <Data key="src_clk_id" value="13A80296-70D5-4f89-B0D3-510602484A50" />
        <Data key="src_clk_name" value="Direct_Sel" />
        <Data key="srss_lite_divider" value="1" />
        <Data key="srss_lite_src_clk_id" value="A8C3204D-9BE8-4092-97CC-B0B247063044" />
        <Data key="srss_lite_src_clk_name" value="IMO" />
        <Data key="srss_src_clk_id" value="13A80296-70D5-4f89-B0D3-510602484A50" />
        <Data key="srss_src_clk_name" value="Direct_Sel" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="0895B506-663A-4890-93A8-F9D21970F5C1">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="DigSig2" />
        <Data key="netlist_name" value="DigSig2" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="False" />
      </Group>
      <Group key="2629E085-8588-4069-AC82-1632B7FD0079">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="DigSig3" />
        <Data key="netlist_name" value="DigSig3" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="False" />
      </Group>
      <Group key="6237B83A-0D23-454D-B9E0-FDDE55BDD181">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="15" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="Timer2 (WDT2)" />
        <Data key="netlist_name" value="Timer2 (WDT2)" />
        <Data key="operation_mode" value="1" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="9A908CA6-5BB3-4db0-B098-959E5D90882B" />
        <Data key="src_clk_name" value="LFCLK" />
        <Data key="start_on_reset" value="False" />
      </Group>
      <Group key="06587E14-7ADD-4811-9EA1-A17991CA4BBA">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="48" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="Routed4" />
        <Data key="netlist_name" value="Routed4" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0.25" />
        <Data key="sourceClk_plus_acuracy_name" value="0.25" />
        <Data key="src_clk_id" value="E08F1AEE-48D4-466e-B1C4-06141E81C5F4" />
        <Data key="src_clk_name" value="DPLL_Sel" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="25052E45-97E1-4618-A621-47712D88CDFB">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="48" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="PLL0_Sel" />
        <Data key="netlist_name" value="PLL0_Sel" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0.25" />
        <Data key="sourceClk_plus_acuracy_name" value="0.25" />
        <Data key="src_clk_id" value="A8C3204D-9BE8-4092-97CC-B0B247063044" />
        <Data key="src_clk_name" value="IMO" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="94835B23-A1C6-4386-9A1F-A6C9073F9668">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="RTC_Sel" />
        <Data key="netlist_name" value="RTC_Sel" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="None" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="A8C3204D-9BE8-4092-97CC-B0B247063044">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="48" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="IMO" />
        <Data key="netlist_name" value="IMO" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0.25" />
        <Data key="sourceClk_plus_acuracy_name" value="0.25" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="True" />
        <Data key="trim_with" value="USB" />
      </Group>
      <Group key="AE29FF0C-09F4-4f1f-90F6-BE0C5AFEF171">
        <Data key="accuracy_display_unit" value="1" />
        <Data key="captrim1" value="9919" />
        <Data key="captrim2" value="8200" />
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="24" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="ECO" />
        <Data key="netlist_name" value="ECO" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="False" />
      </Group>
      <Group key="BA1FF31D-570B-4ee4-8086-5A1087FF2AD2">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="48" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="PLL1_Sel" />
        <Data key="netlist_name" value="PLL1_Sel" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0.25" />
        <Data key="sourceClk_plus_acuracy_name" value="0.25" />
        <Data key="src_clk_id" value="A8C3204D-9BE8-4092-97CC-B0B247063044" />
        <Data key="src_clk_name" value="IMO" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="BBDA22A0-AAD6-4AD0-B78B-036251C15373">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="3" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="generate_isr" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="Timer (WDT) ISR" />
        <Data key="netlist_name" value="Timer (WDT) ISR" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="D09AF12C-2C44-4974-99B3-662843C5DC35">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="32" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="Timer0 (WDT0)" />
        <Data key="netlist_name" value="Timer0 (WDT0)" />
        <Data key="operation_mode" value="1" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="9A908CA6-5BB3-4db0-B098-959E5D90882B" />
        <Data key="src_clk_name" value="LFCLK" />
        <Data key="start_on_reset" value="False" />
      </Group>
      <Group key="D231CDB6-1E83-48bf-9FBA-0584D07512BE">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="32" />
        <Data key="desired_unit" value="3" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="ILO" />
        <Data key="netlist_name" value="ILO" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="60" />
        <Data key="sourceClk_plus_acuracy_name" value="60" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="E08F1AEE-48D4-466e-B1C4-06141E81C5F4">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="48" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="DPLL_Sel" />
        <Data key="netlist_name" value="DPLL_Sel" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0.25" />
        <Data key="sourceClk_plus_acuracy_name" value="0.25" />
        <Data key="src_clk_id" value="A8C3204D-9BE8-4092-97CC-B0B247063044" />
        <Data key="src_clk_name" value="IMO" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="EF840448-5105-4752-9F6A-7A52DA445067">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="DigSig1" />
        <Data key="netlist_name" value="DigSig1" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="False" />
      </Group>
      <Group key="F0E37851-1284-42a2-B1DB-9D4E0792717A">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="2" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="HALFSYSCLK" />
        <Data key="netlist_name" value="HALFSYSCLK" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0.25" />
        <Data key="sourceClk_plus_acuracy_name" value="0.25" />
        <Data key="src_clk_id" value="5A84F528-323D-4eda-8141-58D504D70A4B" />
        <Data key="src_clk_name" value="SYSCLK" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="F19A89AE-D2B5-4d11-9670-CE5139C8F272">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="48" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="Routed3" />
        <Data key="netlist_name" value="Routed3" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0.25" />
        <Data key="sourceClk_plus_acuracy_name" value="0.25" />
        <Data key="src_clk_id" value="25052E45-97E1-4618-A621-47712D88CDFB" />
        <Data key="src_clk_name" value="PLL0_Sel" />
        <Data key="start_on_reset" value="True" />
      </Group>
    </Group>
  </Group>
  <Group key="Pin2">
    <Group key="0dfb7f3f-9f6d-4b2d-a073-bec6d06b12cc">
      <Group key="0">
        <Data key="Port Format" value="0,4" />
      </Group>
    </Group>
    <Group key="1a84992e-0b68-49e2-9fbd-97783e51fc1a/bcf50901-38ae-461b-a57c-bc3cfeb9b89c">
      <Group key="0">
        <Data key="Port Format" value="6,7" />
      </Group>
    </Group>
    <Group key="1db46118-9728-4ae9-bff6-9b6cb3c054c9/bcf50901-38ae-461b-a57c-bc3cfeb9b89c">
      <Group key="0">
        <Data key="Port Format" value="6,6" />
      </Group>
    </Group>
    <Group key="48e09283-6e22-4c96-a30a-1443caf0447f">
      <Group key="0">
        <Data key="Port Format" value="0,3" />
      </Group>
    </Group>
    <Group key="6646cf11-29b9-42e8-956f-9d09d589face">
      <Group key="0">
        <Data key="Port Format" value="0,3" />
      </Group>
    </Group>
    <Group key="181822ee-f6f8-428c-b67e-96bcffc09b9d/0ddd41cd-04f9-4a16-87da-ce31f955b6b0/c02e882c-b6dd-46ba-89c8-4554f020b0c8">
      <Group key="0">
        <Data key="Port Format" value="2,0" />
      </Group>
      <Group key="1">
        <Data key="Port Format" value="2,1" />
      </Group>
      <Group key="2">
        <Data key="Port Format" value="2,2" />
      </Group>
      <Group key="3">
        <Data key="Port Format" value="2,3" />
      </Group>
      <Group key="4">
        <Data key="Port Format" value="2,4" />
      </Group>
      <Group key="5">
        <Data key="Port Format" value="2,5" />
      </Group>
      <Group key="6">
        <Data key="Port Format" value="2,6" />
      </Group>
    </Group>
    <Group key="181822ee-f6f8-428c-b67e-96bcffc09b9d/2dd63102-85f6-487e-8f0c-a642a08fa777/bcf50901-38ae-461b-a57c-bc3cfeb9b89c">
      <Group key="0">
        <Data key="Port Format" value="6,6" />
      </Group>
    </Group>
    <Group key="181822ee-f6f8-428c-b67e-96bcffc09b9d/ea17e02d-226a-4bdd-9cc4-bec9487b6576/bcf50901-38ae-461b-a57c-bc3cfeb9b89c">
      <Group key="0">
        <Data key="Port Format" value="6,7" />
      </Group>
    </Group>
    <Group key="5484040d-da45-4556-9268-487cc7d82d55">
      <Group key="0">
        <Data key="Port Format" value="0,2" />
      </Group>
    </Group>
    <Group key="ac8fb70c-7191-4547-91f8-16d96c1410fe/c02e882c-b6dd-46ba-89c8-4554f020b0c8">
      <Group key="0">
        <Data key="Port Format" value="2,0" />
      </Group>
      <Group key="1">
        <Data key="Port Format" value="2,1" />
      </Group>
      <Group key="2">
        <Data key="Port Format" value="2,2" />
      </Group>
      <Group key="3">
        <Data key="Port Format" value="2,3" />
      </Group>
      <Group key="4">
        <Data key="Port Format" value="2,4" />
      </Group>
      <Group key="5">
        <Data key="Port Format" value="2,5" />
      </Group>
      <Group key="6">
        <Data key="Port Format" value="2,6" />
      </Group>
    </Group>
    <Group key="ad4d1e78-a55d-4c10-8caa-177806ceea02/bcf50901-38ae-461b-a57c-bc3cfeb9b89c">
      <Group key="0">
        <Data key="Port Format" value="6,6" />
      </Group>
    </Group>
    <Group key="c39ef993-d787-4c0c-8ad6-c0c81f866442/8afdd3ea-4211-4f90-8b4b-5771b9e0902b">
      <Group key="0">
        <Data key="Port Format" value="0,5" />
      </Group>
    </Group>
    <Group key="c39ef993-d787-4c0c-8ad6-c0c81f866442/8b77a6c4-10a0-4390-971c-672353e2a49c">
      <Group key="0">
        <Data key="Port Format" value="15,7" />
      </Group>
    </Group>
    <Group key="c39ef993-d787-4c0c-8ad6-c0c81f866442/43f229e4-33ed-4b6c-9f8f-6d60713ebee5">
      <Group key="0">
        <Data key="Port Format" value="15,7" />
      </Group>
    </Group>
    <Group key="c39ef993-d787-4c0c-8ad6-c0c81f866442/427d7144-a1e8-4bca-a79f-71f65bb81965">
      <Group key="0">
        <Data key="Port Format" value="15,6" />
      </Group>
    </Group>
    <Group key="c39ef993-d787-4c0c-8ad6-c0c81f866442/618a72fc-5ddd-4df5-958f-a3d55102db42">
      <Group key="0">
        <Data key="Port Format" value="15,6" />
      </Group>
    </Group>
    <Group key="d938166a-c041-4af5-afaf-ef82f4b4af02/bcf50901-38ae-461b-a57c-bc3cfeb9b89c">
      <Group key="0">
        <Data key="Port Format" value="6,7" />
      </Group>
    </Group>
    <Group key="e851a3b9-efb8-48be-bbb8-b303b216c393">
      <Group key="0">
        <Data key="Port Format" value="2,1" />
      </Group>
    </Group>
    <Group key="ed49cdf9-38df-4d0d-bf88-cc8fc60fbfde">
      <Group key="0">
        <Data key="Port Format" value="1,7" />
      </Group>
    </Group>
    <Group key="UnAssigned Pins" />
    <Group key="Unlocked Pins">
      <Group key="ac8fb70c-7191-4547-91f8-16d96c1410fe/ed092b9b-d398-4703-be89-cebf998501f6">
        <Group key="0">
          <Group key="130861016824834068">
            <Data key="Port Format" value="2,0" />
          </Group>
        </Group>
        <Group key="1">
          <Group key="130861016824834068">
            <Data key="Port Format" value="2,1" />
          </Group>
        </Group>
        <Group key="2">
          <Group key="130861016824834068">
            <Data key="Port Format" value="2,2" />
          </Group>
        </Group>
        <Group key="3">
          <Group key="130861016824834068">
            <Data key="Port Format" value="2,3" />
          </Group>
        </Group>
        <Group key="4">
          <Group key="130861016824834068">
            <Data key="Port Format" value="2,4" />
          </Group>
        </Group>
        <Group key="5">
          <Group key="130861016824834068">
            <Data key="Port Format" value="2,5" />
          </Group>
        </Group>
        <Group key="6">
          <Group key="130861016824834068">
            <Data key="Port Format" value="2,6" />
          </Group>
        </Group>
      </Group>
      <Group key="c39ef993-d787-4c0c-8ad6-c0c81f866442/605a40d2-572d-4c7a-818e-3bfd37f14d87">
        <Group key="0">
          <Group key="130861016832544509">
            <Data key="Port Format" value="0,5" />
          </Group>
        </Group>
      </Group>
    </Group>
  </Group>
  <Group key="System3">
    <Data key="CYDEV_BOOTLOADER_IO_COMP" value="" />
    <Data key="CYDEV_BOOTLOADER_WAIT_COMMAND" value="True" />
    <Data key="CYDEV_BOOTLOADER_WAIT_TIME" value="10" />
    <Data key="CYDEV_CONFIG_FASTBOOT_ENABLED" value="True" />
    <Data key="CYDEV_CONFIG_UNUSED_IO" value="AllowButWarn" />
    <Data key="CYDEV_CONFIGURATION_CLEAR_SRAM" value="True" />
    <Data key="CYDEV_CONFIGURATION_ECC" value="False" />
    <Data key="CYDEV_CONFIGURATION_IMOENABLED" value="False" />
    <Data key="CYDEV_CONFIGURATION_MODE" value="COMPRESSED" />
    <Data key="CYDEV_Data_CACHE_ENABLED" value="True" />
    <Data key="CYDEV_DEBUGGING_DPS" value="SWD_SWV" />
    <Data key="CYDEV_DEBUGGING_REQXRES" value="True" />
    <Data key="CYDEV_DEBUGGING_XRES" value="True" />
    <Data key="CYDEV_ECC_ENABLE" value="False" />
    <Data key="CYDEV_HEAP_SIZE" value="0x80" />
    <Data key="CYDEV_INSTRUCT_CACHE_ENABLED" value="False" />
    <Data key="CYDEV_PROTECTION_ENABLE" value="False" />
    <Data key="CYDEV_STACK_SIZE" value="0x0800" />
    <Data key="CYDEV_SWV_ENABLED" value="True" />
    <Data key="CYDEV_TEMPERATURE" value="-40C - 85/125C" />
    <Data key="CYDEV_TRACE_ENABLED" value="False" />
    <Data key="CYDEV_USE_BUNDLED_CMSIS" value="True" />
    <Data key="CYDEV_VARIABLE_VDDA" value="True" />
    <Data key="CYDEV_VDDA" value="5.0" />
    <Data key="CYDEV_VDDD" value="5.0" />
    <Data key="CYDEV_VDDIO" value="5.0" />
    <Data key="CYDEV_VDDIO0" value="5.0" />
    <Data key="CYDEV_VDDIO1" value="5.0" />
    <Data key="CYDEV_VDDIO2" value="5.0" />
    <Data key="CYDEV_VDDIO3" value="5.0" />
    <Data key="CYDEV_WO_NVL_ENABLED" value="False" />
  </Group>
</DesignWideResources>
<?xml version="1.0" encoding="utf-8"?>
<CyXmlSerializer>
<!--This file is machine generated and read. It is not intended to be edited by hand.-->
<!--Due to this, there is no schema for this file.-->
<CyGuid_2867d519-54d2-4c01-9830-c51cb08bc3dd type_name="CyDesigner.Common.ProjMgmt.Model.CyPrjMgmtWorkspaceSerialize" version="5" xml_contents_version="3" name="CarbonMain" persistent="CarbonMain.cywrk" platform="NOT_USED">
<current_project name="..\NoFXBoard_Bud.cydsn\NoFXBoard_Bud.cyprj" />
<wrkspc_folders />
<files />
<projects>
<v>..\Maka Library.cylib\Maka Library.cyprj</v>
<v>..\Laser Control Board.cydsn\Laser Control Board.cyprj</v>
<v>..\Roverdrive.cydsn\Roverdrive.cyprj</v>
<v>..\Servo Controller.cydsn\Servo Controller.cyprj</v>
<v>..\Capcart Strobe.cydsn\Capcart Strobe.cyprj</v>
<v>..\Seedy Drive.cydsn\Seedy Drive.cyprj</v>
<v>..\USB Null.cydsn\USB Null.cyprj</v>
<v>..\HFX Control.cydsn\HFX Control.cyprj</v>
<v>..\Row Module Board.cydsn\Row Module Board.cyprj</v>
<v>..\Strobe Control Board.cydsn\Strobe Control Board.cyprj</v>
<v>..\FreeRTOS_CM3_72MHz.cylib\FreeRTOS_CM3_72MHz.cyprj</v>
<v>..\Maka FreeRTOS Components.cylib\Maka FreeRTOS Components.cyprj</v>
<v>..\nanopb.cylib\nanopb.cyprj</v>
<v>..\ExperimentBoard.cydsn\ExperimentBoard.cyprj</v>
<v>..\NanoPB_Proto.cylib\NanoPB_Proto.cyprj</v>
<v>..\NanoPB_Converter.cylib\NanoPB_Converter.cyprj</v>
<v>..\RowModuleBoard2.cydsn\RowModuleBoard2.cyprj</v>
<v>..\NoFXBoard.cydsn\NoFXBoard.cyprj</v>
<v>..\RowModuleBoard_Rev4.cydsn\RowModuleBoard_Rev4.cyprj</v>
<v>..\HubbleCameraPowerRelay.cydsn\HubbleCameraPowerRelay.cyprj</v>
<v>..\BenjaminGPSBoard.cydsn\BenjaminGPSBoard.cyprj</v>
<v>..\NoFXBootLoader.cydsn\NoFXBootLoader.cyprj</v>
<v>..\CarbonEthBootloader.cydsn\CarbonEthBootloader.cyprj</v>
<v>..\NoFXBoard_Bud.cydsn\NoFXBoard_Bud.cyprj</v>
</projects>
<workspace_id v="1163c995-42e4-40b9-90a6-7d698fe69998" />
<WriteAppVersionLastSavedWith v="4.4.0.80" />
<WriteAppMarketingVersionLastSavedWith v="4.4" />
<CyGuid_dcbd9771-0334-43dc-9cc3-fe99dc3c5316 type_name="CyDesigner.Common.ProjMgmt.Model.CyPrjMgmtWorkspaceSerialize+CyPrjDependencyList" version="4">
<wrkspc_dependencies>
<wrkspc_dependency project="..\Strobe Control Board.cydsn\Strobe Control Board.cyprj" dependency="..\FreeRTOS_CM3_72MHz.cylib\FreeRTOS_CM3_72MHz.cyprj" />
<wrkspc_dependency project="..\Strobe Control Board.cydsn\Strobe Control Board.cyprj" dependency="..\nanopb.cylib\nanopb.cyprj" />
<wrkspc_dependency project="..\Strobe Control Board.cydsn\Strobe Control Board.cyprj" dependency="..\NanoPB_Proto.cylib\NanoPB_Proto.cyprj" />
<wrkspc_dependency project="..\ExperimentBoard.cydsn\ExperimentBoard.cyprj" dependency="..\NanoPB_Proto.cylib\NanoPB_Proto.cyprj" />
<wrkspc_dependency project="..\ExperimentBoard.cydsn\ExperimentBoard.cyprj" dependency="..\NanoPB_Converter.cylib\NanoPB_Converter.cyprj" />
<wrkspc_dependency project="..\NoFXBoard.cydsn\NoFXBoard.cyprj" dependency="..\Maka Library.cylib\Maka Library.cyprj" />
<wrkspc_dependency project="..\NoFXBoard.cydsn\NoFXBoard.cyprj" dependency="..\FreeRTOS_CM3_72MHz.cylib\FreeRTOS_CM3_72MHz.cyprj" />
<wrkspc_dependency project="..\NoFXBoard.cydsn\NoFXBoard.cyprj" dependency="..\Maka FreeRTOS Components.cylib\Maka FreeRTOS Components.cyprj" />
<wrkspc_dependency project="..\NoFXBoard.cydsn\NoFXBoard.cyprj" dependency="..\nanopb.cylib\nanopb.cyprj" />
<wrkspc_dependency project="..\NoFXBoard.cydsn\NoFXBoard.cyprj" dependency="..\NanoPB_Proto.cylib\NanoPB_Proto.cyprj" />
<wrkspc_dependency project="..\RowModuleBoard_Rev4.cydsn\RowModuleBoard_Rev4.cyprj" dependency="..\NanoPB_Proto.cylib\NanoPB_Proto.cyprj" />
<wrkspc_dependency project="..\RowModuleBoard_Rev4.cydsn\RowModuleBoard_Rev4.cyprj" dependency="..\NanoPB_Converter.cylib\NanoPB_Converter.cyprj" />
<wrkspc_dependency project="..\BenjaminGPSBoard.cydsn\BenjaminGPSBoard.cyprj" dependency="..\Maka Library.cylib\Maka Library.cyprj" />
<wrkspc_dependency project="..\BenjaminGPSBoard.cydsn\BenjaminGPSBoard.cyprj" dependency="..\FreeRTOS_CM3_72MHz.cylib\FreeRTOS_CM3_72MHz.cyprj" />
<wrkspc_dependency project="..\BenjaminGPSBoard.cydsn\BenjaminGPSBoard.cyprj" dependency="..\Maka FreeRTOS Components.cylib\Maka FreeRTOS Components.cyprj" />
<wrkspc_dependency project="..\BenjaminGPSBoard.cydsn\BenjaminGPSBoard.cyprj" dependency="..\nanopb.cylib\nanopb.cyprj" />
<wrkspc_dependency project="..\BenjaminGPSBoard.cydsn\BenjaminGPSBoard.cyprj" dependency="..\NanoPB_Proto.cylib\NanoPB_Proto.cyprj" />
<wrkspc_dependency project="..\CarbonEthBootloader.cydsn\CarbonEthBootloader.cyprj" dependency="${CyRoot}\psoc\content\default\CyAnnotationLibrary\CyAnnotationLibrary.cylib\CyAnnotationLibrary.cyprj" />
<wrkspc_dependency project="..\CarbonEthBootloader.cydsn\CarbonEthBootloader.cyprj" dependency="..\Maka Library.cylib\Maka Library.cyprj" />
<wrkspc_dependency project="..\CarbonEthBootloader.cydsn\CarbonEthBootloader.cyprj" dependency="..\FreeRTOS_CM3_72MHz.cylib\FreeRTOS_CM3_72MHz.cyprj" />
<wrkspc_dependency project="..\CarbonEthBootloader.cydsn\CarbonEthBootloader.cyprj" dependency="..\Maka FreeRTOS Components.cylib\Maka FreeRTOS Components.cyprj" />
<wrkspc_dependency project="..\NoFXBoard_Bud.cydsn\NoFXBoard_Bud.cyprj" dependency="${CyRoot}\psoc\content\default\CyAnnotationLibrary\CyAnnotationLibrary.cylib\CyAnnotationLibrary.cyprj" />
<wrkspc_dependency project="..\NoFXBoard_Bud.cydsn\NoFXBoard_Bud.cyprj" dependency="..\Maka Library.cylib\Maka Library.cyprj" />
<wrkspc_dependency project="..\NoFXBoard_Bud.cydsn\NoFXBoard_Bud.cyprj" dependency="..\FreeRTOS_CM3_72MHz.cylib\FreeRTOS_CM3_72MHz.cyprj" />
<wrkspc_dependency project="..\NoFXBoard_Bud.cydsn\NoFXBoard_Bud.cyprj" dependency="..\Maka FreeRTOS Components.cylib\Maka FreeRTOS Components.cyprj" />
<wrkspc_dependency project="..\NoFXBoard_Bud.cydsn\NoFXBoard_Bud.cyprj" dependency="..\nanopb.cylib\nanopb.cyprj" />
<wrkspc_dependency project="..\NoFXBoard_Bud.cydsn\NoFXBoard_Bud.cyprj" dependency="..\NanoPB_Proto.cylib\NanoPB_Proto.cyprj" />
<wrkspc_dependency project="..\NoFXBoard_Bud.cydsn\NoFXBoard_Bud.cyprj" dependency="..\NanoPB_Converter.cylib\NanoPB_Converter.cyprj" />
</wrkspc_dependencies>
</CyGuid_dcbd9771-0334-43dc-9cc3-fe99dc3c5316>
<CyGuid_63b68103-67f5-4406-8da6-5c8625765b82 type_name="CyDesigner.Common.ProjMgmt.Model.CyPrjMgmtWorkspaceSerialize+CyIgnoredSystemDepsList" version="1">
<wrkspc_dependencies />
</CyGuid_63b68103-67f5-4406-8da6-5c8625765b82>
</CyGuid_2867d519-54d2-4c01-9830-c51cb08bc3dd>
</CyXmlSerializer>
<?xml version="1.0" encoding="utf-8"?>
<CyXmlSerializer>
<!--This file is machine generated and read. It is not intended to be edited by hand.-->
<!--Due to this, there is no schema for this file.-->
<CyGuid_2867d519-54d2-4c01-9830-c51cb08bc3dd type_name="CyDesigner.Common.ProjMgmt.Model.CyPrjMgmtWorkspaceSerialize" version="5" xml_contents_version="3" name="BudWorkspace" persistent="BudWorkspace.cywrk" platform="NOT_USED">
<current_project name="..\NoFXBoard_Bud.cydsn\NoFXBoard_Bud.cyprj" />
<wrkspc_folders />
<files />
<projects>
<v>..\CarbonEthBootloader.cydsn\CarbonEthBootloader.cyprj</v>
<v>..\FreeRTOS_CM3_72MHz.cylib\FreeRTOS_CM3_72MHz.cyprj</v>
<v>..\Maka FreeRTOS Components.cylib\Maka FreeRTOS Components.cyprj</v>
<v>..\Maka Library.cylib\Maka Library.cyprj</v>
<v>..\nanopb.cylib\nanopb.cyprj</v>
<v>..\NanoPB_Converter.cylib\NanoPB_Converter.cyprj</v>
<v>..\NanoPB_Proto.cylib\NanoPB_Proto.cyprj</v>
<v>..\NoFXBoard.cydsn\NoFXBoard.cyprj</v>
<v>..\Pulczar.cydsn\Pulczar.cyprj</v>
<v>..\PulczarBootloader.cydsn\PulczarBootloader.cyprj</v>
<v>..\Strobe Control Board.cydsn\Strobe Control Board.cyprj</v>
<v>..\StrobeControlBootloader.cydsn\StrobeControlBootloader.cyprj</v>
<v>..\NoFXBootloader.cydsn\NoFXBootloader.cyprj</v>
<v>..\NoFXBoard_Bud.cydsn\NoFXBoard_Bud.cyprj</v>
</projects>
<workspace_id v="4bcdc549-45ed-4296-824b-4574346620f4" />
<WriteAppVersionLastSavedWith v="4.4.0.80" />
<WriteAppMarketingVersionLastSavedWith v="4.4" />
<CyGuid_dcbd9771-0334-43dc-9cc3-fe99dc3c5316 type_name="CyDesigner.Common.ProjMgmt.Model.CyPrjMgmtWorkspaceSerialize+CyPrjDependencyList" version="4">
<wrkspc_dependencies>
<wrkspc_dependency project="..\CarbonEthBootloader.cydsn\CarbonEthBootloader.cyprj" dependency="..\FreeRTOS_CM3_72MHz.cylib\FreeRTOS_CM3_72MHz.cyprj" />
<wrkspc_dependency project="..\Maka FreeRTOS Components.cylib\Maka FreeRTOS Components.cyprj" dependency="${CyRoot}\psoc\content\default\CyAnnotationLibrary\CyAnnotationLibrary.cylib\CyAnnotationLibrary.cyprj" />
<wrkspc_dependency project="..\Maka FreeRTOS Components.cylib\Maka FreeRTOS Components.cyprj" dependency="..\Maka Library.cylib\Maka Library.cyprj" />
<wrkspc_dependency project="..\Maka FreeRTOS Components.cylib\Maka FreeRTOS Components.cyprj" dependency="..\FreeRTOS_CM3_72MHz.cylib\FreeRTOS_CM3_72MHz.cyprj" />
<wrkspc_dependency project="..\NanoPB_Converter.cylib\NanoPB_Converter.cyprj" dependency="${CyRoot}\psoc\content\default\CyAnnotationLibrary\CyAnnotationLibrary.cylib\CyAnnotationLibrary.cyprj" />
<wrkspc_dependency project="..\NanoPB_Converter.cylib\NanoPB_Converter.cyprj" dependency="..\NanoPB_Proto.cylib\NanoPB_Proto.cyprj" />
<wrkspc_dependency project="..\NanoPB_Converter.cylib\NanoPB_Converter.cyprj" dependency="..\FreeRTOS_CM3_72MHz.cylib\FreeRTOS_CM3_72MHz.cyprj" />
<wrkspc_dependency project="..\NanoPB_Converter.cylib\NanoPB_Converter.cyprj" dependency="..\Maka FreeRTOS Components.cylib\Maka FreeRTOS Components.cyprj" />
<wrkspc_dependency project="..\NanoPB_Converter.cylib\NanoPB_Converter.cyprj" dependency="..\Maka Library.cylib\Maka Library.cyprj" />
<wrkspc_dependency project="..\NanoPB_Converter.cylib\NanoPB_Converter.cyprj" dependency="..\nanopb.cylib\nanopb.cyprj" />
<wrkspc_dependency project="..\Pulczar.cydsn\Pulczar.cyprj" dependency="..\FreeRTOS_CM3_72MHz.cylib\FreeRTOS_CM3_72MHz.cyprj" />
<wrkspc_dependency project="..\Pulczar.cydsn\Pulczar.cyprj" dependency="..\NanoPB_Converter.cylib\NanoPB_Converter.cyprj" />
<wrkspc_dependency project="..\Pulczar.cydsn\Pulczar.cyprj" dependency="..\NanoPB_Proto.cylib\NanoPB_Proto.cyprj" />
<wrkspc_dependency project="..\PulczarBootloader.cydsn\PulczarBootloader.cyprj" dependency="..\Maka FreeRTOS Components.cylib\Maka FreeRTOS Components.cyprj" />
<wrkspc_dependency project="..\Strobe Control Board.cydsn\Strobe Control Board.cyprj" dependency="..\NanoPB_Proto.cylib\NanoPB_Proto.cyprj" />
<wrkspc_dependency project="..\NoFXBoard_Bud.cydsn\NoFXBoard_Bud.cyprj" dependency="..\NanoPB_Converter.cylib\NanoPB_Converter.cyprj" />
</wrkspc_dependencies>
</CyGuid_dcbd9771-0334-43dc-9cc3-fe99dc3c5316>
<CyGuid_63b68103-67f5-4406-8da6-5c8625765b82 type_name="CyDesigner.Common.ProjMgmt.Model.CyPrjMgmtWorkspaceSerialize+CyIgnoredSystemDepsList" version="1">
<wrkspc_dependencies />
</CyGuid_63b68103-67f5-4406-8da6-5c8625765b82>
</CyGuid_2867d519-54d2-4c01-9830-c51cb08bc3dd>
</CyXmlSerializer>
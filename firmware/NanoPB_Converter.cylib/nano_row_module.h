/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#ifndef NANO_ROW_MODULE_H
#define NANO_ROW_MODULE_H

#include "generated/lib/drivers/nanopb/proto/row_module.pb.h"
#include "m_row_module_request.h"

void Nano_Convert_Row_Module_Request(row_module_Request *nano, M_Row_Module_Request_t *request,
                                     Request_Metadata_t *metadata);

#endif // NANO_ROW_MODULE_H
/* [] END OF FILE */

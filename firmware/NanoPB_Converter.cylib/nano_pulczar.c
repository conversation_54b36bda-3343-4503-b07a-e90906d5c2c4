/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "nano_pulczar.h"
#include "nano_dawg.h"
#include "nano_gimbal.h"
#include "nano_laser.h"
#include "nano_lens.h"

void Nano_Convert_Pulczar_Request(pulczar_Request *nano, M_Pulczar_Request_t *request, Request_Metadata_t *metadata) {
  switch (nano->which_request) {
  case pulczar_Request_reset_tag:
    request->request_type = PULCZAR_REQUEST_TYPE_RESET;
    break;
  case pulczar_Request_clear_tag:
    request->request_type = PULCZAR_REQUEST_TYPE_CLEAR_CONFIG;
    request->request.metadata = *metadata;
    break;
  case pulczar_Request_dawg_tag:
    request->request_type = PULCZAR_REQUEST_TYPE_DAWG;
    Nano_Convert_Dawg_Request(&nano->request.dawg, &request->request.dawg, metadata);
    break;
  case pulczar_Request_gimbal_tag:
    request->request_type = PULCZAR_REQUEST_TYPE_GIMBAL;
    Nano_Convert_Gimbal_Request(&nano->request.gimbal, &request->request.gimbal, metadata);
    break;
  case pulczar_Request_laser_tag:
    request->request_type = PULCZAR_REQUEST_TYPE_LASER;
    Nano_Convert_Laser_Request(&nano->request.laser, &request->request.laser, metadata);
    break;
  case pulczar_Request_lens_tag:
    request->request_type = PULCZAR_REQUEST_TYPE_LENS;
    Nano_Convert_Lens_Request(&nano->request.lens, &request->request.lens, metadata);
    break;
  case pulczar_Request_status_tag:
    request->request_type = PULCZAR_REQUEST_TYPE_STATUS;
    request->request.metadata = *metadata;
    break;
  case pulczar_Request_override_tag:
    request->request_type = PULCZAR_REQUEST_TYPE_OVERRIDE;
    request->request.override.metadata = *metadata;
    request->request.override.override = (uint8_t)nano->request.override.override;
    break;
  default:
    request->request_type = PULCZAR_REQUEST_TYPE_NONE;
    break;
  }
}

/* [] END OF FILE */

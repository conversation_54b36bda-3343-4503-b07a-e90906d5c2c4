/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#ifndef NANO_PULSAR_H
#define NANO_PULSAR_H

#include "generated/lib/drivers/nanopb/proto/pulczar.pb.h"
#include "m_pulczar_request.h"
#include "request_metadata.h"

void Nano_Convert_Pulczar_Request(pulczar_Request *nano, M_Pulczar_Request_t *request, Request_Metadata_t *metadata);

#endif // NANO_PULSAR_H
/* [] END OF FILE */

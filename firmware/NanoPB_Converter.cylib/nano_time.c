/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "nano_time.h"

inline void Nano_Convert_Time_Set_Request(time_Set_Epoch_Time_Request *nano, M_Time_Set_Epoch_Request_t *request) {
  request->timestamp.seconds = nano->timestamp.seconds;
  request->timestamp.micros = nano->timestamp.micros;
}

void Nano_Convert_Time_Request(time_Request *nano, M_Time_Request_t *request, Request_Metadata_t *metadata) {
  request->metadata = *metadata;
  switch (nano->which_request) {
  case time_Request_set_tag:
    request->request_type = TIME_REQUEST_TYPE_SET;
    Nano_Convert_Time_Set_Request(&nano->request.set, &request->request.set);
    break;
  case time_Request_get_tag:
    request->request_type = TIME_REQUEST_TYPE_GET;
    break;
  case time_Request_debug_tag:
    request->request_type = TIME_REQUEST_TYPE_DEBUG;
    break;
  default:
    request->request_type = TIME_REQUEST_TYPE_NONE;
    break;
  }
}

inline void Nano_Convert_Timestamp_Reply(M_Timestamp_t *reply, time_Timestamp *nano) {
  nano->seconds = reply->seconds;
  nano->micros = reply->micros;
}

inline void Nano_Convert_Time_Error_Reply(M_Error_Code_t *reply, error_Error *nano) { nano->code = *reply; }

inline void Nano_Convert_Time_Debug_Reply(M_Time_Debug_Reply_t *reply, time_Get_Debug_Timestamp_Reply *nano) {
  nano->has_timestamp = true;
  Nano_Convert_Timestamp_Reply(&reply->timestamp, &nano->timestamp);
  nano->pps_ticks = reply->pps_ticks;
  nano->pps_timer_val = reply->pps_timer_val;
  nano->freq_mul = reply->freq_mul;
  nano->error_ticks = reply->error_ticks;
  nano->error_ticks2 = reply->error_ticks2;
}

void Nano_Convert_Time_Reply(M_Time_Reply_t *reply, time_Reply *nano) {
  switch (reply->reply_type) {
  case TIME_REPLY_TYPE_ACK:
    nano->which_reply = time_Reply_ack_tag;
    break;
  case TIME_REPLY_TYPE_ERROR:
    nano->which_reply = time_Reply_error_tag;
    Nano_Convert_Time_Error_Reply(&reply->reply.error, &nano->reply.error);
    break;
  case TIME_REPLY_TYPE_TIMESTAMP:
    nano->which_reply = time_Reply_timestamp_tag;
    Nano_Convert_Timestamp_Reply(&reply->reply.timestamp, &nano->reply.timestamp);
    break;
  case TIME_REPLY_TYPE_DEBUG:
    nano->which_reply = time_Reply_debug_tag;
    Nano_Convert_Time_Debug_Reply(&reply->reply.debug, &nano->reply.debug);
    break;
  default:
    break;
  }
}

/* [] END OF FILE */

/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "nano_row_module.h"
#include "nano_dawg.h"
#include "nano_scanner.h"

inline void Nano_Convert_Row_Module_Scanner_Request(row_module_Scanner_Request *nano,
                                                    M_Row_Module_Scanner_Request_t *request,
                                                    Request_Metadata_t *metadata) {
  request->scanner_id = nano->scanner_id;
  if (nano->has_request) {
    Nano_Convert_Scanner_Request(&nano->request, &request->scanner, metadata->request_id);
  }
}

void Nano_Convert_Row_Module_Request(row_module_Request *nano, M_Row_Module_Request_t *request,
                                     Request_Metadata_t *metadata) {
  switch (nano->which_request) {
  case row_module_Request_reset_tag:
    request->request_type = ROW_MODULE_REQUEST_TYPE_RESET;
    break;
  case row_module_Request_clear_tag:
    request->request_type = ROW_MODULE_REQUEST_TYPE_CLEAR_CONFIG;
    request->request.clear.request_id = metadata->request_id;
    break;
  case row_module_Request_dawg_tag:
    request->request_type = ROW_MODULE_REQUEST_TYPE_DAWG;
    Nano_Convert_Dawg_Request(&nano->request.dawg, &request->request.dawg, metadata);
    break;
  case row_module_Request_scanner_tag:
    request->request_type = ROW_MODULE_REQUEST_TYPE_SCANNER;
    Nano_Convert_Row_Module_Scanner_Request(&nano->request.scanner, &request->request.scanner, metadata);
    break;
  default:
    request->request_type = ROW_MODULE_REQUEST_TYPE_NONE;
    break;
  }
}

/* [] END OF FILE */

/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "nano_servo.h"
#include "nano_epos.h"

inline void Nano_Convert_Servo_Config_Request(servo_Config_Request *nano, M_Servo_Config_Request_t *request) {
  if (nano->has_config) {
    request->config.max_profile_velocity = nano->config.max_profile_velocity;
    request->config.settle_timeout = nano->config.settle_timeout;
    request->config.settle_window = nano->config.settle_window;
  }
  request->node_id = nano->node_id;
}

inline void Nano_Convert_Servo_Go_To_Request(servo_Go_To_Request *nano, M_Servo_Go_To_Request_t *request) {
  request->position = nano->position;
  request->velocity = nano->velocity;
  request->await = nano->await_settle;
}

inline void Nano_Convert_Servo_Go_To_Delta_Request(servo_Go_To_Delta_Request *nano,
                                                   M_Servo_Go_To_Delta_Request_t *request) {
  request->delta_position = nano->delta_position;
  request->velocity = nano->velocity;
  switch (nano->mode) {
  case servo_GoToMode_IMMEDIATE:
    request->mode = SERVO_GO_TO_MODE_IMMEDIATE;
    break;
  case servo_GoToMode_REACHED:
    request->mode = SERVO_GO_TO_MODE_REACHED;
    break;
  case servo_GoToMode_SETTLED:
    request->mode = SERVO_GO_TO_MODE_SETTLED;
    break;
  default:
    request->mode = SERVO_GO_TO_MODE_IMMEDIATE;
    break;
  }
}

inline void Nano_Convert_Servo_Go_To_Delta_Follow_Request(servo_Go_To_Delta_Follow_Request *nano,
                                                          M_Servo_Go_To_Delta_Follow_Request_t *request) {
  Nano_Convert_Servo_Go_To_Delta_Request(&nano->delta, &request->delta);
  request->follow_velocity_vector = nano->follow_velocity_vector;
  request->follow_velocity_mrpm = nano->follow_velocity_mrpm;
  request->interval_sleep_time_ms = nano->interval_sleep_time_ms;
}

inline void Nano_Convert_Servo_Boot_Request(servo_Boot_Request *nano, M_Servo_Boot_Request_t *request) {
  if (nano->has_params) {
    Nano_Convert_Epos_Home_Params(&nano->params, &request->params);
  }
}

inline void Nano_Convert_Servo_Go_To_Calibrate(servo_Go_To_Calibrate_Request *nano,
                                               M_Servo_Go_To_Calibrate_Request_t *request) {
  request->position = nano->position;
  request->velocity = nano->velocity;
  request->window = nano->window;
  request->time_window_ms = nano->time_window_ms;
  request->timeout_ms = nano->timeout_ms;
  request->period_ms = nano->period_ms;
}

inline void Nano_Convert_Servo_Go_To_Timestamp(servo_Go_To_Timestamp_Request *nano,
                                               M_Servo_Go_To_Timestamp_Request_t *request) {
  request->timestamp.seconds = nano->timestamp.seconds;
  request->timestamp.micros = nano->timestamp.micros;
  switch (nano->mode) {
  case servo_GoToMode_IMMEDIATE:
    request->mode = SERVO_GO_TO_MODE_IMMEDIATE;
    break;
  case servo_GoToMode_REACHED:
    request->mode = SERVO_GO_TO_MODE_REACHED;
    break;
  case servo_GoToMode_SETTLED:
    request->mode = SERVO_GO_TO_MODE_SETTLED;
    break;
  default:
    request->mode = SERVO_GO_TO_MODE_IMMEDIATE;
    break;
  }
  request->position = nano->position;
  request->velocity_mrpm = nano->velocity_mrpm;
  request->follow_velocity = nano->follow_velocity;
  request->follow_accel = nano->follow_accel;
  request->interval_sleep_time_ms = nano->interval_sleep_time_ms;
}

inline void Nano_Convert_Servo_Follow_Timestamp(servo_Follow_Timestamp_Request *nano,
                                                M_Servo_Follow_Timestamp_Request_t *request) {
  request->timestamp.seconds = nano->timestamp.seconds;
  request->timestamp.micros = nano->timestamp.micros;
  request->follow_velocity = nano->follow_velocity;
  request->follow_accel = nano->follow_accel;
}

void Nano_Convert_Servo_Request(servo_Request *nano, M_Servo_Request_t *request) {
  switch (nano->which_request) {
  case servo_Request_config_tag:
    request->request_type = SERVO_REQUEST_TYPE_CONFIG;
    Nano_Convert_Servo_Config_Request(&nano->request.config, &request->request.config);
    break;
  case servo_Request_boot_tag:
    request->request_type = SERVO_REQUEST_TYPE_BOOT;
    Nano_Convert_Servo_Boot_Request(&nano->request.boot, &request->request.boot);
    break;
  case servo_Request_stop_tag:
    request->request_type = SERVO_REQUEST_TYPE_STOP;
    break;
  case servo_Request_go_to_tag:
    request->request_type = SERVO_REQUEST_TYPE_GO_TO;
    Nano_Convert_Servo_Go_To_Request(&nano->request.go_to, &request->request.go_to);
    break;
  case servo_Request_limit_tag:
    request->request_type = SERVO_REQUEST_TYPE_GET_LIMITS;
    break;
  case servo_Request_epos_tag:
    request->request_type = SERVO_REQUEST_TYPE_EPOS;
    Nano_Convert_Epos_Request(&nano->request.epos, &request->request.epos);
    break;
  case servo_Request_delta_tag:
    request->request_type = SERVO_REQUEST_TYPE_DELTA;
    Nano_Convert_Servo_Go_To_Delta_Request(&nano->request.delta, &request->request.delta);
    break;
  case servo_Request_follow_tag:
    request->request_type = SERVO_REQUEST_TYPE_DELTA_FOLLOW;
    Nano_Convert_Servo_Go_To_Delta_Follow_Request(&nano->request.follow, &request->request.follow);
    break;
  case servo_Request_calibrate_tag:
    request->request_type = SERVO_REQUEST_TYPE_GO_TO_CALIBRATE;
    Nano_Convert_Servo_Go_To_Calibrate(&nano->request.calibrate, &request->request.calibrate);
    break;
  case servo_Request_go_to_timestamp_tag:
    request->request_type = SERVO_REQUEST_TYPE_GO_TO_TIMESTAMP;
    Nano_Convert_Servo_Go_To_Timestamp(&nano->request.go_to_timestamp, &request->request.go_to_timestamp);
    break;
  case servo_Request_follow_timestamp_tag:
    request->request_type = SERVO_REQUEST_TYPE_FOLLOW_TIMESTAMP;
    Nano_Convert_Servo_Follow_Timestamp(&nano->request.follow_timestamp, &request->request.follow_timestamp);
    break;
  default:
    request->request_type = SERVO_REQUEST_TYPE_NONE;
    break;
  }
}

inline void Nano_Convert_Servo_Limits_Reply(M_Servo_Limits_Reply_t *reply, servo_Limits_Reply *nano) {
  nano->min = reply->min;
  nano->max = reply->max;
}

inline void Nano_Convert_Servo_Position_Reply(M_Servo_Position_Reply_t *reply, servo_Position_Reply *nano) {
  nano->position = reply->position;
}

inline void Nano_Convert_Servo_Settle_Time_Reply(M_Servo_Settle_Time_Reply_t *reply, servo_Settle_Time_Reply *nano) {
  nano->settle_time = reply->settle_time;
}

inline void Nano_Convert_Servo_Error_Reply(M_Error_Code_t *reply, error_Error *nano) { nano->code = *reply; }

inline void Nano_Convert_Servo_Go_To_Timestamp_Reply(M_Servo_Go_To_Timestamp_Reply_t *reply,
                                                     servo_Go_To_Timestamp_Reply *nano) {
  nano->pre_position = reply->pre_position;
  nano->post_position = reply->post_position;
  nano->has_pre_timestamp = true;
  nano->pre_timestamp.seconds = reply->pre_timestamp.seconds;
  nano->pre_timestamp.micros = reply->pre_timestamp.micros;
  nano->has_post_timestamp = true;
  nano->post_timestamp.seconds = reply->post_timestamp.seconds;
  nano->post_timestamp.micros = reply->post_timestamp.micros;
}

inline void Nano_Convert_Servo_Follow_Timestamp_Reply(M_Servo_Follow_Timestamp_Reply_t *reply,
                                                      servo_Follow_Timestamp_Reply *nano) {
  nano->pre_position = reply->pre_position;
  nano->has_pre_timestamp = true;
  nano->pre_timestamp.seconds = reply->pre_timestamp.seconds;
  nano->pre_timestamp.micros = reply->pre_timestamp.micros;
}

void Nano_Convert_Servo_Reply(M_Servo_Reply_t *reply, servo_Reply *nano) {
  switch (reply->reply_type) {
  case SERVO_REPLY_TYPE_ACK:
    nano->which_reply = servo_Reply_ack_tag;
    break;
  case SERVO_REPLY_TYPE_ERROR:
    nano->which_reply = servo_Reply_error_tag;
    Nano_Convert_Servo_Error_Reply(&reply->reply.error, &nano->reply.error);
    break;
  case SERVO_REPLY_TYPE_LIMITS:
    nano->which_reply = servo_Reply_limit_tag;
    Nano_Convert_Servo_Limits_Reply(&reply->reply.limits, &nano->reply.limit);
    break;
  case SERVO_REPLY_TYPE_EPOS:
    nano->which_reply = servo_Reply_epos_tag;
    Nano_Convert_Epos_Reply(&reply->reply.epos, &nano->reply.epos);
    break;
  case SERVO_REPLY_TYPE_POSITION:
    nano->which_reply = servo_Reply_pos_tag;
    Nano_Convert_Servo_Position_Reply(&reply->reply.pos, &nano->reply.pos);
    break;
  case SERVO_REPLY_TYPE_SETTLE_TIME:
    nano->which_reply = servo_Reply_settle_tag;
    Nano_Convert_Servo_Settle_Time_Reply(&reply->reply.settle, &nano->reply.settle);
    break;
  case SERVO_REPLY_TYPE_GO_TO_TIMESTAMP:
    nano->which_reply = servo_Reply_go_to_timestamp_tag;
    Nano_Convert_Servo_Go_To_Timestamp_Reply(&reply->reply.go_to_timestamp, &nano->reply.go_to_timestamp);
    break;
  case SERVO_REPLY_TYPE_FOLLOW_TIMESTAMP:
    nano->which_reply = servo_Reply_follow_timestamp_tag;
    Nano_Convert_Servo_Follow_Timestamp_Reply(&reply->reply.follow_timestamp, &nano->reply.follow_timestamp);
    break;
  default:
    break;
  }
}

/* [] END OF FILE */

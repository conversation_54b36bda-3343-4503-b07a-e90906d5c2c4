/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#ifndef NANO_SCANNER_H
#define NANO_SCANNER_H

#include "generated/lib/drivers/nanopb/proto/scanner.pb.h"
#include "m_scanner_request.h"

void Nano_Convert_Scanner_Request(scanner_Request *nano, M_Scanner_Request_t *request, uint16_t request_id);
void Nano_Convert_Scanner_Reply(M_Scanner_Reply_t *reply, scanner_Reply *nano);

#endif // NANO_SCANNER_H
/* [] END OF FILE */

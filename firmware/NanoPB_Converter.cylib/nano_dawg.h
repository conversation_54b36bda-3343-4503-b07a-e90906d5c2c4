/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#ifndef NANO_DAWG_H
#define NANO_DAWG_H

#include "generated/lib/drivers/nanopb/proto/dawg.pb.h"
#include "m_dawg_request.h"

void Nano_Convert_Dawg_Request(dawg_Request *nano, M_Dawg_Request_t *request, Request_Metadata_t *metadata);
void Nano_Convert_Dawg_Reply(M_Dawg_Reply_t *reply, dawg_Reply *nano);

#endif // NANO_DAWG_H
/* [] END OF FILE */

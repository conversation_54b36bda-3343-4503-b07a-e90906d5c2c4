/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#ifndef NANO_TIME_H
#define NANO_TIME_H

#include "generated/lib/drivers/nanopb/proto/time.pb.h"
#include "m_time_request.h"
#include "request_metadata.h"

void Nano_Convert_Time_Request(time_Request *nano, M_Time_Request_t *request, Request_Metadata_t *metadata);
void Nano_Convert_Time_Reply(M_Time_Reply_t *reply, time_Reply *nano);

#endif // NANO_TIME_H
/* [] END OF FILE */

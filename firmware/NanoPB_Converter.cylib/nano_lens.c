/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "nano_lens.h"

inline void Nano_Convert_Lens_Set_Request(lens_Set_Request *nano, M_Lens_Value_t *request) {
  request->value = nano->value;
}

void Nano_Convert_Lens_Request(lens_Request *nano, M_Lens_Request_t *request, Request_Metadata_t *metadata) {
  request->metadata = *metadata;
  switch (nano->which_request) {
  case lens_Request_set_tag:
    request->request_type = LENS_REQUEST_TYPE_SET;
    Nano_Convert_Lens_Set_Request(&nano->request.set, &request->request.set);
    break;
  case lens_Request_get_tag:
    request->request_type = LENS_REQUEST_TYPE_GET;
    break;
  default:
    request->request_type = LENS_REQUEST_TYPE_NONE;
    break;
  }
}

inline void Nano_Convert_Lens_Get_Reply(M_Lens_Value_t *reply, lens_Get_Reply *nano) { nano->value = reply->value; }

inline void Nano_Convert_Lens_Error_Reply(M_Error_Code_t *reply, error_Error *nano) { nano->code = *reply; }

void Nano_Convert_Lens_Reply(M_Lens_Reply_t *reply, lens_Reply *nano) {
  switch (reply->reply_type) {
  case LENS_REPLY_TYPE_ACK:
    nano->which_reply = lens_Reply_ack_tag;
    break;
  case LENS_REPLY_TYPE_ERROR:
    nano->which_reply = lens_Reply_error_tag;
    Nano_Convert_Lens_Error_Reply(&reply->reply.error, &nano->reply.error);
    break;
  case LENS_REPLY_TYPE_GET:
    nano->which_reply = lens_Reply_get_tag;
    Nano_Convert_Lens_Get_Reply(&reply->reply.get, &nano->reply.get);
    break;
  default:
    break;
  }
}

/* [] END OF FILE */

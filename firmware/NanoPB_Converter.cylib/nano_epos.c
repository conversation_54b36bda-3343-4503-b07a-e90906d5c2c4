/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "nano_epos.h"
#include "nano_can_open.h"
#include "stdint.h"
#include "string.h"

inline void Nano_Convert_Epos_Hard_Home_Params(epos_Hard_Home_Params *nano, M_Epos_Hard_Home_Params_t *params) {
  params->offset = nano->offset;
  params->step_size = nano->step_size;
}

inline void Nano_Convert_Epos_Switch_Home_Params(epos_Switch_Home_Params *nano, M_Epos_Switch_Home_Params_t *params) {
  params->threshold_step = nano->threshold_step;
  params->step_size = nano->step_size;
}

inline void Nano_Convert_Epos_Home_Params(epos_Home_Params *nano, M_Epos_Home_Params_t *params) {
  params->max_position = nano->max_position;
  params->min_position = nano->min_position;
  params->profile_velocity = nano->profile_velocity;

  switch (nano->which_params) {
  case epos_Home_Params_hard_stop_tag:
    params->param_type = M_EPOS_PARAM_TYPE_HARD;
    Nano_Convert_Epos_Hard_Home_Params(&nano->params.hard_stop, &params->params.hard_stop);
    break;
  case epos_Home_Params_limit_switch_tag:
    params->param_type = M_EPOS_PARAM_TYPE_SWITCH;
    Nano_Convert_Epos_Switch_Home_Params(&nano->params.limit_switch, &params->params.limit_switch);
    break;
  default:
    params->param_type = M_EPOS_PARAM_TYPE_NONE;
    break;
  }
}

inline void Nano_Convert_Epos_Home_Request(epos_Home_Request *nano, M_Epos_HomeRequest_t *request) {
  if (nano->has_params) {
    Nano_Convert_Epos_Home_Params(&nano->params, &request->params);
  }
}

inline void Nano_Convert_Epos_Await_Settling_Request(epos_Await_Settling_Request *nano,
                                                     M_Epos_AwaitSettlingRequest_t *request) {
  request->target_position = nano->target_position;
  request->window = nano->window;
  request->timeout_ms = nano->timeout_ms;
}

inline void Nano_Convert_Epos_Go_To_Request(epos_Go_To_Request *nano, M_Epos_GoToRequest_t *request) {
  request->position = nano->position;
  request->velocity = nano->velocity;
  request->window = nano->window;
  request->timeout_ms = nano->timeout_ms;
}

inline void Nano_Convert_Epos_Await_Status_Request(epos_Await_Status_Request *nano,
                                                   M_Epos_AwaitStatusRequest_t *request) {
  request->expected = nano->expected;
  request->expected_neg = nano->expected_neg;
  request->timeout_ms = nano->timeout_ms;
}
inline void Nano_Convert_Epos_Positional_PID_Request(epos_Set_Positional_PID_Request *nano,
                                                     M_Epos_SetPositionalPIDRequest_t *request) {
  request->gain_p = nano->gain_p;
  request->gain_i = nano->gain_i;
  request->gain_d = nano->gain_d;
  request->gain_ffv = nano->gain_ffv;
  request->gain_ffa = nano->gain_ffa;
}
inline void Nano_Convert_Epos_PID_Request(epos_Set_PID_Request *nano, M_Epos_SetPIDRequest_t *request) {
  request->pos_pid.gain_p = nano->positional_pid.gain_p;
  request->pos_pid.gain_i = nano->positional_pid.gain_i;
  request->pos_pid.gain_d = nano->positional_pid.gain_d;
  request->pos_pid.gain_ffv = nano->positional_pid.gain_ffv;
  request->pos_pid.gain_ffa = nano->positional_pid.gain_ffa;
  request->current_p = nano->current_p;
  request->current_i = nano->current_i;
}

void Nano_Convert_Epos_Request(epos_Request *nano, M_Epos_Request_t *request) {
  switch (nano->which_request) {
  case epos_Request_can_tag:
    request->request_type = M_EPOS_REQUEST_TYPE_CAN_OPEN;
    Nano_Convert_CAN_Open_Request(&nano->request.can, &request->request.can);
    break;
  case epos_Request_setup_pdos_tag:
    request->request_type = M_EPOS_REQUEST_TYPE_SETUP_PDOS;
    break;
  case epos_Request_enable_tag:
    request->request_type = M_EPOS_REQUEST_TYPE_ENABLE;
    break;
  case epos_Request_disable_tag:
    request->request_type = M_EPOS_REQUEST_TYPE_DISABLE;
    break;
  case epos_Request_home_tag:
    request->request_type = M_EPOS_REQUEST_TYPE_HOME;
    Nano_Convert_Epos_Home_Request(&nano->request.home, &request->request.home);
    break;
  case epos_Request_settle_tag:
    request->request_type = M_EPOS_REQUEST_TYPE_AWAIT_SETTLING;
    Nano_Convert_Epos_Await_Settling_Request(&nano->request.settle, &request->request.settle);
    break;
  case epos_Request_pos_vel_tag:
    request->request_type = M_EPOS_REQUEST_TYPE_GET_POS_VEL;
    break;
  case epos_Request_go_to_tag:
    request->request_type = M_EPOS_REQUEST_TYPE_GO_TO;
    Nano_Convert_Epos_Go_To_Request(&nano->request.go_to, &request->request.go_to);
    break;
  case epos_Request_status_tag:
    request->request_type = M_EPOS_REQUEST_TYPE_AWAIT_STATUS;
    Nano_Convert_Epos_Await_Status_Request(&nano->request.status, &request->request.status);
    break;
  case epos_Request_positional_pid_tag:
    request->request_type = M_EPOS_REQUEST_TYPE_POSITIONAL_PID;
    Nano_Convert_Epos_Positional_PID_Request(&nano->request.positional_pid, &request->request.positional_pid);
    break;
  case epos_Request_pid_tag:
    request->request_type = M_EPOS_REQUEST_TYPE_PID;
    Nano_Convert_Epos_PID_Request(&nano->request.pid, &request->request.pid);
    break;
  default:
    request->request_type = M_EPOS_REQUEST_TYPE_NONE;
    break;
  }
}

inline void Nano_Convert_Epos_Homing_Limit_Reply(int32_t limit, epos_Homing_Limit_Reply *nano) { nano->limit = limit; }

inline void Nano_Convert_Epos_Settling_Time_Reply(uint16_t duration, epos_Settling_Time_Reply *nano) {
  nano->duration = duration;
}

inline void Nano_Convert_Epos_Pos_Vel_Reply(M_Epos_PositionVelocityReply_t *reply, epos_Pos_Vel_Reply *nano) {
  nano->position = reply->position;
  nano->velocity = reply->velocity;
}

inline void Nano_Convert_Epos_Error_Reply(M_Error_Code_t *reply, error_Error *nano) { nano->code = *reply; }

void Nano_Convert_Epos_Reply(M_Epos_Reply_t *reply, epos_Reply *nano) {
  switch (reply->reply_type) {
  case M_EPOS_REPLY_TYPE_ACK:
    nano->which_reply = epos_Reply_ack_tag;
    break;
  case M_EPOS_REPLY_TYPE_CAN_OPEN:
    nano->which_reply = epos_Reply_can_tag;
    Nano_Convert_CAN_Open_Reply(&reply->reply.can, &nano->reply.can);
    break;
  case M_EPOS_REPLY_TYPE_HOMING_LIMIT:
    nano->which_reply = epos_Reply_limit_tag;
    Nano_Convert_Epos_Homing_Limit_Reply(reply->reply.limit, &nano->reply.limit);
    break;
  case M_EPOS_REPLY_TYPE_SETTLING_TIME:
    nano->which_reply = epos_Reply_settle_tag;
    Nano_Convert_Epos_Settling_Time_Reply(reply->reply.duration, &nano->reply.settle);
    break;
  case M_EPOS_REPLY_TYPE_POS_VEL:
    nano->which_reply = epos_Reply_pos_vel_tag;
    Nano_Convert_Epos_Pos_Vel_Reply(&reply->reply.pos_vel, &nano->reply.pos_vel);
    break;
  case M_EPOS_REPLY_TYPE_ERROR:
    nano->which_reply = epos_Reply_error_tag;
    Nano_Convert_Epos_Error_Reply(&reply->reply.error, &nano->reply.error);
    break;
  default:
    break;
  }
}

/* [] END OF FILE */

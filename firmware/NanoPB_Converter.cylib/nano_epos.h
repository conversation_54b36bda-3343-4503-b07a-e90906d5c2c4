/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#ifndef NANO_EPOS_H
#define NANO_EPOS_H

#include "generated/lib/drivers/nanopb/proto/epos.pb.h"
#include "m_epos_request.h"

void Nano_Convert_Epos_Request(epos_Request *nano, M_Epos_Request_t *request);
void Nano_Convert_Epos_Reply(M_Epos_Reply_t *reply, epos_Reply *nano);
void Nano_Convert_Epos_Home_Params(epos_Home_Params *nano, M_Epos_Home_Params_t *params);

#endif // NANO_EPOS_H
/* [] END OF FILE */

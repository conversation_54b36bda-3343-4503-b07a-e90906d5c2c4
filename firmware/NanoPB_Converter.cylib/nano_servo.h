/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#ifndef NANO_SERVO_H
#define NANO_SERVO_H

#include "generated/lib/drivers/nanopb/proto/servo.pb.h"
#include "m_servo_request.h"

void Nano_Convert_Servo_Request(servo_Request *nano, M_Servo_Request_t *request);
void Nano_Convert_Servo_Reply(M_Servo_Reply_t *reply, servo_Reply *nano);

#endif // NANO_SERVO_H
/* [] END OF FILE */

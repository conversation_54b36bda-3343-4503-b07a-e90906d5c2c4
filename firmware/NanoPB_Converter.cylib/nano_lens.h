/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#ifndef NANO_LENS_H
#define NANO_LENS_H

#include "generated/lib/drivers/nanopb/proto/lens.pb.h"
#include "m_lens_request.h"
#include "request_metadata.h"

void Nano_Convert_Lens_Request(lens_Request *nano, M_Lens_Request_t *request, Request_Metadata_t *metadata);
void Nano_Convert_Lens_Reply(M_Lens_Reply_t *reply, lens_Reply *nano);

#endif // NANO_LENS_H
/* [] END OF FILE */

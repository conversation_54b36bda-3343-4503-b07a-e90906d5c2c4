/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#ifndef NANO_LASER_H
#define NANO_LASER_H

#include "generated/lib/drivers/nanopb/proto/laser.pb.h"
#include "m_laser_request.h"
#include "request_metadata.h"

void Nano_Convert_Laser_Request(laser_Request *nano, M_Laser_Request_t *request, Request_Metadata_t *metadata);
void Nano_Convert_Laser_Reply(M_Laser_Reply_t *reply, laser_Reply *nano);

#endif // NANO_LASER_H
/* [] END OF FILE */

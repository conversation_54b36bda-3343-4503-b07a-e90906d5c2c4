/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#ifndef NANO_GIMBAL_H
#define NANO_GIMBAL_H

#include "generated/lib/drivers/nanopb/proto/gimbal.pb.h"
#include "m_gimbal_request.h"
#include "request_metadata.h"

void Nano_Convert_Gimbal_Request(gimbal_Request *nano, M_Gimbal_Request_t *request, Request_Metadata_t *metadata);
void Nano_Convert_Gimbal_Reply(M_Gimbal_Reply_t *reply, gimbal_Reply *nano);

#endif // NANO_GIMBAL_H
/* [] END OF FILE */

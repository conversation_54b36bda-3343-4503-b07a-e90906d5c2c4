/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "nano_laser.h"

inline void Nano_Convert_Laser_Set_Request(laser_Laser_Request *nano, M_Laser_State_t *request) {
  request->on = nano->on;
}

inline void Nano_Convert_Laser_Intensity_Request(laser_Intensity_Request *nano, M_Laser_Intensity_Request_t *request) {
  request->intensity = nano->intensity;
}

void Nano_Convert_Laser_Request(laser_Request *nano, M_Laser_Request_t *request, Request_Metadata_t *metadata) {
  request->metadata = *metadata;
  switch (nano->which_request) {
  case laser_Request_laser_tag:
    request->request_type = LASER_REQUEST_TYPE_SET;
    Nano_Convert_Laser_Set_Request(&nano->request.laser, &request->request.laser);
    break;
  case laser_Request_get_laser_tag:
    request->request_type = LASER_REQUEST_TYPE_GET;
    break;
  case laser_Request_intensity_tag:
    request->request_type = LASER_REQUEST_TYPE_INTENSITY;
    Nano_Convert_Laser_Intensity_Request(&nano->request.intensity, &request->request.intensity);
    break;
  default:
    request->request_type = LASER_REQUEST_TYPE_NONE;
    break;
  }
}

inline void Nano_Convert_Laser_State_Reply(M_Laser_State_t *reply, laser_Laser_State_Reply *nano) {
  nano->on = reply->on;
}

inline void Nano_Convert_Laser_Error_Reply(M_Error_Code_t *reply, error_Error *nano) { nano->code = *reply; }

inline void Nano_Convert_Laser_Reply_Reply(M_Laser_Reply_Laser_Reply *reply, laser_Laser_Reply *nano) {
  nano->raw_therm1_reading_mv = reply->raw_therm1_reading_mv;
  nano->raw_therm2_reading_mv = reply->raw_therm2_reading_mv;
  nano->on = reply->on;
  nano->lpsu_state = reply->lpsu_state;
  nano->fireable = reply->fireable;
}

void Nano_Convert_Laser_Reply(M_Laser_Reply_t *reply, laser_Reply *nano) {
  switch (reply->reply_type) {
  case LASER_REPLY_TYPE_ACK:
    nano->which_reply = laser_Reply_ack_tag;
    break;
  case LASER_REPLY_TYPE_ERROR:
    nano->which_reply = laser_Reply_error_tag;
    Nano_Convert_Laser_Error_Reply(&reply->reply.error, &nano->reply.error);
    break;
  case LASER_REPLY_TYPE_LASER_STATE:
    nano->which_reply = laser_Reply_laser_tag;
    Nano_Convert_Laser_State_Reply(&reply->reply.laser, &nano->reply.laser);
    break;
  case LASER_REPLY_TYPE_LASER_REPLY:
    nano->which_reply = laser_Reply_laser_reply_tag;
    Nano_Convert_Laser_Reply_Reply(&reply->reply.laser_reply, &nano->reply.laser_reply);
    break;
  default:
    break;
  }
}

/* [] END OF FILE */

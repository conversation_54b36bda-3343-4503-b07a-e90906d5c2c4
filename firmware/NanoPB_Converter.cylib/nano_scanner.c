/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "nano_scanner.h"
#include "nano_epos.h"
#include "nano_servo.h"

inline void Nano_Convert_Scanner_Laser_Request(scanner_Laser_Request *nano, M_Scanner_Laser_State_t *request) {
  request->on = nano->on;
}

inline void Nano_Convert_Scanner_Intensity_Request(scanner_Intensity_Request *nano,
                                                   M_Scanner_Intensity_Request_t *request) {
  request->intensity = nano->intensity;
}

inline void Nano_Convert_Scanner_Gimbal_Request(scanner_Gimbal_Request *nano, M_Scanner_Gimbal_Request_t *request) {
  request->pan.request_type = SERVO_REQUEST_TYPE_NONE;
  request->tilt.request_type = SERVO_REQUEST_TYPE_NONE;
  if (nano->has_pan) {
    Nano_Convert_Servo_Request(&nano->pan, &request->pan);
  }
  if (nano->has_tilt) {
    Nano_Convert_Servo_Request(&nano->tilt, &request->tilt);
  }
}

inline void Nano_Convert_Scanner_Boot_Request(scanner_Boot_Request *nano, M_Scanner_Boot_Request_t *request) {
  if (nano->has_pan_params) {
    Nano_Convert_Epos_Home_Params(&nano->pan_params, &request->pan_params);
  }
  if (nano->has_tilt_params) {
    Nano_Convert_Epos_Home_Params(&nano->tilt_params, &request->tilt_params);
  }
}

void Nano_Convert_Scanner_Request(scanner_Request *nano, M_Scanner_Request_t *request, uint16_t request_id) {
  request->request_id = request_id;
  switch (nano->which_request) {
  case scanner_Request_laser_tag:
    request->request_type = SCANNER_REQUEST_TYPE_LASER;
    Nano_Convert_Scanner_Laser_Request(&nano->request.laser, &request->request.laser);
    break;
  case scanner_Request_get_laser_tag:
    request->request_type = SCANNER_REQUEST_TYPE_GET_LASER;
    break;
  case scanner_Request_boot_tag:
    request->request_type = SCANNER_REQUEST_TYPE_BOOT;
    Nano_Convert_Scanner_Boot_Request(&nano->request.boot, &request->request.boot);
    break;
  case scanner_Request_stop_tag:
    request->request_type = SCANNER_REQUEST_TYPE_STOP;
    break;
  case scanner_Request_gimbal_tag:
    request->request_type = SCANNER_REQUEST_TYPE_GIMBAL;
    Nano_Convert_Scanner_Gimbal_Request(&nano->request.gimbal, &request->request.gimbal);
    break;
  case scanner_Request_intensity_tag:
    request->request_type = SCANNER_REQUEST_TYPE_INTENSITY;
    Nano_Convert_Scanner_Intensity_Request(&nano->request.intensity, &request->request.intensity);
    break;
  default:
    request->request_type = SCANNER_REQUEST_TYPE_NONE;
    break;
  }
}

inline void Nano_Convert_Scanner_Laser_State_Reply(M_Scanner_Laser_State_t *reply, scanner_Laser_State_Reply *nano) {
  nano->on = reply->on;
}

inline void Nano_Convert_Scanner_Gimbal_Reply(M_Scanner_Gimbal_Reply_t *reply, scanner_Gimbal_Reply *nano) {
  nano->has_pan = true;
  Nano_Convert_Servo_Reply(&reply->pan, &nano->pan);
  nano->has_tilt = true;
  Nano_Convert_Servo_Reply(&reply->tilt, &nano->tilt);
}

void Nano_Convert_Scanner_Reply(M_Scanner_Reply_t *reply, scanner_Reply *nano) {
  switch (reply->reply_type) {
  case SCANNER_REPLY_TYPE_ACK:
    nano->which_reply = scanner_Reply_ack_tag;
    break;
  case SCANNER_REPLY_TYPE_ERROR:
    nano->which_reply = scanner_Reply_error_tag;
    break;
  case SCANNER_REPLY_TYPE_LASER_STATE:
    nano->which_reply = scanner_Reply_laser_tag;
    Nano_Convert_Scanner_Laser_State_Reply(&reply->reply.laser, &nano->reply.laser);
    break;
  case SCANNER_REPLY_TYPE_GIMBAL:
    nano->which_reply = scanner_Reply_gimbal_tag;
    Nano_Convert_Scanner_Gimbal_Reply(&reply->reply.gimbal, &nano->reply.gimbal);
    break;
  default:
    break;
  }
}

/* [] END OF FILE */

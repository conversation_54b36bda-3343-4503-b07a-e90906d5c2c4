/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#ifndef NANO_CAN_OPEN_H
#define NANO_CAN_OPEN_H

#include "generated/lib/drivers/nanopb/proto/can_open.pb.h"
#include "m_can_request.h"

void Nano_Convert_CAN_Open_Request(can_open_Request *nano, M_CAN_Request_t *request);
void Nano_Convert_CAN_Open_Reply(M_CAN_Reply_t *reply, can_open_Reply *nano);

#endif // NANO_CAN_OPEN_H
/* [] END OF FILE */

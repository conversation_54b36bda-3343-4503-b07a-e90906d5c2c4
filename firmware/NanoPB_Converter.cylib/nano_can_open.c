/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "nano_can_open.h"
#include "string.h"

inline void Nano_Convert_CAN_Open_SDO_Request(can_open_SDO_Request *nano, M_CAN_SDO_Request_t *request) {
  request->index = nano->index;
  request->subindex = nano->index;
  request->value = nano->value;
  request->cs = nano->cs;
  request->expedited = nano->expedited;
}

inline void Nano_Convert_CAN_Open_PDO_Request(can_open_PDO_Request *nano, M_CAN_PDO_Request_t *request) {
  request->func = nano->func;
  request->size = nano->size;
  memcpy(request->data, nano->data, 8);
}

inline void Nano_Convert_CAN_Open_RTR_PDO_Request(can_open_RTR_PDO_Request *nano, M_CAN_RTR_PDO_Request_t *request) {
  request->func = nano->func;
}

inline void Nano_Convert_CAN_Open_NMT_Request(can_open_NMT_Request *nano, M_CAN_NMT_Request_t *request) {
  request->state = nano->state;
}

inline void Nano_Convert_CAN_Open_Await_Request(can_open_Await_Request *nano, M_CAN_Await_Request_t *request) {
  request->timeout_ms = nano->timeout_ms;
  request->func = nano->func;
}

inline void Nano_Convert_CAN_Open_SDO_Download_Request(can_open_SDO_Download_Request *nano,
                                                       M_CAN_SDO_Download_Request_t *request) {
  request->index = nano->index;
  request->subindex = nano->subindex;
  request->value = nano->value;
}

inline void Nano_Convert_CAN_Open_SDO_Upload_Request(can_open_SDO_Upload_Request *nano,
                                                     M_CAN_SDO_Upload_Request_t *request) {
  request->index = nano->index;
  request->subindex = nano->subindex;
}

void Nano_Convert_CAN_Open_Request(can_open_Request *nano, M_CAN_Request_t *request) {
  switch (nano->which_request) {
  case can_open_Request_sdo_tag:
    request->request_type = M_CAN_REQUEST_TYPE_SDO;
    Nano_Convert_CAN_Open_SDO_Request(&nano->request.sdo, &request->request.sdo);
    break;
  case can_open_Request_pdo_tag:
    request->request_type = M_CAN_REQUEST_TYPE_PDO;
    Nano_Convert_CAN_Open_PDO_Request(&nano->request.pdo, &request->request.pdo);
    break;
  case can_open_Request_rtr_tag:
    request->request_type = M_CAN_REQUEST_TYPE_RTR_PDO;
    Nano_Convert_CAN_Open_RTR_PDO_Request(&nano->request.rtr, &request->request.rtr);
    break;
  case can_open_Request_nmt_tag:
    request->request_type = M_CAN_REQUEST_TYPE_NMT;
    Nano_Convert_CAN_Open_NMT_Request(&nano->request.nmt, &request->request.nmt);
    break;
  case can_open_Request_await_tag:
    request->request_type = M_CAN_REQUEST_TYPE_AWAIT_REPLY;
    Nano_Convert_CAN_Open_Await_Request(&nano->request.await, &request->request.await);
    break;
  case can_open_Request_sdo_download_tag:
    request->request_type = M_CAN_REQUEST_TYPE_SDO_DOWNLOAD;
    Nano_Convert_CAN_Open_SDO_Download_Request(&nano->request.sdo_download, &request->request.sdo_download);
    break;
  case can_open_Request_sdo_upload_tag:
    request->request_type = M_CAN_REQUEST_TYPE_SDO_UPLOAD;
    Nano_Convert_CAN_Open_SDO_Upload_Request(&nano->request.sdo_upload, &request->request.sdo_upload);
    break;
  case can_open_Request_reset_tag:
    request->request_type = M_CAN_REQUEST_TYPE_NMT_RESET;
    break;
  case can_open_Request_start_tag:
    request->request_type = M_CAN_REQUEST_TYPE_NMT_START;
    break;
  case can_open_Request_stop_tag:
    request->request_type = M_CAN_REQUEST_TYPE_NMT_STOP;
    break;
  default:
    request->request_type = M_CAN_REQUEST_TYPE_NONE;
    break;
  }
}

inline void Nano_Convert_CAN_Open_SDO_Packet_Reply(CAN_Open_SDO_packet_t *packet, can_open_SDO_Packet *nano) {
  nano->spec = packet->spec;
  nano->index = packet->index;
  nano->subindex = packet->subindex;
  memcpy(nano->data, packet->data, CAN_OPEN_SDO_PKT_DATA_SIZE);
}

inline void Nano_Convert_CAN_Open_PDO_Packet_Reply(CAN_Open_PDO_packet_t *packet, can_open_PDO_Packet *nano) {
  memcpy(nano->data, packet->data, CAN_OPEN_PDO_PKT_DATA_SIZE);
}

inline void Nano_Convert_CAN_Open_NMT_Packet_Reply(CAN_Open_NMT_packet_t *packet, can_open_NMT_Packet *nano) {
  nano->state = packet->state;
  nano->node_id = packet->node_id;
}

inline void Nano_Convert_CAN_Open_Message_Reply(CAN_Open_Message_t *msg, can_open_Message_Reply *nano) {
  nano->func = msg->func;
  nano->node_id = msg->node_id;
  switch (msg->pkt_type) {
  case CAN_OPEN_SDO_PKT_TYPE:
    nano->which_pkt = can_open_Message_Reply_sdo_tag;
    Nano_Convert_CAN_Open_SDO_Packet_Reply(&msg->pkt.sdo, &nano->pkt.sdo);
    break;
  case CAN_OPEN_PDO_PKT_TYPE:
    nano->which_pkt = can_open_Message_Reply_pdo_tag;
    Nano_Convert_CAN_Open_PDO_Packet_Reply(&msg->pkt.pdo, &nano->pkt.pdo);
    break;
  case CAN_OPEN_NMT_PKT_TYPE:
    nano->which_pkt = can_open_Message_Reply_nmt_tag;
    Nano_Convert_CAN_Open_NMT_Packet_Reply(&msg->pkt.nmt, &nano->pkt.nmt);
    break;
  default:
    break;
  }
}

inline void Nano_Convert_CAN_Open_Error_Reply(M_Error_Code_t *reply, error_Error *nano) { nano->code = *reply; }

void Nano_Convert_CAN_Open_Reply(M_CAN_Reply_t *reply, can_open_Reply *nano) {
  switch (reply->reply_type) {
  case M_CAN_REPLY_TYPE_ACK:
    nano->which_reply = can_open_Reply_ack_tag;
    break;
  case M_CAN_REPLY_TYPE_MESSAGE:
    nano->which_reply = can_open_Reply_msg_tag;
    Nano_Convert_CAN_Open_Message_Reply(&reply->reply.msg, &nano->reply.msg);
    break;
  case M_CAN_REPLY_TYPE_ERROR:
    nano->which_reply = can_open_Reply_error_tag;
    Nano_Convert_CAN_Open_Error_Reply(&reply->reply.error, &nano->reply.error);
    break;
  default:
    break;
  }
}

/* [] END OF FILE */

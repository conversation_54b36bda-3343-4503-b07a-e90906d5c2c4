/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "nano_gimbal.h"
#include "nano_epos.h"
#include "nano_servo.h"

inline void Nano_Convert_Gimbal_Servos_Request(gimbal_Servos_Request *nano, M_Gimbal_Servos_Request_t *request) {
  request->pan.request_type = SERVO_REQUEST_TYPE_NONE;
  request->tilt.request_type = SERVO_REQUEST_TYPE_NONE;
  if (nano->has_pan) {
    Nano_Convert_Servo_Request(&nano->pan, &request->pan);
  }
  if (nano->has_tilt) {
    Nano_Convert_Servo_Request(&nano->tilt, &request->tilt);
  }
}

inline void Nano_Convert_Gimbal_Boot_Request(gimbal_Boot_Request *nano, M_Gimbal_Boot_Request_t *request) {
  if (nano->has_pan_params) {
    Nano_Convert_Epos_Home_Params(&nano->pan_params, &request->pan_params);
  }
  if (nano->has_tilt_params) {
    Nano_Convert_Epos_Home_Params(&nano->tilt_params, &request->tilt_params);
  }
}

void Nano_Convert_Gimbal_Request(gimbal_Request *nano, M_Gimbal_Request_t *request, Request_Metadata_t *metadata) {
  request->metadata = *metadata;
  switch (nano->which_request) {
  case gimbal_Request_boot_tag:
    request->request_type = GIMBAL_REQUEST_TYPE_BOOT;
    Nano_Convert_Gimbal_Boot_Request(&nano->request.boot, &request->request.boot);
    break;
  case gimbal_Request_stop_tag:
    request->request_type = GIMBAL_REQUEST_TYPE_STOP;
    break;
  case gimbal_Request_servos_tag:
    request->request_type = GIMBAL_REQUEST_TYPE_SERVOS;
    Nano_Convert_Gimbal_Servos_Request(&nano->request.servos, &request->request.servos);
    break;
  default:
    request->request_type = GIMBAL_REQUEST_TYPE_NONE;
    break;
  }
}

inline void Nano_Convert_Gimbal_Servos_Reply(M_Gimbal_Servos_Reply_t *reply, gimbal_Servos_Reply *nano) {
  nano->has_pan = true;
  Nano_Convert_Servo_Reply(&reply->pan, &nano->pan);
  nano->has_tilt = true;
  Nano_Convert_Servo_Reply(&reply->tilt, &nano->tilt);
}

inline void Nano_Convert_Gimbal_Error_Reply(M_Error_Code_t *reply, error_Error *nano) { nano->code = *reply; }

void Nano_Convert_Gimbal_Reply(M_Gimbal_Reply_t *reply, gimbal_Reply *nano) {
  switch (reply->reply_type) {
  case GIMBAL_REPLY_TYPE_ACK:
    nano->which_reply = gimbal_Reply_ack_tag;
    break;
  case GIMBAL_REPLY_TYPE_ERROR:
    nano->which_reply = gimbal_Reply_error_tag;
    Nano_Convert_Gimbal_Error_Reply(&reply->reply.error, &nano->reply.error);
    break;
  case GIMBAL_REPLY_TYPE_SERVOS:
    nano->which_reply = gimbal_Reply_servos_tag;
    Nano_Convert_Gimbal_Servos_Reply(&reply->reply.servos, &nano->reply.servos);
    break;
  default:
    break;
  }
}

/* [] END OF FILE */

/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "nano_dawg.h"

inline void Nano_Convert_Dawg_Config_Request(dawg_Config_Request *nano, M_Dawg_Config_t *request) {
  request->timeout_ms = nano->timeout_ms;
}

inline void Nano_Convert_Dawg_Arm_Request(dawg_Arm_Request *nano, M_Dawg_Arming_Request_t *request) {
  request->armed = nano->armed;
}

void Nano_Convert_Dawg_Request(dawg_Request *nano, M_Dawg_Request_t *request, Request_Metadata_t *metadata) {
  request->metadata = *metadata;
  switch (nano->which_request) {
  case dawg_Request_config_tag:
    request->request_type = DAWG_REQUEST_TYPE_CONFIG;
    Nano_Convert_Dawg_Config_Request(&nano->request.config, &request->request.config);
    break;
  case dawg_Request_arm_tag:
    request->request_type = DAWG_REQUEST_TYPE_ARM;
    Nano_Convert_Dawg_Arm_Request(&nano->request.arm, &request->request.arming);
    break;
  case dawg_Request_pet_tag:
    request->request_type = DAWG_REQUEST_TYPE_PET;
    break;
  case dawg_Request_state_tag:
    request->request_type = DAWG_REQUEST_TYPE_STATE;
    break;
  default:
    request->request_type = DAWG_REQUEST_TYPE_NONE;
    break;
  }
}

inline void Nano_Convert_Dawg_State_Reply(M_Dawg_State_Reply_t *reply, dawg_State_Reply *nano) {
  nano->armed = reply->armed;
  nano->petted = reply->petted;
}

inline void Nano_Convert_Dawg_Error_Reply(M_Error_Code_t *reply, error_Error *nano) { nano->code = *reply; }

void Nano_Convert_Dawg_Reply(M_Dawg_Reply_t *reply, dawg_Reply *nano) {
  switch (reply->reply_type) {
  case DAWG_REPLY_TYPE_ACK:
    nano->which_reply = dawg_Reply_ack_tag;
    break;
  case DAWG_REPLY_TYPE_ERROR:
    nano->which_reply = dawg_Reply_error_tag;
    Nano_Convert_Dawg_Error_Reply(&reply->reply.error, &nano->reply.error);
    break;
  case DAWG_REPLY_TYPE_STATE:
    nano->which_reply = dawg_Reply_state_tag;
    Nano_Convert_Dawg_State_Reply(&reply->reply.state, &nano->reply.state);
    break;
  default:
    break;
  }
}

/* [] END OF FILE */

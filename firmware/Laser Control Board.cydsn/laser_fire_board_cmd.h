
// Generated Code. Do not modify.
#include "project.h"
#include "leb128.h"
#include "stop.h"
#ifndef MAKA_laser_fire_board_cmd_H
#define MAKA_laser_fire_board_cmd_H
#pragma pack(1)
    typedef enum {
        LASER_FIRE_BOARD_CMD_AS_DISARM = 1,
        LASER_FIRE_BOARD_CMD_AS_ARM = 2,
        LASER_FIRE_BOARD_CMD_AS_SET_INTENSITY = 3
} _tag_laser_fire_board_cmd;
typedef struct {
        uint8_t scanner_no;
        uint16_t intensity;
} set_intensity;
typedef struct {
        _tag_laser_fire_board_cmd tag;
        union {
                set_intensity as_set_intensity;
        } val;
} laser_fire_board_cmd;
void laser_fire_board_cmd_as_disarm(void* ctx);
void laser_fire_board_cmd_as_arm(void* ctx);
void laser_fire_board_cmd_as_set_intensity(set_intensity* val, void* ctx);


inline void demux_laser_fire_board_cmd(laser_fire_board_cmd *tu, void* ctx) {
    switch(tu->tag) {

        case LASER_FIRE_BOARD_CMD_AS_DISARM:
                laser_fire_board_cmd_as_disarm(ctx);
                break;
        case LASER_FIRE_BOARD_CMD_AS_ARM:
                laser_fire_board_cmd_as_arm(ctx);
                break;
        case LASER_FIRE_BOARD_CMD_AS_SET_INTENSITY:
                laser_fire_board_cmd_as_set_intensity(&(tu->val.as_set_intensity), ctx);
                break;
        default:
            stop("Unknown tag for laser_fire_board_cmd");
    }
}

#pragma pack()
#endif // MAKA_laser_fire_board_cmd_H

/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#include "project.h"
#include "laser_fire_board_cmd.h"

void stop(const char *fmt, ...) {
    // TODO this
    while(1) { // Prepare for a visit from Mr. Resetti.
        LED_Write(1);
        CyDelay(300);
        LED_Write(0);
        CyDelay(300);
    }
}

void laser_fire_board_cmd_as_disarm(void* ctx) {
    Arm_Write(0);
}

void laser_fire_board_cmd_as_arm(void* ctx) {
    Arm_Write(0xFF);
}

void laser_fire_board_cmd_as_set_intensity(set_intensity* val, void* ctx) {
    if (val->intensity) {
        Fire_Reg_Write(Fire_Reg_Read() | (1 << val->scanner_no));
        M_I2CDAC_SetChannelValue(val->scanner_no, val->intensity);
    } else {
        Fire_Reg_Write(Fire_Reg_Read() & ~(1 << val->scanner_no));
        M_I2CDAC_SetChannelValue(val->scanner_no, 0);
    }
}

void Protocol_Receive_Callback(uint8_t* pkt, size_t size) {
    // size isn't used here cause packet structure determines length
    demux_laser_fire_board_cmd((laser_fire_board_cmd*)pkt, NULL);
}

int main(void) {
    CyGlobalIntEnable; 
    Protocol_Start();
    M_I2CDAC_Start();

    LED_Write(1);
    CyDelay(1000);
    LED_Write(0);
    CyDelay(1000);

    for(;;) {
        Protocol_Loop();
    }
}

/* [] END OF FILE */

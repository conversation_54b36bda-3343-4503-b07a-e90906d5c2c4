/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#include "project.h"
#include "protocol.h"
#include "canopen.h"
#include "utils.h"


/**
 * Fault handler. Just stops the processor.
 *
 * @param[in] Unused.
 */
void stop(const char *fmt, ...)
{
    UNUSED(fmt);
    CyHalt(0);
}

/**
 * CAN receive ISR. To be fleshed out in the future.
 */
void recv_isr()
{
    Pin_1_Write(!Pin_1_Read());
}

/**
 * CAN ISR handler, for all CAN related interrupts.
 * De-multiplexes the interrupts and calls the appropriate handler functions.
 */
CY_ISR(CAN_ISR)
{
    // Multiplex tx/rx isrs and clear the interrupt pending bit
    if (CAN_INT_SR_REG.byte[1u] & CAN_RX_MESSAGE_MASK)
    {
        recv_isr();
        CAN_INT_SR_REG.byte[1u] = CAN_RX_MESSAGE_MASK;
    }
}

/**
 * Converts the contents of a message received from the USB side into one that
 * is emitted on the CAN side, with all the correct addressing bits packed.
 *
 * @param val[in]   The message to send over CAN along with all its metadata.
 * @param ctx[in]   Unused.
 */
void hfx_control_cmd_as_send_message(const send_message* val, const void* ctx)
{
    UNUSED(ctx);

    CAN_TX_MSG message;
    // J1939 message ID is defined as:
    // +-------------------------------------------------------------+
    // | Priority (3 bits) | PGN (18 bits) | Source Address (8 bits) |
    // +-------------------------------------------------------------+
    //
    // See https://copperhilltech.com/a-brief-introduction-to-the-sae-j1939-protocol/ for more.

    // We pad the high order bits as 0s. This is confirmed to work correctly
    // using a CAN bus sniffer.
    message.id = (uint32_t)(val->priority & 0x7) << 26;
    message.id |= (val->pgn & 0x3FFFF) << 8;
    message.id |= val->src_addr;

    // Remote transmission request
    // RTR must be dominant (0) for all transmitted data frames,
    // and recessive (1) for remote request frames.
    // The RTR bit is **always** 0 for J1939.
    // See https://www.kvaser.com/about-can/higher-layer-protocols/j1939-introduction/ for more J1939 details.
    message.rtr = 0;

    // Identifier extension bit
    // Must be 1 to indicate that we're using a 29 bit
    // identifier instead of an 11 bit one.
    message.ide = 1;

    // Data Length Code
    // The size of the data message we're going to send, in bytes. Cannot be more than J1939_DATA_LENGTH.
    if (val->length > J1939_DATA_LENGTH)
    {
        // No need to halt the chip and force a reboot, this should be an easily correctable error
        // on the client side.
        return;
    }
    message.dlc = val->length;

    // Whether we want a transmit interrupt to fire. It's not useful here, so turning it off.
    message.irq = false;

    // Now fill in the message body. The message struct needs an external buffer to
    // actually hold the messages, so we allocate one here on the stack. It is
    // guaranteed to not go out of scope because the CAN_SendMsg function is blocking.
    uint8_t msgbuf[CANOPEN_DATA_LENGTH];
    message.msg = (CAN_DATA_BYTES_MSG *)msgbuf;

    // Make sure we're not about to send some random uninitialized memory, then send the message!
    memset(message.msg->byte, 0, CANOPEN_DATA_LENGTH);
    memcpy(message.msg->byte, val->data, val->length);
    if (!(CAN_SendMsg(&message) == CYRET_SUCCESS))
    {
        // Simple diagnostics to show if there was a fault sending the CAN message.
        Pin_1_Write(1);
    }
    else
    {
        Pin_1_Write(0);
    }
}

/**
 * Callback on USB data packet being received.
 *
 * @param pkt[in]       The USB packet contents buffer.
 * @param size[in]      The size of the packet buffer.
 */
void M_ProtoUSB_Receive_Callback(const uint8_t* pkt, const size_t size)
{
    if (size == sizeof(hfx_control_cmd))
    {
        // We can't be totally sure it's safe to cast the packet contents to an
        // hfx_control_cmd struct, but if the size of the message matches the
        // size of the struct it's at least some reassurance.
        demux_hfx_control_cmd((hfx_control_cmd *)pkt, NULL);
    }
}

/**
 * Main entry point.
 *
 * @return 0 on success. Should never return.
 */
int main(void)
{
    // Enable global interrupts.
    CyGlobalIntEnable;

    // Initialize all the components.
    CyIntSetVector(CAN_ISR_NUMBER, CAN_ISR);
    M_ProtoUSB_Start();
    CAN_Start();

    for(;;)
    {
        // Serve the USB handler loop forever.
        M_ProtoUSB_Loop();
    }

    return 0;
}

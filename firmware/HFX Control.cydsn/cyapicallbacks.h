/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#ifndef CYAPICALLBACKS_H
#define CYAPICALLBACKS_H

#include "stdint.h"
#include "stddef.h"
    
#define M_ProtoUSB_RECEIVE_CALLBACK
void M_ProtoUSB_Receive_Callback(const uint8_t* pkt, const size_t size);
    
#endif /* CYAPICALLBACKS_H */

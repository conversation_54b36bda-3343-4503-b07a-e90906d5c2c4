/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
// Generated Code. Do not modify.
#include "project.h"
#include "leb128.h"
#include "stop.h"
#ifndef MAKA_hfx_control_cmd_H
#define MAKA_hfx_control_cmd_H

#pragma pack(1)

typedef enum
{
    HFX_CONTROL_CMD_AS_SEND_MESSAGE = 1
} _tag_hfx_control_cmd;

typedef uint8_t data[8];

typedef struct
{
    uint32_t pgn;
    uint16_t timing_ms;
    uint8_t src_addr;
    uint8_t priority;
    uint8_t length;
    data data;
} send_message;

typedef struct
{
    _tag_hfx_control_cmd tag;
    union
    {
        send_message as_send_message;
    } val;
} hfx_control_cmd;

void hfx_control_cmd_as_send_message(const send_message* val, const void* ctx);

inline void demux_hfx_control_cmd(const hfx_control_cmd *tu, const void* ctx)
{
    switch(tu->tag)
    {
        case HFX_CONTROL_CMD_AS_SEND_MESSAGE:
            hfx_control_cmd_as_send_message(&(tu->val.as_send_message), ctx);
            break;
        default:
            stop("Unknown tag for hfx_control_cmd");
    }
}

#pragma pack()
#endif // MAKA_hfx_control_cmd_H

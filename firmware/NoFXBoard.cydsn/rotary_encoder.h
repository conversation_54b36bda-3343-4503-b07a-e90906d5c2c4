/* ========================================
 *
 * Copyright Maka Autonomous Robotic Systems, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */
#include "generated/lib/drivers/nanopb/proto/rotary_encoder.pb.h"
#include "stdint.h"

#define NUM_ENCODERS 4
#define NUM_HISTORY 10
#define MAX_SNAPSHOTS 10

enum RE_TYPE { RE_QUAD, RE_NONE };
void RotaryEncoder_Boot(uint8_t priority, enum RE_TYPE encoder_types[NUM_ENCODERS]);
void GetRotaryEncoderSnapshots(rotary_encoder_RotaryEncoderSnapshots_Reply *reply, uint32_t num_snapshots);
void GetRotaryEncoderTicks(int64_t rotaries[NUM_ENCODERS]);

/* [] END OF FILE */

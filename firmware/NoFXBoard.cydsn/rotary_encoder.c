/* ========================================
 *
 * Copyright Maka Autonomous Robotic Systems, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PR<PERSON><PERSON>ETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */
#include "rotary_encoder.h"
#include "FreeRTOS.h"
#include "project.h"
#include "task.h"

/********************************************************************************
 * From datasheet:
 *******************************************************************************/
// Count modes
#define NQUAD 0x00   // non-quadrature mode
#define QUADRX1 0x01 // X1 quadrature mode
#define QUADRX2 0x02 // X2 quadrature mode
#define QUADRX4 0x03 // X4 quadrature mode
// Running modes
#define FREE_RUN 0x00
#define SINGE_CYCLE 0x04
#define RANGE_LIMIT 0x08
#define MODULO_N 0x0C
// Index modes
#define DISABLE_INDX 0x00 // index_disabled
#define INDX_LOADC 0x10   // index_load_CNTR
#define INDX_RESETC 0x20  // index_rest_CNTR
#define INDX_LOADO 0x30   // index_load_OL
#define ASYNCH_INDX 0x00  // asynchronous index
#define SYNCH_INDX 0x80   // synchronous index
// Clock filter modes
#define FILTER_1 0x00 // filter clock frequncy division factor 1
#define FILTER_2 0x80 // filter clock frequncy division factor 2
/* **MDR1 configuration data; any of these***
 ***data segments can be ORed together***/
// Flag modes
#define NO_FLAGS 0x00 // all flags disabled
#define IDX_FLAG 0x10 // IDX flag
#define CMP_FLAG 0x20 // CMP flag
#define BW_FLAG 0x40  // BW flag
#define CY_FLAG 0x80  // CY flag
// 1 to 4 bytes data-width
#define BYTE_4 0x00 // four byte mode
#define BYTE_3 0x01 // three byte mode
#define BYTE_2 0x02 // two byte mode
#define BYTE_1 0x03 // one byte mode
// Enable/disable counter
#define EN_CNTR 0x00  // counting enabled
#define DIS_CNTR 0x04 // counting disabled

/* LS7366R op-code list */
#define CLR_MDR0 0x08
#define CLR_MDR1 0x10
#define CLR_CNTR 0x20
#define CLR_STR 0x30
#define READ_MDR0 0x48
#define READ_MDR1 0x50
#define READ_CNTR 0x60
#define READ_OTR 0x68
#define READ_STR 0x70
#define WRITE_MDR1 0x90
#define WRITE_MDR0 0x88
#define WRITE_DTR 0x98
#define LOAD_CNTR 0xE0
#define LOAD_OTR 0xE4

/* STR Bits */
#define STR_CNT_EN 0x02

int booted = 0;

typedef void (*write_sig_t)(uint8);

struct quad_rotary {
  write_sig_t SSn;
};

typedef struct RotaryEncoder {
  int64_t *ticks;
  uint8_t type;
  union {
    struct quad_rotary quad;
  } rotary;
} RotaryEncoder;

/* global rotaries being tracked */
int64_t g_rotaries[NUM_ENCODERS][NUM_HISTORY];
uint32_t g_timers[NUM_HISTORY];
uint32_t g_next_index = 0;

RotaryEncoder quad_encoders[NUM_ENCODERS] = {{g_rotaries[0], RE_QUAD, .rotary = {.quad = {FL_SSn_Write}}},
                                             {g_rotaries[1], RE_QUAD, .rotary = {.quad = {FR_SSn_Write}}},
                                             {g_rotaries[2], RE_QUAD, .rotary = {.quad = {BL_SSn_Write}}},
                                             {g_rotaries[3], RE_QUAD, .rotary = {.quad = {BR_SSn_Write}}}};

RotaryEncoder *encoders[NUM_ENCODERS] = {NULL, NULL, NULL, NULL};

void EnableSlave(struct quad_rotary *quad) { quad->SSn(0); }

void DisableSlave(struct quad_rotary *quad) { quad->SSn(1); }

void WaitTx() {
  uint8_t bits;
  bits = SPI_Wheels_ReadTxStatus();
  while (!(bits & SPI_Wheels_STS_SPI_DONE))
    bits = SPI_Wheels_ReadTxStatus();
}

void WaitRx() {
  uint8_t size;
  size = SPI_Wheels_GetRxBufferSize();
  while (size == 0)
    size = SPI_Wheels_GetRxBufferSize();
}

void SPIWrite(struct quad_rotary *quad, uint8_t command) {
  DisableSlave(quad);
  SPI_Wheels_ClearTxBuffer();
  EnableSlave(quad);
  SPI_Wheels_WriteTxData(command);
  WaitTx();
  DisableSlave(quad);
}

void SPIWriteOp(struct quad_rotary *quad, uint8_t op, uint8_t data) {
  DisableSlave(quad);
  EnableSlave(quad);

  SPI_Wheels_ClearTxBuffer();
  SPI_Wheels_WriteTxData(op);
  WaitTx();
  SPI_Wheels_WriteTxData(data);
  WaitTx();

  DisableSlave(quad);
}

uint8_t SPIRead(struct quad_rotary *quad, uint8_t command) {
  uint8_t value;
  DisableSlave(quad);
  EnableSlave(quad);

  SPI_Wheels_ClearTxBuffer();
  SPI_Wheels_WriteTxData(command);
  WaitTx();

  SPI_Wheels_ClearRxBuffer();
  SPI_Wheels_WriteTxData(0x0);
  WaitRx();
  value = SPI_Wheels_ReadRxData();

  DisableSlave(quad);
  return value;
}

/*
 * SPI read for quadrature encoders
 */
int32_t SPIRead4(struct quad_rotary *quad, uint8_t command) {
  int32_t value;
  uint8_t *p = (uint8_t *)&value;

  DisableSlave(quad);
  EnableSlave(quad);

  SPI_Wheels_ClearTxBuffer();
  SPI_Wheels_WriteTxData(command);
  WaitTx();

  for (int i = 0; i < 4; i++) {
    SPI_Wheels_ClearRxBuffer();
    SPI_Wheels_WriteTxData(0x0);
    WaitTx(); // slow - bulk of loop time spent here
    WaitRx();
    p[3 - i] = SPI_Wheels_ReadRxData();
  }

  DisableSlave(quad);
  return value;
}

/*
 * handle read task for quadrature encoders
 *
 */
void handle_quad_rotary(RotaryEncoder *encoder) {

  /* Update */
  encoder->ticks[g_next_index] = SPIRead4(&encoder->rotary.quad, READ_CNTR);
}

uint32_t decrement(uint32_t i) {
  if (i == 0) {
    return NUM_HISTORY - 1;
  } else {
    return i - 1;
  }
}

void tick_index() {
  g_timers[g_next_index] = Timer_1_ReadCounter();
  g_next_index = (g_next_index + 1) % NUM_HISTORY;
}

void RotaryEncoder_Reader_Task(void *unused) {
  (void)unused;

  for (;;) {
    taskENTER_CRITICAL();
    for (int i = 0; i < NUM_ENCODERS; i++) {
      RotaryEncoder *encoder = encoders[i];
      if (encoder == NULL)
        continue;
      if (encoder->type == RE_QUAD) {
        handle_quad_rotary(encoder);
      }
    }
    tick_index();
    taskEXIT_CRITICAL();
    taskYIELD();
  }
}

void RotaryEncoder_Boot(uint8_t task_priority, enum RE_TYPE encoder_types[NUM_ENCODERS]) {
  // 32 bit 60 seconds
  Timer_1_Start();

  for (int i = 0; i < NUM_ENCODERS; i++) {
    if (encoder_types[i] == RE_NONE) {
      // skip
    } else if (encoder_types[i] == RE_QUAD) {
      encoders[i] = &quad_encoders[i];
    }
  }

  if (!booted) {
    booted = 1;
    SPI_Wheels_Start();

    for (int i = 0; i < NUM_ENCODERS; i++) {
      if (encoders[i] == NULL)
        continue;
      if (encoders[i]->type == RE_QUAD) {
        struct quad_rotary *quad = &encoders[i]->rotary.quad;
        SPIWrite(quad, CLR_CNTR);
        SPIWriteOp(quad, WRITE_MDR0, QUADRX4 | FREE_RUN | DISABLE_INDX | ASYNCH_INDX | FILTER_1);
        SPIWriteOp(quad, WRITE_MDR1, NO_FLAGS | BYTE_4 | EN_CNTR);
      }
    }

    xTaskCreate(RotaryEncoder_Reader_Task, "Rotary Encoders", configMINIMAL_STACK_SIZE + 256, 0, task_priority, 0);
  }
}

void GetRotaryEncoderSnapshot(rotary_encoder_RotaryEncoderSnapshot *snapshot, bool *has_snapshot, uint32_t index) {
  snapshot->timer = g_timers[index];
  snapshot->front_left_ticks = quad_encoders[0].ticks[index];
  snapshot->front_right_ticks = quad_encoders[1].ticks[index];
  snapshot->back_left_ticks = quad_encoders[2].ticks[index];
  snapshot->back_right_ticks = quad_encoders[3].ticks[index];
  *has_snapshot = true;
}

void GetRotaryEncoderSnapshots(rotary_encoder_RotaryEncoderSnapshots_Reply *reply, uint32_t num_snapshots) {
  rotary_encoder_RotaryEncoderSnapshot *reply_snapshots[MAX_SNAPSHOTS] = {
      &reply->snapshot1, &reply->snapshot2, &reply->snapshot3, &reply->snapshot4, &reply->snapshot5,
      &reply->snapshot6, &reply->snapshot7, &reply->snapshot8, &reply->snapshot9, &reply->snapshot10,
  };
  bool *reply_has_snapshots[MAX_SNAPSHOTS] = {
      &reply->has_snapshot1, &reply->has_snapshot2,  &reply->has_snapshot3, &reply->has_snapshot4,
      &reply->has_snapshot5, &reply->has_snapshot6,  &reply->has_snapshot7, &reply->has_snapshot8,
      &reply->has_snapshot9, &reply->has_snapshot10,
  };

  uint32_t loop_end = MAX_SNAPSHOTS;
  if (num_snapshots < loop_end) {
    loop_end = num_snapshots;
  }

  taskENTER_CRITICAL();
  uint32_t index = decrement(g_next_index);
  for (uint32_t i = 0; i < loop_end; i++) {
    GetRotaryEncoderSnapshot(reply_snapshots[i], reply_has_snapshots[i], index);
    index = decrement(index);
  }

  taskEXIT_CRITICAL();
}

void GetRotaryEncoderTicks(int64_t rotaries[NUM_ENCODERS]) {
  taskENTER_CRITICAL();
  uint32_t index = decrement(g_next_index);

  for (int i = 0; i < NUM_ENCODERS; i++) {
    if (encoders[i] == NULL)
      continue;
    if (encoders[i]->type == RE_QUAD) {
      rotaries[i] = g_rotaries[i][index];
    }
  }
  taskEXIT_CRITICAL();
}

/* [] END OF FILE */

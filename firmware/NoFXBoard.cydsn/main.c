/* ========================================
 *
 * Copyright Maka Autonomous Robotic Systems, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
 */
#include "FreeRTOS.h"
#include "ethernet_config.h"
#include "freertos_setup.h"
#include "pb_decode.h"
#include "pb_encode.h"
#include "project.h"
#include "rotary_encoder.h"
#include "stdbool.h"
#include "task.h"

#include "generated/lib/drivers/nanopb/proto/nofx_board.pb.h"
#include "generated/lib/drivers/nanopb/proto/park_brake.pb.h"
#include "generated/lib/drivers/nanopb/proto/rotary_encoder.pb.h"

#define NOFX_VERSION_MAJOR 1 // Increase this for major breaking changes
#define NOFX_VERSION_MINOR 0 // Increase this for minor non-breaking changes

void Handle_Ping(diagnostic_Ping *req, diagnostic_Pong *resp) { resp->x = req->x; }

void Handle_Version(version_Version_Reply *resp) {
  resp->major = NOFX_VERSION_MAJOR;
  resp->minor = NOFX_VERSION_MINOR;
}

void Xlate_Config(enum RE_TYPE *out, rotary_encoder_RotaryEncodersConfig_Request_Type val) {
  if (val == rotary_encoder_RotaryEncodersConfig_Request_Type_QUAD)
    *out = RE_QUAD;
  else
    *out = RE_NONE;
}

void Handle_Rotary_Config(rotary_encoder_RotaryEncodersConfig_Request *config) {
  enum RE_TYPE types[4];

  // Configure Rotary Encoders, same priority as udp read because rotaries never blocks
  const uint8_t encoder_task_priority = 1;

  // map from proto enumerations to C types
  Xlate_Config(&types[0], config->FL_type);
  Xlate_Config(&types[1], config->FR_type);
  Xlate_Config(&types[2], config->BL_type);
  Xlate_Config(&types[3], config->BR_type);

  RotaryEncoder_Boot(encoder_task_priority, types);
}

void Handle_Rotary_Snapshots(rotary_encoder_RotaryEncoderSnapshots_Reply *reply, uint32_t num_snapshots) {
  GetRotaryEncoderSnapshots(reply, num_snapshots);
}

void Handle_Rotary(rotary_encoder_RotaryEncoder_Reply *reply) {
  int64_t ticks[NUM_ENCODERS];
  GetRotaryEncoderTicks(ticks);

  reply->timestamp_ms = xTaskGetTickCount() / (configTICK_RATE_HZ / 1000); // In ms
  reply->front_left_ticks = ticks[0];
  reply->front_right_ticks = ticks[1];
  reply->back_left_ticks = ticks[2];
  reply->back_right_ticks = ticks[3];
}

void Handle_Park_Brake(park_brake_Request *req) {
  if (req->onoff == true) {
    Park_Brake_Write(0);
  } else {
    Park_Brake_Write(1);
  }
}

void Handle_Park_Brake_Query(park_brake_Query_Reply *reply) { reply->onoff = !Park_Brake_Read(); }

void Handle_FuelGauge(float *value) { *value = 0; }

void UDP_Task(void *unused) {
  (void)unused;

  for (;;) {
    nofx_board_Request req = nofx_board_Request_init_zero;
    M_Ethernet_W5500_msg_t msg = M_Ethernet_W5500_UdpRead();

    // Decode request
    pb_istream_t stream = pb_istream_from_buffer(msg.data, msg.count);
    bool status = pb_decode(&stream, nofx_board_Request_fields, &req);
    M_Ethernet_W5500_Release_Msg_Data(&msg);
    if (!status) {
      // Failed to decode
      continue;
    }

    int buffer_size = 64;
    nofx_board_Reply resp = nofx_board_Reply_init_zero;
    resp.has_header = true;
    resp.header.requestId = req.header.requestId;
    switch (req.which_request) {
    case nofx_board_Request_ping_tag:
      resp.which_reply = nofx_board_Reply_pong_tag;
      Handle_Ping(&req.request.ping, &resp.reply.pong);
      break;
    case nofx_board_Request_rotary_encoder_tag:
      resp.which_reply = nofx_board_Reply_rotary_encoder_tag;
      switch (req.request.rotary_encoder.which_request) {
      case rotary_encoder_Request_rotary_tag:
        resp.reply.rotary_encoder.which_reply = rotary_encoder_Reply_rotary_tag;
        Handle_Rotary(&resp.reply.rotary_encoder.reply.rotary);
        break;
      case rotary_encoder_Request_config_tag:
        resp.reply.rotary_encoder.which_reply = rotary_encoder_Reply_config_tag;
        Handle_Rotary_Config(&req.request.rotary_encoder.request.config);
        break;
      case rotary_encoder_Request_rotary_snapshots_tag:
        resp.reply.rotary_encoder.which_reply = rotary_encoder_Reply_rotary_snapshots_tag;
        Handle_Rotary_Snapshots(&resp.reply.rotary_encoder.reply.rotary_snapshots,
                                req.request.rotary_encoder.request.rotary_snapshots.num_snapshots);
        // need large buffer to encode full response
        buffer_size = nofx_board_Reply_size;
        break;
      default:
        // unknown
        break;
      }
      break;
    case nofx_board_Request_park_brake_tag:
      resp.which_reply = nofx_board_Reply_park_brake_tag;
      Handle_Park_Brake(&req.request.park_brake);
      break;
    case nofx_board_Request_park_brake_query_tag:
      resp.which_reply = nofx_board_Reply_park_brake_query_tag;
      Handle_Park_Brake_Query(&resp.reply.park_brake_query);
      break;
    case nofx_board_Request_version_tag:
      resp.which_reply = nofx_board_Reply_version_tag;
      Handle_Version(&resp.reply.version);
      break;
    case nofx_board_Request_reset_tag:
      CySoftwareReset();
      break;
    case nofx_board_Request_sensors_tag:
      resp.which_reply = nofx_board_Reply_sensors_tag;
      switch (req.request.sensors.which_request) {
      case sensors_Request_fuel_gauge_tag:
        resp.reply.sensors.which_reply = sensors_Reply_fuel_gauge_tag;
        Handle_FuelGauge(&resp.reply.sensors.reply.fuel_gauge.value);
        break;
      }
      break;
    default:
      // unknown
      break;
    }

    // Encode response.
    uint8_t buffer[buffer_size];
    {
      pb_ostream_t stream = pb_ostream_from_buffer(buffer, sizeof(buffer));
      bool status = pb_encode(&stream, nofx_board_Reply_fields, &resp);
      configASSERT(status); // encoding should not fail
      msg.data = buffer;
      msg.count = stream.bytes_written;
    }
    M_Ethernet_W5500_UdpWrite(msg);
  }
}

void Boot_Task(void *unused) {
  (void)unused;

  // Configure Ethernet.
  uint8_t mac[] = BOARD_MAC(NOFX_IP_INDEX, NOFX_IP_SUB_INDEX);
  uint8_t ip[] = BOARD_IP(NOFX_IP_INDEX, NOFX_IP_SUB_INDEX);
  uint8_t netmask[] = BOARD_NETMASK;
  uint8_t gateway[] = BOARD_GATEWAY;
  size_t read_queue_len = 10;
  M_Ethernet_W5500_Boot(mac, ip, netmask, gateway, read_queue_len);
  M_Ethernet_W5500_UdpOpen(BASE_PORT);

  // Schedule UDP task.
  xTaskCreate(UDP_Task, "UDP", configMINIMAL_STACK_SIZE + 512, 0, 1, 0);

  // Finish boot sequence.
  vTaskDelete(NULL);
}

int main(void) {
  // Enable global interrupts.
  CyGlobalIntEnable;

  // Install FreeRTOS interrupts.
  setup_FreeRTOS_interrupts();

  // Schedule boot sequence.
  xTaskCreate(Boot_Task, "Boot", configMINIMAL_STACK_SIZE, 0, 1, 0);

  // Start the scheduler.
  vTaskStartScheduler();
}

/* [] END OF FILE */

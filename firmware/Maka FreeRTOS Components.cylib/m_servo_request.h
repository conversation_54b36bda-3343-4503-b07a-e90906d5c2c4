/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef M_SERVO_REQUEST_H
#define M_SERVO_REQUEST_H
    
#include "stdint.h"
#include "stdbool.h"
#include "m_epos_request.h"
#include "m_time_request.h"

typedef struct {
    uint32_t max_profile_velocity;
    uint16_t settle_window;
    uint16_t settle_timeout;
    uint16_t max_diff_millis;
} M_Servo_Config_t;

typedef enum {
    SERVO_GO_TO_MODE_IMMEDIATE = 0,
    SERVO_GO_TO_MODE_REACHED = 1,
    SERVO_GO_TO_MODE_SETTLED = 2,
} M_Servo_Go_To_Mode_t;

typedef enum {
    SERVO_REQUEST_TYPE_NONE = 0, // None
    SERVO_REQUEST_TYPE_CONFIG = 1, // M_Servo_Config_Request_t
    SERVO_REQUEST_TYPE_BOOT = 2, // M_Servo_Boot_Request_t
    SERVO_REQUEST_TYPE_STOP = 3, // None
    SERVO_REQUEST_TYPE_GO_TO = 4, // M_Servo_Go_To_Request_t
    SERVO_REQUEST_TYPE_GET_LIMITS = 5, // None
    SERVO_REQUEST_TYPE_EPOS = 6, // M_Epos_Request_t
    SERVO_REQUEST_TYPE_DELTA = 7, // M_Servo_Go_To_Delta_Request_t
    SERVO_REQUEST_TYPE_DELTA_FOLLOW = 8, // M_Servo_Go_To_Delta_Follow_Request_t
    SERVO_REQUEST_TYPE_GO_TO_CALIBRATE = 9, // M_Servo_Go_To_Calibrate_Request_t
    SERVO_REQUEST_TYPE_GO_TO_TIMESTAMP = 10, // M_Servo_Go_To_Timestamp_Request_t
    SERVO_REQUEST_TYPE_FOLLOW_TIMESTAMP = 11 // M_Servo_Follow_Timestamp_Request_t
    
} M_Servo_RequestType;

typedef enum {
    SERVO_REPLY_TYPE_NONE = 0, // None
    SERVO_REPLY_TYPE_ERROR = 1, // None 
    SERVO_REPLY_TYPE_ACK = 2, // None
    SERVO_REPLY_TYPE_LIMITS = 3, // M_Servo_Limits_t
    SERVO_REPLY_TYPE_EPOS = 4, // M_Epos_Reply_t
    SERVO_REPLY_TYPE_POSITION = 5, // M_Servo_Position_Reply_t
    SERVO_REPLY_TYPE_SETTLE_TIME = 6, // M_Servo_Settle_Time_Reply_t
    SERVO_REPLY_TYPE_GO_TO_TIMESTAMP = 7, // M_Servo_Reply_Type_Go_To_Timestamp_t
    SERVO_REPLY_TYPE_FOLLOW_TIMESTAMP = 8, // M_Servo_Reply_Type_Go_To_Timestamp_t
} M_Servo_ReplyType;

typedef struct {
    M_Servo_Config_t config;
    uint8_t node_id;
} M_Servo_Config_Request_t;

typedef struct {
    int32_t position;
    uint32_t velocity;
    bool await;
} M_Servo_Go_To_Request_t;

typedef struct {
    int32_t delta_position;
    uint32_t velocity;
    M_Servo_Go_To_Mode_t mode;
} M_Servo_Go_To_Delta_Request_t;

typedef struct {
    M_Servo_Go_To_Delta_Request_t delta;
    int16_t follow_velocity_vector;
    uint32_t follow_velocity_mrpm;
    uint16_t interval_sleep_time_ms;
} M_Servo_Go_To_Delta_Follow_Request_t;

typedef struct {
    M_Timestamp_t timestamp;
    M_Servo_Go_To_Mode_t mode; 
    int32_t position;
    uint32_t velocity_mrpm;
    int32_t follow_velocity; 
    int32_t follow_accel;
    uint16_t interval_sleep_time_ms;
} M_Servo_Go_To_Timestamp_Request_t;

typedef struct {
    M_Timestamp_t timestamp;
    int32_t follow_velocity; 
    int32_t follow_accel;
} M_Servo_Follow_Timestamp_Request_t;

typedef struct {
    M_Epos_Home_Params_t params;
} M_Servo_Boot_Request_t;

typedef struct {
    int32_t position;
    uint32_t velocity;
    uint16_t window;
    uint16_t time_window_ms;
    uint16_t timeout_ms;
    uint8_t period_ms;
} M_Servo_Go_To_Calibrate_Request_t;

typedef struct {
    M_Servo_RequestType request_type;
    union {
        M_Servo_Boot_Request_t boot;
        M_Servo_Config_Request_t config;
        M_Servo_Go_To_Request_t go_to;
        M_Epos_Request_t epos;
        M_Servo_Go_To_Delta_Request_t delta;
        M_Servo_Go_To_Delta_Follow_Request_t follow;
        M_Servo_Go_To_Calibrate_Request_t calibrate;
        M_Servo_Go_To_Timestamp_Request_t go_to_timestamp;
        M_Servo_Follow_Timestamp_Request_t follow_timestamp;
    } request;
} M_Servo_Request_t;

typedef struct {
    int32_t min;
    int32_t max;
} M_Servo_Limits_Reply_t;

typedef struct {
    int32_t position;
} M_Servo_Position_Reply_t;

typedef struct {
    uint16_t settle_time;
} M_Servo_Settle_Time_Reply_t;

typedef struct {
    int32_t pre_position;
    int32_t post_position; 
    M_Timestamp_t pre_timestamp;
    M_Timestamp_t post_timestamp;    
} M_Servo_Go_To_Timestamp_Reply_t;

typedef struct {
    int32_t pre_position;
    M_Timestamp_t pre_timestamp;  
} M_Servo_Follow_Timestamp_Reply_t;

typedef struct {
    M_Servo_ReplyType reply_type;
    union {
        M_Servo_Limits_Reply_t limits;
        M_Epos_Reply_t epos;
        M_Servo_Position_Reply_t pos;
        M_Error_Code_t error;
        M_Servo_Settle_Time_Reply_t settle;
        M_Servo_Go_To_Timestamp_Reply_t go_to_timestamp;
        M_Servo_Follow_Timestamp_Reply_t follow_timestamp;
    } reply;
} M_Servo_Reply_t;

typedef struct {
    CAN_Node_Ref_t *node_ref;
    M_Servo_Config_t config;
    int32_t limit;
    int32_t resolution;
    QueueHandle_t request_queue;
    QueueHandle_t reply_queue;
    M_Error_Code_t (*get_timestamp)(M_Timestamp_t *out);
} M_Servo_Ref_t;
    
#endif // M_SERVO_REQUEST_H

/* [] END OF FILE */

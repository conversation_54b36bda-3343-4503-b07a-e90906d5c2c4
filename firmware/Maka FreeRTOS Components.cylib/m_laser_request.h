/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef M_LASER_REQUEST_H
#define M_LASER_REQUEST_H
    
#include "stdint.h"
#include "stdbool.h"
#include "queue.h"
#include "m_error_codes.h"
#include "request_metadata.h"

typedef enum {
    LASER_REQUEST_TYPE_NONE = 0, // None
    LASER_REQUEST_TYPE_SET = 1, // M_Laser_State_t
    LASER_REQUEST_TYPE_GET = 2, // None
    LASER_REQUEST_TYPE_INTENSITY = 3, // M_Laser_Intensity_Request_t
} M_Laser_RequestType;

typedef enum {
    LASER_REPLY_TYPE_NONE = 0, // None
    LASER_REPLY_TYPE_ERROR = 1, // uint32_t
    LASER_REPLY_TYPE_ACK = 2, // None
    LASER_REPLY_TYPE_LASER_STATE = 3, // M_Laser_State_t
    LASER_REPLY_TYPE_LASER_REPLY = 4,
} M_Laser_ReplyType;

typedef struct {
    bool on;
} M_Laser_State_t;

typedef struct {
    uint16_t intensity;
} M_Laser_Intensity_Request_t;

typedef struct {
    Request_Metadata_t metadata;
    M_Laser_RequestType request_type;
    union {
        M_Laser_State_t laser;
        M_Laser_Intensity_Request_t intensity;
    } request;
} M_Laser_Request_t;

typedef struct {
  int32_t raw_therm1_reading_mv;
  int32_t raw_therm2_reading_mv;
  bool on;
  bool lpsu_state;
  bool fireable;
} M_Laser_Reply_Laser_Reply;

typedef struct {
    Request_Metadata_t metadata;
    M_Laser_ReplyType reply_type;
    union {
        M_Laser_State_t laser;
        M_Error_Code_t error;
        M_Laser_Reply_Laser_Reply laser_reply;
    } reply;
} M_Laser_Reply_t;

typedef struct {
    QueueHandle_t request_queue;
    M_Error_Code_t (*send_reply_func)(M_Laser_Reply_t *);
} M_Laser_Ref_t;

#endif // M_LASER_REQUEST_H

/* [] END OF FILE */

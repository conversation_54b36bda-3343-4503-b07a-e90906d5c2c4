/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/

#include "project.h"
#include "FreeRTOS.h"
#include "timers.h"
#include "stdbool.h"
#include "queue.h"
#include "semphr.h"
#include "maka_endian.h"

#define ETHERNET_BLOCK_COMMON_REG           0x00
#define ETHERNET_BLOCK_SOCKET_REG(x)        ( (x << 2) | 0x01 )
#define ETHERNET_BLOCK_SOCKET_TX(x)         ( (x << 2) | 0x02 )
#define ETHERNET_BLOCK_SOCKET_RX(x)         ( (x << 2) | 0x03 )
    
// Common Registers
#define ETHERNET_REG_GATEWAY_IP             0x0001
#define ETHERNET_REG_SUBNET_MASK            0x0005
#define ETHERNET_REG_MAC                    0x0009
#define ETHERNET_REG_IP                     0x000f
#define ETHERNET_REG_ALL_SOCKETS_INTR       0x0017
#define ETHERNET_REG_ALL_SOCKETS_INTR_MASK  0x0018
#define ETHERNET_REG_PHYCFGR                0x002e
#define ETHERNET_REG_VERSION                0x0039

#define ETHERNET_PHYCFGR_100M_FD_AND_RST    0b01011000
#define ETHERNET_PHYCFGR_100M_FD            0b11011000

// Socket Registers
#define ETHERNET_REG_SOCKET_MODE            0x0000
#define ETHERNET_REG_SOCKET_COMMAND         0x0001
#define ETHERNET_REG_SOCKET_INTR            0x0002
#define ETHERNET_REG_SOCKET_INTR_MASK       0x002c
#define ETHERNET_REG_SOCKET_STATUS          0x0003
#define ETHERNET_REG_SOCKET_PORT            0x0004
#define ETHERNET_REG_SOCKET_DST_IP          0x000c
#define ETHERNET_REG_SOCKET_DST_PORT        0x0010
#define ETHERNET_REG_SOCKET_TX_READ_PTR     0x0022
#define ETHERNET_REG_SOCKET_TX_WRITE_PTR    0x0024
#define ETHERNET_REG_SOCKET_RX_READ_PTR     0x0028
#define ETHERNET_REG_SOCKET_RX_WRITE_PTR    0x002a

#define ETHERNET_SOCKET_MODE_UDP            0x02

#define ETHERNET_SOCKET_COMMAND_OPEN        0x01
#define ETHERNET_SOCKET_COMMAND_CLOSE       0x10
#define ETHERNET_SOCKET_COMMAND_SEND        0x20
#define ETHERNET_SOCKET_COMMAND_RECV        0x40

#define ETHERNET_SOCKET_INTR_RECV           0x04
#define ETHERNET_SOCKET_INTR_SEND_OK        0x10
#define ETHERNET_SOCKET_TIMEOUT             0x08
#define ETHERNET_SOCKET_DISCON              0x02

#define ETHERNET_SOCKET_STATUS_CLOSED       0x00
#define ETHERNET_SOCKET_STATUS_UDP          0x22
    
#define ETHERNET_EXPECTED_VERSION           0x04


typedef enum
{
    `$INSTANCE_NAME`_OP_TYPE_READ = 0,
    `$INSTANCE_NAME`_OP_TYPE_WRITE = 1
} `$INSTANCE_NAME`_op_type_t;

SemaphoreHandle_t `$INSTANCE_NAME`_op_mutex;
SemaphoreHandle_t `$INSTANCE_NAME`_int_notify_semphr;
QueueHandle_t `$INSTANCE_NAME`_read_msg_queue;
SemaphoreHandle_t `$INSTANCE_NAME`_write_mutex;
SemaphoreHandle_t `$INSTANCE_NAME`_write_notify_semphr;
int `$INSTANCE_NAME`_num_open_sockets = 0;
int `$INSTANCE_NAME`_local_port_mapping[8];  // Wiznet can do 8 sockets

inline void `$INSTANCE_NAME`_Op_Mutex_Acquire()
{
    xSemaphoreTake(`$INSTANCE_NAME`_op_mutex, portMAX_DELAY);
}

inline void `$INSTANCE_NAME`_Op_Mutex_Release()
{
    xSemaphoreGive(`$INSTANCE_NAME`_op_mutex);
}

inline void `$INSTANCE_NAME`_SPI_Xfer(uint8_t size)
{
    `$INSTANCE_NAME`_SPI_INPUT_RDY_Control = size;
    while (!`$INSTANCE_NAME`_SPI_XFER_DONE_Status) ;
    `$INSTANCE_NAME`_SPI_INPUT_RDY_Control = 0;
}

inline void `$INSTANCE_NAME`_SPI_WriteHeader(uint8_t block, uint16_t address, `$INSTANCE_NAME`_op_type_t type)
{
    // These are all registers for performance.
    `$INSTANCE_NAME`_SPI_DATA_W_0_Control = address >> 8; // Address MSB
    `$INSTANCE_NAME`_SPI_DATA_W_1_Control = address & 0xff; // Address LSB
    `$INSTANCE_NAME`_SPI_DATA_W_2_Control = (block << 3) | (type << 2); // block & read/write
    `$INSTANCE_NAME`_SPI_Xfer(24);
}

inline void `$INSTANCE_NAME`_SPI_ReadFourBytes(uint8_t* data)
{
    // These are all registers for performance.
    `$INSTANCE_NAME`_SPI_DATA_W_0_Control = 0;
    `$INSTANCE_NAME`_SPI_DATA_W_1_Control = 0;
    `$INSTANCE_NAME`_SPI_DATA_W_2_Control = 0;
    `$INSTANCE_NAME`_SPI_DATA_W_3_Control = 0;
    `$INSTANCE_NAME`_SPI_Xfer(32);
    data[0] = `$INSTANCE_NAME`_SPI_DATA_R_0_Status;
    data[1] = `$INSTANCE_NAME`_SPI_DATA_R_1_Status;
    data[2] = `$INSTANCE_NAME`_SPI_DATA_R_2_Status;
    data[3] = `$INSTANCE_NAME`_SPI_DATA_R_3_Status;
}

inline void `$INSTANCE_NAME`_SPI_WriteFourBytes(uint8_t* data)
{
    // These are all registers for performance.
    `$INSTANCE_NAME`_SPI_DATA_W_0_Control = data[0];
    `$INSTANCE_NAME`_SPI_DATA_W_1_Control = data[1];
    `$INSTANCE_NAME`_SPI_DATA_W_2_Control = data[2];
    `$INSTANCE_NAME`_SPI_DATA_W_3_Control = data[3];
    `$INSTANCE_NAME`_SPI_Xfer(32);
}

inline uint8_t `$INSTANCE_NAME`_SPI_WriteByte(uint8_t data)
{
    // These are all registers for performance.
    `$INSTANCE_NAME`_SPI_DATA_W_0_Control = data;
    `$INSTANCE_NAME`_SPI_Xfer(8);
    return `$INSTANCE_NAME`_SPI_DATA_R_3_Status;
}

void `$INSTANCE_NAME`_Op(uint8_t block, uint16_t address, `$INSTANCE_NAME`_op_type_t type, void* data, size_t count)
{
    // We have callers synchronize access via op mutex.
    configASSERT(uxSemaphoreGetCount(`$INSTANCE_NAME`_op_mutex) == 0);
    
    // Enable chip-select.
    `$INSTANCE_NAME`_SCSn_W_Write(0);

    // Write header.
    `$INSTANCE_NAME`_SPI_WriteHeader(block, address, type);

    if (type == `$INSTANCE_NAME`_OP_TYPE_READ)
    {
        for (void* end_ptr = data + count - count % 4; data != end_ptr; data += 4)
        {
            `$INSTANCE_NAME`_SPI_ReadFourBytes((uint8_t*)data);
        }
        for (void* end_ptr = data + count % 4; data != end_ptr; data++)
        {
            *(uint8_t*)data = `$INSTANCE_NAME`_SPI_WriteByte(0);
        }
    }
    else // `$INSTANCE_NAME`_OP_TYPE_WRITE
    {
        for (void* end_ptr = data + count - count % 4; data != end_ptr; data += 4)
        {
            `$INSTANCE_NAME`_SPI_WriteFourBytes((uint8_t*)data);
        }
        for (void* end_ptr = data + count % 4; data != end_ptr; data++)
        {
            `$INSTANCE_NAME`_SPI_WriteByte(*(uint8_t*)data);
        }
    }
    
    // Disable chip-select
    `$INSTANCE_NAME`_SCSn_W_Write(1);
}

inline void `$INSTANCE_NAME`_Write(uint8_t block, uint16_t address, void* data, size_t count)
{
    `$INSTANCE_NAME`_Op(block, address, `$INSTANCE_NAME`_OP_TYPE_WRITE, data, count);
}

inline void `$INSTANCE_NAME`_Read(uint8_t block, uint16_t address, void* data, size_t count)
{
    `$INSTANCE_NAME`_Op(block, address, `$INSTANCE_NAME`_OP_TYPE_READ, data, count);
}

inline void `$INSTANCE_NAME`_Write_UInt8(uint8_t block, uint16_t address, uint8_t data)
{
    `$INSTANCE_NAME`_Write(block, address, &data, sizeof(data));
}

inline uint8_t `$INSTANCE_NAME`_Read_UInt8(uint8_t block, uint16_t address)
{
    uint8_t data;
    `$INSTANCE_NAME`_Read(block, address, &data, sizeof(data));
    return data;
}

inline void `$INSTANCE_NAME`_Write_UInt16(uint8_t block, uint16_t address, uint16_t data)
{
    data = htobe16(data);
    `$INSTANCE_NAME`_Write(block, address, &data, sizeof(data));
}

inline uint16_t `$INSTANCE_NAME`_Read_UInt16(uint8_t block, uint16_t address)
{
    uint16_t data;
    `$INSTANCE_NAME`_Read(block, address, &data, sizeof(data));
    return be16toh(data);
}

uint16_t `$INSTANCE_NAME`_Read_Consistent_UInt16(uint8_t block, uint16_t address)
{
    
    uint16_t prev_data, curr_data;
    `$INSTANCE_NAME`_Read(block, address, &curr_data, sizeof(curr_data));
    do {
        prev_data = curr_data;
        `$INSTANCE_NAME`_Read(block, address, &curr_data, sizeof(curr_data));
    } while (prev_data != curr_data);
    return be16toh(curr_data);
}

CY_ISR(`$INSTANCE_NAME`_INTn_ISR_Impl)
{
    BaseType_t pxHigherPriorityTaskWoken = pdFALSE;
    xSemaphoreGiveFromISR(`$INSTANCE_NAME`_int_notify_semphr, &pxHigherPriorityTaskWoken);
    portEND_SWITCHING_ISR(pxHigherPriorityTaskWoken);
}

void `$INSTANCE_NAME`_Int_Handler_Task(void* unused)
{
    /*
     * This function uses ETHERNET_BLOCK_SOCKET_REG(0) - IE you are only allowed ONE socket.
     */
    (void) unused;

    for (;;)
    {
        // Wait to be awaken by interrupt handler.
        xSemaphoreTake(`$INSTANCE_NAME`_int_notify_semphr, portMAX_DELAY);

        // Acquire mutex to perform Ethernet ops.
        `$INSTANCE_NAME`_Op_Mutex_Acquire();

        /* Which sockets have interrupts?
         * 7 6 5 4 3 2 1 0
         * S7_INT S6_INT S5_INT S4_INT S3_INT S2_INT S1_INT S0_INT */
        uint8_t sockets_with_interrupts = `$INSTANCE_NAME`_Read_UInt8(ETHERNET_BLOCK_COMMON_REG, ETHERNET_REG_ALL_SOCKETS_INTR);

        while (sockets_with_interrupts) {

            /* Get the interrupts for the open sockets */
            for (int i = 0; i < `$INSTANCE_NAME`_num_open_sockets; i++)
            {
                uint8_t socket_interrupt = `$INSTANCE_NAME`_Read_UInt8(ETHERNET_BLOCK_SOCKET_REG(i), ETHERNET_REG_SOCKET_INTR);
                // Clear interrupts.
                `$INSTANCE_NAME`_Write_UInt8(ETHERNET_BLOCK_SOCKET_REG(i), ETHERNET_REG_SOCKET_INTR, socket_interrupt);

                if (socket_interrupt & ETHERNET_SOCKET_INTR_RECV)
                {
                    // Grab pointers. Write pointer may be changing, so to ensure consistency we read it multiple times.
                    uint16_t read_ptr = `$INSTANCE_NAME`_Read_UInt16(ETHERNET_BLOCK_SOCKET_REG(i), ETHERNET_REG_SOCKET_RX_READ_PTR);
                    uint16_t write_ptr = `$INSTANCE_NAME`_Read_Consistent_UInt16(ETHERNET_BLOCK_SOCKET_REG(i), ETHERNET_REG_SOCKET_RX_WRITE_PTR);

                    while (read_ptr != write_ptr)
                    {
                        // Read the data & send to the message buffer.
                        int count;
                        uint8_t header[8];
                        `$INSTANCE_NAME`_Read(ETHERNET_BLOCK_SOCKET_RX(i), read_ptr, &header, sizeof(header));

                        // Create message object & read data.
                        `$INSTANCE_NAME`_msg_t msg;
                        memcpy(msg.ip, &header[0], 4);
                        msg.port = ((header[4] << 8) | header[5]);
                        msg.local_port = `$INSTANCE_NAME`_local_port_mapping[i];
                        msg.count = ((header[6] << 8) | header[7]);
                        count = msg.count;
                        msg.data = pvPortMalloc(msg.count);
                        `$INSTANCE_NAME`_Read(ETHERNET_BLOCK_SOCKET_RX(i), (read_ptr + 8) & 0xffff, msg.data, msg.count);

                        /* If we're full then drop this packet on the floor */
                        if (uxQueueSpacesAvailable(`$INSTANCE_NAME`_read_msg_queue) == 0)
                        {
                            `$INSTANCE_NAME`_Release_Msg_Data(&msg);
                        }
                        else
                        {
                            // Add message to queue.
                            xQueueSendToBack(`$INSTANCE_NAME`_read_msg_queue, &msg, portMAX_DELAY);
                        }

                        // Move read ptr, wraparound using 0xffff.
                        read_ptr = (read_ptr + count + sizeof(header)) & 0xffff;
                        `$INSTANCE_NAME`_Write_UInt16(ETHERNET_BLOCK_SOCKET_REG(i), ETHERNET_REG_SOCKET_RX_READ_PTR, read_ptr);
                        `$INSTANCE_NAME`_Write_UInt8(ETHERNET_BLOCK_SOCKET_REG(i), ETHERNET_REG_SOCKET_COMMAND, ETHERNET_SOCKET_COMMAND_RECV);
                    }
                }
                if (socket_interrupt & ETHERNET_SOCKET_INTR_SEND_OK)
                {
                    xSemaphoreGive(`$INSTANCE_NAME`_write_notify_semphr);
                }
                if (socket_interrupt & ETHERNET_SOCKET_DISCON || socket_interrupt & ETHERNET_SOCKET_TIMEOUT)
                {
                    xSemaphoreGive(`$INSTANCE_NAME`_write_notify_semphr);
                }
                sockets_with_interrupts = `$INSTANCE_NAME`_Read_UInt8(ETHERNET_BLOCK_COMMON_REG, ETHERNET_REG_ALL_SOCKETS_INTR);
            }
        }

        // Release mutex for performing Ethernet ops.
        `$INSTANCE_NAME`_Op_Mutex_Release();
    }
}

int `$INSTANCE_NAME`_VerifyVersion()
{
    uint8_t version = `$INSTANCE_NAME`_Read_UInt8(ETHERNET_BLOCK_COMMON_REG, ETHERNET_REG_VERSION);
    return version == ETHERNET_EXPECTED_VERSION;
}

void `$INSTANCE_NAME`_Reset()
{
    `$INSTANCE_NAME`_RSTn_W_Write(0);
    vTaskDelay(pdMS_TO_TICKS(1));
    `$INSTANCE_NAME`_RSTn_W_Write(1);
    vTaskDelay(pdMS_TO_TICKS(1));
}

void `$INSTANCE_NAME`_Boot(uint8_t* mac, uint8_t* ip, uint8_t* netmask, uint8_t* gateway, size_t read_queue_len)
{
    // Restart Ethernet module.
    `$INSTANCE_NAME`_Reset();

    // Ethernet op mutex.
    `$INSTANCE_NAME`_op_mutex = xSemaphoreCreateMutex();

    // Acquire mutex to perform Ethernet ops.
    `$INSTANCE_NAME`_Op_Mutex_Acquire();

    // Verify version readback.
    configASSERT(`$INSTANCE_NAME`_VerifyVersion());

    // Create read queue.
    `$INSTANCE_NAME`_read_msg_queue = xQueueCreate(read_queue_len, sizeof(`$INSTANCE_NAME`_msg_t));
    
    // Create write mutex.
    `$INSTANCE_NAME`_write_mutex = xSemaphoreCreateMutex();
    `$INSTANCE_NAME`_write_notify_semphr = xSemaphoreCreateBinary();
    
    // Ethernet interrupt.
    `$INSTANCE_NAME`_int_notify_semphr = xSemaphoreCreateBinary();
    xTaskCreate(`$INSTANCE_NAME`_Int_Handler_Task, "Eth Intr", configMINIMAL_STACK_SIZE + 512, 0, 4, 0);
    `$INSTANCE_NAME`_INTn_ISR_ClearPending();
    `$INSTANCE_NAME`_INTn_ISR_StartEx(`$INSTANCE_NAME`_INTn_ISR_Impl);

    // Set PHY to 100M full duplex.
    `$INSTANCE_NAME`_Write_UInt8(ETHERNET_BLOCK_COMMON_REG, ETHERNET_REG_PHYCFGR, ETHERNET_PHYCFGR_100M_FD_AND_RST);
    `$INSTANCE_NAME`_Write_UInt8(ETHERNET_BLOCK_COMMON_REG, ETHERNET_REG_PHYCFGR, ETHERNET_PHYCFGR_100M_FD);
    
    // Set up MAC, IP, netmask and gateway.
    `$INSTANCE_NAME`_Write(ETHERNET_BLOCK_COMMON_REG, ETHERNET_REG_MAC, mac, 6);
    `$INSTANCE_NAME`_Write(ETHERNET_BLOCK_COMMON_REG, ETHERNET_REG_IP, ip, 4);
    `$INSTANCE_NAME`_Write(ETHERNET_BLOCK_COMMON_REG, ETHERNET_REG_SUBNET_MASK, netmask, 4);
    `$INSTANCE_NAME`_Write(ETHERNET_BLOCK_COMMON_REG, ETHERNET_REG_GATEWAY_IP, gateway, 4);

    // Release mutex for performing Ethernet ops.
    `$INSTANCE_NAME`_Op_Mutex_Release();
}

void `$INSTANCE_NAME`_UdpOpen(uint16_t port)
{
    // Acquire mutex to perform Ethernet ops.
    `$INSTANCE_NAME`_Op_Mutex_Acquire();
    int this_socket = `$INSTANCE_NAME`_num_open_sockets;

    // Enable socket interrupts.
    int SIMR = `$INSTANCE_NAME`_Read_UInt8(ETHERNET_BLOCK_COMMON_REG, ETHERNET_REG_ALL_SOCKETS_INTR_MASK);
    SIMR |= 1 << this_socket;
    `$INSTANCE_NAME`_Write_UInt8(ETHERNET_BLOCK_COMMON_REG, ETHERNET_REG_ALL_SOCKETS_INTR_MASK, SIMR);

    // Set up UDP socket.
    `$INSTANCE_NAME`_Write_UInt8(ETHERNET_BLOCK_SOCKET_REG(this_socket), ETHERNET_REG_SOCKET_MODE, ETHERNET_SOCKET_MODE_UDP);
    `$INSTANCE_NAME`_Write_UInt16(ETHERNET_BLOCK_SOCKET_REG(this_socket), ETHERNET_REG_SOCKET_PORT, port);
    `$INSTANCE_NAME`_Write_UInt8(ETHERNET_BLOCK_SOCKET_REG(this_socket), ETHERNET_REG_SOCKET_COMMAND, ETHERNET_SOCKET_COMMAND_OPEN);
    
    // Verify that socket got opened.
    uint8_t socket_status = `$INSTANCE_NAME`_Read_UInt8(ETHERNET_BLOCK_SOCKET_REG(this_socket), ETHERNET_REG_SOCKET_STATUS);
    configASSERT(socket_status == ETHERNET_SOCKET_STATUS_UDP);    

    `$INSTANCE_NAME`_local_port_mapping[this_socket] = port;
    `$INSTANCE_NAME`_num_open_sockets++;
    // Release mutex for performing Ethernet ops.
    `$INSTANCE_NAME`_Op_Mutex_Release();
}

`$INSTANCE_NAME`_msg_t `$INSTANCE_NAME`_UdpRead()
{
    `$INSTANCE_NAME`_msg_t msg;
    xQueueReceive(`$INSTANCE_NAME`_read_msg_queue, &msg, portMAX_DELAY);
    return msg;
}

`$INSTANCE_NAME`_msg_t `$INSTANCE_NAME`_UdpReadTimeout(int ms)
{
    `$INSTANCE_NAME`_msg_t msg;
    msg.data = NULL;
    xQueueReceive(`$INSTANCE_NAME`_read_msg_queue, &msg, ms / portTICK_PERIOD_MS);
    return msg;
}

void `$INSTANCE_NAME`_Release_Msg_Data(`$INSTANCE_NAME`_msg_t* msg)
{
    vPortFree(msg->data);
    msg->data = NULL;
    msg->count = 0;
}

int `$INSTANCE_NAME`_socket_from_port(uint16_t port)
{
    /* Support legacy single socket not setting local_port */
    if (`$INSTANCE_NAME`_num_open_sockets == 1)
    {
        return 0;
    }

    for (int i = 0; i < `$INSTANCE_NAME`_num_open_sockets; i++)
    {
        if (`$INSTANCE_NAME`_local_port_mapping[i] == port)
        {
            return i;
        }
    }
    return -1;
}

void `$INSTANCE_NAME`_UdpWrite(`$INSTANCE_NAME`_msg_t msg)
{
    int socket_no = `$INSTANCE_NAME`_socket_from_port(msg.local_port);
    if (socket_no < 0)
    {
        return;
    }
    
    xSemaphoreTake(`$INSTANCE_NAME`_write_mutex, portMAX_DELAY);


    // Acquire mutex to perform Ethernet ops.
    `$INSTANCE_NAME`_Op_Mutex_Acquire();

    // Set destination IP and port.
    `$INSTANCE_NAME`_Write(ETHERNET_BLOCK_SOCKET_REG(socket_no), ETHERNET_REG_SOCKET_DST_IP, msg.ip, 4);
    `$INSTANCE_NAME`_Write_UInt16(ETHERNET_BLOCK_SOCKET_REG(socket_no), ETHERNET_REG_SOCKET_DST_PORT, msg.port);
    
    // Read write pointer.
    uint16_t write_ptr = `$INSTANCE_NAME`_Read_UInt16(ETHERNET_BLOCK_SOCKET_REG(socket_no), ETHERNET_REG_SOCKET_TX_WRITE_PTR);
    
    // Write data.
    `$INSTANCE_NAME`_Write(ETHERNET_BLOCK_SOCKET_TX(socket_no), write_ptr, msg.data, msg.count);

    // Update write_ptr and send SEND command. Wraparound using 0xffff.
    write_ptr = (write_ptr + msg.count) & 0xffff;
    `$INSTANCE_NAME`_Write_UInt16(ETHERNET_BLOCK_SOCKET_REG(socket_no), ETHERNET_REG_SOCKET_TX_WRITE_PTR, write_ptr);
    `$INSTANCE_NAME`_Write_UInt8(ETHERNET_BLOCK_SOCKET_REG(socket_no), ETHERNET_REG_SOCKET_COMMAND, ETHERNET_SOCKET_COMMAND_SEND);

    // Release mutex for performing Ethernet ops.
    `$INSTANCE_NAME`_Op_Mutex_Release();
    
    // Wait for SEND_OK.
    xSemaphoreTake(`$INSTANCE_NAME`_write_notify_semphr, portMAX_DELAY);
    xSemaphoreGive(`$INSTANCE_NAME`_write_mutex);
}

/* [] END OF FILE */

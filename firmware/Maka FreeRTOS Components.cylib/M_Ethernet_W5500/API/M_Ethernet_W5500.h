/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/

#ifndef `$INSTANCE_NAME`_H
#define `$INSTANCE_NAME`_H

#include "CyLib.h"

typedef struct {
    uint8_t ip[4];
    uint16_t port; // For historical reasons port is remote_port
    void* data;
    uint16_t count;
    uint16_t local_port;
} `$INSTANCE_NAME`_msg_t;

void `$INSTANCE_NAME`_Boot(uint8_t* mac, uint8_t* ip, uint8_t* netmask, uint8_t* gateway, size_t read_queue_len);
void `$INSTANCE_NAME`_UdpOpen(uint16_t port);
`$INSTANCE_NAME`_msg_t `$INSTANCE_NAME`_UdpRead();
`$INSTANCE_NAME`_msg_t `$INSTANCE_NAME`_UdpReadTimeout(int ms);
void `$INSTANCE_NAME`_UdpWrite(`$INSTANCE_NAME`_msg_t msg);
void `$INSTANCE_NAME`_Release_Msg_Data(`$INSTANCE_NAME`_msg_t* msg);
int `$INSTANCE_NAME`_VerifyVersion();
void `$INSTANCE_NAME`_Reset();

#endif

/* [] END OF FILE */

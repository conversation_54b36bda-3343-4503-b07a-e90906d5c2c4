/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#include "`$INSTANCE_NAME`_M_CAN_Open.h"
#include "project.h"
#include "stdbool.h"
#include "stdint.h"
#include "CAN_Open.h"
#include "FreeRTOS.h"
#include "queue.h"
#include "maka_endian.h"
#include "utils.h"

#define QUEUE_LENGTH 8
QueueHandle_t `$INSTANCE_NAME`_send_queue;

#define MAX_NUMBER_OF_NODES 8
// Should be referenced by pointers, not copied
CAN_Node_Ref_t `$INSTANCE_NAME`_node_refs[MAX_NUMBER_OF_NODES];
uint8_t next_node_index = 0;

// Not thread safe, should only be used at boot time
M_Error_Code_t `$INSTANCE_NAME`_Configure_Node(uint8_t node_id, uint8_t *index)
{
    if (next_node_index >= MAX_NUMBER_OF_NODES)
    {
        return M_ERROR_CODE_CAN_NODE_CONFIGURATION_FAIL;
    }
    *index = next_node_index++;
    `$INSTANCE_NAME`_node_refs[*index].node_id = node_id;
    return M_ERROR_CODE_OK;
}

void `$INSTANCE_NAME`_Clear_Node_Configuration()
{
    for (uint8_t i = 0; i < MAX_NUMBER_OF_NODES; i++)
    {
        `$INSTANCE_NAME`_node_refs[i].node_id = 0;
    }
    next_node_index = 0;
}

CAN_Node_Ref_t * `$INSTANCE_NAME`_Get_Node_Ref(uint8_t index)
{
    if (index >= MAX_NUMBER_OF_NODES)
    {
        return NULL;
    }
    return &`$INSTANCE_NAME`_node_refs[index];
}


M_Error_Code_t `$INSTANCE_NAME`_Find_Node_Ref_Index(uint8_t node_id, uint8_t *index)
{
    // node_id == 0 case can happen for NMT messages or broadcast, we discard them
    if (node_id == 0)
    {
        return M_ERROR_CODE_CAN_NODE_INDEX_FROM_NMT;
    }
    
    for (uint8_t i = 0; i < MAX_NUMBER_OF_NODES; i++)
    {
        if (`$INSTANCE_NAME`_node_refs[i].node_id == node_id)
        {
            *index = i;
            return M_ERROR_CODE_OK;
        }
    }
    return M_ERROR_CODE_CAN_NODE_NOT_FOUND;
}

inline void `$INSTANCE_NAME`_Push_To_Receive_Queue_From_ISR(CAN_Open_Message_t *msg, portBASE_TYPE *xHigherPriorityTaskWoken)
{
    // If the Queue is full, we will drop messages
    uint8_t index = 0;
    if (`$INSTANCE_NAME`_Find_Node_Ref_Index(msg->node_id, &index) == M_ERROR_CODE_OK)
    {
        xQueueSendToBackFromISR(`$INSTANCE_NAME`_node_refs[index].recv_queue, msg, xHigherPriorityTaskWoken);   
    }
}

inline void `$INSTANCE_NAME`_Push_To_Send_Queue(CAN_Open_Message_t *msg)
{
    xQueueSendToBack(`$INSTANCE_NAME`_send_queue, msg, portMAX_DELAY);
}

inline void `$INSTANCE_NAME`_Pop_From_Send_Queue(CAN_Open_Message_t *msg)
{
    xQueueReceive(`$INSTANCE_NAME`_send_queue, msg, portMAX_DELAY);
}

// Called in ISR
inline bool `$INSTANCE_NAME`_SDO_Received_From_ISR(CAN_Open_Message_t *msg, uint32_t* raw_pkt)
{
    msg->pkt_type = CAN_OPEN_SDO_PKT_TYPE;
    msg->pkt.sdo = *(CAN_Open_SDO_packet_t*)raw_pkt;
    switch(CAN_OPEN_SDO_SPEC_CS(msg->pkt.sdo.spec))
    {
        case CAN_OPEN_CS_SDO_UL:
        case CAN_OPEN_CS_SDO_ABORT:
        case CAN_OPEN_CS_SDO_DL_RESPONSE:
            return true;
        case CAN_OPEN_CS_SDO_DL_REQUEST: // We don't have a dictionary to answer
        default:
            return false;
    }
}

// Called in ISR
inline bool `$INSTANCE_NAME`_PDO_Received_From_ISR(CAN_Open_Message_t *msg, uint32_t* raw_pkt)
{
    msg->pkt_type = CAN_OPEN_PDO_PKT_TYPE;
    msg->pkt.pdo = *(CAN_Open_PDO_packet_t*)raw_pkt;
    return true;
}

// Called in ISR
inline bool `$INSTANCE_NAME`_NMT_Received_From_ISR(CAN_Open_Message_t *msg, uint32_t* raw_pkt)
{
    msg->pkt_type = CAN_OPEN_NMT_PKT_TYPE;
    msg->pkt.nmt = *(CAN_Open_NMT_packet_t*)raw_pkt;
    return true;
}

//Called in ISR
void `$INSTANCE_NAME`_ReceiveMsg_From_ISR(uint8 rxMailbox, portBASE_TYPE *xHigherPriorityTaskWoken) 
{
    CAN_Open_Message_t msg;
    uint32_t* raw_pkt = (uint32_t*)`$INSTANCE_NAME``[CAN]`RX[rxMailbox].rxdata.byte;
    raw_pkt[0] = be32toh(raw_pkt[0]);
    raw_pkt[1] = be32toh(raw_pkt[1]);
    uint32_t rx_id = `$INSTANCE_NAME``[CAN]`GET_RX_ID(rxMailbox);
    msg.func = CAN_OPEN_COB_ID_FUNC(rx_id);
    msg.node_id = CAN_OPEN_COB_ID_NODE_ID(rx_id);
    
    bool should_send = false;
    switch(CAN_OPEN_COB_ID_FUNC(`$INSTANCE_NAME``[CAN]`GET_RX_ID(rxMailbox))) {
        case CAN_OPEN_FUNC_CODE_SDO_TX:
            should_send = `$INSTANCE_NAME`_SDO_Received_From_ISR(&msg, raw_pkt);
            break;
        case CAN_OPEN_FUNC_CODE_PDO1_TX:
        case CAN_OPEN_FUNC_CODE_PDO2_TX:
        case CAN_OPEN_FUNC_CODE_PDO3_TX:
        case CAN_OPEN_FUNC_CODE_PDO4_TX:
            should_send = `$INSTANCE_NAME`_PDO_Received_From_ISR(&msg, raw_pkt);
            break;
        case CAN_OPEN_BROADCAST_FUNC_CODE_NMT:
        case CAN_OPEN_FUNC_CODE_NMT_ERROR:
            should_send = `$INSTANCE_NAME`_NMT_Received_From_ISR(&msg, raw_pkt);
            break;
        case CAN_OPEN_FUNC_CODE_SDO_RX: // We don't currently do anything about other people trying to talk to the thing we are talking to.
        case CAN_OPEN_FUNC_CODE_PDO1_RX:
        case CAN_OPEN_FUNC_CODE_PDO2_RX:
        case CAN_OPEN_FUNC_CODE_PDO3_RX:
        case CAN_OPEN_FUNC_CODE_PDO4_RX:
        default:
            break;
    }
    if (should_send)
    {
        `$INSTANCE_NAME`_Push_To_Receive_Queue_From_ISR(&msg, xHigherPriorityTaskWoken);
    }
}

void `$INSTANCE_NAME`_MsgRXIsr(void) 
{
    uint8 mailboxNumber;
    uint16 shift = 0x01u;
    portBASE_TYPE xHigherPriorityTaskWoken = 0;

    /* Clear Receive Message flag */
    `$INSTANCE_NAME``[CAN]`INT_SR_REG.byte[1u] = `$INSTANCE_NAME``[CAN]`RX_MESSAGE_MASK;
    
    for (mailboxNumber = 0u; mailboxNumber < `$INSTANCE_NAME``[CAN]`NUMBER_OF_RX_MAILBOXES; mailboxNumber++)
    {
        shift = 1 << mailboxNumber;
        if (CY_GET_REG16((reg16 *) &`$INSTANCE_NAME``[CAN]`BUF_SR_REG.byte[0u]) & shift)
        {
            if ((`$INSTANCE_NAME``[CAN]`RX[mailboxNumber].rxcmd.byte[0u] &
                 `$INSTANCE_NAME``[CAN]`RX_INT_ENABLE_MASK) != 0u &&
                (`$INSTANCE_NAME``[CAN]`RX[mailboxNumber].rxcmd.byte[0u] &
                 `$INSTANCE_NAME``[CAN]`RX_ACK_MSG))
            {
                xHigherPriorityTaskWoken = 0;
                `$INSTANCE_NAME`_ReceiveMsg_From_ISR(mailboxNumber, &xHigherPriorityTaskWoken);
                `$INSTANCE_NAME``[CAN]`RX_ACK_MESSAGE(mailboxNumber);
                portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
            }
        }
    }
}

CY_ISR(`$INSTANCE_NAME``[CAN]`ISR)
{
    /* RX Message Available */
    #if (`$INSTANCE_NAME``[CAN]`RX_MESSAGE && (`$INSTANCE_NAME``[CAN]`RX_MESSAGE_USE_HELPER || \
        (!`$INSTANCE_NAME``[CAN]`ADVANCED_INTERRUPT_CFG)))
        if ((`$INSTANCE_NAME``[CAN]`INT_SR_REG.byte[1u] & `$INSTANCE_NAME``[CAN]`RX_MESSAGE_MASK) != 0u)
        {
           `$INSTANCE_NAME`_MsgRXIsr();
        }
    #endif /* `$INSTANCE_NAME``[CAN]`RX_MESSAGE && `$INSTANCE_NAME``[CAN]`RX_MESSAGE_USE_HELPER */
}

void `$INSTANCE_NAME`_Send_CAN_Open_Message_t(CAN_Open_Message_t *msg)
{
    `$INSTANCE_NAME``[CAN]`TX_MSG message;
    `$INSTANCE_NAME``[CAN]`DATA_BYTES_MSG msgbuf;
    
    message.id = CAN_OPEN_COB_ID_CONSTRUCT(msg->node_id, msg->func);
    message.msg = &msgbuf;
    message.rtr = msg->pkt_type == CAN_OPEN_PDO_RTR_PKT_TYPE;
    message.ide = 0;
    message.irq = true;
    
    switch (msg->pkt_type)
    {
        case CAN_OPEN_NMT_PKT_TYPE:
            message.dlc = sizeof(CAN_Open_NMT_packet_t);
            break;
        case CAN_OPEN_PDO_RTR_PKT_TYPE:
            message.dlc = 0;
            break;
        case CAN_OPEN_PDO_PKT_TYPE:
            message.dlc = msg->size;
            break;
        case CAN_OPEN_SDO_PKT_TYPE:
            message.dlc = sizeof(msg->pkt);
            break;
        default:
            message.dlc = 0;
            break;
    }
    
    uint32_t* raw_pkt = (uint32_t*)msgbuf.byte;
    memcpy((uint8_t *)raw_pkt, (uint8_t *)&msg->pkt, message.dlc);
    raw_pkt[0] = htole32(raw_pkt[0]);
    raw_pkt[1] = htole32(raw_pkt[1]);
    
    // We retry a few times if the bus is too busy with a 1 ms cooldown to let the bus empty
    for (int i = 0; i < 5; i++)
    {
        if (`$INSTANCE_NAME``[CAN]`SendMsg(&message) == CYRET_SUCCESS) 
        {
            break;
        }
        vTaskDelay(1);
    }
}

void `$INSTANCE_NAME`_Send_Task(void *unused)
{
    (void) unused;
    
    for (;;)
    {
        CAN_Open_Message_t msg;
        `$INSTANCE_NAME`_Pop_From_Send_Queue(&msg);
        `$INSTANCE_NAME`_Send_CAN_Open_Message_t(&msg);
    }
}

void `$INSTANCE_NAME`_boot(UBaseType_t send_task_priority) {
    `$INSTANCE_NAME`_send_queue = xQueueCreate(QUEUE_LENGTH, sizeof(CAN_Open_Message_t));
    
    for (uint8_t i = 0; i < MAX_NUMBER_OF_NODES; i++)
    {
        `$INSTANCE_NAME`_node_refs[i].node_id = 0;
        `$INSTANCE_NAME`_node_refs[i].recv_queue = xQueueCreate(QUEUE_LENGTH / 2, sizeof(CAN_Open_Message_t));
    }
    
    `$INSTANCE_NAME``[CAN]`Start();
    
    xTaskCreate(`$INSTANCE_NAME`_Send_Task, "CAN-S", configMINIMAL_STACK_SIZE + 512, 0, send_task_priority, 0);
}

void `$INSTANCE_NAME`_Send_CAN_Open_Message(CAN_Open_Message_t *msg, uint8_t node_id, uint8_t func, uint8_t pkt_type)
{
    msg->func = func;
    msg->node_id = node_id;
    msg->pkt_type = pkt_type;
    `$INSTANCE_NAME`_Push_To_Send_Queue(msg);
}

void `$INSTANCE_NAME`_Send_SDO(uint8_t node_id, uint16_t index, uint8_t subindex, uint32_t value, uint8_t cs, uint8_t expedited)
{
    CAN_Open_Message_t msg;
    msg.pkt_type = CAN_OPEN_SDO_PKT_TYPE;
    msg.pkt.sdo.index = index;
    msg.pkt.sdo.subindex = subindex;
    msg.pkt.sdo.spec = CAN_OPEN_SDO_SPEC_XLOAD_CONSTRUCT(cs, 0, 0, expedited, 0);
    memcpy(msg.pkt.sdo.data, &value, sizeof(uint32_t)); 
    `$INSTANCE_NAME`_Send_CAN_Open_Message(&msg, node_id, CAN_OPEN_FUNC_CODE_SDO_RX, CAN_OPEN_SDO_PKT_TYPE);
}

void `$INSTANCE_NAME`_Send_PDO(uint8_t node_id, uint8_t func, uint8_t *data, uint8_t size)
{
    CAN_Open_Message_t msg;
    msg.size = size;
    memcpy(msg.pkt.pdo.data, data, sizeof(uint8_t) * size); 
    `$INSTANCE_NAME`_Send_CAN_Open_Message(&msg, node_id, func, CAN_OPEN_PDO_PKT_TYPE);
}

void `$INSTANCE_NAME`_Send_RTR_PDO(uint8_t node_id, uint8_t func)
{
    CAN_Open_Message_t msg;
    `$INSTANCE_NAME`_Send_CAN_Open_Message(&msg, node_id, func, CAN_OPEN_PDO_RTR_PKT_TYPE);
}

void `$INSTANCE_NAME`_Send_NMT(uint8_t node_id, uint8_t state)
{
    CAN_Open_Message_t msg;
    msg.pkt.nmt.node_id = node_id;
    msg.pkt.nmt.state = state;
    `$INSTANCE_NAME`_Send_CAN_Open_Message(&msg, 0x00, CAN_OPEN_BROADCAST_FUNC_CODE_NMT, CAN_OPEN_NMT_PKT_TYPE);
}

M_Error_Code_t `$INSTANCE_NAME`_Await_Reply(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg, uint16_t timeout_ms, uint8_t func)
{
    TickType_t tick_start = xTaskGetTickCount();
    TickType_t max_wait_time = timeout_ms / portTICK_PERIOD_MS;
    while (xTaskGetTickCount() - tick_start < max_wait_time)
    {
        TickType_t wait_time = max_wait_time - (xTaskGetTickCount() - tick_start);
        BaseType_t result = xQueueReceive(node_ref->recv_queue, msg, wait_time > 0 ? wait_time : 0);
        if (result && msg->func == func)
        {
            return M_ERROR_CODE_OK;   
        }
    }
    return M_ERROR_CODE_CAN_AWAIT_REPLY_TIMEOUT;
}

M_Error_Code_t `$INSTANCE_NAME`_Send_SDO_Download(CAN_Node_Ref_t *node_ref, uint16_t index, uint8_t subindex, uint32_t value)
{
    xQueueReset(node_ref->recv_queue);
    CAN_Open_Message_t msg;
    `$INSTANCE_NAME`_Send_SDO(node_ref->node_id, index, subindex, value, CAN_OPEN_CS_SDO_DL_REQUEST, 1);
    
    M_Error_Code_t ret = `$INSTANCE_NAME`_Await_Reply(node_ref, &msg, CAN_OPEN_DEFAULT_TIMEOUT_MS, CAN_OPEN_FUNC_CODE_SDO_TX);
    if(ret != M_ERROR_CODE_OK)
    {
        return ret;
    }
    
    
    if(
        msg.pkt_type != CAN_OPEN_SDO_PKT_TYPE ||
        msg.pkt.sdo.index != index ||
        msg.pkt.sdo.subindex != subindex ||
        CAN_OPEN_SDO_SPEC_CS(msg.pkt.sdo.spec) != CAN_OPEN_CS_SDO_DL_RESPONSE
    )
    {
        return M_ERROR_CODE_CAN_SDO_DOWNLOAD_BAD_REPLY;   
    }
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_Send_SDO_Upload_Request(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg, uint16_t index, uint8_t subindex)
{
    xQueueReset(node_ref->recv_queue);
    `$INSTANCE_NAME`_Send_SDO(node_ref->node_id, index, subindex, 0x00, CAN_OPEN_CS_SDO_UL, 0);
    
     M_Error_Code_t ret = `$INSTANCE_NAME`_Await_Reply(node_ref, msg, CAN_OPEN_DEFAULT_TIMEOUT_MS, CAN_OPEN_FUNC_CODE_SDO_TX);
    if(ret != M_ERROR_CODE_OK)
    {
        return ret;
    }
    
    if(
        msg->pkt_type != CAN_OPEN_SDO_PKT_TYPE ||
        msg->pkt.sdo.index != index ||
        msg->pkt.sdo.subindex != subindex ||
        CAN_OPEN_SDO_SPEC_CS(msg->pkt.sdo.spec) != CAN_OPEN_CS_SDO_UL
    )
    {
        return M_ERROR_CODE_CAN_SDO_UPLOAD_BAD_REPLY;
    }
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_NMT_Reset_Node(CAN_Node_Ref_t *node_ref)
{
    CAN_Open_Message_t msg;
    xQueueReset(node_ref->recv_queue);
    
    // Reset Node, on average takes 1.5 seconds
    `$INSTANCE_NAME`_Send_NMT(node_ref->node_id, CAN_OPEN_NMT_RESET_NODE);
    if (`$INSTANCE_NAME`_Await_Reply(node_ref, &msg, 3000, CAN_OPEN_FUNC_CODE_NMT_ERROR) != M_ERROR_CODE_OK ||
        msg.pkt_type != CAN_OPEN_NMT_PKT_TYPE ||
        msg.pkt.nmt.state != 0x00)
    {
        // Try To Keep Going Anyway
    }
    
    
    // Reset Communications, on average takes a few ms
    `$INSTANCE_NAME`_Send_NMT(node_ref->node_id, CAN_OPEN_NMT_RESET_COMMUNICATION);
    M_Error_Code_t ret = `$INSTANCE_NAME`_Await_Reply(node_ref, &msg, 100, CAN_OPEN_FUNC_CODE_NMT_ERROR);
    if(ret != M_ERROR_CODE_OK)
    {
        return ret;
    }
    
    if(
        msg.pkt_type != CAN_OPEN_NMT_PKT_TYPE ||
        msg.pkt.nmt.state != 0x00
    )
    {
        return M_ERROR_CODE_CAN_RESET_COMMS_BAD_REPLY;
    }

    // No NMT Response Expected
    `$INSTANCE_NAME`_Send_NMT(node_ref->node_id, CAN_OPEN_NMT_ENTER_PRE_OPERATIONAL);
    vTaskDelay(pdMS_TO_TICKS(10));
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_NMT_Start_Node(CAN_Node_Ref_t *node_ref)
{
    // No NMT Response Expected
    `$INSTANCE_NAME`_Send_NMT(node_ref->node_id, CAN_OPEN_NMT_ENTER_OPERATIONAL);
    vTaskDelay(pdMS_TO_TICKS(10));
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_NMT_Stop_Node(CAN_Node_Ref_t *node_ref)
{
    // No NMT Response Expected
    `$INSTANCE_NAME`_Send_NMT(node_ref->node_id, CAN_OPEN_NMT_ENTER_STOP);
    vTaskDelay(pdMS_TO_TICKS(10));
    
    // No NMT Response Expected
    `$INSTANCE_NAME`_Send_NMT(node_ref->node_id, CAN_OPEN_NMT_ENTER_PRE_OPERATIONAL);
    vTaskDelay(pdMS_TO_TICKS(10));
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_Handle_Request(CAN_Node_Ref_t *node_ref, M_CAN_Request_t *request, M_CAN_Reply_t *out_reply)
{
    M_Error_Code_t ret = M_ERROR_CODE_OK;
    out_reply->reply_type = M_CAN_REPLY_TYPE_ACK;
    switch(request->request_type)
    {
        case M_CAN_REQUEST_TYPE_SDO:
            `$INSTANCE_NAME`_Send_SDO(node_ref->node_id, request->request.sdo.index, request->request.sdo.subindex, request->request.sdo.value, request->request.sdo.cs, request->request.sdo.expedited);
            break;
        case M_CAN_REQUEST_TYPE_PDO:
            `$INSTANCE_NAME`_Send_PDO(node_ref->node_id, request->request.pdo.func, request->request.pdo.data, request->request.pdo.size);
            break;
        case M_CAN_REQUEST_TYPE_RTR_PDO:
            `$INSTANCE_NAME`_Send_RTR_PDO(node_ref->node_id, request->request.rtr.func);
            break;
        case M_CAN_REQUEST_TYPE_NMT:
            `$INSTANCE_NAME`_Send_NMT(node_ref->node_id, request->request.nmt.state);
            break;
        case M_CAN_REQUEST_TYPE_AWAIT_REPLY:
            out_reply->reply_type = M_CAN_REPLY_TYPE_MESSAGE;
            ret = `$INSTANCE_NAME`_Await_Reply(node_ref, &out_reply->reply.msg, request->request.await.timeout_ms, request->request.await.func);   
            break;       
        case M_CAN_REQUEST_TYPE_SDO_DOWNLOAD:
            out_reply->reply_type = M_CAN_REPLY_TYPE_ACK;
            ret = `$INSTANCE_NAME`_Send_SDO_Download(node_ref, request->request.sdo_download.index, request->request.sdo_download.subindex, request->request.sdo_download.value);
            break;
        case M_CAN_REQUEST_TYPE_SDO_UPLOAD:
            out_reply->reply_type = M_CAN_REPLY_TYPE_MESSAGE;
            ret = `$INSTANCE_NAME`_Send_SDO_Upload_Request(node_ref, &out_reply->reply.msg, request->request.sdo_upload.index, request->request.sdo_upload.subindex);
            break;
        case M_CAN_REQUEST_TYPE_NMT_RESET:
            out_reply->reply_type = M_CAN_REPLY_TYPE_ACK;
            ret = `$INSTANCE_NAME`_NMT_Reset_Node(node_ref);
            break;
        case M_CAN_REQUEST_TYPE_NMT_START:
            out_reply->reply_type = M_CAN_REPLY_TYPE_ACK;
            ret = `$INSTANCE_NAME`_NMT_Start_Node(node_ref);
            break;
        case M_CAN_REQUEST_TYPE_NMT_STOP:
            out_reply->reply_type = M_CAN_REPLY_TYPE_ACK;
            ret = `$INSTANCE_NAME`_NMT_Stop_Node(node_ref);
            break;
        default:
            ret = M_ERROR_CODE_CAN_REQUEST_NOT_SUPPORTED;
            break;
    }
    
    if (ret != M_ERROR_CODE_OK)
    {
        out_reply->reply_type = M_CAN_REPLY_TYPE_ERROR;
        out_reply->reply.error =  ret;
    }
    
    return M_ERROR_CODE_OK;
}

/* [] END OF FILE */

/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef `$INSTANCE_NAME`_M_CAN_OPEN_H
#define `$INSTANCE_NAME`_M_CAN_OPEN_H
    
#include "FreeRTOS.h"
#include "stdbool.h"
#include "stdint.h"
#include "CAN_Open.h"
#include "m_can_request.h"
#include "m_error_codes.h"

// Node Management
M_Error_Code_t `$INSTANCE_NAME`_Configure_Node(uint8_t node_id, uint8_t *index);
void `$INSTANCE_NAME`_Clear_Node_Configuration();
CAN_Node_Ref_t * `$INSTANCE_NAME`_Get_Node_Ref(uint8_t index);
M_Error_Code_t `$INSTANCE_NAME`_Find_Node_Ref_Index(uint8_t node_id, uint8_t *index);

// Boot
void `$INSTANCE_NAME`_boot(UBaseType_t send_task_priority);

// Basic API
void `$INSTANCE_NAME`_Send_SDO(uint8_t node_id, uint16_t index, uint8_t subindex, uint32_t value, uint8_t cs, uint8_t expedited);
void `$INSTANCE_NAME`_Send_PDO(uint8_t node_id, uint8_t func, uint8_t data[8], uint8_t size);
void `$INSTANCE_NAME`_Send_RTR_PDO(uint8_t node_id, uint8_t func);
void `$INSTANCE_NAME`_Send_NMT(uint8_t node_id, uint8_t state);

// API
M_Error_Code_t `$INSTANCE_NAME`_Await_Reply(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg, uint16_t timeout_ms, uint8_t func);
M_Error_Code_t `$INSTANCE_NAME`_Send_SDO_Download(CAN_Node_Ref_t *node_ref, uint16_t index, uint8_t subindex, uint32_t value);
M_Error_Code_t `$INSTANCE_NAME`_Send_SDO_Upload_Request(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg, uint16_t index, uint8_t subindex);

// NMT API
M_Error_Code_t `$INSTANCE_NAME`_NMT_Reset_Node(CAN_Node_Ref_t *node_ref);
M_Error_Code_t `$INSTANCE_NAME`_NMT_Start_Node(CAN_Node_Ref_t *node_ref);
M_Error_Code_t `$INSTANCE_NAME`_NMT_Stop_Node(CAN_Node_Ref_t *node_ref);

// Request API
M_Error_Code_t `$INSTANCE_NAME`_Handle_Request(CAN_Node_Ref_t *node_ref, M_CAN_Request_t *request, M_CAN_Reply_t *out_reply);

#endif // `$INSTANCE_NAME`_M_CAN_OPEN_H

/* [] END OF FILE */

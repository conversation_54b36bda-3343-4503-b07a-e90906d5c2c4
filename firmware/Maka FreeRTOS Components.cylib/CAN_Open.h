/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef CAN_OPEN_H
#define CAN_OPEN_H

#include "stdint.h"
#include "queue.h"

// CAN Open Broadcast Function Codes
#define CAN_OPEN_BROADCAST_FUNC_CODE_NMT 0b0000
#define CAN_OPEN_BROADCAST_FUNC_CODE_SYNC 0b0001
#define CAN_OPEN_BROADCAST_FUNC_CODE_TIME_STAMP 0b0010

// CAN Open Function Codes
#define CAN_OPEN_FUNC_CODE_EMERGENCY 0b0001
#define CAN_OPEN_FUNC_CODE_PDO1_TX 0b0011
#define CAN_OPEN_FUNC_CODE_PDO1_RX 0b0100
#define CAN_OPEN_FUNC_CODE_PDO2_TX 0b0101
#define CAN_OPEN_FUNC_CODE_PDO2_RX 0b0110
#define CAN_OPEN_FUNC_CODE_PDO3_TX 0b0111
#define CAN_OPEN_FUNC_CODE_PDO3_RX 0b1000
#define CAN_OPEN_FUNC_CODE_PDO4_TX 0b1001
#define CAN_OPEN_FUNC_CODE_PDO4_RX 0b1010
#define CAN_OPEN_FUNC_CODE_SDO_TX 0b1011
#define CAN_OPEN_FUNC_CODE_SDO_RX 0b1100
#define CAN_OPEN_FUNC_CODE_NMT_ERROR 0b1110

// CAN Open Command Specifier
#define CAN_OPEN_CS_SDO_DL_REQUEST 1
#define CAN_OPEN_CS_SDO_UL 2
#define CAN_OPEN_CS_SDO_DL_RESPONSE 3
#define CAN_OPEN_CS_SDO_ABORT 4

#define CAN_OPEN_SDO_SPEC_CS(spec) (((spec) & 0b11100000) >> 5) // Command Specifier
#define CAN_OPEN_SDO_SPEC_XLOAD_T(spec) (((spec) & 0b00010000) >> 4) // Toggle bit
#define CAN_OPEN_SDO_SPEC_XLOAD_N(spec) (((spec) & 0b00001100) >> 2) // 
#define CAN_OPEN_SDO_SPEC_XLOAD_E(spec) (((spec) & 0b00000010) >> 1) // Expedited Transfer
#define CAN_OPEN_SDO_SPEC_XLOAD_S(spec) ((spec) & 0b00000001) // Size Indicator

#define CAN_OPEN_SDO_SPEC_XLOAD_CONSTRUCT(cs, t, n, e, s) ((s & 0b00000001) | ((e << 1) & 0b00000010) | ((n << 2) & 0b00001100) | ((t << 4) & 0b00010000) | ((cs << 5) & 0b11100000))

#define CAN_OPEN_DEFAULT_TIMEOUT_MS 500
#define CAN_OPEN_DEFAULT_PDO_RTR_TIMEOUT_MS 5

// COB-ID
#define CAN_OPEN_COB_ID_FUNC(id) (id >> 7)
#define CAN_OPEN_COB_ID_NODE_ID(id) (id & 0x7F)
#define CAN_OPEN_COB_ID_CONSTRUCT(node_id, func) (((node_id) & 0x7F) | (((func) & 0xF) << 7))

// NMT Commands
#define CAN_OPEN_NMT_ENTER_OPERATIONAL 0x01
#define CAN_OPEN_NMT_ENTER_STOP 0x02
#define CAN_OPEN_NMT_ENTER_PRE_OPERATIONAL 0x80
#define CAN_OPEN_NMT_RESET_NODE 0x81
#define CAN_OPEN_NMT_RESET_COMMUNICATION 0x82

// NMT State
#define CAN_OPEN_NMT_BOOT_STATE = 0x00
#define CAN_OPEN_NMT_STOPPED_STATE = 0x04
#define CAN_OPEN_NMT_OPERATIONAL_STATE = 0x05
#define CAN_OPEN_NMT_PRE_OPERATIONAL_STATE = 0x7f
    
#define CAN_OPEN_SDO_PKT_DATA_SIZE 4
#define CAN_OPEN_PDO_PKT_DATA_SIZE 8

#pragma pack(1)
typedef struct { 
    uint8_t spec;
    uint16_t index;
    uint8_t subindex;
    uint8_t data[CAN_OPEN_SDO_PKT_DATA_SIZE];
} CAN_Open_SDO_packet_t;

typedef struct {
    uint8_t data[CAN_OPEN_PDO_PKT_DATA_SIZE];
} CAN_Open_PDO_packet_t;

typedef struct {
    uint8_t state;
    uint8_t node_id;
} CAN_Open_NMT_packet_t;
#pragma pack(0)

#define CAN_OPEN_SDO_PKT_TYPE 1
#define CAN_OPEN_PDO_PKT_TYPE 2
#define CAN_OPEN_NMT_PKT_TYPE 3
#define CAN_OPEN_PDO_RTR_PKT_TYPE 4

typedef struct {
    uint8_t func;
    uint8_t node_id;
    uint8_t pkt_type;
    uint8_t size;
    union {
        CAN_Open_SDO_packet_t sdo;
        CAN_Open_PDO_packet_t pdo;
        CAN_Open_NMT_packet_t nmt;
    } pkt;
} CAN_Open_Message_t;

typedef struct {
    uint8_t node_id;
    QueueHandle_t recv_queue;
} CAN_Node_Ref_t;

#endif // CAN_OPEN_H

/* [] END OF FILE */

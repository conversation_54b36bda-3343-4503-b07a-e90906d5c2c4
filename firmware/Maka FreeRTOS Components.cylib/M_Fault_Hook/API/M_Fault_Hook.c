/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#include "project.h"
#include "FreeRTOS.h"
#include "task.h"
#include "utils.h"

#define DEFAULT_FAULT_ALERT_REPS 30
#define ENABLE 1
#define DISABLE 0

void `$INSTANCE_NAME`_Fault_Loop(uint16_t on_time_ms, uint16_t off_time_ms)
{
    taskDISABLE_INTERRUPTS();
    #if !defined(`$INSTANCE_NAME`_Fault_Reg_Sync_ctrl_reg__REMOVED)
    `$INSTANCE_NAME`_Fault_Reg_Write(1);
    #endif
    #if !defined(`$INSTANCE_NAME`_Fault_LED_Reg_Sync_ctrl_reg__REMOVED)
    for(int i = 0; i < DEFAULT_FAULT_ALERT_REPS; i++)
    {
        `$INSTANCE_NAME`_Fault_LED_Reg_Write(ENABLE);
        CyDelay(on_time_ms);
        `$INSTANCE_NAME`_Fault_LED_Reg_Write(DISABLE);
        CyDelay(off_time_ms);
    }
    #else
    CyDelay(DEFAULT_FAULT_ALERT_REPS * 1000);
    #endif
    CySoftwareReset();
}

void vApplicationStackOverflowHook(TaskHandle_t pxTask, char *pcTaskName)
{
    UNUSED(pxTask);
    UNUSED(pcTaskName);
    `$INSTANCE_NAME`_Fault_Loop(100, 1000);
}

void vApplicationMallocFailedHook(void)
{
    `$INSTANCE_NAME`_Fault_Loop(1000, 100);
}

void vAssertCalled(const char* file, const int line)
{
    UNUSED(file);
    UNUSED(line);
    `$INSTANCE_NAME`_Fault_Loop(500, 500);
}

/* [] END OF FILE */

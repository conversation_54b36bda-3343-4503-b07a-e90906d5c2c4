/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef `$INSTANCE_NAME`_M_SCANNER_H
#define `$INSTANCE_NAME`_M_SCANNER_H

#include "m_scanner_request.h"
#include "m_error_codes.h"

M_Error_Code_t `$INSTANCE_NAME`_Send_Request(M_Scanner_Request_t *request);
void `$INSTANCE_NAME`_Boot_Component( void (*send_reply_func)(M_Scanner_Reply_t *), UBaseType_t priority);

#endif // `$INSTANCE_NAME`_M_SCANNER_H

/* [] END OF FILE */

/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#include "FreeRTOS.h"
#include "project.h"
#include "queue.h"
#include "`$INSTANCE_NAME`_M_Scanner.h"

#define DEFAULT_SERVO_REQUEST_TIMEOUT 1000
#define DEFAULT_SERVO_REPLY_TIMEOUT 10000

M_Scanner_Ref_t `$INSTANCE_NAME`_Scanner_ref;

M_Servo_Request_t `$INSTANCE_NAME`_pan_request;
M_Servo_Request_t `$INSTANCE_NAME`_tilt_request;
M_Servo_Reply_t `$INSTANCE_NAME`_pan_reply;
M_Servo_Reply_t `$INSTANCE_NAME`_tilt_reply;

inline M_Error_Code_t `$INSTANCE_NAME`_Send_Request(M_Scanner_Request_t *request)
{
    // We drop requests if there isn't enough space
    return xQueueSendToBack(`$INSTANCE_NAME`_Scanner_ref.request_queue, request, 0) == pdTRUE;
}

inline bool `$INSTANCE_NAME`_Await_Request(M_Scanner_Request_t *request, uint32_t timeout_ms)
{
    return xQueueReceive(`$INSTANCE_NAME`_Scanner_ref.request_queue, request, pdMS_TO_TICKS(timeout_ms)) == pdTRUE;
}

bool `$INSTANCE_NAME`_Do_Both_Servos(M_Servo_Request_t *pan, M_Servo_Request_t *tilt, M_Servo_Reply_t *out_pan, M_Servo_Reply_t *out_tilt)
{
    return `$INSTANCE_NAME`_Pan_Send_Request(pan, DEFAULT_SERVO_REQUEST_TIMEOUT) == M_ERROR_CODE_OK &&
           `$INSTANCE_NAME`_Tilt_Send_Request(tilt, DEFAULT_SERVO_REQUEST_TIMEOUT) == M_ERROR_CODE_OK &&
           `$INSTANCE_NAME`_Pan_Await_Reply(out_pan, DEFAULT_SERVO_REPLY_TIMEOUT) == M_ERROR_CODE_OK &&
           `$INSTANCE_NAME`_Tilt_Await_Reply(out_tilt, DEFAULT_SERVO_REPLY_TIMEOUT) == M_ERROR_CODE_OK;
}

void `$INSTANCE_NAME`_Laser(bool on)
{
    `$INSTANCE_NAME`_Laser_Fire_Reg_Write(on);
}

bool `$INSTANCE_NAME`_Get_Laser()
{
    return (bool) `$INSTANCE_NAME`_Laser_Fire_Reg_Read();
}

void `$INSTANCE_NAME`_Intensity(uint16_t intensity)
{
    `$INSTANCE_NAME`_Intensity_PWM_WriteCompare(intensity);
}

bool `$INSTANCE_NAME`_Boot(M_Scanner_Boot_Request_t *request)
{
    `$INSTANCE_NAME`_Laser(false);
    `$INSTANCE_NAME`_pan_request.request_type = SERVO_REQUEST_TYPE_BOOT;
    `$INSTANCE_NAME`_pan_request.request.boot.params = request->pan_params;
    `$INSTANCE_NAME`_tilt_request.request_type = SERVO_REQUEST_TYPE_BOOT;
    `$INSTANCE_NAME`_tilt_request.request.boot.params = request->tilt_params;
    
    return `$INSTANCE_NAME`_Do_Both_Servos(&`$INSTANCE_NAME`_pan_request, &`$INSTANCE_NAME`_tilt_request, &`$INSTANCE_NAME`_pan_reply, &`$INSTANCE_NAME`_tilt_reply) &&
           `$INSTANCE_NAME`_pan_reply.reply_type == SERVO_REPLY_TYPE_ACK &&
           `$INSTANCE_NAME`_tilt_reply.reply_type == SERVO_REPLY_TYPE_ACK;
}

bool `$INSTANCE_NAME`_Stop()
{
    `$INSTANCE_NAME`_Laser(false);
    `$INSTANCE_NAME`_pan_request.request_type = SERVO_REQUEST_TYPE_STOP;
    `$INSTANCE_NAME`_tilt_request.request_type = SERVO_REQUEST_TYPE_STOP;
    
    return `$INSTANCE_NAME`_Do_Both_Servos(&`$INSTANCE_NAME`_pan_request, &`$INSTANCE_NAME`_tilt_request, &`$INSTANCE_NAME`_pan_reply, &`$INSTANCE_NAME`_tilt_reply) &&
           `$INSTANCE_NAME`_pan_reply.reply_type == SERVO_REPLY_TYPE_ACK &&
           `$INSTANCE_NAME`_tilt_reply.reply_type == SERVO_REPLY_TYPE_ACK;
}

void `$INSTANCE_NAME`_Handle_Request(M_Scanner_Request_t *request) 
{
    bool result = true;
    M_Scanner_Reply_t reply;
    reply.request_id = request->request_id;
    switch(request->request_type)
    {
        case SCANNER_REQUEST_TYPE_LASER:
            reply.reply_type = SCANNER_REPLY_TYPE_ACK;
            reply.reply.laser = request->request.laser;
            `$INSTANCE_NAME`_Laser(reply.reply.laser.on);
            break;
        case SCANNER_REQUEST_TYPE_GET_LASER:
            reply.reply_type = SCANNER_REPLY_TYPE_LASER_STATE;
            reply.reply.laser.on = `$INSTANCE_NAME`_Get_Laser();
            break;
        case SCANNER_REQUEST_TYPE_BOOT:
            reply.reply_type = SCANNER_REPLY_TYPE_ACK;
            result = `$INSTANCE_NAME`_Boot(&request->request.boot);
            break;
        case SCANNER_REQUEST_TYPE_STOP:
            reply.reply_type = SCANNER_REPLY_TYPE_ACK;
            result = `$INSTANCE_NAME`_Stop();
            break;
        case SCANNER_REQUEST_TYPE_GIMBAL:
            reply.reply_type = SCANNER_REPLY_TYPE_GIMBAL;
            result = `$INSTANCE_NAME`_Do_Both_Servos(&request->request.gimbal.pan, &request->request.gimbal.tilt, &reply.reply.gimbal.pan, &reply.reply.gimbal.tilt) &&
                     reply.reply.gimbal.pan.reply_type != SERVO_REPLY_TYPE_ERROR &&
                     reply.reply.gimbal.tilt.reply_type != SERVO_REPLY_TYPE_ERROR;
            break;
        case SCANNER_REQUEST_TYPE_INTENSITY:
            reply.reply_type = SCANNER_REPLY_TYPE_ACK;
            `$INSTANCE_NAME`_Intensity(request->request.intensity.intensity);
            break;
        default:
            reply.reply_type = SCANNER_REPLY_TYPE_ERROR;
            break;
    }
    
    if (!result)
    {
        reply.reply_type = SCANNER_REPLY_TYPE_ERROR;
    }
    
    `$INSTANCE_NAME`_Scanner_ref.send_reply_func(&reply);
}

void `$INSTANCE_NAME`_Task(void *context)
{
    (void) context;
    M_Scanner_Request_t request;
    
    while (true)
    {
        if (!`$INSTANCE_NAME`_Await_Request(&request, portMAX_DELAY / portTICK_PERIOD_MS))
        {
            continue;
        }
        
        `$INSTANCE_NAME`_Handle_Request(& request);
    }
}

void `$INSTANCE_NAME`_Boot_Component( void (*send_reply_func)(M_Scanner_Reply_t *), UBaseType_t priority)
{
    `$INSTANCE_NAME`_Intensity_PWM_Start();
    `$INSTANCE_NAME`_PWM_Clock_Start();
    `$INSTANCE_NAME`_Intensity(UINT16_MAX);
    `$INSTANCE_NAME`_Scanner_ref.request_queue = xQueueCreate(4, sizeof(M_Scanner_Request_t));
    `$INSTANCE_NAME`_Scanner_ref.send_reply_func = send_reply_func;
    
    `$INSTANCE_NAME`_Pan_Boot_Component(priority);
    `$INSTANCE_NAME`_Tilt_Boot_Component(priority);
    
    xTaskCreate(`$INSTANCE_NAME`_Task, "`$INSTANCE_NAME`", configMINIMAL_STACK_SIZE + 512, NULL, priority, 0);
}



/* [] END OF FILE */

/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/

#include "project.h"
#include "FreeRTOS.h"
#include "timers.h"
#include "stdbool.h"
#include "queue.h"
#include "`$INSTANCE_NAME`_M_USB.h"

#define USB_PACKET_SIZE 64
#define MIN(X, Y) (((X) < (Y)) ? (X) : (Y))
#define QUEUE_LENGTH 8
#define MAGIC 0x42
#define PACKET_CMD 0x87
#define PING_CMD 0xFA
#define ECHO_CMD 0xBB
#define HEADER_SIZE 4u
// When reading successive packets, wait for the bus to be ready for at most this many retries
// Linux requires at least 10000 for more than 256 bytes of data to be sent.
#define READ_RETRIES_TIMEOUT 10000


QueueHandle_t `$INSTANCE_NAME`_read_msg_queue;
QueueHandle_t `$INSTANCE_NAME`_send_msg_queue;

inline `$INSTANCE_NAME`_Msg_t `$INSTANCE_NAME`_Create_Msg(uint16_t size) 
{
    `$INSTANCE_NAME`_Msg_t msg;
    msg.magic = MAGIC;
    msg.cmd = PACKET_CMD;
    msg.size = size;
    if (size > 0)
    {
        msg.data = pvPortMalloc(size);
    }
    return msg;
}

inline void `$INSTANCE_NAME`_Delete_Msg(`$INSTANCE_NAME`_Msg_t msg)
{
    if (msg.size > 0)
    {
        vPortFree(msg.data);
    }
}

inline void `$INSTANCE_NAME`_Push_To_Send_Queue(`$INSTANCE_NAME`_Msg_t *msg)
{
    xQueueSendToBack(`$INSTANCE_NAME`_send_msg_queue, msg, portMAX_DELAY);
}

inline void `$INSTANCE_NAME`_Push_To_Read_Queue(`$INSTANCE_NAME`_Msg_t *msg)
{
    xQueueSendToBack(`$INSTANCE_NAME`_read_msg_queue, msg, portMAX_DELAY);
}

inline `$INSTANCE_NAME`_Msg_t `$INSTANCE_NAME`_Pop_From_Read_Queue()
{
    `$INSTANCE_NAME`_Msg_t msg;
    xQueueReceive(`$INSTANCE_NAME`_read_msg_queue, &msg, portMAX_DELAY);
    return msg;
}

inline `$INSTANCE_NAME`_Msg_t `$INSTANCE_NAME`_Pop_From_Send_Queue()
{
    `$INSTANCE_NAME`_Msg_t msg;
    xQueueReceive(`$INSTANCE_NAME`_send_msg_queue, &msg, portMAX_DELAY);
    return msg;
}

inline void `$INSTANCE_NAME`_CDC_Wait()
{
    /* Wait until component is ready to send data to PC. */
    while (0u == `$INSTANCE_NAME``[USBFS]`CDCIsReady())
    {
        taskYIELD();
    }
}

inline static void `$INSTANCE_NAME`_Send(`$INSTANCE_NAME`_Msg_t msg)
{
    static uint8_t sendbuf[USB_PACKET_SIZE];
    uint16_t count = 0;
    
    sendbuf[0] = msg.magic; // Magic 
    sendbuf[1] = msg.cmd;
    memcpy(sendbuf + 2, &msg.size, sizeof(msg.size));
    
    uint16_t remaining = msg.size;
    
    // First Packet With Header
    count = MIN(remaining, USB_PACKET_SIZE - HEADER_SIZE);
    memcpy(sendbuf + HEADER_SIZE, msg.data + msg.size - remaining, count);
    remaining -= count;
    
    while (0u == `$INSTANCE_NAME``[USBFS]`CDCIsReady())
    {
        taskYIELD();
    }
    
    `$INSTANCE_NAME``[USBFS]`PutData(sendbuf, count + HEADER_SIZE);
    
    // Subsequent Packets, no header needed
    while (remaining > 0) {
        count = MIN(remaining, USB_PACKET_SIZE); 
        `$INSTANCE_NAME`_CDC_Wait();        
        `$INSTANCE_NAME``[USBFS]`PutData(msg.data + msg.size - remaining, count);
        remaining -= count;
    }
    
    /* If the last sent packet is exactly the maximum packet 
    *  size, it is followed by a zero-length packet to assure
    *  that the end of the segment is properly identified by 
    *  the terminal.
    */
    if (USB_PACKET_SIZE == count || msg.size == USB_PACKET_SIZE - HEADER_SIZE)
    {
        `$INSTANCE_NAME`_CDC_Wait();
        /* Send zero-length packet to PC. */
        `$INSTANCE_NAME``[USBFS]`PutData(NULL, 0u);
    }
}

inline static void `$INSTANCE_NAME`_Receive()
{
    static uint8_t recvbuf[USB_PACKET_SIZE];
    static uint16 count;
    static `$INSTANCE_NAME`_Msg_t msg;
    
     /* Read received data and re-enable OUT endpoint. */
    count = `$INSTANCE_NAME``[USBFS]`GetAll(recvbuf);

    if (0u != count)
    {
        // First Packet with Header
        if (count < HEADER_SIZE)
        {
            return;
        }
        
        uint8_t magic = *recvbuf;
        
        if (magic != MAGIC)
        {
            return;
        }
        
        msg = `$INSTANCE_NAME`_Create_Msg(* (uint16_t *)(recvbuf + 2));
        msg.cmd = *(recvbuf + 1);
        
        
        uint16_t remaining = msg.size;
        uint8_t * buf_pointer = msg.data;
        
        count = MIN(count - HEADER_SIZE, remaining);
        memcpy(buf_pointer, recvbuf + HEADER_SIZE, count);
        buf_pointer += count;
        remaining -= count;
        
        // Subsequent Packets, no header
        while (remaining > 0)
        {
            count = MIN(USB_PACKET_SIZE, remaining);
            uint16_t rep = 0;
            while (0u == `$INSTANCE_NAME``[USBFS]`DataIsReady())
            {
                if (rep++ > READ_RETRIES_TIMEOUT)
                {
                    `$INSTANCE_NAME`_Delete_Msg(msg);
                    return;
                }
                taskYIELD();
            }
            count = `$INSTANCE_NAME``[USBFS]`GetData(buf_pointer, count);
            buf_pointer += count;
            remaining -= count;
        }
        
        // CMD
        if (msg.cmd == PACKET_CMD)
        {
            `$INSTANCE_NAME`_Push_To_Read_Queue(&msg);
        }
        else if (msg.cmd == PING_CMD)
        {
            `$INSTANCE_NAME`_Delete_Msg(msg);
            msg = `$INSTANCE_NAME`_Create_Msg(2);
            msg.cmd = PING_CMD;
            msg.data[0] = 'O';
            msg.data[1] = 'K';
            `$INSTANCE_NAME`_Push_To_Send_Queue(&msg);      
        }
        else if (msg.cmd == ECHO_CMD)
        {
            `$INSTANCE_NAME`_Push_To_Send_Queue(&msg);
        }
    }
}

void `$INSTANCE_NAME`_Receive_Task(void *unused)
{
    (void) unused;
    
    for (;;)
    {
        if (0u != `$INSTANCE_NAME``[USBFS]`IsConfigurationChanged()) {
            if (0u != `$INSTANCE_NAME``[USBFS]`GetConfiguration()) {
                `$INSTANCE_NAME``[USBFS]`CDC_Init();
            }
        }
        
        /* Service USB CDC when device is configured. */
        if (0u != `$INSTANCE_NAME``[USBFS]`GetConfiguration())
        {
            /* Check for input data from host. */
            if (0u != `$INSTANCE_NAME``[USBFS]`DataIsReady())
            {
               `$INSTANCE_NAME`_Receive();
            }
        }
        
        taskYIELD();
    }
}

void `$INSTANCE_NAME`_Send_Task(void *unused)
{
    (void) unused;
    `$INSTANCE_NAME`_Msg_t msg;
    
    for (;;)
    {
        msg = `$INSTANCE_NAME`_Pop_From_Send_Queue();
        `$INSTANCE_NAME`_Send(msg);
        `$INSTANCE_NAME`_Delete_Msg(msg);
    }
}

void `$INSTANCE_NAME`_boot()
{
    `$INSTANCE_NAME`_read_msg_queue = xQueueCreate(QUEUE_LENGTH, sizeof(`$INSTANCE_NAME`_Msg_t));
    `$INSTANCE_NAME`_send_msg_queue = xQueueCreate(QUEUE_LENGTH, sizeof(`$INSTANCE_NAME`_Msg_t));
    `$INSTANCE_NAME``[USBFS]`Start(0, `$INSTANCE_NAME``[USBFS]`5V_OPERATION);
    
    xTaskCreate(`$INSTANCE_NAME`_Receive_Task, "USB-R", configMINIMAL_STACK_SIZE + 32, 0, 1, 0);
    xTaskCreate(`$INSTANCE_NAME`_Send_Task, "USB-S", configMINIMAL_STACK_SIZE + 32, 0, 1, 0);
}

/* [] END OF FILE */

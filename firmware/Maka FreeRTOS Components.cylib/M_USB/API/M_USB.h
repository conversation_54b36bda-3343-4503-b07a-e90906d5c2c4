/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/

#ifndef `$INSTANCE_NAME`_H
#define `$INSTANCE_NAME`_H

#include "CyLib.h"

typedef struct
{
    uint8_t magic;
    uint8_t cmd;
    uint16_t size;
    uint8_t *data;
} `$INSTANCE_NAME`_Msg_t;

void `$INSTANCE_NAME`_boot();
`$INSTANCE_NAME`_Msg_t `$INSTANCE_NAME`_Create_Msg(uint16_t size);
void `$INSTANCE_NAME`_Delete_Msg(`$INSTANCE_NAME`_Msg_t msg);
`$INSTANCE_NAME`_Msg_t `$INSTANCE_NAME`_Pop_From_Read_Queue();
void `$INSTANCE_NAME`_Push_To_Send_Queue(`$INSTANCE_NAME`_Msg_t *msg);



#endif

/* [] END OF FILE */

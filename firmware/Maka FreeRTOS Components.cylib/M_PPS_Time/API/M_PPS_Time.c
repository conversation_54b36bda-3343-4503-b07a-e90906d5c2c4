/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#include "FreeRTOS.h"
#include "project.h"
#include "queue.h"
#include "stdlib.h"
#include "`$INSTANCE_NAME`_M_PPS_Time.h"

#define MVG_AVG_PCT 0.05
#define CAPTURE_USEC (100 * 1000)

#define TIMER_REFERENCE 1
#define RELATIVE 0

volatile uint32_t g_pps_epoch_resets = 0;
volatile uint32_t g_pps_plus1_micros = 0;
volatile uint32_t g_pps_signal_reject = 0;
volatile uint32_t g_pps_signal_missed = 0;

M_Time_Ref_t `$INSTANCE_NAME`_Time_ref;

uint32_t read_timer_val(int timer_reference)
{
    uint32_t val;
    uint32_t last_val;

    last_val = `$INSTANCE_NAME`_Time_ref._last_pps_timer_triggered;

    if (timer_reference)
    {
        val = `$INSTANCE_NAME`_PPS_TIMER_ReadCounter();
        /* Save off last triggered val and set it to the current counter value unmolested */
        `$INSTANCE_NAME`_Time_ref._last_pps_timer_triggered = val;
    }
    else
    {
        val = `$INSTANCE_NAME`_PPS_TIMER_ReadCounter();
    }

    /* If the value I just read is more than the last value I gave out then we have rolled over */
    if (val > last_val)
    {
        val = last_val + PPS_TIMER_PERIOD - val;
    }
    else
    {
        val = last_val - val;
    }

    val += `$INSTANCE_NAME`_Time_ref.pps_timer_addition;
    if (timer_reference)
    {
        `$INSTANCE_NAME`_Time_ref.pps_timer_addition = 0;
    }
    return val;
}

M_Error_Code_t `$INSTANCE_NAME`_Get_Timestamp_Unsafe(M_Timestamp_t *out, int32_t *drift_out)
{
    uint64_t total_usecs;
    uint32_t scaled_usecs = read_timer_val(RELATIVE) * `$INSTANCE_NAME`_Time_ref.freq_mul;

    total_usecs = `$INSTANCE_NAME`_Time_ref.epoch_usecs +                        // base for counting
                  (uint64_t)`$INSTANCE_NAME`_Time_ref.pps_ticks * USEC_PER_SEC + // ticks since then in usec
                  scaled_usecs;                                                  // usecs since last tick

    /* Don't go back in time, let reality catch up to us */
    if (`$INSTANCE_NAME`_Time_ref.latest_usecs >= total_usecs)
    {
        g_pps_plus1_micros++;
        total_usecs = `$INSTANCE_NAME`_Time_ref.latest_usecs + 1;
    }
    `$INSTANCE_NAME`_Time_ref.latest_usecs = total_usecs;
    out->seconds = total_usecs / USEC_PER_SEC;
    out->micros = total_usecs % USEC_PER_SEC;
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_Get_Timestamp(M_Timestamp_t *out)
{
    taskENTER_CRITICAL();
    M_Error_Code_t result = `$INSTANCE_NAME`_Get_Timestamp_Unsafe(out, NULL);
    taskEXIT_CRITICAL();
    return result;
}

M_Error_Code_t `$INSTANCE_NAME`_Get_Timestamp_From_Critical(M_Timestamp_t *out)
{
    return `$INSTANCE_NAME`_Get_Timestamp_Unsafe(out, NULL);
}

M_Error_Code_t `$INSTANCE_NAME`_Get_Debug(M_Time_Debug_Reply_t *reply)
{
    taskENTER_CRITICAL();
    reply->error_ticks2 = g_pps_signal_reject + g_pps_signal_missed;
    M_Error_Code_t result = `$INSTANCE_NAME`_Get_Timestamp_Unsafe(&reply->timestamp, &reply->error_ticks2);
    reply->pps_ticks = `$INSTANCE_NAME`_Time_ref.pps_ticks;
    reply->pps_timer_val = `$INSTANCE_NAME`_Time_ref.pps_timer_val;
    reply->freq_mul = `$INSTANCE_NAME`_Time_ref.freq_mul;
    reply->error_ticks = (`$INSTANCE_NAME`_Time_ref.freq_mul - `$INSTANCE_NAME`_Time_ref.last_freq_mul) *
                         `$INSTANCE_NAME`_Time_ref.pps_timer_val;
    taskEXIT_CRITICAL();
    return result;
}

CY_ISR(`$INSTANCE_NAME`_PPS_ON_ISR)
{
    UBaseType_t uxSavedInterruptStatus = taskENTER_CRITICAL_FROM_ISR();
    
    uint32_t timer_val = read_timer_val(RELATIVE);
    `$INSTANCE_NAME`_Time_ref._last_pps_on_time = timer_val;

    taskEXIT_CRITICAL_FROM_ISR(uxSavedInterruptStatus);
}

CY_ISR(`$INSTANCE_NAME`_PPS_OFF_ISR)
{
    UBaseType_t uxSavedInterruptStatus = taskENTER_CRITICAL_FROM_ISR();

   /* Check for spurious signals */
    uint32_t timer_val = read_timer_val(TIMER_REFERENCE);
    if (timer_val - `$INSTANCE_NAME`_Time_ref._last_pps_on_time < 95000 ||
        timer_val - `$INSTANCE_NAME`_Time_ref._last_pps_on_time > 105000)
    {
        g_pps_signal_reject++;
        `$INSTANCE_NAME`_Time_ref.pps_timer_addition = timer_val;
        taskEXIT_CRITICAL_FROM_ISR(uxSavedInterruptStatus);
        return;
    }

    double last = `$INSTANCE_NAME`_Time_ref.freq_mul;
    `$INSTANCE_NAME`_Time_ref.last_freq_mul = last;
    while (timer_val >= 1500000)
    {
        `$INSTANCE_NAME`_Time_ref.pps_ticks += 1;
        timer_val -= 1000000;
    }
    `$INSTANCE_NAME`_Time_ref.pps_ticks += 1; // one more tick
    `$INSTANCE_NAME`_Time_ref.pps_timer_val = timer_val; // Counter val we hit at

    /* From here we can assume all missed / extras have been taken care of */

    double mvg_avg_pct = MVG_AVG_PCT;
    double target = USEC_PER_SEC / (double)`$INSTANCE_NAME`_Time_ref.pps_timer_val;
    double error = target - last;
    if (abs(error * `$INSTANCE_NAME`_Time_ref.pps_timer_val) > 200)
    {
        mvg_avg_pct = 0.9;
    }
    `$INSTANCE_NAME`_Time_ref.freq_mul = (1.0-mvg_avg_pct) * `$INSTANCE_NAME`_Time_ref.freq_mul +
                                         mvg_avg_pct * target;

    taskEXIT_CRITICAL_FROM_ISR(uxSavedInterruptStatus);
}

uint64_t get_epoch_usecs(M_Timestamp_t *timestamp)
{
    /* Compose from the timestamp components */
    /* For now we just truncate to the last second */
    uint64_t usecs = (uint64_t)timestamp->seconds * (uint64_t)USEC_PER_SEC;
    return usecs;
}

M_Error_Code_t `$INSTANCE_NAME`_Set_Epoch_Time(M_Timestamp_t *timestamp)
{
    taskENTER_CRITICAL();
    g_pps_epoch_resets++;
    g_pps_plus1_micros = 0;
    g_pps_signal_reject = 0;
    g_pps_signal_missed = 0;


    /* Remember these */
    uint32_t last_triggered = `$INSTANCE_NAME`_Time_ref._last_pps_timer_triggered;
    double freq_mul = `$INSTANCE_NAME`_Time_ref.freq_mul;

    memset(&`$INSTANCE_NAME`_Time_ref, 0, sizeof(M_Time_Ref_t));

    `$INSTANCE_NAME`_Time_ref._last_pps_timer_triggered = last_triggered;

    /* reset the epoch */
    `$INSTANCE_NAME`_Time_ref.epoch_usecs = get_epoch_usecs(timestamp) + CAPTURE_USEC;

    /* Reset the new frequency in case things got very out of sync */
    `$INSTANCE_NAME`_Time_ref.freq_mul = 1.0;

    /* keep the old frequiency for comparison */
    `$INSTANCE_NAME`_Time_ref.last_freq_mul = freq_mul;

    taskEXIT_CRITICAL();

    return M_ERROR_CODE_OK;
}

void `$INSTANCE_NAME`_Handle_Request(M_Time_Request_t *request, M_Time_Reply_t *reply) 
{
    M_Error_Code_t result;
    reply->metadata = request->metadata;
    switch(request->request_type)
    {
        case TIME_REQUEST_TYPE_SET:
            reply->reply_type = TIME_REPLY_TYPE_ACK;
            result = `$INSTANCE_NAME`_Set_Epoch_Time(&request->request.set.timestamp);
            break;
        case TIME_REQUEST_TYPE_GET:
            reply->reply_type = TIME_REPLY_TYPE_TIMESTAMP;
            result = `$INSTANCE_NAME`_Get_Timestamp(&reply->reply.timestamp);
            break;
        case TIME_REQUEST_TYPE_DEBUG:
            reply->reply_type = TIME_REPLY_TYPE_DEBUG;
            result = `$INSTANCE_NAME`_Get_Debug(&reply->reply.debug);
            break;
        default:
            result = M_ERROR_CODE_PPS_NO_REQUEST_HANDLER;
            break;
    }
    
    if (result != M_ERROR_CODE_OK)
    {
        reply->reply_type = TIME_REPLY_TYPE_ERROR;
        reply->reply.error = result;
    }
}

void `$INSTANCE_NAME`_Boot_Component()
{
    memset(&`$INSTANCE_NAME`_Time_ref, 0, sizeof(M_Time_Ref_t));
    `$INSTANCE_NAME`_Time_ref.freq_mul = 1.0;
    `$INSTANCE_NAME`_Time_ref.last_freq_mul = 1.0;

    `$INSTANCE_NAME`_PPS_CLOCK_Start();
    `$INSTANCE_NAME`_PPS_TIMER_Start();
    `$INSTANCE_NAME`_PPS_ON_ISR_StartEx(`$INSTANCE_NAME`_PPS_ON_ISR);
    `$INSTANCE_NAME`_PPS_OFF_ISR_StartEx(`$INSTANCE_NAME`_PPS_OFF_ISR);
}



/* [] END OF FILE */

/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef `$INSTANCE_NAME`_M_PPS_TIME_H
#define `$INSTANCE_NAME`_M_PPS_TIME_H

#include "m_time_request.h"
#include "FreeRTOS.h"

void `$INSTANCE_NAME`_Boot_Component();
void `$INSTANCE_NAME`_Handle_Request(M_Time_Request_t *request, M_Time_Reply_t *reply);
M_Error_Code_t `$INSTANCE_NAME`_Get_Timestamp(M_Timestamp_t *out);
M_Error_Code_t `$INSTANCE_NAME`_Get_Timestamp_From_Critical(M_Timestamp_t *out);

#endif // `$INSTANCE_NAME`_M_LASER_H

/* [] END OF FILE */

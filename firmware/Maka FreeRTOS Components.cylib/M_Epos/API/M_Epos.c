/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#include "`$INSTANCE_NAME`_M_Epos.h"
#include "stdlib.h"
#include "string.h"
#include "utils.h"

M_Error_Code_t `$INSTANCE_NAME`_Await_Status_PDO(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg, uint16_t timeout_ms, uint16_t expected, uint16_t expected_neg)
{
    TickType_t tick_start = xTaskGetTickCount();
    TickType_t max_wait_time = timeout_ms / portTICK_PERIOD_MS;
    TickType_t elapsed_time = xTaskGetTickCount() - tick_start;
    while (elapsed_time < max_wait_time)
    {  
        uint16_t* pdo_data_ptr = (uint16_t*)msg->pkt.pdo.data;
        // Verify expected 1 bits and 0 bits. Definition of which bit means what is in the epos firmware spec.
        RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME``[MCAN]`Await_Reply(node_ref, msg, max_wait_time - elapsed_time, CAN_OPEN_FUNC_CODE_PDO1_TX))
        if (msg->pkt_type == CAN_OPEN_PDO_PKT_TYPE &&
            ((*pdo_data_ptr) & expected) == expected &&
            (~(*pdo_data_ptr) & expected_neg) == expected_neg)
        {
            return M_ERROR_CODE_OK;
        }
        elapsed_time = xTaskGetTickCount() - tick_start;
    }
    return M_ERROR_CODE_EPOS_AWAIT_STATUS_PDO_TIMEOUT;
}

inline M_Error_Code_t `$INSTANCE_NAME`_Await_Status(CAN_Node_Ref_t *node_ref, uint16_t timeout_ms, uint16_t expected, uint16_t expected_neg)
{
    CAN_Open_Message_t msg;
    return `$INSTANCE_NAME`_Await_Status_PDO(node_ref, &msg, timeout_ms, expected, expected_neg);
}

M_Error_Code_t `$INSTANCE_NAME`_Control_Word_Await_Status(CAN_Node_Ref_t *node_ref, uint16_t control_word, uint16_t expected_status, uint16_t expected_neg_status)
{
    CAN_Open_Message_t msg;
    `$INSTANCE_NAME`_MCAN_Send_PDO(node_ref->node_id, CAN_OPEN_FUNC_CODE_PDO1_RX, (uint8_t *)&control_word, sizeof(control_word));
    return `$INSTANCE_NAME`_Await_Status_PDO(node_ref, &msg, CAN_OPEN_DEFAULT_TIMEOUT_MS, expected_status, expected_neg_status);
}

M_Error_Code_t `$INSTANCE_NAME`_Enable_Node(CAN_Node_Ref_t *node_ref)
{
    // Shutdown Command Actually gets the node in a state closer to operational
    RETURN_CODE_IF_NOT_OK(
        `$INSTANCE_NAME`_Control_Word_Await_Status(
            node_ref, 
            `$INSTANCE_NAME`_CONTROL_COMMAND_SHUTDOWN, 
            `$INSTANCE_NAME`_STATUS_QUICK_STOP | `$INSTANCE_NAME`_STATUS_READY_TO_SWITCH_ON,
            `$INSTANCE_NAME`_STATUS_SWITCH_ON_DISABLED | `$INSTANCE_NAME`_STATUS_SWITCHED_ON | `$INSTANCE_NAME`_STATUS_OPERATIONS_ENABLED | `$INSTANCE_NAME`_STATUS_FAULT
        )
    )
    
    // Switch On Enable, get the node in its operational state, Set Immediate makes the position request immediate instead of queued
    return `$INSTANCE_NAME`_Control_Word_Await_Status(
                node_ref, 
                `$INSTANCE_NAME`_CONTROL_COMMAND_SWITCH_ON_ENABLE | `$INSTANCE_NAME`_CONTROL_CHANGE_SET_IMMEDIATELY, 
                `$INSTANCE_NAME`_STATUS_QUICK_STOP | `$INSTANCE_NAME`_STATUS_READY_TO_SWITCH_ON | `$INSTANCE_NAME`_STATUS_SWITCHED_ON | `$INSTANCE_NAME`_STATUS_OPERATIONS_ENABLED,
                `$INSTANCE_NAME`_STATUS_SWITCH_ON_DISABLED | `$INSTANCE_NAME`_STATUS_FAULT
            );
}

M_Error_Code_t `$INSTANCE_NAME`_Disable_Node(CAN_Node_Ref_t *node_ref)
{
    return `$INSTANCE_NAME`_Control_Word_Await_Status(
            node_ref, 
            `$INSTANCE_NAME`_CONTROL_COMMAND_DISABLE, 
            `$INSTANCE_NAME`_STATUS_QUICK_STOP | `$INSTANCE_NAME`_STATUS_READY_TO_SWITCH_ON | `$INSTANCE_NAME`_STATUS_SWITCHED_ON,
            `$INSTANCE_NAME`_STATUS_SWITCH_ON_DISABLED | `$INSTANCE_NAME`_STATUS_OPERATIONS_ENABLED | `$INSTANCE_NAME`_STATUS_FAULT
        );
}

// Must be called in Profile Position Mode Only
// Profile velocity is unsigned
M_Error_Code_t `$INSTANCE_NAME`_Go_To_Position(CAN_Node_Ref_t *node_ref, int32_t position, uint32_t velocity)
{   
    CAN_Open_Message_t msg;
    const uint16_t start_word = `$INSTANCE_NAME`_CONTROL_COMMAND_START_OPERATION;
    const uint16_t reset_word = `$INSTANCE_NAME`_CONTROL_COMMAND_SWITCH_ON_ENABLE | `$INSTANCE_NAME`_CONTROL_CHANGE_SET_IMMEDIATELY;
    uint8_t data[8];
    memcpy(data, &position, sizeof(int32_t));
    memcpy(data + sizeof(int32_t), &velocity, sizeof(uint32_t));
    `$INSTANCE_NAME``[MCAN]`Send_PDO(node_ref->node_id, CAN_OPEN_FUNC_CODE_PDO2_RX, data, 8);
    
    `$INSTANCE_NAME``[MCAN]`Send_PDO(node_ref->node_id, CAN_OPEN_FUNC_CODE_PDO1_RX, (uint8_t*)&start_word, sizeof(start_word));
    
    // The below may fail for near zero movements
    // I found it preferable to ignore the result if it times out
    `$INSTANCE_NAME`_Await_Status_PDO(node_ref, &msg, 10, `$INSTANCE_NAME`_STATUS_SETPOINT_AWKNOWLEDGED, 0x00);
    
    // Reset Command Control Word so next Go To works
    // The servo is already moving when we make that call
    `$INSTANCE_NAME``[MCAN]`Send_PDO(node_ref->node_id, CAN_OPEN_FUNC_CODE_PDO1_RX, (uint8_t *)&reset_word, sizeof(reset_word));
    
    return M_ERROR_CODE_OK;
}

// Actual Velocity is signed
M_Error_Code_t `$INSTANCE_NAME`_Get_Actual_Position_Velocity(CAN_Node_Ref_t *node_ref, int32_t *position, int32_t *velocity)
{
    CAN_Open_Message_t msg;
    xQueueReset(node_ref->recv_queue);
    `$INSTANCE_NAME``[MCAN]`Send_RTR_PDO(node_ref->node_id, CAN_OPEN_FUNC_CODE_PDO2_TX);
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME``[MCAN]`Await_Reply(node_ref, &msg, CAN_OPEN_DEFAULT_PDO_RTR_TIMEOUT_MS, CAN_OPEN_FUNC_CODE_PDO2_TX))
    uint32_t* pdo_data_ptr = (uint32_t*)msg.pkt.pdo.data;
    *position = *(int32_t *)pdo_data_ptr;
    *velocity = *(int32_t *)(pdo_data_ptr + 1);
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_Limit_Condition(CAN_Node_Ref_t *node_ref, uint32_t bin_switch, bool *out)
{
    CAN_Open_Message_t msg;
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Read_Digital_Input_State_Polarized(node_ref, &msg))
    uint32_t* sdo_data_ptr = (uint32_t*)msg.pkt.sdo.data;
    *out = ((*sdo_data_ptr) & bin_switch) == bin_switch;
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_Await_Target_Reached(CAN_Node_Ref_t *node_ref, uint16_t timeout_ms, uint16_t *reach_time_ms_out)
{
    TickType_t tick_start = xTaskGetTickCount();
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Await_Status(node_ref, timeout_ms, EPOS_STATUS_TARGET_REACHED, 0x00));
    if (reach_time_ms_out != NULL)
    {
        *reach_time_ms_out = (xTaskGetTickCount() - tick_start) * portTICK_PERIOD_MS;   
    }
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_Await_Settling(CAN_Node_Ref_t *node_ref, int32_t target_position, uint16_t window, uint16_t timeout_ms, uint16_t *settle_time_ms_out)
{   
    TickType_t tick_start = xTaskGetTickCount();
    TickType_t max_wait_time = pdMS_TO_TICKS(timeout_ms);
    TickType_t last_round_time = tick_start;
    const static TickType_t period = pdMS_TO_TICKS(3); // TODO make this configurable
    int32_t current_position = 0;
    int32_t last_error = 0;
    int32_t current_error = 0;
    int32_t last_velocity = 0;
    int32_t current_velocity = 0;
    
    do
    {
        if (`$INSTANCE_NAME`_Get_Actual_Position_Velocity(node_ref, &current_position, &current_velocity) != M_ERROR_CODE_OK)
        {
            vTaskDelayUntil(&last_round_time, period);
            continue;
        }
        
        current_error = abs(target_position - current_position);
        
        // Settling Conditions
        // Standstill Condition Test First (No Velocity + positions within window)
        // Derivative Condition Test Second (Velocity sign change meaning peak + positions within window)
        if (current_error < window &&
            last_error < window &&
            ((last_velocity == 0 && current_velocity == 0) ||
             (last_velocity ^ current_velocity) < 0))
        {
            if (settle_time_ms_out != NULL)
            {
                *settle_time_ms_out = (xTaskGetTickCount() - tick_start) * portTICK_PERIOD_MS;
            }
            return M_ERROR_CODE_OK;
        }
        
        last_error = current_error;
        last_velocity = current_velocity;
        
        vTaskDelayUntil(&last_round_time, period);
             
    } while (last_round_time - tick_start < max_wait_time);
    return M_ERROR_CODE_EPOS_SETTLE_TIMEOUT;
}

M_Error_Code_t `$INSTANCE_NAME`_Calibration_Settle(
    CAN_Node_Ref_t *node_ref,
    int32_t target_position,
    uint16_t window,
    uint16_t time_window_ms,
    uint16_t timeout_ms,
    uint8_t period_ms,
    uint16_t *settle_time_ms_out)
{   
    TickType_t tick_start = xTaskGetTickCount();
    TickType_t max_wait_time = pdMS_TO_TICKS(timeout_ms);
    TickType_t last_round_time = tick_start;
    TickType_t time_window = pdMS_TO_TICKS(time_window_ms);
    TickType_t period = pdMS_TO_TICKS(period_ms);
    TickType_t current_time = 0;
    int32_t current_position = 0;
    int32_t current_error = 0;
    int32_t current_velocity = 0;
    
    bool found_first = false;
    TickType_t first_in_window_time = 0;
    
    do
    {
        RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Get_Actual_Position_Velocity(node_ref, &current_position, &current_velocity));
        
        current_error = abs(target_position - current_position);
        
        if (current_error < window)
        {
            current_time = xTaskGetTickCount();
            if (!found_first)
            {
                first_in_window_time = current_time;
                found_first = true;
            }
            else if (current_time - first_in_window_time > time_window)
            {
                *settle_time_ms_out = (first_in_window_time - tick_start) * portTICK_PERIOD_MS;
                return M_ERROR_CODE_OK;
            }
        }
        else
        {
            found_first = false;
        }
        
        vTaskDelayUntil(&last_round_time, period);
             
    } while (last_round_time - tick_start < max_wait_time);
    return M_ERROR_CODE_EPOS_SETTLE_TIMEOUT;
}

M_Error_Code_t `$INSTANCE_NAME`_Find_Hard_Stop_Limit(CAN_Node_Ref_t *node_ref, int16_t step_size, uint16_t offset, int32_t min_position, int32_t max_position, uint32_t profile_velocity, uint16_t settle_timeout_ms, int32_t *out)
{
    int32_t last_position = 0;
    int32_t last_retry_position = 0;
    uint8_t retries = 0;
    
    do
    {
        if (retries >= 10)
        {
            return M_ERROR_CODE_EPOS_FIND_HARD_LIMITS_MAX_RETRIES;
        }
        
        RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Go_To_Position(node_ref, last_position, profile_velocity))
        
        if(`$INSTANCE_NAME`_Await_Settling(node_ref, last_position, `$INSTANCE_NAME`_DEFAULT_SETTLE_WINDOW, settle_timeout_ms, NULL) != M_ERROR_CODE_OK)
        {
            RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_Control_Word(node_ref, `$INSTANCE_NAME`_CONTROL_COMMAND_SWITCH_ON_ENABLE | `$INSTANCE_NAME`_CONTROL_CHANGE_SET_IMMEDIATELY | `$INSTANCE_NAME`_CONTROL_QUICK_STOP))
            
            // If settling times out, we found our stop
            int32_t velocity;
            RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Get_Actual_Position_Velocity(node_ref, out, &velocity));
            if (*out <= offset)
            {
                return M_ERROR_CODE_EPOS_HARD_LIMITS_RANGE_TOO_SMALL;
            }
            
            // If we failed to settled too close to the target position, we may not have actually reached the hard stop so we continue
            // This can occur in a few rare instances
            if(!(*out < (last_position - (step_size / 10))))
            {
                retries += 1;
                last_position = last_position + step_size;
                continue;
            }
            
            // In case we didn't actually move
            if (*out > (last_position - step_size - (step_size / 20)) &&
                *out < (last_position - step_size + (step_size / 20)) &&
                (*out != last_retry_position || last_retry_position == 0))
            {
                retries += 1;
                last_retry_position = *out;
                continue;
            }
            last_retry_position = 0;
            
            *out -= offset;
              
            RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Go_To_Position(node_ref, *out, profile_velocity));
            
            // The timeout is overly large below since after hitting the hard stop, the servo takes a little longer to recover
            RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Await_Settling(node_ref, *out, 50, 5000, NULL));
            return M_ERROR_CODE_OK;
        }
        
        // Update Position
        last_position = last_position + step_size;
        
    } while (last_position >= min_position && last_position <= max_position);
    return M_ERROR_CODE_EPOS_FIND_HARD_LIMITS_FAIL;
}

M_Error_Code_t `$INSTANCE_NAME`_Find_Switch_Limit(CAN_Node_Ref_t *node_ref, uint32_t bin_switch, int16_t step_size, int16_t threshold_step, int32_t min_position, int32_t max_position, uint32_t profile_velocity, uint16_t settle_timeout_ms, int32_t *out)
{
    int32_t last_position = 0;
    bool result = false;
    bool last_result = false;
    
    // Initial State of the Switch
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Limit_Condition(node_ref, bin_switch, &last_result))
    
    do
    {
        RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Go_To_Position(node_ref, last_position, profile_velocity))
        
        
        if(`$INSTANCE_NAME`_Await_Settling(node_ref, last_position, threshold_step, settle_timeout_ms, NULL) != M_ERROR_CODE_OK)
        {
            // If settling times out, we should still be close enough
            // Keep Going
        }
        
        RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Limit_Condition(node_ref, bin_switch, &result))
        
        // Switch State changed
        if (result != last_result) 
        {
            // Condition Achieved, position found
            if (abs(step_size) <= abs(threshold_step)) 
            {
                *out = last_position;
                return M_ERROR_CODE_OK;
            }
            // Keep searching with smaller step
            else {          
                step_size = -step_size / 2;
            }
        }
        
        // Update Position
        last_result = result;
        last_position = last_position + step_size;
        
    } while (last_position >= min_position && last_position <= max_position);
    return M_ERROR_CODE_EPOS_FIND_LIMIT_SWITCH_FAIL;
}

M_Error_Code_t `$INSTANCE_NAME`_Setup_PDOs(CAN_Node_Ref_t *node_ref)
{
    // Control RX PDO
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_RXPDO1_Mapping_Number(node_ref, 0))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_RXPDO1_COBID(node_ref, `$INSTANCE_NAME`_PDO_BASE_COBID_RX1 | node_ref->node_id))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_RXPDO1_Transmission(node_ref, `$INSTANCE_NAME`_PDO_TRANSMISSION_ASYNC))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_RXPDO1_Mapping_Object(node_ref, 0x01, (0x6040 << 16) + (0x00 << 8) + 16)) // Control Word
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_RXPDO1_Mapping_Number(node_ref, 1))
    
    // Go To and Move RX PDO
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_RXPDO2_Mapping_Number(node_ref, 0))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_RXPDO2_COBID(node_ref, `$INSTANCE_NAME`_PDO_BASE_COBID_RX2 | node_ref->node_id))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_RXPDO2_Transmission(node_ref, `$INSTANCE_NAME`_PDO_TRANSMISSION_ASYNC))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_RXPDO2_Mapping_Object(node_ref, 0x01, (0x607A << 16) + (0x00 << 8) + 32)) // Target Position
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_RXPDO2_Mapping_Object(node_ref, 0x02, (0x6081 << 16) + (0x00 << 8) + 32)) // Profile Velocity
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_RXPDO2_Mapping_Number(node_ref, 2))
    
    // Disabled RX PDO
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_RXPDO3_Mapping_Number(node_ref, 0))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_RXPDO3_COBID(node_ref, `$INSTANCE_NAME`_PDO_BASE_COBID_RX3 | node_ref->node_id | `$INSTANCE_NAME`_PDO_COBID_VALID))
    
    // Disabled RX PDO
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_RXPDO4_Mapping_Number(node_ref, 0))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_RXPDO4_COBID(node_ref, `$INSTANCE_NAME`_PDO_BASE_COBID_RX4 | node_ref->node_id | `$INSTANCE_NAME`_PDO_COBID_VALID))
    
    // Status TX PDO
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_TXPDO1_Mapping_Number(node_ref, 0))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_TXPDO1_COBID(node_ref, `$INSTANCE_NAME`_PDO_BASE_COBID_TX1 | node_ref->node_id))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_TXPDO1_Transmission(node_ref, `$INSTANCE_NAME`_PDO_TRANSMISSION_ASYNC))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_TXPDO1_Mapping_Object(node_ref, 0x01, (0x6041 << 16) + (0x00 << 8) + 16)) // Status Word
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_TXPDO1_Mapping_Number(node_ref, 1))
    
    // Actual Position/Velocity RTR TX PDO
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_TXPDO2_Mapping_Number(node_ref, 0))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_TXPDO2_COBID(node_ref, `$INSTANCE_NAME`_PDO_BASE_COBID_TX2 | node_ref->node_id))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_TXPDO2_Transmission(node_ref, `$INSTANCE_NAME`_PDO_TRANSMISSION_ASYNC_RTR))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_TXPDO2_Mapping_Object(node_ref, 0x01, (0x6064 << 16) + (0x00 << 8) + 32)) // Position Actual
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_TXPDO2_Mapping_Object(node_ref, 0x02, (0x606C << 16) + (0x00 << 8) + 32)) // Velocity Actual
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_TXPDO2_Mapping_Number(node_ref, 2))
    
    // Disabled TX PDO
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_TXPDO3_Mapping_Number(node_ref, 0))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_TXPDO3_COBID(node_ref, `$INSTANCE_NAME`_PDO_BASE_COBID_TX3 | node_ref->node_id | `$INSTANCE_NAME`_PDO_COBID_VALID))
    
    // Disabled TX PDO
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_TXPDO4_Mapping_Number(node_ref, 0))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_TXPDO4_COBID(node_ref, `$INSTANCE_NAME`_PDO_BASE_COBID_TX4 | node_ref->node_id | `$INSTANCE_NAME`_PDO_COBID_VALID))
    
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_Home_With_Method(CAN_Node_Ref_t *node_ref, uint32_t method)
{
    CAN_Open_Message_t msg;
    M_Error_Code_t result = M_ERROR_CODE_OK;
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_Homing_Method(node_ref, method))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Read_Homing_Method(node_ref, &msg))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_Control_Word(node_ref, `$INSTANCE_NAME`_CONTROL_COMMAND_START_OPERATION))
    
    if(`$INSTANCE_NAME`_Await_Status_PDO(node_ref, &msg, 1000, `$INSTANCE_NAME`_STATUS_SETPOINT_AWKNOWLEDGED, 0x00) != M_ERROR_CODE_OK)
    {
        // This is an error case where we can't find the switch (Started already past the switch)
        // We halt the homing to avoid having the servo apply pressure against the hard stop
        // We then reboot the node, ready to receive new commands
        result = `$INSTANCE_NAME`_Write_Control_Word(node_ref, `$INSTANCE_NAME`_CONTROL_COMMAND_SWITCH_ON_ENABLE | `$INSTANCE_NAME`_CONTROL_CHANGE_SET_IMMEDIATELY | `$INSTANCE_NAME`_CONTROL_HALT | `$INSTANCE_NAME`_CONTROL_QUICK_STOP);
        result = result != M_ERROR_CODE_OK ? result : `$INSTANCE_NAME`_Await_Status_PDO(node_ref, &msg, CAN_OPEN_DEFAULT_TIMEOUT_MS, 0x00, 0x00);
        result = result != M_ERROR_CODE_OK ? result : `$INSTANCE_NAME`_Disable_Node(node_ref);
        result = result != M_ERROR_CODE_OK ? result : `$INSTANCE_NAME`_Enable_Node(node_ref);
    }
    
    // We need to reset the control word after homing is done
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_Control_Word(node_ref, `$INSTANCE_NAME`_CONTROL_COMMAND_SWITCH_ON_ENABLE | `$INSTANCE_NAME`_CONTROL_CHANGE_SET_IMMEDIATELY))
    return result;
}

M_Error_Code_t `$INSTANCE_NAME`_Hard_Stop_Home(CAN_Node_Ref_t *node_ref, int16_t step_size, uint16_t offset, int32_t min_position, int32_t max_position, uint32_t profile_velocity, uint16_t settle_timeout_ms, int32_t *out_limit)
{
    // Set Homing Mode
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_Mode_Of_Operation(node_ref, EPOS_HOMING_MODE))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_Home_Offset(node_ref, offset))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_Homing_Current_Threshold(node_ref, `$INSTANCE_NAME`_DEFAULT_HOMING_CURRENT))
    
    // Base Double Homing to ensure position is consistent
    if (`$INSTANCE_NAME`_Home_With_Method(node_ref, `$INSTANCE_NAME`_HOMING_METHOD_CURRENT_POSITIVE_SPEED) != M_ERROR_CODE_OK)
    {
        // If the first homing direction times out, we may still be able to home and should
        // be above the second switch, leading to a valid homing position
    }
    
    // Actual Homing
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Home_With_Method(node_ref, `$INSTANCE_NAME`_HOMING_METHOD_CURRENT_NEGATIVE_SPEED))
    
    // Stop Movement
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_Control_Word(node_ref, `$INSTANCE_NAME`_CONTROL_COMMAND_SWITCH_ON_ENABLE | `$INSTANCE_NAME`_CONTROL_CHANGE_SET_IMMEDIATELY | `$INSTANCE_NAME`_CONTROL_QUICK_STOP));
    
    // Profile Position Mode Ready
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_Mode_Of_Operation(node_ref, `$INSTANCE_NAME`_PROFILE_POSITION_MODE))
    
    // Find Positive Hard Limit
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Find_Hard_Stop_Limit(node_ref, step_size, offset, min_position, max_position, profile_velocity, settle_timeout_ms, out_limit))
    
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_Limit_Switch_Home(CAN_Node_Ref_t *node_ref, int16_t step_size, int16_t threshold_step, int32_t min_position, int32_t max_position, uint32_t profile_velocity, uint16_t settle_timeout_ms, int32_t *out_limit)
{
    // Set Homing Mode
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_Mode_Of_Operation(node_ref, `$INSTANCE_NAME`_HOMING_MODE))
    RETURN_CODE_IF_NOT_OK(EPOS_Write_Home_Offset(node_ref, 0))
    
    // Base Double Homing to ensure position is not in switch slack
    if (`$INSTANCE_NAME`_Home_With_Method(node_ref, `$INSTANCE_NAME`_HOMING_METHOD_POSITIVE_LIMIT_SWITCH) != M_ERROR_CODE_OK)
    {
        // If the first homing direction times out, we may still be able to home and should
        // be above the second switch, leading to a valid homing position
    }
    
    // Actual Homing
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Home_With_Method(node_ref, `$INSTANCE_NAME`_HOMING_METHOD_NEGATIVE_LIMIT_SWITCH))
    
    // Profile Position Mode Ready
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_Mode_Of_Operation(node_ref, `$INSTANCE_NAME`_PROFILE_POSITION_MODE))
    
    // Find Positive Hard Limit
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Find_Switch_Limit(node_ref, `$INSTANCE_NAME`_DI_POSITIVE_LIMIT_SWITCH_NO_ERROR, step_size, threshold_step, min_position, max_position, profile_velocity, settle_timeout_ms, out_limit))
    
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_Set_Positional_PID(CAN_Node_Ref_t *node_ref, M_Epos_SetPositionalPIDRequest_t *request)
{
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_Position_Control_P(node_ref, request->gain_p));
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_Position_Control_I(node_ref, request->gain_i));
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_Position_Control_D(node_ref, request->gain_d));
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_Position_Control_FFV(node_ref, request->gain_ffv));
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_Position_Control_FFA(node_ref, request->gain_ffa));
    return M_ERROR_CODE_OK;
}
M_Error_Code_t `$INSTANCE_NAME`_Set_PID(CAN_Node_Ref_t *node_ref, M_Epos_SetPIDRequest_t *request)
{
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_Current_Control_P(node_ref, request->current_p));
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Write_Current_Control_I(node_ref, request->current_i));
    return `$INSTANCE_NAME`_Set_Positional_PID(node_ref, &(request->pos_pid));
}

M_Error_Code_t `$INSTANCE_NAME`_Home_From_Params(CAN_Node_Ref_t *node_ref, M_Epos_Home_Params_t *params, uint16_t settle_timeout_ms, int32_t* out_limit)
{
    switch(params->param_type)
    {
        case M_EPOS_PARAM_TYPE_HARD:
            return `$INSTANCE_NAME`_Hard_Stop_Home(node_ref, params->params.hard_stop.step_size, params->params.hard_stop.offset, params->min_position, params->max_position, params->profile_velocity, settle_timeout_ms, out_limit);
        case M_EPOS_PARAM_TYPE_SWITCH:
            return `$INSTANCE_NAME`_Limit_Switch_Home(node_ref, params->params.limit_switch.step_size, params->params.limit_switch.threshold_step, params->min_position, params->max_position, params->profile_velocity, settle_timeout_ms, out_limit);
        default:
            break;
    }
    return M_ERROR_CODE_EPOS_INVALID_HOMING_PARAMS;
}

M_Error_Code_t `$INSTANCE_NAME`_Handle_Request(CAN_Node_Ref_t *node_ref, M_Epos_Request_t *request, M_Epos_Reply_t *out_reply) 
{
    M_Error_Code_t ret = M_ERROR_CODE_OK;
    switch(request->request_type)
    {
        case M_EPOS_REQUEST_TYPE_CAN_OPEN:
            out_reply->reply_type = M_EPOS_REPLY_TYPE_CAN_OPEN;
            ret = `$INSTANCE_NAME``[MCAN]`Handle_Request(node_ref, &request->request.can, &out_reply->reply.can);
            break;
        case M_EPOS_REQUEST_TYPE_SETUP_PDOS:
            out_reply->reply_type = M_EPOS_REPLY_TYPE_ACK;
            ret = `$INSTANCE_NAME`_Setup_PDOs(node_ref);
            break;
        case M_EPOS_REQUEST_TYPE_ENABLE:
            out_reply->reply_type = M_EPOS_REPLY_TYPE_ACK;
            ret = `$INSTANCE_NAME`_Enable_Node(node_ref);
            break;
        case M_EPOS_REQUEST_TYPE_DISABLE:
            out_reply->reply_type = M_EPOS_REPLY_TYPE_ACK;
            ret = `$INSTANCE_NAME`_Disable_Node(node_ref); 
            break;
        case M_EPOS_REQUEST_TYPE_HOME:
            out_reply->reply_type = M_EPOS_REPLY_TYPE_HOMING_LIMIT;
            ret = `$INSTANCE_NAME`_Home_From_Params(node_ref, &request->request.home.params, `$INSTANCE_NAME`_DEFAULT_SETTLE_TIMEOUT_MS, &out_reply->reply.limit); 
            break;
        case M_EPOS_REQUEST_TYPE_AWAIT_SETTLING:
            out_reply->reply_type = M_EPOS_REPLY_TYPE_SETTLING_TIME;
            ret = `$INSTANCE_NAME`_Await_Settling(node_ref, request->request.settle.target_position, request->request.settle.window, request->request.settle.timeout_ms, &out_reply->reply.duration);
            break;
        case M_EPOS_REQUEST_TYPE_GET_POS_VEL:
            out_reply->reply_type = M_EPOS_REPLY_TYPE_POS_VEL;
            ret = `$INSTANCE_NAME`_Get_Actual_Position_Velocity(node_ref, &out_reply->reply.pos_vel.position, &out_reply->reply.pos_vel.velocity);
            break;
        case M_EPOS_REQUEST_TYPE_GO_TO:
            out_reply->reply_type = M_EPOS_REPLY_TYPE_SETTLING_TIME;
            ret = `$INSTANCE_NAME`_Go_To_Position(node_ref, request->request.go_to.position, request->request.go_to.velocity);
            if (ret == M_ERROR_CODE_OK && request->request.go_to.timeout_ms != 0)
            {
                ret = `$INSTANCE_NAME`_Await_Settling(node_ref, request->request.go_to.position, request->request.go_to.window, request->request.go_to.timeout_ms, &out_reply->reply.duration);
            }
            break;
        case M_EPOS_REQUEST_TYPE_AWAIT_STATUS:
            out_reply->reply_type = M_EPOS_REPLY_TYPE_ACK;
            ret = `$INSTANCE_NAME`_Await_Status(node_ref, request->request.status.timeout_ms, request->request.status.expected, request->request.status.expected_neg);
            break;
        case M_EPOS_REQUEST_TYPE_POSITIONAL_PID:
            out_reply->reply_type = M_EPOS_REPLY_TYPE_ACK;
            ret = `$INSTANCE_NAME`_Set_Positional_PID(node_ref, &request->request.positional_pid);
            break;
        case M_EPOS_REQUEST_TYPE_PID:
            out_reply->reply_type = M_EPOS_REPLY_TYPE_ACK;
            ret = `$INSTANCE_NAME`_Set_PID(node_ref, &request->request.pid);
            break;
        default:
            ret = M_ERROR_CODE_EPOS_REQUEST_NOT_SUPPORTED;
            break;
    }
    
    if (ret != M_ERROR_CODE_OK)
    {
        out_reply->reply_type = M_EPOS_REPLY_TYPE_ERROR;
        out_reply->reply.error =  ret;
    }
    
    return M_ERROR_CODE_OK;
}

void `$INSTANCE_NAME`_boot(UBaseType_t send_task_priority)
{
    `$INSTANCE_NAME``[MCAN]`boot(send_task_priority);
}

/* [] END OF FILE */

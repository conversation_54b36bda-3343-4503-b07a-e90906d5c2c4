/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

/*
 * epos.h and epos.c are only meant to provide aliases fop the firmware spec: https://www.maxongroup.com/medias/sys_master/root/8834324856862/EPOS4-Firmware-Specification-En.pdf
*/

#ifndef `$INSTANCE_NAME`_M_EPOS_EPOS_H
#define `$INSTANCE_NAME`_M_EPOS_EPOS_H

#include "stdint.h"
#include "stdbool.h"
#include "`$INSTANCE_NAME``[<PERSON>N]`M_CAN_Open.h"
#include "CAN_Open.h"
#include "m_error_codes.h"

// **** Profile Position Mode Commands ****
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Target_Position(CAN_Node_Ref_t *node_ref, int32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x607A, 0x00, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Profile_Velocity(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x6081, 0x00, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Profile_Acceleration(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x6083, 0x00, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Profile_Deceleration(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x6084, 0x00, value); }

inline M_Error_Code_t `$INSTANCE_NAME`_Read_Target_Position(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x607A, 0x00); } // int32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Profile_Velocity(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x6081, 0x00); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Profile_Acceleration(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x6083, 0x00); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Profile_Deceleration(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x6084, 0x00); } // uint32 return value


// **** Profile Position Mode Configuration ****
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Software_Position_Min_Limit(CAN_Node_Ref_t *node_ref, int32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x607D, 0x01, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Software_Position_Max_Limit(CAN_Node_Ref_t *node_ref, int32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x607D, 0x02, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Max_Profile_Velocity(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x607f, 0x00, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Max_Acceleration(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x60C5, 0x00, value); }

inline M_Error_Code_t `$INSTANCE_NAME`_Read_Software_Position_Min_Limit(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x607D, 0x01); } // int32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Software_Position_Max_Limit(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x607D, 0x01); } // int32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Max_Profile_Velocity(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x607f, 0x00); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Max_Acceleration(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x60C5, 0x00); } // uint32 return value

// **** General Epos ****
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Control_Word(CAN_Node_Ref_t *node_ref, uint16_t word) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x6040, 0x00, word); } // 16 bits word

inline M_Error_Code_t `$INSTANCE_NAME`_Read_Control_Word(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x6040, 0x00); } // 16 bits word return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Status_Word(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x6041, 0x00); } // 16 bits word return value

#define `$INSTANCE_NAME`_STATUS_REFERENCE_TO_HOME 0x8000
#define `$INSTANCE_NAME`_STATUS_FOLLOWING_ERROR 0x2000
#define `$INSTANCE_NAME`_STATUS_SETPOINT_AWKNOWLEDGED 0x1000
#define `$INSTANCE_NAME`_STATUS_INTERNAL_LIMIT_ACTIVE 0x800
#define `$INSTANCE_NAME`_STATUS_TARGET_REACHED 0x400
#define `$INSTANCE_NAME`_STATUS_REMOTE 0x200
#define `$INSTANCE_NAME`_STATUS_WARNING 0x80
#define `$INSTANCE_NAME`_STATUS_SWITCH_ON_DISABLED 0x40
#define `$INSTANCE_NAME`_STATUS_QUICK_STOP 0x20
#define `$INSTANCE_NAME`_STATUS_VOLTAGE_ENABLED 0x10
#define `$INSTANCE_NAME`_STATUS_FAULT 0x08
#define `$INSTANCE_NAME`_STATUS_OPERATIONS_ENABLED 0x04
#define `$INSTANCE_NAME`_STATUS_SWITCHED_ON 0x02
#define `$INSTANCE_NAME`_STATUS_READY_TO_SWITCH_ON 0x01

#define `$INSTANCE_NAME`_CONTROL_ENDLESS_MOVEMENT 0x8000
#define `$INSTANCE_NAME`_CONTROL_HALT 0x100
#define `$INSTANCE_NAME`_CONTROL_FAULT_RESET 0x80
#define `$INSTANCE_NAME`_CONTROL_ABS_REL 0x40
#define `$INSTANCE_NAME`_CONTROL_CHANGE_SET_IMMEDIATELY 0x20
#define `$INSTANCE_NAME`_CONTROL_START_OPERATION 0x10
#define `$INSTANCE_NAME`_CONTROL_ENABLE_OPERATION 0x08
#define `$INSTANCE_NAME`_CONTROL_QUICK_STOP 0x04
#define `$INSTANCE_NAME`_CONTROL_ENABLE_VOLTAGE 0x02
#define `$INSTANCE_NAME`_CONTROL_SWITCHED_ON 0x01

#define `$INSTANCE_NAME`_CONTROL_COMMAND_SHUTDOWN `$INSTANCE_NAME`_CONTROL_ENABLE_VOLTAGE | `$INSTANCE_NAME`_CONTROL_QUICK_STOP
#define `$INSTANCE_NAME`_CONTROL_COMMAND_SWITCH_ON `$INSTANCE_NAME`_CONTROL_ENABLE_VOLTAGE | `$INSTANCE_NAME`_CONTROL_QUICK_STOP | `$INSTANCE_NAME`_CONTROL_SWITCHED_ON
#define `$INSTANCE_NAME`_CONTROL_COMMAND_SWITCH_ON_ENABLE `$INSTANCE_NAME`_CONTROL_ENABLE_VOLTAGE | `$INSTANCE_NAME`_CONTROL_QUICK_STOP | `$INSTANCE_NAME`_CONTROL_SWITCHED_ON | `$INSTANCE_NAME`_CONTROL_ENABLE_OPERATION
#define `$INSTANCE_NAME`_CONTROL_COMMAND_DISABLE `$INSTANCE_NAME`_CONTROL_ENABLE_VOLTAGE | `$INSTANCE_NAME`_CONTROL_QUICK_STOP | `$INSTANCE_NAME`_CONTROL_SWITCHED_ON
#define `$INSTANCE_NAME`_CONTROL_COMMAND_FAULT_RESET `$INSTANCE_NAME`_CONTROL_FAULT_RESET
#define `$INSTANCE_NAME`_CONTROL_COMMAND_START_OPERATION `$INSTANCE_NAME`_CONTROL_COMMAND_SWITCH_ON_ENABLE | `$INSTANCE_NAME`_CONTROL_CHANGE_SET_IMMEDIATELY | `$INSTANCE_NAME`_CONTROL_START_OPERATION


// **** Epos Configuration ****
extern const uint8_t `$INSTANCE_NAME`_SAVE_PARAM_VALUE[];
extern const uint8_t `$INSTANCE_NAME`_RESTORE_PARAM_VALUE[];
extern const uint32_t* `$INSTANCE_NAME`_SAVE_PARAM_VALUE_POINTER;
extern const uint32_t* `$INSTANCE_NAME`_RESTORE_PARAM_VALUE_POINTER;
inline M_Error_Code_t `$INSTANCE_NAME`_Save_All_Parameters(CAN_Node_Ref_t *node_ref) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1010, 0x01, *`$INSTANCE_NAME`_SAVE_PARAM_VALUE_POINTER); }
inline M_Error_Code_t `$INSTANCE_NAME`_Restore_Default_Parameters(CAN_Node_Ref_t *node_ref) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1011, 0x01, *`$INSTANCE_NAME`_RESTORE_PARAM_VALUE_POINTER); }
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Vendor_ID(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x1018, 0x01); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Product_Code(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x1018, 0x02); } // uint32 (uint16/uint16) return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Revision_Number(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x1018, 0x03); } // uint32 (uint16/uint16) return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Last_Serial_Number(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x1018, 0x04); } // uint32 (8 4 bits digits) return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Power_Supply_Voltage(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x2200, 0x01); } // uint16 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Sensor_Resolution(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x3000, 0x05); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Motor_Nominal_Current(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x3001, 0x01); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Motor_Output_Current_Limit(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x3001, 0x02); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Digital_Encoder_Pulse_Resolution(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x3010, 0x01); } // uint32 return value

// **** PID Configuration ****
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Current_Control_P(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30A0, 0x01, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Current_Control_I(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30A0, 0x02, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Position_Control_P(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30A1, 0x01, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Position_Control_I(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30A1, 0x02, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Position_Control_D(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30A1, 0x03, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Position_Control_FFV(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30A1, 0x04, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Position_Control_FFA(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30A1, 0x05, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Velocity_Control_P(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30A2, 0x01, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Velocity_Control_I(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30A2, 0x02, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Velocity_Control_FFV(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30A2, 0x03, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Velocity_Control_FFA(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30A2, 0x04, value); }

inline M_Error_Code_t `$INSTANCE_NAME`_Read_Current_Control_P(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30A0, 0x01); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Current_Control_I(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30A0, 0x02); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Position_Control_P(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30A1, 0x01); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Position_Control_I(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30A1, 0x02); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Position_Control_D(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30A1, 0x03); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Position_Control_FFV(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30A1, 0x04); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Position_Control_FFA(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30A1, 0x05); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Velocity_Control_P(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30A2, 0x01); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Velocity_Control_I(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30A2, 0x02); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Velocity_Control_FFV(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30A2, 0x03); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Velocity_Control_FFA(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30A2, 0x04); } // uint32 return value

// **** Velocity Observer ****
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Observer_Position_Gain(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30A3, 0x01, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Observer_Velocity_Gain(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30A3, 0x02, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Observer_Load_Gain(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30A3, 0x03, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Observer_Friction(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30A3, 0x04, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Observer_Inertia(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30A3, 0x05, value); }

inline M_Error_Code_t `$INSTANCE_NAME`_Read_Observer_Position_Gain(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30A3, 0x01); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Observer_Velocity_Gain(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30A3, 0x02); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Observer_Load_Gain(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30A3, 0x03); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Observer_Friction(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30A3, 0x04); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Observer_Inertia(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30A3, 0x05); } // uint32 return value

// **** Dual Loop Configuration ****
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Main_Loop_P_Gain_Low(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30AE, 0x01, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Main_Loop_P_Gain_High(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30AE, 0x02, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Main_Loop_Scheduling_Weight(CAN_Node_Ref_t *node_ref, uint16_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30AE, 0x03, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Main_Loop_Filter_A(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30AE, 0x10, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Main_Loop_Filter_B(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30AE, 0x11, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Main_Loop_Filter_C(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30AE, 0x12, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Main_Loop_Filter_D(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30AE, 0x13, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Main_Loop_Filter_E(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30AE, 0x14, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Aux_Loop_P_Gain(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30AE, 0x20, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Aux_Loop_I_Gain(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30AE, 0x21, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Aux_Loop_FFV_Gain(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30AE, 0x22, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Aux_Loop_FFA_Gain(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30AE, 0x23, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Aux_Loop_Position_Gain(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30AE, 0x30, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Aux_Loop_Velocity_Gain(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30AE, 0x31, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Aux_Loop_Load_Gain(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30AE, 0x32, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Aux_Loop_Friction(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30AE, 0x33, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Aux_Loop_Inertia(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30AE, 0x34, value); }

inline M_Error_Code_t `$INSTANCE_NAME`_Read_Main_Loop_P_Gain_Low(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30AE, 0x01); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Main_Loop_P_Gain_HIGH(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30AE, 0x02); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Main_Loop_Scheduling_Weight(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30AE, 0x03); } // uint16 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Main_Loop_Filter_A(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30AE, 0x10); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Main_Loop_Filter_B(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30AE, 0x11); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Main_Loop_Filter_C(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30AE, 0x12); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Main_Loop_Filter_D(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30AE, 0x13); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Main_Loop_Filter_E(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30AE, 0x14); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Aux_Loop_P_Gain(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30AE, 0x20); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Aux_Loop_I_Gain(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30AE, 0x21); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Aux_Loop_FFV_Gain(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30AE, 0x22); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Aux_Loop_FFA_Gain(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30AE, 0x23); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Aux_Loop_Position_Gain(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30AE, 0x30); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Aux_Loop_Velocity_Gain(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30AE, 0x31); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Aux_Loop_Load_Gain(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30AE, 0x32); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Aux_Loop_Friction(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30AE, 0x33); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Aux_Loop_Inertia(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30AE, 0x34); } // uint32 return value

// **** Measurement ****
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Current_Demand(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30D0, 0x00); } // int32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Current_Average(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30D1, 0x01); } // int32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Current_Actual(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30D1, 0x02); } // int32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Torque_Average(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30D2, 0x01); } // int16 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Velocity_Average(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30D3, 0x01); } // int32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_I2T_Level_Motor(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x3200, 0x01); } // uint16 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_I2T_Level_Controller(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x3200, 0x02); } // uint16 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Temperature_Power_Stage(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x3201, 0x01); } // int16 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Position_Demand(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x6062, 0x00); } // int32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Position_Actual(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x6064, 0x00); } // int32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Velocity_Demand(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x606B, 0x00); } // int32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Velocity_Actual(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x606C, 0x00); } // int32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Target_Torque(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x6071, 0x00); } // int16 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Motor_Rated_Torque(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x6076, 0x00); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Torque_Actual(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x6077, 0x00); } // int16 return value

// **** Standstill Configuration ****
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Standstill_Window(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30E0, 0x01, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Standstill_Window_Time(CAN_Node_Ref_t *node_ref, uint16_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30E0, 0x02, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Standstill_Window_Timeout(CAN_Node_Ref_t *node_ref, uint16_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30E0, 0x03, value); }

inline M_Error_Code_t `$INSTANCE_NAME`_Read_Standstill_Window(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30E0, 0x01); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Standstill_Window_Time(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30E0, 0x02); } // uint16 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Standstill_Window_Timeout(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30E0, 0x03); } // uint16 return value

// **** Digital Inputs ****
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Digital_Input_Polarity(CAN_Node_Ref_t *node_ref, uint16_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x3141, 0x02, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Digital_Input_Config(CAN_Node_Ref_t *node_ref, uint8_t digital_input_id, uint8_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x3141, digital_input_id, value); }

inline M_Error_Code_t `$INSTANCE_NAME`_Read_Digital_Input_State(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x3141, 0x01); } // uint16 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Digital_Input_State_Polarized(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x60FD, 0x00); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Digital_Input_Polarity(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x3141, 0x02); } // uint16 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Digital_Input_Config(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg, uint8_t digital_input_id) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x3141, digital_input_id); } // uint8 return value

#define `$INSTANCE_NAME`_DIGITAL_INPUT_HS_4 0x80
#define `$INSTANCE_NAME`_DIGITAL_INPUT_HS_3 0x40
#define `$INSTANCE_NAME`_DIGITAL_INPUT_HS_2 0x20
#define `$INSTANCE_NAME`_DIGITAL_INPUT_HS_1 0x10
#define `$INSTANCE_NAME`_DIGITAL_INPUT_4    0x08
#define `$INSTANCE_NAME`_DIGITAL_INPUT_3    0x04
#define `$INSTANCE_NAME`_DIGITAL_INPUT_2    0x02
#define `$INSTANCE_NAME`_DIGITAL_INPUT_1    0x01

#define `$INSTANCE_NAME`_DIGITAL_INPUT_ID_HS_4 8
#define `$INSTANCE_NAME`_DIGITAL_INPUT_ID_HS_3 7
#define `$INSTANCE_NAME`_DIGITAL_INPUT_ID_HS_2 6
#define `$INSTANCE_NAME`_DIGITAL_INPUT_ID_HS_1 5
#define `$INSTANCE_NAME`_DIGITAL_INPUT_ID_4    4
#define `$INSTANCE_NAME`_DIGITAL_INPUT_ID_3    3
#define `$INSTANCE_NAME`_DIGITAL_INPUT_ID_2    2
#define `$INSTANCE_NAME`_DIGITAL_INPUT_ID_1    1

#define `$INSTANCE_NAME`_DIGITAL_INPUT_NEGATIVE_LIMIT_SWITCH 0
#define `$INSTANCE_NAME`_DIGITAL_INPUT_POSITIVE_LIMIT_SWITCH 1
#define `$INSTANCE_NAME`_DIGITAL_INPUT_HOME_SWITCH 2
#define `$INSTANCE_NAME`_DIGITAL_INPUT_NEGATIVE_LIMIT_SWITCH_NO_ERRORS 24
#define `$INSTANCE_NAME`_DIGITAL_INPUT_POSITIVE_LIMIT_SWITCH_NO_ERRORS 25
#define `$INSTANCE_NAME`_DIGITAL_INPUT_DRIVE_ENABLE 27
#define `$INSTANCE_NAME`_DIGITAL_INPUT_QUICK_STOP 28
#define `$INSTANCE_NAME`_DIGITAL_INPUT_NONE 255

#define `$INSTANCE_NAME`_DI_QUICK_STOP (1 << `$INSTANCE_NAME`_DIGITAL_INPUT_QUICK_STOP)
#define `$INSTANCE_NAME`_DI_DRIVE_ENABLE (1 << `$INSTANCE_NAME`_DIGITAL_INPUT_DRIVE_ENABLE)
#define `$INSTANCE_NAME`_DI_POSITIVE_LIMIT_SWITCH_NO_ERROR (1 << `$INSTANCE_NAME`_DIGITAL_INPUT_POSITIVE_LIMIT_SWITCH_NO_ERRORS)
#define `$INSTANCE_NAME`_DI_NEGATIVE_LIMIT_SWITCH_NO_ERROR (1 << `$INSTANCE_NAME`_DIGITAL_INPUT_NEGATIVE_LIMIT_SWITCH_NO_ERRORS)
#define `$INSTANCE_NAME`_DI_HOME_SWITCH (1 << `$INSTANCE_NAME`_DIGITAL_INPUT_HOME_SWITCH)
#define `$INSTANCE_NAME`_DI_POSITIVE_LIMIT_SWITCH (1 << `$INSTANCE_NAME`_DIGITAL_INPUT_POSITIVE_LIMIT_SWITCH)
#define `$INSTANCE_NAME`_DI_NEGATIVE_LIMIT_SWITCH (1 << `$INSTANCE_NAME`_DIGITAL_INPUT_NEGATIVE_LIMIT_SWITCH)

// **** Epos Errors ****
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Error_Register(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x1001, 0x00); } // 8 bits word return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Error_Code(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x603F, 0x00); } // uint16 word return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Following_Error_Value(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x60F4, 0x00); } // int32 word return value

#define `$INSTANCE_NAME`_ERROR_MOTION        0x80
#define `$INSTANCE_NAME`_ERROR_DEVICE        0x20
#define `$INSTANCE_NAME`_ERROR_COMMUNICATION 0x10
#define `$INSTANCE_NAME`_ERROR_TEMPERATURE   0x08
#define `$INSTANCE_NAME`_ERROR_VOLTAGE       0x04
#define `$INSTANCE_NAME`_ERROR_CURRENT       0x02
#define `$INSTANCE_NAME`_ERROR_GENERIC       0x01

// **** Modes of Operations ****
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Mode_Of_Operation(CAN_Node_Ref_t *node_ref, int8_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x6060, 0x00, value); }

inline M_Error_Code_t `$INSTANCE_NAME`_Read_Mode_Of_Operation(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x6061, 0x00); } // int8 return value

#define `$INSTANCE_NAME`_PROFILE_POSITION_MODE 1
#define `$INSTANCE_NAME`_PROFILE_VELOCITY_MODE 3
#define `$INSTANCE_NAME`_HOMING_MODE 6
#define `$INSTANCE_NAME`_CSP_MODE 8
#define `$INSTANCE_NAME`_CSV_MODE 9
#define `$INSTANCE_NAME`_CST_MODE 10

// **** Homing ****
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Homing_Method(CAN_Node_Ref_t *node_ref, int8_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x6098, 0x00, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Homing_Switch_Search_Speed(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x6099, 0x01, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Homing_Zero_Search_Speed(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x6099, 0x02, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Homing_Acceleration(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x609A, 0x00, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Home_Position(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30B0, 0x00, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Home_Offset(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30B1, 0x00, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Homing_Current_Threshold(CAN_Node_Ref_t *node_ref, uint16_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x30B2, 0x00, value); }

inline M_Error_Code_t `$INSTANCE_NAME`_Read_Homing_Method(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x6098, 0x00); } // int8 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Homing_Switch_Search_Speed(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x6099, 0x01); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Homing_Zero_Search_Speed(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x6099, 0x02); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Homing_Acceleration(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x609A, 0x00); } // uint32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Home_Position(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30B0, 0x00); } // int32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Home_Offset(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30B1, 0x00); } // int32 return value
inline M_Error_Code_t `$INSTANCE_NAME`_Read_Homing_Current_Threshold(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x30B2, 0x00); } // uint16 return value

#define `$INSTANCE_NAME`_HOMING_METHOD_ACTUAL_POSITION 37
#define `$INSTANCE_NAME`_HOMING_METHOD_INDEX_POSITIVE_SPEED 34
#define `$INSTANCE_NAME`_HOMING_METHOD_INDEX_NEGATIVE_SPEED 33
#define `$INSTANCE_NAME`_HOMING_METHOD_HOME_SWITCH_NEGATIVE_SPEED 27
#define `$INSTANCE_NAME`_HOMING_METHOD_HOME_SWITCH_POSITIVE_SPEED 23
#define `$INSTANCE_NAME`_HOMING_METHOD_POSITIVE_LIMIT_SWITCH 18
#define `$INSTANCE_NAME`_HOMING_METHOD_NEGATIVE_LIMIT_SWITCH 17
#define `$INSTANCE_NAME`_HOMING_METHOD_HOME_SWITCH_NEGATIVE_SPEED_INDEX 11
#define `$INSTANCE_NAME`_HOMING_METHOD_HOME_SWITCH_POSITIVE_SPEED_INDEX 7
#define `$INSTANCE_NAME`_HOMING_METHOD_POSITIVE_LIMIT_SWITCH_INDEX 2
#define `$INSTANCE_NAME`_HOMING_METHOD_NEGATIVE_LIMIT_SWITCH_INDEX 1
#define `$INSTANCE_NAME`_HOMING_METHOD_CURRENT_POSITIVE_SPEED_INDEX -1
#define `$INSTANCE_NAME`_HOMING_METHOD_CURRENT_NEGATIVE_SPEED_INDEX -2
#define `$INSTANCE_NAME`_HOMING_METHOD_CURRENT_POSITIVE_SPEED -3
#define `$INSTANCE_NAME`_HOMING_METHOD_CURRENT_NEGATIVE_SPEED -4

// **** Units ****
inline M_Error_Code_t `$INSTANCE_NAME`_Write_Unit_Velocity(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x60A9, 0x00, value); }

inline M_Error_Code_t `$INSTANCE_NAME`_Read_Unit_Velocity(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Upload_Request(node_ref, msg, 0x60A9, 0x00); } // uint32 return value

#define `$INSTANCE_NAME`_Velocity_RPM 0x00B44700
#define `$INSTANCE_NAME`_Velocity_DECI_RPM 0xFFB44700
#define `$INSTANCE_NAME`_Velocity_CENTI_RPM 0xFEB44700
#define `$INSTANCE_NAME`_Velocity_MILLI_RPM 0xFDB44700
#define `$INSTANCE_NAME`_Velocity_M4_RPM 0xFCB44700
#define `$INSTANCE_NAME`_Velocity_M5_RPM 0xFBB44700
#define `$INSTANCE_NAME`_Velocity_MICRO_RPM 0xFAB44700

// **** PDO ****

#define `$INSTANCE_NAME`_PDO_TRANSMISSION_SYNC 1
#define `$INSTANCE_NAME`_PDO_TRANSMISSION_ASYNC_RTR 253
#define `$INSTANCE_NAME`_PDO_TRANSMISSION_ASYNC 255

#define `$INSTANCE_NAME`_PDO_COBID_VALID (1 << 31)
#define `$INSTANCE_NAME`_PDO_COBID_RTR (1 << 30)

#define `$INSTANCE_NAME`_PDO_BASE_COBID_RX1 0x200
#define `$INSTANCE_NAME`_PDO_BASE_COBID_RX2 0x300
#define `$INSTANCE_NAME`_PDO_BASE_COBID_RX3 0x400
#define `$INSTANCE_NAME`_PDO_BASE_COBID_RX4 0x500
#define `$INSTANCE_NAME`_PDO_BASE_COBID_TX1 0x180
#define `$INSTANCE_NAME`_PDO_BASE_COBID_TX2 0x280
#define `$INSTANCE_NAME`_PDO_BASE_COBID_TX3 0x380
#define `$INSTANCE_NAME`_PDO_BASE_COBID_TX4 0x480

inline M_Error_Code_t `$INSTANCE_NAME`_Write_RXPDO1_COBID(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1400, 0x01, value); } // 32 bit word
inline M_Error_Code_t `$INSTANCE_NAME`_Write_RXPDO1_Transmission(CAN_Node_Ref_t *node_ref, uint8_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1400, 0x02, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_RXPDO2_COBID(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1401, 0x01, value); } // 32 bit word
inline M_Error_Code_t `$INSTANCE_NAME`_Write_RXPDO2_Transmission(CAN_Node_Ref_t *node_ref, uint8_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1401, 0x02, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_RXPDO3_COBID(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1402, 0x01, value); } // 32 bit word
inline M_Error_Code_t `$INSTANCE_NAME`_Write_RXPDO3_Transmission(CAN_Node_Ref_t *node_ref, uint8_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1402, 0x02, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_RXPDO4_COBID(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1403, 0x01, value); } // 32 bit word
inline M_Error_Code_t `$INSTANCE_NAME`_Write_RXPDO4_Transmission(CAN_Node_Ref_t *node_ref, uint8_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1403, 0x02, value); }

inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO1_COBID(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1800, 0x01, value); } // 32 bit word
inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO1_Transmission(CAN_Node_Ref_t *node_ref, uint8_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1800, 0x02, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO1_Inhibit(CAN_Node_Ref_t *node_ref, uint16_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1800, 0x03, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO2_COBID(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1801, 0x01, value); } // 32 bit word
inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO2_Transmission(CAN_Node_Ref_t *node_ref, uint8_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1801, 0x02, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO2_Inhibit(CAN_Node_Ref_t *node_ref, uint16_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1801, 0x03, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO3_COBID(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1802, 0x01, value); } // 32 bit word
inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO3_Transmission(CAN_Node_Ref_t *node_ref, uint8_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1802, 0x02, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO3_Inhibit(CAN_Node_Ref_t *node_ref, uint16_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1802, 0x03, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO4_COBID(CAN_Node_Ref_t *node_ref, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1803, 0x01, value); } // 32 bit word
inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO4_Transmission(CAN_Node_Ref_t *node_ref, uint8_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1803, 0x02, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO4_Inhibit(CAN_Node_Ref_t *node_ref, uint16_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1803, 0x03, value); }

inline M_Error_Code_t `$INSTANCE_NAME`_Write_RXPDO1_Mapping_Number(CAN_Node_Ref_t *node_ref, uint8_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1600, 0x00, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_RXPDO1_Mapping_Object(CAN_Node_Ref_t *node_ref, uint16_t index, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1600, index, value); } // 32 bit word
inline M_Error_Code_t `$INSTANCE_NAME`_Write_RXPDO2_Mapping_Number(CAN_Node_Ref_t *node_ref, uint8_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1601, 0x00, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_RXPDO2_Mapping_Object(CAN_Node_Ref_t *node_ref, uint16_t index, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1601, index, value); } // 32 bit word
inline M_Error_Code_t `$INSTANCE_NAME`_Write_RXPDO3_Mapping_Number(CAN_Node_Ref_t *node_ref, uint8_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1602, 0x00, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_RXPDO3_Mapping_Object(CAN_Node_Ref_t *node_ref, uint16_t index, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1602, index, value); } // 32 bit word
inline M_Error_Code_t `$INSTANCE_NAME`_Write_RXPDO4_Mapping_Number(CAN_Node_Ref_t *node_ref, uint8_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1603, 0x00, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_RXPDO4_Mapping_Object(CAN_Node_Ref_t *node_ref, uint16_t index, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1603, index, value); } // 32 bit word

inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO1_Mapping_Number(CAN_Node_Ref_t *node_ref, uint8_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1A00, 0x00, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO1_Mapping_Object(CAN_Node_Ref_t *node_ref, uint16_t index, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1A00, index, value); } // 32 bit word
inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO2_Mapping_Number(CAN_Node_Ref_t *node_ref, uint8_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1A01, 0x00, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO2_Mapping_Object(CAN_Node_Ref_t *node_ref, uint16_t index, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1A01, index, value); } // 32 bit word
inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO3_Mapping_Number(CAN_Node_Ref_t *node_ref, uint8_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1A02, 0x00, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO3_Mapping_Object(CAN_Node_Ref_t *node_ref, uint16_t index, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1A02, index, value); } // 32 bit word
inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO4_Mapping_Number(CAN_Node_Ref_t *node_ref, uint8_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1A03, 0x00, value); }
inline M_Error_Code_t `$INSTANCE_NAME`_Write_TXPDO4_Mapping_Object(CAN_Node_Ref_t *node_ref, uint16_t index, uint32_t value) { return `$INSTANCE_NAME``[MCAN]`Send_SDO_Download(node_ref, 0x1A03, index, value); } // 32 bit word

#endif // `$INSTANCE_NAME`_M_EPOS_EPOS_H

/* [] END OF FILE */

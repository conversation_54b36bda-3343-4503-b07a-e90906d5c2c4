/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef `$INSTANCE_NAME`_M_EPOS_H
#define `$INSTANCE_NAME`_M_EPOS_H
    
#include "`$INSTANCE_NAME`_epos.h"
#include "m_epos_request.h"

#define `$INSTANCE_NAME`_DEFAULT_VELOCITY_MRPM 590000

#define `$INSTANCE_NAME`_DEFAULT_SETTLE_TIMEOUT_MS 250
#define `$INSTANCE_NAME`_DEFAULT_SETTLE_WINDOW 50
#define `$INSTANCE_NAME`_MINIMUM_SETTLE_WINDOW 5

#define `$INSTANCE_NAME`_DEFAULT_HOMING_STEP_SIZE 1000
#define `$INSTANCE_NAME`_DEFAULT_HOMING_THRESHOLD_STEP 10
#define `$INSTANCE_NAME`_DEFAULT_HOMING_MIN 0
#define `$INSTANCE_NAME`_DEFAULT_HOMING_MAX 20000
#define `$INSTANCE_NAME`_DEFAULT_HOMING_CURRENT 1000

// Boot
void `$INSTANCE_NAME`_boot(UBaseType_t send_task_priority);
    
// Management API
M_Error_Code_t `$INSTANCE_NAME`_Enable_Node(CAN_Node_Ref_t *node_ref);
M_Error_Code_t `$INSTANCE_NAME`_Disable_Node(CAN_Node_Ref_t *node_ref);
M_Error_Code_t `$INSTANCE_NAME`_Setup_PDOs(CAN_Node_Ref_t *node_ref);

// Status API
M_Error_Code_t `$INSTANCE_NAME`_Await_Status_PDO(CAN_Node_Ref_t *node_ref, CAN_Open_Message_t *msg, uint16_t timeout_ms, uint16_t expected, uint16_t expected_neg);
M_Error_Code_t `$INSTANCE_NAME`_Await_Status(CAN_Node_Ref_t *node_ref, uint16_t timeout_ms, uint16_t expected, uint16_t expected_neg);

// Profile Position Mode API
M_Error_Code_t `$INSTANCE_NAME`_Go_To_Position(CAN_Node_Ref_t *node_ref, int32_t position, uint32_t velocity);
M_Error_Code_t `$INSTANCE_NAME`_Get_Actual_Position_Velocity(CAN_Node_Ref_t *node_ref, int32_t *position, int32_t *velocity);
M_Error_Code_t `$INSTANCE_NAME`_Await_Settling(CAN_Node_Ref_t *node_ref, int32_t target_position, uint16_t window, uint16_t timeout_ms, uint16_t *settle_time_ms_out);
M_Error_Code_t `$INSTANCE_NAME`_Await_Target_Reached(CAN_Node_Ref_t *node_ref, uint16_t timeout_ms, uint16_t *reach_time_ms_out);
M_Error_Code_t `$INSTANCE_NAME`_Find_Limit(CAN_Node_Ref_t *node_ref, uint32_t bin_switch, int16_t step_size, int16_t threshold_step, int32_t min_position, int32_t max_position, uint32_t profile_velocity, int32_t *out);
M_Error_Code_t `$INSTANCE_NAME`_Calibration_Settle(CAN_Node_Ref_t *node_ref, int32_t target_position, uint16_t window, uint16_t time_window_ms, uint16_t timeout_ms, uint8_t period_ms, uint16_t *settle_time_ms_out);

// Homing API
M_Error_Code_t `$INSTANCE_NAME`_Home_With_Method(CAN_Node_Ref_t *node_ref, uint32_t method);
M_Error_Code_t `$INSTANCE_NAME`_Limit_Switch_Home(CAN_Node_Ref_t *node_ref, int16_t step_size, int16_t threshold_step, int32_t min_position, int32_t max_position, uint32_t profile_velocity, uint16_t settle_timeout_ms, int32_t *out_limit);
M_Error_Code_t `$INSTANCE_NAME`_Hard_Stop_Home(CAN_Node_Ref_t *node_ref, int16_t step_size, uint16_t offset, int32_t min_position, int32_t max_position, uint32_t profile_velocity, uint16_t settle_timeout_ms, int32_t *out_limit);

// Request API
M_Error_Code_t `$INSTANCE_NAME`_Handle_Request(CAN_Node_Ref_t *node_ref, M_Epos_Request_t *request, M_Epos_Reply_t *out_reply);
M_Error_Code_t `$INSTANCE_NAME`_Home_From_Params(CAN_Node_Ref_t *node_ref, M_Epos_Home_Params_t *request, uint16_t settle_timeout_ms, int32_t* out_limit);

#endif // `$INSTANCE_NAME`_M_EPOS_H

/* [] END OF FILE */

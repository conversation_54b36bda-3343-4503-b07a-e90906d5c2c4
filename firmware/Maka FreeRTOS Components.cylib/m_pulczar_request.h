/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef M_PULCZAR_REQUEST_H
#define M_PULCZAR_REQUEST_H

#include "m_gimbal_request.h"
#include "m_laser_request.h"
#include "m_dawg_request.h"
#include "m_lens_request.h"

typedef enum {
    PULCZAR_REQUEST_TYPE_NONE = 0, // None
    PULCZAR_REQUEST_TYPE_RESET = 1, // None
    PULCZAR_REQUEST_TYPE_CLEAR_CONFIG = 2, // Metadata Only
    PULCZAR_REQUEST_TYPE_GIMBAL = 3,
    PU<PERSON>ZAR_REQUEST_TYPE_DAWG = 4,
    PU<PERSON>ZAR_REQUEST_TYPE_LASER = 5,
    PULCZAR_REQUEST_TYPE_STATUS = 6, // Metadata Only
    PULCZAR_REQUEST_TYPE_LENS = 7,
    PULCZAR_REQUEST_TYPE_OVERRIDE = 8,
} M_Pulczar_RequestType;

typedef struct {
    Request_Metadata_t metadata;
    uint8_t override;
} M_Pulczar_Override_Request_t;

typedef struct {
    M_Pulczar_RequestType request_type;
    union {
        Request_Metadata_t metadata;
        M_Gimbal_Request_t gimbal;
        M_Dawg_Request_t dawg;
        M_Laser_Request_t laser;
        M_Lens_Request_t lens;
        M_Pulczar_Override_Request_t override;
    } request;
} M_Pulczar_Request_t;

#endif // M_ROW_MODULE_REQUEST_H

/* [] END OF FILE */

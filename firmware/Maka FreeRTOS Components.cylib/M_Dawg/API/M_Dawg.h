/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PR<PERSON>RIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef `$INSTANCE_NAME`_M_DAWG_H
#define `$INSTANCE_NAME`_M_DAWG_H

#include "m_dawg_request.h"
#include "m_error_codes.h"

M_Error_Code_t `$INSTANCE_NAME`_Send_Request(M_Dawg_Request_t *request);
void `$INSTANCE_NAME`_Boot_Component( M_Error_Code_t (*send_reply_func)(M_Dawg_Reply_t *), UBaseType_t priority);

#endif // `$INSTANCE_NAME`_M_DAWG_H

/* [] END OF FILE */

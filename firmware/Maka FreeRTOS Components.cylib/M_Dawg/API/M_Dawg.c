/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#include "FreeRTOS.h"
#include "project.h"
#include "queue.h"
#include "utils.h"
#include "`$INSTANCE_NAME`_M_Dawg.h"

#define DEFAULT_PETTING_TIMEOUT_MS 1000
#define DEFAULT_QUEUE_SIZE 4

M_Dawg_Ref_t `$INSTANCE_NAME`_dawg_ref;

inline M_Error_Code_t `$INSTANCE_NAME`_Send_Request(M_Dawg_Request_t *request)
{
    // We drop requests if there isn't enough space
    if (xQueueSendToBack(`$INSTANCE_NAME`_dawg_ref.request_queue, request, 0) != pdTRUE)
    {
        return M_ERROR_CODE_DAWG_SEND_REQUEST_FAIL;
    }
    return M_ERROR_CODE_OK;
}

inline M_Error_Code_t `$INSTANCE_NAME`_Await_Request(M_Dawg_Request_t *request, uint32_t timeout_ms)
{
    if (xQueueReceive(`$INSTANCE_NAME`_dawg_ref.request_queue, request, pdMS_TO_TICKS(timeout_ms)) != pdTRUE)
    {
        return M_ERROR_CODE_DAWG_AWAIT_REQUEST_FAIL;
    }
    return M_ERROR_CODE_OK;
}

void `$INSTANCE_NAME`_Arm(bool armed)
{
    #if !defined(`$INSTANCE_NAME`_Arming_Reg_Sync_ctrl_reg__REMOVED)
    `$INSTANCE_NAME`_Arming_Reg_Write(armed);
    #endif
}

void `$INSTANCE_NAME`_Pet(bool valid)
{
    #if !defined(`$INSTANCE_NAME`_Petting_Reg_Sync_ctrl_reg__REMOVED)
    `$INSTANCE_NAME`_Petting_Reg_Write(valid);
    #endif
}

bool `$INSTANCE_NAME`_Get_Arming_State()
{
    #if !defined(`$INSTANCE_NAME`_Arming_Reg_Sync_ctrl_reg__REMOVED)
    return (bool) `$INSTANCE_NAME`_Arming_Reg_Read();
    #else
    return false;
    #endif
}

bool `$INSTANCE_NAME`_Get_Petting_State()
{
    #if !defined(`$INSTANCE_NAME`_Petting_Reg_Sync_ctrl_reg__REMOVED)
    return (bool) `$INSTANCE_NAME`_Petting_Reg_Read();
    #else
    return false;
    #endif
}

void `$INSTANCE_NAME`_Handle_Request(M_Dawg_Request_t *request) 
{
    M_Dawg_Reply_t reply;
    reply.metadata = request->metadata;
    switch(request->request_type)
    {
        case DAWG_REQUEST_TYPE_CONFIG:
            reply.reply_type = DAWG_REPLY_TYPE_ACK;
            `$INSTANCE_NAME`_dawg_ref.config = request->request.config;
            break;
        case DAWG_REQUEST_TYPE_ARM:
            reply.reply_type = DAWG_REPLY_TYPE_ACK;
            `$INSTANCE_NAME`_Arm(request->request.arming.armed);
            break;
        case DAWG_REQUEST_TYPE_PET:
            reply.reply_type = DAWG_REPLY_TYPE_ACK;
            `$INSTANCE_NAME`_Pet(true);
            break;
        case DAWG_REQUEST_TYPE_STATE:
            reply.reply_type = DAWG_REPLY_TYPE_STATE;
            reply.reply.state.armed = `$INSTANCE_NAME`_Get_Arming_State();
            reply.reply.state.petted = `$INSTANCE_NAME`_Get_Petting_State();
            break;
        default:
            reply.reply_type = DAWG_REPLY_TYPE_ERROR;
            break;
    }
    
    `$INSTANCE_NAME`_dawg_ref.send_reply_func(&reply);
}

void `$INSTANCE_NAME`_Task(void *context)
{
    UNUSED(context);
    M_Dawg_Request_t request;
    
    while (true)
    {
        if (`$INSTANCE_NAME`_Await_Request(&request, `$INSTANCE_NAME`_dawg_ref.config.timeout_ms) == M_ERROR_CODE_OK)
        {
            `$INSTANCE_NAME`_Handle_Request(&request);
        }
        else
        {
            `$INSTANCE_NAME`_Pet(false);
        }
    }
}

void `$INSTANCE_NAME`_Boot_Component( M_Error_Code_t (*send_reply_func)(M_Dawg_Reply_t *), UBaseType_t priority)
{
    `$INSTANCE_NAME`_dawg_ref.request_queue = xQueueCreate(DEFAULT_QUEUE_SIZE, sizeof(M_Dawg_Request_t));
    `$INSTANCE_NAME`_dawg_ref.config.timeout_ms = DEFAULT_PETTING_TIMEOUT_MS;
    `$INSTANCE_NAME`_dawg_ref.send_reply_func = send_reply_func;
    
    xTaskCreate(`$INSTANCE_NAME`_Task, "`$INSTANCE_NAME`", configMINIMAL_STACK_SIZE + 512, NULL, priority, 0);
}


/* [] END OF FILE */

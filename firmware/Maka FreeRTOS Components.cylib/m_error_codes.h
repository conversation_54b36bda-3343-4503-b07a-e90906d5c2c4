/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef M_ERROR_CODES_H
#define M_ERROR_CODES_H
    
typedef enum {
    M_ERROR_CODE_OK = 0, // No Error
    
    // Gimbal Errors, [1,100] Reserved
    M_ERROR_CODE_GIMBAL_SEND_REQUEST_FAIL = 1,
    M_ERROR_CODE_GIMBAL_AWAIT_REQUEST_FAIL = 2,
    M_ERROR_CODE_GIMBAL_DO_BOTH_FAIL = 3,
    M_ERROR_CODE_GIMBAL_SERVO_BOOT_FAIL = 4,
    M_ERROR_CODE_GIMBAL_SERVO_STOP_FAIL = 5,
    M_ERROR_CODE_GIMBAL_NO_REQUEST_HANDLER = 6,
    
    // Laser Errors, [101, 200] Reserved
    M_ERROR_CODE_LASER_SEND_REQUEST_FAIL = 101,
    M_ERROR_CODE_LASER_AWAIT_REQUEST_FAIL = 102,
    M_ERROR_CODE_LASER_NO_REQUEST_HANDLER = 103,
    
    // Ethernet Errors, [201, 300] Reserved
    M_ERROR_CODE_ETH_NANOPB_ENCODE_FAIL = 201,
    M_ERROR_CODE_ETH_NANOPB_DECODE_FAIL = 202,
    
    // Request Errors, [301, 400] Reserved
    M_ERROR_CODE_INVALID_METADATA = 301,
    
    // Lens Errors, [401, 500] Reserved
    M_ERROR_CODE_LENS_SEND_REQUEST_FAIL = 401,
    M_ERROR_CODE_LENS_AWAIT_REQUEST_FAIL = 402,
    M_ERROR_CODE_LENS_NO_REQUEST_HANDLER = 403,
    M_ERROR_CODE_LENS_I2C_WRITE_ERROR = 404,
    M_ERROR_CODE_LENS_I2C_READ_ERROR = 405,
    M_ERROR_CODE_LENS_I2C_SEND_START_ERROR = 406,
    M_ERROR_CODE_LENS_I2C_SEND_STOP_ERROR = 407,
    M_ERROR_CODE_LENS_I2C_REGISTER_WRITE_ERROR = 408,
    M_ERROR_CODE_LENS_I2C_SEND_RESTART_ERROR = 409,
    
    // Dawg Errors, [501, 600] Reserved
    M_ERROR_CODE_DAWG_SEND_REQUEST_FAIL = 501,
    M_ERROR_CODE_DAWG_AWAIT_REQUEST_FAIL = 502,
    
    // CAN Open Errors [601, 700] Reserved
    M_ERROR_CODE_CAN_NODE_CONFIGURATION_FAIL = 601,
    M_ERROR_CODE_CAN_NODE_INDEX_FROM_NMT = 602,
    M_ERROR_CODE_CAN_NODE_NOT_FOUND = 603,
    M_ERROR_CODE_CAN_AWAIT_REPLY_TIMEOUT = 604,
    M_ERROR_CODE_CAN_SDO_DOWNLOAD_BAD_REPLY = 605,
    M_ERROR_CODE_CAN_SDO_UPLOAD_BAD_REPLY = 606,
    M_ERROR_CODE_CAN_RESET_COMMS_BAD_REPLY = 607,
    M_ERROR_CODE_CAN_REQUEST_NOT_SUPPORTED = 608,
    
    // EPOS Errors [701, 800] Reserved
    M_ERROR_CODE_EPOS_AWAIT_STATUS_PDO_TIMEOUT = 701,
    M_ERROR_CODE_EPOS_SETTLE_TIMEOUT = 702,
    M_ERROR_CODE_EPOS_FIND_HARD_LIMITS_MAX_RETRIES = 703,
    M_ERROR_CODE_EPOS_HARD_LIMITS_RANGE_TOO_SMALL = 704,
    M_ERROR_CODE_EPOS_FIND_HARD_LIMITS_FAIL = 705,
    M_ERROR_CODE_EPOS_FIND_LIMIT_SWITCH_FAIL = 706,
    M_ERROR_CODE_EPOS_INVALID_HOMING_PARAMS = 707,
    M_ERROR_CODE_EPOS_REQUEST_NOT_SUPPORTED = 708,
    
    // Servo Errors [801, 900] Reserved
    M_ERROR_CODE_SERVO_SEND_REQUEST_FAIL = 801,
    M_ERROR_CODE_SERVO_AWAIT_REQUEST_FAIL = 802,
    M_ERROR_CODE_SERVO_SEND_REPLY_FAIL = 803,
    M_ERROR_CODE_SERVO_AWAIT_REPLY_FAIL = 804,
    M_ERROR_CODE_SERVO_NODE_CONFIGURATION_FAIL = 805,
    M_ERROR_CODE_SERVO_MOVE_OUT_OF_RANGE = 806,
    M_ERROR_CODE_SERVO_REQUEST_NOT_SUPPORTED = 807,
    M_ERROR_CODE_SERVO_TIME_DIFF_TOO_BIG = 808,
    M_ERROR_CODE_SERVO_FOLLOW_TIME_DIFF_TOO_BIG = 809,
    
    // PPS Time Errors [901, 1000] Reserved
    M_ERROR_CODE_PPS_SEM_TIMEOUT = 901,
    M_ERROR_CODE_PPS_NO_REQUEST_HANDLER = 902,
    
    
} M_Error_Code_t;

#define RETURN_CODE_IF_NOT_OK(ret) if (ret != M_ERROR_CODE_OK) {return ret; };
    
    
#endif // M_ERROR_CODES_H

/* [] END OF FILE */

/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND <PERSON><PERSON><PERSON>ETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef REQUEST_METADATA_H
#define REQUEST_METADATA_H
    
#include "stdint.h"
    
typedef enum {
    REQUEST_METADATA_TYPE_NONE = 0,
    REQUEST_METADATA_TYPE_ETHERNET = 1,
} Request_Metadata_Type_t;

typedef struct {
    uint8_t ip[4];
    uint16_t port;
    uint16_t local_port;
} Request_Metadata_Ethernet_t;

typedef struct {
    uint16_t request_id;
    Request_Metadata_Type_t request_type;
    union {
        Request_Metadata_Ethernet_t eth;
    } metadata;
} Request_Metadata_t;

#endif // REQUEST_METADATA_H

/* [] END OF FILE */

/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef M_ROW_MODULE_REQUEST_H
#define M_ROW_MODULE_REQUEST_H

#include "m_scanner_request.h"
#include "m_dawg_request.h"

typedef enum {
    ROW_MODULE_REQUEST_TYPE_NONE = 0, // None
    ROW_MODULE_REQUEST_TYPE_RESET = 1, // None
    ROW_MODULE_REQUEST_TYPE_CLEAR_CONFIG = 2,
    ROW_MODULE_REQUEST_TYPE_SCANNER = 3,
    ROW_MODULE_REQUEST_TYPE_DAWG = 4,
} M_Row_Module_RequestType;

typedef struct {
    uint16_t request_id;
} M_Row_Module_Clear_Config_Request_t;

typedef struct {
    uint8_t scanner_id;
    M_Scanner_Request_t scanner;
} M_Row_Module_Scanner_Request_t;

typedef struct {
    M_Row_Module_RequestType request_type;
    union {
        M_Row_Module_Clear_Config_Request_t clear;
        M_Row_Module_Scanner_Request_t scanner;
        M_Dawg_Request_t dawg;
    } request;
} M_Row_Module_Request_t;

#endif // M_ROW_MODULE_REQUEST_H

/* [] END OF FILE */

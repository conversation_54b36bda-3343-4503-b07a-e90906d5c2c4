/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef `$INSTANCE_NAME`_M_LENS_H
#define `$INSTANCE_NAME`_M_LENS_H

#include "m_lens_request.h"
#include "FreeRTOS.h"

M_Error_Code_t `$INSTANCE_NAME`_Send_Request(M_Lens_Request_t *request);
void `$INSTANCE_NAME`_Boot_Component( M_Error_Code_t (*send_reply_func)(M_Lens_Reply_t *), UBaseType_t priority);

#endif // `$INSTANCE_NAME`_M_LENS_H

/* [] END OF FILE */

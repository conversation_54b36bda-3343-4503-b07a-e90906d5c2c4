/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#include "FreeRTOS.h"
#include "project.h"
#include "queue.h"
#include "`$INSTANCE_NAME`_M_Lens.h"

#define LENS_WRITE_ADDR 0xECu >> 1
#define LENS_READ_ADDR 0xEDu >> 1
#define LENS_MODE_REGISTER 0x00
#define LENS_VALUE_REGISTER 0x01
#define LENS_SLEEP_MODE 0x00
#define LENS_NORMAL_MODE 0x01

M_Lens_Ref_t `$INSTANCE_NAME`_Lens_ref;

inline M_Error_Code_t `$INSTANCE_NAME`_Send_Request(M_Lens_Request_t *request)
{
    // We drop requests if there isn't enough space
    if (xQueueSendToBack(`$INSTANCE_NAME`_Lens_ref.request_queue, request, 0) != pdTRUE)
    {
        return M_ERROR_CODE_LENS_SEND_REQUEST_FAIL;
    }
    return M_ERROR_CODE_OK;
}

inline M_Error_Code_t `$INSTANCE_NAME`_Await_Request(M_Lens_Request_t *request, uint32_t timeout_ms)
{
    if (xQueueReceive(`$INSTANCE_NAME`_Lens_ref.request_queue, request, pdMS_TO_TICKS(timeout_ms)) != pdTRUE)
    {
        return M_ERROR_CODE_LENS_AWAIT_REQUEST_FAIL;
    }
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_Set(uint8_t value)
{
    M_Error_Code_t ret = M_ERROR_CODE_OK;
    if(`$INSTANCE_NAME`_I2C_MasterSendStart(LENS_WRITE_ADDR, `$INSTANCE_NAME`_I2C_WRITE_XFER_MODE) == `$INSTANCE_NAME`_I2C_MSTR_NO_ERROR)
    {
        if(`$INSTANCE_NAME`_I2C_MasterWriteByte(LENS_VALUE_REGISTER) ==  `$INSTANCE_NAME`_I2C_MSTR_NO_ERROR) // Output DAC register addr
        {
            if(`$INSTANCE_NAME`_I2C_MasterWriteByte(value) ==  `$INSTANCE_NAME`_I2C_MSTR_NO_ERROR)
            {
            } else 
            {
                ret = M_ERROR_CODE_LENS_I2C_WRITE_ERROR;
            }
        } else
        {
            ret = M_ERROR_CODE_LENS_I2C_REGISTER_WRITE_ERROR;
        }

    } else
    {
        ret = M_ERROR_CODE_LENS_I2C_SEND_START_ERROR;
    }
    
    if(`$INSTANCE_NAME`_I2C_MasterSendStop() != `$INSTANCE_NAME`_I2C_MSTR_NO_ERROR && ret != M_ERROR_CODE_OK)
    {
        ret = M_ERROR_CODE_LENS_I2C_SEND_STOP_ERROR;
    }
    
    return ret;
}

M_Error_Code_t `$INSTANCE_NAME`_Get(uint8_t *value)
{
    M_Error_Code_t ret = M_ERROR_CODE_OK;
    if(`$INSTANCE_NAME`_I2C_MasterSendStart(LENS_READ_ADDR, `$INSTANCE_NAME`_I2C_WRITE_XFER_MODE) == `$INSTANCE_NAME`_I2C_MSTR_NO_ERROR)
    {
        if(`$INSTANCE_NAME`_I2C_MasterWriteByte(LENS_VALUE_REGISTER) ==  `$INSTANCE_NAME`_I2C_MSTR_NO_ERROR) // Output DAC register addr
        {
            if(`$INSTANCE_NAME`_I2C_MasterSendRestart(LENS_READ_ADDR, `$INSTANCE_NAME`_I2C_READ_XFER_MODE) == `$INSTANCE_NAME`_I2C_MSTR_NO_ERROR)
            {
                *value = `$INSTANCE_NAME`_I2C_MasterReadByte(`$INSTANCE_NAME`_I2C_NAK_DATA);
            }
            else {
                ret = M_ERROR_CODE_LENS_I2C_SEND_RESTART_ERROR;   
            }
        } else
        {
            ret = M_ERROR_CODE_LENS_I2C_REGISTER_WRITE_ERROR;
        }

    } else
    {
        ret = M_ERROR_CODE_LENS_I2C_SEND_START_ERROR;
    }
    
    if(`$INSTANCE_NAME`_I2C_MasterSendStop() != `$INSTANCE_NAME`_I2C_MSTR_NO_ERROR && ret != M_ERROR_CODE_OK)
    {
        ret = M_ERROR_CODE_LENS_I2C_SEND_STOP_ERROR;
    }
    
    return ret;
}

void `$INSTANCE_NAME`_Handle_Request(M_Lens_Request_t *request) 
{
    M_Error_Code_t result = M_ERROR_CODE_OK;
    M_Lens_Reply_t reply;
    reply.metadata = request->metadata;
    switch(request->request_type)
    {
        case LENS_REQUEST_TYPE_SET:
            reply.reply_type = LENS_REPLY_TYPE_ACK;
            result = `$INSTANCE_NAME`_Set(request->request.set.value);
            break;
        case LENS_REQUEST_TYPE_GET:
            reply.reply_type = LENS_REPLY_TYPE_GET;
            result = `$INSTANCE_NAME`_Get(&reply.reply.get.value);
            break;
        default:
            result = M_ERROR_CODE_LENS_NO_REQUEST_HANDLER;
            break;
    }
    
    if (result != M_ERROR_CODE_OK)
    {
        reply.reply_type = LENS_REPLY_TYPE_ERROR;
        reply.reply.error = result;
    }
    
    `$INSTANCE_NAME`_Lens_ref.send_reply_func(&reply);
}

void `$INSTANCE_NAME`_Task(void *context)
{
    (void) context;
    M_Lens_Request_t request;
    
    `$INSTANCE_NAME`_I2C_MasterSendStart(LENS_WRITE_ADDR, `$INSTANCE_NAME`_I2C_WRITE_XFER_MODE);
    `$INSTANCE_NAME`_I2C_MasterWriteByte(LENS_MODE_REGISTER);
    `$INSTANCE_NAME`_I2C_MasterWriteByte(LENS_NORMAL_MODE);
    `$INSTANCE_NAME`_I2C_MasterSendStop();
    
    while (true)
    {
        if (`$INSTANCE_NAME`_Await_Request(&request, portMAX_DELAY / portTICK_PERIOD_MS) != M_ERROR_CODE_OK)
        {
            continue;
        }
        
        `$INSTANCE_NAME`_Handle_Request(&request);
    }
}

void `$INSTANCE_NAME`_Boot_Component( M_Error_Code_t (*send_reply_func)(M_Lens_Reply_t *), UBaseType_t priority)
{
    `$INSTANCE_NAME`_I2C_Start();
    `$INSTANCE_NAME`_Lens_ref.request_queue = xQueueCreate(4, sizeof(M_Lens_Request_t));
    `$INSTANCE_NAME`_Lens_ref.send_reply_func = send_reply_func;
    
    xTaskCreate(`$INSTANCE_NAME`_Task, "`$INSTANCE_NAME`", configMINIMAL_STACK_SIZE + 512, NULL, priority, 0);
}



/* [] END OF FILE */

/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#include "FreeRTOS.h"
#include "m_time_request.h"

int32_t Div_Round_Closest(int32_t A, int32_t B)
{
    if (A==0)
        return 0;
    if(A<0)
        if(B<0)
            if(B==-1)
                return -A;
            else
                return (A + (-B+1)/2) / B + 1;
        else
            if(B==1)
                return A;
            else
                return (A + ( B+1)/2) / B - 1;
    else
        if(B<0)
            return (A - (-B+1)/2) / B - 1;
        else
            return (A - ( B+1)/2) / B + 1;
}

/* [] END OF FILE */

/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef M_DAWG_REQUEST_H
#define M_DAWG_REQUEST_H

  
#include "stdint.h"
#include "stdbool.h"
#include "queue.h"
#include "request_metadata.h"
#include "m_error_codes.h"

typedef enum {
    DAWG_REQUEST_TYPE_NONE = 0, // None
    DAWG_REQUEST_TYPE_CONFIG = 1, // M_Dawg_Config_t
    DAWG_REQUEST_TYPE_ARM = 2, // M_Dawg_Arming_request_t
    DAWG_REQUEST_TYPE_PET = 3, // None
    DAWG_REQUEST_TYPE_STATE = 4, // None
} M_Dawg_RequestType;

typedef enum {
    DAWG_REPLY_TYPE_NONE = 0, // None
    DAWG_REPLY_TYPE_ERROR = 1, // None
    DAWG_REPLY_TYPE_ACK = 2, // None
    DAWG_REPLY_TYPE_STATE = 3, // M_Dawg_State_Reply_t
} M_Dawg_ReplyType;

typedef struct {
    uint32_t timeout_ms;
} M_Dawg_Config_t;

typedef struct {
    bool armed;
} M_Dawg_Arming_Request_t;

typedef struct {
    Request_Metadata_t metadata;
    M_Dawg_RequestType request_type;
    union {
        M_Dawg_Arming_Request_t arming;
        M_Dawg_Config_t config;
    } request;
} M_Dawg_Request_t;

typedef struct {
    bool armed;
    bool petted;
} M_Dawg_State_Reply_t;

typedef struct {
    Request_Metadata_t metadata;
    M_Dawg_ReplyType reply_type;
    union {
        M_Dawg_State_Reply_t state;
        M_Error_Code_t error;
    } reply;
} M_Dawg_Reply_t;

typedef struct {
    QueueHandle_t request_queue;
    M_Dawg_Config_t config;
    M_Error_Code_t (*send_reply_func)(M_Dawg_Reply_t *);
} M_Dawg_Ref_t;

#endif // M_DAWG_REQUEST_H

/* [] END OF FILE */

/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef M_TIME_REQUEST_H
#define M_TIME_REQUEST_H

#include "FreeRTOS.h"
#include "stdint.h"
#include "stdbool.h"
#include <stdlib.h>
#include "m_error_codes.h"
#include "request_metadata.h"

typedef enum {
    TIME_REQUEST_TYPE_NONE = 0, // None
    TIME_REQUEST_TYPE_SET = 1, // M_Time_Set_Epoch_Request_t
    TIME_REQUEST_TYPE_GET = 2, // None
    TIME_REQUEST_TYPE_DEBUG = 3,
} M_Time_RequestType;

typedef enum {
    TIME_REPLY_TYPE_NONE = 0, // None
    TIME_REPLY_TYPE_ERROR = 1, // uint32_t
    TIME_REPLY_TYPE_ACK = 2, // None
    TIME_REPLY_TYPE_TIMESTAMP = 3, // M_Timestamp
    TIME_REPLY_TYPE_DEBUG = 4,
} M_Time_ReplyType;

typedef struct {
    uint32_t seconds;
    uint32_t micros;
} M_Timestamp_t;

typedef struct {
    M_Timestamp_t timestamp;
    int32_t offset_us;
    int32_t kp;
    int32_t ki;
} M_Time_Set_Epoch_Request_t;

typedef struct {
    Request_Metadata_t metadata;
    M_Time_RequestType request_type;
    union {
        M_Time_Set_Epoch_Request_t set;
    } request;
} M_Time_Request_t;

typedef struct {
    M_Timestamp_t timestamp;
    uint32_t pps_timer_val;
    uint32_t pps_ticks;
    double freq_mul;
    int32_t error_ticks;
    int32_t error_ticks2;
} M_Time_Debug_Reply_t;

typedef struct {
    Request_Metadata_t metadata;
    M_Time_ReplyType reply_type;
    union {
        M_Timestamp_t timestamp;
        M_Error_Code_t error;
        M_Time_Debug_Reply_t debug;
    } reply;
} M_Time_Reply_t;

#define PPS_TIMER_PERIOD 0xFFFFFFFF // We count down from max u32
#define PPS_EXPIRED_TIMER_PERIOD (10 * 1000 * 1000) // 10 seconds with no pps means manual tick update
#define USEC_PER_SEC (1000 * 1000)

typedef struct {
    uint64_t epoch_usecs; // epoch usecs and 0 signal val
    uint64_t latest_usecs; // last we've ever returned
    uint32_t pps_timer_val; // ticks at the last pps signal
    uint32_t pps_ticks; // pps ticks since epoch
    double freq_mul; // frequency multiplier to make clock line up
    uint32_t pps_timer_addition; // for spurious tick recovery

    /* For debug calculations */
    double last_freq_mul; // what this was last time
    
    /* Handle rollover */
    uint32_t _last_pps_timer_triggered;

    /* PPS filtering */
    uint32_t _last_pps_on_time;
} M_Time_Ref_t;

int32_t Div_Round_Closest(int32_t A, int32_t B);

inline int32_t Timestamp_Diff_Micros(M_Timestamp_t *left, M_Timestamp_t *right)
{
    return (((int32_t)left->seconds - (int32_t)right->seconds) * 1000000) + ((int32_t)left->micros - (int32_t)right->micros);
}
#endif // M_TIME_REQUEST_H

/* [] END OF FILE */

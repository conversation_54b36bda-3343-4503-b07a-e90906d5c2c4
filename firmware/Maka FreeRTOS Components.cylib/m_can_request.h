/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef M_CAN_REQUEST_H
#define M_CAN_REQUEST_H
    
#include "stdint.h"
#include "CAN_Open.h"
#include "m_error_codes.h"

// Requests

typedef enum {
    M_CAN_REQUEST_TYPE_NONE = 0, // None
    M_CAN_REQUEST_TYPE_SDO = 1, // M_CAN_SDO_Request
    M_CAN_REQUEST_TYPE_PDO = 2, // M_CAN_SDO_Request
    M_CAN_REQUEST_TYPE_RTR_PDO = 3, // M_CAN_RTR_PDO_Request
    M_CAN_REQUEST_TYPE_NMT = 4, // M_CAN_NMT_Request
    M_CAN_REQUEST_TYPE_AWAIT_REPLY = 5, // M_CAN_Await_Request
    M_CAN_REQUEST_TYPE_SDO_DOWNLOAD = 6, // M_CAN_SDO_Download_Request
    M_CAN_REQUEST_TYPE_SDO_UPLOAD = 7, // M_CAN_SDO_Upload_Request
    M_CAN_REQUEST_TYPE_NMT_RESET = 8, // None
    M_CAN_REQUEST_TYPE_NMT_START = 9, // None
    M_CAN_REQUEST_TYPE_NMT_STOP = 10, // None
} M_CAN_RequestType;

typedef enum {
    M_CAN_REPLY_TYPE_NONE = 0, // None
    M_CAN_REPLY_TYPE_ACK = 1, // None
    M_CAN_REPLY_TYPE_MESSAGE = 2, // CAN_Open_Message_t
    M_CAN_REPLY_TYPE_ERROR = 3, // M_Error_Code_t
    
} M_CAN_ReplyType;

typedef struct {
    uint32_t value;
    uint16_t index;
    uint8_t subindex;
    uint8_t cs;
    uint8_t expedited;
} M_CAN_SDO_Request_t;

typedef struct {
    uint8_t func;
    uint8_t size;
    uint8_t data[8];
} M_CAN_PDO_Request_t;

typedef struct {
    uint8_t func;
} M_CAN_RTR_PDO_Request_t;

typedef struct {
    uint8_t state;
} M_CAN_NMT_Request_t;

typedef struct {
    uint16_t timeout_ms;
    uint8_t func;
} M_CAN_Await_Request_t;

typedef struct {
    uint32_t value;
    uint16_t index;
    uint8_t subindex;
} M_CAN_SDO_Download_Request_t;

typedef struct {
    uint16_t index;
    uint8_t subindex;
} M_CAN_SDO_Upload_Request_t;

typedef struct {
    M_CAN_RequestType request_type;
    union {
        M_CAN_SDO_Request_t sdo;
        M_CAN_PDO_Request_t pdo;
        M_CAN_RTR_PDO_Request_t rtr;
        M_CAN_NMT_Request_t nmt;
        M_CAN_Await_Request_t await;
        M_CAN_SDO_Download_Request_t sdo_download;
        M_CAN_SDO_Upload_Request_t sdo_upload;
    } request;
} M_CAN_Request_t;

typedef struct {
    M_CAN_ReplyType reply_type;
    union {
        CAN_Open_Message_t msg;
        M_Error_Code_t error;
    } reply;
} M_CAN_Reply_t;

#endif // M_CAN_REQUEST_H

/* [] END OF FILE */

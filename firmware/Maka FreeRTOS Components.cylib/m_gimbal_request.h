/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef M_GIMBAL_REQUEST_H
#define M_GIMBAL_REQUEST_H

#include "m_servo_request.h"
#include "m_error_codes.h"
#include "request_metadata.h"

typedef enum {
    GIMBAL_REQUEST_TYPE_NONE = 0, // None
    GIMBAL_REQUEST_TYPE_BOOT = 1, // M_Gimbal_Boot_Request_t
    GIMBAL_REQUEST_TYPE_STOP = 2, // None
    GIMBAL_REQUEST_TYPE_SERVOS = 3, // M_Gimbal_Servos_Request_t
} M_Gimbal_RequestType;

typedef enum {
    GIMBAL_REPLY_TYPE_NONE = 0, // None
    GIMBAL_REPLY_TYPE_ERROR = 1, // uint32_t
    GIMBAL_REPLY_TYPE_ACK = 2, // None
    GIMBAL_REPLY_TYPE_SERVOS = 3, // M_Gimbal_Servos_Reply_t
} M_Gimbal_ReplyType;

typedef struct {
    M_Servo_Request_t pan;
    M_Servo_Request_t tilt;
} M_Gimbal_Servos_Request_t;

typedef struct {
    M_Epos_Home_Params_t pan_params;
    M_Epos_Home_Params_t tilt_params;
} M_Gimbal_Boot_Request_t;

typedef struct {
    Request_Metadata_t metadata;
    M_Gimbal_RequestType request_type;
    union {
        M_Gimbal_Boot_Request_t boot;
        M_Gimbal_Servos_Request_t servos;
    } request;
} M_Gimbal_Request_t;

typedef struct {
    M_Servo_Reply_t pan;
    M_Servo_Reply_t tilt;
} M_Gimbal_Servos_Reply_t;

typedef struct {
    Request_Metadata_t metadata;
    M_Gimbal_ReplyType reply_type;
    union {
        M_Gimbal_Servos_Reply_t servos;
        M_Error_Code_t error;
    } reply;
} M_Gimbal_Reply_t;

typedef struct {
    QueueHandle_t request_queue;
    M_Error_Code_t (*send_reply_func)(M_Gimbal_Reply_t *);
} M_Gimbal_Ref_t;

#endif // M_GIMBAL_REQUEST_H

/* [] END OF FILE */

/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef M_LENS_REQUEST_H
#define M_LENS_REQUEST_H
    
#include "stdint.h"
#include "stdbool.h"
#include "queue.h"
#include "m_error_codes.h"
#include "request_metadata.h"

typedef enum {
    LENS_REQUEST_TYPE_NONE = 0, // None
    LENS_REQUEST_TYPE_SET = 1, // M_Laser_State_t
    LENS_REQUEST_TYPE_GET = 2, // None
} M_Lens_RequestType;

typedef enum {
    LENS_REPLY_TYPE_NONE = 0, // None
    LENS_REPLY_TYPE_ERROR = 1, // uint32_t
    LENS_REPLY_TYPE_ACK = 2, // None
    LENS_REPLY_TYPE_GET = 3, // M_Laser_State_t
} M_Lens_ReplyType;

typedef struct {
    uint8_t value;
} M_Lens_Value_t;

typedef struct {
    Request_Metadata_t metadata;
    M_Lens_RequestType request_type;
    union {
        M_Lens_Value_t set;
    } request;
} M_Lens_Request_t;

typedef struct {
    Request_Metadata_t metadata;
    M_Lens_ReplyType reply_type;
    union {
        M_Lens_Value_t get;
        M_Error_Code_t error;
    } reply;
} M_Lens_Reply_t;

typedef struct {
    QueueHandle_t request_queue;
    M_Error_Code_t (*send_reply_func)(M_Lens_Reply_t *);
} M_Lens_Ref_t;

#endif // M_LENS_REQUEST_H

/* [] END OF FILE */

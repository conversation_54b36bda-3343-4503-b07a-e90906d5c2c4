/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#include "FreeRTOS.h"
#include "project.h"
#include "queue.h"
#include "`$INSTANCE_NAME`_M_Laser.h"

M_Laser_Ref_t `$INSTANCE_NAME`_Laser_ref;

inline M_Error_Code_t `$INSTANCE_NAME`_Send_Request(M_Laser_Request_t *request)
{
    // We drop requests if there isn't enough space
    if (xQueueSendToBack(`$INSTANCE_NAME`_Laser_ref.request_queue, request, 0) != pdTRUE)
    {
        return M_ERROR_CODE_LASER_SEND_REQUEST_FAIL;
    }
    return M_ERROR_CODE_OK;
}

inline M_Error_Code_t `$INSTANCE_NAME`_Await_Request(M_Laser_Request_t *request, uint32_t timeout_ms)
{
    if (xQueueReceive(`$INSTANCE_NAME`_Laser_ref.request_queue, request, pdMS_TO_TICKS(timeout_ms)) != pdTRUE)
    {
        return M_ERROR_CODE_LASER_AWAIT_REQUEST_FAIL;
    }
    return M_ERROR_CODE_OK;
}

void `$INSTANCE_NAME`_Laser(bool on)
{
    #if !defined(`$INSTANCE_NAME`_Laser_Fire_Reg_Sync_ctrl_reg__REMOVED)
    `$INSTANCE_NAME`_Laser_Fire_Reg_Write(on);
    #endif
}

bool `$INSTANCE_NAME`_Get_Laser()
{
    #if !defined(`$INSTANCE_NAME`_Laser_Fire_Reg_Sync_ctrl_reg__REMOVED)
    return (bool) `$INSTANCE_NAME`_Laser_Fire_Reg_Read();
    #else
    return false;
    #endif
}

void `$INSTANCE_NAME`_Intensity(uint16_t intensity)
{
    `$INSTANCE_NAME`_Intensity_PWM_WriteCompare(intensity);
}

void `$INSTANCE_NAME`_Handle_Request(M_Laser_Request_t *request) 
{
    M_Error_Code_t result = M_ERROR_CODE_OK;
    M_Laser_Reply_t reply;
    reply.metadata = request->metadata;
    switch(request->request_type)
    {
        case LASER_REQUEST_TYPE_SET:
            reply.reply_type = LASER_REPLY_TYPE_LASER_REPLY;
            reply.reply.laser_reply.on = false; // since we will never have thermistors set to false so data is ok
            reply.reply.laser_reply.lpsu_state = false; // since we will never have thermistors set to false
            reply.reply.laser_reply.fireable = false; // since we will never have thermistors set to false
            reply.reply.laser_reply.raw_therm1_reading_mv = 0;
            reply.reply.laser_reply.raw_therm2_reading_mv = 0;
            `$INSTANCE_NAME`_Laser(request->request.laser.on);
            break;
        case LASER_REQUEST_TYPE_GET:
            reply.reply_type = LASER_REPLY_TYPE_LASER_STATE;
            reply.reply.laser.on = `$INSTANCE_NAME`_Get_Laser();
            break;
        case LASER_REQUEST_TYPE_INTENSITY:
            reply.reply_type = LASER_REPLY_TYPE_ACK;
            `$INSTANCE_NAME`_Intensity(request->request.intensity.intensity);
            break;
        default:
            result = M_ERROR_CODE_LASER_NO_REQUEST_HANDLER;
            break;
    }
    
    if (result != M_ERROR_CODE_OK)
    {
        reply.reply_type = LASER_REPLY_TYPE_ERROR;
        reply.reply.error = result;
    }
    
    `$INSTANCE_NAME`_Laser_ref.send_reply_func(&reply);
}

void `$INSTANCE_NAME`_Task(void *context)
{
    (void) context;
    M_Laser_Request_t request;
    
    while (true)
    {
        if (`$INSTANCE_NAME`_Await_Request(&request, portMAX_DELAY / portTICK_PERIOD_MS) != M_ERROR_CODE_OK)
        {
            continue;
        }
        
        `$INSTANCE_NAME`_Handle_Request(&request);
    }
}

void `$INSTANCE_NAME`_Boot_Component( M_Error_Code_t (*send_reply_func)(M_Laser_Reply_t *), UBaseType_t priority)
{
    `$INSTANCE_NAME`_Intensity_PWM_Start();
    `$INSTANCE_NAME`_PWM_Clock_Start();
    `$INSTANCE_NAME`_Intensity(UINT16_MAX);
    `$INSTANCE_NAME`_Laser(false);
    `$INSTANCE_NAME`_Laser_ref.request_queue = xQueueCreate(4, sizeof(M_Laser_Request_t));
    `$INSTANCE_NAME`_Laser_ref.send_reply_func = send_reply_func;
    
    xTaskCreate(`$INSTANCE_NAME`_Task, "`$INSTANCE_NAME`", configMINIMAL_STACK_SIZE + 512, NULL, priority, 0);
}



/* [] END OF FILE */

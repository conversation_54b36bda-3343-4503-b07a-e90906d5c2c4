/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#include "FreeRTOS.h"
#include "project.h"
#include "queue.h"
#include "utils.h"
#include "`$INSTANCE_NAME`_M_Servo.h"

#define DEFAULT_SERVO_REPLY_TIMEOUT_MS 500

// Servo Component is designed to have only one master it communicates with
M_Servo_Ref_t `$INSTANCE_NAME`_servo_ref;

inline M_Error_Code_t `$INSTANCE_NAME`_Send_Request(M_Servo_Request_t *request, uint16_t timeout_ms)
{
    xQueueReset(`$INSTANCE_NAME`_servo_ref.reply_queue);
    if(xQueueSendToBack(`$INSTANCE_NAME`_servo_ref.request_queue, request, pdMS_TO_TICKS(timeout_ms)) != pdTRUE)
    {
        return M_ERROR_CODE_SERVO_SEND_REQUEST_FAIL;
    }
    return M_ERROR_CODE_OK;
}

inline M_Error_Code_t `$INSTANCE_NAME`_Await_Request(M_Servo_Request_t *request)
{
    if(xQueueReceive(`$INSTANCE_NAME`_servo_ref.request_queue, request, portMAX_DELAY) != pdTRUE)
    {
        return M_ERROR_CODE_SERVO_AWAIT_REQUEST_FAIL;
    }
    return M_ERROR_CODE_OK;
}

inline M_Error_Code_t `$INSTANCE_NAME`_Send_Reply(M_Servo_Reply_t *reply)
{
    if(xQueueSendToBack(`$INSTANCE_NAME`_servo_ref.reply_queue, reply, pdMS_TO_TICKS(DEFAULT_SERVO_REPLY_TIMEOUT_MS)) != pdTRUE)
    {
        return M_ERROR_CODE_SERVO_SEND_REPLY_FAIL;
    }
    return M_ERROR_CODE_OK;
}

inline M_Error_Code_t `$INSTANCE_NAME`_Await_Reply(M_Servo_Reply_t *reply, uint16_t timeout_ms)
{
    if(xQueueReceive(`$INSTANCE_NAME`_servo_ref.reply_queue, reply, pdMS_TO_TICKS(timeout_ms)) != pdTRUE)
    {
        return M_ERROR_CODE_SERVO_AWAIT_REPLY_FAIL;
    }
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_Configure(uint8_t node_id, M_Servo_Config_t *config)
{
    uint8_t index;
    RETURN_CODE_IF_NOT_OK(`$EPOS_INSTANCE`_MCAN_Configure_Node(node_id, &index))
    `$INSTANCE_NAME`_servo_ref.config = *config;
    `$INSTANCE_NAME`_servo_ref.node_ref = `$EPOS_INSTANCE`_MCAN_Get_Node_Ref(index);
    if(`$INSTANCE_NAME`_servo_ref.node_ref == NULL)
    {
        return M_ERROR_CODE_SERVO_NODE_CONFIGURATION_FAIL;
    }
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_Go_To(int32_t position, uint32_t velocity, bool await)
{
    if(!(position >= 0 && position <= `$INSTANCE_NAME`_servo_ref.limit && velocity <= `$INSTANCE_NAME`_servo_ref.config.max_profile_velocity))
    {
        return M_ERROR_CODE_SERVO_MOVE_OUT_OF_RANGE;
    }
    RETURN_CODE_IF_NOT_OK(`$EPOS_INSTANCE`_Go_To_Position(`$INSTANCE_NAME`_servo_ref.node_ref, position, velocity))
    
    if (await)
    {
        RETURN_CODE_IF_NOT_OK(`$EPOS_INSTANCE`_Await_Settling(`$INSTANCE_NAME`_servo_ref.node_ref, position, `$INSTANCE_NAME`_servo_ref.config.settle_window, `$INSTANCE_NAME`_servo_ref.config.settle_timeout, NULL))
    }
    
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_Go_To_Calibrate(
    int32_t position,
    uint32_t velocity,
    uint16_t window,
    uint16_t time_window_ms,
    uint16_t timeout_ms,
    uint8_t period_ms,
    uint16_t *settle_time_ms_out
)
{
    if(!(position >= 0 && position <= `$INSTANCE_NAME`_servo_ref.limit && velocity <= `$INSTANCE_NAME`_servo_ref.config.max_profile_velocity))
    {
        return M_ERROR_CODE_SERVO_MOVE_OUT_OF_RANGE;
    }
    RETURN_CODE_IF_NOT_OK(`$EPOS_INSTANCE`_Go_To_Position(`$INSTANCE_NAME`_servo_ref.node_ref, position, velocity))
    RETURN_CODE_IF_NOT_OK(`$EPOS_INSTANCE`_Calibration_Settle(`$INSTANCE_NAME`_servo_ref.node_ref, position, window, time_window_ms, timeout_ms, period_ms, settle_time_ms_out))
    
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_Follow_Timestamp(
    M_Timestamp_t *timestamp,
    int32_t follow_velocity, 
    int32_t follow_accel,
    int32_t *out_p1,
    M_Timestamp_t *out_t1,
    int32_t extra_time_diff_ms
)
{
    int32_t current_velocity = 0;
    RETURN_CODE_IF_NOT_OK(`$EPOS_INSTANCE`_Get_Actual_Position_Velocity(`$INSTANCE_NAME`_servo_ref.node_ref, out_p1, &current_velocity))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_servo_ref.get_timestamp(out_t1))
    
    int32_t diff_millis = Timestamp_Diff_Micros(out_t1, timestamp);  
    diff_millis = Div_Round_Closest(diff_millis, 1000);
    
    if (diff_millis > `$INSTANCE_NAME`_servo_ref.config.max_diff_millis + extra_time_diff_ms)
    {
        // TODO: Until We Figure out the 808 issue, we don't return an error here but simply presume no time has elapsed
        // return M_ERROR_CODE_SERVO_FOLLOW_TIME_DIFF_TOO_BIG;
    }
    else
    {
        follow_velocity = follow_velocity + Div_Round_Closest(follow_accel * diff_millis, 1000);
    }

    if (follow_velocity > 0 || follow_velocity < 0)
    {
        int32_t follow_position = follow_velocity > 0 ? `$INSTANCE_NAME`_servo_ref.limit : 0;
        uint32_t follow_velocity_mrpm = abs(Div_Round_Closest(follow_velocity * 60 * 1000, `$INSTANCE_NAME`_servo_ref.resolution));

        // Follow
        if(!(follow_position >= 0 && follow_position <= `$INSTANCE_NAME`_servo_ref.limit && follow_velocity_mrpm <= `$INSTANCE_NAME`_servo_ref.config.max_profile_velocity))
        {
            return M_ERROR_CODE_SERVO_MOVE_OUT_OF_RANGE;
        } 
        RETURN_CODE_IF_NOT_OK(`$EPOS_INSTANCE`_Go_To_Position(`$INSTANCE_NAME`_servo_ref.node_ref, follow_position, follow_velocity_mrpm))
    }
    
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_Go_To_Timestamp(
    M_Timestamp_t *timestamp,
    M_Servo_Go_To_Mode_t mode, 
    int32_t position,
    uint32_t velocity_mrpm, 
    int32_t follow_velocity, 
    int32_t follow_accel,
    uint16_t interval_sleep_time_ms, 
    int32_t *out_p1, 
    int32_t *out_p2, 
    M_Timestamp_t *out_t1, 
    M_Timestamp_t *out_t2
)
{
    int32_t current_velocity = 0;
    
    RETURN_CODE_IF_NOT_OK(`$EPOS_INSTANCE`_Get_Actual_Position_Velocity(`$INSTANCE_NAME`_servo_ref.node_ref, out_p1, &current_velocity))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_servo_ref.get_timestamp(out_t1))
    
    int32_t diff_millis = Timestamp_Diff_Micros(out_t1, timestamp);
    diff_millis = Div_Round_Closest(diff_millis, 1000);
    
    if (diff_millis > `$INSTANCE_NAME`_servo_ref.config.max_diff_millis)
    {
        // TODO: Until We Figure out the 808 issue, we don't return an error here but simply presume no time has elapsed
        // return M_ERROR_CODE_SERVO_TIME_DIFF_TOO_BIG;
    }
    else
    {
        position = position + Div_Round_Closest(diff_millis * follow_velocity, 1000) + Div_Round_Closest(Div_Round_Closest(follow_accel * diff_millis * diff_millis, 1000000), 2);
    }
    
    if(!(position >= 0 && position <= `$INSTANCE_NAME`_servo_ref.limit && velocity_mrpm <= `$INSTANCE_NAME`_servo_ref.config.max_profile_velocity))
    {
        return M_ERROR_CODE_SERVO_MOVE_OUT_OF_RANGE;
    }
    
    RETURN_CODE_IF_NOT_OK(`$EPOS_INSTANCE`_Go_To_Position(`$INSTANCE_NAME`_servo_ref.node_ref, position, velocity_mrpm))
    
    switch(mode){
        case SERVO_GO_TO_MODE_IMMEDIATE:
            break;
        case SERVO_GO_TO_MODE_REACHED:
            RETURN_CODE_IF_NOT_OK(`$EPOS_INSTANCE`_Await_Target_Reached(`$INSTANCE_NAME`_servo_ref.node_ref, `$INSTANCE_NAME`_servo_ref.config.settle_timeout, NULL))
            break;
        case SERVO_GO_TO_MODE_SETTLED:
            RETURN_CODE_IF_NOT_OK(`$EPOS_INSTANCE`_Await_Settling(`$INSTANCE_NAME`_servo_ref.node_ref, position, `$INSTANCE_NAME`_servo_ref.config.settle_window, `$INSTANCE_NAME`_servo_ref.config.settle_timeout, NULL))
            break;
        default:
            return false;
    }
    
    // Arbitrary sleep
    vTaskDelay(pdMS_TO_TICKS(interval_sleep_time_ms));
    
    return `$INSTANCE_NAME`_Follow_Timestamp(timestamp, follow_velocity, follow_accel, out_p2, out_t2, interval_sleep_time_ms + `$INSTANCE_NAME`_servo_ref.config.settle_timeout);
}

M_Error_Code_t `$INSTANCE_NAME`_Go_To_Delta(int32_t delta_position, uint32_t velocity, M_Servo_Go_To_Mode_t mode, int32_t *out_position)
{
    int32_t current_position = 0;
    int32_t current_velocity = 0;
    RETURN_CODE_IF_NOT_OK(EPOS_Get_Actual_Position_Velocity(`$INSTANCE_NAME`_servo_ref.node_ref, &current_position, &current_velocity))
    
    if (delta_position > 4 || delta_position < -4)
    {
        // Now we have the latest position, we add the delta
        current_position += delta_position;
        if(!(current_position >= 0 && current_position <= `$INSTANCE_NAME`_servo_ref.limit && velocity <= `$INSTANCE_NAME`_servo_ref.config.max_profile_velocity))
        {
            return M_ERROR_CODE_SERVO_MOVE_OUT_OF_RANGE;
        }
        RETURN_CODE_IF_NOT_OK(EPOS_Go_To_Position(`$INSTANCE_NAME`_servo_ref.node_ref, current_position, velocity))
        
        switch(mode){
        case SERVO_GO_TO_MODE_IMMEDIATE:
            break;
        case SERVO_GO_TO_MODE_REACHED:
            RETURN_CODE_IF_NOT_OK(EPOS_Await_Target_Reached(`$INSTANCE_NAME`_servo_ref.node_ref, `$INSTANCE_NAME`_servo_ref.config.settle_timeout, NULL))
            break;
        case SERVO_GO_TO_MODE_SETTLED:
            RETURN_CODE_IF_NOT_OK(EPOS_Await_Settling(`$INSTANCE_NAME`_servo_ref.node_ref, current_position, `$INSTANCE_NAME`_servo_ref.config.settle_window, `$INSTANCE_NAME`_servo_ref.config.settle_timeout, NULL))
            break;
        default:
            return false;
        }
    }
    
    if (out_position != NULL)
    {
        *out_position = current_position;
    }
    
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_Go_To_Delta_Follow(int32_t delta_position, uint32_t velocity, int16_t follow_velocity_vector, uint32_t follow_velocity_mrpm, uint16_t interval_sleep_time_ms, M_Servo_Go_To_Mode_t mode, int32_t *out_position)
{
    int32_t current_position = 0;
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Go_To_Delta(delta_position, velocity, mode, &current_position))
    
    if (follow_velocity_vector > 4 || follow_velocity_vector < -4)
    {
        int32_t follow_position = current_position + follow_velocity_vector;
        
        // Limit Conditions Handling
        if (follow_velocity_vector > 0 && follow_position > `$INSTANCE_NAME`_servo_ref.limit)
        {
            follow_position = `$INSTANCE_NAME`_servo_ref.limit;
        } else if (follow_velocity_vector < 0 && follow_position < 0)
        {
            follow_position = 0;
        }
        
        // Arbitrary sleep
         vTaskDelay(pdMS_TO_TICKS(interval_sleep_time_ms));
        
        // Follow
        if(!(follow_position >= 0 && follow_position <= `$INSTANCE_NAME`_servo_ref.limit && follow_velocity_mrpm <= `$INSTANCE_NAME`_servo_ref.config.max_profile_velocity))
        {
            return M_ERROR_CODE_SERVO_MOVE_OUT_OF_RANGE;
        }
        RETURN_CODE_IF_NOT_OK(EPOS_Go_To_Position(`$INSTANCE_NAME`_servo_ref.node_ref, follow_position, follow_velocity_mrpm))
    }
    else
    {
        // For Now Throttle with that, we may want to revisit in the future
        vTaskDelay(pdMS_TO_TICKS(interval_sleep_time_ms));
    }
    
    if (out_position != NULL)
    {
        *out_position = current_position;
    }
    
    return M_ERROR_CODE_OK;
}

inline M_Error_Code_t `$INSTANCE_NAME`_Boot(M_Servo_Boot_Request_t *request)
{
    if (`$EPOS_INSTANCE`_Setup_PDOs(`$INSTANCE_NAME`_servo_ref.node_ref) != M_ERROR_CODE_OK)
    {
        RETURN_CODE_IF_NOT_OK(`$EPOS_INSTANCE`_MCAN_NMT_Reset_Node(`$INSTANCE_NAME`_servo_ref.node_ref))
        RETURN_CODE_IF_NOT_OK(`$EPOS_INSTANCE`_Setup_PDOs(`$INSTANCE_NAME`_servo_ref.node_ref))
    }
    
    RETURN_CODE_IF_NOT_OK(`$EPOS_INSTANCE`_MCAN_NMT_Start_Node(`$INSTANCE_NAME`_servo_ref.node_ref))
    RETURN_CODE_IF_NOT_OK(`$EPOS_INSTANCE`_Enable_Node(`$INSTANCE_NAME`_servo_ref.node_ref))
    RETURN_CODE_IF_NOT_OK(`$EPOS_INSTANCE`_Home_From_Params(`$INSTANCE_NAME`_servo_ref.node_ref, &request->params, `$INSTANCE_NAME`_servo_ref.config.settle_timeout, &`$INSTANCE_NAME`_servo_ref.limit))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Go_To(`$INSTANCE_NAME`_servo_ref.limit / 2, `$INSTANCE_NAME`_servo_ref.config.max_profile_velocity, true))
    
    return M_ERROR_CODE_OK;
}

inline M_Error_Code_t `$INSTANCE_NAME`_Stop()
{
    RETURN_CODE_IF_NOT_OK(`$EPOS_INSTANCE`_Disable_Node(`$INSTANCE_NAME`_servo_ref.node_ref))
    return `$EPOS_INSTANCE`_MCAN_NMT_Stop_Node(`$INSTANCE_NAME`_servo_ref.node_ref);
}

M_Error_Code_t `$INSTANCE_NAME`_Handle_Request(M_Servo_Request_t *request, M_Servo_Reply_t *out_reply) 
{
    M_Error_Code_t result = M_ERROR_CODE_OK;
    switch(request->request_type)
    {
        case SERVO_REQUEST_TYPE_NONE:
            out_reply->reply_type = SERVO_REPLY_TYPE_NONE;
            break;
        case SERVO_REQUEST_TYPE_CONFIG:
            out_reply->reply_type = SERVO_REPLY_TYPE_ACK;
            result = `$INSTANCE_NAME`_Configure(request->request.config.node_id, &request->request.config.config);
            break;
        case SERVO_REQUEST_TYPE_BOOT:
            out_reply->reply_type = SERVO_REPLY_TYPE_ACK;
            result = `$INSTANCE_NAME`_Boot(&request->request.boot);
            break;
        case SERVO_REQUEST_TYPE_STOP:
            out_reply->reply_type = SERVO_REPLY_TYPE_ACK;
            result = `$INSTANCE_NAME`_Stop();
            break;
        case SERVO_REQUEST_TYPE_GO_TO:
            out_reply->reply_type = SERVO_REPLY_TYPE_ACK;
            result = `$INSTANCE_NAME`_Go_To(request->request.go_to.position, request->request.go_to.velocity, request->request.go_to.await);
            break;
        case SERVO_REQUEST_TYPE_GET_LIMITS:
            out_reply->reply_type = SERVO_REPLY_TYPE_LIMITS;
            out_reply->reply.limits.min = 0;
            out_reply->reply.limits.max = `$INSTANCE_NAME`_servo_ref.limit;
            break;
        case SERVO_REQUEST_TYPE_EPOS:
            out_reply->reply_type = SERVO_REPLY_TYPE_EPOS;
            result = `$EPOS_INSTANCE`_Handle_Request(`$INSTANCE_NAME`_servo_ref.node_ref, &request->request.epos, &out_reply->reply.epos);
            break;
        case SERVO_REQUEST_TYPE_DELTA:
            out_reply->reply_type = SERVO_REPLY_TYPE_POSITION;
            result = `$INSTANCE_NAME`_Go_To_Delta(request->request.delta.delta_position, request->request.delta.velocity, request->request.delta.mode, &out_reply->reply.pos.position);
            break;
        case SERVO_REQUEST_TYPE_DELTA_FOLLOW:
            out_reply->reply_type = SERVO_REPLY_TYPE_POSITION;
            result = `$INSTANCE_NAME`_Go_To_Delta_Follow(request->request.follow.delta.delta_position, request->request.follow.delta.velocity, request->request.follow.follow_velocity_vector, request->request.follow.follow_velocity_mrpm, request->request.follow.interval_sleep_time_ms, request->request.follow.delta.mode, &out_reply->reply.pos.position);
            break;
        case SERVO_REQUEST_TYPE_GO_TO_CALIBRATE:
            out_reply->reply_type = SERVO_REPLY_TYPE_SETTLE_TIME;
            result = `$INSTANCE_NAME`_Go_To_Calibrate(request->request.calibrate.position, request->request.calibrate.velocity, request->request.calibrate.window, request->request.calibrate.time_window_ms, request->request.calibrate.timeout_ms, request->request.calibrate.period_ms, &out_reply->reply.settle.settle_time);
            break;
        case SERVO_REQUEST_TYPE_GO_TO_TIMESTAMP:
            out_reply->reply_type = SERVO_REPLY_TYPE_GO_TO_TIMESTAMP;
            result = `$INSTANCE_NAME`_Go_To_Timestamp(
                &request->request.go_to_timestamp.timestamp,
                request->request.go_to_timestamp.mode, 
                request->request.go_to_timestamp.position,
                request->request.go_to_timestamp.velocity_mrpm, 
                request->request.go_to_timestamp.follow_velocity, 
                request->request.go_to_timestamp.follow_accel,
                request->request.go_to_timestamp.interval_sleep_time_ms, 
                &out_reply->reply.go_to_timestamp.pre_position,
                &out_reply->reply.go_to_timestamp.post_position,
                &out_reply->reply.go_to_timestamp.pre_timestamp,
                &out_reply->reply.go_to_timestamp.post_timestamp
            );
            break;
        case SERVO_REQUEST_TYPE_FOLLOW_TIMESTAMP:
            out_reply->reply_type = SERVO_REPLY_TYPE_FOLLOW_TIMESTAMP;
            result = `$INSTANCE_NAME`_Follow_Timestamp(
                &request->request.follow_timestamp.timestamp,
                request->request.follow_timestamp.follow_velocity, 
                request->request.follow_timestamp.follow_accel,
                &out_reply->reply.follow_timestamp.pre_position,
                &out_reply->reply.follow_timestamp.pre_timestamp,
                0
            );
            break;
        default:
            result = M_ERROR_CODE_SERVO_REQUEST_NOT_SUPPORTED;
            break;
    }
    if (result != M_ERROR_CODE_OK)
    {
        out_reply->reply_type = SERVO_REPLY_TYPE_ERROR;
        out_reply->reply.error = result;
    }
    return M_ERROR_CODE_OK;
}

void `$INSTANCE_NAME`_Task(void *context)
{
    (void) context;
    M_Servo_Request_t request;
    M_Servo_Reply_t reply;
    
    while (true)
    {
        if (`$INSTANCE_NAME`_Await_Request(&request) != M_ERROR_CODE_OK)
        {
            continue;
        }
        
        if (`$INSTANCE_NAME`_Handle_Request(&request, &reply) != M_ERROR_CODE_OK)
        {
            // Ignore, Error is propagated in reply
        }
        
        `$INSTANCE_NAME`_Send_Reply(&reply);
    }
}

void `$INSTANCE_NAME`_Boot_Component(M_Error_Code_t (*get_timestamp)(M_Timestamp_t *out), UBaseType_t priority)
{
    `$INSTANCE_NAME`_servo_ref.node_ref = NULL;
    `$INSTANCE_NAME`_servo_ref.limit = 0;
    `$INSTANCE_NAME`_servo_ref.resolution = 262144; // TODO Pull From Servo Controller
    `$INSTANCE_NAME`_servo_ref.request_queue = xQueueCreate(1, sizeof(M_Servo_Request_t));
    `$INSTANCE_NAME`_servo_ref.reply_queue = xQueueCreate(1, sizeof(M_Servo_Reply_t));
    `$INSTANCE_NAME`_servo_ref.get_timestamp = get_timestamp;
    
    xTaskCreate(`$INSTANCE_NAME`_Task, "`$INSTANCE_NAME`", configMINIMAL_STACK_SIZE + 512, NULL, priority, 0);
}

/* [] END OF FILE */

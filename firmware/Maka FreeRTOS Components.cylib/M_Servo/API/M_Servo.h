/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef `$INSTANCE_NAME`_M_SERVO_H
#define `$INSTANCE_NAME`_M_SERVO_H

#include "m_servo_request.h"
#include "m_error_codes.h"

M_Error_Code_t `$INSTANCE_NAME`_Send_Request(M_Servo_Request_t *request, uint16_t timeout_ms);
M_Error_Code_t `$INSTANCE_NAME`_Await_Reply(M_Servo_Reply_t *reply, uint16_t timeout_ms);
void `$INSTANCE_NAME`_Boot_Component(M_Error_Code_t (*get_timestamp)(M_Timestamp_t *out), UBaseType_t priority);

#endif // `$INSTANCE_NAME`_M_SERVO_H
    
/* [] END OF FILE */

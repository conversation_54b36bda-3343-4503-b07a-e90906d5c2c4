/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#include "project.h"
#include "FreeRTOS.h"
#include "task.h"
#include "stdbool.h"
#include "freertos_setup.h"
#include "ethernet_config.h"
#include "`$INSTANCE_NAME`_M_Eth_Bootloader.h"


/* Don't use fault handler - we will blow the bootloader size */
void vAssertCalled(const char* file, int line)
{
    (void) file;
    (void) line;
}

void vApplicationMallocFailedHook()
{
}

void vApplicationStackOverflowHook()
{
}

void CyBtldrCommStart()
{
}

void CyBtldrCommStop()
{
}

uint8_t `$INSTANCE_NAME`_request_ip[4];
uint16_t `$INSTANCE_NAME`_request_port;

cystatus CyBtldrCommRead(uint8* buffer, uint16 size, uint16* count, uint8 timeOut)
{
    (void) timeOut;
    (void) size; // XXX Check this.

    /* Timeout is in 100s of ms (according to code comments in bootloader), but it does this 10 times. */
    `$INSTANCE_NAME`_Eth_msg_t msg = `$INSTANCE_NAME`_Eth_UdpReadTimeout(timeOut * 10);

    if (msg.data == NULL)
        return CYRET_TIMEOUT;
    memcpy(`$INSTANCE_NAME`_request_ip, msg.ip, 4);
    `$INSTANCE_NAME`_request_port = msg.port;
    memcpy(buffer, msg.data, msg.count);
    *count = msg.count;

    `$INSTANCE_NAME`_Eth_Release_Msg_Data(&msg);
    return CYRET_SUCCESS;
}

cystatus CyBtldrCommWrite(uint8 *buffer, uint16 size, uint16 *count, uint8 timeOut)
{
    (void) timeOut;

    `$INSTANCE_NAME`_Eth_msg_t msg;
    memcpy(msg.ip, `$INSTANCE_NAME`_request_ip, 4);
    msg.port = `$INSTANCE_NAME`_request_port;
    msg.data = buffer;
    msg.count = size;
    *count = size; // XXX Check this.

    `$INSTANCE_NAME`_Eth_UdpWrite(msg);

    return CYRET_SUCCESS;
}

void `$INSTANCE_NAME`_Task(void *unused)
{
    (void) unused;
    `$INSTANCE_NAME`_Bootloader_Start();
}

void `$INSTANCE_NAME`_Boot(uint8_t index, uint8_t subindex)
{
    uint8_t mac[] = BOARD_MAC(index, subindex);
    uint8_t ip[] = BOARD_IP(index, subindex);
    uint8_t netmask[] = BOARD_NETMASK;
    uint8_t gateway[] = BOARD_GATEWAY;
    size_t read_queue_len = 10;
    
    `$INSTANCE_NAME`_Eth_Boot(mac, ip, netmask, gateway, read_queue_len);
    `$INSTANCE_NAME`_Eth_UdpOpen(BASE_PORT);
    
    // Schedule Bootloader task.
    xTaskCreate(`$INSTANCE_NAME`_Task, "Bootloader", configMINIMAL_STACK_SIZE + 256, 0, 1, 0);

    // Finish boot sequence.
    vTaskDelete(NULL);
}


/* [] END OF FILE */

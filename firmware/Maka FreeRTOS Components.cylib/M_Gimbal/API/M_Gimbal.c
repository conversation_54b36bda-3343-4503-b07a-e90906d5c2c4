/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#include "FreeRTOS.h"
#include "project.h"
#include "queue.h"
#include "`$INSTANCE_NAME`_M_Gimbal.h"
#include "m_error_codes.h"

#define DEFAULT_SERVO_REQUEST_TIMEOUT 1000
#define DEFAULT_SERVO_REPLY_TIMEOUT 10000

M_Gimbal_Ref_t `$INSTANCE_NAME`_Gimbal_ref;

M_Servo_Request_t `$INSTANCE_NAME`_pan_request;
M_Servo_Request_t `$INSTANCE_NAME`_tilt_request;
M_Servo_Reply_t `$INSTANCE_NAME`_pan_reply;
M_Servo_Reply_t `$INSTANCE_NAME`_tilt_reply;

inline M_Error_Code_t `$INSTANCE_NAME`_Send_Request(M_Gimbal_Request_t *request)
{
    // We drop requests if there isn't enough space
    if (xQueueSendToBack(`$INSTANCE_NAME`_Gimbal_ref.request_queue, request, 0) != pdTRUE)
    {
        return M_ERROR_CODE_GIMBAL_SEND_REQUEST_FAIL;
    }
    return M_ERROR_CODE_OK;
}

inline M_Error_Code_t `$INSTANCE_NAME`_Await_Request(M_Gimbal_Request_t *request, uint32_t timeout_ms)
{
    if (xQueueReceive(`$INSTANCE_NAME`_Gimbal_ref.request_queue, request, pdMS_TO_TICKS(timeout_ms)) != pdTRUE)
    {
        return M_ERROR_CODE_GIMBAL_AWAIT_REQUEST_FAIL;
    }
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_Do_Both_Servos(M_Servo_Request_t *pan, M_Servo_Request_t *tilt, M_Servo_Reply_t *out_pan, M_Servo_Reply_t *out_tilt)
{
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Pan_Send_Request(pan, DEFAULT_SERVO_REQUEST_TIMEOUT))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Tilt_Send_Request(tilt, DEFAULT_SERVO_REQUEST_TIMEOUT))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Pan_Await_Reply(out_pan, DEFAULT_SERVO_REPLY_TIMEOUT))
    RETURN_CODE_IF_NOT_OK(`$INSTANCE_NAME`_Tilt_Await_Reply(out_tilt, DEFAULT_SERVO_REPLY_TIMEOUT))
    return M_ERROR_CODE_OK;
}

M_Error_Code_t `$INSTANCE_NAME`_Boot(M_Gimbal_Boot_Request_t *request)
{
    `$INSTANCE_NAME`_pan_request.request_type = SERVO_REQUEST_TYPE_BOOT;
    `$INSTANCE_NAME`_pan_request.request.boot.params = request->pan_params;
    `$INSTANCE_NAME`_tilt_request.request_type = SERVO_REQUEST_TYPE_BOOT;
    `$INSTANCE_NAME`_tilt_request.request.boot.params = request->tilt_params;
    
    M_Error_Code_t ret = `$INSTANCE_NAME`_Do_Both_Servos(&`$INSTANCE_NAME`_pan_request, &`$INSTANCE_NAME`_tilt_request, &`$INSTANCE_NAME`_pan_reply, &`$INSTANCE_NAME`_tilt_reply);
    
    if (ret != 0)
    {
        return ret;
    }
    
    if (`$INSTANCE_NAME`_pan_reply.reply_type != SERVO_REPLY_TYPE_ACK)
    {
        return `$INSTANCE_NAME`_pan_reply.reply.error;    
    } else if(`$INSTANCE_NAME`_tilt_reply.reply_type != SERVO_REPLY_TYPE_ACK)
    {
        return `$INSTANCE_NAME`_tilt_reply.reply.error;
    }
    
    return M_ERROR_CODE_OK;    
}

M_Error_Code_t `$INSTANCE_NAME`_Stop()
{
    `$INSTANCE_NAME`_pan_request.request_type = SERVO_REQUEST_TYPE_STOP;
    `$INSTANCE_NAME`_tilt_request.request_type = SERVO_REQUEST_TYPE_STOP;
    
    M_Error_Code_t ret = `$INSTANCE_NAME`_Do_Both_Servos(&`$INSTANCE_NAME`_pan_request, &`$INSTANCE_NAME`_tilt_request, &`$INSTANCE_NAME`_pan_reply, &`$INSTANCE_NAME`_tilt_reply);
    
    if (ret != 0)
    {
        return ret;
    }
    
    if (`$INSTANCE_NAME`_pan_reply.reply_type != SERVO_REPLY_TYPE_ACK ||
        `$INSTANCE_NAME`_tilt_reply.reply_type != SERVO_REPLY_TYPE_ACK)
    {
        return M_ERROR_CODE_GIMBAL_SERVO_STOP_FAIL;
    }
    
    return M_ERROR_CODE_OK;    
}

void `$INSTANCE_NAME`_Handle_Request(M_Gimbal_Request_t *request) 
{
    M_Error_Code_t result = M_ERROR_CODE_OK;
    M_Gimbal_Reply_t reply;
    reply.metadata = request->metadata;
    switch(request->request_type)
    {
        case GIMBAL_REQUEST_TYPE_BOOT:
            reply.reply_type = GIMBAL_REPLY_TYPE_ACK;
            result = `$INSTANCE_NAME`_Boot(&request->request.boot);
            break;
        case GIMBAL_REQUEST_TYPE_STOP:
            reply.reply_type = GIMBAL_REPLY_TYPE_ACK;
            result = `$INSTANCE_NAME`_Stop();
            break;
        case GIMBAL_REQUEST_TYPE_SERVOS:
            reply.reply_type = GIMBAL_REPLY_TYPE_SERVOS;
            result = `$INSTANCE_NAME`_Do_Both_Servos(&request->request.servos.pan, &request->request.servos.tilt, &reply.reply.servos.pan, &reply.reply.servos.tilt);
            break;
        default:
            result = M_ERROR_CODE_GIMBAL_NO_REQUEST_HANDLER;
            break;
    }
    
    if (result != M_ERROR_CODE_OK)
    {
        reply.reply_type = GIMBAL_REPLY_TYPE_ERROR;
        reply.reply.error =  result;
    }
    
    `$INSTANCE_NAME`_Gimbal_ref.send_reply_func(&reply);
}

void `$INSTANCE_NAME`_Task(void *context)
{
    (void) context;
    M_Gimbal_Request_t request;
    
    while (true)
    {
        if (`$INSTANCE_NAME`_Await_Request(&request, portMAX_DELAY / portTICK_PERIOD_MS) != M_ERROR_CODE_OK)
        {
            continue;
        }
        
        `$INSTANCE_NAME`_Handle_Request(& request);
    }
}

void `$INSTANCE_NAME`_Boot_Component( M_Error_Code_t (*send_reply_func)(M_Gimbal_Reply_t *), M_Error_Code_t (*get_timestamp)(M_Timestamp_t *out), UBaseType_t priority)
{
    `$INSTANCE_NAME`_Gimbal_ref.request_queue = xQueueCreate(4, sizeof(M_Gimbal_Request_t));
    `$INSTANCE_NAME`_Gimbal_ref.send_reply_func = send_reply_func;
    
    `$INSTANCE_NAME`_Pan_Boot_Component(get_timestamp, priority);
    `$INSTANCE_NAME`_Tilt_Boot_Component(get_timestamp, priority);
    
    xTaskCreate(`$INSTANCE_NAME`_Task, "`$INSTANCE_NAME`", configMINIMAL_STACK_SIZE + 1024, NULL, priority, 0);
}



/* [] END OF FILE */

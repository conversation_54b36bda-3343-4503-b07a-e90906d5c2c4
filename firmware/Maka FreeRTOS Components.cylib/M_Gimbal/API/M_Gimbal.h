/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef `$INSTANCE_NAME`_M_GIMBAL_H
#define `$INSTANCE_NAME`_M_GIMBAL_H

#include "m_gimbal_request.h"
#include "FreeRTOS.h"

M_Error_Code_t `$INSTANCE_NAME`_Send_Request(M_Gimbal_Request_t *request);
void `$INSTANCE_NAME`_Boot_Component( M_Error_Code_t (*send_reply_func)(M_Gimbal_Reply_t *), M_Error_Code_t (*get_timestamp)(M_Timestamp_t *out), UBaseType_t priority);

#endif // `$INSTANCE_NAME`_M_GIMBAL_H

/* [] END OF FILE */

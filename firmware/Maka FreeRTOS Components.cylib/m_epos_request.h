/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef M_EPOS_REQUEST_H
#define M_EPOS_REQUEST_H

#include "m_can_request.h"
#include "m_error_codes.h"
    
// Requests
typedef enum {
    M_EPOS_REQUEST_TYPE_NONE = 0, // None
    M_EPOS_REQUEST_TYPE_CAN_OPEN = 1, // M_EPOS_MCAN_Request_t
    M_EPOS_REQUEST_TYPE_SETUP_PDOS = 2, // None
    M_EPOS_REQUEST_TYPE_ENABLE = 3, // None
    M_EPOS_REQUEST_TYPE_DISABLE = 4, // None
    M_EPOS_REQUEST_TYPE_HOME = 5, // M_EPOS_HomeRequest_t
    M_EPOS_REQUEST_TYPE_AWAIT_SETTLING = 6, // M_EPOS_AwaitSettlingRequest_t
    M_EPOS_REQUEST_TYPE_GET_POS_VEL = 7, // None
    M_EPOS_REQUEST_TYPE_GO_TO = 8, // M_EPOS_GoToRequest_t
    M_EPOS_REQUEST_TYPE_AWAIT_STATUS = 10, // M_EPOS_AwaitStatusRequest_t
    M_EPOS_REQUEST_TYPE_POSITIONAL_PID = 11, // M_Epos_SetPositionalPIDRequest_t
    M_EPOS_REQUEST_TYPE_PID = 12, //M_Epos_SetPIDRequest_t
} M_Epos_RequestType;

typedef enum {
    M_EPOS_REPLY_TYPE_NONE = 0, // None
    M_EPOS_REPLY_TYPE_ACK = 1, // None
    M_EPOS_REPLY_TYPE_CAN_OPEN = 2, // M_EPOS_MCAN_Reply_t
    M_EPOS_REPLY_TYPE_HOMING_LIMIT = 3, // int32_t
    M_EPOS_REPLY_TYPE_SETTLING_TIME = 4, // uint16_t
    M_EPOS_REPLY_TYPE_POS_VEL = 5, // M_EPOS_PositionVelocityReply_t
    M_EPOS_REPLY_TYPE_ERROR = 6, // M_Error_Code_t
} M_Epos_ReplyType;

typedef enum {
    M_EPOS_PARAM_TYPE_NONE = 0,
    M_EPOS_PARAM_TYPE_SWITCH = 1,
    M_EPOS_PARAM_TYPE_HARD = 2,
} M_Epos_Home_Param_Type;

typedef struct {
    int16_t step_size;
    int16_t threshold_step; 
} M_Epos_Switch_Home_Params_t;

typedef struct {
    int16_t step_size;
    uint16_t offset; 
} M_Epos_Hard_Home_Params_t;

typedef struct {
    M_Epos_Home_Param_Type param_type;
    int32_t min_position;
    int32_t max_position; 
    uint32_t profile_velocity;
    union {
        M_Epos_Switch_Home_Params_t limit_switch;
        M_Epos_Hard_Home_Params_t hard_stop;
    } params;
} M_Epos_Home_Params_t;

typedef struct {
    M_Epos_Home_Params_t params;
} M_Epos_HomeRequest_t;

typedef struct {
    int32_t target_position;
    uint16_t window;
    uint16_t timeout_ms;
} M_Epos_AwaitSettlingRequest_t;

typedef struct {
    int32_t position;
    uint32_t velocity;
    uint16_t window;
    uint16_t timeout_ms; // 0 disables settling await
} M_Epos_GoToRequest_t;

typedef struct {
    uint16_t timeout_ms;
    uint16_t expected;
    uint16_t expected_neg;
} M_Epos_AwaitStatusRequest_t;

typedef struct {
    uint32_t gain_p;
    uint32_t gain_i;
    uint32_t gain_d;
    uint32_t gain_ffv;
    uint32_t gain_ffa;
} M_Epos_SetPositionalPIDRequest_t;
typedef struct {
    uint32_t current_p;
    uint32_t current_i;
    M_Epos_SetPositionalPIDRequest_t pos_pid;
} M_Epos_SetPIDRequest_t;
typedef struct {
    M_Epos_RequestType request_type;
    union {
        M_CAN_Request_t can;
        M_Epos_HomeRequest_t home;
        M_Epos_AwaitSettlingRequest_t settle;
        M_Epos_GoToRequest_t go_to;
        M_Epos_AwaitStatusRequest_t status;
        M_Epos_SetPositionalPIDRequest_t positional_pid;
        M_Epos_SetPIDRequest_t pid;
    } request;
} M_Epos_Request_t;

typedef struct {
    int32_t position;
    int32_t velocity;
} M_Epos_PositionVelocityReply_t;

typedef struct {
    M_Epos_ReplyType reply_type;
    union {
        M_CAN_Reply_t can;
        int32_t limit;
        uint16_t duration;
        M_Epos_PositionVelocityReply_t pos_vel;
        M_Error_Code_t error;
    } reply;
} M_Epos_Reply_t;

#endif // M_EPOS_REQUEST_H

/* [] END OF FILE */

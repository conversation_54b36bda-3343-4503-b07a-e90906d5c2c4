/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef ETHERNET_CONFIG_H
#define ETHERNET_CONFIG_H
    
#define BASE_PORT 4243
#define FASTBIN_PORT 4244
    
#define PULCZAR_IP_INDEX 4
#define PULCZAR_IP_SUB_INDEX_BASE 1
#define PULCZAR_BOOTLOADER_IP_SUB_INDEX_BASE 11    
    
#define STROBE_IP_INDEX 6
#define STROBE_IP_SUB_INDEX 1
#define STROBE_BOOTLOADER_IP_SUB_INDEX 11
    
#define NOFX_IP_INDEX 7
#define NOFX_IP_SUB_INDEX 1
#define NOFX_BOOTLOADER_IP_SUB_INDEX 11

#define SUBINDEX_OFFSET(base, offset) (base + offset)
#define BOARD_IP(index, subindex) {10, 11, index, subindex}
#define BOARD_MAC(index, subindex) {0x06, 0x42, index, subindex, 0xbe, 0xef}
#define BOARD_NETMASK {255, 255, 0, 0}
#define BOARD_GATEWAY {10, 11, 1, 1}

#endif // ETHERNET_CONFIG_H

/* [] END OF FILE */

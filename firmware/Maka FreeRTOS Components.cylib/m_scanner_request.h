/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#ifndef M_SCANNER_REQUEST_H
#define M_SCANNER_REQUEST_H

#include "m_servo_request.h"

typedef enum {
    SCANNER_REQUEST_TYPE_NONE = 0, // None
    SCANNER_REQUEST_TYPE_LASER = 1, // M_Scanner_Laser_State_t
    SCANNER_REQUEST_TYPE_GET_LASER = 2, // None
    SCANNER_REQUEST_TYPE_BOOT = 3, // M_Scanner_Boot_Request_t
    SCANNER_REQUEST_TYPE_STOP = 4, // None
    SCANNER_REQUEST_TYPE_GIMBAL = 5, // M_Scanner_Gimbal_Request_t
    SCANNER_REQUEST_TYPE_INTENSITY = 6, // M_Scanner_Intensity_Request_t
} M_Scanner_RequestType;

typedef enum {
    SCANNER_REPLY_TYPE_NONE = 0, // None
    SCANNER_REPLY_TYPE_ERROR = 1, // None
    SCANNER_REPLY_TYPE_ACK = 2, // None
    SCANNER_REPLY_TYPE_LASER_STATE = 3, // M_Scanner_Laser_State_t
    SCANNER_REPLY_TYPE_GIMBAL = 4, // M_Scanner_Gimbal_Reply_t
} M_Scanner_ReplyType;

typedef struct {
    bool on;
} M_Scanner_Laser_State_t;

typedef struct {
    uint16_t intensity;
} M_Scanner_Intensity_Request_t;

typedef struct {
    M_Servo_Request_t pan;
    M_Servo_Request_t tilt;
} M_Scanner_Gimbal_Request_t;

typedef struct {
    M_Epos_Home_Params_t pan_params;
    M_Epos_Home_Params_t tilt_params;
} M_Scanner_Boot_Request_t;

typedef struct {
    uint16_t request_id;
    M_Scanner_RequestType request_type;
    union {
        M_Scanner_Boot_Request_t boot;
        M_Scanner_Laser_State_t laser;
        M_Scanner_Gimbal_Request_t gimbal;
        M_Scanner_Intensity_Request_t intensity;
    } request;
} M_Scanner_Request_t;

typedef struct {
    M_Servo_Reply_t pan;
    M_Servo_Reply_t tilt;
} M_Scanner_Gimbal_Reply_t;

typedef struct {
    uint16_t request_id;
    M_Scanner_ReplyType reply_type;
    union {
        M_Scanner_Laser_State_t laser;
        M_Scanner_Gimbal_Reply_t gimbal;
    } reply;
} M_Scanner_Reply_t;

typedef struct {
    QueueHandle_t request_queue;
    void (*send_reply_func)(M_Scanner_Reply_t *);
} M_Scanner_Ref_t;

#endif // M_SCANNER_REQUEST_H

/* [] END OF FILE */

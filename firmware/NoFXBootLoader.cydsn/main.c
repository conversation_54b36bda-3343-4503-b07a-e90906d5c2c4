/* ========================================
 *
 * Copyright Maka Autonomous Robotic Systems, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */
#include "FreeRTOS.h"
#include "ethernet_config.h"
#include "freertos_setup.h"
#include "project.h"
#include "stdbool.h"
#include "task.h"

void Boot_Task(void *unused) {
  (void)unused;

  // Configure Ethernet.
  uint8_t index = NOFX_IP_INDEX;
  uint8_t subindex = NOFX_BOOTLOADER_IP_SUB_INDEX;

  Bootloader_Boot(index, subindex);

  // Finish boot sequence.
  vTaskDelete(NULL);
}

int main(void) {
  // Enable global interrupts.
  CyGlobalIntEnable;

  // Install FreeRTOS interrupts.
  setup_FreeRTOS_interrupts();

  // Schedule boot sequence.
  xTaskCreate(Boot_Task, "Boot", configMINIMAL_STACK_SIZE, NULL, 1, NULL);

  // Start the scheduler.
  vTaskStartScheduler();
}

/* [] END OF FILE */

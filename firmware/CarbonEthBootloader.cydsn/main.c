/* ========================================
 *
 * Copyright Maka Autonomous Robotic Systems, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */
#include "FreeRTOS.h"
#include "freertos_setup.h"
#include "project.h"
#include "stdbool.h"
#include "task.h"

/* Don't use fault handler - we will blow the bootloader size */
void vAssertCalled(const char *file, int line) {
  (void)file;
  (void)line;
}

void vApplicationMallocFailedHook() {}

void vApplicationStackOverflowHook() {}

void CyBtldrCommStart() {}

void CyBtldrCommStop() {}

uint8_t request_ip[4];
uint16_t request_port;

cystatus CyBtldrCommRead(uint8 *buffer, uint16 size, uint16 *count, uint8 timeOut) {
  (void)timeOut;
  (void)size; // XXX Check this.

  /* Timeout is in 100s of ms (according to code comments in bootloader), but it does this 10 times. */
  M_Ethernet_W5500_msg_t msg = M_Ethernet_W5500_UdpReadTimeout(timeOut * 10);

  if (msg.data == NULL)
    return CYRET_TIMEOUT;
  memcpy(request_ip, msg.ip, 4);
  request_port = msg.port;
  memcpy(buffer, msg.data, msg.count);
  *count = msg.count;

  M_Ethernet_W5500_Release_Msg_Data(&msg);
  return CYRET_SUCCESS;
}

cystatus CyBtldrCommWrite(uint8 *buffer, uint16 size, uint16 *count, uint8 timeOut) {
  (void)timeOut;

  M_Ethernet_W5500_msg_t msg;
  memcpy(msg.ip, request_ip, 4);
  msg.port = request_port;
  msg.data = buffer;
  msg.count = size;
  *count = size; // XXX Check this.

  M_Ethernet_W5500_UdpWrite(msg);

  return CYRET_SUCCESS;
}

void Bootloader_Task(void *unused) {
  (void)unused;
  Bootloader_Start();
}

#define PORT 14245
#define NoFX_LAST_QUAD 22
#define Czar_LAST_QUAD 121

void Boot_Task(void *unused) {
  (void)unused;
  uint8_t last_quad = NoFX_LAST_QUAD;
  int control = 0;
  int addr_mod = 0;

  for (; control < 2; control++) {
    WizMuxControl_Write(control);
    M_Ethernet_W5500_Reset();

    if (M_Ethernet_W5500_VerifyVersion())
      break;
  }

  if (control == 0) {
    addr_mod = 1 << 3;
  } else {
    addr_mod = ((ADDR2_Read() << 2) | (ADDR1_Read() << 1) | (ADDR0_Read()));
    /* Wiznet NoFX Configuration not detected, set up last quad for Czar */
    last_quad = Czar_LAST_QUAD + addr_mod;
  }

  // Configure Ethernet.
  uint8_t mac[] = {0x06, 0x42, addr_mod, 0x02, 0xbe, 0xef};
  uint8_t ip[] = {10, 11, 3, last_quad};
  uint8_t netmask[] = {255, 255, 0, 0};
  uint8_t gateway[] = {10, 11, 1, 1};
  size_t read_queue_len = 10;

  M_Ethernet_W5500_Boot(mac, ip, netmask, gateway, read_queue_len);
  M_Ethernet_W5500_UdpOpen(PORT);

  // Schedule Bootloader task.
  xTaskCreate(Bootloader_Task, "Bootloader", configMINIMAL_STACK_SIZE + 256, 0, 1, 0);

  // Finish boot sequence.
  vTaskDelete(NULL);
}

int main(void) {
  // Enable global interrupts.
  CyGlobalIntEnable;

  // Install FreeRTOS interrupts.
  setup_FreeRTOS_interrupts();

  // Schedule boot sequence.
  xTaskCreate(Boot_Task, "Boot", configMINIMAL_STACK_SIZE, NULL, 1, NULL);

  // Start the scheduler.
  vTaskStartScheduler();
}

/* [] END OF FILE */

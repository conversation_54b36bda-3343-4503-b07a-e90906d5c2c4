/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#ifndef CYAPICALLBACKS_H
#define CYAPICALLBACKS_H
#include "stdint.h"
#include "stdlib.h"


    /*Define your macro callbacks here */
    /*For more information, refer to the Writing Code topic in the PSoC Creator Help.*/

#define M_ProtoUSB_RECEIVE_CALLBACK
void M_ProtoUSB_Receive_Callback(uint8_t* pkt, size_t size);
#define Watchdog_INTERRUPT_INTERRUPT_CALLBACK
void Watchdog_Interrupt_InterruptCallback();
#define Watchdog_Interrupt_Inte
#endif /* CYAPICALLBACKS_H */   

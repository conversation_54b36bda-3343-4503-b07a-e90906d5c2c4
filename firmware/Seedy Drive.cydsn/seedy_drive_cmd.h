// Generated Code. Do not modify.
#include "project.h"
#include "leb128.h"
#include "stop.h"
#ifndef MAKA_seedy_drive_cmd_H
#define MAKA_seedy_drive_cmd_H
#pragma pack(1)
    typedef enum {
        SEEDY_DRIVE_CMD_AS_DISARM = 1,
        SEEDY_DRIVE_CMD_AS_ARM = 2,
        SEEDY_DRIVE_CMD_AS_SET_INTENSITY = 3,
        SEEDY_DRIVE_CMD_AS_SET_DRIVE = 4
} _tag_seedy_drive_cmd;
typedef struct {
        uint8_t scanner_no;
        uint16_t intensity;
} set_intensity;
typedef struct {
        uint8_t motor_no;
        int16_t velocity;
} set_drive;
typedef struct {
        _tag_seedy_drive_cmd tag;
        union {
                set_intensity as_set_intensity;
                set_drive as_set_drive;
        } val;
} seedy_drive_cmd;
void seedy_drive_cmd_as_disarm(void* ctx);
void seedy_drive_cmd_as_arm(void* ctx);
void seedy_drive_cmd_as_set_intensity(set_intensity* val, void* ctx);
void seedy_drive_cmd_as_set_drive(set_drive* val, void* ctx);

inline void demux_seedy_drive_cmd(seedy_drive_cmd *tu, void* ctx) {
    switch(tu->tag) {
        case SEEDY_DRIVE_CMD_AS_DISARM:
                seedy_drive_cmd_as_disarm(ctx);
                break;
        case SEEDY_DRIVE_CMD_AS_ARM:
                seedy_drive_cmd_as_arm(ctx);
                break;
        case SEEDY_DRIVE_CMD_AS_SET_INTENSITY:
                seedy_drive_cmd_as_set_intensity(&(tu->val.as_set_intensity), ctx);
                break;
        case SEEDY_DRIVE_CMD_AS_SET_DRIVE:
                seedy_drive_cmd_as_set_drive(&(tu->val.as_set_drive), ctx);
                break;
        default:
            stop("Unknown tag for seedy_drive_cmd");
    }
}

#pragma pack()
#endif // MAKA_seedy_drive_cmd_H
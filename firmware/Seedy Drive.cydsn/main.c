/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#include "project.h"
#include "seedy_drive_cmd.h"
#include "stdarg.h"
#include "stdio.h"
#include "stdlib.h"

#define NUM_LASERS 2
#define NUM_MOTORS 2

void vdebug(const char* fmt, va_list ap) {
    static char errbuf[256];
    vsnprintf(errbuf, 100, fmt, ap);
    UART_PutString(errbuf);
    UART_PutString("\r\n");
}

void debug(const char *fmt, ...) {
    va_list ap;
    va_start(ap, fmt);
    vdebug(fmt, ap);
}

void stop(const char *fmt, ...) {
    Fault_LED_Write(1);
    Arm_Write(0);
    Left_Motor_Forward_Write(0);
    Left_Motor_Reverse_Write(0);
    Right_Motor_Forward_Write(0);
    Right_Motor_Reverse_Write(0);
    M_ProtoUSB_Stop();
    Watchdog_Stop();    
    va_list ap;
    va_start(ap, fmt);
    vdebug(fmt, ap);
    CyDelay(2000);
    CySoftwareReset();
}

void Watchdog_Interrupt_InterruptCallback() {
    Watchdog_ClearPending();
    stop("Insufficient pets");
}

static int16_t vel_setpoints[NUM_MOTORS];
static int16_t vel_actual[NUM_MOTORS];
static uint16_t int_setpoints[NUM_LASERS];
static uint16_t int_actual[NUM_LASERS];

void seedy_drive_cmd_as_disarm(void* ctx) {
    Arm_Write(0);
    Pet_Watchdog_Write(1); // 🐕 good boy!
}

void seedy_drive_cmd_as_arm(void* ctx) {
    Arm_Write(1);
    Pet_Watchdog_Write(1); // 🐕 good boy!
}

void seedy_drive_cmd_as_set_intensity(set_intensity* val, void* ctx) {
    uint8_t scanner_no = val->scanner_no;
    if (scanner_no >= NUM_LASERS) {
        stop("bad laser %d", scanner_no);
    }
    int_setpoints[scanner_no] = val->intensity;
    Pet_Watchdog_Write(1); // 🐕 good boy!
}

void seedy_drive_cmd_as_set_drive(set_drive* val, void* ctx) {
    uint8_t motor_no = val->motor_no;
    if (motor_no >= NUM_MOTORS) {
        stop("bad motor %d", motor_no);
    }
    vel_setpoints[motor_no] = val->velocity;
    Pet_Watchdog_Write(1); // 🐕 good boy!
}
static size_t packet_no;
void M_ProtoUSB_Receive_Callback(uint8_t* pkt, size_t size) {
    // size isn't used here cause packet structure determines length
    debug("packet (%d %d):", packet_no++, size);
    for (int i = 0; i < size; i++) {
        debug("%02x ", pkt[i]);
    }
    demux_seedy_drive_cmd((seedy_drive_cmd*)pkt, NULL);
}

void Laser_Loop() {
    for(uint8_t laser_no = 0; laser_no < NUM_LASERS; laser_no++) {
        uint16_t sp = int_setpoints[laser_no];
        if (sp != int_actual[laser_no]) {
            M_DAC6573_SetChannelValue(laser_no, sp);
            if (sp != 0) {
                Fire_Write(Fire_Read() | 1 << laser_no);
            } else {
                Fire_Write(Fire_Read() & ~(1 << laser_no));
            }
            int_actual[laser_no] = sp;
        }
    }
}
static char debug_strbuf[256];
// Left is 0
void Motor_Loop() {
    for(uint8_t motor_no = 0; motor_no < NUM_MOTORS; motor_no++) {
        int16_t sp = vel_setpoints[motor_no];
        uint16_t usp = abs(sp);
        usp <<= 1;
        if (sp != vel_actual[motor_no]) {
            snprintf(debug_strbuf, 256, "SP %05d %05d, actual %05d %05d\r\n", vel_setpoints[0], vel_setpoints[1], vel_actual[0], vel_actual[1]);
            UART_PutString(debug_strbuf);
            M_DAC6573_SetChannelValue(NUM_LASERS + (motor_no == 0 ? 1 : 0), usp); // lasers are the lower channels, motors are signed
            if (sp > 0) {
                if (motor_no == 0) {
                    Left_Motor_Forward_Write(1);
                    Left_Motor_Reverse_Write(0);
                } else if (motor_no == 1) {
                    Right_Motor_Forward_Write(1);
                    Right_Motor_Reverse_Write(0);
                }
            } else if (sp < 0) {                
                if (motor_no == 0) {
                    Left_Motor_Forward_Write(0);
                    Left_Motor_Reverse_Write(1);
                } else if (motor_no == 1) {
                    Right_Motor_Forward_Write(0);
                    Right_Motor_Reverse_Write(1);
                }
            } else {
                if (motor_no == 0) {
                    Left_Motor_Forward_Write(0);
                    Left_Motor_Reverse_Write(0);
                } else if (motor_no == 1) {
                    Right_Motor_Forward_Write(0);
                    Right_Motor_Reverse_Write(0);
                }
            }
            vel_actual[motor_no] = sp;
        }
    }
}

// If nothing is moving or on, we will pet the watchdog for you.
// This is meant as a stopgap until we can make a protocol/API that
// does it continuously and can re-establish connections better.
void Autopet_Loop() {
    bool auto_pet = true;
    for(uint8_t motor_no = 0; motor_no < NUM_MOTORS; motor_no++) {
        if (vel_setpoints[motor_no] || vel_actual[motor_no]) {
            auto_pet = false;
        }
    }
    for(uint8_t laser_no = 0; laser_no < NUM_MOTORS; laser_no++) {
        if ((int_setpoints[laser_no] || int_actual[laser_no]) && Arm_Read()) {
            auto_pet = false;
        }
    }
    if (auto_pet) {
        Pet_Watchdog_Write(1); // 🐕 good boy!
    }
}

int main(void) {
    CyGlobalIntEnable;
    M_ProtoUSB_Start();
    M_DAC6573_Start();
    Watchdog_Timer_Start();
    Watchdog_Start();
    UART_Start();
    // Give some indication that we're doing something.
    for (int i = 0; i < 5; i++) {
        Fault_LED_Write(1);
        CyDelay(200);
        Fault_LED_Write(0);
        CyDelay(200);
    }
    for(;;) {
        M_ProtoUSB_Loop();
        Laser_Loop();
        Motor_Loop();
        Autopet_Loop();
    }
}

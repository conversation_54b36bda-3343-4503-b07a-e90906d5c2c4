/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
 */

#ifndef GPS_H
#define GPS_H 1

#include "stdbool.h"
#include "stdint.h"

typedef struct {
  int day_millis;
  uint64_t timestamp_ms;
} gps_time_t;

typedef struct {
  bool have_fix;
  double latitude;
  double longitude;
  int32_t num_sats;
  float hdop;
  uint64_t timestamp_ms;
} gps_position_t;

void GPS_Boot(uint8_t gps_task_priority);
gps_position_t GPS_Get_Position();

#endif

/* [] END OF FILE */

/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "FreeRTOS.h"
#include "project.h"
#include "stdio.h"
#include "stream_buffer.h"
#include "task.h"
#include "time.h"

#include "gps.h"

#define GPS_I2C_ADDRESS 0x3A

gps_time_t latest_time;
gps_position_t position;
StreamBufferHandle_t uart_buffer;

CY_ISR(GPS_UART_RX_ISR_Impl) {
  BaseType_t pxHigherPriorityTaskWoken = pdFALSE;
  for (;;) {
    uint8_t rx_status = GPS_UART_ReadRxStatus();
    configASSERT(!(rx_status & GPS_UART_RX_STS_OVERRUN));
    if (!(rx_status & GPS_UART_RX_STS_FIFO_NOTEMPTY)) {
      // Nothing left to read.
      break;
    }
    uint8_t data = GPS_UART_ReadRxData();
    uint8_t sent = xStreamBufferSendFromISR(uart_buffer, &data, 1, &pxHigherPriorityTaskWoken);
    configASSERT(sent);
  }
  portEND_SWITCHING_ISR(pxHigherPriorityTaskWoken);
}

// GPGGA,065447.800,4732.13092,N,12209.53912,W,1,11,0.9,170.78,M,-17.3,M,,
// GPGGA,<Timestamp>,<Lat>,<N/S>,<Long>,<E/W>,<GPSQual>,<Sats>,<HDOP>,<Alt>,<AltVal>,<GeoSep>,<GeoVal>,<DGPSAge>,<DGPSRef>
void GPS_Handle_GPGGA(char *buf) {
  int hh, mm, ss, ms;
  int lat_deg_min, lat_frac;
  char n_s;
  int long_deg_min, long_frac;
  char e_w;
  int qual;
  int sats;
  int hdop_int, hdop_frac;
  int successful =
      sscanf(buf, "GPGGA,%2d%2d%2d.%3d,%d.%5d,%c,%d.%5d,%c,%d,%d,%d.%d,%*s", &hh, &mm, &ss, &ms, &lat_deg_min,
             &lat_frac, &n_s, &long_deg_min, &long_frac, &e_w, &qual, &sats, &hdop_int, &hdop_frac);
  configASSERT(successful == 14);

  int day_sec = hh * 3600 + mm * 60 + ss;
  int day_millis = day_sec * 1000 + ms;

  int lat_deg = lat_deg_min / 100;
  int lat_min = lat_deg_min % 100;
  double lat = (n_s == 'N' ? 1 : -1) * (lat_deg + ((lat_min + (lat_frac / 100000.0)) / 60.0));

  int long_deg = long_deg_min / 100;
  int long_min = long_deg_min % 100;
  double long_ = (e_w == 'E' ? 1 : -1) * (long_deg + ((long_min + (long_frac / 100000.0)) / 60.0));

  float hdop = hdop_int + (hdop_frac / 10.0f);

  taskENTER_CRITICAL();
  gps_time_t latest_time_copy = latest_time;
  taskEXIT_CRITICAL();

  uint64_t timestamp_ms = latest_time_copy.timestamp_ms + day_millis - latest_time_copy.day_millis;

  taskENTER_CRITICAL();
  position.have_fix = (qual == 1 || qual == 2);
  position.latitude = lat;
  position.longitude = long_;
  position.num_sats = sats;
  position.hdop = hdop;
  position.timestamp_ms = timestamp_ms;
  taskEXIT_CRITICAL();
}

// GPZDA,065447.800,20,10,2020,00,00
// GPZDA,<Timestamp>,<Day>,<Month>,<Year>,00,00
void GPS_Handle_GPZDA(char *buf) {
  int hh, mm, ss, ms;
  int day;
  int month;
  int year;
  int successful = sscanf(buf, "GPZDA,%2d%2d%2d.%3d,%d,%d,%d,%*s", &hh, &mm, &ss, &ms, &day, &month, &year);
  configASSERT(successful == 7);

  int day_sec = hh * 3600 + mm * 60 + ss;
  int day_millis = day_sec * 1000 + ms;

  struct tm t;
  t.tm_year = year - 1900;
  t.tm_mon = month - 1;
  t.tm_mday = day;
  t.tm_hour = hh;
  t.tm_min = mm;
  t.tm_sec = ss;
  t.tm_isdst = 0;
  time_t timestamp_s = mktime(&t);

  uint64_t timestamp_ms = (uint64_t)timestamp_s * 1000 + ms;

  taskENTER_CRITICAL();
  latest_time.day_millis = day_millis;
  latest_time.timestamp_ms = timestamp_ms;
  taskEXIT_CRITICAL();
}

void GPS_UART_Reader_Task(void *unused) {
  (void)unused;
  static char buf[256];

  for (;;) {
    char leader;
    xStreamBufferReceive(uart_buffer, &leader, 1, portMAX_DELAY);
    if (leader != '$') {
      // Wait for $ to indicate a new message.
      continue;
    }

    uint8_t checksum = 0;
    for (uint8_t idx = 0;; idx++) {
      xStreamBufferReceive(uart_buffer, buf + idx, 1, portMAX_DELAY);
      if (buf[idx] == '*') {
        // Message is terminated.
        buf[idx] = '\0';
        break;
      }
      checksum ^= buf[idx];
    }

    char checksum_provided_str[3];
    unsigned int checksum_provided;
    xStreamBufferReceive(uart_buffer, checksum_provided_str, 1, portMAX_DELAY);
    xStreamBufferReceive(uart_buffer, checksum_provided_str + 1, 1, portMAX_DELAY);
    checksum_provided_str[2] = '\0';
    sscanf(checksum_provided_str, "%x", &checksum_provided);
    if (checksum != checksum_provided) {
      // Corrupted message.
      continue;
    }

    // Dispatch message.
    if (!strncmp(buf, "GPGGA", 5)) {
      GPS_Handle_GPGGA(buf);
    } else if (!strncmp(buf, "GPZDA", 5)) {
      GPS_Handle_GPZDA(buf);
    }
  }
}

void GPS_I2C_Send(const char *message) {
  // I2C is quite unreliable, so we pepper it up with a lot of sleeps.
  while (GPS_I2C_MasterClearStatus() == GPS_I2C_MSTR_NOT_READY) {
    vTaskDelay(pdMS_TO_TICKS(1));
  }
  configASSERT(GPS_I2C_MasterSendStart(GPS_I2C_ADDRESS, 0) == GPS_I2C_MSTR_NO_ERROR);
  vTaskDelay(pdMS_TO_TICKS(1));
  for (unsigned int i = 0; i < strlen(message); i++) {
    configASSERT(GPS_I2C_MasterWriteByte(message[i]) == GPS_I2C_MSTR_NO_ERROR);
    vTaskDelay(pdMS_TO_TICKS(1));
  }
  configASSERT(GPS_I2C_MasterSendStop() == GPS_I2C_MSTR_NO_ERROR);
  vTaskDelay(pdMS_TO_TICKS(1));
}

void GPS_Boot(uint8_t gps_task_priority) {
  GPS_I2C_Start();

  // Restart GPS.
  GPS_RESETn_Write(0);
  vTaskDelay(pdMS_TO_TICKS(500));
  GPS_RESETn_Write(1);
  vTaskDelay(pdMS_TO_TICKS(10000));

  // Reset to factory settings.
  GPS_I2C_Send("$PSTMRESTOREPAR\n\r"
               "$PSTMSRR\n\r");
  vTaskDelay(pdMS_TO_TICKS(10000));

  // Configure GPS over I2C.
  GPS_I2C_Send("$PSTMSETPAR,1102,10\n\r"   // set baud rate to 115200
               "$PSTMSETPAR,1301,0.01\n\r" // PPS duration 10ms
               "$PSTMSETPAR,1130,0x00\n\r" // set highest CPU frequency
               "$PSTMSETPAR,1303,0.1\n\r"  // acquire fix every 100ms
               // UART messages:
               // 0000 0000 0000 0002 - GPGGA, GPS position
               // 0000 0000 0100 0000 - GPZDA, UTC date and time
               "$PSTMSETPAR,1201,0x01000002\n\r" // UART message list (low)
               "$PSTMSETPAR,1228,0x0\n\r"        // UART message list (high)
               "$PSTMSETCONSTMASK,11\n\r"        // GPS+GLONASS+Galileo
               "$PSTMSAVEPAR\n\r"
               "$PSTMSRR\n\r");

  // Enable UART.
  uart_buffer = xStreamBufferCreate(1024, 1);
  xTaskCreate(GPS_UART_Reader_Task, "GPS UART", configMINIMAL_STACK_SIZE + 256, 0, gps_task_priority, 0);
  GPS_UART_RX_ISR_ClearPending();
  GPS_UART_RX_ISR_StartEx(GPS_UART_RX_ISR_Impl);
  GPS_UART_Start();

  // Wait for the first message.
  vTaskDelay(pdMS_TO_TICKS(10000));
  configASSERT(latest_time.day_millis != 0);
}

gps_position_t GPS_Get_Position() {
  taskENTER_CRITICAL();
  gps_position_t position_copy = position;
  taskEXIT_CRITICAL();
  return position_copy;
}

/* [] END OF FILE */

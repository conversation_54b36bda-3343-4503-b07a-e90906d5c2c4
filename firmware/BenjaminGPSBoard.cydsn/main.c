/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
 */
#include "FreeRTOS.h"
#include "freertos_setup.h"
#include "pb_decode.h"
#include "pb_encode.h"
#include "project.h"
#include "stdbool.h"
#include "task.h"

#include "generated/lib/drivers/nanopb/proto/benjamin_gps_board.pb.h"
#include "generated/lib/drivers/nanopb/proto/gps.pb.h"
#include "gps.h"

void Handle_Ping(diagnostic_Ping *req, diagnostic_Pong *resp) { resp->x = req->x; }

void Handle_GPS_Position(gps_Position_Request *unused, gps_Position_Reply *resp) {
  (void)unused;

  gps_position_t position = GPS_Get_Position();
  resp->have_fix = position.have_fix;
  resp->latitude = position.latitude;
  resp->longitude = position.longitude;
  resp->num_sats = position.num_sats;
  resp->hdop = position.hdop;
  resp->timestamp_ms = position.timestamp_ms;
}

void Handle_GPS(gps_Request *req, gps_Reply *resp) {
  switch (req->which_request) {
  case gps_Request_position_tag:
    resp->which_reply = gps_Reply_position_tag;
    Handle_GPS_Position(&req->request.position, &resp->reply.position);
    break;
  default:
    // Unknown request.
    return;
  }
}

void UDP_Task(void *unused) {
  (void)unused;

  for (;;) {
    benjamin_gps_board_Request req = benjamin_gps_board_Request_init_zero;
    M_Ethernet_W5500_msg_t msg = M_Ethernet_W5500_UdpRead();

    // Decode request.
    {
      pb_istream_t stream = pb_istream_from_buffer(msg.data, msg.count);
      bool status = pb_decode(&stream, benjamin_gps_board_Request_fields, &req);
      M_Ethernet_W5500_Release_Msg_Data(&msg);
      if (!status) {
        // Decoding failed.
        continue;
      }
    }

    // Process request.
    benjamin_gps_board_Reply resp = benjamin_gps_board_Reply_init_zero;
    resp.has_header = true;
    resp.header.requestId = req.header.requestId;
    switch (req.which_request) {
    case benjamin_gps_board_Request_ping_tag:
      resp.which_reply = benjamin_gps_board_Reply_pong_tag;
      Handle_Ping(&req.request.ping, &resp.reply.pong);
      break;
    case benjamin_gps_board_Request_gps_tag:
      resp.which_reply = benjamin_gps_board_Reply_gps_tag;
      Handle_GPS(&req.request.gps, &resp.reply.gps);
      break;
    default:
      // Unknown request.
      continue;
    }

    // Encode response.
    uint8_t buffer[64];
    {
      pb_ostream_t stream = pb_ostream_from_buffer(buffer, sizeof(buffer));
      bool status = pb_encode(&stream, benjamin_gps_board_Reply_fields, &resp);
      configASSERT(status); // encoding should not fail
      msg.data = buffer;
      msg.count = stream.bytes_written;
    }
    M_Ethernet_W5500_UdpWrite(msg);
  }
}

void Boot_Task(void *unused) {
  (void)unused;

  // Configure GPS.
  uint8_t gps_task_priority = 3;
  GPS_Boot(gps_task_priority);

  // Configure Ethernet.
  uint8_t mac[] = {0x06, 0x00, 0x00, 0x01, 0xbe, 0xef};
  uint8_t ip[] = {10, 11, 3, 21};
  uint8_t netmask[] = {255, 255, 0, 0};
  uint8_t gateway[] = {10, 11, 1, 1};
  size_t read_queue_len = 10;
  M_Ethernet_W5500_Boot(mac, ip, netmask, gateway, read_queue_len);
  M_Ethernet_W5500_UdpOpen(4244);

  // Schedule UDP task.
  xTaskCreate(UDP_Task, "UDP", configMINIMAL_STACK_SIZE + 192, 0, 2, 0);

  // Finish boot sequence.
  vTaskDelete(NULL);
}

int main(void) {
  // Enable global interrupts.
  CyGlobalIntEnable;

  // Install FreeRTOS interrupts.
  setup_FreeRTOS_interrupts();

  // Schedule boot sequence.
  xTaskCreate(Boot_Task, "Boot", configMINIMAL_STACK_SIZE + 64, 0, 1, 0);

  // Start the scheduler.
  vTaskStartScheduler();
}

/* [] END OF FILE */

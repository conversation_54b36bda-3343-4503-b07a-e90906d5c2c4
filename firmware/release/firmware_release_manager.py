import os
from typing import Dict, List, Optional

_CONFIG_DIR = os.path.realpath(os.path.join(os.getcwd(), os.path.dirname(__file__), "boards"))

_BOARDS_RELEASE_MAPPING: Dict[str, str] = {}
for board in os.listdir(_CONFIG_DIR):
    board_path = os.path.join(_CONFIG_DIR, board)
    if not os.path.isdir(board_path):
        continue
    _BOARDS_RELEASE_MAPPING[board] = board_path


def get_boards_dir() -> str:
    return _CONFIG_DIR


class FirmwareVersion:
    def __init__(self, major: int, minor: int, rev: int) -> None:
        self.major = major
        self.minor = minor
        self.rev = rev

    def is_more_recent(self, other: "FirmwareVersion") -> bool:
        return (
            self.major > other.major
            or (self.major == other.major and self.minor > other.minor)
            or (self.major == other.major and self.minor == other.minor and self.rev > other.rev)
        )

    def __lt__(self, other: "FirmwareVersion") -> bool:
        return other.is_more_recent(self)

    def is_equal(self, other: "FirmwareVersion") -> bool:
        return self.major == other.major and self.minor == other.minor and self.rev == other.rev

    def has_major_difference(self, other: "FirmwareVersion") -> bool:
        return self.major != other.major

    def has_only_rev_difference(self, other: "FirmwareVersion") -> bool:
        return self.major == other.major and self.minor == other.minor and self.rev != other.rev

    def bump(self, level: str) -> None:
        if level == "major":
            self.major += 1
            self.minor = 0
            self.rev = 0
        elif level == "minor":
            self.minor += 1
            self.rev = 0
        elif level == "rev":
            self.rev += 1

    def __repr__(self) -> str:
        return f"V{self.major}_{self.minor}_{self.rev}"


class Firmware:
    """
    Format is: BoardName.V<Major>_<Minor>.cyacd or BoardName.V<Major>_<Minor>_<Rev>.bin
    """

    def __init__(self, path: str, version: FirmwareVersion) -> None:
        self.path = path
        self.version = version

    @staticmethod
    def create(path: str) -> Optional["Firmware"]:
        name = os.path.basename(path)
        parts = name.split(".")
        if len(parts) < 3 or parts[-1] not in ["cyacd", "bin", "hex", "tar"]:
            return None
        version = parts[-2]
        if not version.startswith("V"):
            return None
        version = version[1:]
        versions = version.split("_")
        if len(versions) == 2 and parts[-1] == "cyacd":
            major = int(versions[0])
            minor = int(versions[1])
            rev = 0
        elif len(versions) == 3 and (parts[-1] in ["bin", "hex", "tar"]):
            major = int(versions[0])
            minor = int(versions[1])
            rev = int(versions[2])
        else:
            return None
        firmware_version = FirmwareVersion(major, minor, rev)
        return Firmware(path, firmware_version)


def make_firmware_path(board: str, version: FirmwareVersion, ext: str = "bin") -> str:
    if board not in _BOARDS_RELEASE_MAPPING:
        board_path = f"{_CONFIG_DIR}/{board}"
        os.makedirs(board_path, exist_ok=True)
        _BOARDS_RELEASE_MAPPING[board] = board_path
    board_path = _BOARDS_RELEASE_MAPPING[board]
    return f"{board_path}/{board}.{str(version)}.{ext}"


def get_all_firmware_versions(board: str) -> List[Firmware]:
    all_versions: List[Firmware] = []
    if board not in _BOARDS_RELEASE_MAPPING:
        return all_versions
    dir_path = _BOARDS_RELEASE_MAPPING[board]
    for firmware_name in os.listdir(dir_path):
        firmware_path = os.path.join(dir_path, firmware_name)
        if not os.path.isdir(firmware_path):
            firmware = Firmware.create(firmware_path)
            if firmware is not None:
                all_versions.append(firmware)
    return all_versions


def get_purgeable_firmware_version(board: str) -> List[Firmware]:
    purgeable: List[Firmware] = []
    all_firmwares = sorted(get_all_firmware_versions(board), key=lambda x: x.version)

    comp: Optional[Firmware] = None
    for firmware in all_firmwares:
        if comp is None:
            comp = firmware
            continue
        if firmware.version.has_only_rev_difference(comp.version):
            if firmware.version > comp.version:
                purgeable.append(comp)
                comp = firmware
            else:
                purgeable.append(firmware)
        else:
            comp = firmware
    return purgeable


def get_latest_firmware_version(board: str) -> Optional[Firmware]:
    all_versions = get_all_firmware_versions(board)
    if len(all_versions) == 0:
        return None
    best = all_versions[0]
    for v in all_versions:
        if v.version.is_more_recent(best.version):
            best = v
    return best

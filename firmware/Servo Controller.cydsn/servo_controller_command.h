// Generated Code. Do not modify.
#include "project.h"
#include "leb128.h"
#include "stop.h"
#ifndef MAKA_servo_controller_command_H
#define MAKA_servo_controller_command_H
#pragma pack(1)
    typedef enum {
        SERVO_CONTROLLER_COMMAND_AS_SET_XFER_COMMAND = 1,
        SERVO_CONTROLLER_COMMAND_AS_SDO_DOWNLOAD_COMMAND = 2,
        SERVO_CONTROLLER_COMMAND_AS_SDO_UPLOAD_COMMAND = 3
} _tag_servo_controller_command;
typedef uint8_t samples[257];
typedef uint8_t carries[33];
typedef struct {
        uint8_t table_no;
        samples samples;
        carries carries;
} set_xfer_command;
typedef struct {
        uint8_t node_id;
        uint16_t index;
        uint8_t subindex;
        uint32_t value;
} sdo_download_command;
typedef uint16_t request_id;
typedef struct {
        uint8_t node_id;
        uint16_t index;
        uint8_t subindex;
        request_id request_id;
} sdo_upload_command;
typedef struct {
        _tag_servo_controller_command tag;
        union {
                set_xfer_command as_set_xfer_command;
                sdo_download_command as_sdo_download_command;
                sdo_upload_command as_sdo_upload_command;
        } val;
} servo_controller_command;
void servo_controller_command_as_set_xfer_command(set_xfer_command* val, void* ctx);
void servo_controller_command_as_sdo_download_command(sdo_download_command* val, void* ctx);
void servo_controller_command_as_sdo_upload_command(sdo_upload_command* val, void* ctx);

inline void demux_servo_controller_command(servo_controller_command *tu, void* ctx) {
    switch(tu->tag) {


        case SERVO_CONTROLLER_COMMAND_AS_SET_XFER_COMMAND:
                servo_controller_command_as_set_xfer_command(&(tu->val.as_set_xfer_command), ctx);
                break;
        case SERVO_CONTROLLER_COMMAND_AS_SDO_DOWNLOAD_COMMAND:
                servo_controller_command_as_sdo_download_command(&(tu->val.as_sdo_download_command), ctx);
                break;
        case SERVO_CONTROLLER_COMMAND_AS_SDO_UPLOAD_COMMAND:
                servo_controller_command_as_sdo_upload_command(&(tu->val.as_sdo_upload_command), ctx);
                break;
        default:
            stop("Unknown tag for servo_controller_command");
    }
}
typedef enum {
        SERVO_CONTROLLER_RESPONSE_AS_SDO_UPLOAD_RESPONSE = 1
} _tag_servo_controller_response;
typedef struct {
        request_id request_id;
        uint32_t value;
} sdo_upload_response;
typedef struct {
        _tag_servo_controller_response tag;
        union {
                sdo_upload_response as_sdo_upload_response;
        } val;
} servo_controller_response;
void servo_controller_response_as_sdo_upload_response(sdo_upload_response* val, void* ctx);

inline void demux_servo_controller_response(servo_controller_response *tu, void* ctx) {
    switch(tu->tag) {


        case SERVO_CONTROLLER_RESPONSE_AS_SDO_UPLOAD_RESPONSE:
                servo_controller_response_as_sdo_upload_response(&(tu->val.as_sdo_upload_response), ctx);
                break;
        default:
            stop("Unknown tag for servo_controller_response");
    }
}

#pragma pack()
#endif // MAKA_servo_controller_command_H
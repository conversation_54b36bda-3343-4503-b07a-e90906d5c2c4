/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/

#include "project.h"
#include "cyapicallbacks.h"
#include "servo_controller_command.h"
#include "xfer.h"
#include "sdo_rpc.h"


void servo_controller_command_as_set_xfer_command(set_xfer_command* val, void* ctx) {
    set_xfer_table(val->table_no, val->samples, val->carries);
    M_LCD_Print(3, 0, "Set xfer %d", val->table_no);
}

void servo_controller_command_as_sdo_download_command(sdo_download_command* val, void* ctx) {
    M_CANOpen_SetObject_u32(val->node_id, val->index, val->subindex, be32toh(val->value));
}

void servo_controller_command_as_sdo_upload_command(sdo_upload_command* val, void* ctx) {
    register_sdo_upload_request(val->request_id, val->node_id, val->index, val->subindex);
    M_CANOpen_GetObject(val->node_id, val->index, val->subindex);
}

void M_ProtoUSB_Receive_Callback(uint8_t* pkt, size_t size) {
    demux_servo_controller_command((servo_controller_command*)pkt, NULL);
}

void protocol_Loop() {
    M_CANOpen_eventq_elem eqe = M_CANOpen_NextEvent();
    while (eqe.header.typ != M_CANOpen_INPUT_EVENT_NOTHING) {
        if (eqe.header.typ == M_CANOpen_INPUT_EVENT_SDO_UPLOAD_COMPLETE) {
            M_CANOpen_SDO_upload_complete_event *ev = (M_CANOpen_SDO_upload_complete_event*)&eqe;
            M_LCD_Status(M_LCD_CHAR_HAPPY, " SDO %02x.%04x.%02x", ev->header.node_id, ev->index, ev->subindex, ev->data.as_u16[0]);
            M_LCD_Print(2, 0, "%04x %04x", ev->data.as_u16[0], ev->data.as_u16[1]);
            servo_controller_response resp;
            resp.tag = SERVO_CONTROLLER_RESPONSE_AS_SDO_UPLOAD_RESPONSE;
            int32_t request_id = register_sdo_upload_response(eqe.header.node_id, eqe.as_M_CANOpen_SDO_upload_complete_event.index, eqe.as_M_CANOpen_SDO_upload_complete_event.subindex);
            if (request_id >= 0) {
                resp.val.as_sdo_upload_response.request_id = request_id;
                resp.val.as_sdo_upload_response.value = ev->data.as_u32[0];
                M_ProtoUSB_Send((uint8_t*)&resp, sizeof(resp));
            }
        }
        // TODO (landon) we have abort events, but we ignore them
        // TODO (landon) More event types will go here when we have them
        eqe = M_CANOpen_NextEvent();
    }
}

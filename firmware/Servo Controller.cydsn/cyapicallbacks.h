/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PR<PERSON><PERSON>ETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/
#ifndef CYAPICALLBACKS_H
#define CYAPICALLBACKS_H
    
#include "stdint.h"
#include "unistd.h"

#define Long_Loop_INTERRUPT_INTERRUPT_CALLBACK
void Long_Loop_Interrupt_InterruptCallback();

#define M_ProtoUSB_RECEIVE_CALLBACK
void M_ProtoUSB_Receive_Callback(uint8_t* pkt, size_t size);
#endif /* CYAPICALLBACKS_H */   
/* [] */

/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
 */
#include "assert.h"
#include "project.h"
#include "protocol.h"
#include "stdarg.h"
#include "stop.h"

#define DRIVE_CHANNEL 4

void stop(const char *fmt, ...) {
  Fault_Write(1);
  va_list ap;
  va_start(ap, fmt);
  M_DebugConsole_Print("Stop \r\n");
  M_DebugConsole_Printv(fmt, ap);
  CyHalt(5);
  while (1)
    ; // Prepare for a visit from Mr. <PERSON><PERSON><PERSON>.
}

// This is not exactly pretty, but we only will ever have one machine that does it
// this way, so it's not parameterized as nicely as we'd normally like.

// The dir DOF ordering is reversed from the ADC, so deal with that here.
static uint8_t dir_chan_map[4] = {3, 2, 1, 0};
#define DIR_NUM_CHANNELS sizeof(dir_chan_map)
void set_channel_direction(uint8_t channel_no, direction dir) {
  if (channel_no > DIR_NUM_CHANNELS) {
    stop("Bad dirdof %d", channel_no);
  }
  M_DebugConsole_Print("Channel %d direction %d\r\n", channel_no, dir);
  channel_no = dir_chan_map[channel_no];

  M_DebugConsole_Print("Channel2 %d direction %d\r\n", channel_no, dir);
  uint8_t saved_intstatus = CyEnterCriticalSection();
  uint8_t mask = 0b11 << (channel_no * 2);
  uint8_t dir_bits = 0;
  switch (dir) {
  case FORWARD:
    dir_bits = 0b10;
    break;
  case REVERSE:
    dir_bits = 0b01;
    break;
  case STOP:
    // FALLTHROUGH
  default:
    dir_bits = 0;
    break;
  }
  uint8_t others = Dir_DOFs_Read() & ~mask;
  Dir_DOFs_Write(others | (dir_bits << (channel_no * 2)));
  CyExitCriticalSection(saved_intstatus);
}

void roverdrive_cmd_as_stop(void *ctx) {
  // turn off all 5 Hydro channels (4 wheels, one drive velocity)
  for (uint8_t channel_no = 0; channel_no < DIR_NUM_CHANNELS + 1; channel_no++) {
    M_HydroControl_SetChannelVelocity(channel_no, 0);
  }
  for (uint8_t channel_no = 0; channel_no < DIR_NUM_CHANNELS; channel_no++) {
    set_channel_direction(channel_no, STOP);
  }
  M_HelacDOF_Pause();
}

// TODO (landon) Only one of these APIs should survive long term, but it's unclear which
// one we want at the moment.
void roverdrive_cmd_as_set_positions(set_positions *val, void *ctx) {
  const size_t num_channels = sizeof(val->positions) / sizeof(*val->positions);
  _Static_assert(num_channels == M_HelacDOF_NUM_CHANNELS, "Mismatched channel count in HelacDOF and protocol");
  for (uint8_t channel_no = 0; channel_no < num_channels; channel_no++) {
    M_HelacDOF_SetChannelPosition(channel_no, val->positions[channel_no]);
  }
}

void roverdrive_cmd_as_set_position(set_position *val, void *ctx) {
  M_HelacDOF_SetChannelPosition(val->channel_no, val->position);
}

// TODO (landon) ibid
void roverdrive_cmd_as_set_directions(set_directions *val, void *ctx) {
  const size_t num_channels = sizeof(val->directions) / sizeof(*val->directions);
  _Static_assert(num_channels == DIR_NUM_CHANNELS, "Mismatched channel count in Dir DOF and protocol");
  for (uint8_t channel_no = 0; channel_no < num_channels; channel_no++) {
    set_channel_direction(channel_no, val->directions[channel_no]);
  }
}

void roverdrive_cmd_as_set_direction(set_direction *val, void *ctx) {
  set_channel_direction(val->channel_no, val->direction);
}
void roverdrive_cmd_as_set_drive_velocity(set_drive_velocity *val, void *ctx) {
  M_HydroControl_SetChannelVelocity(DRIVE_CHANNEL, val->velocity);
}

void roverdrive_cmd_as_set_gains(set_gains *val, void *ctx) {
  for (uint8_t channel_no = 0; channel_no < M_HelacDOF_NUM_CHANNELS; channel_no++) {
    M_HelacDOF_EnableChannel(channel_no, val->P, val->I, val->D);
  }
}

void M_ProtoUSB_Receive_Callback(const uint8_t *pkt, size_t size) {
  // size isn't used here cause packet structure determines length
  demux_roverdrive_cmd((roverdrive_cmd *)pkt, NULL);
}
#define DEFAULT_P_GAIN 50
int main(void) {
  CyGlobalIntEnable;
  M_DebugConsole_Start();
  M_DebugConsole_Print("Initializing\r\n");
  M_ProtoUSB_Start();
  M_DS1803_Start();
  ADC_SAR_Seq_Start();
  ADC_SAR_Seq_StartConvert();
  M_HydroControl_EnableChannel(DRIVE_CHANNEL, -1, 0, 1);
  M_HydroControl_EnableChannel(1, -1, 2, 3);
  M_HydroControl_EnableChannel(2, -1, 8, 9);
  M_HydroControl_EnableChannel(3, -1, 10, 11);
  M_HydroControl_EnableChannel(0, -1, 14, 15);

  // Which helac is which is taken care of in python, so here they're just numbers.
  M_HelacDOF_EnableChannel(0, DEFAULT_P_GAIN, 0, 0);
  M_HelacDOF_EnableChannel(1, DEFAULT_P_GAIN, 0, 0);
  M_HelacDOF_EnableChannel(2, DEFAULT_P_GAIN, 0, 0);
  M_HelacDOF_EnableChannel(3, DEFAULT_P_GAIN, 0, 0);

  M_HelacDOF_SetChannelPosition(0, 2500);
  M_HelacDOF_SetChannelPosition(1, 2500);
  M_HelacDOF_SetChannelPosition(2, 2500);
  M_HelacDOF_SetChannelPosition(3, 2500);

  M_HydroControl_Start();
  M_HelacDOF_Start();
  PID_Timer_Start();
  PID_Timer_Reset_Write(1);
  M_DebugConsole_Print("Starting loop\r\n");
  for (;;) {
    Run_Write(!Run_Read());
    M_ProtoUSB_Loop();
    /*
    // Do not erase. Used for centering joral encoders.
    if (!Button_Read()) {
        M_HydroControl_SetChannelVelocity(2, 15000);
    } else {
        M_HydroControl_SetChannelVelocity(2, 0);
    }
    */
    size_t dt_us = PID_Timer_ReadCounter();
    float dt_s = dt_us / 1.0e6;
    PID_Timer_Reset_Write(1);
    M_HelacDOF_Loop(dt_s);
    M_HydroControl_Loop();
  }
}

// Generated Code. Do not modify.
#include "leb128.h"
#include "project.h"
#include "stop.h"
#ifndef MAKA_roverdrive_cmd_H
#define MAKA_roverdrive_cmd_H
#pragma pack(1)
typedef enum {
  ROVERDRIVE_CMD_AS_STOP = 1,
  ROVERDRIVE_CMD_AS_SET_POSITIONS = 2,
  ROVERDRIVE_CMD_AS_SET_DIRECTIONS = 3,
  ROVERDRIVE_CMD_AS_SET_POSITION = 4,
  ROVERDRIVE_CMD_AS_SET_DIRECTION = 5,
  ROVERDRIVE_CMD_AS_SET_DRIVE_VELOCITY = 6,
  ROVERDRIVE_CMD_AS_SET_GAINS = 7
} _tag_roverdrive_cmd;
typedef int16_t positions[4];
typedef struct {
  positions positions;
} set_positions;
typedef enum { STOP = 0, FORWARD = 1, REVERSE = 2 } direction;
typedef direction directions[4];
typedef struct {
  directions directions;
} set_directions;
typedef struct {
  uint8_t channel_no;
  int16_t position;
} set_position;
typedef struct {
  uint8_t channel_no;
  direction direction;
} set_direction;
typedef struct {
  int16_t velocity;
} set_drive_velocity;
typedef struct {
  float32 P;
  float32 I;
  float32 D;
} set_gains;
typedef struct {
  _tag_roverdrive_cmd tag;
  union {
    set_positions as_set_positions;
    set_directions as_set_directions;
    set_position as_set_position;
    set_direction as_set_direction;
    set_drive_velocity as_set_drive_velocity;
    set_gains as_set_gains;
  } val;
} roverdrive_cmd;
void roverdrive_cmd_as_stop(void *ctx);
void roverdrive_cmd_as_set_positions(set_positions *val, void *ctx);
void roverdrive_cmd_as_set_directions(set_directions *val, void *ctx);
void roverdrive_cmd_as_set_position(set_position *val, void *ctx);
void roverdrive_cmd_as_set_direction(set_direction *val, void *ctx);
void roverdrive_cmd_as_set_drive_velocity(set_drive_velocity *val, void *ctx);
void roverdrive_cmd_as_set_gains(set_gains *val, void *ctx);

inline void demux_roverdrive_cmd(roverdrive_cmd *tu, void *ctx) {
  switch (tu->tag) {

  case ROVERDRIVE_CMD_AS_STOP:
    roverdrive_cmd_as_stop(ctx);
    break;
  case ROVERDRIVE_CMD_AS_SET_POSITIONS:
    roverdrive_cmd_as_set_positions(&(tu->val.as_set_positions), ctx);
    break;
  case ROVERDRIVE_CMD_AS_SET_DIRECTIONS:
    roverdrive_cmd_as_set_directions(&(tu->val.as_set_directions), ctx);
    break;
  case ROVERDRIVE_CMD_AS_SET_POSITION:
    roverdrive_cmd_as_set_position(&(tu->val.as_set_position), ctx);
    break;
  case ROVERDRIVE_CMD_AS_SET_DIRECTION:
    roverdrive_cmd_as_set_direction(&(tu->val.as_set_direction), ctx);
    break;
  case ROVERDRIVE_CMD_AS_SET_DRIVE_VELOCITY:
    roverdrive_cmd_as_set_drive_velocity(&(tu->val.as_set_drive_velocity), ctx);
    break;
  case ROVERDRIVE_CMD_AS_SET_GAINS:
    roverdrive_cmd_as_set_gains(&(tu->val.as_set_gains), ctx);
    break;
  default:
    stop("Unknown tag for roverdrive_cmd");
  }
}
typedef enum { ROVERDRIVE_RESP_AS_STATUS = 1 } _tag_roverdrive_resp;
typedef struct {
  positions positions;
  directions directions;
  direction drive_direction;
  positions setpoints;
} status;
typedef struct {
  _tag_roverdrive_resp tag;
  union {
    status as_status;
  } val;
} roverdrive_resp;
void roverdrive_resp_as_status(status *val, void *ctx);

inline void demux_roverdrive_resp(roverdrive_resp *tu, void *ctx) {
  switch (tu->tag) {

  case ROVERDRIVE_RESP_AS_STATUS:
    roverdrive_resp_as_status(&(tu->val.as_status), ctx);
    break;
  default:
    stop("Unknown tag for roverdrive_resp");
  }
}

#pragma pack()
#endif // MAKA_roverdrive_cmd_H
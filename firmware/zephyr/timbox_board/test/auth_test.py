# type: ignore
# mypy: ignore-errors
import asyncio
import ctypes
import time

import can


# Define the enumeration Auth_Status using ctypes
class Auth_Status(ctypes.c_uint8):
    Auth_Status_Not_Authenticated = 0x00
    Auth_Status_Authenticated = 0x01
    Auth_Status_Error = 0x0E
    Auth_Status_Not_Available = 0x0F


class Auth_Substatus(ctypes.Union):
    class SubstatusBits(ctypes.LittleEndianStructure):
        _fields_ = [
            ("reserved", ctypes.c_uint8, 1),
            ("challenge_signed", ctypes.c_uint8, 1),
            ("device_certificate_valid", ctypes.c_uint8, 1),
            ("manufacturer_series_certificate_valid", ctypes.c_uint8, 1),
            ("manufacturer_certificate_valid", ctypes.c_uint8, 1),
            ("testlab_certificate_valid", ctypes.c_uint8, 1),
            ("lwa_possible", ctypes.c_uint8, 1),
            ("restart_authentication", ctypes.c_uint8, 1),
        ]

    _fields_ = [("substatus", SubstatusBits), ("raw", ctypes.c_uint8)]


# Define the enumeration Auth_CertificateType using ctypes
class Auth_CertificateType(ctypes.c_uint8):
    Auth_CertificateType_Aef_Root = 0x00
    Auth_CertificateType_Testlab = 0x01
    Auth_CertificateType_Manufacturer = 0x02
    Auth_CertificateType_Manufacturer_Series = 0x03
    Auth_CertificateType_Device = 0x04
    Auth_CertificateType_Crl_Signing = 0x05
    Auth_CertificateType_Crl = 0x06
    Auth_CertificateType_Crl_SignSubCa = 0x07


# Define the enumeration Auth_Message_Code using ctypes
class Auth_Message_Code(ctypes.c_uint8):
    Auth_Message_Code_ClientAuthenticationStatus = 0xFA
    Auth_Message_Code_ServerAuthenticationStatus = 0xF9
    Auth_Message_Code_ServerCertificate = 0x02
    Auth_Message_Code_ClientCertificate = 0x03
    Auth_Message_Code_ServerRandomChallenge = 0x04
    Auth_Message_Code_ClientRandomChallenge = 0x05
    Auth_Message_Code_ServerSignedChallenge = 0x06
    Auth_Message_Code_ClientSignedChallenge = 0x07
    Auth_Message_Code_ServerAuthenticationInfo = 0x08
    Auth_Message_Code_ClientAuthenticationInfo = 0x09


# Define the structure Auth_ClientAuthenticationStatus_t using ctypes
class Auth_ClientAuthenticationStatus_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("error_code", ctypes.c_uint8, 8),
        ("authentication_status", Auth_Status, 4),
        ("authentication_type", ctypes.c_uint8, 4),
        ("authentication_substatus", Auth_Substatus),
        ("reserved0", ctypes.c_uint8, 8),
        ("reserved1", ctypes.c_uint8, 8),
        ("reserved2", ctypes.c_uint8, 8),
        ("reserved3", ctypes.c_uint8, 8),
    ]


# Define the structure Auth_ServerCertificateRequest_t using ctypes
class Auth_ClientCertificateRequest_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("reserved0", ctypes.c_uint8, 8),
        ("reserved1", ctypes.c_uint8, 4),
        ("authentication_type", ctypes.c_uint8, 4),
        ("certificate_format", ctypes.c_uint8, 8),
        ("certificate_type", ctypes.c_uint8, 8),
        ("reserved2", ctypes.c_uint8, 8),
        ("reserved3", ctypes.c_uint8, 8),
        ("reserved4", ctypes.c_uint8, 8),
    ]


# Define the structure Auth_ServerCertificateResponse_t using ctypes
class Auth_ClientCertificateResponse_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("error_code", ctypes.c_uint8, 8),
        ("authentication_type", ctypes.c_uint8, 4),
        ("certificate_format", ctypes.c_uint8, 8),
        ("certificate_type", ctypes.c_uint8, 8),
        ("length_lsb", ctypes.c_uint8, 8),
        ("length_msb", ctypes.c_uint8, 8),
        ("certificate", ctypes.c_uint8 * 32),
    ]


# Define the structure Auth_ServerAuthenticationStatus_t using ctypes
class Auth_ServerAuthenticationStatus_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("error_code", ctypes.c_uint8, 8),
        ("authentication_status", ctypes.c_uint8, 4),
        ("authentication_type", ctypes.c_uint8, 4),
        ("authentication_substatus", ctypes.c_uint8, 8),
        ("reserved0", ctypes.c_uint8, 8),
        ("reserved1", ctypes.c_uint8, 8),
        ("reserved2", ctypes.c_uint8, 8),
        ("reserved3", ctypes.c_uint8, 8),
    ]


# Define the structure Auth_ClientSignedChallengeRequest_t using ctypes
class Auth_ClientSignedChallengeRequest_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("reserved0", ctypes.c_uint8, 8),
        ("reserved1", ctypes.c_uint8, 4),
        ("authentication_type", ctypes.c_uint8, 4),
        ("reserved2", ctypes.c_uint8, 8),
        ("reserved3", ctypes.c_uint8, 8),
        ("reserved4", ctypes.c_uint8, 8),
        ("reserved5", ctypes.c_uint8, 8),
        ("reserved6", ctypes.c_uint8, 8),
    ]


# Define the structure Auth_ClientSignedChallengeResponse_t using ctypes
class Auth_ClientSignedChallengeResponse_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("error_code", ctypes.c_uint8, 8),
        ("reserved1", ctypes.c_uint8, 4),
        ("authentication_type", ctypes.c_uint8, 4),
        ("reserved2", ctypes.c_uint8, 8),
        ("reserved3", ctypes.c_uint8, 8),
        ("length_lsb", ctypes.c_uint8, 8),
        ("length_msb", ctypes.c_uint8, 8),
        ("signed_challenge", ctypes.c_uint8, 8),
    ]


# Define the structure Auth_ServerAuthenticationInfoRequest_t using ctypes
class Auth_ServerAuthenticationInfoRequest_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("reserved0", ctypes.c_uint8, 8),
        ("authentication_type", ctypes.c_uint8, 4),
        ("reserved1", ctypes.c_uint8, 8),
        ("reserved2", ctypes.c_uint8, 8),
        ("reserved3", ctypes.c_uint8, 8),
        ("reserved4", ctypes.c_uint8, 8),
        ("reserved5", ctypes.c_uint8, 8),
    ]


# Define the structure Auth_ServerCertificateRequest_t using ctypes
class Auth_ServerCertificateRequest_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("reserved0", ctypes.c_uint8, 8),
        ("reserved1", ctypes.c_uint8, 4),
        ("authentication_type", ctypes.c_uint8, 4),
        ("certificate_format", ctypes.c_uint8, 8),
        ("certificate_type", ctypes.c_uint8, 8),
        ("reserved2", ctypes.c_uint8, 8),
        ("reserved3", ctypes.c_uint8, 8),
        ("reserved4", ctypes.c_uint8, 8),
    ]


# Define the structure Auth_ServerCertificateResponse_t using ctypes
class Auth_ServerCertificateResponse_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("error_code", ctypes.c_uint8, 8),
        ("authentication_type", ctypes.c_uint8, 4),
        ("certificate_format", ctypes.c_uint8, 8),
        ("certificate_type", ctypes.c_uint8, 8),
        ("length_lsb", ctypes.c_uint8, 8),
        ("length_msb", ctypes.c_uint8, 8),
        ("certificate", ctypes.c_uint8 * 32),
    ]


# Define the structure Auth_ServerRandomChallengeRequest_t using ctypes
class Auth_ServerRandomChallengeRequest_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("reserved0", ctypes.c_uint8, 8),
        ("reserved1", ctypes.c_uint8, 4),
        ("authentication_type", ctypes.c_uint8, 4),
        ("reserved2", ctypes.c_uint8, 8),
        ("reserved3", ctypes.c_uint8, 8),
        ("reserved4", ctypes.c_uint8, 8),
        ("reserved5", ctypes.c_uint8, 8),
        ("reserved6", ctypes.c_uint8, 8),
    ]


# Define the structure Auth_ServerRandomChallengeResponse_t using ctypes
class Auth_ServerRandomChallengeResponse_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("error_code", ctypes.c_uint8, 8),
        ("reserved0", ctypes.c_uint8, 4),
        ("authentication_type", ctypes.c_uint8, 4),
        ("reserved1", ctypes.c_uint8, 8),
        ("reserved2", ctypes.c_uint8, 8),
        ("length_lsb", ctypes.c_uint8, 8),
        ("length_msb", ctypes.c_uint8, 8),
        ("challenge", ctypes.c_uint8 * 32),
    ]


# Define the structure Auth_ClientRandomChallengeRequest_t using ctypes
class Auth_ClientRandomChallengeRequest_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("reserved0", ctypes.c_uint8, 8),
        ("reserved1", ctypes.c_uint8, 4),
        ("authentication_type", ctypes.c_uint8, 4),
        ("reserved2", ctypes.c_uint8, 8),
        ("reserved3", ctypes.c_uint8, 8),
        ("reserved4", ctypes.c_uint8, 8),
        ("reserved5", ctypes.c_uint8, 8),
        ("reserved6", ctypes.c_uint8, 8),
    ]


# Define the structure Auth_ClientRandomChallengeResponse_t using ctypes
class Auth_ClientRandomChallengeResponse_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("error_code", ctypes.c_uint8, 8),
        ("reserved0", ctypes.c_uint8, 4),
        ("authentication_type", ctypes.c_uint8, 4),
        ("reserved1", ctypes.c_uint8, 8),
        ("reserved2", ctypes.c_uint8, 8),
        ("length_lsb", ctypes.c_uint8, 8),
        ("length_msb", ctypes.c_uint8, 8),
        ("challenge", ctypes.c_uint8 * 32),
    ]


"""
# Define the structure Auth_Message_t using ctypes
class Auth_Message_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("message_code", ctypes.c_uint8, 8),
        ("message", ctypes.Union, {
            "empty": (ctypes.c_uint8 * 7),
            "client_authentication_status": Auth_ClientAuthenticationStatus_t,
            "server_authentication_status": Auth_ServerAuthenticationStatus_t,
            # Add other structure definitions here
            "server_random_challenge_request": Auth_ServerRandomChallengeRequest_t,
            "client_random_challenge_request": Auth_ClientRandomChallengeRequest_t,
            "client_random_challenge_response": Auth_ClientRandomChallengeResponse_t
        }),
        ("raw", ctypes.c_uint8 * 40)
    ]
"""


class Auth_Message_t(ctypes.Union):
    class Data_t(ctypes.LittleEndianStructure):
        class Message_t(ctypes.Union):
            _fields_ = [
                ("empty", ctypes.c_uint8 * 7),
                ("client_authentication_status", Auth_ClientAuthenticationStatus_t),
                ("server_authentication_status", Auth_ServerAuthenticationStatus_t),
                ("client_certificate_request", Auth_ClientCertificateRequest_t),
                ("client_certificate_response", Auth_ClientCertificateResponse_t),
                ("client_signed_challenge_request", Auth_ClientSignedChallengeRequest_t),
                ("client_signed_challenge_response", Auth_ClientSignedChallengeResponse_t),
                ("server_auth_info_request", Auth_ServerAuthenticationInfoRequest_t),
                ("server_certificate_request", Auth_ServerCertificateRequest_t),
                ("server_certificate_response", Auth_ServerCertificateResponse_t),
                ("server_random_challenge_request", Auth_ServerRandomChallengeRequest_t),
                ("server_random_challenge_response", Auth_ServerRandomChallengeResponse_t),
                ("client_random_challenge_request", Auth_ClientRandomChallengeRequest_t),
                ("client_random_challenge_response", Auth_ClientRandomChallengeResponse_t),
            ]

        _fields_ = [("message_code", ctypes.c_uint8, 8), ("message", Message_t)]

    _fields_ = [("data", Data_t), ("raw", ctypes.c_uint8 * 40)]


# SECTION: Tim State Machine
class Tim_Client_State(ctypes.c_uint8):
    TIM_Client_State_Automation_Unavailable = 0x00
    TIM_Client_State_Automation_Not_Ready = 0x01
    TIM_Client_State_Automation_Ready = 0x02
    TIM_Client_State_Automation_Enabled = 0x03
    TIM_Client_State_Automation_Active = 0x05
    TIM_Client_State_Automation_Fault = 0x0D
    TIM_Client_State_Error = 0x0E


class Tim_Server_State(ctypes.c_uint8):
    TIM_Server_State_Automation_Unavailable = 0x00
    TIM_Server_State_Automation_Not_Ready = 0x01
    TIM_Server_State_Automation_Ready = 0x02
    TIM_Server_State_Automation_Enabled = 0x03
    TIM_Server_State_Automation_Active = 0x05
    TIM_Server_State_Automation_Fault = 0x0D
    TIM_Server_State_Error = 0x0E


class Tim_System_Operation_State(ctypes.c_uint8):
    TIM_System_Operation_State_Requirements_Not_Fulfilled = 0x00
    TIM_System_Operation_State_Requirements_Normal_Fulfilled = 0x01
    TIM_System_Operation_State_Requirements_Standstill_Fulfilled = 0x02
    TIM_System_Operation_State_Requirements_Stationary_Fulfilled = 0x03
    TIM_System_Operation_State_Error = 0x0E
    TIM_System_Operation_State_Not_Available = 0x0F


class Tim_System_State(ctypes.c_uint8):
    TIM_System_State_No_Automation_Active = 0x01
    TIM_System_State_Automation_Active = 0x05
    TIM_System_State_Error = 0x0E
    TIM_System_State_Not_Available = 0x0F


class Tim_Server_Master_Indication(ctypes.c_uint8):
    Tim_Server_Master_Indication_Automation_Not_Allowed = 0x00
    Tim_Server_Master_Indication_Automation_Allowed = 0x01
    Tim_Server_Master_Indication_Error = 0x0E
    Tim_Server_Master_Indication_Not_Available = 0x0F


# SECTION: Tim Message Codes (AEF 023 A.2.3)
class Tim_Server_Message_Code(ctypes.c_uint8):
    TIM_FunctionsAssignmentStatusResponse = 0xF3
    TIM_FunctionsSupportResponse = 0xF4
    TIM_FunctionsAssignmentResponse = 0xF5
    TIM_ConnectionVersionResponse = 0xF6
    TIM_ClientVersionRequest = 0xF7
    TIM_ServerVersionResponse = 0xF8
    TIM_ServerStatus_Msg = 0xFA


class Tim_Client_Message_Code(ctypes.c_uint8):
    TIM_FunctionsAssignmentStatusRequest = 0xF3
    TIM_FunctionsSupportRequest = 0xF4
    TIM_FunctionsAssignmentRequest = 0xF5
    TIM_ConnectionVersionRequest = 0xF6
    TIM_ClientVersionResponse = 0xF7
    TIM_ServerVersionRequest = 0xF8
    TIM_ClientStatus_Msg = 0xF9


# SECTION: TIM Status & Operation Messages
class Tim_Client_Version_Response_Message_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("reserved0", ctypes.c_uint8, 8),
        ("implemented_tim_version_number", ctypes.c_uint8, 8),
        ("minimum_tim_version_number", ctypes.c_uint8, 8),
        ("reserved1", ctypes.c_uint8, 8),
        ("reserved2", ctypes.c_uint8, 8),
        ("reserved3", ctypes.c_uint8, 8),
        ("reserved4", ctypes.c_uint8, 8),
    ]


class Tim_Connection_Version_Request_Message_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("reserved0", ctypes.c_uint8, 8),
        ("implemented_tim_version_number", ctypes.c_uint8, 8),
        ("minimum_tim_version_number", ctypes.c_uint8, 8),
        ("reserved1", ctypes.c_uint8, 8),
        ("reserved2", ctypes.c_uint8, 8),
        ("reserved3", ctypes.c_uint8, 8),
        ("reserved4", ctypes.c_uint8, 8),
    ]


class Tim_Connection_Version_Response_Message_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("error_code", ctypes.c_uint8, 8),
        ("tim_connection_version", ctypes.c_uint8, 8),
        ("reserved0", ctypes.c_uint8, 8),
        ("reserved1", ctypes.c_uint8, 8),
        ("reserved2", ctypes.c_uint8, 8),
        ("reserved3", ctypes.c_uint8, 8),
        ("reserved4", ctypes.c_uint8, 8),
        ("reserved5", ctypes.c_uint8, 8),
    ]


class Tim_ServerStatus_Msg_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("heartbeat_counter", ctypes.c_uint8, 8),
        ("tim_system_state", ctypes.c_uint8, 4),
        ("tim_server_master_indication", ctypes.c_uint8, 4),
        ("tim_server_operation_state", ctypes.c_uint8, 4),
        ("tim_server_state", ctypes.c_uint8, 4),
        ("reserved0", ctypes.c_uint8, 8),
        ("reserved1", ctypes.c_uint8, 8),
        ("reserved2", ctypes.c_uint8, 8),
        ("reserved3", ctypes.c_uint8, 8),
    ]


class Tim_ClientStatus_Msg_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("heartbeat_counter", ctypes.c_uint8, 8),
        ("reserved0", ctypes.c_uint8, 4),
        ("tim_client_state", ctypes.c_uint8, 4),
        ("reserved1", ctypes.c_uint8, 8),
        ("reserved2", ctypes.c_uint8, 8),
        ("reserved3", ctypes.c_uint8, 8),
        ("reserved4", ctypes.c_uint8, 8),
        ("reserved5", ctypes.c_uint8, 8),
    ]


class Tim_FunctionsSupportRequest_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("reserved0", ctypes.c_uint8, 8),
        ("reserved1", ctypes.c_uint8, 8),
        ("reserved2", ctypes.c_uint8, 8),
        ("reserved3", ctypes.c_uint8, 8),
        ("reserved4", ctypes.c_uint8, 8),
        ("reserved5", ctypes.c_uint8, 8),
        ("reserved6", ctypes.c_uint8, 8),
    ]


class Tim_ServerVersionResponse_t(ctypes.LittleEndianStructure):
    _fields_ = [
        ("boot_time", ctypes.c_uint8, 8),
        ("implemented_tim_version", ctypes.c_uint8, 8),
        ("minimum_tim_version", ctypes.c_uint8, 8),
        ("reserved0", ctypes.c_uint8, 8),
        ("reserved1", ctypes.c_uint8, 8),
        ("reserved2", ctypes.c_uint8, 8),
        ("reserved3", ctypes.c_uint8, 8),
    ]


class Tim_Operation_Message_t(ctypes.Union):
    class Data_t(ctypes.LittleEndianStructure):
        class Message_t(ctypes.Union):
            _fields_ = [
                ("empty", ctypes.c_uint8 * 7),
                ("client_version_response", Tim_Client_Version_Response_Message_t),
                ("server_version_response", Tim_ServerVersionResponse_t),
                ("connection_version_request", Tim_Connection_Version_Request_Message_t),
                ("connection_version_response", Tim_Connection_Version_Response_Message_t),
                ("function_support_request", Tim_FunctionsSupportRequest_t),
                ("client_status", Tim_ClientStatus_Msg_t),
                ("server_status", Tim_ServerStatus_Msg_t),
            ]

        _fields_ = [("message_code", ctypes.c_uint8, 8), ("message", Message_t)]

    _fields_ = [("data", Data_t), ("raw", ctypes.c_uint8 * 8)]


J1939_PGN_DATA_SIZE = 8
J1939_PGN_ID_ADDR_REQUEST = 0xEA
J1939_PGN_ID_ADDR_CLAIMED = 0xEE
J1939_PGN_TIM_SERVER = 0x23
J1939_PGN_TIM_CLIENT = 0x24
J1939_PGN_AUTH_CLIENT = 0x6F
J1939_PGN_AUTH_SERVER = 0x70
J1939_CONFIG_ID_NUMBER = 0
J1939_CONFIG_MANUFACTURER_CODE = 0x745
J1939_CONFIG_ECU_INSTANCE = 0
J1939_CONFIG_FUNCTION_INSTANCE = 5
J1939_CONFIG_FUNCTION = 0
J1939_CONFIG_VEHICLE_SYSTEM_2 = 0
J1939_CONFIG_VEHICLE_SYSTEM_INSTANCE = 0
J1939_CONFIG_INDUSTRY_GROUP = 2
J1939_CONFIG_ARBITRARY_ADDRESS_CAPABLE = 1
J1939_CONFIG_ADDRESS = 5
J1939_CONFIG_TIM_SERVER_ADDRESS = 0xED

bus = None


class J1939_Id_t(ctypes.Union):
    class Data_t(ctypes.LittleEndianStructure):
        _fields_ = [
            ("source", ctypes.c_uint8, 8),
            ("ps", ctypes.c_uint8, 8),
            ("pf", ctypes.c_uint8, 8),
            ("dp", ctypes.c_uint8, 1),
            ("reserved", ctypes.c_uint8, 1),
            ("priority", ctypes.c_uint8, 3),
        ]

    _fields_ = [("id", Data_t), ("raw", ctypes.c_uint32)]


class J1939_Pgn_Packet_t(ctypes.Structure):
    _fields_ = [("id", J1939_Id_t), ("data", ctypes.POINTER(ctypes.c_uint8)), ("dlc", ctypes.c_uint8)]


class J1939_Pgn_Address_Claim_Data_t(ctypes.Union):
    class Data_t(ctypes.LittleEndianStructure):
        _fields_ = [
            ("identity", ctypes.c_uint32, 21),
            ("manufacturer_code", ctypes.c_uint16, 11),
            ("ecu_instance", ctypes.c_uint8, 3),
            ("function_instance", ctypes.c_uint8, 5),
            ("function", ctypes.c_uint8, 8),
            ("reserved", ctypes.c_uint8, 1),
            ("vehicle_system", ctypes.c_uint8, 7),
            ("vehicle_system_instance", ctypes.c_uint8, 4),
            ("industry_group", ctypes.c_uint8, 3),
            ("arbitrary_addresss_compatible", ctypes.c_uint8, 1),
        ]

    _fields_ = [("data", Data_t), ("raw", ctypes.c_uint8 * 8)]


def send_can_frame(frame):
    global bus
    bus.send(frame, timeout=0.1)
    # print(f"Sending msg id: [{frame.arbitration_id}]")


def j1939_send_pgn(pkt):
    frame = can.Message(
        arbitration_id=pkt.id.raw,
        # extended_id=True,
        is_remote_frame=False,
        data=pkt.data[: pkt.dlc],
    )
    # print(f"Sent J1939 ID {frame.arbitration_id}")
    send_can_frame(frame)


def isobus_send_tim_msg(data):
    j1939_pkt = J1939_Pgn_Packet_t(
        id=J1939_Id_t(
            id=J1939_Id_t.Data_t(
                priority=6,
                dp=0,
                ps=J1939_CONFIG_TIM_SERVER_ADDRESS,
                pf=J1939_PGN_TIM_CLIENT,
                source=J1939_CONFIG_ADDRESS,
            )
        ),
        dlc=8,
        data=data,
    )
    j1939_send_pgn(j1939_pkt)
    return 0


def isobus_send_auth_msg(data, length):
    j1939_pkt = J1939_Pgn_Packet_t(
        id=J1939_Id_t(
            id=J1939_Id_t.Data_t(
                priority=6,
                dp=0,
                ps=J1939_CONFIG_TIM_SERVER_ADDRESS,
                pf=J1939_PGN_AUTH_CLIENT,
                source=J1939_CONFIG_ADDRESS,
            )
        ),
        dlc=length,
        data=data,
    )
    j1939_send_pgn(j1939_pkt)
    return 0


def j1939_claim_address():
    address_request_data = (ctypes.c_uint8 * 3)(0x00, 0xEE, 0x00)
    address_request_packet = J1939_Pgn_Packet_t(
        id=J1939_Id_t(
            id=J1939_Id_t.Data_t(priority=6, dp=0, ps=255, pf=J1939_PGN_ID_ADDR_REQUEST, source=J1939_CONFIG_ADDRESS)
        ),
        dlc=3,
        data=address_request_data,
    )

    j1939_send_pgn(address_request_packet)

    time.sleep(0.01)

    address_claimed_data = J1939_Pgn_Address_Claim_Data_t(
        data=J1939_Pgn_Address_Claim_Data_t.Data_t(
            identity=J1939_CONFIG_ID_NUMBER,
            manufacturer_code=J1939_CONFIG_MANUFACTURER_CODE,
            ecu_instance=J1939_CONFIG_ECU_INSTANCE,
            function_instance=J1939_CONFIG_FUNCTION_INSTANCE,
            function=J1939_CONFIG_FUNCTION,
            vehicle_system=J1939_CONFIG_VEHICLE_SYSTEM_2,
            vehicle_system_instance=J1939_CONFIG_VEHICLE_SYSTEM_INSTANCE,
            industry_group=J1939_CONFIG_INDUSTRY_GROUP,
            arbitrary_addresss_compatible=J1939_CONFIG_ARBITRARY_ADDRESS_CAPABLE,
        )
    )

    address_claimed_packet = J1939_Pgn_Packet_t(
        id=J1939_Id_t(
            id=J1939_Id_t.Data_t(priority=6, dp=0, ps=255, pf=J1939_PGN_ID_ADDR_CLAIMED, source=J1939_CONFIG_ADDRESS)
        ),
        dlc=8,
        # data=ctypes.byref(address_claimed_data)
        data=address_claimed_data.raw,
    )
    j1939_send_pgn(address_claimed_packet)
    print("Claimed J1939 address")

    # TODO: check that address request was accepted
    # j1939.address = J1939_CONFIG_ADDRESS

    return 0


j1939_rx_task = None
j1939_tim_handlers = []
j1939_auth_handlers = []


async def j1939_add_tim_handler(handler, message_code):
    j1939_tim_handlers.append((handler, message_code))
    return 0


async def j1939_add_auth_handler(handler, message_code):
    j1939_auth_handlers.append((handler, message_code))


async def j1939_rx_thread():
    global bus
    while True:
        can_msg = bus.recv()
        j1939_id = J1939_Id_t(raw=can_msg.arbitration_id)
        if j1939_id.id.pf == J1939_PGN_TIM_SERVER:
            tim_msg = Tim_Operation_Message_t(raw=(ctypes.c_ubyte * 8)(*can_msg.data))
            for (handler, message_code) in j1939_tim_handlers:
                if tim_msg.data.message_code == message_code:
                    handler(tim_msg)
        elif j1939_id.id.pf == J1939_PGN_AUTH_SERVER:
            auth_msg = Auth_Message_t(raw=can_msg.data)
            for (handler, message_code) in j1939_auth_handlers:
                if auth_msg.data.message_code == message_code:
                    handler(auth_msg)
        await asyncio.sleep(0.001)


async def j1939_init():
    # Initialize canbus
    global j1939_rx_task
    global bus
    bus = can.Bus(channel="can1", bustype="socketcan")
    # Start rx thread
    # loop = asyncio.get_event_loop()
    # j1939_rx_task = loop.create_task(j1939_rx_thread())
    j1939_rx_task = asyncio.create_task(j1939_rx_thread())
    # Claim address
    j1939_claim_address()
    return 0


tim_server_version = None
tim_status_task = None
tim_client_status = None


def connection_version_response_handler(msg: Tim_Operation_Message_t):
    global tim_server_version
    tim_server_version = msg.data.message.connection_version_response.tim_connection_version
    print(f"Connected to TIM server running version {tim_server_version}")


async def tim_cyclic_client_status_thread():
    global tim_client_status
    while True:
        msg = Tim_Operation_Message_t(
            data=Tim_Operation_Message_t.Data_t(
                message_code=Tim_Client_Message_Code.TIM_ClientStatus_Msg,
                message=Tim_Operation_Message_t.Data_t.Message_t(client_status=tim_client_status),
            )
        )
        isobus_send_tim_msg(msg.raw)
        tim_client_status.heartbeat_counter += 1
        if tim_client_status.heartbeat_counter >= 0xFA:
            tim_client_status.heartbeat_counter = 0x00
        await asyncio.sleep(0.09)


async def tim_init():
    # Setup handler for server version response
    # Setup handler for connection version response
    await j1939_add_tim_handler(
        connection_version_response_handler, Tim_Server_Message_Code.TIM_ConnectionVersionResponse
    )
    # Send connection version request
    connection_version_request_msg = Tim_Operation_Message_t(
        data=Tim_Operation_Message_t.Data_t(
            message_code=Tim_Client_Message_Code.TIM_ConnectionVersionRequest,
            message=Tim_Operation_Message_t.Data_t.Message_t(
                connection_version_request=Tim_Connection_Version_Request_Message_t(
                    implemented_tim_version_number=1, minimum_tim_version_number=1
                )
            ),
        )
    )
    isobus_send_tim_msg(connection_version_request_msg.raw)
    print("Sent ConnectionVersionRequest")

    # Start sending cyclic client status messages
    global tim_status_task
    global tim_client_status
    tim_client_status = Tim_ClientStatus_Msg_t(
        heartbeat_counter=0xFB,
        reserved0=0xFF,
        tim_client_state=Tim_Client_State.TIM_Client_State_Automation_Unavailable,
        reserved1=0xFF,
        reserved2=0xFF,
        reserved3=0xFF,
        reserved4=0xFF,
        reserved5=0xFF,
    )
    # loop = asyncio.get_event_loop()
    print("Starting TIM status messages")
    tim_status_task = asyncio.create_task(tim_cyclic_client_status_thread())

    # Send Function Support Request
    function_support_request_msg = Tim_Operation_Message_t(
        data=Tim_Operation_Message_t.Data_t(
            message_code=Tim_Client_Message_Code.TIM_FunctionsAssignmentRequest,
            message=Tim_Operation_Message_t.Data_t.Message_t(
                function_support_request=Tim_FunctionsSupportRequest_t(
                    reserved0=0xFF,
                    reserved1=0xFF,
                    reserved2=0xFF,
                    reserved3=0xFF,
                    reserved4=0xFF,
                    reserved5=0xFF,
                    reserved6=0xFF,
                )
            ),
        )
    )
    isobus_send_tim_msg(function_support_request_msg.raw)

    # Await TIM_ConnectionVersionResponse
    # global tim_server_version
    # while tim_server_version is None:
    # await asyncio.sleep(0.1)
    # print(f"Connected to TIM server running version {tim_server_version}")

    # Setup receiving cyclic server status messages
    return 0


async def cyclic_tim_msg():
    while True:
        await asyncio.sleep(0.1)


auth_server_device_certificate = None
auth_server_random_challenge = None
auth_client_random_challenge = None
auth_server_status = None
auth_client_status = None
auth_shared_secret = None
auth_shared_key = None

auth_client_device_certificate = None
auth_client_manufacturer_certificate = None
auth_client_manufacturer_series_ceritificate = None
auth_server_device_certificate = None
auth_server_manufacturer_certificate = None
auth_server_manufacturer_series_ceritificate = None

auth_is_authenticated = False
auth_status_task = None
auth_server_status = None
auth_client_status = None
auth_client_substatus = None


def generate_random_challenge():
    # TODO: Implement the logic to generate a random challenge
    # For demonstration purposes, a test challenge is provided
    test_challenge = [
        0x01,
        0x02,
        0x03,
        0x04,
        0x05,
        0x06,
        0x07,
        0x08,
        0x09,
        0x0A,
        0x0B,
        0x0C,
        0x0D,
        0x0E,
        0x0F,
        0x10,
        0x11,
        0x12,
        0x13,
        0x14,
        0x15,
        0x16,
        0x17,
        0x18,
        0x19,
        0x1A,
        0x1B,
        0x1C,
        0x1D,
        0x1E,
        0x1F,
        0x20,
    ]
    return test_challenge


def auth_random_challenge_request_handler(msg):
    global auth_client_random_challenge
    response = Auth_Message_t(
        data=Auth_Message_t.Data_t(
            message_code=Auth_Message_Code.Auth_Message_Code_ClientRandomChallenge,
            message=Auth_Message_t.Data_t.Message_t(
                client_random_challenge_response=Auth_ClientRandomChallengeResponse_t(
                    error_code=0x0,
                    authentication_type=0x0,
                    length_lsb=32,
                    length_msb=0,
                    challenge=(ctypes.c_ubyte * 32)(*auth_client_random_challenge),
                )
            ),
        )
    )
    isobus_send_auth_msg(response.raw, 40)
    print("Sent random challenge response")


def auth_random_challenge_response_handler(msg):
    global auth_server_random_challenge
    auth_server_random_challenge = msg.data.message.Server_random_challenge_response.challenge
    print("Received server random challenge response")


def auth_client_certificate_request_handler(msg):
    global auth_client_device_certificate
    global auth_client_manufacturer_certificate
    global auth_client_manufacturer_series_ceritificate
    certificate_type = msg.client_certificate_request.certificate_type
    response = Auth_Message_t(
        data=Auth_Message_t.Data_t(
            message_code=Auth_Message_Code.Auth_Message_Code_ClientCertificate,
            message=Auth_Message_t.Data_t.Message_t(
                client_certificate_response=Auth_ClientCertificateResponse_t(
                    error_code=0x00,
                    authentication_type=0x0,
                    certificate_format=0x00,
                    certificate_type=certificate_type,
                    length_lsb=32,
                    length_msb=0,
                )
            ),
        )
    )
    if certificate_type == Auth_CertificateType.Auth_CertificateType_Device:
        response.data.message.client_certificate_response.certificate = auth_client_device_certificate
        pass
    elif certificate_type == Auth_CertificateType.Auth_CertificateType_Manufacturer:
        response.data.message.client_certificate_response.certificate = auth_client_manufacturer_certificate
        pass
    elif certificate_type == Auth_CertificateType.Auth_CertificateType_Manufacturer_Series:
        response.data.message.client_certificate_response.certificate = auth_client_manufacturer_series_ceritificate
        pass
    else:
        print("Error: Unhandled client certificate request")


def auth_server_certificate_request_handler(msg):
    global auth_server_device_certificate
    global auth_server_manufacturer_certificate
    global auth_server_manufacturer_series_ceritificate


async def auth_cyclic_status_thread():
    global auth_client_status
    global auth_is_authenticated
    while not auth_is_authenticated:
        msg = Auth_Message_t(
            data=Auth_Message_t.Data_t(
                message_code=Auth_Message_Code.Auth_Message_Code_ClientAuthenticationStatus,
                message=Auth_Message_t.Data_t.Message_t(client_authentication_status=auth_client_status),
            )
        )
        isobus_send_auth_msg(msg.raw, 8)
        await asyncio.sleep(0.1)


async def auth_init():
    # Setup handlers
    await j1939_add_auth_handler(
        auth_random_challenge_request_handler, Auth_Message_Code.Auth_Message_Code_ClientRandomChallenge
    )
    await j1939_add_auth_handler(
        auth_random_challenge_response_handler, Auth_Message_Code.Auth_Message_Code_ServerRandomChallenge
    )

    # Load certificates

    # Send Auth status cyclic message
    global auth_client_status
    global auth_client_substatus
    auth_client_substatus = Auth_Substatus(
        substatus=Auth_Substatus.SubstatusBits(
            challenged_signed=0b0,
            device_certificate_valid=0b0,
            manufacturer_series_certificate_valid=0b0,
            manufacturer_certificate_valid=0b0,
            testlab_certificate_valid=0b0,
            lwa_possible=0b0,
            restart_authentication=0b1,
        )
    )
    auth_client_status = Auth_ClientAuthenticationStatus_t(
        error_code=0x0,
        authentication_status=Auth_Status.Auth_Status_Not_Authenticated,
        authentication_type=0x0,
        authentication_substatus=auth_client_substatus,
        reserved0=0xFF,
        reserved1=0xFF,
        reserved2=0xFF,
        reserved3=0xFF,
    )
    global auth_status_task
    auth_status_task = asyncio.create_task(auth_cyclic_status_thread())
    print("Auth init")
    return 0


async def auth_restart():
    print("Auth restart")
    global auth_is_authenticated
    auth_is_authenticated = False

    # Set auth restart bit
    auth_client_status.authentication_substatus.substatus.restart_authentication = 0b1

    # Start generating random challenge
    global auth_client_random_challenge
    auth_client_random_challenge = generate_random_challenge()

    await asyncio.sleep(0.1)

    # Send server random challenge request
    msg = Auth_Message_t(
        data=Auth_Message_t.Data_t(
            message_code=Auth_Message_Code.Auth_Message_Code_ServerRandomChallenge,
            message=Auth_Message_t.Data_t.Message_t(
                server_random_challenge_request=Auth_ServerRandomChallengeRequest_t(authentication_type=0b0)
            ),
        )
    )
    print("Sending random challenge request")
    isobus_send_auth_msg(msg.raw, 8)
    # Send server certificate requests
    # Wait for all server certificates
    # Compute shared secret
    # Compute shared key
    # Sign challenge with key
    # Set valid certificate status
    # Wait for valid certificate status
    # Request signed challenge
    # Set status authenticated
    return 0


def initialize_can_bus():
    global bus
    # Create a CAN bus instance using the SocketCAN interface 'can1'
    bus = can.Bus(channel="can1", bustype="socketcan")
    return bus


async def send_can_message(bus):
    # Define the CAN message to be sent
    can_message = can.Message(
        arbitration_id=0x123,  # Arbitration ID of the CAN message
        data=[0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88],  # Data bytes
        is_extended_id=False,  # Set to True if using 29-bit extended ID
    )

    # Send the CAN message cyclically at 100 ms intervals
    while True:
        bus.send(can_message)
        await asyncio.sleep(0.1)  # Sleep for 100 ms


TIM_SKIP_AUTH = True


async def main():
    # loop = asyncio.get_event_loop()

    await j1939_init()
    await tim_init()
    if not TIM_SKIP_AUTH:
        await auth_init()
        await auth_restart()

    tasks = [j1939_rx_task, tim_status_task]
    await asyncio.gather(*tasks)

    # Create a task for sending CAN messages
    # can_task = loop.create_task(send_can_message(bus))

    # Wait for the task to finish
    # await can_task


# Run the main program
asyncio.run(main())


"""
# Function to generate random challenge
def generate_random_challenge():
    # TODO: Implement the logic to generate a random challenge
    # For demonstration purposes, a test challenge is provided
    test_challenge = [
        0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
        0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10,
        0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
        0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F, 0x20,
    ]
    return test_challenge

# Function to sign the challenge
def sign_challenge(challenge, lwa_key):
    challengeSz = len(challenge)
    mac = (c_ubyte * 16)()
    macSz = c_size_t(16)
    key = (c_ubyte * 16)(*lwa_key)
    keySz = c_size_t(16)
    ret = wc_AesCmacGenerate(mac, byref(macSz), challenge, byref(c_size_t(challengeSz)), key, byref(keySz))
    if ret != 0:
        print("Failed to compute CMAC")
    signed_challenge_buf = bytearray(mac)
    return signed_challenge_buf

def auth_restart_authentication():
    # Start sending cyclic status updates
    client_status_message.data.message.client_authentication_status.authentication_substatus = auth_substatus.raw
    client_status_sem = threading.Semaphore(0)
    isobus_start_cyclic_auth_msg(client_status_sem, client_status_message.raw)

    # Get server authentication status
    server_status_msg = Auth_Message_t()
    isobus_await_auth_msg(Auth_Message_Code_ServerAuthenticationStatus, server_status_msg.raw, 100)

    # Update client status
    auth_substatus.substatus.restart_authentication = 0b0
    client_status_message.data.message.client_authentication_status.authentication_substatus = auth_substatus.raw

    # Phase 1: LwA key table check and random challenge generation

    # Receive client random challenge request
    client_random_challenge_request_msg = Auth_Message_t()
    isobus_await_auth_msg(Auth_Message_Code_ClientRandomChallenge, client_random_challenge_request_msg.raw, 100)

    # Send server random challenge request
    server_random_challenge_request_msg = Auth_Message_t()
    server_random_challenge_request_msg.data.message_code = Auth_Message_Code_ServerRandomChallenge
    server_random_challenge_request_msg.data.message.server_random_challenge_request.authentication_type = 0x00
    isobus_send_auth_msg(server_random_challenge_request_msg.raw, 8)

    # Send client random challenge response
    client_random_challenge_response_msg = Auth_Message_t()
    client_random_challenge_response_msg.data.message_code = Auth_Message_Code_ClientRandomChallenge
    client_random_challenge_response_msg.data.message.client_random_challenge_response.error_code = 0x00
    client_random_challenge_response_msg.data.message.client_random_challenge_response.authentication_type = 0x00
    client_random_challenge_response_msg.data.message.client_random_challenge_response.length_lsb = 32

    client_random_challenge = generate_random_challenge()
    isobus_send_auth_msg(client_random_challenge_response_msg.raw + client_random_challenge, 40)

    # Receive server random challenge request

    # Send client random challenge response

    # Receive server random challenge response
    server_random_challenge = []

    # Rest of the function logic goes here...

    # Example delay for demonstration purposes
    time.sleep(1)
"""

# type: ignore
# mypy: ignore-errors
import ctypes

u8 = ctypes.c_uint8
u16 = ctypes.c_uint16
u32 = ctypes.c_uint32


J1939_PGN_ID_ADDR_REQUEST = 0xEA
J1939_PGN_ID_ADDR_CLAIMED = 0xEE
J1939_PGN_TIM_SERVER = 0x23
J1939_PGN_TIM_CLIENT = 0x24
J1939_PGN_AUTH_CLIENT = 0x6F
J1939_PGN_AUTH_SERVER = 0x70

J1939_CONFIG_ID_NUMBER = 0
J1939_CONFIG_MANUFACTURER_CODE = 0x745
J1939_CONFIG_ECU_INSTANCE = 0
J1939_CONFIG_FUNCTION_INSTANCE = 5
J1939_CONFIG_FUNCTION = 0
J1939_CONFIG_VEHICLE_SYSTEM_2 = 0
J1939_CONFIG_VEHICLE_SYSTEM_INSTANCE = 0
J1939_CONFIG_INDUSTRY_GROUP = 2
J1939_CONFIG_ARBITRARY_ADDRESS_CAPABLE = 1
J1939_CONFIG_ADDRESS = 5
J1939_CONFIG_TIM_SERVER_ADDRESS = 0xED


class IdData(ctypes.LittleEndianStructure):
    _fields_ = [
        ("source", u8, 8),
        ("ps", u8, 8),
        ("pf", u8, 8),
        ("dp", u8, 1),
        ("reserved", u8, 1),
        ("priority", u8, 3),
    ]


class Id(ctypes.Union):
    _anonymous_ = ("id",)
    _fields_ = [("id", IdData), ("raw", u32)]


class J1939_Pgn_Address_Claim_Data_Data(ctypes.LittleEndianStructure):
    _fields_ = [
        ("identity", u32, 21),
        ("manufacturer_code", u16, 11),
        ("ecu_instance", u8, 3),
        ("function_instance", u8, 5),
        ("function", u8, 8),
        ("reserved", u8, 1),
        ("vehicle_system", u8, 7),
        ("vehicle_system_instance", u8, 4),
        ("industry_group", u8, 3),
        ("arbitrary_address_compatible", u8, 1),
    ]


class J1939_Pgn_Address_Claim_Data(ctypes.Union):
    _anonymous_ = (("data"),)
    _fields_ = [("data", J1939_Pgn_Address_Claim_Data_Data), ("raw", u8 * 8)]


id = Id()
id.priority = 6
id.dp = 0
id.ps = 255
id.pf = J1939_PGN_ID_ADDR_REQUEST
id.source = J1939_CONFIG_ADDRESS
print(f"Request address : {hex(id.raw)[2:]}#00EE00")

id = Id()
id.priority = 6
id.dp = 0
id.ps = 255
id.pf = J1939_PGN_ID_ADDR_CLAIMED
id.source = J1939_CONFIG_ADDRESS

data = J1939_Pgn_Address_Claim_Data()
data.identity = J1939_CONFIG_ID_NUMBER
data.manufacturer_code = J1939_CONFIG_MANUFACTURER_CODE
data.ecu_instance = J1939_CONFIG_ECU_INSTANCE
data.function_instance = J1939_CONFIG_FUNCTION_INSTANCE
data.function = J1939_CONFIG_FUNCTION
data.vehicle_system = J1939_CONFIG_VEHICLE_SYSTEM_2
data.vehicle_system_instance = J1939_CONFIG_VEHICLE_SYSTEM_INSTANCE
data.industry_group = J1939_CONFIG_INDUSTRY_GROUP
data.arbitrary_address_compatible = J1939_CONFIG_ARBITRARY_ADDRESS_CAPABLE
print(f"Claim address : {hex(id.raw)[2:]}#{''.join([hex(i)[2:] for i in data.raw])}")


# J1939 claim address
# cansend can1 18eaff05#00EE00; sleep 0.001; cansend can1 18eeff05#00002800a0


# TIM Connection Version Request


class Tim_Connection_Version_Request_Data(ctypes.BigEndianStructure):
    _fields_ = [
        ("message_code", u8, 8),
        ("reserved0", u8, 8),
        ("implemented_tim_version_number", u8, 8),
        ("minimum_tim_version_number", u8, 8),
        ("reserved1", u8, 8),
        ("reserved2", u8, 8),
        ("reserved3", u8, 8),
        ("reserved4", u8, 8),
    ]


class Tim_Connection_Version_Request(ctypes.Union):
    _anonymous_ = (("data"),)
    _fields_ = [("data", Tim_Connection_Version_Request_Data), ("raw", u8 * 8)]


id = Id()
id.priority = 6
id.dp = 0
id.ps = J1939_CONFIG_TIM_SERVER_ADDRESS
id.pf = J1939_PGN_TIM_CLIENT
id.source = J1939_CONFIG_ADDRESS

data = Tim_Connection_Version_Request()
data.message_code = 0xF6
data.reserved0 = 0xFF
data.implemented_tim_version_number = 0x01
data.minimum_tim_version_number = 0x01
data.reserved1 = 0xFF
data.reserved2 = 0xFF
data.reserved3 = 0xFF
data.reserved4 = 0xFF

print(f"{1:#04x}")

# cansend can1 1824ed05#F6FF0101FFFFFFFF

print(f"TIM Connection Version Request : {hex(id.raw)[2:]}#{''.join([hex(i)[2:] for i in data.raw])}")

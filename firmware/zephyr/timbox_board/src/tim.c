#include "tim.h"
#include <logging/log.h>
LOG_MODULE_REGISTER(tim, CONFIG_APP_LOG_LEVEL);

struct k_sem tim_client_status_sem;
Tim_Operation_Message_t tim_client_status_msg = {
    .data = {.message_code = TIM_ClientStatus_Msg,
             .message.client_status = {.heartbeat_counter = 0,
                                       .tim_client_state = TIM_Client_State_Automation_Unavailable}}};

// SECTION: cyclic tim status message thread
K_THREAD_STACK_DEFINE(cyclic_tim_stack_area, THREAD_STACK_SIZE);
struct k_thread cyclic_tim_thread_data;
k_tid_t cyclic_tim_tid;

void cyclic_tim_msg(struct k_sem *sem, uint8_t *data, void *p3) {
  while (1) {
    isobus_send_tim_msg(data);
    k_msleep(100);
    // increment heartbeat
    tim_client_status_msg.data.message.client_status.heartbeat_counter += 1;
  }
}

int start_cyclic_tim_msg(struct k_sem *sem, uint8_t *data) {
  // create thread
  cyclic_tim_tid =
      k_thread_create(&cyclic_tim_thread_data, cyclic_tim_stack_area, K_THREAD_STACK_SIZEOF(cyclic_tim_stack_area),
                      cyclic_tim_msg, sem, data, NULL, THREAD_PRIORITY, 0, K_NO_WAIT);

  return 0;
}

// SECTION: tim initialization
int tim_init() {
  Tim_Operation_Message_t tim_msg = {
      .data = {
          .message_code = TIM_ConnectionVersionRequest,
          .message = {.connection_version_request = {.implemented_tim_version_number = TIM_CONFIG_IMPLEMENTED_VERSION,
                                                     .minimum_tim_version_number = TIM_CONFIG_MINIMUM_VERSION}}}};
  Tim_Operation_Message_t response_msg_buf;
  int ret = 1;
  while (ret != 0) {
    isobus_send_tim_msg(tim_msg.raw);
    ret = isobus_await_tim_msg(TIM_ConnectionVersionResponse, response_msg_buf.raw, 100);
    if (ret)
      k_sleep(K_MSEC(1000));
  }
  LOG_INF("TIM Connection Version: %d",
          response_msg_buf.data.message.connection_version_response.tim_connection_version);

  // Start cyclic status message
  k_sem_init(&tim_client_status_sem, 0, 1);
  start_cyclic_tim_msg(&tim_client_status_sem, &tim_client_status_msg.raw);
  return 0;
}

int tim_set_client_status(Tim_Client_State state) {
  tim_client_status_msg.data.message.client_status.tim_client_state = state;
  return 0;
}
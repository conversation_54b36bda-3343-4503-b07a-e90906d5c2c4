#include <logging/log.h>
LOG_MODULE_REGISTER(udp_server, CONFIG_APP_LOG_LEVEL);

#include <errno.h>
#include <lib/udp/udp.h>
#include <pb_decode.h>
#include <pb_encode.h>
#include <sys/reboot.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

#include "udp_server.h"

#if IS_ENABLED(CONFIG_NET_TC_THREAD_COOPERATIVE)
#define THREAD_PRIORITY K_PRIO_COOP(CONFIG_NUM_COOP_PRIORITIES - 1)
#else
#define THREAD_PRIORITY K_PRIO_PREEMPT(8)
#endif

#define RECV_BUFFER_SIZE 1280

static void process_udp();

K_THREAD_DEFINE(udp_thread_id, RECV_BUFFER_SIZE + 1024, process_udp, NULL, NULL, NULL, THREAD_PRIORITY, 0,
                K_TICKS_FOREVER);
UDP_SERVER_DEFINE(npb_udp_server, CONFIG_PB_REQUESTS_BUFFER_SIZE, 1024, THREAD_PRIORITY);

static void serve_request(uint8_t *data, uint16_t received, udp_msg_metadata *metadata) {}

static void process_udp() {
  for (;;) {
    uint8_t *data;
    udp_msg_metadata metadata;
    uint16_t received = udp_get_data_no_copy(&npb_udp_server, &data, &metadata, 0);
    serve_request(data, received, &metadata);
    udp_get_data_release(&npb_udp_server, data);
  }
}

void start_udp_server() {
  UDP_SERVER_START(npb_udp_server, CONFIG_APP_UDP_PORT);
  k_thread_name_set(udp_thread_id, "udp");
  k_thread_start(udp_thread_id);
}

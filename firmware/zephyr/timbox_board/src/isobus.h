#pragma once

#include "j1939.h"
#include "zephyr.h"
#include <stdint.h>
#include <stdlib.h>
#include <utils/carbon_response_codes.h>

int isobus_send_tim_msg(uint8_t *data);
int isobus_send_auth_msg(uint8_t *data, uint8_t len);
int isobus_await_tim_msg(uint8_t message_code, uint8_t *msgbuf, uint16_t timeout_msec);
int isobus_await_auth_msg(uint8_t message_code, uint8_t *msgbuf, uint16_t timeout_msec);
int isobus_start_cyclic_auth_msg(struct k_sem *sem, uint8_t *data);
int isobus_stop_cyclic_auth_msg();

#define THREAD_STACK_SIZE 1024
#define THREAD_PRIORITY 5
// extern void isobus_cyclic_auth_msg(void *, void *, void *);
extern void isobus_cyclic_auth_msg(struct k_sem *sem, uint8_t *data, void *p3);
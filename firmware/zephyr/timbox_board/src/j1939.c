#include <logging/log.h>
LOG_MODULE_REGISTER(j1939, CONFIG_APP_LOG_LEVEL);

#include "j1939.h"
#include <device.h>
#include <devicetree.h>
#include <drivers/can.h>
#include <string.h>
#include <utils/handle_errors.h>

#define MAILBOX_SIZE (10)
typedef struct {
  // char __aligned(4) buffer[MAILBOX_SIZE * sizeof(J1939_Pgn_Packet_t)];
  J1939_Pgn_Packet_t buffer[MAILBOX_SIZE];
  struct k_msgq msgq;
} J1939_Mailbox;

typedef struct J1939_t {
  const struct device *can;
  J1939_Mailbox mailbox;
  uint32_t address;
} J1939;

J1939 j1939;

void rx_callback(const struct device *dev, struct zcan_frame *msg, void *arg) {
  LOG_INF("RX %d", msg->data[1]);
  ARG_UNUSED(dev);

  J1939_Pgn_Packet_t j1939_msg;
  uint8_t data[8];
  memcpy(&data[0], &(msg->data[0]), 8);

  // Parse message as J1939 PGN
  j1939_msg.dlc = msg->dlc;
  j1939_msg.data = msg->data;
  j1939_msg.id.raw = msg->id;

  // TODO: filter out all non-TIM messages

  // DEBUG {
  if (j1939_msg.data[0] == 0xF6) {
    LOG_INF("DEBUG %d", msg->data[2]);
    j1939_msg.data = data;
  } else {
    return;
  }
  // }DEBUG

  // Put message in message queue
  while (k_msgq_put(&j1939.mailbox.msgq, &j1939_msg, K_NO_WAIT) != 0) {
    k_msgq_purge(&j1939.mailbox.msgq);
  }
}

int j1939_await_pgn(uint32_t pgn, J1939_Pgn_Packet_t *pkt, uint16_t timeout_ms) {
  int64_t start_time = k_uptime_get();
  int64_t remaining = (int64_t)timeout_ms;
  while (remaining > 0) {
    if (k_msgq_get(&j1939.mailbox.msgq, pkt, K_MSEC(remaining)) == 0) {
      if (pkt->id.id.pf == pgn) {
        return 0;
      }
    }
    int64_t now = k_uptime_get();
    remaining -= (now - start_time);
    start_time = now;
  }
  return 1;
}

void dummy_rx_callback(const struct device *dev, struct zcan_frame *msg, void *arg) {
  LOG_INF("RX dummy");
  return;
}

int j1939_init() {
  j1939.can = DEVICE_DT_GET(DT_CHOSEN(zephyr_can_primary));
  HANDLE_UNLIKELY_BOOL(device_is_ready(j1939.can), ENODEV);
  k_msgq_init(&j1939.mailbox.msgq, j1939.mailbox.buffer, sizeof(J1939_Pgn_Packet_t), MAILBOX_SIZE);

  const struct zcan_filter my_filter = {
      .id_type = CAN_EXTENDED_IDENTIFIER, .rtr = CAN_DATAFRAME, .id = 0x0, .rtr_mask = 1, .id_mask = 0x00};

  int filter_id;

  filter_id = can_add_rx_filter(j1939.can, rx_callback, NULL, &my_filter);
  if (filter_id < 0) {
    LOG_ERR("Unable to attach isr [%d]", filter_id);
  }
  // DEBUG{
  const struct zcan_filter everything_filter = {
      .id_type = CAN_EXTENDED_IDENTIFIER, .rtr = CAN_DATAFRAME, .id = 0x0, .rtr_mask = 0, .id_mask = 0};
  filter_id = can_add_rx_filter(j1939.can, dummy_rx_callback, NULL, &everything_filter);
  // }DEBUG
  can_set_mode(j1939.can, CAN_MODE_NORMAL);
  return 0;
}

void send_can_frame(struct zcan_frame *frame) {
  int ret = 0;
  for (int i = 0; i < 5; i++) {
    ret = can_send(j1939.can, frame, K_MSEC(100), NULL, NULL);
    if (ret == 0) {
      return 0;
    }
  }
  LOG_ERR("Sending msg id: [%u] failed, err %d", frame->id, ret);
}

void j1939_send_pgn(J1939_Pgn_Packet_t *pkt) {
  struct zcan_frame frame = {
      .id_type = CAN_EXTENDED_IDENTIFIER,
      .rtr = 0,
      .id = pkt->id.raw,
      .fd = 0,
      .brs = 0,
      .dlc = pkt->dlc,
  };
  LOG_INF("Sent J1939 ID %d", frame.id);
  memcpy(frame.data, pkt->data, pkt->dlc);
  send_can_frame(&frame);
}

int j1939_test() {
  J1939_Pgn_Address_Claim_Data_t data = {.data.identity = 0xFF, .data.arbitrary_addresss_compatible = 0b1};

  uint8_t data_raw[8];

  J1939_Pgn_Packet_t j1939_pkt = {
      .id = {.id = {.priority = 1, .ps = 255, .pf = J1939_PGN_ID_ADDR_CLAIMED, .source = J1939_CONFIG_ADDRESS}},
      .dlc = 8,
      .data = data.raw};
  j1939_send_pgn(&j1939_pkt);
  return 0;
}

int j1939_claim_address() {

  uint8_t address_request_data[3] = {0x00, 0xEE, 0x00};
  J1939_Pgn_Packet_t address_request_packet = {
      .id =
          {.id = {.priority = 6, .dp = 0, .ps = 255, .pf = J1939_PGN_ID_ADDR_REQUEST, .source = J1939_CONFIG_ADDRESS}},
      .dlc = 3,
      .data = address_request_data};

  j1939_send_pgn(&address_request_packet);

  k_sleep(K_MSEC(10));

  J1939_Pgn_Address_Claim_Data_t address_claimed_data = {
      .data = {.identity = J1939_CONFIG_ID_NUMBER,
               .manufacturer_code = J1939_CONFIG_MANUFACTURER_CODE,
               .ecu_instance = J1939_CONFIG_ECU_INSTANCE,
               .function_instance = J1939_CONFIG_FUNCTION_INSTANCE,
               .function = J1939_CONFIG_FUNCTION,
               .vehicle_system = J1939_CONFIG_VEHICLE_SYSTEM_2,
               .vehicle_system_instance = J1939_CONFIG_VEHICLE_SYSTEM_INSTANCE,
               .industry_group = J1939_CONFIG_INDUSTRY_GROUP,
               .arbitrary_addresss_compatible = J1939_CONFIG_ARBITRARY_ADDRESS_CAPABLE}};

  J1939_Pgn_Packet_t address_claimed_packet = {
      .id =
          {.id = {.priority = 6, .dp = 0, .ps = 255, .pf = J1939_PGN_ID_ADDR_CLAIMED, .source = J1939_CONFIG_ADDRESS}},
      .dlc = 8,
      .data = address_claimed_data.raw};
  j1939_send_pgn(&address_claimed_packet);

  // TODO: check that address request was accepted
  j1939.address = J1939_CONFIG_ADDRESS;

  return 0;
}
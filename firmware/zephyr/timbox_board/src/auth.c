#include "auth.h"

Auth_State_t auth_state = {.is_authenticated = false,
                           .client_authentication_status = {
                               .error_code = 0,
                               .authentication_status = Auth_Status_Not_Authenticated,
                               .authentication_substatus = 0,
                           }};

struct k_sem client_status_sem;
Auth_Substatus auth_substatus = {.substatus = {.challenge_signed = 0b0,
                                               .device_certificate_valid = 0b0,
                                               .manufacturer_series_certificate_valid = 0b0,
                                               .manufacturer_certificate_valid = 0b0,
                                               .testlab_certificate_valid = 0b0,
                                               .lwa_possible = 0b0,
                                               .restart_authentication = 0b1}};
Auth_Message_t client_status_message = {
    .data = {.message_code = Auth_Message_Code_ClientAuthenticationStatus,
             .message.client_authentication_status = {.error_code = 0x00,
                                                      .authentication_status = Auth_Status_Not_Authenticated,
                                                      .authentication_substatus = 0x00,
                                                      .authentication_type = 0x00,
                                                      .reserved0 = 0xFF,
                                                      .reserved1 = 0xFF,
                                                      .reserved2 = 0xFF,
                                                      .reserved3 = 0xFF}}};

int auth_init() { return 0; }

int sign_challenge(uint8_t *challenge, uint8_t *lwa_key, uint8_t *signed_challenge_buf) {
  uint8_t challengeSz = CHALLENGE_LEN;
  uint8_t mac[SIGNED_CHALLENGE_LEN];
  uint8_t macSz = SIGNED_CHALLENGE_LEN;
  uint8_t key[LWA_KEY_LEN];
  uint8_t keySz = LWA_KEY_LEN;
  int ret = wc_AesCmacGenerate(mac, &macSz, challenge, &challengeSz, key, &keySz);
  if (ret != 0) {
    LOG_ERR("Failed to compute CMAC");
  }
  memcpy(signed_challenge_buf, mac, macSz);
}

int generate_random_challenge(uint8_t *buf) {
  // TODO: generate a random 32 byte number
  uint8_t test_challenge = {
      0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10,
      0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F, 0x20,
  } mempcpy(buf, &test_challenge, CHALLENGE_LEN);
}

int auth_restart_authentication() {
  // Start sending cyclic status updates
  client_status_message.data.message.client_authentication_status.authentication_substatus = auth_substatus.raw;
  k_sem_init(&client_status_sem, 0, 1);
  isobus_start_cyclic_auth_msg(&client_status_sem, &client_status_message.raw);

  // Get server authentication status
  Auth_Message_t server_status_msg;
  isobus_await_auth_msg(Auth_Message_Code_ServerAuthenticationStatus, &server_status_msg.raw, 100);

  // Update client status
  auth_substatus.substatus.restart_authentication = 0b0;
  client_status_message.data.message.client_authentication_status.authentication_substatus = auth_substatus.raw;

  /**
   * #######################################################
   * Phase 1: LwA key table check and random challenge generation
   * #######################################################
   * */

  // Receive client random challenge request
  Auth_Message_t client_random_challenge_request_msg;
  isobus_await_auth_msg(Auth_Message_Code_ClientRandomChallenge, &client_random_challenge_request_msg.raw, 100);

  // Send server random challenge request
  Auth_Message_t server_random_challenge_request_msg = {
      .data = {.message_code = Auth_Message_Code_ServerRandomChallenge,
               .message.server_random_challenge_request = {.authentication_type = 0x00}}};
  isobus_send_auth_msg(&server_random_challenge_request_msg.raw, 8);

  // Send client random challenge response
  Auth_Message_t client_random_challenge_response_msg = {
      .data = {.message_code = Auth_Message_Code_ClientRandomChallenge,
               .message.client_random_challenge_response = {
                   .error_code = 0x00,
                   .authentication_type = 0x00,
                   .length_lsb = 32,
               }}};
  uint8_t client_random_challenge[CHALLENGE_LEN];
  generate_random_challenge(&client_random_challenge);
  isobus_send_auth_msg(&client_random_challenge_response_msg.raw, 40);

  // Receive server random challenge request

  // Send client random challenge response

  // Receive server random challenge response
  uint8_t server_random_challenge[CHALLENGE_LEN];

  // TODO: Check for LwA

  /**
   * #######################################################
   * Phase 2: Certificate Exchange and Certificate Validation
   * #######################################################
   * */

  // ## Step (1a) ## Request certificates
  // Send Client Certificate Requests
  // Receive server testlab cert
  // Receive server manufacturer cert
  // Receive server device cert

  // ## Step (1b) ## Respond to certificate requests
  // Await Server Certificate Requests
  // Send testlab cert
  // Send device cert
  // Send manufacturer cert
  // TODO: ^^ put this in dedicated auth handler called by j1939 rx

  // ## Step (2) ## Check certificates against local CRL

  // ## Step (3a) ## Verify certificate chain
  // Skip

  // ## Step (3b) ## Check ConformanceTestID and supported functions match TIM_FunctionsSupportResponse
  // Skip

  // ## Step (4) ## Check ISO information
  // Skip

  // ## Step (5) ## Notify certificate chain is finsished successfullly

  /**
   * #######################################################
   * Phase 3: ECDH and Key Derivation
   * #######################################################
   * */

  // ## Step (1) ## Use the generated challenge from phase 1
  // Already done in phase 1

  // ## Step (2a) ## After reaching SYNC point, request challenges from eachother
  // Already done in phase 1

  // ## Step (2b) ## Response to challenge requests. If LwA agreed, skip steps 3..5 and start phase 4

  // ## Step (3) ## Server & client independently generate shared secret key
  // ECDH.ComputeSharedSecret

  // ## Step (4) ## Server & client independently compute their individual copies of the shared secret key
  // Derive LwA key using KDF

  // ## Step (5) ## Split LwA key into two parts

  /**
   * #######################################################
   * Phase 5: CMAC Authentication
   * #######################################################
   * */

  // ## Step (3) ## Response generation compute response for the challenge

  // ## Step (4a) ## Request signed challenge

  // ## Step (4b) ## Respond signed challenge after status signals computing CMAC finished

  // ## Step (5) ## Verify server signed challenge
  // TODO: skipping

  // ## Step (6) ## Send acknowledgement or error message

  // Stop sending cyclic status updates
  return 0;
}

void auth_test() {
  Auth_Message_t test_msg = {.data = {.message_code = Auth_Message_Code_ClientAuthenticationStatus,
                                      .message = {.empty = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}}}};
  test_msg.raw[1] = 0xFF;
  isobus_send_auth_msg(&test_msg.raw, 8);
}
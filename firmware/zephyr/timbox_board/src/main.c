#include <logging/log.h>
LOG_MODULE_REGISTER(main, CONFIG_APP_LOG_LEVEL);

#include "auth.h"
#include "isobus.h"
#include "j1939.h"
#include "lib/smp/smp.h"
#include "tim.h"
#include "udp_server.h"
#include <devicetree.h>
#include <drivers/can.h>
#include <lib/watchdog/watchdog.h>
#include <string.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

#define TEST_LOOP_RATE_MS 500

/** SECTION: CAN testing */

const struct device *can_dev;

void test_can_init() {
  LOG_INF("Setting up CAN device...");

  can_dev = DEVICE_DT_GET(DT_CHOSEN(zephyr_can_primary));
  HANDLE_UNLIKELY_BOOL(device_is_ready(can_dev), ENODEV);
}

void test_can() {
  struct zcan_frame frame = {.id_type = CAN_STANDARD_IDENTIFIER, .rtr = 0, .id = 0xFF, .fd = 0, .brs = 0, .dlc = 0};
  can_send(can_dev, &frame, K_MSEC(1), NULL, NULL);
}

static int boot() {
  LOG_INF("Timbox booting");

  // Initialize CAN bus
  HANDLE_UNLIKELY(j1939_init());

  // Claim J1939 address
  HANDLE_UNLIKELY(j1939_claim_address());

  // Initialize TIM
  HANDLE_UNLIKELY(tim_init());

  // Initialize Auth
  HANDLE_UNLIKELY(auth_init());

  return 0;
}

void main() {
  start_watchdog();
  start_smp_lib();
  HANDLE_CRITICAL(boot());
  start_udp_server();

  // Authenticate TIM Client with TIM Server
  // Start authentication
  auth_restart_authentication();

  // Set TIM client status to automation not ready
  // tim_set_client_status(TIM_Client_State_Automation_Not_Ready);

  // int auth_ret = auth_authenticate();

  LOG_INF("Starting test loop");
  for (;;) {
    k_msleep(TEST_LOOP_RATE_MS);
    // auth_test();
    // j1939_claim_address();
    // j1939_test();
    // test_can();
  }
}
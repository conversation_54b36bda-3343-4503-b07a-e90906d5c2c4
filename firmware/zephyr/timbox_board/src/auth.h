#pragma once

#include "isobus.h"
#include "zephyr.h"
#include <stdint.h>
#include <stdlib.h>

#define CHALLENGE_LEN (32)
#define SIGNED_CHALLENGE_LEN (16)
#define LWA_KEY_LEN (16)

/**
#define SERVO_THREAD_PRIORITY K_PRIO_PREEMPT(8)
#define SERVO_THREAD_STACK_SIZE (1024)

#define AUTH_DEFINE
    K_THREAD_DEFINE(auth_thread_id, AUTH_THREAD_STACK_SIZE, auth_run, NULL, NULL, NULL, AUTH_THREAD_PRIORITY, 0,
K_TICKS_FOREVER);

#define AUTH_START
    auth_init();
    k_thread_name_set(auth_thread_id, "auth");
    k_thread_start(auth_thread_id);
**/

// SECTION: Auth Codes

// TODO: auth error codes

typedef enum Auth_Status {
  Auth_Status_Not_Authenticated = 0x00,
  Auth_Status_Authenticated = 0x01,
  Auth_Status_Error = 0x0E,
  Auth_Status_Not_Available = 0x0F,
} Auth_Status;

typedef union Auth_Substatus {
  struct {
    uint8_t reserved : 1;
    uint8_t challenge_signed : 1;
    uint8_t device_certificate_valid : 1;
    uint8_t manufacturer_series_certificate_valid : 1;
    uint8_t manufacturer_certificate_valid : 1;
    uint8_t testlab_certificate_valid : 1;
    uint8_t lwa_possible : 1;
    uint8_t restart_authentication : 1;
  } substatus;
  uint8_t raw;
} Auth_Substatus;

typedef enum Auth_CertificateType {
  Auth_CertificateType_Aef_Root = 0x00,
  Auth_CertificateType_Testlab = 0x01,
  Auth_CertificateType_Manufacturer = 0x02,
  Auth_CertificateType_Manufacturer_Series = 0x03,
  Auth_CertificateType_Device = 0x04,
  Auth_CertificateType_Crl_Signing = 0x05,
  Auth_CertificateType_Crl = 0x06,
  Auth_CertificateType_Crl_SignSubCa = 0x07
} Auth_CertificateType;

// SECTION: Auth Messages

typedef enum Auth_Message_Code {
  Auth_Message_Code_ClientAuthenticationStatus = 0xFA,
  Auth_Message_Code_ServerAuthenticationStatus = 0xF9,
  Auth_Message_Code_ServerCertificate = 0x02,
  Auth_Message_Code_ClientCertificate = 0x03,
  Auth_Message_Code_ServerRandomChallenge = 0x04,
  Auth_Message_Code_ClientRandomChallenge = 0x05,
  Auth_Message_Code_ServerSignedChallenge = 0x06,
  Auth_Message_Code_ClientSignedChallenge = 0x07,
  Auth_Message_Code_ServerAuthenticationInfo = 0x08,
  Auth_Message_Code_ClientAuthenticationInfo = 0x09,
} Auth_Message_Code;

typedef struct Auth_ClientAuthenticationStatus_t {
  uint8_t error_code : 8;
  uint8_t authentication_status : 4;
  uint8_t authentication_type : 4;
  uint8_t authentication_substatus : 8;
  uint8_t reserved0 : 8;
  uint8_t reserved1 : 8;
  uint8_t reserved2 : 8;
  uint8_t reserved3 : 8;
} Auth_ClientAuthenticationStatus_t;

typedef struct Auth_ServerAuthenticationStatus_t {
  uint8_t error_code : 8;
  uint8_t authentication_status : 4;
  uint8_t authentication_type : 4;
  uint8_t authentication_substatus : 8;
  uint8_t reserved0 : 8;
  uint8_t reserved1 : 8;
  uint8_t reserved2 : 8;
  uint8_t reserved3 : 8;
} Auth_ServerAuthenticationStatus_t;

typedef struct Auth_ClientSignedChallengeRequest_t {
  uint8_t reserved0 : 8;
  uint8_t reserved1 : 4;
  uint8_t authentication_type : 4;
  uint8_t reserved2 : 8;
  uint8_t reserved3 : 8;
  uint8_t reserved4 : 8;
  uint8_t reserved5 : 8;
  uint8_t reserved6 : 8;
} Auth_ClientSignedChallengeRequest_t;

typedef struct Auth_ClientSignedChallengeResponse_t {
  uint8_t error_code : 8;
  uint8_t reserved1 : 4;
  uint8_t authentication_type : 4;
  uint8_t reserved2 : 8;
  uint8_t reserved3 : 8;
  uint8_t length_lsb : 8;
  uint8_t length_msb : 8;
  uint8_t signed_challenge : 8;
} Auth_ClientSignedChallengeResponse_t;

typedef struct Auth_ServerAuthenticationInfoRequest_t {
  uint8_t reserved0 : 8;
  uint8_t authentication_type : 4;
  uint8_t reserved1 : 8;
  uint8_t reserved2 : 8;
  uint8_t reserved3 : 8;
  uint8_t reserved4 : 8;
  uint8_t reserved5 : 8;
} Auth_ServerAuthenticationInfoRequest_t;

typedef struct Auth_ServerCertificateRequest_t {
  uint8_t reserved0 : 8;
  uint8_t reserved1 : 4;
  uint8_t authentication_type : 4;
  uint8_t certificate_format : 8; // only 0x00 (DER) is valid
  uint8_t certificate_type : 8;
  uint8_t reserved2 : 8;
  uint8_t reserved3 : 8;
  uint8_t reserved4 : 8;
} Auth_ServerCertificateRequest_t;

typedef struct Auth_ServerCertificateResponse_t {
  uint8_t error_code : 8;
  uint8_t authentication_type : 4;
  uint8_t certificate_format : 8; // only 0x00 (DER) is valid
  uint8_t certificate_type : 8;
  uint8_t length_lsb : 8;
  uint8_t length_msb : 8;
  uint8_t certificate : 8;
} Auth_ServerCertificateResponse_t;

typedef struct Auth_ServerRandomChallengeRequest_t {
  uint8_t reserved0 : 8;
  uint8_t reserved1 : 4;
  uint8_t authentication_type : 4;
  uint8_t reserved2 : 8;
  uint8_t reserved3 : 8;
  uint8_t reserved4 : 8;
  uint8_t reserved5 : 8;
  uint8_t reserved6 : 8;
} Auth_ServerRandomChallengeRequest_t;

typedef struct Auth_ServerRandomChallengeResponse_t {
  uint8_t error_code : 8;
  uint8_t reserved0 : 4;
  uint8_t authentication_type : 4;
  uint8_t reserved1 : 8;
  uint8_t reserved2 : 8;
  uint8_t length_lsb : 8;
  uint8_t length_msb : 8;
  uint8_t challenge[32];
} Auth_ServerRandomChallengeResponse_t;

typedef struct Auth_ClientRandomChallengeRequest_t {
  uint8_t reserved0 : 8;
  uint8_t reserved1 : 4;
  uint8_t authentication_type : 4;
  uint8_t reserved2 : 8;
  uint8_t reserved3 : 8;
  uint8_t reserved4 : 8;
  uint8_t reserved5 : 8;
  uint8_t reserved6 : 8;
} Auth_ClientRandomChallengeRequest_t;

typedef struct Auth_ClientRandomChallengeResponse_t {
  uint8_t error_code : 8;
  uint8_t reserved0 : 4;
  uint8_t authentication_type : 4;
  uint8_t reserved1 : 8;
  uint8_t reserved2 : 8;
  uint8_t length_lsb : 8;
  uint8_t length_msb : 8;
  uint8_t challenge[32];
} Auth_ClientRandomChallengeResponse_t;

typedef union Auth_Message_t {
  struct {
    uint8_t message_code : 8;
    union {
      uint8_t empty[7];
      Auth_ClientAuthenticationStatus_t client_authentication_status;
      Auth_ServerAuthenticationStatus_t server_authentication_status;
      Auth_ClientSignedChallengeRequest_t client_signed_challenge_request;
      Auth_ClientSignedChallengeResponse_t client_signed_challenge_response;
      Auth_ServerAuthenticationInfoRequest_t server_auth_info_request;
      Auth_ServerCertificateRequest_t server_certificate_request;
      Auth_ServerCertificateResponse_t server_certificate_response;
      Auth_ServerRandomChallengeRequest_t server_random_challenge_request;
      Auth_ServerRandomChallengeResponse_t server_random_challenge_response;
      Auth_ClientRandomChallengeRequest_t client_random_challenge_request;
      Auth_ClientRandomChallengeResponse_t client_random_challenge_response;
    } message;
  } data;
  uint8_t raw[40];
} Auth_Message_t;

typedef struct Auth_State_t {
  Auth_ClientAuthenticationStatus_t client_authentication_status;
  Auth_ServerAuthenticationStatus_t server_authentication_status;
  bool is_authenticated;
} Auth_State_t;

extern Auth_State_t auth_state;

int auth_init();
int auth_restart_authentication();
int auth_request_random_challenge();
void auth_test();
#pragma once

#include "isobus.h"
#include "zephyr.h"
#include <stdint.h>
#include <stdlib.h>
#include <utils/carbon_response_codes.h>

#define TIM_CONFIG_IMPLEMENTED_VERSION (1)
#define TIM_CONFIG_MINIMUM_VERSION (1)

// SECTION: Tim State Machine
typedef enum Tim_Client_State {
  TIM_Client_State_Automation_Unavailable = 0x00,
  TIM_Client_State_Automation_Not_Ready = 0x01,
  TIM_Client_State_Automation_Ready = 0x02,
  TIM_Client_State_Automation_Enabled = 0x03,
  TIM_Client_State_Automation_Active = 0x05,
  TIM_Client_State_Automation_Fault = 0x0D,
  TIM_Client_State_Error = 0x0E
} Tim_Client_State;

typedef enum Tim_Server_State {
  TIM_Server_State_Automation_Unavailable = 0x00,
  TIM_Server_State_Automation_Not_Ready = 0x01,
  TIM_Server_State_Automation_Ready = 0x02,
  TIM_Server_State_Automation_Enabled = 0x03,
  TIM_Server_State_Automation_Active = 0x05,
  TIM_Server_State_Automation_Fault = 0x0D,
  TIM_Server_State_Error = 0x0E
} Tim_Server_State;

typedef enum Tim_System_Operation_State {
  TIM_System_Operation_State_Requirements_Not_Fulfilled = 0x00,
  TIM_System_Operation_State_Requirements_Normal_Fulfilled = 0x01,
  TIM_System_Operation_State_Requirements_Standstill_Fulfilled = 0x02,
  TIM_System_Operation_State_Requirements_Stationary_Fulfilled = 0x03,
  TIM_System_Operation_State_Error = 0x0E,
  TIM_System_Operation_State_Not_Available = 0x0F
} Tim_System_Operation_State;

typedef enum Tim_System_State {
  TIM_System_State_No_Automation_Active = 0x01,
  TIM_System_State_Automation_Active = 0x05,
  TIM_System_State_Error = 0x0E,
  TIM_System_State_Not_Available = 0x0F
} Tim_System_State;

typedef enum Tim_Server_Master_Indication {
  Tim_Server_Master_Indication_Automation_Not_Allowed = 0x00,
  Tim_Server_Master_Indication_Automation_Allowed = 0x01,
  Tim_Server_Master_Indication_Error = 0x0E,
  Tim_Server_Master_Indication_Not_Available = 0x0F
} Tim_Server_Master_Indication;

// SECTION: Tim Message Codes (AEF 023 A.2.3)
typedef enum Tim_Server_Message_Code {
  TIM_FunctionsAssignmentStatusResponse = 0xF3,
  TIM_FunctionsSupportResponse = 0xF4,
  TIM_FunctionsAssignmentResponse = 0xF5,
  TIM_ConnectionVersionResponse = 0xF6,
  TIM_ClientVersionRequest = 0xF7,
  TIM_ServerVersionResponse = 0xF8,
  TIM_ServerStatus_Msg = 0xFA
} Tim_Server_Message_Code;

typedef enum Tim_Client_Message_Code {
  TIM_FunctionsAssignmentStatusRequest = 0xF3,
  TIM_FunctionsSupportRequest = 0xF4,
  TIM_FunctionsAssignmentRequest = 0xF5,
  TIM_ConnectionVersionRequest = 0xF6,
  TIM_ClientVersionResponse = 0xF7,
  TIM_ServerVersionRequest = 0xF8,
  TIM_ClientStatus_Msg = 0xF9
} Tim_Client_Message_Code;

// SECTION: TIM Status & Operation Messages
typedef struct Tim_Client_Version_Response_Message_t {
  uint8_t reserved0 : 8;
  uint8_t implemented_tim_version_number : 8;
  uint8_t minimum_tim_version_number : 8;
  uint8_t reserved1 : 8;
  uint8_t reserver2 : 8;
  uint8_t reserved3 : 8;
  uint8_t reserved4 : 8;
} Tim_Client_Version_Response_Message_t;

typedef struct Tim_Connection_Version_Request_Message_t {
  uint8_t reserved0 : 8;
  uint8_t implemented_tim_version_number : 8;
  uint8_t minimum_tim_version_number : 8;
  uint8_t reserved1 : 8;
  uint8_t reserved2 : 8;
  uint8_t reserved3 : 8;
  uint8_t reserved4 : 8;
} Tim_Connection_Version_Request_Message_t;

typedef struct Tim_Connection_Version_Response_Message_t {
  uint8_t error_code : 8;
  uint8_t tim_connection_version : 8;
  uint8_t reserved0 : 8;
  uint8_t reserved1 : 8;
  uint8_t reserved2 : 8;
  uint8_t reserved3 : 8;
  uint8_t reserved4 : 8;
  uint8_t reserved5 : 8;
} Tim_Connection_Version_Response_Message_t;

// SECTION: Tim Status Messages
typedef struct Tim_ServerStatus_Msg_t {
  uint8_t heartbeat_counter : 8;
  uint8_t tim_system_state : 4;
  uint8_t tim_server_master_indication : 4;
  uint8_t tim_server_operation_state : 4;
  uint8_t tim_server_state : 4;
  uint8_t reserved0 : 8;
  uint8_t reserved1 : 8;
  uint8_t reserved2 : 8;
  uint8_t reserved3 : 8;
} Tim_ServerStatus_Msg_t;

typedef struct Tim_ClientStatus_Msg_t {
  uint8_t heartbeat_counter : 8;
  uint8_t reserved0 : 4;
  uint8_t tim_client_state : 4;
  uint8_t reserved1 : 8;
  uint8_t reserved2 : 8;
  uint8_t reserved3 : 8;
  uint8_t reserved4 : 8;
  uint8_t reserved5 : 8;
} Tim_ClientStatus_Msg_t;

typedef union Tim_Operation_Message_t {
  struct {
    uint8_t message_code : 8;
    union {
      uint8_t empty[7];
      Tim_Client_Version_Response_Message_t client_version_response;
      Tim_Connection_Version_Request_Message_t connection_version_request;
      Tim_Connection_Version_Response_Message_t connection_version_response;
      Tim_ClientStatus_Msg_t client_status;
      Tim_ServerStatus_Msg_t server_status;
    } message;
  } data;
  uint8_t raw[8];
} Tim_Operation_Message_t;

int tim_init();
int tim_set_client_status(Tim_Client_State state);
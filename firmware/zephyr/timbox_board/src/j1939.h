#pragma once

#include <device.h>
#include <stdint.h>
#include <utils/carbon_response_codes.h>
#include <zephyr.h>

//#include <generated/lib/drivers/nanopb/proto/timbox.pb.h">

#define J1939_PGN_DATA_SIZE (8)

#define J1939_PGN_ID_ADDR_REQUEST (0xEA)
#define J1939_PGN_ID_ADDR_CLAIMED (0xEE)
#define J1939_PGN_TIM_SERVER (0x23)
#define J1939_PGN_TIM_CLIENT (0x24)
#define J1939_PGN_AUTH_CLIENT (0x6F)
#define J1939_PGN_AUTH_SERVER (0x70)

#define J1939_CONFIG_ID_NUMBER (0)
#define J1939_CONFIG_MANUFACTURER_CODE (0x745)
#define J1939_CONFIG_ECU_INSTANCE (0)
#define J1939_CONFIG_FUNCTION_INSTANCE (5)
#define J1939_CONFIG_FUNCTION (0)
#define J1939_CONFIG_VEHICLE_SYSTEM_2 (0)
#define J1939_CONFIG_VEHICLE_SYSTEM_INSTANCE (0)
#define J1939_CONFIG_INDUSTRY_GROUP (2)
#define J1939_CONFIG_ARBITRARY_ADDRESS_CAPABLE (1)
#define J1939_CONFIG_ADDRESS (5)
#define J1939_CONFIG_TIM_SERVER_ADDRESS (0xED)

/**
typedef struct J1939_Id_t {
    uint8_t source : 8;
    uint32_t pgn : 18;
    uint8_t priority : 3;
} J1939_Id_t;
*/

// Speed Wheel Message
// Received frame on can0 - ID: 8CFFFE8C, DLC: 8, Data: 8C FA 9F 1F 37 FF FF FF

typedef union J1939_Id_t {
  struct {
    uint8_t source : 8;
    uint8_t ps : 8; // PDU specific (destination address or group extension)
    uint8_t pf : 8; // PDU format
    uint8_t dp : 1; // Data Page
    uint8_t reserved : 1;
    uint8_t priority : 3;
  } id;
  uint32_t raw;
} J1939_Id_t;

typedef struct J1939_Pgn_Packet_t {
  J1939_Id_t id;
  // uint8_t data[J1939_PGN_DATA_SIZE];
  uint8_t *data;
  uint8_t dlc;
} J1939_Pgn_Packet_t;

typedef union J1939_Pgn_Address_Claim_Data_t {
  struct {
    uint32_t identity : 21;
    uint16_t manufacturer_code : 11;
    uint8_t ecu_instance : 3;
    uint8_t function_instance : 5;
    uint8_t function : 8;
    uint8_t reserved : 1;
    uint8_t vehicle_system : 7;
    uint8_t vehicle_system_instance : 4;
    uint8_t industry_group : 3;
    uint8_t arbitrary_addresss_compatible : 1;
  } data;
  uint8_t raw[8];
} J1939_Pgn_Address_Claim_Data_t;

int j1939_init();

void clear_mailbox();

void j1939_send_pgn(J1939_Pgn_Packet_t *pkt);
int j1939_await_pgn(uint32_t pgn, J1939_Pgn_Packet_t *pkt, uint16_t timeout_ms);
int j1939_test();

int j1939_claim_address();
#include <logging/log.h>
LOG_MODULE_REGISTER(isobus, CONFIG_APP_LOG_LEVEL);

#include "isobus.h"

int isobus_send_tim_msg(uint8_t *data) {
  J1939_Pgn_Packet_t j1939_pkt = {.id = {.id = {.priority = 6,
                                                .dp = 0,
                                                .ps = J1939_CONFIG_TIM_SERVER_ADDRESS,
                                                .pf = J1939_PGN_TIM_CLIENT,
                                                .source = J1939_CONFIG_ADDRESS}},
                                  .dlc = 8,
                                  .data = data};
  j1939_send_pgn(&j1939_pkt);
  return 0;
}

int isobus_send_auth_msg(uint8_t *data, uint8_t len) {
  J1939_Pgn_Packet_t j1939_pkt = {.id = {.id = {.priority = 6,
                                                .dp = 0,
                                                .ps = J1939_CONFIG_TIM_SERVER_ADDRESS,
                                                .pf = J1939_PGN_AUTH_CLIENT,
                                                .source = J1939_CONFIG_ADDRESS}},
                                  .dlc = len,
                                  .data = data};
  j1939_send_pgn(&j1939_pkt);
  return 0;
}

int isobus_await_tim_msg(uint8_t message_code, uint8_t *msgbuf, uint16_t timeout_msec) {
  int64_t start_time = k_uptime_get();
  int64_t remaining = (int64_t)timeout_msec;
  J1939_Pgn_Packet_t j1939_pkt;

  int ret = 1;

  while (remaining > 0 && (ret != 0 || j1939_pkt.data[0] != message_code)) {
    ret = j1939_await_pgn(J1939_PGN_TIM_SERVER, &j1939_pkt, timeout_msec);
    LOG_INF("DEBUG message_code %d", j1939_pkt.data[0]);
    int64_t now = k_uptime_get();
    remaining -= (now - start_time);
    start_time = now;
  }
  if (remaining <= 0)
    return 1;

  // DEBUG {
  if (j1939_pkt.data[0] == 0xF6) {
    LOG_INF("DEBUG %d", j1939_pkt.data[2]);
  }
  // }DEBUG

  if (ret != 0)
    return ret;
  memcpy(msgbuf, j1939_pkt.data, 8);
  return 0;
}

int isobus_await_auth_msg(uint8_t message_code, uint8_t *msgbuf, uint16_t timeout_msec) {
  int64_t start_time = k_uptime_get();
  int64_t remaining = (int64_t)timeout_msec;
  J1939_Pgn_Packet_t j1939_pkt;

  int ret = 1;

  while (remaining > 0 && (ret != 0 || j1939_pkt.data[0] != message_code)) {
    ret = j1939_await_pgn(J1939_PGN_AUTH_SERVER, &j1939_pkt, timeout_msec);
    LOG_INF("DEBUG message_code %d", j1939_pkt.data[0]);
    int64_t now = k_uptime_get();
    remaining -= (now - start_time);
    start_time = now;
  }
  if (remaining <= 0)
    return 1;

  if (ret != 0)
    return ret;
  memcpy(msgbuf, j1939_pkt.data, 8);
  return 0;
}

// SECTION: cyclic auth status message thread
K_THREAD_STACK_DEFINE(cyclic_auth_stack_area, THREAD_STACK_SIZE);
struct k_thread cyclic_auth_thread_data;
k_tid_t cyclic_auth_tid;

void isobus_cyclic_auth_msg(struct k_sem *sem, uint8_t *data, void *p3) {
  while (1) {
    isobus_send_auth_msg(data, 8);
    k_msleep(100);
  }
}

int isobus_start_cyclic_auth_msg(struct k_sem *sem, uint8_t *data) {
  // create thread
  cyclic_auth_tid =
      k_thread_create(&cyclic_auth_thread_data, cyclic_auth_stack_area, K_THREAD_STACK_SIZEOF(cyclic_auth_stack_area),
                      isobus_cyclic_auth_msg, sem, data, NULL, THREAD_PRIORITY, 0, K_NO_WAIT);

  return 0;
}
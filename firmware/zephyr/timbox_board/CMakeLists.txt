
cmake_minimum_required(VERSION 3.13.1)

find_package(Zephyr REQUIRED HINTS $ENV{ZEPHYR_BASE})
project(timbox_board)

# proto paths
set(ROOT_PATH ../../..)
set(GENERATED_PROTO_PATH ${ROOT_PATH}/generated/lib/drivers/nanopb/proto)
file(GLOB PROTO_SOURCES CONFIGURE_DEPENDS ${GENERATED_PROTO_PATH}/*.c)
file(GLOB PROTO_HEADERS CONFIGURE_DEPENDS ${GENERATED_PROTO_PATH}/*.h)
zephyr_library_include_directories(${ROOT_PATH})

file(GLOB APP_SOURCES CONFIGURE_DEPENDS src/*.c)
file(GLOB APP_HEADeRS CONFIGURE_DEPENDS src/*.h)
target_sources(app PRIVATE ${APP_SOURCES} ${APP_HEADERS} ${PROTO_SOURCES} ${PROTO_HEADERS})
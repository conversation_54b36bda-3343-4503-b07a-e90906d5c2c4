/ {
    chosen {
        zephyr,code-partition = &slot0_partition;
        zephyr,can-primary = &can1;
    };

    aliases {
        watchdog0 = &iwdg1;
        zlcb0 = &zlcb0;
    };
};

&iwdg1 {
    status = "okay";
};

&can1 {
	pinctrl-0 = <&fdcan1_rx_pd0 &fdcan1_tx_pd1>;
	pinctrl-names = "default";
	bus-speed = <125000>;
	bus-speed-data = <1000000>;
    status = "okay";

    /*
    sjw = < 0x1 >;
    prop-seg = < 1 >;
    phase-seg1 = < 4 >;
    phase-seg2 = < 2 >;
    */
};

&timers8 {
    status = "okay";
    st,prescaler = <119>;
    zlcb0: zlcb {
        compatible = "st,stm32-zlcb";
        status = "okay";
        label = "ZLCB0";
    };
};

&clk_hse {
    /delete-property/ hse-bypass;
    clock-frequency = < DT_FREQ_M(25) >; // 25 MHz crystal
    status="okay";
};

&pll {
    mul-n = <8>;
};

&rcc {
    clock-frequency = < DT_FREQ_M(100) >;
};

&usart3 {
    status = "disabled";
};

import asyncio
import copy
import logging
import os
from argparse import Argument<PERSON><PERSON><PERSON>, Namespace
from shutil import copyfile
from typing import List, Optional

import yaml

from firmware.release.firmware_release_manager import (
    Firmware,
    FirmwareVersion,
    get_all_firmware_versions,
    get_boards_dir,
    get_latest_firmware_version,
    get_purgeable_firmware_version,
    make_firmware_path,
)
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.zephyr.mcumgr.mcumgr import MCUMGR<PERSON>lient
from lib.drivers.zephyr.west.west import WestClient

LOG = logging.getLogger(__name__)

# ### Commands ###


PROJECT_IPS = {
    "sample_a": "*********",
    "pulsar_board": "10.11.4.{}",
    "strobe_mother_board": "*********",
    "wheel_encoder_board": "*********",
    "gps_board": "*********",
    "jimbox_board": "**********",
    "asc_board": "**********",
    "hasselhoff_board": "**********",
    "reaper_module_controller": "**********",
}


# This acts as a whitelist of available hardware board revisions
PROJECT_REVISION_BOARDS = {
    "sample_a": {"h745a": "nucleo_h745zi_q_m7", "h753a": "nucleo_h753zi", "gigalite": "gigalite"},
    # Note: h745RevC will be dropped once we replace boards on SlayerTB
    "pulsar_board": {
        "h745a": "nucleo_h745zi_q_m7",
        "h745RevC": "nucleo_h745zi_q_m7",
        "h753a": "nucleo_h753zi",
        "h745b": "nucleo_h745zi_q_m7",
        "scanner_gd": "scanner_gd",
        "scanner_gd_maxon": "scanner_gd",
        "scanner_gd_reaper": "scanner_gd",
        "scanner_h753_reaper": "scanner_h753_reaper",
        "scanner_h753_slayer": "scanner_h753_slayer",
    },
    "pulsar_board_secondary": {
        "h745a": "nucleo_h745zi_q_m4",
        "h745RevC": "nucleo_h745zi_q_m4",
        "h745b": "nucleo_h745zi_q_m4",
    },
    "strobe_mother_board": {"h745a": "nucleo_h745zi_q_m7", "h753a": "nucleo_h753zi"},
    "wheel_encoder_board": {
        "h745a": "nucleo_h745zi_q_m7",
        "h753a": "nucleo_h753zi",
        "reaper": "wheel_encoder_board_reaper",
    },
    "gps_board": {"h745a": "nucleo_h745zi_q_m7", "h753a": "nucleo_h753zi", "hh_dual": "hh_dual_gps"},
    "timbox_board": {"timbox": "timbox", "h753a": "nucleo_h753zi"},
    "jimbox_board": {"jimbox": "jimbox", "h753a": "nucleo_h753zi"},
    "asc_board": {"asc": "asc"},
    "hasselhoff_board": {"hasselhoff": "hasselhoff", "h753a": "hasselhoff"},
    "reaper_module_controller": {
        # STM32H743ZI on overseas-made dev board
        "h743_dev": "reaper_module_controller_h743_dev",
        # STM32H753ZI on domestically produced dev board
        "h753_dev": "reaper_module_controller_h743_dev",
        # production board (rev1, with STM32H753ZI)
        "prod": "reaper_module_controller",
    },
}
MACRO_DEFINES = {
    "pulsar_board": {
        "h745a": ["USE_MAXON=1"],
        "h745RevC": ["USE_MAXON=1", "ALT_CHANNEL=4"],
        "h753a": ["USE_MAXON=1"],
        "h745b": ["USE_FAULHABER=1"],
        "scanner_gd": ["USE_FAULHABER=1", "USE_GD32=1"],
        "scanner_gd_maxon": ["USE_MAXON=1", "USE_GD32=1"],
        "scanner_gd_reaper": ["USE_FAULHABER=1", "USE_GD32=1", "USE_REAPER=1"],
        "scanner_h753_reaper": ["USE_FAULHABER=1", "USE_REAPER=1", "USE_NEW_EEPROM=1"],
        "scanner_h753_slayer": ["USE_FAULHABER=1", "USE_NEW_EEPROM=1"],
    },
    "gps_board": {"hh_dual": ["USE_DUAL_GPS=1"]},
    "wheel_encoder_board": {"reaper": ["USE_REAPER=1"]},
}


def get_project_ip(project: str, scanner_id: int) -> str:
    if project not in PROJECT_IPS:
        raise Exception("Project not in IP List, please add project to the above dictionary")

    ip = PROJECT_IPS[project]
    if "{}" in ip:
        ip = ip.format(scanner_id)

    return ip


def get_secondary_core(rev: str) -> Optional[str]:
    if "h745" in rev:
        return "nucleo_h745zi_q_m4"
    return None


def get_board_zephyr_name(project: str, rev: str) -> str:
    if project not in PROJECT_REVISION_BOARDS:
        raise Exception(f"Unknown project: {project}")
    if rev not in PROJECT_REVISION_BOARDS[project]:
        raise Exception(f"Unknown revision: {rev} for project: {project}")
    return PROJECT_REVISION_BOARDS[project][rev]


def get_macro_defines(project: str, rev: str) -> List[str]:
    if project in MACRO_DEFINES and rev in MACRO_DEFINES[project]:
        return MACRO_DEFINES[project][rev]
    return []


def get_board(args: Namespace) -> str:
    return get_board_zephyr_name(args.project, args.revision)


def get_release_name(args: Namespace) -> str:
    return f"{args.project}_{args.revision}"


async def list_releases(args: Namespace) -> None:
    versions = sorted([x.version for x in get_all_firmware_versions(get_release_name(args))])
    print("\n".join([str(x).replace("_", ".")[1:] for x in versions]))


async def purge(args: Namespace) -> None:
    purgeable_firmwares = get_purgeable_firmware_version(get_release_name(args))
    for firmware in purgeable_firmwares:
        print(f"Removing: {firmware.path}")
        os.remove(firmware.path)


async def upgrade(args: Namespace) -> None:
    client = MCUMGRClient(args.ip)
    await client.upgrade(args.project, version=args.version)


async def release(args: Namespace) -> None:
    if args.revision == "all":
        revisions = list(PROJECT_REVISION_BOARDS[args.project].keys())
    else:
        revisions = [args.revision]
    for rev in revisions:
        args.revision = rev
        latest_version = get_latest_firmware_version(get_release_name(args))
        if latest_version is None:
            new_version = FirmwareVersion(1, 0, 0)
        else:
            new_version = copy.deepcopy(latest_version.version)
            new_version.bump(args.version)

        LOG.info(
            f"Latest Version: {latest_version.version if latest_version is not None else None}, Releasing New Version: {new_version}"
        )

        firmware_path = make_firmware_path(get_release_name(args), new_version, ext="bin")

        # TODO Consider at some point maybe passing in a special release.conf file to projects etc...
        west_client = WestClient()
        await west_client.clean_build(args.project)
        await west_client.build_project(
            args.project, get_board(args), args.revision, get_macro_defines(args.project, args.revision)
        )
        await west_client.sign_project(args.project, str(new_version)[1:].replace("_", "."))

        copyfile(f"{west_client.build_dir}/{args.project}/zephyr/zephyr.signed.bin", firmware_path)
        LOG.info(f"Created new release at: {firmware_path}")

        # archive ELFs if needed
        if args.project in ["reaper_module_controller"]:
            elf_path = firmware_path.replace(".bin", ".elf")
            copyfile(f"{west_client.build_dir}/{args.project}/zephyr/zephyr.elf", elf_path)
            LOG.info(f"Saved ELF (with debug symbols) at {elf_path}")


async def reset(args: Namespace) -> None:
    await MCUMGRClient(args.ip).reset_mcu()


async def confirm(args: Namespace) -> None:
    await MCUMGRClient(args.ip).confirm_current_image()


async def mark_test(args: Namespace) -> None:
    await MCUMGRClient(args.ip).mark_new_image_for_test()


async def list_images(args: Namespace) -> None:
    images = await MCUMGRClient(args.ip).get_image_dict()
    print(yaml.dump(images, allow_unicode=True, default_flow_style=False))


async def upload(args: Namespace) -> None:
    await MCUMGRClient(args.ip).upload_new_dev_image(args.project)


async def flash(args: Namespace) -> None:
    await WestClient().flash(args.project)


async def flash_secondary(args: Namespace) -> None:
    core = get_secondary_core(args.revision)
    if core is None:
        LOG.info(f"Nothing to flash, as no secondary core found for {args.revision}")
        return
    await WestClient().manual_flash(
        os.path.join(get_boards_dir(), "minimal_h745_m4/h745_minimal.hex"), core,
    )


async def build(args: Namespace) -> None:
    client = WestClient()
    if args.clean:
        await client.clean_build(args.project)
    await client.build_project(
        args.project, get_board(args), args.revision, get_macro_defines(args.project, args.revision)
    )
    await client.sign_project(args.project)


async def bootloader_build(args: Namespace) -> None:
    client = WestClient()
    await client.clean_build("carbon_bootloader")
    await client.build_bootloader_for_project(args.project, get_board(args), args.revision)
    if "gd" in args.revision:
        await client.gd_enterboot_build()


async def bootloader_flash(args: Namespace) -> None:
    client = WestClient()
    await client.flash("carbon_bootloader")
    if args.enterboot:
        await client.gd_enterboot_build()
        await client.gd_enterboot_flash_from_build()


async def bootloader_install(args: Namespace) -> None:
    target_firmware: Optional[Firmware] = None
    if args.version is None:
        target_firmware = get_latest_firmware_version(f"bootloader_{get_release_name(args)}")
    else:
        firmwares = get_all_firmware_versions(f"bootloader_{get_release_name(args)}")
        major, minor, rev = [int(x) for x in args.version.split(".")]
        target_version = FirmwareVersion(major, minor, rev)
        for firmware in firmwares:
            if firmware.version.is_equal(target_version):
                target_firmware = firmware
                break
    if target_firmware is None:
        LOG.error(f"Bootloader Firmware Version Not Found: {args.version}, available versions are: ")
        args.project = f"bootloader_{get_release_name(args)}"
        await list_releases(args)
        return
    await WestClient().manual_flash(target_firmware.path, get_board(args))


async def bootloader_purge(args: Namespace) -> None:
    purgeable_firmwares = get_purgeable_firmware_version(f"bootloader_{get_release_name(args)}")
    for firmware in purgeable_firmwares:
        print(f"Removing: {firmware.path}")
        os.remove(firmware.path)


async def bootloader_release(args: Namespace) -> None:
    name = f"bootloader_{get_release_name(args)}"
    latest_version = get_latest_firmware_version(name)
    if latest_version is None:
        new_version = FirmwareVersion(1, 0, 0)
    else:
        new_version = copy.deepcopy(latest_version.version)
        new_version.bump(args.version)

    LOG.info(
        f"Latest Version: {latest_version.version if latest_version is not None else None}, Releasing New Version: {new_version}"
    )

    firmware_path = make_firmware_path(name, new_version, ext="hex")

    # TODO Consider at some point maybe passing in a special release.conf file to projects etc...
    west_client = WestClient()
    await west_client.clean_build("carbon_bootloader")
    await west_client.build_bootloader_for_project(args.project, get_board(args), args.revision)

    copyfile(f"{west_client.build_dir}/carbon_bootloader/zephyr/zephyr.hex", firmware_path)
    LOG.info(f"Created new bootloader release at: {firmware_path}")


def main() -> None:
    logging.basicConfig(level=logging.INFO)
    parser = ArgumentParser("Carbon Zephyr CLI")

    # Overrides
    parser.add_argument("--ip", default=None, type=str, help="Override Automatic IP Detection")
    parser.add_argument(
        "--scanner-id", default=1, type=int, help="Scanner ID to use when communicating with a scanner board"
    )

    subparsers = parser.add_subparsers(dest="cmd")
    subparsers.required = True

    # Bootloader Commands
    bootloader_parser = subparsers.add_parser("bootloader", help="Bootloader Commands")
    bootloader_subparsers = bootloader_parser.add_subparsers(dest="sub_cmd")

    # Bootloader Build
    bootloader_build_parser = bootloader_subparsers.add_parser("build", help="Build Bootloader for a specific project")
    bootloader_build_parser.set_defaults(func=bootloader_build)
    bootloader_build_parser.add_argument("-r", "--revision", type=str, required=True, help="Board revision, e.g. h745a")
    bootloader_build_parser.add_argument("-p", "--project", type=str, required=True, help="Name of the Project")

    # Bootloader Flash
    bootloader_flash_parser = bootloader_subparsers.add_parser("flash", help="Flash last built Bootloader")
    bootloader_flash_parser.set_defaults(func=bootloader_flash)
    bootloader_flash_parser.add_argument("-e", "--enterboot", action="store_true", help="Flash enterboot for GD32")

    # Bootloader Release
    bootloader_release_parser = bootloader_subparsers.add_parser("release", help="Release Bootloader for Project")
    bootloader_release_parser.set_defaults(func=bootloader_release)
    bootloader_release_parser.add_argument(
        "-r", "--revision", type=str, required=True, help="Board revision, e.g. h745a"
    )
    bootloader_release_parser.add_argument("-p", "--project", type=str, required=True, help="Name of the Project")
    bootloader_release_parser.add_argument(
        "-v",
        "--version",
        type=str,
        choices=["major", "minor", "rev"],
        required=True,
        help="Version Number Update: Major -> Breaking Change or Large Feature, Minor -> Small Feature or Bug Fix, Rev -> Development",
    )

    # Bootloader Install
    bootloader_install_parser = bootloader_subparsers.add_parser("install", help="Install a released bootloader")
    bootloader_install_parser.set_defaults(func=bootloader_install)
    bootloader_install_parser.add_argument(
        "-r", "--revision", type=str, required=True, help="Board revision, e.g. h745a"
    )
    bootloader_install_parser.add_argument("-p", "--project", type=str, required=True, help="Name of the Project")
    bootloader_install_parser.add_argument(
        "-v", "--version", type=str, default=None, help="Version Number to Update To, Format: X.Y.Z"
    )

    # Purge Revs
    bootloader_purge_parser = bootloader_subparsers.add_parser(
        "purge", help="Purge All the non latest revs of the Bootloader of a Project"
    )
    bootloader_purge_parser.set_defaults(func=bootloader_purge)
    bootloader_purge_parser.add_argument("-p", "--project", type=str, required=True, help="Name of the Project")
    bootloader_purge_parser.add_argument("-r", "--revision", type=str, required=True, help="Board revision, e.g. h745a")

    # Build Project
    build_parser = subparsers.add_parser("build", help="Build Project")
    build_parser.set_defaults(func=build)
    build_parser.add_argument("-r", "--revision", type=str, required=True, help="Board revision, e.g. h745a")
    build_parser.add_argument("-p", "--project", type=str, required=True, help="Name of the Project")
    build_parser.add_argument(
        "-c", "--clean", default=False, action="store_true", help="Deletes the previous build before rebuilding"
    )

    # Flash Project
    flash_parser = subparsers.add_parser("flash", help="Flash Project")
    flash_parser.set_defaults(func=flash)
    flash_parser.add_argument("-p", "--project", type=str, required=True, help="Name of the Project")

    # Flash Secondary core
    flash_secondary_parser = subparsers.add_parser("flash-secondary", help="Flash Secondary Core")
    flash_secondary_parser.set_defaults(func=flash_secondary)
    flash_secondary_parser.add_argument("-r", "--revision", type=str, required=True, help="Board revision, e.g. h745a")

    # Release Project
    release_parser = subparsers.add_parser("release", help="Release Project")
    release_parser.set_defaults(func=release)
    release_parser.add_argument("-r", "--revision", type=str, default="all", help="Board revision, e.g. h745a")
    release_parser.add_argument("-p", "--project", type=str, required=True, help="Name of the Project")
    release_parser.add_argument(
        "-v",
        "--version",
        type=str,
        choices=["major", "minor", "rev"],
        required=True,
        help="Version Number Update: Major -> Breaking Change, Minor -> New Feature, Rev -> Bug Fix",
    )

    # Upload Project
    upload_parser = subparsers.add_parser("upload", help="Upload Project Binaries")
    upload_parser.set_defaults(func=upload)
    upload_parser.add_argument("-p", "--project", type=str, required=True, help="Name of the Project")

    # Upgrade Project
    upgrade_parser = subparsers.add_parser("upgrade", help="Upgrade Project Binaries to a released version")
    upgrade_parser.set_defaults(func=upgrade)
    upgrade_parser.add_argument("-p", "--project", type=str, required=True, help="Name of the Project")
    upgrade_parser.add_argument(
        "-v", "--version", type=str, default=None, help="Version Number to Update To, Format: X.Y.Z"
    )

    # List Available Releases
    list_releases_parser = subparsers.add_parser("list_releases", help="List Available Releases of a Project")
    list_releases_parser.set_defaults(func=list_releases)
    list_releases_parser.add_argument("-p", "--project", type=str, required=True, help="Name of the Project")
    list_releases_parser.add_argument("-r", "--revision", type=str, required=True, help="Board revision, e.g. h745a")

    # Purge Revs
    purge_parser = subparsers.add_parser("purge", help="Purge All the non latest revs of a Project")
    purge_parser.set_defaults(func=purge)
    purge_parser.add_argument("-p", "--project", type=str, required=True, help="Name of the Project")
    purge_parser.add_argument("-r", "--revision", type=str, required=True, help="Board revision, e.g. h745a")

    # List Images
    list_parser = subparsers.add_parser("list", help="List Images on the Devices")
    list_parser.set_defaults(func=list_images)
    list_parser.add_argument("-p", "--project", type=str, required=False, help="Name of the Project")

    # Test Image
    test_parser = subparsers.add_parser("test", help="Marks Image in Slot 1 for testing on next reboot")
    test_parser.set_defaults(func=mark_test)
    test_parser.add_argument("-p", "--project", type=str, required=False, help="Name of the Project")

    # Confirm Image
    confirm_parser = subparsers.add_parser("confirm", help="Confirms Image in Slot 0 as valid and verified")
    confirm_parser.set_defaults(func=confirm)
    confirm_parser.add_argument("-p", "--project", type=str, required=False, help="Name of the Project")

    # Reset
    reset_parser = subparsers.add_parser("reset", help="Reboots the MCU")
    reset_parser.set_defaults(func=reset)
    reset_parser.add_argument("-p", "--project", type=str, required=False, help="Name of the Project")

    args = parser.parse_args()
    if args.ip is None and hasattr(args, "project"):
        args.ip = get_project_ip(args.project, args.scanner_id)

    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    os.chdir("firmware/zephyr")
    asyncio.run_coroutine_threadsafe(args.func(args), get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

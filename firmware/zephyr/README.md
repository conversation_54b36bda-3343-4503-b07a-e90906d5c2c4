## Installation

1. Follow [instructions](https://docs.google.com/document/d/19tTfqmbMViEXfUc025RORbwiSLBbStqBi4VMz77-mmk/edit#heading=h.91jabqdtitzm)

2. Run `west update` in this directory.

## Prepare board

The following will initialize board with empty code on both M4 and M7 processors:

```
west build -p auto -b nucleo_h745zi_q_m4 -s thirdparty/zephyr/samples/basic/minimal && west flash
west build -p auto -b nucleo_h745zi_q_m7 -s thirdparty/zephyr/samples/basic/minimal && west flash
```

## Carbon Zephyr based boards

These instructions will change as we integrate bootloader, but for now the process is as follows:

1. Cd into project you're building.

2. `west build -p auto -b nucleo_h745zi_q_m7` inside.

    - To build in debug mode, run `west build -p auto -b nucleo_h745zi_q_m7 -- -DOVERLAY_CONFIG=debug.conf`

3. `west flash` inside.


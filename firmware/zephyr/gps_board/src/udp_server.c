#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(udp_server, CONFIG_APP_LOG_LEVEL);

#include <errno.h>
#include <pb_decode.h>
#include <pb_encode.h>
#include <string.h>
#include <zephyr/sys/reboot.h>
#include <zephyr/zephyr.h>

#include <drivers/hw_rev_straps.h>
#include <lib/udp/udp.h>
#include <utils/handle_errors.h>

#include "eeprom.h"
#include "generated/lib/drivers/nanopb/proto/benjamin_gps_board.pb.h"
#include "gps.h"
#include "leds.h"
#include "udp_server.h"
#if USE_DUAL_GPS
#include "dual_gps.h"
#endif

#define RECV_BUFFER_SIZE 1280

/// Stack size for UDP worker; must be large enough to hold decoded protobuf msg
#define UDP_WORKER_THREAD_STACK_SIZE (1024 + RECV_BUFFER_SIZE)
/// Priority for nanopb queries
#define UDP_WORKER_THREAD_PRIORITY K_PRIO_PREEMPT(10)

static void process_udp();

static int handle_ping(const diagnostic_Ping *req, diagnostic_Pong *resp);
static int handle_gps_position(const gps_Position_Request *req, gps_Position_Reply *resp);
static int handle_gps(const gps_Request *req, gps_Reply *resp);
static int handle_heading(const heading_Request *req, heading_Reply *resp);
static int handle_hwinfo(const hwinfo_Request *req, hwinfo_Reply *resp);
static int handle_ptp_config(const benjamin_gps_board_PtpConfig_Request *req);

K_THREAD_DEFINE(udp_thread_id, UDP_WORKER_THREAD_STACK_SIZE, process_udp, NULL, NULL, NULL, UDP_WORKER_THREAD_PRIORITY,
                0, K_TICKS_FOREVER);

// TODO: move away from using udp lib
UDP_SERVER_DEFINE(npb_udp_server, CONFIG_PB_REQUESTS_BUFFER_SIZE, benjamin_gps_board_Request_size * 2,
                  UDP_WORKER_THREAD_PRIORITY);

/**
 * @brief Respond to ping request
 */
static int handle_ping(const diagnostic_Ping *req, diagnostic_Pong *resp) {
  resp->x = req->x;
  return 0;
}

/**
 * @brief Respond with GPS position
 */
static int handle_gps_position(const gps_Position_Request *req, gps_Position_Reply *resp) {
  struct gps_query_result r;
  get_latest_gps_query_result(&r);

  resp->timestamp_ms = r.timestamp_ms;
  resp->have_approx_fix = r.have_approx_fix;
  resp->have_fix = r.have_fix;
  resp->latitude = r.latitude;
  resp->longitude = r.longitude;
  resp->height_mm = r.height_mm;
  resp->hdop = r.hdop;
  resp->num_sats = r.num_sats;
  resp->fix_type = r.fix_type;
  resp->fix_flags = r.fix_flags;

  // if dual gps board, respond with that data too
#if USE_DUAL_GPS
  gps_relposned_result_t relpos;
  resp->which__dual = gps_Position_Reply_dual_tag;
  gps_DualGpsData *dual = &resp->_dual.dual;

  dual_gps_get_relpos(&relpos);

  dual->gnss_valid = relpos.gnss_valid;
  dual->diff_corrections = relpos.diff_corrections_applied;
  dual->is_moving_base = relpos.is_moving_base;

  if (!relpos.has_carrier_phase_soln) {
    dual->carrier_phase = gps_CarrierPhaseSoln_NONE;
  } else {
    if (relpos.carrier_phase_soln_fixed) {
      dual->carrier_phase = gps_CarrierPhaseSoln_FIXED;
    } else {
      dual->carrier_phase = gps_CarrierPhaseSoln_FLOATING;
    }
  }

  // position vector is valid
  if (relpos.rel_pos_valid) {
    dual->which__north = gps_DualGpsData_north_tag;
    dual->_north.north.value = relpos.rel_pos_north;
    dual->_north.north.accuracy = relpos.rel_pos_north_accuracy;

    dual->which__east = gps_DualGpsData_east_tag;
    dual->_east.east.value = relpos.rel_pos_east;
    dual->_east.east.accuracy = relpos.rel_pos_east_accuracy;

    dual->which__down = gps_DualGpsData_down_tag;
    dual->_down.down.value = relpos.rel_pos_down;
    dual->_down.down.accuracy = relpos.rel_pos_down_accuracy;

    dual->which__length = gps_DualGpsData_length_tag;
    dual->_length.length.value = relpos.rel_pos_length;
    dual->_length.length.accuracy = relpos.rel_pos_length_accuracy;
  }

  // heading component is valid
  if (relpos.rel_pos_heading_valid) {
    dual->which__heading = gps_DualGpsData_heading_tag;
    dual->_heading.heading.value = relpos.rel_pos_heading;
    dual->_heading.heading.accuracy = relpos.rel_pos_heading_accuracy;
  }

  // TODO: timestamp stuff
#endif

  return 0;
}

/**
 * @brief Process GPS-specific requests
 */
static int handle_gps(const gps_Request *req, gps_Reply *resp) {
  switch (req->which_request) {
  case gps_Request_position_tag:
    resp->which_reply = gps_Reply_position_tag;
    HANDLE_UNLIKELY(handle_gps_position(&req->request.position, &resp->reply.position));
    break;
  case gps_Request_spartn_tag:
    resp->which_reply = gps_Reply_spartn_tag;
    send_spartn_data(req->request.spartn.data.bytes, req->request.spartn.data.size, req->request.spartn.end);
    break;
  case gps_Request_rtcm_tag:
    resp->which_reply = gps_Reply_rtcm_tag;
    send_rtcm_data(req->request.rtcm.data.bytes, req->request.rtcm.data.size, req->request.rtcm.end);
    break;

  case gps_Request_gga_tag:
    resp->which_reply = gps_Reply_gga_tag;
    HANDLE_UNLIKELY(gps_get_last_gga(resp->reply.gga.raw_sentence, sizeof(resp->reply.gga.raw_sentence)));
    break;

#if USE_DUAL_GPS
  case gps_Request_heading_correction_tag:
    resp->which_reply = gps_Reply_heading_correction_tag;
    HANDLE_UNLIKELY(dual_gps_set_heading_offset(req->request.heading_correction.heading_offset));
    break;
#endif

  default:
    LOG_WRN("Received unknown gps request with tag: %d", req->which_request);
    return -ENOMSG;
  }

  return 0;
}

/**
 * @brief Query the current heading
 */
static int handle_heading(const heading_Request *req, heading_Reply *resp) {
  // if dual gps board, respond with that data too
#if USE_DUAL_GPS
  gps_relposned_result_t relpos;
  dual_gps_get_relpos(&relpos);

  resp->have_fix = relpos.gnss_valid;
  // TODO: change this?
  resp->have_approx_fix = relpos.gnss_valid;

  // heading component is valid
  if (relpos.rel_pos_heading_valid) {
    resp->heading_deg = relpos.rel_pos_heading;
    resp->accuracy_deg = relpos.rel_pos_heading_accuracy;
  }
  /* TODO: return correct timestamp */
  resp->timestamp_ms = 0;
#endif

  return 0;
}

/**
 * @brief Service requests for hardware info endpoint
 *
 * This is used to retrieve the hardware/board version via software.
 */
static int handle_hwinfo(const hwinfo_Request *req, hwinfo_Reply *resp) {
  switch (req->which_request) {
  case hwinfo_Request_version_tag:
    resp->which_reply = hwinfo_Reply_version_tag;

    const char *compatible = DT_PROP(DT_ROOT, compatible);
    if (compatible) {
      strncpy(resp->reply.version.model, compatible, sizeof(resp->reply.version.model));
    }

    // get board rev: this may not be supported so handle that gracefully; default is -1
    resp->reply.version.rev = UINT32_MAX;

    static const struct device *gHwRevs = DEVICE_DT_GET_OR_NULL(DT_PATH(hw_rev));
    if (!gHwRevs || !device_is_ready(gHwRevs)) {
      LOG_WRN("HW rev straps device not ready");
      return -ENXIO;
    }
    HANDLE_UNLIKELY(hw_rev_straps_get(gHwRevs, &resp->reply.version.rev));
    break;

  default:
    LOG_WRN("%s: unimplemented request %u", __FUNCTION__, req->which_request);
    return -ENOMSG;
  }

  return 0;
}

/**
 * @brief Update the board's PTP config
 *
 * @remark Depending on the changes, a soft reset/reboot may be necessary to apply
 */
static int handle_ptp_config(const benjamin_gps_board_PtpConfig_Request *req) {
  // get the current config (to see if things will change)
  eeprom_ptp_role_t oldMode;
  HANDLE_UNLIKELY(eeprom_get_ptp_mode(&oldMode));

  // set the master/slave config option
  const eeprom_ptp_role_t mode = req->is_master ? kEepromPtpRoleMaster : kEepromPtpRoleSlave;
  if (mode != oldMode) {
    LOG_INF("Changing PTP mode: %u -> %u (reboot required to apply)", oldMode, mode);

    HANDLE_UNLIKELY(eeprom_set_ptp_mode(mode));
  }

  return 0;
}

/**
 * @brief Top level UDP request handler
 *
 * Process a single UDP message received from a client.
 */
static void serve_request(uint8_t *data, uint16_t received, udp_msg_metadata *metadata) {
  int err;

  // decode the client request
  benjamin_gps_board_Request req = benjamin_gps_board_Request_init_zero;
  pb_istream_t istream = pb_istream_from_buffer(data, received);

  if (!pb_decode(&istream, benjamin_gps_board_Request_fields, &req)) {
    LOG_WRN("Failed to decode UDP packet with size: %d", received);
    return;
  }

  /*
   * Prepare a response structure; then invoke the appropriate handler.
   *
   * Any errors in the handler are converted into an error response sent back to the client;
   * otherwise the response the handler set will be applied. The default response will be an ack
   * message.
   */
  benjamin_gps_board_Reply resp = benjamin_gps_board_Reply_init_zero;
  resp.has_header = true;
  resp.header.requestId = req.header.requestId;
  resp.which_reply = benjamin_gps_board_Reply_ack_tag;

  switch (req.which_request) {
  case benjamin_gps_board_Request_ping_tag:
    resp.which_reply = benjamin_gps_board_Reply_pong_tag;
    err = handle_ping(&req.request.ping, &resp.reply.pong);
    break;
  case benjamin_gps_board_Request_gps_tag:
    resp.which_reply = benjamin_gps_board_Reply_gps_tag;
    err = handle_gps(&req.request.gps, &resp.reply.gps);
    break;
  case benjamin_gps_board_Request_heading_tag:
    resp.which_reply = benjamin_gps_board_Reply_heading_tag;
    err = handle_heading(&req.request.heading, &resp.reply.heading);
    break;
  case benjamin_gps_board_Request_hwinfo_tag:
    resp.which_reply = benjamin_gps_board_Reply_hwinfo_tag;
    err = handle_hwinfo(&req.request.hwinfo, &resp.reply.hwinfo);
    break;

  case benjamin_gps_board_Request_ptp_config_tag:
    err = handle_ptp_config(&req.request.ptp_config);
    break;

  default:
    LOG_WRN("Received unknown request with tag: %d", req.which_request);
    err = -ENOMSG;
    return;
  }

  if (err) {
    LOG_WRN("failed to handle request (tag=%d): %d", req.which_request, err);

    resp.which_reply = benjamin_gps_board_Reply_error_tag;
    resp.reply.error.code = err;
  }

  // encode reply and send it
  pb_ostream_t ostream = pb_ostream_from_buffer(data, udp_buffer_size(&npb_udp_server));
  if (!pb_encode(&ostream, benjamin_gps_board_Reply_fields, &resp)) {
    LOG_WRN("Failed to encode nanoPB response");
    return;
  }
  udp_tx(&npb_udp_server, data, ostream.bytes_written, metadata);
}

static void process_udp() {
  for (;;) {
    uint8_t *data;
    udp_msg_metadata metadata;
    uint16_t received = udp_get_data_no_copy(&npb_udp_server, &data, &metadata, 0);
    HANDLE_CRITICAL(led_set(&udp_led, 1));
    serve_request(data, received, &metadata);
    HANDLE_CRITICAL(led_set(&udp_led, 0));
    udp_get_data_release(&npb_udp_server, data);
  }
}

/**
 * @brief Initialize the UDP server
 */
int start_udp_server() {
  UDP_SERVER_START(npb_udp_server, CONFIG_APP_UDP_PORT);
  k_thread_name_set(udp_thread_id, "udp");
  k_thread_start(udp_thread_id);

  return 0;
}

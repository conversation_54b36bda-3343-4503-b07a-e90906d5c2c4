#include <logging/log.h>
LOG_MODULE_REGISTER(rtc, CONFIG_APP_LOG_LEVEL);

#include <drivers/counter.h>
#include <drivers/gpio.h>
#include <drivers/rtc/maxim_ds3231.h>
#include <zephyr.h>

#include <utils/handle_errors.h>

#include "clock.h"
#include "leds.h"
#include "rtc.h"

/* this time needs to be after 1978 */
#define DS3231_ALARM_MAGIC 253133760

static const struct device *rtc = DEVICE_DT_GET(DT_ALIAS(rtc0));
static const struct gpio_dt_spec rtc_nrst = GPIO_DT_SPEC_GET(DT_PATH(gpios, rtc_nrst), gpios);
static const struct gpio_dt_spec rtc_nint = GPIO_DT_SPEC_GET(DT_ALIAS(rtc0), isw_gpios);
static struct gpio_callback rtc_nint_cb_data;
static struct net_ptp_time last_pps_clock_time;
static struct sys_notify save_rtc_time_notify;
static bool rtc_started = false;

static void rtc_nint_cb(const struct device *dev, struct gpio_callback *cb, uint32_t pins) {
  HANDLE_CRITICAL(clock_get(&last_pps_clock_time));
}

static void rtc_pps_handler(const struct device *dev, uint8_t id, uint32_t syncclock, void *ud) {
  if (claim_rtc_control_clock()) {
    HANDLE_CRITICAL(led_set(&pps_led, 1));

    uint32_t ctr;
    HANDLE_CRITICAL(counter_get_value(rtc, &ctr));

    struct net_ptp_time last_pps_real_time = {
        .second = ctr,
        .nanosecond = 0,
    };
    HANDLE_CRITICAL(clock_adjust(&last_pps_clock_time, &last_pps_real_time, "RTC"));

    HANDLE_CRITICAL(led_set(&pps_led, 0));
  }
}

int get_rtc_clock_valid(bool *valid) {
  HANDLE_UNLIKELY_BOOL(device_is_ready(rtc), ENODEV);

  /* we assume clock is valid if the alarm matches our magic number */
  struct maxim_ds3231_alarm alarm;
  HANDLE_UNLIKELY(maxim_ds3231_get_alarm(rtc, 1, &alarm));

  *valid = alarm.time == DS3231_ALARM_MAGIC;
  return 0;
}

int start_rtc() {
  if (rtc_started) {
    return 0;
  }

  /* configure RTC_nRST pin */
  HANDLE_UNLIKELY_BOOL(device_is_ready(rtc_nrst.port), ENODEV);
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&rtc_nrst, GPIO_OUTPUT | GPIO_OUTPUT_INIT_HIGH));

  /* configure RTC */
  // NOTE: this only works because Zephyr driver doesn't use IRQ pin; we can't have two drivers
  // claiming the pins simultaneously
  gpio_init_callback(&rtc_nint_cb_data, rtc_nint_cb, BIT(rtc_nint.pin));
  gpio_add_callback(rtc_nint.port, &rtc_nint_cb_data);

  HANDLE_UNLIKELY_BOOL(device_is_ready(rtc), ENODEV);

  struct maxim_ds3231_alarm alarm = {
      .time = 0,
      .user_data = NULL,
      .handler = rtc_pps_handler,
      .flags = MAXIM_DS3231_ALARM_FLAGS_IGNSE | MAXIM_DS3231_ALARM_FLAGS_IGNMN | MAXIM_DS3231_ALARM_FLAGS_IGNHR |
               MAXIM_DS3231_ALARM_FLAGS_IGNDA,
  };
  HANDLE_UNLIKELY(maxim_ds3231_set_alarm(rtc, 0, &alarm));

  rtc_started = true;

  return 0;
}

uint32_t read_rtc_syncclock() { return maxim_ds3231_read_syncclock(rtc); }

int save_rtc_time(struct net_ptp_time *ts, uint32_t syncclock, int32_t offset_ns) {
  HANDLE_UNLIKELY_BOOL(device_is_ready(rtc), ENODEV);

  /*
   * We don't care about notification, so we'll use pollable
   * notiifcation that we will never poll.
   */
  sys_notify_init_spinwait(&save_rtc_time_notify);

  struct maxim_ds3231_syncpoint sp = {
      .rtc =
          {
              .tv_sec = ts->second,
              .tv_nsec = ts->nanosecond + offset_ns,
          },
      .syncclock = syncclock,
  };

  HANDLE_UNLIKELY(maxim_ds3231_set(rtc, &sp, &save_rtc_time_notify));

  if (!rtc_started) {
    start_rtc();
  }

  /* this sets magic number to indicate that time was initialized */
  struct maxim_ds3231_alarm alarm = {
      .time = DS3231_ALARM_MAGIC,
      .user_data = NULL,
      .handler = NULL,
      .flags = 0,
  };
  HANDLE_UNLIKELY(maxim_ds3231_set_alarm(rtc, 1, &alarm));

  /* explicitly set INTCN bit to ensure alarm 0 interrupts are enabled */
  HANDLE_UNLIKELY(maxim_ds3231_ctrl_update(rtc, MAXIM_DS3231_REG_CTRL_INTCN, 0));

  return 0;
}

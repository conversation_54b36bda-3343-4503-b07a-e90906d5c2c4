#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(eeprom, CONFIG_APP_LOG_LEVEL);

#include <zephyr/devicetree.h>
#include <zephyr/drivers/eeprom.h>
#include <zephyr/kernel.h>
#include <zephyr/sys/crc.h>

#include <utils/handle_errors.h>

#include "eeprom.h"

static const struct device *gStorageDev = DEVICE_DT_GET(DT_ALIAS(eeprom0));

// Offset into external EEPROM at which the header is stored
#define EEPROM_HEADER_OFFSET (0)
// Magic value in first four bytes of EEPROM header
#define EEPROM_HEADER_MAGIC (0x12301997)

/**
 * @brief EEPROM header
 *
 * This is stored at the start of the EEPROM and stores basic config data.
 *
 * All multibyte values are stored in native (CPU) byte order.
 */
typedef struct {
  // Header magic (see EEPROM_HEADER_MAGIC)
  uint32_t magic;
  // Length of this header, in bytes
  uint16_t length;

  // Heading correction data
  float headingCorrection;

  // PTP configuration mode
  uint32_t ptpRole;

  // for future expansion
  uint8_t reserved[48];

  // CRC over all previous bytes
  uint16_t checksum;
} __attribute__((packed)) eeprom_header_t;
BUILD_ASSERT(sizeof(eeprom_header_t) == 64, "EEPROM header size incorrect");

typedef struct {
  // Lock to protect against concurrent access to EEPROM
  struct k_mutex lock;

  // EEPROM block header read from EEPROM
  eeprom_header_t header;
  // Flag indicating block header is valid
  bool headerValid;
} eeprom_state_t;

__dtcm_bss_section static eeprom_state_t gState;
// Default EEPROM values
static const eeprom_header_t kHeaderDefaults = {
    .magic = EEPROM_HEADER_MAGIC,
    .length = sizeof(eeprom_header_t),

    .headingCorrection = 0,
    .ptpRole = kEepromPtpRoleMaster,
};

static int header_format();
static int header_write();

/**
 * @brief Initialize EEPROM subsystem
 *
 * Ensure the EEPROM device is available for reading; try to decode the EEPROM header if present.
 *
 * Currently any failures validating the EEPROM header will cause this call to fail and thus the
 * board will not boot.
 */
int eeprom_init() {
  HANDLE_UNLIKELY_BOOL(device_is_ready(gStorageDev), ENODEV);

  k_mutex_init(&gState.lock);

  // read and validate header
  HANDLE_UNLIKELY(eeprom_read(gStorageDev, EEPROM_HEADER_OFFSET, &gState.header, sizeof(eeprom_header_t)));
  LOG_HEXDUMP_DBG(&gState.header, sizeof(gState.header), "Read EEPROM header");

  if (gState.header.magic != EEPROM_HEADER_MAGIC) {
    // special case EEPROM blank case
    if (gState.header.magic == 0xffffffff) {
      LOG_WRN("EEPROM blank - formatting");

      HANDLE_UNLIKELY(header_format());
      goto done;
    } else {
      LOG_WRN("invalid EEPROM magic: %08x", gState.header.magic);
      return -EINVAL;
    }
  }

  // validate length (to detect new version, e.g. larger EEPROM block)
  if (gState.header.length < sizeof(gState.header)) {
    LOG_WRN("invalid EEPROM size: got %u, expected %u", gState.header.length, sizeof(gState.header));
    return -EBADF;
  }

  // validate checksum
  const uint16_t expected = crc16_ccitt(0, (const uint8_t *)&gState.header, offsetof(eeprom_header_t, checksum));
  if (expected != gState.header.checksum) {
    LOG_WRN("invalid EEPROM checksum: got %08x, expected %08x", gState.header.checksum, expected);
    return -EINVAL;
  }

done:;
  // if we get here, EEPROM header's valid
  gState.headerValid = true;

  return 0;
}

/**
 * @brief Read out the heading correction value stored in EEPROM
 */
int eeprom_get_heading_correction(float *outValue) {
  if (!outValue) {
    return -EFAULT;
  }
  if (!gState.headerValid) {
    return -ENOENT;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  { *outValue = gState.header.headingCorrection; }
  k_mutex_unlock(&gState.lock);
  return 0;
}

/**
 * @brief Store the heading correction value to EEPROM
 */
int eeprom_set_heading_correction(const float newValue) {
  int err;

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    gState.header.headingCorrection = newValue;

    err = header_write();
  }
  k_mutex_unlock(&gState.lock);

  return err;
}

/**
 * @brief Read out the desired PTP configuration stored in EEPROM
 */
int eeprom_get_ptp_mode(eeprom_ptp_role_t *outValue) {
  if (!outValue) {
    return -EFAULT;
  }
  if (!gState.headerValid) {
    return -ENOENT;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  { *outValue = gState.header.ptpRole; }
  k_mutex_unlock(&gState.lock);
  return 0;
}

/**
 * @brief Store the heading correction value to EEPROM
 */
int eeprom_set_ptp_mode(const eeprom_ptp_role_t newValue) {
  int err;

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    gState.header.ptpRole = newValue;

    err = header_write();
  }
  k_mutex_unlock(&gState.lock);

  return err;
}

/**
 * @brief Format EEPROM header
 *
 * @remark Caller must hold EEPROM lock
 */
static int header_format() {
  LOG_HEXDUMP_INF(&gState.header, sizeof(gState.header), "EEPROM header before format");

  // just zeroize all data in the header; the magic + checksum are applied by header_write()
  memset(&gState.header, 0, sizeof(gState.header));
  gState.header = kHeaderDefaults;

  HANDLE_UNLIKELY(header_write());

  return 0;
}

/**
 * @brief Write EEPROM header
 *
 * Update checksums and friends in the current EEPROM header and write it to storage.
 *
 * @remark Caller must hold EEPROM lock
 */
static int header_write() {
  // update magic + checksum
  gState.header.magic = EEPROM_HEADER_MAGIC;
  gState.header.length = sizeof(gState.header);

  gState.header.checksum = crc16_ccitt(0, (const uint8_t *)&gState.header, offsetof(eeprom_header_t, checksum));

  // lastly, write it
  LOG_HEXDUMP_DBG(&gState.header, sizeof(gState.header), "Writing EEPROM header");
  HANDLE_UNLIKELY(eeprom_write(gStorageDev, EEPROM_HEADER_OFFSET, &gState.header, sizeof(gState.header)));

  gState.headerValid = true;
  return 0;
}

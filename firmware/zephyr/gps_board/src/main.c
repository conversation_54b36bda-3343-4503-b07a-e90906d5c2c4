#include <logging/log.h>
LOG_MODULE_REGISTER(main, CONFIG_APP_LOG_LEVEL);

#include <zephyr/drivers/eeprom.h>
#include <zephyr/logging/log_ctrl.h>
#include <zephyr/zephyr.h>

#include <lib/ptp/ptp.h>
#include <lib/smp/smp.h>
#include <lib/status_led/status_led.h>
#include <lib/watchdog/watchdog.h>
#include <utils/handle_errors.h>

#include "clock.h"
#include "eeprom.h"
#include "gps.h"
#include "leds.h"
#include "rtc.h"
#include "udp_server.h"

#define TM_VLD_TIMEOUT_MS 5000

/**
 * @brief Get current PTP clock for log timestamps
 *
 * This is the same as `log_get_ptp_timestamp` but it uses the `clock_get` routine instead such
 * that the correct slave/master clock is read.
 */
static inline log_timestamp_t log_get_ptp_clock_timestamp(void) {
  struct net_ptp_time ts;
  clock_get(&ts);

  log_timestamp_t us = ptp_ts_net_to_us(&ts);
  return us;
}

static int boot() {
  LOG_INF("GPS board booting");

  // set up LEDs
#if IS_ENABLED(CONFIG_LIB_STATUS_LED)
  HANDLE_UNLIKELY(leds_init());
  HANDLE_UNLIKELY(leds_set_status(LED_COLOR_BLUE));
#endif

  // set up EEPROM: neds to be done early to get configs
  HANDLE_UNLIKELY(eeprom_init());

  /* configure ptp clock and log timestamp */
  HANDLE_UNLIKELY(clock_init());
  log_set_timestamp_func(log_get_ptp_clock_timestamp, USEC_PER_SEC);

  /* configure LEDs */
  HANDLE_UNLIKELY(start_leds());

  /* start RTC if RTC has valid time */
  bool rtc_clock_valid;
  HANDLE_UNLIKELY(get_rtc_clock_valid(&rtc_clock_valid));
  if (rtc_clock_valid) {
    HANDLE_UNLIKELY(start_rtc());
  } else {
    LOG_INF("RTC clock is invalid, waiting for GPS to set the RTC clock");
  }

  /* start GPS */
  HANDLE_UNLIKELY(start_gps());

  return 0;
}

static void update_leds_forever() {
  int run_blink_state = 0;
  struct gps_query_result fix;

  for (;;) {
    /* time is valid if there was PPS in last 5 seconds */
    bool time_valid =
        last_clock_control_claim() >= 0 && (k_uptime_get() - last_clock_control_claim()) < TM_VLD_TIMEOUT_MS;
    HANDLE_CRITICAL(led_set(&tm_vld_led, time_valid));

    /* blink run */
    HANDLE_CRITICAL(led_set(&run_led, run_blink_state));
    run_blink_state = !run_blink_state;
    k_msleep(500);

    /* fix state on RGB LED: blink yellow = none, blink green = approx, solid green = good */
#if IS_ENABLED(CONFIG_LIB_STATUS_LED)
    get_latest_gps_query_result(&fix);
    if (fix.have_fix) {
      leds_set_status(LED_COLOR_GREEN);
    } else if (fix.have_approx_fix) {
      leds_set_status(run_blink_state ? LED_COLOR_GREEN : LED_COLOR_OFF);
    } else {
      leds_set_status(run_blink_state ? LED_COLOR_YELLOW : LED_COLOR_OFF);
    }
#endif
  }
}

void main() {
  start_watchdog();
  start_smp_lib();

  HANDLE_CRITICAL(boot());
  HANDLE_CRITICAL(start_udp_server());

  LOG_INF("GPS board booting complete");
#if IS_ENABLED(CONFIG_LIB_STATUS_LED)
  leds_set_status(LED_COLOR_GREEN);
#endif

  update_leds_forever();
}

#include <logging/log.h>
LOG_MODULE_REGISTER(gps, CONFIG_APP_LOG_LEVEL);

#include <zephyr/drivers/spi.h>
#include <zephyr/sys/atomic.h>
#include <zephyr/zephyr.h>

#include <drivers/gps.h>
#include <lib/ptp/ptp.h>
#include <utils/handle_errors.h>

#include "clock.h"
#include "gps.h"
#include "leds.h"
#include "rtc.h"

#if USE_DUAL_GPS
#include "dual_gps.h"
#endif

/*
 * Note depending on the GPS module configuration, it may not be able to maintian the
 * rate defined by GPS_POLL_DELAY_MS (default measure rate on f9p module is 1hz).
 * However, the work queue used to schedule these tasks handles this fine, as
 * per the documentation found
 * https://developer.nordicsemi.com/nRF_Connect_SDK/doc-legacy/1.2.1/zephyr/reference/kernel/threads/workqueue.html
 *
 * If an ISR or a thread attempts to submit a work item that is already pending,
 * the work item is not affected; the work item remains in its current place in
 * the workqueue's queue, and the work is only performed once.
 *
 * This prevents the queue from growing unbounded if for example we submit work
 * at 10hz but can only perform that work at 1hz
 */
#if IS_ENABLED(CONFIG_NET_TC_THREAD_COOPERATIVE)
#define THREAD_PRIORITY K_PRIO_COOP(CONFIG_NUM_COOP_PRIORITIES - 1)
#else
#define THREAD_PRIORITY K_PRIO_PREEMPT(8)
#endif

#define GPS_POLL_DELAY_MS 100
#define SAVE_TO_RTC_DELAY_MS 60000
#define RTC_RACE_AVOID_OFFSET_NS 2000000
#define NO_PPS_TIMEOUT 5000
#define SPARTN_BUF_LEN 1024
#define RTCM_BUF_LEN 1024

static struct net_ptp_time last_pps_clock_time;
static uint32_t last_pps_rtc_syncclock;
static int64_t last_save_to_rtc_ms = -1;
static bool rtc_offset_set = false;

static size_t spartn_buffer_size = 0;
static uint8_t spartn_buffer[SPARTN_BUF_LEN];

static size_t rtcm_buffer_size = 0;
static uint8_t rtcm_buffer[RTCM_BUF_LEN];

atomic_t flags = ATOMIC_INIT(0x1);
#define PPS_BIT 0
static bool valid = false;

static const struct device *gps = DEVICE_DT_GET(DT_ALIAS(gps0));

K_KERNEL_STACK_DEFINE(gps_work_q_stack, 1024);
struct k_work_q gps_work_q;

static void gps_updater(struct k_work *work);
K_WORK_DEFINE(gps_work, gps_updater);

static void gps_q_pusher(struct k_timer *timer_id);
K_TIMER_DEFINE(gps_timer, gps_q_pusher, NULL);

struct gps_query_result gps_query_result;
static int64_t last_pps_query_ms = -1;
K_MUTEX_DEFINE(gps_position_mutex);
K_MUTEX_DEFINE(spartn_buffer_mutex);
K_MUTEX_DEFINE(rtcm_buffer_mutex);

/**
 * @brief PPS callback (invoked via GPS driver)
 */
static void pps_callback(void *ctx) {
  HANDLE_CRITICAL(clock_get(&last_pps_clock_time));
  last_pps_rtc_syncclock = read_rtc_syncclock();
  atomic_clear_bit(&flags, PPS_BIT);
}

static void gps_updater(struct k_work *work) {
  bool set_clock = false;
  struct gps_query_result r;
  r.time_valid = false;
  if (!atomic_test_and_set_bit(&flags, PPS_BIT)) {
    set_clock = true;
    HANDLE_CRITICAL(led_set(&pps_led, 1));
  }
  if (valid || set_clock) {
    HANDLE_CRITICAL(gps_query(gps, &r));

    if (set_clock && claim_gps_control_clock()) {
      last_pps_query_ms = k_uptime_get();
      if (!r.time_valid) {
        LOG_WRN("Got PPS pulse but time is not valid, will try again.");
      } else {
        /* adjust clock */
        struct net_ptp_time last_pps_real_time = {
            .second = r.timestamp_ms / 1000,
            .nanosecond = 0,
        };
        HANDLE_CRITICAL(clock_adjust(&last_pps_clock_time, &last_pps_real_time, "GPS"));

        /* save time to RTC every minute */
        int64_t timestamp_ms = k_uptime_get();
        if (timestamp_ms - last_save_to_rtc_ms > SAVE_TO_RTC_DELAY_MS) {
          if (!rtc_offset_set) {
            /* set time with Xms offset to avoid race condition with RTC second boundary */
            HANDLE_CRITICAL(save_rtc_time(&last_pps_real_time, last_pps_rtc_syncclock, RTC_RACE_AVOID_OFFSET_NS));
            rtc_offset_set = true;
          } else {
            /* on the next second, save time without offset, and restart last_save_to_rtc_ms */
            HANDLE_CRITICAL(save_rtc_time(&last_pps_real_time, last_pps_rtc_syncclock, 0));
            rtc_offset_set = false;
            last_save_to_rtc_ms = timestamp_ms;
          }
        }
        valid = true;
      }
    }
  }
  if (r.time_valid) {
    /* update pos_vld LED */
    HANDLE_CRITICAL(led_set(&pos_vld_led, r.have_fix || r.have_approx_fix));
    k_mutex_lock(&gps_position_mutex, K_FOREVER);
    memcpy(&gps_query_result, &r, sizeof(gps_query_result));
    k_mutex_unlock(&gps_position_mutex);
  } else if (last_pps_query_ms == -1 || k_uptime_get() - last_pps_query_ms > NO_PPS_TIMEOUT) {
    HANDLE_CRITICAL(led_set(&pos_vld_led, 0));
    struct net_ptp_time current_time;
    HANDLE_CRITICAL(clock_get(&current_time));
    uint64_t timestamp_ms = ptp_ts_net_to_ms(&current_time);
    k_mutex_lock(&gps_position_mutex, K_FOREVER);
    memset(&gps_query_result, 0, sizeof(gps_query_result));
    gps_query_result.timestamp_ms = timestamp_ms;
    k_mutex_unlock(&gps_position_mutex);
    valid = false;
  }

  if (set_clock) {
    HANDLE_CRITICAL(led_set(&pps_led, 0));
  }
}

static void gps_q_pusher(struct k_timer *timer_id) {
  k_work_submit_to_queue(&gps_work_q, &gps_work);
#if USE_DUAL_GPS
  dual_gps_hook_position_query();
#endif
}
int start_gps() {
  /* setup GPS work queue */
  LOG_INF("Starting GPS");
  k_work_queue_start(&gps_work_q, gps_work_q_stack, K_KERNEL_STACK_SIZEOF(gps_work_q_stack), THREAD_PRIORITY, NULL);
  k_thread_name_set(&gps_work_q.thread, "gps_work_q");
  k_timer_start(&gps_timer, K_MSEC(GPS_POLL_DELAY_MS), K_MSEC(GPS_POLL_DELAY_MS));

  /* configure GPS */
  HANDLE_UNLIKELY_BOOL(device_is_ready(gps), ENODEV);
  HANDLE_UNLIKELY(gps_set_pps_callback(gps, pps_callback, NULL));

#if USE_DUAL_GPS
  HANDLE_UNLIKELY(dual_gps_init());
#endif

  return 0;
}

void get_latest_gps_query_result(struct gps_query_result *r) {
  k_mutex_lock(&gps_position_mutex, K_FOREVER);
  memcpy(r, &gps_query_result, sizeof(gps_query_result));
  k_mutex_unlock(&gps_position_mutex);
}

void send_spartn_data(const uint8_t *data, const size_t size, const bool eom) {
  k_mutex_lock(&spartn_buffer_mutex, K_FOREVER);
  if (spartn_buffer_size + size <= SPARTN_BUF_LEN) {
    memcpy(&spartn_buffer[spartn_buffer_size], data, size);
  }
  spartn_buffer_size += size;
  if (eom) {
    if (spartn_buffer_size <= SPARTN_BUF_LEN) {
      HANDLE_CRITICAL(gps_spartn(gps, spartn_buffer, spartn_buffer_size));
    } else {
      LOG_WRN("Skipping SPARTN msg as it could not fit in buffer.");
    }
    spartn_buffer_size = 0;
  }
  k_mutex_unlock(&spartn_buffer_mutex);
}

/**
 * @brief Buffer RTCM message and submit when full message is assembled
 */
void send_rtcm_data(const uint8_t *data, const size_t size, const bool eom) {
  k_mutex_lock(&rtcm_buffer_mutex, K_FOREVER);
  if (rtcm_buffer_size + size <= RTCM_BUF_LEN) {
    memcpy(&rtcm_buffer[rtcm_buffer_size], data, size);
  }
  rtcm_buffer_size += size;
  if (eom) {
    if (rtcm_buffer_size <= RTCM_BUF_LEN) {
      HANDLE_CRITICAL(gps_rtcm(gps, rtcm_buffer, rtcm_buffer_size));
    } else {
      LOG_WRN("Skipping RTCM msg as it could not fit in buffer.");
    }
    rtcm_buffer_size = 0;
  }
  k_mutex_unlock(&rtcm_buffer_mutex);
}

/**
 * @brief Query NMEA GGA message from the module
 */
int gps_get_last_gga(char *outBuffer, const size_t outBufferLen) {
  memset(outBuffer, 0, outBufferLen);
  HANDLE_UNLIKELY(gps_query_nmea_gga(gps, outBuffer, outBufferLen));

  return 0;
}

#include <zephyr/logging/log.h>
LOG_MODULE_DECLARE(gps, CONFIG_APP_LOG_LEVEL);

#include <math.h>

#include <zephyr/drivers/spi.h>
#include <zephyr/sys/atomic.h>
#include <zephyr/zephyr.h>

#include <drivers/gps.h>
#include <utils/handle_errors.h>

#include "dual_gps.h"
#include "eeprom.h"

static void poll_relpos(struct k_work *work);

static const struct device *gGpsPrimary = DEVICE_DT_GET(DT_ALIAS(gps0));
static const struct device *gGpsSecondary = DEVICE_DT_GET(DT_ALIAS(gps1));

static int apply_module_config();

typedef struct dual_gps_state {
  // Work queue used for polling the secondary GPS
  struct k_work_q workQueue;

  // Lock protecting the relative position data (and correction data)
  struct k_mutex relposLock;
  // Relative position data
  gps_relposned_result_t relpos;
  // Heading correction value
  float headingCorrection;
} dual_gps_state_t;

K_KERNEL_STACK_DEFINE(gSecondaryWorkQueueStack, 1024);
// Work item used to query the relative position/heading data
K_WORK_DEFINE(gPollRelposWork, poll_relpos);

__dtcm_bss_section static dual_gps_state_t gState;

/**
 * @brief Initialize dual GPS functionality
 */
int dual_gps_init() {
  int err;

  // set up work queue and other kernel objects
  k_mutex_init(&gState.relposLock);

  static const struct k_work_queue_config kWorkQueueConfig = {.name = "gps1_worker", .no_yield = false};
  k_work_queue_start(&gState.workQueue, gSecondaryWorkQueueStack, K_KERNEL_STACK_SIZEOF(gSecondaryWorkQueueStack),
                     K_PRIO_PREEMPT(9), &kWorkQueueConfig);

  // get the previously configured heading correction data
  err = eeprom_get_heading_correction(&gState.headingCorrection);
  if (err && err != -ENOENT) {
    LOG_WRN("%s failed: %d", "eeprom_get_heading_correction", err);
    return err;
  }

  LOG_INF("Restoring heading correction value: %d.%03u °", (int)gState.headingCorrection,
          ((int)(gState.headingCorrection * 1000.f)) % 1000);

  // set up the GPS module
  HANDLE_UNLIKELY(apply_module_config());
  return 0;
}

/**
 * @brief Configure secondary GPS module
 *
 * Set up the secondary GPS module in "rover" mode, to receive the RTCM messages via the dedicated
 * UART from the primary module.
 *
 * This does the following:
 *
 * - Configure UART2 baud rate on primary + secondary
 * - Input messages (RTCM) on secondary
 * - Output messages (RTCM) on primary
 */
static int apply_module_config() {
  // ensure module is initialized
  HANDLE_UNLIKELY_BOOL(device_is_ready(gGpsSecondary), ENODEV);
  /*
   * Increase navigation solution rate
   */
  LOG_DBG("Configuring navigation solution rate");
  static const gps_config_item_t kConfigNavRate[] = {
      // CFG-RATE-MEAS = 200 ms
      {kGpsConfigValueLength2, 0x30210001, {.word = 200}},
  };
  HANDLE_UNLIKELY(gps_set_config(gGpsPrimary, kConfigNavRate, ARRAY_SIZE(kConfigNavRate)));
  HANDLE_UNLIKELY(gps_set_config(gGpsSecondary, kConfigNavRate, ARRAY_SIZE(kConfigNavRate)));

  /*
   * Set UART2 on both modules to 460800 baud
   *
   * This is required to allow operation at up to 10Hz
   */
  LOG_DBG("Configuring UART2 baud rate");
  static const gps_config_item_t kConfigUart2Baud[] = {
      // CFG-UART2-BAUDRATE = 460800 baud
      {kGpsConfigValueLength4, 0x40530001, {.dword = 460800}},
  };
  HANDLE_UNLIKELY(gps_set_config(gGpsPrimary, kConfigUart2Baud, ARRAY_SIZE(kConfigUart2Baud)));
  HANDLE_UNLIKELY(gps_set_config(gGpsSecondary, kConfigUart2Baud, ARRAY_SIZE(kConfigUart2Baud)));

  /*
   * Enable RTCM message output via UART2 on primary GPS. At this point the UART should already
   * have been configured for a higher baud rate.
   *
   * The following messages are enabled:
   *
   * 4072.0: Reference station PVT info
   * 4072.1: Additional reference station info
   * 1074: GPS MSM4
   * 1084: GLONASS MSM4
   * 1094: Galileo MSM4
   * 1124: BeiDou MSM4
   * 1230: GLONASS code-phase biases
   */
  LOG_DBG("Configuring RTCM out");
  static const gps_config_item_t kConfigRtcmOut[] = {
      // CFG-MSGOUT-RTCM_3X_TYPE4072_0_UART2 = 1
      {kGpsConfigValueLength1, 0x20910300, {.byte = 1}},
      // CFG-MSGOUT-RTCM_3X_TYPE4072_1_UART2 = 0
      // We do not need 4072.1 (Additional reference station information) since we use firmware
      // version ≥ 1.12 for GPS modules; this will save us some bandwidth on the UART between
      // the F9P modules for correction data
      {kGpsConfigValueLength1, 0x20910383, {.byte = 0}},
      // CFG-MSGOUT-RTCM_3X_TYPE1074_UART2 = 1
      {kGpsConfigValueLength1, 0x20910360, {.byte = 1}},
      // CFG-MSGOUT-RTCM_3X_TYPE1084_UART2 = 1
      {kGpsConfigValueLength1, 0x20910365, {.byte = 1}},
      // CFG-MSGOUT-RTCM_3X_TYPE1094_UART2 = 1
      {kGpsConfigValueLength1, 0x2091036a, {.byte = 1}},
      // CFG-MSGOUT-RTCM_3X_TYPE1124_UART2 = 1
      {kGpsConfigValueLength1, 0x2091036f, {.byte = 1}},
      // CFG-MSGOUT-RTCM_3X_TYPE1230_UART2 = 1
      {kGpsConfigValueLength1, 0x20910305, {.byte = 1}},
  };
  HANDLE_UNLIKELY(gps_set_config(gGpsPrimary, kConfigRtcmOut, ARRAY_SIZE(kConfigRtcmOut)));

  return 0;
}

/**
 * @brief Primary position query hook
 *
 * Invoked whenever the timer to query the position from the primary GPS is fired; this will
 * push a work item that will poll the heading/relative position info from the secondary
 * module.
 */
void dual_gps_hook_position_query() { k_work_submit_to_queue(&gState.workQueue, &gPollRelposWork); }

/**
 * @brief Set the heading correction offset
 *
 * This value is added to the GPS heading (e.g. in order to calibrate out any residual error
 * between the true heading and the mounting of the antennas) for all data queried using the
 * `dual_gps_get_relpos` method.
 */
int dual_gps_set_heading_offset(const float offset) {
  if (offset <= -360.f || offset >= 360.f) {
    return -EDOM;
  }

  k_mutex_lock(&gState.relposLock, K_FOREVER);
  { gState.headingCorrection = offset; }
  k_mutex_unlock(&gState.relposLock);

  HANDLE_UNLIKELY(eeprom_set_heading_correction(offset));
  return 0;
}

/**
 * @brief Get the most recently queried relative position
 *
 * This will apply heading correction data, if configured.
 */
int dual_gps_get_relpos(gps_relposned_result_t *outRelpos) {
  if (!outRelpos) {
    return -EFAULT;
  }

  k_mutex_lock(&gState.relposLock, K_FOREVER);
  {
    gps_relposned_result_t data = gState.relpos;

    // apply heading correction: add offset and ensure it stays in [0, 360)
    data.rel_pos_heading = fmod((data.rel_pos_heading + gState.headingCorrection), 360);

    *outRelpos = data;
  }
  k_mutex_unlock(&gState.relposLock);

  return 0;
}

/**
 * @brief Get relative position work item
 *
 * Called on the secondary GPS work queue to retrieve the relative position information (e.g.
 * heading) from the secondary GPS module
 */
static void poll_relpos(struct k_work *work) {
  int err;
  gps_relposned_result_t res;

  err = gps_query_relpos(gGpsSecondary, &res);
  if (err) {
    LOG_WRN("%s failed: %d", "gps_query_relpos", err);
    return;
  }

  k_mutex_lock(&gState.relposLock, K_FOREVER);
  { gState.relpos = res; }
  k_mutex_unlock(&gState.relposLock);
}

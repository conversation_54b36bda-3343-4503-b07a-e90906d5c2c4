#include <logging/log.h>
LOG_MODULE_REGISTER(leds, CONFIG_APP_LOG_LEVEL);

#include <zephyr.h>

#include <utils/handle_errors.h>

#include "leds.h"

const struct gpio_dt_spec run_led = GPIO_DT_SPEC_GET(DT_PATH(leds, run), gpios);
const struct gpio_dt_spec tm_vld_led = GPIO_DT_SPEC_GET(DT_PATH(leds, tm_vld), gpios);
const struct gpio_dt_spec pps_led = GPIO_DT_SPEC_GET(DT_PATH(leds, pps), gpios);
const struct gpio_dt_spec pos_vld_led = GPIO_DT_SPEC_GET(DT_PATH(leds, pos_vld), gpios);
const struct gpio_dt_spec udp_led = GPIO_DT_SPEC_GET(DT_PATH(leds, udp), gpios);

static int configure_led(const struct gpio_dt_spec *led) {
  HANDLE_UNLIKELY_BOOL(device_is_ready(led->port), ENODEV);
  HANDLE_UNLIKELY(gpio_pin_configure_dt(led, GPIO_OUTPUT));
  return 0;
}

int led_set(const struct gpio_dt_spec *led, int value) { return gpio_pin_set(led->port, led->pin, value); }

int start_leds() {
  HANDLE_UNLIKELY(configure_led(&run_led));
  HANDLE_UNLIKELY(configure_led(&tm_vld_led));
  HANDLE_UNLIKELY(configure_led(&pps_led));
  HANDLE_UNLIKELY(configure_led(&pos_vld_led));
  HANDLE_UNLIKELY(configure_led(&udp_led));
  return 0;
}
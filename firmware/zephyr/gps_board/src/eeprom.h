/**
 * @file
 * @brief EEPROM manager
 *
 * Provides an interface to read out various data from the EEPROM.
 */
#pragma once

#include <stddef.h>
#include <stdint.h>

typedef enum {
  // Operate as a PTP grandmaster clock
  kEepromPtpRoleMaster = 0,
  // Use PTP slave client as standard slave clock mode
  kEepromPtpRoleSlave = 1,
} eeprom_ptp_role_t;

int eeprom_init();

int eeprom_set_heading_correction(const float newValue);
int eeprom_get_heading_correction(float *outValue);

int eeprom_set_ptp_mode(const eeprom_ptp_role_t newValue);
int eeprom_get_ptp_mode(eeprom_ptp_role_t *outValue);

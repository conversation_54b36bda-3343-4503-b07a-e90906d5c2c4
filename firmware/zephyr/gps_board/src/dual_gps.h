/**
 * @file
 * @brief Dual GPS support
 *
 * Handles initialization of the secondary GPS module, which operates in "moving base" mode.
 *
 * This has its own work queue so the SPI transactions can block waiting for data available
 * without stalling the interfacing to the primary module.
 */
#pragma once

#include <drivers/gps.h>

int dual_gps_init();

void dual_gps_hook_position_query();

int dual_gps_set_heading_offset(const float offset);
int dual_gps_get_relpos(gps_relposned_result_t *outRelpos);

#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(clock, CONFIG_APP_LOG_LEVEL);

#include <stdlib.h>

#include <zephyr/net/net_if.h>
#include <zephyr/zephyr.h>

#include <lib/ptp/ptp.h>
#include <utils/handle_errors.h>

#include "clock.h"
#include "eeprom.h"

#define MASTER_XFER_MS 3000
#define CLOCK_ADJUST_PID_KP 0.3
#define CLOCK_ADJUST_PID_KD 0.7

K_MUTEX_DEFINE(clock_control_mutex);

static int64_t last_rtc_control_ms = -1;
static int64_t last_gps_attempt_ms = -1;
static int64_t last_gps_control_ms = -1;

static bool last_rtc_control_clock = false;
static bool last_gps_control_clock = false;

static int64_t last_offset_ns = 0;

static bool ptp_master_started = false;

/// Flag set if we're operating in PTP master mode
static bool gIsPtpMaster = false;

bool claim_rtc_control_clock() {
  bool retval = true;

  k_mutex_lock(&clock_control_mutex, K_FOREVER);
  int64_t timestamp_ms = k_uptime_get();

  if (last_gps_attempt_ms >= 0 && timestamp_ms - last_gps_attempt_ms < MASTER_XFER_MS) {
    /* GPS is trying to take control, give up */
    retval = false;
  }

  if (retval) {
    last_rtc_control_ms = timestamp_ms;
  }
  k_mutex_unlock(&clock_control_mutex);

  if (retval != last_rtc_control_clock) {
    if (retval && gIsPtpMaster) {
      LOG_INF("Clock sync switched to RTC.");
    }
    last_rtc_control_clock = retval;
  }

  return retval;
}

bool claim_gps_control_clock() {
  bool retval = true;

  k_mutex_lock(&clock_control_mutex, K_FOREVER);
  int64_t timestamp_ms = k_uptime_get();

  if (last_rtc_control_ms >= 0 && timestamp_ms - last_rtc_control_ms < MASTER_XFER_MS) {
    /* RTC is still in control */
    retval = false;
  }

  last_gps_attempt_ms = timestamp_ms;
  if (retval) {
    last_gps_control_ms = timestamp_ms;
  }
  k_mutex_unlock(&clock_control_mutex);

  if (retval != last_gps_control_clock) {
    if (retval && gIsPtpMaster) {
      LOG_INF("Clock sync switched to GPS.");
    }
    last_gps_control_clock = retval;
  }

  return retval;
}

int64_t last_clock_control_claim() {
  k_mutex_lock(&clock_control_mutex, K_FOREVER);
  int64_t timestamp_ms = MAX(last_rtc_control_ms, last_gps_control_ms);
  k_mutex_unlock(&clock_control_mutex);
  return timestamp_ms;
}

/**
 * @brief Initialize the clock subsystem
 *
 * Depending on the EEPROM config, this will set up the clock as PTP master or slave.
 */
int clock_init() {
  struct net_if *iface = net_if_get_default();

  eeprom_ptp_role_t role;
  HANDLE_UNLIKELY(eeprom_get_ptp_mode(&role));

  if (role == kEepromPtpRoleMaster) {
    LOG_INF("PTP mode: %s", "Master");

    gIsPtpMaster = true;
    return ptp_master_init(iface);
  } else if (role == kEepromPtpRoleSlave) {
    LOG_INF("PTP mode: %s", "Slave");

    // TODO: should we randomize the PTP sync offset?
    return ptp_slave_init(iface, 100);
  } else {
    LOG_ERR("Unknown PTP clock mode: %d", role);
    return -EINVAL;
  }
}

/**
 * @brief Read out the current PTP time
 */
int clock_get(struct net_ptp_time *net) {
  if (gIsPtpMaster) {
    return ptp_master_clk_get(net);
  } else {
    return ptp_slave_clk_get(net);
  }
}

/**
 * @brief Adjust PTP clock time
 *
 * This is a no-op when not operating as PTP master.
 */
int clock_adjust(struct net_ptp_time *clock_tm, struct net_ptp_time *real_tm, const char *source) {
  if (!gIsPtpMaster) {
    return 0;
  }

  uint64_t clock_ns = ptp_ts_net_to_ns(clock_tm);
  uint64_t real_ns = ptp_ts_net_to_ns(real_tm);
  int64_t offset_ns = real_ns - clock_ns;

  struct net_ptp_time current_clock_tm;
  HANDLE_UNLIKELY(clock_get(&current_clock_tm));
  uint64_t current_clock_ns = ptp_ts_net_to_ns(&current_clock_tm);

  int64_t delay_ns = current_clock_ns - clock_ns;
  if (delay_ns > NSEC_PER_SEC / 2) {
    /*
     * Delay between acquiring clock and calling the clock_adjust() is too large,
     * ignore this update tick.
     */
    LOG_WRN("[%s] Clock (now): %lu.%09u, clock (sync): %lu.%09u, real (sync): %lu.%09u, offset: %c%lu.%09u", source,
            (unsigned long)current_clock_tm.second, current_clock_tm.nanosecond, (unsigned long)clock_tm->second,
            clock_tm->nanosecond, (unsigned long)real_tm->second, real_tm->nanosecond, offset_ns >= 0 ? '+' : '-',
            labs(offset_ns / NSEC_PER_SEC), abs(offset_ns % NSEC_PER_SEC));
    return 0;
  }

  LOG_INF("[%s] Clock (now): %lu.%09u, clock (sync): %lu.%09u, real (sync): %lu.%09u, offset: %c%lu.%09u", source,
          (unsigned long)current_clock_tm.second, current_clock_tm.nanosecond, (unsigned long)clock_tm->second,
          clock_tm->nanosecond, (unsigned long)real_tm->second, real_tm->nanosecond, offset_ns >= 0 ? '+' : '-',
          labs(offset_ns / NSEC_PER_SEC), abs(offset_ns % NSEC_PER_SEC));

  if (llabs(offset_ns) > NSEC_PER_SEC / 2) {
    /* adjust real time by delay between capture and now */
    uint64_t real_adj_ns = real_ns + delay_ns;
    struct net_ptp_time real_adj_tm = {
        .second = real_adj_ns / NSEC_PER_SEC,
        .nanosecond = real_adj_ns % NSEC_PER_SEC,
    };

    /* set clock */
    HANDLE_UNLIKELY(ptp_master_clk_set(&real_adj_tm));
    LOG_INF("PTP clock set: %lu.%09u", (unsigned long)real_adj_tm.second, real_adj_tm.nanosecond);
    last_offset_ns = 0;
  } else {
    /* compensate the offset using a PD controller */
    double rate = 1.0 + CLOCK_ADJUST_PID_KP * (double)offset_ns / NSEC_PER_SEC +
                  CLOCK_ADJUST_PID_KD * (double)(offset_ns - last_offset_ns) / NSEC_PER_SEC;
    int ret = ptp_master_clk_rate_adjust(rate);
    if (ret == -EINVAL) {
      /*
       * Proposed rate is outside of available bounds, we will find the closest acceptable rate.
       * Will make sure to avoid an infinite loop by having 0.1 .. 2.0 guardrails for sanity.
       */
      LOG_WRN("PTP clock rate %d.%03d not accepted, searching for acceptable rate for offset: %c%lu.%09u", (int)rate,
              abs(1000 * (rate - 1.0)), offset_ns >= 0 ? '+' : '-', labs(offset_ns / NSEC_PER_SEC),
              abs(offset_ns % NSEC_PER_SEC));
      double rate_adjustment = rate > 1.0 ? 0.99 : 1.01;
      while (rate > 0.1 && rate < 2.0) {
        rate *= rate_adjustment;
        ret = ptp_master_clk_rate_adjust(rate);
        if (ret != -EINVAL) {
          HANDLE_UNLIKELY(ret);
          LOG_INF("Accepted PTP clock rate %d.%03d.", (int)rate, abs(1000 * (rate - 1.0)));
          break;
        }
      }
      if (!(rate > 0.1 && rate < 2.0)) {
        LOG_ERR("Could not find acceptable PTP clock rate.");
        return 1;
      }
    } else {
      HANDLE_UNLIKELY(ret);
    }
    last_offset_ns = offset_ns;
  }

  /* start master if necessary */
  k_mutex_lock(&clock_control_mutex, K_FOREVER);
  if (!ptp_master_started) {
    HANDLE_UNLIKELY(ptp_master_start());
    ptp_master_started = true;
  }
  k_mutex_unlock(&clock_control_mutex);

  return 0;
}

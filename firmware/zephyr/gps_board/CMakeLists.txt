cmake_minimum_required(VERSION 3.13.1)

find_package(Zephyr REQUIRED HINTS $ENV{ZEPHYR_BASE})
project(gps_board)

# build protobuf defs separately
set(ROOT_PATH ../../..)
set(GENERATED_PROTO_PATH ${ROOT_PATH}/generated/lib/drivers/nanopb/proto)
file(GLOB PROTO_SOURCES CONFIGURE_DEPENDS ${GENERATED_PROTO_PATH}/*.c)
file(GLOB PROTO_HEADERS CONFIGURE_DEPENDS ${GENERATED_PROTO_PATH}/*.h)

zephyr_library_named(protobufs)
target_sources(protobufs PRIVATE
    ${PROTO_SOURCES} ${PROTO_HEADERS}
)
target_include_directories(protobufs PUBLIC ${ROOT_PATH})
# build app
target_sources(app PRIVATE
    src/clock.c
    src/clock.h
    src/gps.c
    src/gps.h
    src/leds.c
    src/leds.h
    src/main.c
    src/rtc.c
    src/rtc.h
    src/udp_server.c
    src/udp_server.h
    src/eeprom.c
    src/eeprom.h
)

target_link_libraries(app PRIVATE protobufs)

# sources for dual GPS
if("${CMAKE_C_FLAGS}" MATCHES "USE_DUAL_GPS=1")
    target_sources(app PRIVATE
        src/dual_gps.c
        src/dual_gps.h
    )
endif()

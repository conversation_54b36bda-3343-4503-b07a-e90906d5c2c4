/ {

    chosen {
        zephyr,code-partition = &slot0_partition;
    };
    
    aliases {
        watchdog0 = &iwdg1;
        eeprom0 = &eeprom0;
        rtc0 = &rtc0;
        gps0 = &gps0;
    };

    gpios {
        compatible = "gpio-keys";
        pps_in {
            gpios = <&gpioe 2 GPIO_ACTIVE_HIGH>;
            label = "PPS_IN";
        };
        gps_nrst {
            gpios = <&gpioa 3 (GPIO_PULL_UP | GPIO_ACTIVE_LOW)>;
            label = "GPS_nRST";
        };
        gps_int {
            gpios = <&gpioa 10 GPIO_ACTIVE_HIGH>;
            label = "GPS_INT";
        };
        rtc_nrst {
            gpios = <&gpioc 8 (GPIO_PULL_UP | GPIO_ACTIVE_LOW)>;
            label = "RTC_nRST";
        };
        rtc_nint {
            gpios = <&gpiob 7 (GPIO_PULL_UP | GPIO_ACTIVE_LOW)>;
            label = "RTC_nINT";
        };
    };

    leds {
        compatible = "gpio-leds";
        run {
            gpios = <&gpioc 0 GPIO_ACTIVE_HIGH>;
            label = "RUN_LED";
        };
        tm_vld {
            gpios = <&gpioc 2 GPIO_ACTIVE_HIGH>;
            label = "TM_VLD_LED";
        };
        pps {
            gpios = <&gpioc 3 GPIO_ACTIVE_HIGH>;
            label = "PPS_LED";
        };
        pos_vld {
            gpios = <&gpioc 6 GPIO_ACTIVE_HIGH>;
            label = "POS_VLD_LED";
        };
        udp {
            gpios = <&gpioc 7 GPIO_ACTIVE_HIGH>;
            label = "UDP_LED";
        };
    };
};

&iwdg1 {
    status = "okay";
};

&i2c2 {
    status = "okay";
    pinctrl-0 = <&i2c2_sda_pb11 &i2c2_scl_pb10>;
    pinctrl-names = "default";

    /* ATMEL AT24C04D */
    eeprom0: atmel_at24@a0 {
        compatible = "atmel,at24";
        reg = <0x50>;
        /**
         * Whole EEPROM is 512 bytes, but address 0x50 will only access half of it.
         * Use address 0x51 to access the other half.
         */
        size = <256>;
        pagesize = <16>;
        address-width = <8>;
        timeout = <5>;
        label = "ATMEL_AT24";
    };

    /* DS3231 */
    rtc0: ds3231@68 {
        compatible = "maxim,ds3231";
        reg = <0x68>;
        label = "DS3231";
        isw-gpios = <&gpiob 7 (GPIO_PULL_UP | GPIO_ACTIVE_LOW)>;
    };
};

&spi1 {
    pinctrl-0 = <&spi1_sck_pa5 &spi1_miso_pa6 &spi1_mosi_pb5 &spi1_nss_pa4>;
    pinctrl-names = "default";
    status = "okay";

    gps0: gps_zed_f9p@0 {
        compatible = "cr,gps-zed-f9p";
        label = "GPS_ZED-F9P";
        reg = <0>;
        spi-max-frequency = < 1000000 >;
    };
};

&clk_hse {
    /delete-property/ hse-bypass;
};

# Config changes format version 1.0
# created by u-center version 20.10 at 23:26:17 on Sunday, 16 Jan 2022
[del]
[set]
  BBR CFG-NAVSPG-DYNMODEL  3                    # write value 3 - PED              to item id 0x20110021 in layer 1
  BBR CFG-MSGOUT-NMEA_ID_RMC_I2C 0x0                  # write value 0  0x0               to item id 0x209100ab in layer
  BBR CFG-MSGOUT-NMEA_ID_RMC_UART1 0x0                  # write value 0  0x0               to item id 0x209100ac in lay
  BBR CFG-MSGOUT-NMEA_ID_RMC_UART2 0x0                  # write value 0  0x0               to item id 0x209100ad in lay
  BBR CFG-MSGOUT-NMEA_ID_RMC_USB 0x0                  # write value 0  0x0               to item id 0x209100ae in layer
  BBR CFG-MSGOUT-NMEA_ID_RMC_SPI 0x0                  # write value 0  0x0               to item id 0x209100af in layer
  BBR CFG-MSGOUT-NMEA_ID_VTG_I2C 0x0                  # write value 0  0x0               to item id 0x209100b0 in layer
  BBR CFG-MSGOUT-NMEA_ID_VTG_UART1 0x0                  # write value 0  0x0               to item id 0x209100b1 in lay
  BBR CFG-MSGOUT-NMEA_ID_VTG_UART2 0x0                  # write value 0  0x0               to item id 0x209100b2 in lay
  BBR CFG-MSGOUT-NMEA_ID_VTG_USB 0x0                  # write value 0  0x0               to item id 0x209100b3 in layer
  BBR CFG-MSGOUT-NMEA_ID_VTG_SPI 0x0                  # write value 0  0x0               to item id 0x209100b4 in layer
  BBR CFG-MSGOUT-NMEA_ID_GGA_I2C 0x0                  # write value 0  0x0               to item id 0x209100ba in layer
  BBR CFG-MSGOUT-NMEA_ID_GGA_UART1 0x0                  # write value 0  0x0               to item id 0x209100bb in lay
  BBR CFG-MSGOUT-NMEA_ID_GGA_UART2 0x0                  # write value 0  0x0               to item id 0x209100bc in lay
  BBR CFG-MSGOUT-NMEA_ID_GGA_USB 0x0                  # write value 0  0x0               to item id 0x209100bd in layer
  BBR CFG-MSGOUT-NMEA_ID_GGA_SPI 0x0                  # write value 0  0x0               to item id 0x209100be in layer
  BBR CFG-MSGOUT-NMEA_ID_GSA_I2C 0x0                  # write value 0  0x0               to item id 0x209100bf in layer
  BBR CFG-MSGOUT-NMEA_ID_GSA_UART1 0x0                  # write value 0  0x0               to item id 0x209100c0 in lay
  BBR CFG-MSGOUT-NMEA_ID_GSA_UART2 0x0                  # write value 0  0x0               to item id 0x209100c1 in lay
  BBR CFG-MSGOUT-NMEA_ID_GSA_USB 0x0                  # write value 0  0x0               to item id 0x209100c2 in layer
  BBR CFG-MSGOUT-NMEA_ID_GSA_SPI 0x0                  # write value 0  0x0               to item id 0x209100c3 in layer
  BBR CFG-MSGOUT-NMEA_ID_GSV_I2C 0x0                  # write value 0  0x0               to item id 0x209100c4 in layer
  BBR CFG-MSGOUT-NMEA_ID_GSV_UART1 0x0                  # write value 0  0x0               to item id 0x209100c5 in lay
  BBR CFG-MSGOUT-NMEA_ID_GSV_UART2 0x0                  # write value 0  0x0               to item id 0x209100c6 in lay
  BBR CFG-MSGOUT-NMEA_ID_GSV_USB 0x0                  # write value 0  0x0               to item id 0x209100c7 in layer
  BBR CFG-MSGOUT-NMEA_ID_GSV_SPI 0x0                  # write value 0  0x0               to item id 0x209100c8 in layer
  BBR CFG-MSGOUT-NMEA_ID_GLL_I2C 0x0                  # write value 0  0x0               to item id 0x209100c9 in layer
  BBR CFG-MSGOUT-NMEA_ID_GLL_UART1 0x0                  # write value 0  0x0               to item id 0x209100ca in lay
  BBR CFG-MSGOUT-NMEA_ID_GLL_UART2 0x0                  # write value 0  0x0               to item id 0x209100cb in lay
  BBR CFG-MSGOUT-NMEA_ID_GLL_USB 0x0                  # write value 0  0x0               to item id 0x209100cc in layer
  BBR CFG-MSGOUT-NMEA_ID_GLL_SPI 0x0                  # write value 0  0x0               to item id 0x209100cd in layer
  BBR CFG-INFMSG-NMEA_I2C  0x0                  # write value 0  0x0               to item id 0x20920006 in layer 1
  BBR CFG-INFMSG-NMEA_UART1 0x0                  # write value 0  0x0               to item id 0x20920007 in layer 1
  BBR CFG-INFMSG-NMEA_UART2 0x0                  # write value 0  0x0               to item id 0x20920008 in layer 1
  BBR CFG-INFMSG-NMEA_USB  0x0                  # write value 0  0x0               to item id 0x20920009 in layer 1
  BBR CFG-INFMSG-NMEA_SPI  0x0                  # write value 0  0x0               to item id 0x2092000a in layer 1
Flash CFG-NAVSPG-DYNMODEL  3                    # write value 3 - PED              to item id 0x20110021 in layer 2
Flash CFG-MSGOUT-NMEA_ID_RMC_I2C 0x0                  # write value 0  0x0               to item id 0x209100ab in layer
Flash CFG-MSGOUT-NMEA_ID_RMC_UART1 0x0                  # write value 0  0x0               to item id 0x209100ac in lay
Flash CFG-MSGOUT-NMEA_ID_RMC_UART2 0x0                  # write value 0  0x0               to item id 0x209100ad in lay
Flash CFG-MSGOUT-NMEA_ID_RMC_USB 0x0                  # write value 0  0x0               to item id 0x209100ae in layer
Flash CFG-MSGOUT-NMEA_ID_RMC_SPI 0x0                  # write value 0  0x0               to item id 0x209100af in layer
Flash CFG-MSGOUT-NMEA_ID_VTG_I2C 0x0                  # write value 0  0x0               to item id 0x209100b0 in layer
Flash CFG-MSGOUT-NMEA_ID_VTG_UART1 0x0                  # write value 0  0x0               to item id 0x209100b1 in lay
Flash CFG-MSGOUT-NMEA_ID_VTG_UART2 0x0                  # write value 0  0x0               to item id 0x209100b2 in lay
Flash CFG-MSGOUT-NMEA_ID_VTG_USB 0x0                  # write value 0  0x0               to item id 0x209100b3 in layer
Flash CFG-MSGOUT-NMEA_ID_VTG_SPI 0x0                  # write value 0  0x0               to item id 0x209100b4 in layer
Flash CFG-MSGOUT-NMEA_ID_GGA_I2C 0x0                  # write value 0  0x0               to item id 0x209100ba in layer
Flash CFG-MSGOUT-NMEA_ID_GGA_UART1 0x0                  # write value 0  0x0               to item id 0x209100bb in lay
Flash CFG-MSGOUT-NMEA_ID_GGA_UART2 0x0                  # write value 0  0x0               to item id 0x209100bc in lay
Flash CFG-MSGOUT-NMEA_ID_GGA_USB 0x0                  # write value 0  0x0               to item id 0x209100bd in layer
Flash CFG-MSGOUT-NMEA_ID_GGA_SPI 0x0                  # write value 0  0x0               to item id 0x209100be in layer
Flash CFG-MSGOUT-NMEA_ID_GSA_I2C 0x0                  # write value 0  0x0               to item id 0x209100bf in layer
Flash CFG-MSGOUT-NMEA_ID_GSA_UART1 0x0                  # write value 0  0x0               to item id 0x209100c0 in lay
Flash CFG-MSGOUT-NMEA_ID_GSA_UART2 0x0                  # write value 0  0x0               to item id 0x209100c1 in lay
Flash CFG-MSGOUT-NMEA_ID_GSA_USB 0x0                  # write value 0  0x0               to item id 0x209100c2 in layer
Flash CFG-MSGOUT-NMEA_ID_GSA_SPI 0x0                  # write value 0  0x0               to item id 0x209100c3 in layer
Flash CFG-MSGOUT-NMEA_ID_GSV_I2C 0x0                  # write value 0  0x0               to item id 0x209100c4 in layer
Flash CFG-MSGOUT-NMEA_ID_GSV_UART1 0x0                  # write value 0  0x0               to item id 0x209100c5 in lay
Flash CFG-MSGOUT-NMEA_ID_GSV_UART2 0x0                  # write value 0  0x0               to item id 0x209100c6 in lay
Flash CFG-MSGOUT-NMEA_ID_GSV_USB 0x0                  # write value 0  0x0               to item id 0x209100c7 in layer
Flash CFG-MSGOUT-NMEA_ID_GSV_SPI 0x0                  # write value 0  0x0               to item id 0x209100c8 in layer
Flash CFG-MSGOUT-NMEA_ID_GLL_I2C 0x0                  # write value 0  0x0               to item id 0x209100c9 in layer
Flash CFG-MSGOUT-NMEA_ID_GLL_UART1 0x0                  # write value 0  0x0               to item id 0x209100ca in lay
Flash CFG-MSGOUT-NMEA_ID_GLL_UART2 0x0                  # write value 0  0x0               to item id 0x209100cb in lay
Flash CFG-MSGOUT-NMEA_ID_GLL_USB 0x0                  # write value 0  0x0               to item id 0x209100cc in layer
Flash CFG-MSGOUT-NMEA_ID_GLL_SPI 0x0                  # write value 0  0x0               to item id 0x209100cd in layer
Flash CFG-INFMSG-NMEA_I2C  0x0                  # write value 0  0x0               to item id 0x20920006 in layer 2
Flash CFG-INFMSG-NMEA_UART1 0x0                  # write value 0  0x0               to item id 0x20920007 in layer 2
Flash CFG-INFMSG-NMEA_UART2 0x0                  # write value 0  0x0               to item id 0x20920008 in layer 2
Flash CFG-INFMSG-NMEA_USB  0x0                  # write value 0  0x0               to item id 0x20920009 in layer 2
Flash CFG-INFMSG-NMEA_SPI  0x0                  # write value 0  0x0               to item id 0x2092000a in layer 2

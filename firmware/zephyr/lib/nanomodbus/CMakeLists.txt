# Fetch the nanomodbus upstream
include(FetchContent)
FetchContent_Declare(
    nanomodbus
    GIT_REPOSITORY  https://github.com/debevv/nanoMODBUS.git
    GIT_TAG         v1.20.0
)

FetchContent_GetProperties(nanomodbus)
if(NOT nanomodbus_POPULATED)
    FetchContent_Populate(nanomodbus)
endif()

add_library(nanomodbus STATIC
    ${nanomodbus_SOURCE_DIR}/nanomodbus.c
)
target_link_libraries(nanomodbus PUBLIC zephyr_interface)
target_compile_definitions(nanomodbus PUBLIC -DNMBS_SERVER_DISABLED=1)
target_include_directories(nanomodbus PUBLIC ${nanomodbus_SOURCE_DIR})

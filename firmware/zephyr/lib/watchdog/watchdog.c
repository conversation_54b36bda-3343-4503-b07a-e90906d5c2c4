#include <logging/log.h>
LOG_MODULE_REGISTER(lib_watchdog, CONFIG_LIB_WATCHDOG_LOG_LEVEL);

#include <drivers/watchdog.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

#define WDT_TIMEOUT_MS 5000U
#define WDT_NODE DT_ALIAS(watchdog0)

static void process_watchdog();

K_THREAD_DEFINE(watchdog_thread_id, 1024, process_watchdog, NULL, NULL, NULL, K_LOWEST_APPLICATION_THREAD_PRIO, 0,
                K_TICKS_FOREVER);

static int setup_watchdog(const struct device **wdt, int *wdt_channel_id) {
  *wdt = DEVICE_DT_GET(WDT_NODE);
  HANDLE_UNLIKELY_BOOL(device_is_ready(*wdt), ENODEV);

  /* install watchdog timeout */
  struct wdt_timeout_cfg wdt_config = {
      /* Reset SoC when watchdog timer expires. */
      .flags = WDT_FLAG_RESET_SOC,

      /* Expire watchdog after max window */
      .window.min = 0U,
      .window.max = WDT_TIMEOUT_MS,
  };
  *wdt_channel_id = wdt_install_timeout(*wdt, &wdt_config);
  HANDLE_UNLIKELY(*wdt_channel_id);

  /* pause watchdog during debugging */
  HANDLE_UNLIKELY(wdt_setup(*wdt, WDT_OPT_PAUSE_HALTED_BY_DBG));

  return 0;
}

static void process_watchdog() {
  int wdt_channel_id = -1;
  const struct device *wdt;
  HANDLE_CRITICAL(setup_watchdog(&wdt, &wdt_channel_id));
  for (;;) {
    wdt_feed(wdt, wdt_channel_id);
    k_msleep(50);
  }
}

void start_watchdog() {
  k_thread_name_set(watchdog_thread_id, "watchdog");
  k_thread_start(watchdog_thread_id);
}

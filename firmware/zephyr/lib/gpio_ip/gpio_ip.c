#include <logging/log.h>
LOG_MODULE_REGISTER(lib_gpio_ip, CONFIG_LIB_GPIO_IP_LOG_LEVEL);

#include <device.h>
#include <drivers/gpio.h>
#include <drivers/hw_rev_straps.h>
#include <drivers/hwinfo.h>
#include <lib/gpio_ip/gpio_ip.h>
#include <net/ethernet_mgmt.h>
#include <net/net_if.h>
#include <net/net_ip.h>
#include <sys/math_extras.h>

#include <stdlib.h>
#include <string.h>

#include <utils/handle_errors.h>

// Whether the current device has a nonvolatile, unique hw id
// TODO: technically the slayer H7xx scanners can have this but they'd need to link hwinfo module
#define IS_NEW_SCANNER (CONFIG_BOARD_SCANNER_H753_REAPER || CONFIG_BOARD_SCANNER_H753_SLAYER)
#define USE_HWINFO_ID_MACADDR (CONFIG_SOC_SERIES_STM32H7X && IS_NEW_SCANNER)

#if USE_HWINFO_ID_MACADDR
/**
 * @brief Calculate a non-cryptographic hash over the given data (for MAC address)
 */
static uint32_t hash(const uint8_t *in, const size_t length) {
  uint32_t hash = 5381;

  for (size_t i = 0; i < length; ++in, ++i) {
    hash = ((hash << 5) + hash) + (*in);
  }

  return hash;
}

/**
 * @brief Calculate stable MAC address and assign
 *
 * This hashes the unique id of the chip and generates a locally administered MAC address. The
 * generated MAC address will have the top two octets of 0x0200, with the lower four being a
 * hash of the chip unique id.
 */
static int set_mac_device_id(struct net_if *intf) {
  struct ethernet_req_params params;
  char uniqueId[16], macAddr[6];
  uint32_t hwRev = 0;

  static const struct device *gHwRevs = DEVICE_DT_GET_OR_NULL(DT_PATH(hw_rev));
  if (gHwRevs) {
    HANDLE_UNLIKELY(hw_rev_straps_get(gHwRevs, &hwRev));
  }

  int uniqueIdLen = hwinfo_get_device_id(uniqueId, sizeof(uniqueId));
  if (uniqueIdLen < 0) {
    return -EINVAL;
  }

  const uint32_t low = hash(uniqueId, uniqueIdLen);

  memset(macAddr, 0, sizeof(macAddr));
  macAddr[0] = 0b00100010; // unicast, locally administered
  macAddr[1] = hwRev & 0xff;
  memcpy(macAddr + 2, &low, 4);

  LOG_INF("MAC address: %02x:%02x:%02x:%02x:%02x:%02x", macAddr[0], macAddr[1], macAddr[2], macAddr[3], macAddr[4],
          macAddr[5]);

  // now apply to the interface
  memset(&params, 0, sizeof(params));
  memcpy(params.mac_address.addr, macAddr, sizeof(macAddr));

  HANDLE_UNLIKELY(net_mgmt(NET_REQUEST_ETHERNET_SET_MAC_ADDRESS, intf, &params, sizeof(params)));
  return 0;
}
#else
/**
 * @brief Offset existing MAC by given number
 *
 * Use the existing MAC address (decided by Zephyr) and offset one of its bytes. Used for GD
 * scanners where we don't have an unique id stored.
 */
bool set_mac_addr_with_offset(const uint8_t offset) {
  struct net_if *iface = net_if_get_default();
  struct net_linkaddr *iface_addr = net_if_get_link_addr(iface);
  struct ethernet_req_params params;
  int ret;

  memcpy(params.mac_address.addr, iface_addr->addr, iface_addr->len);
  params.mac_address.addr[4] += offset;

  ret = net_mgmt(NET_REQUEST_ETHERNET_SET_MAC_ADDRESS, iface, &params, sizeof(struct ethernet_req_params));

  if (ret) {
    return false;
  }

  return true;
}
#endif

/**
 * @brief Update the Ethernet MAC address
 *
 * If we're on an STM32H7xx chip, which have a unique silicon ID, use that to assign a MAC address;
 * otherwise, generate a random one but offset it by the address pin value.
 */
static bool update_mac(struct net_if *intf, const uint8_t offset) {
#if USE_HWINFO_ID_MACADDR
  HANDLE_CRITICAL(set_mac_device_id(intf));
#else
  return set_mac_addr_with_offset(offset);
#endif

  return true;
}

/**
 * @brief Set IP address of the given interface
 *
 * Remove all existing addresses, then add the specified address.
 */
static bool update_address(struct net_if *intf, struct in_addr *newAddr) {
  int err;
  char addrStr[INET_ADDRSTRLEN];
  struct net_if_ipv4 *config;

  // remove old address
  if ((err = net_if_config_ipv4_get(intf, &config))) {
    LOG_ERR("%s failed: %d", "net_if_config_ipv4_get", err);
    return false;
  }

  net_addr_ntop(AF_INET, &config->unicast[0].address.in_addr, addrStr, sizeof(addrStr));
  LOG_DBG("old IP address: %s", addrStr);

  if (!net_if_ipv4_addr_rm(intf, &config->unicast[0].address.in_addr)) {
    LOG_ERR("%s failed", "net_if_ipv4_addr_rm");
    return false;
  }

  // add the new one
  struct net_if_addr *newAddrSet = net_if_ipv4_addr_add(intf, newAddr, AF_INET, 0);
  if (!newAddrSet) {
    LOG_ERR("%s failed", "net_if_ipv4_addr_add");
    return false;
  }

  // update upstream gateway address (for Reaper)
#ifdef USE_REAPER
  struct in_addr gateway = *newAddr, netmask;

  gateway.s4_addr[3] -= 1;
  net_if_ipv4_set_gw(intf, &gateway);

  net_addr_ntop(AF_INET, &gateway, addrStr, sizeof(addrStr));
  LOG_INF("Updated %s to: %s", "gateway", addrStr);

  // also update the netmask (we're using a /30)
  HANDLE_CRITICAL(net_addr_pton(AF_INET, "***************", &netmask));
  net_if_ipv4_set_netmask(intf, &netmask);
#endif

  // print the address we actually assigned
  net_addr_ntop(AF_INET, newAddr, addrStr, sizeof(addrStr));
  LOG_INF("Updated %s to: %s", "unicast address", addrStr);
  return true;
}

/**
 * @brief Read address pins to get binary number
 */
static uint8_t read_address(const gpio_ip_data *gpio_data, const uint8_t numGpios) {
  // set up the GPIOs as an input
  for (uint8_t i = 0; i < numGpios; ++i) {
    const struct device *gpio = device_get_binding(gpio_data[i].label);
    HANDLE_CRITICAL(gpio_pin_configure(gpio, gpio_data[i].pin, GPIO_INPUT | GPIO_ACTIVE_LOW));
  }
  k_sleep(K_MSEC(10)); // NEEDED to give time for cfg to take effect

  // read out each line in sequence
  uint8_t octet = 0;
  for (uint8_t i = 0; i < numGpios; ++i) {
    const struct device *gpio = device_get_binding(gpio_data[i].label);
    octet |= (gpio_pin_get(gpio, gpio_data[i].pin) & 1) << i;
  }

  return octet;
}

/**
 * @brief Set IP address based on IO pins
 *
 * Read out the address pins as an integer, then _add_ that value to the lowest octet of the
 * specified IP address.
 */
bool gpio_ip_set_ip(const char *orig_ip, const char *base_ip, gpio_ip_data *gpio_data, uint8_t gpio_data_size) {
  struct net_if *iface = net_if_get_default();
  struct in_addr new_ip_addr, orig_ip_addr;

  HANDLE_CRITICAL(net_addr_pton(AF_INET, orig_ip, &orig_ip_addr));
  HANDLE_CRITICAL(net_addr_pton(AF_INET, base_ip, &new_ip_addr));

  // take link down
  HANDLE_CRITICAL(net_if_down(iface));

  // read address pins
  const uint8_t octet = read_address(gpio_data, gpio_data_size);

  // update MAC address
  if (!update_mac(iface, octet)) {
    LOG_ERR("Failed to set MAC address");
    return false;
  }

  // calculate new address and apply to interface
  new_ip_addr.s4_addr[3] += octet;
  if (net_ipv4_addr_cmp(&orig_ip_addr, &new_ip_addr)) {
    LOG_DBG("No need to change IP address from default");
    goto beach;
  }

  if (!update_address(iface, &new_ip_addr)) {
    LOG_ERR("failed to update IP address");
    return false;
  }

beach:;
  // bring link back up (with new address + MAC)
  HANDLE_CRITICAL(net_if_up(iface));

  return true;
}

/**
 * @brief Set IP address from list, based on IO pins
 *
 * Read out the specified GPIOs as address input and convert to a number; then use that to index
 * into the specified array and apply that as an IP address.
 *
 * @param outIndex If set, receives the address pin index
 */
bool gpio_ip_set_ip_list(const char **choices, const gpio_ip_data *gpio_data, const uint8_t gpio_data_size,
                         unsigned int *outIndex) {
  struct net_if *iface = net_if_get_default();
  struct in_addr newAddr;

  // take link down
  HANDLE_CRITICAL(net_if_down(iface));

  // read address pins and update MAC address
  const uint8_t octet = read_address(gpio_data, gpio_data_size);

  if (!update_mac(iface, octet)) {
    LOG_ERR("Failed to set MAC address");
    return false;
  }

  // select and apply the new address
  LOG_DBG("selected address: `%s`", choices[octet]);
  HANDLE_CRITICAL(net_addr_pton(AF_INET, choices[octet], &newAddr));

  if (!update_address(iface, &newAddr)) {
    LOG_ERR("failed to update IP address");
    return false;
  }

  if (outIndex) {
    *outIndex = octet;
  }

  // bring link back up (with new address + MAC)
  HANDLE_CRITICAL(net_if_up(iface));

  return true;
}

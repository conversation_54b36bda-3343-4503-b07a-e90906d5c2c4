#include <logging/log.h>
LOG_MODULE_REGISTER(lib_history_list, CONFIG_LIB_HISTORY_LIST_LOG_LEVEL);

#include <lib/history_list/history_list.h>
#include <string.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

#define HIST_LIST_PREV_IDX(h, x) ((x) > 0 ? (x)-1 : h->num_elements - 1)
#define HIST_LIST_NEXT_IDX(h, x) ((x) < h->num_elements - 1 ? (x) + 1 : 0)
#define RELATIVE_INDEX(h, start, idx) (((start) + (idx)) % h->num_elements)

static inline size_t elem_idx_to_buf_idx(history_list *h, size_t idx) {
  return (h->record_size + sizeof(uint64_t)) * idx;
}
static inline size_t buf_ptr_to_elem_idx(history_list *h, uint8_t *ptr) {
  return ((size_t)(ptr - h->records)) / (h->record_size + sizeof(uint64_t));
}
static void get_record_by_elem_idx(history_list *h, size_t idx, history_record *output) {
  uint8_t *ptr = &h->records[elem_idx_to_buf_idx(h, idx)];
  output->usec = (uint64_t *)ptr;
  ptr += sizeof(uint64_t);
  output->record = ptr;
}

static int matched(history_list *h, int index, uint64_t usec) {
  history_record cur, next;
  get_record_by_elem_idx(h, index, &cur);
  get_record_by_elem_idx(h, HIST_LIST_NEXT_IDX(h, index), &next);
  int64_t curr_diff = usec - *(cur.usec);
  int64_t next_diff = usec - *(next.usec);
  if (next_diff == 0 && curr_diff == 0) {
    /* Special case where the list hasn't been filled in yet */
    return 1;
  }
  return curr_diff >= 0 && next_diff < 0;
}

void history_list_init(history_list *h) {
  memset(h->records, 0, h->num_elements * (sizeof(uint64_t) + h->record_size));
}
void history_list_add_record(history_list *h, const history_record *record) {
  size_t tmp = HIST_LIST_NEXT_IDX(h, h->last);
  history_record list_record;
  get_record_by_elem_idx(h, tmp, &list_record);
  *(list_record.usec) = *(record->usec);
  memcpy(list_record.record, record->record, h->record_size);
  h->last = tmp; // Only set after data has been populated
}
int history_list_get_latest(history_list *h, history_record *output) {
  get_record_by_elem_idx(h, h->last, output);
  return h->last;
}
int history_list_get_oldest(history_list *h, history_record *output) {
  size_t elem = HIST_LIST_NEXT_IDX(h, h->last);
  get_record_by_elem_idx(h, elem, output);
  return elem;
}
int history_list_get_next(history_list *h, int index, history_record *output) {
  size_t start = HIST_LIST_NEXT_IDX(h, h->last);
  if (index >= 0) {
    index = HIST_LIST_NEXT_IDX(h, index);
    /* Off the end */
    if (index == start) {
      return HISTORY_LIST_FUTURE_HIST;
    }
    get_record_by_elem_idx(h, index, output);
    return index;
  }
  /* Was before the list, now moving to the beginning */
  if (index == HISTORY_LIST_PAST_HIST) {
    get_record_by_elem_idx(h, start, output);
    return start;
  }
  if (index == HISTORY_LIST_FUTURE_HIST) {
    return HISTORY_LIST_FUTURE_HIST;
  }
  return HISTORY_LIST_PAST_HIST; // This should never happen
}

int history_list_get_best(history_list *h, uint64_t usec, history_record *output) {
  // Leave room in buffer to be filled so we can run lockless
  size_t start_idx = (h->last + h->num_elements / 10) % h->num_elements;
  size_t start = 0;
  history_record cur;
  /* Check for before or after the whole list */
  get_record_by_elem_idx(h, start_idx, &cur);
  if (usec < *cur.usec) {
    // Time is in the past
    return HISTORY_LIST_PAST_HIST;
  }
  size_t end = h->last;
  get_record_by_elem_idx(h, end, &cur);
  if (usec >= *cur.usec) {
    // Current or future time
    output->usec = cur.usec;
    output->record = cur.record;
    return end;
  }

  // convert end to relative index
  if (end > start_idx) {
    end -= start_idx;
  } else {
    end += h->num_elements - start_idx;
  }
  /* Do the bin search */
  int64_t cmp;
  size_t pivot, rel;
  while (1) {
    pivot = (start + end) / 2;                 // pivot in the middle
    rel = RELATIVE_INDEX(h, start_idx, pivot); // convert to array offset
    get_record_by_elem_idx(h, rel, &cur);
    if (matched(h, rel, usec)) // look for ts in the gap
    {
      output->usec = cur.usec;
      output->record = cur.record;
      return rel;
    }
    cmp = usec - *cur.usec;
    if (cmp > 0) {
      start = pivot + 1;
    } else {
      end = pivot - 1;
    }
  }
  return HISTORY_LIST_PAST_HIST;
}
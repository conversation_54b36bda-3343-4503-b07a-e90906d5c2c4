# Status LED Helper

This helper library is used to drive the RGB status LED as well as auxiliary heartbeat/debug LEDs on new Carbon boards. This library supports both LEDs controlled via GPIOs (on/off) or PWMs (intensity) and is configured via device tree.

## Defining LEDs

You may define LEDs anywhere in the device tree, with any name and underlying driver, as long as it conforms to the Zephyr LED API. This means LEDs connected directly to the MCU, as well as those via IO expanders or LED drivers can be controlled by this library.

Which LED is used as the status and debug/heartbeat LED is defined through the `aliases` section of the device tree:

```dts
aliases {
    led-status = &status_led;
    led-debug = &dbg_led;
};
```


### PWM (Timer)

If a LED is attached to a PWM-capable pin, you first need to enable and configure the timer that's used to generate the PWM waveform:

```dts
&timers1 {
    status = "okay";
    st,prescaler = <1000>;

    pwm_status: pwm {
        status = "okay";

        pinctrl-0 = <&tim1_ch2_pe11 &tim1_ch3_pe13 &tim1_ch4_pe14>;
        pinctrl-names = "default";
    };
};
```

Then define the appropriate LEDs with the `pwm-leds` compatible:

```dts
status_led: status {
    compatible = "pwm-leds";

    status_r {
        pwms = <&pwm_status 2 PWM_MSEC(5) PWM_POLARITY_NORMAL>;
        label = "Status LED (Red)";
    };

    status_g {
        pwms = <&pwm_status 4 PWM_MSEC(5) PWM_POLARITY_NORMAL>;
        label = "Status LED (Green)";
    };

    status_b {
        pwms = <&pwm_status 3 PWM_MSEC(5) PWM_POLARITY_NORMAL>;
        label = "Status LED (Blue)";
    };
};
```

A PWM period of 5msec (200Hz) or shorter is suggested; larger periods can lead to visible flickering, especially in video recordings.

Requires `CONFIG_LED_PWM` to be enabled.

**NOTE**: For the status LED, you must define three channels, and they must be ordered as R, G, B as shown above; they may be named anything, only their order inside the `pwm-leds` definition matters.

### On/off (GPIO)
For LEDs attached to regular GPIOs, define them with the `gpio-leds` compatible:

```dts
dbg_led: leds {
    compatible = "gpio-leds";

    debug_led {
        gpios = <&gpioa 11 GPIO_ACTIVE_HIGH>;
    };
};
```

Requires `CONFIG_LED_GPIO` to be enabled.

#include <logging/log.h>
LOG_MODULE_REGISTER(status_led, CONFIG_LIB_STATUS_LED_LOG_LEVEL);

#include <device.h>
#include <devicetree.h>
#include <drivers/gpio.h>
#include <drivers/led.h>
#include <math.h>

#include <utils/handle_errors.h>

#include "lib/status_led/status_led.h"

typedef struct leds_state {
  // lock to protect access to this dude
  struct k_mutex lock;

  // current pattern to process
  struct {
    // flag set to enable pattern processing
    bool enabled;

    // first element of pattern
    const leds_pattern_t *start;
    // number of elements in pattern
    size_t length;

    // offset into pattern
    size_t offset;
    // should pattern repeat?
    bool repeat;
  } pattern;
} leds_state_t;

static void pattern_timer_expired(struct k_timer *);
static void pattern_work(struct k_work *);

static const struct device *const gDebugLed = DEVICE_DT_GET_OR_NULL(DT_ALIAS(led_debug));
static const struct device *const gStatusLed = DEVICE_DT_GET_OR_NULL(DT_ALIAS(led_status));

static leds_state_t gState;

K_TIMER_DEFINE(gPatternTimer, pattern_timer_expired, NULL);
K_WORK_DEFINE(gPatternWork, pattern_work);

/**
 * @brief Initialize the status LEDs
 *
 * Configure the IO pins for the LEDs and ensure they are off.
 */
int leds_init() {
  memset(&gState, 0, sizeof(gState));
  k_mutex_init(&gState.lock);

  // ensure LEDs are set up
  if (gDebugLed) {
    HANDLE_UNLIKELY(!device_is_ready(gDebugLed));
  }
  if (gStatusLed) {
    HANDLE_UNLIKELY(!device_is_ready(gStatusLed));
  }

  // then set the initial state for LEDs
  HANDLE_UNLIKELY(leds_set_debug(false));
  HANDLE_UNLIKELY(leds_set_status(LED_COLOR_OFF));

  return 0;
}

/**
 * @brief Update debug/heartbeat LED state
 */
int leds_set_debug(const bool on) {
  if (!gDebugLed) {
    return 0;
  }
  return led_set_brightness(gDebugLed, 0, on ? 100 : 0);
}

/**
 * @brief Update the color the status LED
 *
 * Depending on the board configuration, this may support arbitrary shades (via PWM) or only the
 * eight options defined in the header.
 *
 * @param color An RGB color to set the LED to; encoded as 0x??RRGGBB, where each color component
 *              has 8 bits of resolution.
 */
int leds_set_status(const uint32_t color) {
  if (!gStatusLed) {
    return 0;
  }

  int err;

  const float r = (float)((color & 0xff0000) >> 16) / 255.f, g = (float)((color & 0x00ff00) >> 8) / 255.f,
              b = (float)(color & 0x0000ff) / 255.f;

  if ((err = led_set_brightness(gStatusLed, 0, r * 100.f))) {
    return err;
  }
  if ((err = led_set_brightness(gStatusLed, 1, g * 100.f))) {
    return err;
  }
  if ((err = led_set_brightness(gStatusLed, 2, b * 100.f))) {
    return err;
  }

  return 0;
}

/**
 * @brief Begin playback of a pattern on the status LED
 *
 * Uses a background timer and work item to change the color on the status LED according to the
 * provided pattern.
 *
 * @param start First element in pattern
 * @param length Number of elements in pattern
 * @param repeat Whether pattern repeats infinitely (set) or stops after running through once (clear)
 *
 * @seeAlso leds_status_pattern_stop
 */
int leds_status_pattern_start(const leds_pattern_t *start, const size_t length, const bool repeat) {
  if (!start) {
    return -EFAULT;
  } else if (!length) {
    return -EINVAL;
  }

  // abort timer if it's already processing
  k_timer_stop(&gPatternTimer);

  // update state to start for new pattern step
  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    gState.pattern.start = start;
    gState.pattern.length = length;
    gState.pattern.repeat = repeat;

    gState.pattern.offset = 0;

    gState.pattern.enabled = true;
  }
  k_mutex_unlock(&gState.lock);

  // then trigger the work item to immediately begin processing the first element
  k_work_submit(&gPatternWork);
  return 0;
}

/**
 * @brief Stop any active status LED pattern
 *
 * This stops the timer used to drive the pattern animations. It does not change the color of the
 * LED; it will remain at the last color.
 *
 * It is always legal to call this even if no pattern is currently active.
 */
int leds_status_pattern_stop() {
  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  { gState.pattern.enabled = false; }
  k_mutex_unlock(&gState.lock);

  k_timer_stop(&gPatternTimer);
  return 0;
}

/**
 * @brief Pattern processing timer expired
 *
 * Trigger the pattern work item, as we cannot take lock in timer ISR.
 */
static void pattern_timer_expired(struct k_timer *) { k_work_submit(&gPatternWork); }

/**
 * @brief Process status LED pattern
 *
 * Process the pattern element at the current offset, then prepare for the next one.
 */
static void pattern_work(struct k_work *) {
  bool done = false;

  HANDLE_CRITICAL(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    const leds_pattern_t *current = &gState.pattern.start[gState.pattern.offset++];

    // set LED
    if (current->setColor) {
      leds_set_status(current->color);
    }

    // handle wrap-around
    if (gState.pattern.offset == gState.pattern.length) {
      if (gState.pattern.repeat) {
        gState.pattern.offset = 0;
      } else {
        done = true;
      }
    }

    // set timer to advance to next element if not at end of pattern
    if (!done) {
      k_timer_start(&gPatternTimer, K_MSEC(current->delay), K_FOREVER);
    }
  }
  k_mutex_unlock(&gState.lock);
}

#if IS_ENABLED(CONFIG_LIB_STATUS_LED_COLOR_CONVERSION)
/**
 * @brief Convert a color in HSI (hue/stauration/intensity) space to 8 bit RGB
 *
 * HSI is a perceptual color space (similar to HSV) optimized for optimal color reproduction using
 * RGB LEDs.
 *
 * @param h Hue of color (in degrees, wraps around 360°)
 * @param s Saturation of color ([0, 1], clamped to range)
 * @param i Intensity of color ([0, 1], clamped to range; translates to overall LED brightness)
 *
 * @param An 8-bit RGB color encoded as 0x00RRGGBB
 */
uint32_t leds_hsi_to_rgb(float h, float s, float i) {
  int r, g, b;

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

  // range limit
  h = fmodf(h, 360);
  h = M_PI * h / 180.f;
  s = s > 0 ? (s < 1 ? s : 1) : 0;
  i = i > 0 ? (i < 1 ? i : 1) : 0;

  if (h < 2.09439) {
    r = 255 * i / 3 * (1 + s * cosf(h) / cosf(1.047196667 - h));
    g = 255 * i / 3 * (1 + s * (1 - cosf(h) / cosf(1.047196667 - h)));
    b = 255 * i / 3 * (1 - s);
  } else if (h < 4.188787) {
    h = h - 2.09439;
    g = 255 * i / 3 * (1 + s * cosf(h) / cosf(1.047196667 - h));
    b = 255 * i / 3 * (1 + s * (1 - cosf(h) / cosf(1.047196667 - h)));
    r = 255 * i / 3 * (1 - s);
  } else {
    h = h - 4.188787;
    b = 255 * i / 3 * (1 + s * cosf(h) / cosf(1.047196667 - h));
    r = 255 * i / 3 * (1 + s * (1 - cosf(h) / cosf(1.047196667 - h)));
    g = 255 * i / 3 * (1 - s);
  }

  return ((r & 0xff) << 16) | ((g & 0xff) << 8) | (b & 0xff);
}
#endif

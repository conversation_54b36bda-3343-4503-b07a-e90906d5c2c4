#include <stdint.h>
#include <stdlib.h>

#include "cobs.h"

/**
 * @brief Encode a message with COBS
 *
 * @param destBuf Buffer to receive the encoded message
 * @param destBufSize Size of the destination buffer (bytes)
 * @param srcBuf Buffer containing the raw input message
 * @param srcBufLen Length of the input message buffer
 * @param outLen Number of bytes written to destination buffer
 */
bool cobs_encode(void *destBuf, size_t destBufSize, const void *srcBuf, size_t srcBufLen, size_t *outLen) {
  bool ok = true;

  const uint8_t *readPtr = srcBuf;
  const uint8_t *readPtrEnd = readPtr + srcBufLen;
  uint8_t *writePtrStart = destBuf;
  uint8_t *writePtrEnd = writePtrStart + destBufSize;
  uint8_t *writePtrCode = destBuf;
  uint8_t *writePtr = writePtrCode + 1;
  uint8_t searchLen = 1;

  if (!destBuf || !srcBuf || !outLen) {
    return false;
  }

  if (srcBufLen != 0) {
    while (true) {
      // bounds check (buffer overflow)
      if (writePtr >= writePtrEnd) {
        ok = false;
        break;
      }

      const uint8_t byte = *readPtr++;
      if (byte == 0) {
        // found NULL byte (terminator)
        *writePtrCode = searchLen;
        writePtrCode = writePtr++;
        searchLen = 1;
        if (readPtr >= readPtrEnd) {
          break;
        }
      } else {
        // write a nonzero byte
        *writePtr++ = byte;
        searchLen++;
        if (readPtr >= readPtrEnd) {
          break;
        }
        if (searchLen == 0xFF) {
          // long string of non-zero bytes;
          *writePtrCode = searchLen;
          writePtrCode = writePtr++;
          searchLen = 1;
        }
      }
    }
  }

  // reached end of source; update the output (code byte) and calculate total written
  if (writePtrCode >= writePtrEnd) {
    // buffer overflow writing last byte
    ok = false;
    writePtr = writePtrEnd;
  } else {
    // write last byte (length)
    *writePtrCode = searchLen;
  }

  *outLen = (size_t)(writePtr - writePtrStart);
  return ok;
}

/**
 * @brief Decode a COBS-encoded message
 *
 * @param destBuf Buffer to receive decoded message
 * @param destBufSize Total capacity of the destination buffer (bytes)
 * @param srcBuf Buffer containing the COBS-encoded message
 * @param srcBufLen Length of the encoded input message
 * @param outLen Number of bytes written to destination buffer
 *
 * @remark The size of the input buffer should exclude the final NULL byte
 */
bool cobs_decode(void *destBuf, size_t destBufSize, const void *srcBuf, size_t srcBufLen, size_t *outLen) {
  bool ok = true;
  const uint8_t *readPtr = srcBuf;
  const uint8_t *readPtrEnd = readPtr + srcBufLen;
  uint8_t *writePtrStart = destBuf;
  uint8_t *writePtrEnd = writePtrStart + destBufSize;
  uint8_t *writePtr = destBuf;

  if (!destBuf || !srcBuf || !outLen) {
    return false;
  }

  if (srcBufLen != 0) {
    while (true) {
      uint8_t lengthCode = *readPtr++;
      if (lengthCode == 0) {
        ok = false;
        break;
      }
      lengthCode--;

      // check for input buffer underflow…
      size_t numBytesRemaining = (size_t)(readPtrEnd - readPtr);
      if (lengthCode > numBytesRemaining) {
        ok = false;
        lengthCode = (uint8_t)numBytesRemaining;
      }
      // …and for output buffer overflow
      numBytesRemaining = (size_t)(writePtrEnd - writePtr);
      if (lengthCode > numBytesRemaining) {
        // output buffer overflow
        ok = false;
        lengthCode = (uint8_t)numBytesRemaining;
      }

      for (uint8_t i = lengthCode; i != 0; i--) {
        const uint8_t byte = *readPtr++;
        if (byte == 0) {
          // XXX: is this actually an error?
          // NULL byte in input
          ok = false;
        }
        *writePtr++ = byte;
      }

      if (readPtr >= readPtrEnd) {
        break;
      }

      // add trailing NULL byte
      if (lengthCode != 0xFE) {
        if (writePtr >= writePtrEnd) {
          // buffer overflow
          ok = false;
          break;
        }
        *writePtr++ = '\0';
      }
    }
  }

  // total length produced
  *outLen = (size_t)(writePtr - writePtrStart);
  return ok;
}

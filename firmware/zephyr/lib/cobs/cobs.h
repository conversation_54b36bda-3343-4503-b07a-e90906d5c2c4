/**
 * @file
 * @brief Consistent Overhead Byte Stuffing (COBS)
 *
 * COBS allows for messages to be framed with a fixed terminator byte and have a consistent
 * amount of overhead replacing that byte in the payload, as compared to other framing schemes
 * such as HDLC where worst case overhead is 2x.
 *
 * This implementation fixes the termination character at 0x00.
 */
#pragma once

#include <stdbool.h>
#include <stddef.h>

bool cobs_encode(void *destBuf, size_t destBufSize, const void *srcBuf, size_t srcBufSize, size_t *outLen);
bool cobs_decode(void *destBuf, size_t destBufSize, const void *srcBuf, size_t srcBufLen, size_t *outLen);

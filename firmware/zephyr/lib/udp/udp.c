#include <logging/log.h>
LOG_MODULE_REGISTER(lib_upd, CONFIG_LIB_UDP_LOG_LEVEL);

#include <lib/udp/udp.h>
#include <net/socket.h>
#include <stdlib.h>
#include <string.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

int udp_init(udp_server *server, int port) {
  struct sockaddr_in bind_addr;
  memset(&bind_addr, 0, sizeof(bind_addr));
  bind_addr.sin_family = AF_INET;
  bind_addr.sin_port = htons(port);
  bind_addr.sin_addr.s_addr = htonl(INADDR_ANY);

  server->sock = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
  HANDLE_CRITICAL(bind(server->sock, (struct sockaddr *)&bind_addr, sizeof(bind_addr)));
  for (uint16_t i = 0; i < server->rx_avail_q->max_msgs; ++i) {
    uint8_t *addr = &(server->rx_data[i * server->rx_size]);
    k_msgq_put(server->rx_avail_q, &addr, K_FOREVER);
  }
  return 0;
}

void udp_rx(void *arg) {
  udp_server *server = (udp_server *)arg;

  uint8_t *data;
  uint8_t *buf;
  udp_buf_header *header;
  for (;;) {
    int resp = k_msgq_get(server->rx_avail_q, &data, K_FOREVER);
    if (resp != 0) {
      LOG_WRN("Failed to get msgq buffer err: %d", resp);
      continue;
    }
    header = (udp_buf_header *)data;
    buf = data + sizeof(udp_buf_header);
    header->metadata.client_addr_len = sizeof(struct sockaddr);

    int received = recvfrom(server->sock, buf, server->rx_size - sizeof(udp_buf_header), 0,
                            &(header->metadata.client_addr), &(header->metadata.client_addr_len));
    if (received <= 0) {
      LOG_WRN("Failed to receive UDP packet: %d", errno);
      k_msgq_put(server->rx_avail_q, &data, K_FOREVER); // No data read so re-use this buffer
      continue;
    }
    header->len = (uint16_t)received;
    k_msgq_put(server->rx_used_q, &data, K_FOREVER);
  }
}

uint16_t udp_get_data(udp_server *server, uint8_t *buf, uint16_t buf_size, udp_msg_metadata *metadata,
                      uint32_t timeout_ms) {
  uint8_t *data;
  uint16_t size = udp_get_data_no_copy(server, &data, metadata, timeout_ms);
  size = buf_size < size ? buf_size : size;
  memcpy(buf, data, size);
  udp_get_data_release(server, data);
  return size;
}
uint16_t udp_get_data_no_copy(udp_server *server, uint8_t **buf_ptr, udp_msg_metadata *metadata, uint32_t timeout_ms) {
  uint8_t *data;
  udp_buf_header *header;
  k_msgq_get(server->rx_used_q, &data, (timeout_ms > 0 ? K_MSEC(timeout_ms) : K_FOREVER));
  header = (udp_buf_header *)data;
  *buf_ptr = data + sizeof(udp_buf_header);
  memcpy(metadata, &header->metadata, sizeof(udp_msg_metadata));
  return header->len;
}
int udp_get_data_release(udp_server *server, uint8_t *buf) {
  buf -= sizeof(udp_buf_header);
  k_msgq_put(server->rx_avail_q, &buf, K_FOREVER);
  return 0;
}
int udp_tx(udp_server *server, uint8_t *buf, uint16_t buf_len, udp_msg_metadata *metadata) {
  int ret = sendto(server->sock, buf, buf_len, 0, &metadata->client_addr, metadata->client_addr_len);
  if (ret < 0) {
    LOG_WRN("Failed to send UDP packet: %d", errno);
  }
  return ret;
}

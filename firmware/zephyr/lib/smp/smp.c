/*
 * Copyright (c) 2020 Prevas A/S
 *
 * SPDX-License-Identifier: Apache-2.0
 */
#include <logging/log_ctrl.h>
#include <mgmt/mcumgr/smp_udp.h>
#include <net/net_conn_mgr.h>
#include <net/net_event.h>
#include <net/net_mgmt.h>
#include <shell/shell.h>
#include <sys/reboot.h>

#include "lib/smp/smp.h"

#ifdef CONFIG_MCUMGR_CMD_FS_MGMT
#include "fs_mgmt/fs_mgmt.h"
#include <device.h>
#include <fs/fs.h>
#include <fs/littlefs.h>
#endif
#ifdef CONFIG_MCUMGR_CMD_OS_MGMT
#include "os_mgmt/os_mgmt.h"
#endif
#ifdef CONFIG_MCUMGR_CMD_IMG_MGMT
#include "img_mgmt/img_mgmt.h"
#endif
#ifdef CONFIG_MCUMGR_CMD_STAT_MGMT
#include "stat_mgmt/stat_mgmt.h"
#include <stats/stats.h>
#endif
#ifdef CONFIG_MCUMGR_CMD_SHELL_MGMT
#include "shell_mgmt/shell_mgmt.h"
#endif
#ifdef CONFIG_MCUMGR_CMD_FS_MGMT
#include "fs_mgmt/fs_mgmt.h"
#endif

#define LOG_LEVEL LOG_LEVEL_DBG
#include <logging/log.h>
LOG_MODULE_REGISTER(lib_smp);

#define EVENT_MASK (NET_EVENT_L4_CONNECTED | NET_EVENT_L4_DISCONNECTED)

static struct net_mgmt_event_callback mgmt_cb;

static void event_handler(struct net_mgmt_event_callback *cb, uint32_t mgmt_event, struct net_if *iface) {
  if ((mgmt_event & EVENT_MASK) != mgmt_event) {
    return;
  }

  if (mgmt_event == NET_EVENT_L4_CONNECTED) {
    LOG_INF("Network connected");

    if (smp_udp_open() < 0) {
      LOG_ERR("could not open smp udp");
    }

    return;
  }

  if (mgmt_event == NET_EVENT_L4_DISCONNECTED) {
    LOG_INF("Network disconnected");
    smp_udp_close();
    return;
  }
}

void start_smp_udp(void) {
  net_mgmt_init_event_callback(&mgmt_cb, event_handler, EVENT_MASK);
  net_mgmt_add_event_callback(&mgmt_cb);
  net_conn_mgr_resend_status();
}

#ifdef CONFIG_MCUMGR_SMP_NOTIFY_CALLBACK
K_MUTEX_DEFINE(smp_callback_mutex);
int64_t last_smp_callback_timestamp_ms;

void mcumgr_smp_notify_init() {
  k_mutex_lock(&smp_callback_mutex, K_FOREVER);
  last_smp_callback_timestamp_ms = k_uptime_get();
  k_mutex_unlock(&smp_callback_mutex);
}

extern void mcumgr_smp_notify_callback() {
  k_mutex_lock(&smp_callback_mutex, K_FOREVER);
  last_smp_callback_timestamp_ms = k_uptime_get();
  k_mutex_unlock(&smp_callback_mutex);
}

bool mcumgr_smp_timeout_elapsed() {
  int64_t delta;
  k_mutex_lock(&smp_callback_mutex, K_FOREVER);
  delta = k_uptime_get() - last_smp_callback_timestamp_ms;
  k_mutex_unlock(&smp_callback_mutex);
  return delta > CONFIG_MCUMGR_SMP_TIMEOUT_MS;
}
#endif

void start_smp_lib() {
#ifdef CONFIG_MCUMGR_SMP_NOTIFY_CALLBACK
  mcumgr_smp_notify_init();
#endif

#ifdef CONFIG_MCUMGR_CMD_OS_MGMT
  os_mgmt_register_group();
#endif
#ifdef CONFIG_MCUMGR_CMD_IMG_MGMT
  img_mgmt_register_group();
#endif
#ifdef CONFIG_MCUMGR_CMD_STAT_MGMT
  stat_mgmt_register_group();
#endif
#ifdef CONFIG_MCUMGR_CMD_SHELL_MGMT
  shell_mgmt_register_group();
#endif
#ifdef CONFIG_MCUMGR_CMD_FS_MGMT
  fs_mgmt_register_group();
#endif
#ifdef CONFIG_MCUMGR_SMP_BT
  start_smp_bluetooth();
#endif
#ifdef CONFIG_MCUMGR_SMP_UDP
  start_smp_udp();
#endif
}

void wait_for_smp_idle() {
#ifdef CONFIG_MCUMGR_SMP_NOTIFY_CALLBACK
  while (!mcumgr_smp_timeout_elapsed()) {
    k_sleep(K_MSEC(1000));
  }
#else
  k_msleep(CONFIG_MCUMGR_SMP_TIMEOUT_MS);
#endif
}

void k_sys_fatal_error_handler(unsigned int reason, const z_arch_esf_t *esf) {
  LOG_ERR("Critical SMP Error Handler Called");
  wait_for_smp_idle();
  LOG_PANIC();
  sys_reboot(SYS_REBOOT_COLD);
}

static int cmd_revision(const struct shell *shell, size_t argc, char **argv) {
  ARG_UNUSED(argc);
  ARG_UNUSED(argv);

  shell_print(shell, "revision=%s", CONFIG_BOARD_REVISION);
  return 0;
}

SHELL_CMD_REGISTER(revision, NULL, "Show board revision", cmd_revision);

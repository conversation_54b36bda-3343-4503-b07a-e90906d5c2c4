menuconfig LIB_SMP
    bool "MCUMGR SMP Lib"
    select SH<PERSON>L
    select <PERSON><PERSON><PERSON>_<PERSON>NIMAL
    select SHELL_BACKEND_DUMM<PERSON>
    select MCUMGR_CMD_SHELL_MGMT
    help
      Enable config options for smp lib.

menuconfig MCUMGR_SMP_TIMEOUT_MS
    int "MCUMGR Notify Timeout"
	default 5000
	help
	  Timeout after no MCUMGR SMP communication for the bootloader to proceed to the main image.

menuconfig BOARD_REVISION
  string "Board revision"

if LIB_SMP

config MCUMGR_BUF_USER_DATA_SIZE
	default 24
  help
    mcumgr UDP transport stores source IP/port here. Must be >= 20 bytes on STM32H753 w/ IPv4 UDP, or >= 24 bytes w/ IPv6 UDP.

module = LIB_SMP
module-str = lib_smp
source "subsys/logging/Kconfig.template.log_config"

endif

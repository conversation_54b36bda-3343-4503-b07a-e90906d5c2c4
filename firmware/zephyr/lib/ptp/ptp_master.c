#include <logging/log.h>
LOG_MODULE_REGISTER(lib_ptp_master, CONFIG_LIB_PTP_LOG_LEVEL);

#include "ptp_intercept.h"
#include <drivers/ptp_clock.h>
#include <lib/ptp/ptp.h>
#include <net/ethernet.h>
#include <net/net_if.h>
#include <net/net_pkt.h>
#include <stdlib.h>
#include <string.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

static const struct net_eth_addr ptp_mcast_eth_addr = {{0x01, 0x1b, 0x19, 0x00, 0x00, 0x00}};

struct ptp_master_state {
  struct net_if *iface;
  const struct device *clk;
  struct ptp_port_identity port_identity;
#ifndef CONFIG_CARBON_PTP_USE_INTERCEPT
  struct net_context *context;
#endif
  struct net_pkt *sync_pkt;
  struct net_if_timestamp_cb sync_sent_cb;
  uint16_t announce_seq_id;
  uint16_t sync_seq_id;
  uint8_t domain_number;
  uint8_t log_announce_interval;
  uint8_t log_sync_interval;
  uint8_t log_min_delay_req_interval;
};

static struct ptp_master_state state;

static void ptp_announce_sender();
static void ptp_sync_sender();

K_THREAD_DEFINE(ptp_announce_sender_thread_id, 1024, ptp_announce_sender, NULL, NULL, NULL, K_PRIO_PREEMPT(8), 0,
                K_TICKS_FOREVER);
K_THREAD_DEFINE(ptp_sync_sender_thread_id, 1024, ptp_sync_sender, NULL, NULL, NULL, K_PRIO_PREEMPT(8), 0,
                K_TICKS_FOREVER);

static void handle_ptp_sync_sent(struct net_pkt *pkt);
static void send_ptp_follow_up(struct net_ptp_time *ts);

static void send_ptp_announce() {
  /* prepare body */
  struct ptp_announce_body ptp_body;
  memset(&ptp_body, 0, sizeof(ptp_body));
  ptp_body.grandmaster_clock_quality.clock_class = 248;
  ptp_body.grandmaster_clock_quality.clock_accuracy = 0xfe;
  ptp_body.grandmaster_clock_quality.offset_scaled_log_var = 0xffff;
  memcpy(&ptp_body.grandmaster_identity, &state.port_identity.clock_identity, sizeof(ptp_body.grandmaster_identity));
  ptp_body.time_source = 0xa0;

  /* prepare header */
  struct ptp_header ptp_hdr;
  memset(&ptp_hdr, 0, sizeof(ptp_hdr));
  ptp_hdr.message_type = PTP_MESSAGE_TYPE_ANNOUNCE;
  ptp_hdr.version_ptp = 2;
  ptp_hdr.minor_version_ptp = 0;
  ptp_hdr.message_length = sizeof(ptp_hdr) + sizeof(ptp_body);
  ptp_hdr.domain_number = state.domain_number;
  ptp_hdr.flag_field = PTP_FLAG_PTP_TIMESCALE;
  memcpy(&ptp_hdr.source_port_identity, &state.port_identity, sizeof(ptp_hdr.source_port_identity));
  ptp_hdr.sequence_id = ++state.announce_seq_id;
  ptp_hdr.log_message_interval = state.log_announce_interval;

  /* allocate packet with PTP settings */
  struct net_pkt *pkt = net_pkt_alloc_with_buffer(state.iface, ptp_hdr.message_length, AF_UNSPEC, 0, K_NO_WAIT);
  net_pkt_set_ptp(pkt, true);
  net_pkt_set_priority(pkt, NET_PRIORITY_CA);

  /* set src mac addr */
  struct net_linkaddr *iface_addr = net_if_get_link_addr(state.iface);
  net_pkt_lladdr_src(pkt)->addr = iface_addr->addr;
  net_pkt_lladdr_src(pkt)->len = iface_addr->len;

  /* set dst mac addr */
  net_pkt_lladdr_dst(pkt)->addr = (uint8_t *)&ptp_mcast_eth_addr;
  net_pkt_lladdr_dst(pkt)->len = sizeof(ptp_mcast_eth_addr);

  /* write packet PTP data */
  net_pkt_write(pkt, &ptp_hdr, sizeof(ptp_hdr));
  net_pkt_write(pkt, &ptp_body, sizeof(ptp_body));

  /* enqueue for sending */
  net_if_queue_tx(state.iface, pkt);
}

static void send_ptp_sync() {
  /* prepare body */
  struct ptp_sync_body ptp_body;
  memset(&ptp_body, 0, sizeof(ptp_body));

  /* prepare header */
  struct ptp_header ptp_hdr;
  memset(&ptp_hdr, 0, sizeof(ptp_hdr));
  ptp_hdr.message_type = PTP_MESSAGE_TYPE_SYNC;
  ptp_hdr.version_ptp = 2;
  ptp_hdr.minor_version_ptp = 0;
  ptp_hdr.message_length = sizeof(ptp_hdr) + sizeof(ptp_body);
  ptp_hdr.domain_number = state.domain_number;
  ptp_hdr.flag_field = PTP_FLAG_TWO_STEP;
  memcpy(&ptp_hdr.source_port_identity, &state.port_identity, sizeof(ptp_hdr.source_port_identity));
  ptp_hdr.sequence_id = ++state.sync_seq_id;
  ptp_hdr.log_message_interval = state.log_sync_interval;

  /* allocate packet with PTP settings */
  struct net_pkt *pkt = net_pkt_alloc_with_buffer(state.iface, ptp_hdr.message_length, AF_UNSPEC, 0, K_NO_WAIT);
  net_pkt_set_ptp(pkt, true);
  net_pkt_set_priority(pkt, NET_PRIORITY_CA);

  /* set src mac addr */
  struct net_linkaddr *iface_addr = net_if_get_link_addr(state.iface);
  net_pkt_lladdr_src(pkt)->addr = iface_addr->addr;
  net_pkt_lladdr_src(pkt)->len = iface_addr->len;

  /* set dst mac addr */
  net_pkt_lladdr_dst(pkt)->addr = (uint8_t *)&ptp_mcast_eth_addr;
  net_pkt_lladdr_dst(pkt)->len = sizeof(ptp_mcast_eth_addr);

  /* write packet PTP data */
  net_pkt_write(pkt, &ptp_hdr, sizeof(ptp_hdr));
  net_pkt_write(pkt, &ptp_body, sizeof(ptp_body));

  if (state.sync_pkt != NULL) {
    /* were not able to send last sync packet, unregister callback and unref packet */
    net_if_unregister_timestamp_cb(&state.sync_sent_cb);
    net_pkt_unref(state.sync_pkt);
    state.sync_pkt = NULL;
  }

  state.sync_pkt = pkt;
  net_pkt_ref(pkt);
  net_if_register_timestamp_cb(&state.sync_sent_cb, pkt, state.iface, handle_ptp_sync_sent);

  /* enqueue for sending */
  net_if_queue_tx(state.iface, pkt);
}

static void handle_ptp_sync_sent(struct net_pkt *pkt) {
  struct net_ptp_time *ts = net_pkt_timestamp(pkt);
  HANDLE_CRITICAL_BOOL(ts != NULL);
  send_ptp_follow_up(ts);

  net_if_unregister_timestamp_cb(&state.sync_sent_cb);
  net_pkt_unref(pkt);
  state.sync_pkt = NULL;
}

static void send_ptp_follow_up(struct net_ptp_time *ts) {
  /* prepare body */
  struct ptp_follow_up_body ptp_body;
  memset(&ptp_body, 0, sizeof(ptp_body));
  ptp_ts_net_to_wire(&ptp_body.precise_origin_timestamp, ts);

  /* prepare header */
  struct ptp_header ptp_hdr;
  memset(&ptp_hdr, 0, sizeof(ptp_hdr));
  ptp_hdr.message_type = PTP_MESSAGE_TYPE_FOLLOW_UP;
  ptp_hdr.version_ptp = 2;
  ptp_hdr.minor_version_ptp = 0;
  ptp_hdr.message_length = sizeof(ptp_hdr) + sizeof(ptp_body);
  ptp_hdr.domain_number = state.domain_number;
  memcpy(&ptp_hdr.source_port_identity, &state.port_identity, sizeof(ptp_hdr.source_port_identity));
  ptp_hdr.sequence_id = state.sync_seq_id;
  ptp_hdr.log_message_interval = state.log_sync_interval;

  /* allocate packet with PTP settings */
  struct net_pkt *pkt = net_pkt_alloc_with_buffer(state.iface, ptp_hdr.message_length, AF_UNSPEC, 0, K_NO_WAIT);
  net_pkt_set_ptp(pkt, true);
  net_pkt_set_priority(pkt, NET_PRIORITY_CA);

  /* set src mac addr */
  struct net_linkaddr *iface_addr = net_if_get_link_addr(state.iface);
  net_pkt_lladdr_src(pkt)->addr = iface_addr->addr;
  net_pkt_lladdr_src(pkt)->len = iface_addr->len;

  /* set dst mac addr */
  net_pkt_lladdr_dst(pkt)->addr = (uint8_t *)&ptp_mcast_eth_addr;
  net_pkt_lladdr_dst(pkt)->len = sizeof(ptp_mcast_eth_addr);

  /* write packet PTP data */
  net_pkt_write(pkt, &ptp_hdr, sizeof(ptp_hdr));
  net_pkt_write(pkt, &ptp_body, sizeof(ptp_body));

  /* enqueue for sending */
  net_if_queue_tx(state.iface, pkt);
}

static void handle_ptp_delay_req(struct net_pkt *req_pkt, struct ptp_header *req_ptp_hdr) {
  struct net_ptp_time *ts = net_pkt_timestamp(req_pkt);
  HANDLE_CRITICAL_BOOL(ts != NULL);

  /* prepare body */
  struct ptp_delay_resp_body ptp_body;
  memset(&ptp_body, 0, sizeof(ptp_body));
  ptp_ts_net_to_wire(&ptp_body.receive_timestamp, ts);
  memcpy(&ptp_body.requesting_port_identity, &req_ptp_hdr->source_port_identity,
         sizeof(ptp_body.requesting_port_identity));

  /* prepare header */
  struct ptp_header ptp_hdr;
  memset(&ptp_hdr, 0, sizeof(ptp_hdr));
  ptp_hdr.message_type = PTP_MESSAGE_TYPE_DELAY_RESP;
  ptp_hdr.version_ptp = 2;
  ptp_hdr.minor_version_ptp = 0;
  ptp_hdr.message_length = sizeof(ptp_hdr) + sizeof(ptp_body);
  ptp_hdr.domain_number = state.domain_number;
  memcpy(&ptp_hdr.source_port_identity, &state.port_identity, sizeof(ptp_hdr.source_port_identity));
  ptp_hdr.sequence_id = req_ptp_hdr->sequence_id;
  ptp_hdr.log_message_interval = state.log_min_delay_req_interval;

  /* allocate packet with PTP settings */
  struct net_pkt *pkt = net_pkt_alloc_with_buffer(state.iface, ptp_hdr.message_length, AF_UNSPEC, 0, K_NO_WAIT);
  net_pkt_set_ptp(pkt, true);
  net_pkt_set_priority(pkt, NET_PRIORITY_CA);

  /* set src mac addr */
  struct net_linkaddr *iface_addr = net_if_get_link_addr(state.iface);
  net_pkt_lladdr_src(pkt)->addr = iface_addr->addr;
  net_pkt_lladdr_src(pkt)->len = iface_addr->len;

  /* set dst mac addr */
  net_pkt_lladdr_dst(pkt)->addr = (uint8_t *)&ptp_mcast_eth_addr;
  net_pkt_lladdr_dst(pkt)->len = sizeof(ptp_mcast_eth_addr);

  /* write packet PTP data */
  net_pkt_write(pkt, &ptp_hdr, sizeof(ptp_hdr));
  net_pkt_write(pkt, &ptp_body, sizeof(ptp_body));

  /* enqueue for sending */
  net_if_queue_tx(state.iface, pkt);
}

static void process_ptp_packet(struct net_pkt *pkt) {
  if (ntohs(NET_ETH_HDR(pkt)->type) != NET_ETH_PTYPE_PTP) {
    goto done;
  }

  /* skip ethernet header */
  net_pkt_cursor_init(pkt);
  net_pkt_skip(pkt, sizeof(struct net_eth_hdr));

  /* read PTP header */
  struct ptp_header ptp_hdr;
  net_pkt_read(pkt, &ptp_hdr, sizeof(ptp_hdr));

  if (ptp_hdr.version_ptp != 2 || ptp_hdr.minor_version_ptp != 0) {
    LOG_WRN("Unsupported PTP v%d.%d packet ignored", ptp_hdr.version_ptp, ptp_hdr.minor_version_ptp);
    goto done;
  }

  switch (ptp_hdr.message_type) {
  case PTP_MESSAGE_TYPE_DELAY_REQ:
    handle_ptp_delay_req(pkt, &ptp_hdr);
    break;
  }

done:
  return;
}

#ifndef CONFIG_CARBON_PTP_USE_INTERCEPT
static void pkt_received(struct net_context *context, struct net_pkt *pkt, union net_ip_header *ip_hdr,
                         union net_proto_header *proto_hdr, int status, void *user_data) {
  process_ptp_packet(pkt);
  net_pkt_unref(pkt);
}
#endif

static void ptp_announce_sender() {
  for (;;) {
    send_ptp_announce();
    k_msleep(1000 * (1 << state.log_announce_interval));
  }
}

static void ptp_sync_sender() {
  for (;;) {
    send_ptp_sync();
    k_msleep(1000 * (1 << state.log_sync_interval));
  }
}

int ptp_master_clk_get(struct net_ptp_time *net) { return ptp_clock_get(state.clk, net); }

int ptp_master_clk_set(struct net_ptp_time *net) { return ptp_clock_set(state.clk, net); }

int ptp_master_clk_rate_adjust(float rate) { return ptp_clock_rate_adjust(state.clk, rate); }

int ptp_master_init(struct net_if *iface) {
  memset(&state, 0, sizeof(state));

  state.iface = iface;

  state.clk = net_eth_get_ptp_clock(iface);
  HANDLE_UNLIKELY_BOOL(device_is_ready(state.clk), ENODEV);

  struct net_linkaddr *iface_addr = net_if_get_link_addr(state.iface);
  memcpy(state.port_identity.clock_identity, iface_addr->addr, iface_addr->len);

  /* default linuxptp settings */
  state.domain_number = 0;
  state.log_announce_interval = 1;
  state.log_sync_interval = 0;
  state.log_min_delay_req_interval = 0;

#ifdef CONFIG_CARBON_PTP_USE_INTERCEPT
  ptp_intercept_init(state.iface, &process_ptp_packet);
#endif

  return 0;
}

int ptp_master_start() {
#ifdef CONFIG_CARBON_PTP_USE_INTERCEPT
  ptp_intercept_set_active(true);
#else
  struct sockaddr_ll dst;
  dst.sll_ifindex = net_if_get_by_iface(state.iface);
  dst.sll_family = AF_PACKET;
  dst.sll_protocol = htons(NET_ETH_PTYPE_PTP);
  HANDLE_UNLIKELY(net_context_get(AF_PACKET, SOCK_RAW, ETH_P_ALL, &state.context));
  HANDLE_UNLIKELY(net_context_bind(state.context, (const struct sockaddr *)&dst, sizeof(dst)));
  HANDLE_UNLIKELY(net_context_recv(state.context, pkt_received, K_NO_WAIT, NULL));
#endif
  k_thread_name_set(ptp_announce_sender_thread_id, "announce_sender");
  k_thread_name_set(ptp_sync_sender_thread_id, "sync_sender");
  k_thread_start(ptp_announce_sender_thread_id);
  k_thread_start(ptp_sync_sender_thread_id);

  return 0;
}
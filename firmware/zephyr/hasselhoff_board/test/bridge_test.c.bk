#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <net/if.h>
#include <linux/can.h>
#include <linux/can/raw.h>
#include <time.h>

#define CAN_INTERFACE_0 "can0"
#define CAN_INTERFACE_1 "can1"
#define CAN_PACKET_ID 0x12345678
#define PACKET_DATA_SIZE 8
#define PACKET_SEND_INTERVAL_MS 100

void print_can_frame(struct can_frame frame) {
    printf("ID: %X  DLC: %d  Data: ", frame.can_id, frame.can_dlc);
    for (int i = 0; i < frame.can_dlc; i++) {
        printf("%02X ", frame.data[i]);
    }
    printf("\n");
}

int main() {
    int s0, s1;
    struct sockaddr_can addr0, addr1;
    struct ifreq ifr0, ifr1;
    struct can_frame frame_send, frame_recv;
    struct timespec start_time, end_time;
    double latency;

    // Create socket for can0
    if ((s0 = socket(PF_CAN, SOCK_RAW, CAN_RAW)) < 0) {
        perror("Socket creation error for can0");
        return EXIT_FAILURE;
    }

    memset(&ifr0, 0, sizeof(ifr0));
    strcpy(ifr0.ifr_name, CAN_INTERFACE_0);
    ioctl(s0, SIOCGIFINDEX, &ifr0);

    addr0.can_family = AF_CAN;
    addr0.can_ifindex = ifr0.ifr_ifindex;

    if (bind(s0, (struct sockaddr *)&addr0, sizeof(addr0)) < 0) {
        perror("Binding error for can0");
        close(s0);
        return EXIT_FAILURE;
    }

    // Create socket for can1
    if ((s1 = socket(PF_CAN, SOCK_RAW, CAN_RAW)) < 0) {
        perror("Socket creation error for can1");
        close(s0);
        return EXIT_FAILURE;
    }

    memset(&ifr1, 0, sizeof(ifr1));
    strcpy(ifr1.ifr_name, CAN_INTERFACE_1);
    ioctl(s1, SIOCGIFINDEX, &ifr1);

    addr1.can_family = AF_CAN;
    addr1.can_ifindex = ifr1.ifr_ifindex;

    if (bind(s1, (struct sockaddr *)&addr1, sizeof(addr1)) < 0) {
        perror("Binding error for can1");
        close(s0);
        close(s1);
        return EXIT_FAILURE;
    }

    printf("Bridge between %s and %s is active.\n", CAN_INTERFACE_0, CAN_INTERFACE_1);

    // Prepare CAN frame for sending
    frame_send.can_id = CAN_PACKET_ID;
    frame_send.can_dlc = PACKET_DATA_SIZE;
    memset(frame_send.data, 0, PACKET_DATA_SIZE);

    int packet_count = 0;

    while (1) {
        // Update data in the CAN frame (incrementing packet count)
        frame_send.data[0] = packet_count & 0xFF;
        frame_send.data[1] = (packet_count >> 8) & 0xFF;
        frame_send.data[2] = (packet_count >> 16) & 0xFF;
        frame_send.data[3] = (packet_count >> 24) & 0xFF;

        // Get start time
        clock_gettime(CLOCK_MONOTONIC, &start_time);

        // Send CAN frame
        if (write(s0, &frame_send, sizeof(frame_send)) < 0) {
            perror("Error writing CAN frame");
            return EXIT_FAILURE;
        }

        // Increment packet count
        packet_count++;

        // Receive CAN frame on CAN_INTERFACE_1
        ssize_t recv_bytes = read(s1, &frame_recv, sizeof(frame_recv));
        if (recv_bytes >= 0) {
            // Get end time
            clock_gettime(CLOCK_MONOTONIC, &end_time);

            // Calculate latency
            latency = (end_time.tv_sec - start_time.tv_sec) * 1000.0; // seconds to milliseconds
            latency += (end_time.tv_nsec - start_time.tv_nsec) / 1000000.0; // nanoseconds to milliseconds

            printf("Latency: %.3f ms\n", latency);
            print_can_frame(frame_recv);
        } else {
            perror("Error reading CAN frame");
            return EXIT_FAILURE;
        }

        // Sleep for the defined interval
        usleep(PACKET_SEND_INTERVAL_MS * 1000);
    }

    close(s0);
    close(s1);
    return EXIT_SUCCESS;
}

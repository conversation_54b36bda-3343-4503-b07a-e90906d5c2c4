#include <fcntl.h>
#include <linux/can.h>
#include <linux/can/raw.h>
#include <net/if.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <termios.h>
#include <unistd.h>

#define CAN_INTERFACE_0 "can0"
#define CAN_INTERFACE_1 "can1"
#define TARGET_CAN_ID 0x8CFFFE8C
#define FIRST_DATA_BYTE 0x8C

int positive_ticks = 0; // Global variable to hold positive ticks

void incrementPositiveTicks() {
  positive_ticks++;
  printf("Positive ticks incremented to: %d\n", positive_ticks);
}

int main() {
  int s0, s1;
  struct sockaddr_can addr0, addr1;
  struct ifreq ifr0, ifr1;
  struct can_frame frame;

  // Create socket for can0
  if ((s0 = socket(PF_CAN, SOCK_RAW, CAN_RAW)) < 0) {
    perror("Socket creation error for can0");
    return EXIT_FAILURE;
  }

  memset(&ifr0, 0, sizeof(ifr0));
  strcpy(ifr0.ifr_name, CAN_INTERFACE_0);
  ioctl(s0, SIOCGIFINDEX, &ifr0);

  addr0.can_family = AF_CAN;
  addr0.can_ifindex = ifr0.ifr_ifindex;

  if (bind(s0, (struct sockaddr *)&addr0, sizeof(addr0)) < 0) {
    perror("Binding error for can0");
    close(s0);
    return EXIT_FAILURE;
  }

  // Create socket for can1
  if ((s1 = socket(PF_CAN, SOCK_RAW, CAN_RAW)) < 0) {
    perror("Socket creation error for can1");
    close(s0);
    return EXIT_FAILURE;
  }

  memset(&ifr1, 0, sizeof(ifr1));
  strcpy(ifr1.ifr_name, CAN_INTERFACE_1);
  ioctl(s1, SIOCGIFINDEX, &ifr1);

  addr1.can_family = AF_CAN;
  addr1.can_ifindex = ifr1.ifr_ifindex;

  if (bind(s1, (struct sockaddr *)&addr1, sizeof(addr1)) < 0) {
    perror("Binding error for can1");
    close(s0);
    close(s1);
    return EXIT_FAILURE;
  }

  printf("Bridge between %s and %s is active. Logging Speed Wheel message with ID 0x%08X and first data byte 0x%02X "
         "from %s to stdout...\n",
         CAN_INTERFACE_0, CAN_INTERFACE_1, TARGET_CAN_ID, FIRST_DATA_BYTE, CAN_INTERFACE_0);

  struct termios oldt, newt;
  int ch;
  int oldf = fcntl(STDIN_FILENO, F_GETFL, 0);
  fcntl(STDIN_FILENO, F_SETFL, oldf | O_NONBLOCK);

  while (1) {
    ssize_t nbytes = read(s0, &frame, sizeof(struct can_frame));
    if (nbytes < 0) {
      perror("Read error");
      break;
    }

    if (frame.can_id == TARGET_CAN_ID && frame.data[0] == FIRST_DATA_BYTE) {
      printf("Received Speed Wheel message on %s - ID: %08X, DLC: %d\n", CAN_INTERFACE_0, frame.can_id, frame.can_dlc);

      if (frame.can_dlc >= 5) {
        unsigned char negative_ticks = frame.data[4];

        printf("Positive ticks: %u\n", positive_ticks);
        printf("Negative ticks: %u\n", negative_ticks);

        unsigned char new_positive_ticks = frame.data[3] + positive_ticks;

        printf("Transmitting Positive ticks: %u\n", new_positive_ticks);

        frame.data[3] = new_positive_ticks;
      } else {
        printf("Incomplete Speed Wheel message received\n");
      }
    }

    // Check for user input
    tcgetattr(STDIN_FILENO, &oldt);
    newt = oldt;
    newt.c_lflag &= ~(ICANON | ECHO);
    tcsetattr(STDIN_FILENO, TCSANOW, &newt);
    ch = getchar();
    tcsetattr(STDIN_FILENO, TCSANOW, &oldt);

    if (ch == 'j') {
      incrementPositiveTicks();
    }

    // Forward received frame to can1
    nbytes = write(s1, &frame, sizeof(struct can_frame));
    if (nbytes < 0) {
      perror("Write error");
      break;
    }
  }

  close(s0);
  close(s1);
  return EXIT_SUCCESS;
}

#include <logging/log.h>
LOG_MODULE_REGISTER(udp_server, CONFIG_APP_LOG_LEVEL);

#include <errno.h>
#include <lib/udp/udp.h>
#include <pb_decode.h>
#include <pb_encode.h>
#include <sys/reboot.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

#include "config.h"
#include "generated/lib/drivers/nanopb/proto/carbon_tractor.pb.h"
#include "generated/lib/drivers/nanopb/proto/hasselhoff_board.pb.h"
#include "generated/lib/drivers/nanopb/proto/ots_tractor.pb.h"
#include "hasselhoff.h"
#include "speed_wheel.h"
#include "tractor_can.h"
#include "udp_server.h"
#include "watchdawg.h"

#if IS_ENABLED(CONFIG_NET_TC_THREAD_COOPERATIVE)
#define THREAD_PRIORITY K_PRIO_COOP(CONFIG_NUM_COOP_PRIORITIES - 1)
#else
#define THREAD_PRIORITY K_PRIO_PREEMPT(8)
#endif

#define RECV_BUFFER_SIZE 2048
#define UDP_STACK_SIZE 8192

static void process_udp();

K_THREAD_DEFINE(udp_thread_id, RECV_BUFFER_SIZE, process_udp, NULL, NULL, NULL, 8, 0, K_TICKS_FOREVER);
UDP_SERVER_DEFINE(npb_udp_server, CONFIG_PB_REQUESTS_BUFFER_SIZE, UDP_STACK_SIZE, 8);

static void handle_ping(diagnostic_Ping *req, diagnostic_Pong *resp) { resp->x = req->x; }

static void handle_set_gear(ots_tractor_GearState *req, ots_tractor_GearState *resp) {
  LOG_INF("Gear request: %d", req->gear);
  rtc_command_state.gear_command = req->gear;
  resp->gear = rtc_command_state.gear_command;
}

static void handle_get_gear(ots_tractor_Empty *req, ots_tractor_GearState *resp) {
  resp->gear = command_state.gear_command;
}

static void handle_set_speed(ots_tractor_SpeedControlState *req, ots_tractor_SpeedControlState *resp) {
  rtc_command_state.speed_command = ((int)(req->speed * 100.0f));
}

static void handle_get_speed(ots_tractor_Empty *req, ots_tractor_SpeedControlState *resp) {
  resp->speed = ((float)command_state.speed_command) / 100.0f;
}

static void handle_set_hitch(ots_tractor_HitchRequest *req, ots_tractor_HitchReply *resp) {
  // log hex value of hitch_lift_force
  LOG_INF("Hitch lift force: %x", req->hitch_lift_force);
  rtc_command_state.lift_command = req->hitch_lift_force;
  k_msleep(500); // lift message runs at 2 hz. A cleaner solution should be implemented to avoid blocking the server for
                 // 500ms
  rtc_command_state.lift_command = 0x7D; // hacky fix for old lift logic
}

static void handle_set_variant(ots_tractor_TractorVariantState *req, ots_tractor_TractorVariantState *resp) {
  LOG_INF("Variant request: %d", req->variant);
  int ret = config_write_tractor_variant(req->variant);
  if (ret != 0) {
    LOG_ERR("Failed to write variant to EEPROM: %d", ret);
  }
  // resp->variant.has_variant = true;
  resp->variant = req->variant;
}

static void handle_set_wheel_cal(ots_tractor_WheelAngleCalState *req, ots_tractor_WheelAngleCalState *resp) {
  LOG_INF("Wheel cal request: sensor_deg_per_bit=%f, sensor_center_val=%d", req->sensor_deg_per_bit,
          req->sensor_center_val);

  wheel_sensor_config_t config = {.center_trim_deg = req->center_trim_deg,
                                  .right_lock_deg = req->right_lock_deg,
                                  .sensor_full_left_val = req->sensor_full_left_val,
                                  .sensor_full_right_val = req->sensor_full_right_val,
                                  .sensor_deg_per_bit = req->sensor_deg_per_bit,
                                  .sensor_center_val = req->sensor_center_val};

  int ret = config_write_wheel_sensor_config(&config);
  if (ret != 0) {
    LOG_ERR("Failed to write wheel cal config: %d", ret);
  }

  // Copy request values to response
  resp->sensor_deg_per_bit = req->sensor_deg_per_bit;
  resp->sensor_center_val = req->sensor_center_val;
  resp->center_trim_deg = req->center_trim_deg;
  resp->right_lock_deg = req->right_lock_deg;
  resp->sensor_full_left_val = req->sensor_full_left_val;
  resp->sensor_full_right_val = req->sensor_full_right_val;
}

static void handle_get_rpms(ots_tractor_Empty *req, ots_tractor_EngineRpmState *resp) {
  resp->rpms = tractor_state.rpms;
}

static void handle_get_front_pto(ots_tractor_Empty *req, ots_tractor_PtoState *resp) {
  // TODO: implement get front pto
}

static void handle_get_rear_pto(ots_tractor_Empty *req, ots_tractor_PtoState *resp) {
  // TODO: implement get rear pto
}

static void handle_get_variant(ots_tractor_Empty *req, ots_tractor_TractorVariantState *resp) {
  resp->variant = tractor_variant;
}

static void handle_get_wheel_cal(ots_tractor_Empty *req, ots_tractor_WheelAngleCalState *resp) {
  wheel_sensor_config_t config;
  int ret = config_read_wheel_sensor_config(&config);
  if (ret != 0) {
    LOG_ERR("Failed to read wheel cal config: %d", ret);
    return;
  }

  resp->sensor_deg_per_bit = config.sensor_deg_per_bit;
  resp->sensor_center_val = config.sensor_center_val;
  resp->center_trim_deg = config.center_trim_deg;
  resp->right_lock_deg = config.right_lock_deg;
  resp->sensor_full_left_val = config.sensor_full_left_val;
  resp->sensor_full_right_val = config.sensor_full_right_val;
}

static void handle_get_fuel_level(ots_tractor_Empty *req, ots_tractor_FuelLevel *resp) {
  resp->fuel_level = tractor_state.fuel_level / 255.0f;
}

static void handle_set_ignition_off(ots_tractor_Empty *req, ots_tractor_Empty *resp) {
  LOG_INF("Ignition off request");
  rtc_command_state.ignition_off_command = true;
}

static void handle_ots_tractor_set(ots_tractor_SetRequest *req, ots_tractor_SetReply *resp) {
  switch (req->which_set) {
  case ots_tractor_SetRequest_gear_tag:
    resp->which_set = ots_tractor_SetReply_gear_tag;
    handle_set_gear(&req->set.gear, &resp->set.gear);
    break;
  case ots_tractor_SetRequest_speed_control_tag:
    resp->which_set = ots_tractor_SetReply_speed_control_tag;
    handle_set_speed(&req->set.speed_control, &resp->set.speed_control);
    break;
  case ots_tractor_SetRequest_hitch_tag:
    resp->which_set = ots_tractor_SetReply_hitch_tag;
    handle_set_hitch(&req->set.hitch, &resp->set.hitch);
    break;
  case ots_tractor_SetRequest_variant_tag:
    resp->which_set = ots_tractor_SetReply_variant_tag;
    // resp->set.variant.has_variant = true;
    handle_set_variant(&req->set.variant, &resp->set.variant);
    break;
  case ots_tractor_SetRequest_wheel_cal_tag:
    resp->which_set = ots_tractor_SetReply_wheel_cal_tag;
    handle_set_wheel_cal(&req->set.wheel_cal, &resp->set.wheel_cal);
    break;
  case ots_tractor_SetRequest_ignition_off_tag:
    resp->which_set = ots_tractor_SetReply_ignition_off_tag;
    handle_set_ignition_off(&req->set.ignition_off, &resp->set.ignition_off);
    break;
  }
}

static void handle_ots_tractor_get(ots_tractor_GetRequest *req, ots_tractor_GetReply *resp) {
  switch (req->which_get) {
  case ots_tractor_GetRequest_gear_tag:
    resp->which_get = ots_tractor_GetReply_gear_tag;
    handle_get_gear(&req->get.gear, &resp->get.gear);
    break;
  case ots_tractor_GetRequest_speed_control_tag:
    resp->which_get = ots_tractor_GetReply_speed_control_tag;
    handle_get_speed(&req->get.speed_control, &resp->get.speed_control);
    break;
  case ots_tractor_GetRequest_rpms_tag:
    resp->which_get = ots_tractor_GetReply_rpms_tag;
    handle_get_rpms(&req->get.rpms, &resp->get.rpms);
    break;
  case ots_tractor_GetRequest_front_pto_tag:
    resp->which_get = ots_tractor_GetReply_front_pto_tag;
    handle_get_front_pto(&req->get.front_pto, &resp->get.front_pto);
    break;
  case ots_tractor_GetRequest_rear_pto_tag:
    resp->which_get = ots_tractor_GetReply_rear_pto_tag;
    handle_get_rear_pto(&req->get.rear_pto, &resp->get.rear_pto);
    break;
  case ots_tractor_GetRequest_variant_tag:
    resp->which_get = ots_tractor_GetReply_variant_tag;
    handle_get_variant(&req->get.variant, &resp->get.variant);
    break;
  case ots_tractor_GetRequest_wheel_cal_tag:
    resp->which_get = ots_tractor_GetReply_wheel_cal_tag;
    handle_get_wheel_cal(&req->get.wheel_cal, &resp->get.wheel_cal);
    break;
  case ots_tractor_GetRequest_fuel_level_tag:
    resp->which_get = ots_tractor_GetReply_fuel_level_tag;
    handle_get_fuel_level(&req->get.fuel_level, &resp->get.fuel_level);
    break;
  }
}

static void handle_ots_tractor(ots_tractor_Request *req, ots_tractor_Reply *resp) {
  switch (req->which_request) {
  case ots_tractor_Request_set_tag:
    resp->which_reply = ots_tractor_Reply_set_tag;
    handle_ots_tractor_set(&req->request.set, &resp->reply.set);
    break;
  case ots_tractor_Request_get_tag:
    resp->which_reply = ots_tractor_Reply_get_tag;
    handle_ots_tractor_get(&req->request.get, &resp->reply.get);
    break;
  }
}

static carbon_tractor_HHStateStatus convert_hh_state(hh_state_t state) {
  switch (state) {
  case HH_STATE_OPERATIONAL:
    return carbon_tractor_HHStateStatus_HH_OPERATIONAL;
  case HH_STATE_DISABLED:
    return carbon_tractor_HHStateStatus_HH_DISABLED;
  case HH_STATE_STOPPED:
    return carbon_tractor_HHStateStatus_HH_STOPPED;
  case HH_STATE_SAFE:
    return carbon_tractor_HHStateStatus_HH_SAFE;
  case HH_STATE_ESTOPPED:
    return carbon_tractor_HHStateStatus_HH_ESTOP;
  default:
    return carbon_tractor_HHStateStatus_HH_UNKNOWN;
  }
}

void fill_tractor_status(carbon_tractor_TractorStatus *status) {
  status->has_state = true;
  status->state.state = convert_hh_state(HH_STATE);
  status->error_flag = 0;
  status->ground_speed = ((float)tractor_state.ground_speed);
  status->wheel_angle = ((float)tractor_state.wheel_angle);
  status->hitch_lift_percentage = tractor_state.hitch_lift_percentage;
  status->has_gear = true;
  status->gear.gear = tractor_state.gear;
  status->safety_triggered = safety_state.safety_sensor_tripped;
  status->safety_bypass = safety_state.safety_bypass;
  status->remote_lockout = safety_state.lockout_switch;
}

void handle_set_brakes(carbon_tractor_BrakeState *req, carbon_tractor_BrakeState *resp) {
  // log both forces
  LOG_INF("Brake request: %d, %d", req->force_left, req->force_right);
  rtc_command_state.brake_left_command = req->force_left;
  rtc_command_state.brake_right_command = req->force_right;
}

void handle_set_steering(carbon_tractor_SteeringState *req, carbon_tractor_SteeringState *resp) {
  rtc_command_state.steering_command = req->angle;
}

void handle_set_steering_cfg(carbon_tractor_SteeringCfgState *req, carbon_tractor_SteeringCfgState *resp) {
  LOG_INF("Steering cfg request: kp=%f, ki=%f, kd=%f, integral_limit=%f, update_rate_hz=%d, "
          "min_steering_valve_current=%d, max_steering_valve_current=%d",
          req->kp, req->ki, req->kd, req->integral_limit, req->update_rate_hz, req->min_steering_valve_current,
          req->max_steering_valve_current);

  steering_config_t config = {.kp = req->kp,
                              .ki = req->ki,
                              .kd = req->kd,
                              .integral_limit = req->integral_limit,
                              .update_rate_hz = req->update_rate_hz,
                              .min_steering_valve_current = req->min_steering_valve_current,
                              .max_steering_valve_current = req->max_steering_valve_current};

  int ret = config_write_steering_config(&config);
  if (ret != 0) {
    LOG_ERR("Failed to write steering config: %d", ret);
  }

  // Copy request values to response
  resp->kp = req->kp;
  resp->ki = req->ki;
  resp->kd = req->kd;
  resp->integral_limit = req->integral_limit;
  resp->update_rate_hz = req->update_rate_hz;
  resp->min_steering_valve_current = req->min_steering_valve_current;
  resp->max_steering_valve_current = req->max_steering_valve_current;
}

void handle_set_pet_loss(carbon_tractor_PetLossState *req, carbon_tractor_PetLossState *resp) {
  LOG_INF("Pet loss request: use_stop=%d", req->use_stop);
  // Just store the configuration, actual arming happens in state transitions
  resp->use_stop = req->use_stop;
}

void handle_set_hh_state(carbon_tractor_HHState *req, carbon_tractor_HHState *resp) {
  // Convert from proto enum to internal enum
  hh_state_t new_state;
  switch (req->state) {
  case carbon_tractor_HHStateStatus_HH_OPERATIONAL:
    LOG_INF("HH state request: carbon_tractor_HHStateStatus_HH_OPERATIONAL");
    new_state = HH_STATE_OPERATIONAL;
    hh_state_flags.rtc_enable_command_pending = 3;
    watchdawg_arm(true); // Arm watchdog when entering operational state
    break;
  case carbon_tractor_HHStateStatus_HH_DISABLED:
    LOG_INF("HH state request: carbon_tractor_HHStateStatus_HH_DISABLED");
    watchdawg_arm(false); // Disarm watchdog when disabling
    new_state = HH_STATE_DISABLED;
    hh_state_flags.rtc_disable_command_pending = 3;
    break;
  case carbon_tractor_HHStateStatus_HH_STOPPED:
    LOG_INF("HH state request: carbon_tractor_HHStateStatus_HH_STOPPED");
    watchdawg_arm(true); // Disarm watchdog when stopping
    new_state = HH_STATE_STOPPED;
    break;
  case carbon_tractor_HHStateStatus_HH_SAFE:
    LOG_INF("HH state request: carbon_tractor_HHStateStatus_HH_SAFE");
    watchdawg_arm(true); // Disarm watchdog when entering safe state
    new_state = HH_STATE_SAFE;
    break;
  case carbon_tractor_HHStateStatus_HH_ESTOP:
    LOG_INF("HH state request: carbon_tractor_HHStateStatus_HH_ESTOP");
    watchdawg_arm(false); // Disarm watchdog when e-stopped
    new_state = HH_STATE_ESTOPPED;
    break;
  default:
    LOG_WRN("Invalid HH state requested: %d", req->state);
    watchdawg_arm(false);
    new_state = HH_STATE_DISABLED;
    break;
  }

  hh_set_state(new_state);
  resp->state = convert_hh_state(HH_STATE);
}

void handle_set_safety_bypass(carbon_tractor_SafetySensorBypassState *req,
                              carbon_tractor_SafetySensorBypassState *resp) {
  safety_state.safety_bypass = req->bypass;
  resp->bypass = safety_state.safety_bypass;
}

// void handle_lockout_state(carbon_tractor_Remote_Lockout_State_Request *req,
// carbon_tractor_Remote_Lockout_State_Reply *resp) {
// resp->engaged = safety_state.lockout_switch;
//}

static void handle_carbon_set(carbon_tractor_SetRequest *req, carbon_tractor_SetReply *resp) {
  switch (req->which_set) {
  case carbon_tractor_SetRequest_hh_state_tag:
    resp->which_set = carbon_tractor_SetReply_hh_state_tag;
    handle_set_hh_state(&req->set.hh_state, &resp->set.hh_state);
    break;
  case carbon_tractor_SetRequest_brakes_tag:
    resp->which_set = carbon_tractor_SetReply_brakes_tag;
    handle_set_brakes(&req->set.brakes, &resp->set.brakes);
    break;
  case carbon_tractor_SetRequest_safety_bypass_tag:
    resp->which_set = carbon_tractor_SetReply_safety_bypass_tag;
    handle_set_safety_bypass(&req->set.safety_bypass, &resp->set.safety_bypass);
    break;
  case carbon_tractor_SetRequest_steering_tag:
    resp->which_set = carbon_tractor_SetReply_steering_tag;
    handle_set_steering(&req->set.steering, &resp->set.steering);
    break;
  case carbon_tractor_SetRequest_steering_cfg_tag:
    resp->which_set = carbon_tractor_SetReply_steering_cfg_tag;
    handle_set_steering_cfg(&req->set.steering_cfg, &resp->set.steering_cfg);
    break;
  case carbon_tractor_SetRequest_pet_loss_tag:
    resp->which_set = carbon_tractor_SetReply_pet_loss_tag;
    handle_set_pet_loss(&req->set.pet_loss, &resp->set.pet_loss);
    break;
  }
}

static void handle_get_hh_state(carbon_tractor_Empty *req, carbon_tractor_HHState *resp) {
  resp->state = convert_hh_state(HH_STATE);
}

static void handle_get_brakes(carbon_tractor_Empty *req, carbon_tractor_BrakeState *resp) {
  resp->force_left = command_state.brake_left_command;
  resp->force_right = command_state.brake_right_command;
}

static void handle_get_safety(carbon_tractor_Empty *req, carbon_tractor_SafetySensorsState *resp) {
  resp->triggered_sensor_1 = safety_state.radar1;
  resp->triggered_sensor_2 = safety_state.radar2;
  resp->triggered_sensor_3 = safety_state.bumper1;
  resp->triggered_sensor_4 = safety_state.bumper2;
}

static void handle_get_safety_bypass(carbon_tractor_Empty *req, carbon_tractor_SafetySensorBypassState *resp) {
  resp->bypass = safety_state.safety_bypass;
}

static void handle_get_steering(carbon_tractor_Empty *req, carbon_tractor_SteeringState *resp) {
  resp->angle = command_state.steering_command;
}

static void handle_get_steering_cfg(carbon_tractor_Empty *req, carbon_tractor_SteeringCfgState *resp) {
  steering_config_t config;
  int ret = config_read_steering_config(&config);
  if (ret != 0) {
    LOG_ERR("Failed to read steering config: %d", ret);
    return;
  }

  resp->kp = config.kp;
  resp->ki = config.ki;
  resp->kd = config.kd;
  resp->integral_limit = config.integral_limit;
  resp->update_rate_hz = config.update_rate_hz;
  resp->min_steering_valve_current = config.min_steering_valve_current;
  resp->max_steering_valve_current = config.max_steering_valve_current;
}

static void handle_get_pet_loss(carbon_tractor_Empty *req, carbon_tractor_PetLossState *resp) {
  // TODO: implement get pet loss state
}

static void handle_carbon_get(carbon_tractor_GetRequest *req, carbon_tractor_GetReply *resp) {
  // resp->which_reply = carbon_tractor_GetReply_get_tag;
  switch (req->which_get) {
  case carbon_tractor_GetRequest_status_tag:
    resp->which_get = carbon_tractor_GetReply_status_tag;
    fill_tractor_status(&resp->get.status);
    break;
  case carbon_tractor_GetRequest_hh_state_tag:
    resp->which_get = carbon_tractor_GetReply_hh_state_tag;
    handle_get_hh_state(&req->get.hh_state, &resp->get.hh_state);
    break;
  case carbon_tractor_GetRequest_brakes_tag:
    resp->which_get = carbon_tractor_GetReply_brakes_tag;
    handle_get_brakes(&req->get.brakes, &resp->get.brakes);
    break;
  case carbon_tractor_GetRequest_safety_tag:
    resp->which_get = carbon_tractor_GetReply_safety_tag;
    handle_get_safety(&req->get.safety, &resp->get.safety);
    break;
  case carbon_tractor_GetRequest_safety_bypass_tag:
    resp->which_get = carbon_tractor_GetReply_safety_bypass_tag;
    handle_get_safety_bypass(&req->get.safety_bypass, &resp->get.safety_bypass);
    break;
  case carbon_tractor_GetRequest_steering_tag:
    resp->which_get = carbon_tractor_GetReply_steering_tag;
    handle_get_steering(&req->get.steering, &resp->get.steering);
    break;
  case carbon_tractor_GetRequest_steering_cfg_tag:
    resp->which_get = carbon_tractor_GetReply_steering_cfg_tag;
    handle_get_steering_cfg(&req->get.steering_cfg, &resp->get.steering_cfg);
    break;
  case carbon_tractor_GetRequest_pet_loss_tag:
    resp->which_get = carbon_tractor_GetReply_pet_loss_tag;
    handle_get_pet_loss(&req->get.pet_loss, &resp->get.pet_loss);
    break;
  }
}

static void handle_carbon_pet(carbon_tractor_PetRequest *req, carbon_tractor_TractorStatus *resp) {
  // resp->which_reply = carbon_tractor_TractorStatus_pet_tag;
  watchdawg_pet(true);
  fill_tractor_status(resp);
}

static void handle_carbon_debug(carbon_tractor_DebugRequest *req, carbon_tractor_DebugReply *resp) {
  // TODO: implement debug
}

// Carbon Tractor
static void handle_carbon_tractor(carbon_tractor_Request *req, carbon_tractor_Reply *resp) {
  switch (req->which_request) {
  case carbon_tractor_Request_set_tag:
    resp->which_reply = carbon_tractor_Reply_set_tag;
    handle_carbon_set(&req->request.set, &resp->reply.set);
    break;
  case carbon_tractor_Request_get_tag:
    resp->which_reply = carbon_tractor_Reply_get_tag;
    handle_carbon_get(&req->request.get, &resp->reply.get);
    break;
  case carbon_tractor_Request_pet_tag:
    resp->which_reply = carbon_tractor_Reply_pet_tag;
    handle_carbon_pet(&req->request.pet, &resp->reply.pet);
    break;
  case carbon_tractor_Request_debug_tag:
    resp->which_reply = carbon_tractor_Reply_debug_tag;
    handle_carbon_debug(&req->request.debug, &resp->reply.debug);
    break;
  }
}

// Top level handler
static void serve_request(uint8_t *data, uint16_t received, udp_msg_metadata *metadata) {
  hasselhoff_board_Request req = hasselhoff_board_Request_init_zero;
  pb_istream_t istream = pb_istream_from_buffer(data, received);
  if (!pb_decode(&istream, hasselhoff_board_Request_fields, &req)) {
    LOG_WRN("Failed to decode UDP packet with size %d", received);
    return;
  }

  hasselhoff_board_Reply resp = hasselhoff_board_Reply_init_zero;
  resp.has_header = true;
  resp.header.requestId = req.header.requestId;

  switch (req.which_request) {
  case hasselhoff_board_Request_ping_tag:
    resp.which_reply = hasselhoff_board_Reply_pong_tag;
    handle_ping(&req.request.ping, &resp.reply.pong);
    break;
  case hasselhoff_board_Request_ots_tractor_tag:
    resp.which_reply = hasselhoff_board_Reply_ots_tractor_tag;
    handle_ots_tractor(&req.request.ots_tractor, &resp.reply.ots_tractor);
    break;
  case hasselhoff_board_Request_carbon_tractor_tag:
    resp.which_reply = hasselhoff_board_Reply_carbon_tractor_tag;
    handle_carbon_tractor(&req.request.carbon_tractor, &resp.reply.carbon_tractor);
    break;
  }

  pb_ostream_t ostream = pb_ostream_from_buffer(data, udp_buffer_size(&npb_udp_server));
  if (!pb_encode(&ostream, hasselhoff_board_Reply_fields, &resp)) {
    LOG_WRN("Failed to encode nanoPB response");
    return;
  }
  udp_tx(&npb_udp_server, data, ostream.bytes_written, metadata);
}

static void process_udp() {
  for (;;) {
    uint8_t *data;
    udp_msg_metadata metadata;
    uint16_t received = udp_get_data_no_copy(&npb_udp_server, &data, &metadata, 0);
    serve_request(data, received, &metadata);
    udp_get_data_release(&npb_udp_server, data);
  }
}

void start_udp_server() {
  UDP_SERVER_START(npb_udp_server, CONFIG_APP_UDP_PORT);
  k_thread_name_set(udp_thread_id, "udp");
  k_thread_start(udp_thread_id);
}

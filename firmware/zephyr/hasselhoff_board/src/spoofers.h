#pragma once
#include "hasselhoff.h"
#include "tractor_can.h"
#include <lib/tractor_can/tractor_can_messages.h>
#include <zephyr/kernel.h>

void spoof_scv1_lever_detent_up(void);
void spoof_scv1_lever_detent_down(void);
void spoof_scv2_lever(int32_t lever_position);
void spoof_scv3_lever(int32_t lever_position);
void spoof_scv2_lever_detent_up(void);
void spoof_scv2_lever_detent_down(void);
void spoof_scv3_lever_detent_up(void);
void spoof_scv3_lever_detent_down(void);
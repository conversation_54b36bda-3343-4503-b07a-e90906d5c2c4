#include "cab_handlers.h"
#include "LHM_gear.h"
#include "speed_control.h"
#include <logging/log.h>
LOG_MODULE_REGISTER(cab_handlers, CONFIG_APP_LOG_LEVEL);

static const struct gpio_dt_spec lh_park = GPIO_DT_SPEC_GET(DT_PATH(gpios, lh_park), gpios);

void handle_lh_gear(struct zcan_frame *msg) {
  if (!COMPARE_MSG_ID(msg->id, LH_GEAR_SHIFT_ID)) {
    return;
  }

  static bool gear_init = 0;
  if (!gear_init) {
    HANDLE_CRITICAL(gpio_pin_configure_dt(&lh_park, GPIO_OUTPUT));
    gear_init = true;
    cab_state.gear = msg->data[2];
    LOG_INF("LH Gear handler initialized");
  }
  uint8_t gear_selection = msg->data[2];
  uint8_t gear_fwdrvr = msg->data[3];
  if (cab_state.gear != gear_selection) {
    // Operator touched gear selector
    OPERATOR_OVERRIDE("LH Reverser");
  }
  cab_state.gear = gear_selection;

  if (CONTROL_IS_ENABLED) {
    switch (command_state.gear_command) {
    case GEAR_COMMAND_PARK:
      gear_selection = LH_GEAR_SELECT_PARK;
      gear_fwdrvr = LH_GEAR_FWDRVR_NONE;
      break;
    case GEAR_COMMAND_REVERSE:
      gear_selection = LH_GEAR_SELECT_RVR;
      gear_fwdrvr = LH_GEAR_FWDRVR_RVR;
      break;
    case GEAR_COMMAND_NEUTRAL:
      gear_selection = LH_GEAR_SELECT_NEUTRAL;
      gear_fwdrvr = LH_GEAR_FWDRVR_NONE;
      break;
    case GEAR_COMMAND_FORWARD:
      gear_selection = LH_GEAR_SELECT_FWD;
      gear_fwdrvr = LH_GEAR_FWDRVR_FWD;
      break;
    case GEAR_COMMAND_POWER:
      gear_selection = LH_GEAR_SELECT_SCROLL;
      gear_fwdrvr = LH_GEAR_FWDRVR_NONE;
      break;
    default:
      gear_selection = LH_GEAR_SELECT_PARK;
      gear_fwdrvr = LH_GEAR_FWDRVR_NONE;
      break;
    }
  }

  gpio_pin_set(lh_park.port, lh_park.pin, gear_selection == LH_GEAR_SELECT_PARK);

  msg->data[2] = gear_selection;
  msg->data[3] = gear_fwdrvr;
}

void handle_rh_gear(struct zcan_frame *msg) {
  if (!COMPARE_MSG_ID(msg->id, RH_GEAR_SHIFT_ID) || !(msg->data[0] == RH_GEAR_SHIFT_FIRST_BYTE)) {
    return;
  }

  static bool gear_init = 0;
  if (!gear_init) {
    gear_init = true;
    cab_state.gear = msg->data[2];
    LOG_INF("RH Gear handler initialized");
  }
  uint8_t gear_selection = msg->data[2];
  if (cab_state.gear != gear_selection) {
    // Operator touched gear selector
    OPERATOR_OVERRIDE("RH Reverser");
  }
  cab_state.gear = gear_selection;

  if (CONTROL_IS_ENABLED) {
    switch (command_state.gear_command) {
    case GEAR_COMMAND_PARK:
      gear_selection = RH_GEAR_SELECT_PARK;
      break;
    case GEAR_COMMAND_REVERSE:
      gear_selection = RH_GEAR_SELECT_RVR;
      break;
    case GEAR_COMMAND_NEUTRAL:
      gear_selection = RH_GEAR_SELECT_NEUTRAL;
      break;
    case GEAR_COMMAND_FORWARD:
      gear_selection = RH_GEAR_SELECT_F1;
      break;
    case GEAR_COMMAND_POWER:
      gear_selection = RH_GEAR_SELECT_ZERO;
      break;
    default:
      gear_selection = RH_GEAR_SELECT_PARK;
      break;
    }
  }

  msg->data[2] = gear_selection;
}

void handle_cpro_joystick_copy(struct zcan_frame *msg) {
  if (!COMPARE_MSG_ID(msg->id, CPRO_JOYSTICK_COPY_ID) || !(msg->data[0] == CPRO_JOYSTICK_COPY_FIRST_BYTE)) {
    return;
  }

  msg->data[1] = (mitm_state.gear >> 24) & 0xFF;
  msg->data[2] = ((mitm_state.gear >> 16) & 0x0F) | 0xF0;
  msg->data[3] = (mitm_state.gear >> 8) & 0xFF;
}

void handle_cpro_gear_checksum(struct zcan_frame *msg) {
  if (!COMPARE_MSG_ID(msg->id, CPRO_CHECKSUM_ID) || !(msg->data[0] == CPRO_CHECKSUM_FIRST_BYTE)) {
    return;
  }

  if (!CONTROL_IS_ENABLED) {
    return;
  }

  switch (command_state.gear_command) {
  case GEAR_COMMAND_FORWARD:
    // Intentional fallthrough
  case GEAR_COMMAND_REVERSE:
    msg->data[1] = (msg->data[1] & 0x0F) | 0x30;
    break;
  case GEAR_COMMAND_NEUTRAL:
    msg->data[1] = (msg->data[1] & 0x0F) | 0x00;
    break;
  case GEAR_COMMAND_PARK:
    // intentional fallthrough
  default:
    msg->data[1] = (msg->data[1] & 0x0F) | 0x10;
  }
}

void handle_cpro_joystick(struct zcan_frame *msg) {
  if (!COMPARE_MSG_ID(msg->id, CPRO_JOYSTICK_ID) || !(msg->data[0] == CPRO_JOYSTICK_FIRST_BYTE) ||
      !(tractor_variant == JD_6PRO)) {
    return;
  }

  static bool joystick_init = 0;
  uint32_t gear_selection = msg->data[1] << 24 | msg->data[2] << 16 | msg->data[3] << 8 | msg->data[4];
  if (!joystick_init) {
    joystick_init = true;
    cab_state.gear = gear_selection;
    LOG_INF("CPRO Gear handler initialized");
  }
  if (cab_state.gear != gear_selection) {
    // Operator touched gear selector
    LOG_INF("Operator Override: CommandPro Joystick");
    OPERATOR_OVERRIDE("CommandPro Joystick");
  }
  cab_state.gear = gear_selection;

  if (CONTROL_IS_ENABLED) {
    bool set_park = false;
    static long count = 0;

    switch (command_state.gear_command) {
    case GEAR_COMMAND_FORWARD:
      if (tractor_state.gear == GEAR_COMMAND_REVERSE) {
        set_park = true;
      } else {
        gear_selection = 0x11D4F900 | (gear_selection & 0x000000FF);
      }
      break;
    case GEAR_COMMAND_REVERSE:
      if (tractor_state.gear == GEAR_COMMAND_FORWARD) {
        set_park = true;
      } else {
        gear_selection = 0x11D60100 | (gear_selection & 0x000000FF);
      }
      break;
    case GEAR_COMMAND_NEUTRAL:
      uint8_t neutral_nibble = (count / 20) % 8;
      gear_selection = ((neutral_nibble << 28) & 0xF0000000) | (gear_selection & 0x0FFFFFFF);
      count++;
      break;
    default: // default to park
      set_park = true;
    }
    if (set_park) {
      uint8_t park_nibble = (count / 20) % 8;
      gear_selection = ((park_nibble << 24) & 0x0F000000) | (gear_selection & 0xF0FFFFFF);
      count++;
    }

    msg->data[1] = (gear_selection >> 24) & 0xFF;
    msg->data[2] = (gear_selection >> 16) & 0xFF;
    msg->data[3] = (gear_selection >> 8) & 0xFF;
  }
  mitm_state.gear = gear_selection;
  //////////////////////////////////////////////speed wheel//////////////////////////////////////////////

  static bool speed_wheel_init = 0;
  static bool had_control = 0;
  uint8_t speed_wheel_pos = msg->data[5] >= 0xFB ? 0 : msg->data[5];
  uint8_t speed_wheel_neg = msg->data[6] >= 0xFB ? 0 : msg->data[6];

  // LOG_INF("Receiving Pos:%x, Neg%x", msg->data[5], msg->data[6]);
  if (!speed_wheel_init) {
    speed_wheel_init = true;
    cab_state.speed_wheel_pos = speed_wheel_pos;
    cab_state.speed_wheel_neg = speed_wheel_neg;
    mitm_state.speed_wheel_pos = speed_wheel_pos;
    mitm_state.speed_wheel_neg = speed_wheel_neg;
    LOG_INF("Speed Wheel handler initialized");
  }

  if (speed_wheel_pos != cab_state.speed_wheel_pos) {
    // operator touched speed wheel pos
    OPERATOR_OVERRIDE("Speed Wheel");
    if (speed_wheel_pos < cab_state.speed_wheel_pos) {
      mitm_state.speed_wheel_pos += SPEED_WHEEL_OVERFLOW;
    }
    mitm_state.speed_wheel_pos += speed_wheel_pos - cab_state.speed_wheel_pos;
    mitm_state.speed_wheel_pos %= SPEED_WHEEL_OVERFLOW;
  }
  if (speed_wheel_neg != cab_state.speed_wheel_neg) {
    // operator touched speed wheel neg
    OPERATOR_OVERRIDE("Speed Wheel");
    if (speed_wheel_neg < cab_state.speed_wheel_neg) {
      mitm_state.speed_wheel_neg += SPEED_WHEEL_OVERFLOW;
    }
    mitm_state.speed_wheel_neg += speed_wheel_neg - cab_state.speed_wheel_neg;
    mitm_state.speed_wheel_neg %= SPEED_WHEEL_OVERFLOW;
  }

  cab_state.speed_wheel_pos = speed_wheel_pos;
  cab_state.speed_wheel_neg = speed_wheel_neg;

  if (k_mutex_lock(&mitm_state_mutex, K_MSEC(8)) != 0) {
    LOG_ERR("Failed to acquire mitm_state_mutex in speed wheel handler");
  } else {
    if (CONTROL_IS_ENABLED) {
      if (mitm_state.speed_wheel_rtc_ticks > 0) {
        mitm_state.speed_wheel_pos += mitm_state.speed_wheel_rtc_ticks;
        mitm_state.speed_wheel_pos %= SPEED_WHEEL_OVERFLOW;
      }
      if (mitm_state.speed_wheel_rtc_ticks < 0) {
        mitm_state.speed_wheel_neg -= mitm_state.speed_wheel_rtc_ticks;
        mitm_state.speed_wheel_neg %= SPEED_WHEEL_OVERFLOW;
      }
      if (!had_control) { // Drastically lower transmission set point when taking control
        mitm_state.speed_wheel_neg += 20;
        mitm_state.speed_wheel_neg %= SPEED_WHEEL_OVERFLOW;
      }
    } else {
      if (msg->data[5] == 0xFB &&
          msg->data[6] == 0xFB) { // Dramatically lower transmission set point on fresh startup before control
        mitm_state.speed_wheel_neg++;
        mitm_state.speed_wheel_neg %= SPEED_WHEEL_OVERFLOW;
      }
    }

    tractor_state.transmission_speed_stale_data_flag = mitm_state.speed_wheel_rtc_ticks;
    mitm_state.speed_wheel_rtc_ticks = 0;
    k_mutex_unlock(&mitm_state_mutex);
  }

  msg->data[5] = mitm_state.speed_wheel_pos;
  msg->data[6] = mitm_state.speed_wheel_neg;
  // log

  // LOG_INF("Sending Pos:%x, Neg%x", msg->data[5], msg->data[6]);
  had_control = CONTROL_IS_ENABLED;
}

void handle_simple_lift_lever(struct zcan_frame *msg) {
  bool is_valid = false;
  if (COMPARE_MSG_ID(msg->id, LHM_HITCH_LIFT_ID) && (msg->data[0] == LHM_HITCH_LIFT_FIRST_BYTE) &&
      (tractor_variant == JD_6LHM)) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, CPRO_HITCH_LIFT_ID) && (msg->data[0] == CPRO_HITCH_LIFT_FIRST_BYTE) &&
             tractor_variant == JD_6PRO) {
    is_valid = true;
  }

  if (!is_valid) {
    return;
  }

  static bool lift_init = 0;
  if (!lift_init) {
    lift_init = true;
    cab_state.lift = ((msg->data[1] << 24) | (msg->data[2] << 16) | (msg->data[3] << 8) | (msg->data[4]));
    command_state.lift_command = 0x7D;
    LOG_INF("Lift handler initialized");
  }

  if (cab_state.lift != ((msg->data[1] << 24) | (msg->data[2] << 16) | (msg->data[3] << 8))) {
    // Operator touched lift lever
    OPERATOR_OVERRIDE("Lift Lever");
  }
  cab_state.lift = ((msg->data[1] << 24) | (msg->data[2] << 16) | (msg->data[3] << 8));

  if (!CONTROL_IS_ENABLED) {
    return;
  }

  msg->data[3] = command_state.lift_command;

  // LOG_INF("ID: %x --- MSG: %x%x%x%x%x%x%x%x", msg->id, msg->data[0], msg->data[1], msg->data[2], msg->data[3],
  //   msg->data[4], msg->data[5], msg->data[6], msg->data[7]);
}

void handle_lift_lever(struct zcan_frame *msg) {
  if (!COMPARE_MSG_ID(msg->id, HITCH_LIFT_ID) || !(msg->data[0] == HITCH_LIFT_FIRST_BYTE)) {
    return;
  }
  const uint8_t LIFT_UP = 0x00;
  const uint8_t LIFT_DOWN = 0xFA;

  static uint8_t xfa_cnt = 0;
  static uint8_t x00_cnt = 0;
  static bool lift_init = 0;
  if (!lift_init) {
    lift_init = true;
    cab_state.lift = ((msg->data[1] << 24) | (msg->data[2] << 16) | (msg->data[3] << 8) | (msg->data[4]));
    command_state.lift_command = 0x7D;
    LOG_INF("Lift handler initialized");
  }

  if (cab_state.lift != ((msg->data[1] << 24) | (msg->data[2] << 16) | (msg->data[3] << 8) | (msg->data[4]))) {
    // Operator touched lift lever
    OPERATOR_OVERRIDE("Hitch Lift");
  }
  cab_state.lift = ((msg->data[1] << 24) | (msg->data[2] << 16) | (msg->data[3] << 8) | (msg->data[4]));

  if (!CONTROL_IS_ENABLED) {
    x00_cnt = 0;
    xfa_cnt = 0;
    return;
  }

  if (command_state.lift_command == LIFT_UP) {
    x00_cnt = 15;
    xfa_cnt = 0;
  } else if (command_state.lift_command == LIFT_DOWN) {
    xfa_cnt = 15;
    x00_cnt = 0;
  }

  uint8_t lift_lever = msg->data[3];
  if (x00_cnt > 0) {
    x00_cnt--;
    lift_lever = LIFT_UP;
  }
  if (xfa_cnt > 0) {
    xfa_cnt--;
    lift_lever = LIFT_DOWN;
  }
  msg->data[3] = lift_lever;
  // LOG_INF("ID: %x --- MSG: %x%x%x%x%x%x%x%x", msg->id, msg->data[0], msg->data[1], msg->data[2], msg->data[3],
  // msg->data[4], msg->data[5], msg->data[6], msg->data[7]);
}

void handle_scv_lever(struct zcan_frame *msg) {
  if (!COMPARE_MSG_ID(msg->id, RH_SCV_ID) || !(msg->data[0] == RH_SCV_FIRST_BYTE)) {
    return;
  }

  static bool scv_init = 0;
  if (!scv_init) {
    scv_init = true;
    cab_state.scv2_lever = msg->data[3];
    cab_state.scv3_lever = msg->data[4];
    LOG_INF("SCV handler initialized");
  }
  uint8_t scv2_lever = msg->data[3];
  uint8_t scv3_lever = msg->data[4];
  if (cab_state.scv2_lever != scv2_lever || cab_state.scv3_lever != scv3_lever) {
    // Operator touched SCV lever
    OPERATOR_OVERRIDE("SCV Lever");
  }
  cab_state.scv2_lever = scv2_lever;
  cab_state.scv3_lever = scv3_lever;
  if (CONTROL_IS_ENABLED) {
    scv2_lever = mitm_state.scv2_lever;
    scv3_lever = mitm_state.scv3_lever;
  }
  msg->data[3] = scv2_lever;
  msg->data[4] = scv3_lever;
}

// Annoying type of SCV because it requires spoofer
void handle_annoying_scv_lever(struct zcan_frame *msg) {
  if (!COMPARE_MSG_ID(msg->id, ANNOYING_SCV_ID) || !(msg->data[0] == ANNOYING_SCV_FIRST_BYTE)) {
    return;
  }

  static bool scv_init = 0;
  static bool had_control = false;
  if (!scv_init) {
    scv_init = true;
    LOG_INF("Annoying SCV handler initialized");
  }

  if (CONTROL_IS_ENABLED) {
    if ((command_state.brake_left_command != 0) && (msg->data[1] == ANNOYING_SCV2_SECOND_BYTE)) {
      msg->data[3] = 0;
      msg->data[4] = 0;
    }
    if (tractor_variant == JD_6PRO) {
      if ((command_state.brake_right_command != 0) && (msg->data[1] == ANNOYING_SCV1_SECOND_BYTE)) {
        msg->data[3] = 0;
        msg->data[4] = 0;
      }
    } else {
      if ((command_state.brake_right_command != 0) && (msg->data[1] == ANNOYING_SCV3_SECOND_BYTE)) {
        msg->data[3] = 0;
        msg->data[4] = 0;
      }
    }
  } else if (had_control) {
    if ((command_state.brake_left_command != 0) && (msg->data[1] == ANNOYING_SCV2_SECOND_BYTE)) {
      msg->data[3] = 0xFF;
      msg->data[4] = 0xFA;
    }
    if (tractor_variant == JD_6PRO) {
      if ((command_state.brake_right_command != 0) && (msg->data[1] == ANNOYING_SCV1_SECOND_BYTE)) {
        msg->data[3] = 0xFF;
        msg->data[4] = 0xFA;
      }
    } else {
      if ((command_state.brake_right_command != 0) && (msg->data[1] == ANNOYING_SCV3_SECOND_BYTE)) {
        msg->data[3] = 0xFF;
        msg->data[4] = 0xFA;
      }
    }
  }
  had_control = CONTROL_IS_ENABLED;
}

void handle_speed_wheel_lever(struct zcan_frame *msg) {
  bool is_valid = false;
  if (COMPARE_MSG_ID(msg->id, SPEED_WHEEL_LEVER_ID) && (msg->data[0] == SPEED_WHEEL_LEVER_FIRST_BYTE) &&
      (tractor_variant == JD_6LH || tractor_variant == JD_8RH)) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, LHM_SPEED_WHEEL_LEVER_ID) && (msg->data[0] == LHM_SPEED_WHEEL_LEVER_FIRST_BYTE) &&
             tractor_variant == JD_6LHM) {
    is_valid = true;
  }

  if (!is_valid) {
    return;
  }

  static bool speed_wheel_init = 0;
  static bool had_control = 0;
  uint32_t speed_lever = msg->data[1];
  uint32_t speed_lever_f1_f2_value = msg->data[2];
  uint32_t speed_wheel_pos = msg->data[3] % SPEED_WHEEL_OVERFLOW;
  uint32_t speed_wheel_neg = msg->data[4] % SPEED_WHEEL_OVERFLOW;

  if (!speed_wheel_init) {
    LOG_INF("Speed wheel initialized");
    mitm_state.speed_wheel_pos = speed_wheel_pos;
    cab_state.speed_wheel_pos = speed_wheel_pos;
    mitm_state.speed_wheel_neg = speed_wheel_neg;
    cab_state.speed_wheel_neg = speed_wheel_neg;
    cab_state.speed_lever = speed_lever;
    cab_state.speed_lever_s1 = speed_lever_f1_f2_value;
    speed_wheel_init = 1;
  }

  if (speed_lever_f1_f2_value == 0x9F) {
    cab_state.speed_lever_s1 = 1;
  } else {
    cab_state.speed_lever_s1 = 0;
  }

  if ((cab_state.speed_lever != speed_lever)) {
    // Operator touched speed wheel
    OPERATOR_OVERRIDE("Speed Lever");
  }

  if (CONTROL_IS_ENABLED) {
    // Just use top of F1 for all remote speed control
    mitm_state.speed_lever = 0xFA;
    mitm_state.speed_lever_f1_f2_value = 0x9F;
  }

  if (speed_wheel_pos != cab_state.speed_wheel_pos) {
    // operator touched speed wheel
    OPERATOR_OVERRIDE("Speed Wheel");
    if (speed_wheel_pos < cab_state.speed_wheel_pos) {
      mitm_state.speed_wheel_pos += SPEED_WHEEL_OVERFLOW;
    }
    mitm_state.speed_wheel_pos += speed_wheel_pos - cab_state.speed_wheel_pos;
    mitm_state.speed_wheel_pos %= SPEED_WHEEL_OVERFLOW;
  }

  if (speed_wheel_neg != cab_state.speed_wheel_neg) {
    // operator touched speed wheel
    OPERATOR_OVERRIDE("Speed Wheel");
    if (speed_wheel_pos < cab_state.speed_wheel_neg) {
      mitm_state.speed_wheel_neg += SPEED_WHEEL_OVERFLOW;
    }
    mitm_state.speed_wheel_neg += speed_wheel_neg - cab_state.speed_wheel_neg;
    mitm_state.speed_wheel_neg %= SPEED_WHEEL_OVERFLOW;
  }

  cab_state.speed_lever = speed_lever;
  cab_state.speed_lever_s1 = speed_lever_f1_f2_value;
  cab_state.speed_wheel_pos = speed_wheel_pos;
  cab_state.speed_wheel_neg = speed_wheel_neg;

  if (CONTROL_IS_ENABLED) {
    msg->data[1] = mitm_state.speed_lever;
    msg->data[2] = mitm_state.speed_lever_f1_f2_value;
  } else {
    msg->data[1] = speed_lever;
    msg->data[2] = speed_lever_f1_f2_value;
  }

  if (k_mutex_lock(&mitm_state_mutex, K_MSEC(8)) != 0) {
    LOG_ERR("Failed to acquire mitm_state_mutex in speed wheel handler");
  } else {
    if (CONTROL_IS_ENABLED) {
      if (mitm_state.speed_wheel_rtc_ticks > 0) {
        mitm_state.speed_wheel_pos += mitm_state.speed_wheel_rtc_ticks;
        mitm_state.speed_wheel_pos %= SPEED_WHEEL_OVERFLOW;
      }
      if (mitm_state.speed_wheel_rtc_ticks < 0) {
        mitm_state.speed_wheel_neg -= mitm_state.speed_wheel_rtc_ticks;
        mitm_state.speed_wheel_neg %= SPEED_WHEEL_OVERFLOW;
      }
      if (!had_control) { // Drastically lower transmission set point when taking control
        mitm_state.speed_wheel_neg += 20 % SPEED_WHEEL_OVERFLOW;
      }
    }
    tractor_state.transmission_speed_stale_data_flag = mitm_state.speed_wheel_rtc_ticks;
    mitm_state.speed_wheel_rtc_ticks = 0;
    k_mutex_unlock(&mitm_state_mutex);
  }

  msg->data[3] = mitm_state.speed_wheel_pos;
  msg->data[4] = mitm_state.speed_wheel_neg;

  had_control = CONTROL_IS_ENABLED;
}

void filter_log(struct zcan_frame *msg) {
  uint32_t ignore[] = {0x0CFFF8F6};
  uint8_t first_byte[] = {0xB6};

  for (int i = 0; i < sizeof(ignore) / sizeof(ignore[0]); i++) {
    if (COMPARE_MSG_ID(msg->id, ignore[i]) && (msg->data[0] == first_byte[i])) {
      continue;
    }
    LOG_INF("ID: %x --- MSG: %x%x%x%x%x%x%x%x", msg->id, msg->data[0], msg->data[1], msg->data[2], msg->data[3],
            msg->data[4], msg->data[5], msg->data[6], msg->data[7]);
  }
}

int add_cab_handlers() {
  switch (tractor_variant) {
  case JD_6PRO:
    // Command Pro controls
    HANDLE_CRITICAL(add_cab_mitm_handler(handle_cpro_joystick_copy));
    HANDLE_CRITICAL(add_cab_mitm_handler(handle_cpro_gear_checksum));
    HANDLE_CRITICAL(add_cab_mitm_handler(handle_simple_lift_lever));
    HANDLE_CRITICAL(add_cab_mitm_handler(handle_cpro_joystick));
    HANDLE_CRITICAL(add_cab_mitm_handler(handle_annoying_scv_lever));
    break;
  case JD_8RH:
    HANDLE_CRITICAL(add_cab_mitm_handler(handle_rh_gear));
    HANDLE_CRITICAL(add_cab_mitm_handler(handle_speed_wheel_lever));
    break;

  case JD_6LH:
    HANDLE_CRITICAL(add_cab_mitm_handler(handle_lh_gear));
    HANDLE_CRITICAL(add_cab_mitm_handler(handle_annoying_scv_lever));
    HANDLE_CRITICAL(add_cab_mitm_handler(handle_lift_lever));
    HANDLE_CRITICAL(add_cab_mitm_handler(handle_speed_wheel_lever));
    break;

  case JD_6LHM:
    lhm_gear_init();
    HANDLE_CRITICAL(add_cab_mitm_handler(handle_annoying_scv_lever));
    HANDLE_CRITICAL(add_cab_mitm_handler(handle_simple_lift_lever));
    HANDLE_CRITICAL(add_cab_mitm_handler(handle_speed_wheel_lever));
    break;

  default:
    LOG_WRN("Unknown tractor variant");
    return -ENOMEM;
  }
  return 0;
}

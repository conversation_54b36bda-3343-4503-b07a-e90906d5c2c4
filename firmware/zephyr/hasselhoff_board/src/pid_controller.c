#include "pid_controller.h"
#include <string.h>

// Copied from https://github.com/robokamran/ControlSystems, MIT license

void pid_reset(PID *s) {
  /* Clear the state buffer. The size will be always 3 samples */
  memset(s->state, 0, 3u * sizeof(float));
}

void pid_initialize(PID *s, bool reset) {
  /* Derived coefficient A0 */
  s->A0 = s->Kp + s->Ki + s->Kd;

  /* Derived coefficient A1 */
  s->A1 = (-s->Kp) - (2.0f * s->Kd);

  /* Derived coefficient A2 */
  s->A2 = s->Kd;

  /* Check whether state needs reset or not */
  if (reset)
    pid_reset(s);
}

float pid_execute_work(PID *s, float e) {
  float c;

  /* y[n] = y[n-1] + A0 * x[n] + A1 * x[n-1] + A2 * x[n-2] */
  c = (s->A0 * e) + (s->A1 * s->state[0]) + (s->A2 * s->state[1]) + (s->state[2]);

  /* Update state */
  s->state[1] = s->state[0];
  s->state[0] = e;
  s->state[2] = c;

  /* return to application */
  return c;
}

// TODO: No anti-windup
int32_t pid_execute(PID *s, float e) {
  int32_t control_signal = (int32_t)pid_execute_work(s, e);

  // min for absolute value of control signal.
  // -control_signal_min < control_signal < 0
  if ((-1 * s->control_signal_min < control_signal) && (control_signal < 0)) {
    control_signal = -1 * s->control_signal_min;
  }
  // 0 <= control_signal < control_signal_min
  else if ((0 <= control_signal) && (control_signal < s->control_signal_min)) {
    control_signal = s->control_signal_min;
  }

  // max for absolute value of control_signal.
  if (control_signal < -1 * s->control_signal_max) {
    control_signal = -1 * s->control_signal_max;
  } else if (control_signal > s->control_signal_max) {
    control_signal = s->control_signal_max;
  }

  return control_signal;
}
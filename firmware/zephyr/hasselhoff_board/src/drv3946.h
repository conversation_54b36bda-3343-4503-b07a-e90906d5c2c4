#include "stdint.h"
#include <zephyr/device.h> // Note to self, it's easy to forget to include this header, but you need it to use the devicetree.
#include <zephyr/drivers/spi.h>
#include <zephyr/zephyr.h>

#pragma once

/*******************************************************************************
 * DRV3946 SPI Register ADDRESS
 *******************************************************************************/
#define DRV3946_REG_STATUS0 (uint16_t)(0x01)  // STATUS0 Register
#define DRV3946_REG_STATUS1 (uint16_t)(0x02)  // STATUS1 Register
#define DRV3946_REG_STATUS2 (uint16_t)(0x03)  // STATUS2 Register
#define DRV3946_REG_STATUS3 (uint16_t)(0x04)  // STATUS3 Register
#define DRV3946_REG_STATUS4 (uint16_t)(0x0A)  // STATUS4 Register
#define DRV3946_REG_STATUS5 (uint16_t)(0x0B)  // STATUS5 Register
#define DRV3946_REG_MEAS0 (uint16_t)(0x05)    // MEAS1 Register
#define DRV3946_REG_MEAS1 (uint16_t)(0x06)    // MEAS2 Register
#define DRV3946_REG_MEAS2 (uint16_t)(0x07)    // MEAS3 Register
#define DRV3946_REG_MEAS3 (uint16_t)(0x08)    // MEAS4 Register
#define DRV3946_REG_MEAS4 (uint16_t)(0x09)    // MEAS5 Register
#define DRV3946_REG_MEAS5 (uint16_t)(0x0C)    // MEAS6 Register
#define DRV3946_REG_MEAS6 (uint16_t)(0x0D)    // MEAS7 Register
#define DRV3946_REG_CONFIGA0 (uint16_t)(0x10) // CONFIGA0 Register
#define DRV3946_REG_CONFIGA1 (uint16_t)(0x11) // CONFIGA1 Register
#define DRV3946_REG_CONFIGA2 (uint16_t)(0x12) // CONFIGA2 Register
#define DRV3946_REG_CONFIGA3 (uint16_t)(0x13) // CONFIGA3 Register
#define DRV3946_REG_CONFIGA4 (uint16_t)(0x14) // CONFIGA4 Register
#define DRV3946_REG_CONFIGA5 (uint16_t)(0x15) // CONFIGA5 Register
#define DRV3946_REG_CONFIGA6 (uint16_t)(0x16) // CONFIGA6 Register
#define DRV3946_REG_CONFIGB0 (uint16_t)(0x17) // CONFIGB0 Register
#define DRV3946_REG_CONFIGB1 (uint16_t)(0x18) // CONFIGB1 Register
#define DRV3946_REG_CONFIGB2 (uint16_t)(0x19) // CONFIGB2 Register
#define DRV3946_REG_CONFIGB3 (uint16_t)(0x1A) // CONFIGB3 Register
#define DRV3946_REG_CONFIGB4 (uint16_t)(0x1B) // CONFIGB4 Register
#define DRV3946_REG_CMD0 (uint16_t)(0x1C)     // CMD_CONFIG Register
#define DRV3946_REG_CMD1 (uint16_t)(0x1D)     // CRC_PROTECTED_CMD Register
#define DRV3946_REG_CMD2 (uint16_t)(0x1E)     // CMD_CRC Register

// Types

/**
 * @note:
 * __packed__ is not strictly needed as long as the number of bits in the bitfield
 *  exactly matches the other type in the union (16 bits). But if there is a typo in the bitfield
 *  and there are not 16 bits, it makes it easier to troubleshoot.
 * aligned(4) is to remove the warning when taking pointer to the 'data' field of union:
 *  taking address of packed member of 'struct x' may result in an unaligned
 *  pointer value [-Waddress-of-packed-member]
 */
typedef struct __attribute__((__packed__, aligned(4))) _DRV3946_STATUS0_REG {
  union {
    struct {
      uint16_t CH2_STAT : 3; // LSB (0)
      uint16_t CH2_OFF_DIAG_STAT : 1;
      uint16_t CH1_STAT : 3;
      uint16_t CH1_OFF_DIAG_STAT : 1;

      uint16_t WARNINGS : 1;
      uint16_t DEVICE_ERR : 1;
      uint16_t nFAULT_PIN_STAT : 1;
      uint16_t EN2_PIN_STAT : 1;
      uint16_t EN1_PIN_STAT : 1;
      uint16_t POR : 1;
      uint16_t NAD : 2; // MSB (15)
    } bits;
    uint16_t data;
  };
} DRV3946_STATUS0_REG;

typedef struct __attribute__((__packed__, aligned(4))) _DRV3946_STATUS1_REG {
  union {
    struct {
      uint16_t CONFIG_B_CRC_W : 1;
      uint16_t CONFIG_A_CRC_W : 1;
      uint16_t CH2_RIPROPI_W : 1;
      uint16_t CH1_RIPROPI_W : 1;
      uint16_t STARTUP_BIST_W : 1;
      uint16_t SPI_WD_W : 1;
      uint16_t OT : 1;
      uint16_t PVDD_OV : 1;

      uint16_t PVDD_UV : 1;
      uint16_t RSVD : 1;
      uint16_t DEVICE_ID : 2;
      uint16_t EN1_PIN_STAT_2 : 1;
      uint16_t EN1_PIN_STAT_1 : 1;
      uint16_t NAD : 2;
    } bits;
    uint16_t data;
  };
} DRV3946_STATUS1_REG;

typedef struct __attribute__((__packed__, aligned(4))) _DRV3946_STATUS2_REG {
  union {
    struct {
      uint16_t RSVD : 8;

      uint16_t CH1_HRT_W : 2;
      uint16_t CH1_QTOT_W : 2;
      uint16_t CH1_PRT_W : 2;
      uint16_t NAD : 2;
    } bits;
    uint16_t data;
  };
} DRV3946_STATUS2_REG;

typedef struct __attribute__((__packed__, aligned(4))) _DRV3946_STATUS3_REG {
  union {
    struct {
      uint16_t RSVD : 8;

      uint16_t CH2_HRT_W : 2;
      uint16_t CH2_QTOT_W : 2;
      uint16_t CH2_PRT_W : 2;
      uint16_t NAD : 2;
    } bits;
    uint16_t data;
  };
} DRV3946_STATUS3_REG;

typedef struct __attribute__((__packed__, aligned(4))) _DRV3946_STATUS4_REG {
  union {
    struct {
      uint16_t CH1_PWM_BIST_W : 1;
      uint16_t CH1_HC_LS_SNS_TO_W : 1;
      uint16_t CH1_HC_LOW_CURRENT_W : 1;
      uint16_t CH1_HC_HS_SNS_TO_W : 1;
      uint16_t CH1_HC_RIPPLE_LOW_W : 1;
      uint16_t CH1_HC_RIPPLE_HIGH_W : 1;
      uint16_t CH1_HC_CYCLE_SKIP_W : 1;
      uint16_t RSVD_2 : 1;

      uint16_t RSVD_1 : 1;
      uint16_t CH1_PC_LS_SNS_TO_W : 1;
      uint16_t CH1_PC_LOW_CURRENT_W : 1;
      uint16_t CH1_PC_HS_SNS_TO_W : 1;
      uint16_t CH1_PC_RIPPLE_LOW_W : 1;
      uint16_t CH1_PC_RIPPLE_HIGH_W : 1;
      uint16_t CH1_PC_CYCLE_SKIP_W : 1;
      uint16_t CH1_UCLO_W : 1;
    } bits;
    uint16_t data;
  };
} DRV3946_STATUS4_REG;

typedef struct __attribute__((__packed__, aligned(4))) _DRV3946_STATUS5_REG {
  union {
    struct {
      uint16_t CH2_PWM_BIST_W : 1;
      uint16_t CH2_HC_LS_SNS_TO_W : 1;
      uint16_t CH2_HC_LOW_CURRENT_W : 1;
      uint16_t CH2_HC_HS_SNS_TO_W : 1;
      uint16_t CH2_HC_RIPPLE_LOW_W : 1;
      uint16_t CH2_HC_RIPPLE_HIGH_W : 1;
      uint16_t CH2_HC_CYCLE_SKIP_W : 1;
      uint16_t RSVD_2 : 1;

      uint16_t RSVD_1 : 1;
      uint16_t CH2_PC_LS_SNS_TO_W : 1;
      uint16_t CH2_PC_LOW_CURRENT_W : 1;
      uint16_t CH2_PC_HS_SNS_TO_W : 1;
      uint16_t CH2_PC_RIPPLE_LOW_W : 1;
      uint16_t CH2_PC_RIPPLE_HIGH_W : 1;
      uint16_t CH2_PC_CYCLE_SKIP_W : 1;
      uint16_t CH2_UCLO_W : 1;
    } bits;
    uint16_t data;
  };
} DRV3946_STATUS5_REG;

typedef struct _DRV3946_STATUS {
  DRV3946_STATUS0_REG status0;
  DRV3946_STATUS1_REG status1;
  DRV3946_STATUS2_REG status2;
  DRV3946_STATUS3_REG status3;
  DRV3946_STATUS4_REG status4;
  DRV3946_STATUS5_REG status5;
} DRV3946_STATUS;

// Note: Native byte order is little-endian, and data over SPI is big-endian.
//       Don't memcpy in, the two bytes of each U16 need to be swapped.
typedef struct __attribute__((__packed__, aligned(4))) _DRV3946_CONFIGA_REGS {
  union {
    struct {
      // CONFIG_AO
      uint16_t CH1_HC : 8; // LSB

      uint16_t CH1_PC : 8;

      // CONFIG_A1
      uint16_t CH2_HC : 8; // LSB

      uint16_t CH2_PC : 8;

      // CONFIG_A2
      uint16_t CH2_RIPPLE_THRS : 3; // LSB
      uint16_t CYCLE_SKIP_W_DIS : 1;
      uint16_t CH2_UCLO_THRS : 2;
      uint16_t RSVD_1 : 1;
      uint16_t PWM_CYCLE_SKIP_DIS : 1;

      uint16_t CH1_RIPPLE_THRS : 3;
      uint16_t RSVD_2 : 1;
      uint16_t CH1_UCLO_THRS : 2;
      uint16_t RSVD_3 : 2;

      // CONFIG_A3
      uint16_t PT2 : 4; // LSB
      uint16_t PT1 : 4;

      uint16_t PVDD_UV_FLTR : 2;
      uint16_t PVDD_OV_FLTR : 2;
      uint16_t VDD_UV_FLTR : 2;
      uint16_t VDD_OV_FLTR : 2;

      // CONFIG_A4
      uint16_t HRT_MIN_THRS : 2; // LSB
      uint16_t HRT_MAX_THRS : 2;
      uint16_t PRT_MIN_THRS : 2;
      uint16_t QTOT_MIN_THRS : 2;

      uint16_t QTOT_MAX_THRS : 2;
      uint16_t UCLO_FAULT_FLTR : 3;
      uint16_t OCP_FLTR : 1;
      uint16_t PIN_CONFIG : 1;
      uint16_t CH1_SLOPE_COMP_EN : 1;

      // CONFIG_A5
      uint16_t CH2_PIN_TURNOFF_DLY : 3; // LSB
      uint16_t CH2_PIN_TURNON_DLY : 3;
      uint16_t CH1_PIN_TURNOFF_DLY : 3; // Wraps over a byte boundary

      uint16_t CH1_PIN_TURNON_DLY : 3;
      uint16_t nFAULT_CONFIG : 4;

      // CONFIG_A6
      uint16_t CONFIG_A_CRC8 : 8; // LSB

      uint16_t CH1_SLOPE_COMP : 8;
    } bits;
    uint16_t data[7];
  };
} DRV3946_CONFIGA_REGS;

// Note: Native byte order is little-endian, and data over SPI is big-endian.
//       Don't memcpy in, the two bytes of each U16 need to be swapped.
typedef struct __attribute__((__packed__, aligned(4))) _DRV3946_CONFIGB_REGS {
  union {
    struct {
      // CONFIG_BO
      uint16_t OLP_SEL : 3; // LSB
      uint16_t OT_W_ACTION : 1;
      uint16_t SPI_WD_SHUTOFF_EN : 1;
      uint16_t PVDD_OV_SHUTOFF_EN : 1;
      uint16_t UCLO_EN : 1;
      uint16_t RSVD_1 : 1;

      uint16_t PC_REG_DIS : 1;
      uint16_t SPI_WD_SEL : 2;
      uint16_t RETRY_WAIT_SEL : 3;
      uint16_t RSVD_2 : 1;
      uint16_t VDD_OV_SHUTOFF_EN : 1;

      // CONFIG_B1
      uint16_t PVDD_UV_W_DIS : 1; // LSB
      uint16_t LOW_CUR_W_DIS : 1;
      uint16_t RIPPLE_L_W_DIS : 1;
      uint16_t RIPPLE_U_W_DIS : 1;
      uint16_t HS_SNS_TO_DIS : 1;
      uint16_t LS_SNS_TO_DIS : 1;
      uint16_t PC_PWM_W_DIS : 1;
      uint16_t HRT_MIN_W_DIS : 1;

      uint16_t HRT_MAX_W_DIS : 1;
      uint16_t PRT_MIN_W_DIS : 1;
      uint16_t PRT_MAX_W_DIS : 1;
      uint16_t QTOT_MIN_W_DIS : 1;
      uint16_t QTOT_MAX_W_DIS : 1;
      uint16_t SPI_WD_DIS : 1;
      uint16_t OT_W_DIS : 1;
      uint16_t PVDD_OV_W_DIS : 1;

      // CONFIG_B2
      uint16_t CH2_fC_PWM : 5; // LSB
      uint16_t CH2_fSS_SEL : 2;
      uint16_t CH2_fSS_SEL_TYPE : 1;

      uint16_t CH1_fC_PWM : 5;
      uint16_t CH1_fSS_SEL : 2;
      uint16_t CH1_fSS_SEL_TYPE : 1;

      //  CONFIG_B3
      uint16_t CH2_SLOPE_COMP : 8; // LSB

      uint16_t CH1_SLOPE_COMP_EN : 1;
      uint16_t RIPROPI_W_BYPASS : 1;
      uint16_t RSVD_3 : 1;
      uint16_t CH2_OLP_DIS : 1;
      uint16_t CH1_OLP_DIS : 1;
      uint16_t STARTUP_ABIST_BYPASS : 1;
      uint16_t CP_SSC_CTRL : 1;
      uint16_t DIS_SSC : 1;

      // CONFIG_B4
      uint16_t CONFIG_B_CRC8 : 8; // LSB

      uint16_t CH2_CTRL_CONFIG : 1;
      uint16_t CH1_CTRL_CONFIG : 1;
      uint16_t RSVD_4 : 6;
    } bits;
    uint16_t data[5];
  };
} DRV3946_CONFIGB_REGS;

// subset of CANOpen DS402 power state machine
typedef enum _DS402_STATE { ds402_SWITCH_ON_DISABLED = 0, ds402_OPERATION_ENABLED = 1, ds402_FAULT = 2 } DS402_STATE;

// Not a chip register
typedef struct _DRV3946_SPI_ERRS {
  union {
    struct {
      uint8_t DEFAULT_ERR : 1; // Not a DRV3946 err code. We didn't identify which type of SPI error it was.
      uint8_t DEV_ERR : 1;
      uint8_t NAD_ERR : 1;
      uint8_t VDD_ERR : 1;
      uint8_t SPI_ERR : 1;
    } bits;
    uint8_t data;
  };
} DRV3946_SPI_ERRS;

typedef struct _DRV3946_SW_DRIVER {
  // Latest status from chip
  // STATUS_0 is most frequently Read register.
  DRV3946_STATUS status;

  // Where we keep the latest configuration intended to be written to the chip
  // CONFIG_AO and CONFIG_A1 (Hold Current) are most frequently Written registers.
  DRV3946_CONFIGA_REGS config_a;
  DRV3946_CONFIGB_REGS config_b;

  // This is not the SPI TX and RX data, just the bookkeeping for it.
  struct spi_buf tx_buf;
  struct spi_buf rx_buf;
  struct spi_buf_set tx_buf_set;
  struct spi_buf_set rx_buf_set;

  uint32_t spi_errs;               // SPI error counter
  DRV3946_SPI_ERRS spi_errs_cause; // SPI err codes seen since last cleared

  // TODO: Add ptr to SPI device tree spec. Doesn't seem to work when accessed from struct. Not actually needed if drv
  // chips all have same SPI Chip Select pin.

  uint8_t spi_addr; // Address (from 0 to 3) of the drv3946 chip on the SPI bus.

  // Motor Control State Machine
  // This is not from DRV3946.
  DS402_STATE state;
  bool enable_operation; // Required for ds402_SWITCH_ON_DISABLED -> ds402_OPERATION_ENABLED

  bool is_initialized;

  // Diagnostic information
  // In case the DRV chip is calling init in a loop, the nanopb interface can see what the cause is.
  int32_t last_init_return; // Updated by drv3946_init().

} DRV3946_SW_DRIVER;

// Public functions
void drv3946_struct_init(DRV3946_SW_DRIVER *drv, uint8_t spi_addr);
int drv3946_init(DRV3946_SW_DRIVER *drv);

int drv3946_tx_rx(DRV3946_SW_DRIVER *drv, uint8_t *txData,
                  uint8_t *rxData); // TODO: Check if not re-initializing the buffers has a performance benefit.
                                    // Otherwise use prev. drv3946_spi_tx_rx
void steering_control_set_enable_pins(void);
void steering_control_disable_enable_pins(void);
void brakes_set_enable_pins(void);
void brakes_disable_enable_pins(void);
DRV3946_STATUS drv3946_get_status_regs_pkt(DRV3946_SW_DRIVER *drv);
void drv3946_set_ch1_ch2(DRV3946_SW_DRIVER *drv, uint8_t ch1_active, uint8_t ch2_active);
int drv3946_set_ch1_current(DRV3946_SW_DRIVER *drv, uint8_t ch1_hold_current);
int drv3946_set_ch2_current(DRV3946_SW_DRIVER *drv, uint8_t ch2_hold_current);

uint32_t drv3946_get_spi_errs_count(DRV3946_SW_DRIVER *drv);
uint32_t drv3946_reset_spi_errs_count(DRV3946_SW_DRIVER *drv);

void drv3946_fault_reset(DRV3946_SW_DRIVER *drv);
void drv3946_clear_NAD_ERR(DRV3946_SW_DRIVER *drv);

void drv3946_disable_operation(DRV3946_SW_DRIVER *drv);
void drv3946_enable_operation(DRV3946_SW_DRIVER *drv);

// Stuff to get rid of
void drv3946_clear_addr1_faults_and_reinit(DRV3946_SW_DRIVER *drv);

// Things I'm keeping around solely so the nanopb interface can call something without having a pointer to drv
uint32_t
drv3946_spi_tx_rx(uint8_t *txData, uint8_t *rxData,
                  uint32_t len); // Can't get rid of this because of bug with keeping spi_dt_spec in drv struct.
DRV3946_STATUS drv3946_get_status_regs(void);
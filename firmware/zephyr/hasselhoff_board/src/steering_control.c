#include "steering_control.h"
#include "config.h"
#include "drv3946.h"
#include "pid_controller.h"
#include "threads.h"
#include <logging/log.h>
#include <utils/repeat_counter.h>
#include <zephyr.h>
#include <zephyr/drivers/gpio.h>

#include "hasselhoff.h" // tractor_variant
#include "tractor_state.h"

LOG_MODULE_REGISTER(steering_control, CONFIG_APP_LOG_LEVEL);

#define STACK_SIZE 1024
K_THREAD_DEFINE(steering_control_thread_id, STACK_SIZE, steering_control_thread, NULL, NULL, NULL,
                STEERING_CONTROL_THREAD_PRIORITY, 0, K_TICKS_FOREVER);

PID_CONTROLLER pid_steering_angle;
PID new_steering_control_pid; // NEW

// Public variables
struct k_mutex drv_mutex;
DRV3946_SW_DRIVER drv;
DRV3946_SW_DRIVER drv_brakes;
volatile STEERING_CONTROL_DATA pid_steering_angle_data; // Volatile to make sure the Ozone debugger can read/write this

// Private variables
static const struct gpio_dt_spec steering_enable_valve_in =
    GPIO_DT_SPEC_GET(DT_PATH(gpios, steering_enable_valve_in), gpios);

// Forward declarations of private functions
void drv_update(DRV3946_SW_DRIVER *drv);
void steering_control_built_in_test(DRV3946_SW_DRIVER *drv, PID_CONTROLLER *p, volatile STEERING_CONTROL_DATA *io);

void hh_steering_control_update(DRV3946_SW_DRIVER *drv, PID_CONTROLLER *p, volatile STEERING_CONTROL_DATA *io) {
  // k_mutex_lock(&tractor_state_mutex, K_MSEC(1));
  io->current_angle = tractor_state.wheel_angle;
  // k_mutex_unlock(&tractor_state_mutex);

  float error = io->desired_angle - io->current_angle;

  io->control_signal = pid_execute(&new_steering_control_pid, error);

  if (io->control_signal < 0) // The steering angle is too far to the right
  {
    // Change drv3946 registers in this order:
    //  Make sure the side that's supposed to be ON is set to the right value.
    //  Now switch one side OFF and the other side ON.
    //  Now set the current in the OFF side to be low incase it's somehow switched on in the future.

    // Now switch Ch1 all the way off.
    // Ch2 is either already on (and unchanged by this command)
    // or was just switched on w/ a low hold_current value of about 0.375 Amps.
    DO_ONCE(LOG_INF("Steer one way."));
    drv3946_set_ch2_current(drv, -1 * io->control_signal); // flip to positive
    drv3946_set_ch1_ch2(drv, 0, 1);
    drv3946_set_ch1_current(
        drv, 0); // NOTE: A value of '0' is not all the way turned off. That's why drv3946_set_ch1_ch2() is called.
  } else {
    DO_ONCE(LOG_INF("Steer the other way."));
    drv3946_set_ch1_current(drv, io->control_signal);
    drv3946_set_ch1_ch2(drv, 1, 0);
    drv3946_set_ch2_current(drv, 0);
  }

  steering_control_built_in_test(drv, p, io);
}

void steering_control_built_in_test(DRV3946_SW_DRIVER *drv, PID_CONTROLLER *p, volatile STEERING_CONTROL_DATA *io) {
  float error = io->desired_angle - io->current_angle;

  static int32_t wheel_angle_large_err_count = 0;

  // Monitor a condition, but rate limit the LOG_INF() calls. And don't require the condition to be present continually
  // each time it's checked.
  //  If there are some spurious readings that come up a few times, still recognize the issue.

  // If we should be steering, and the wheel angle is off by >1 degree for 90% of the time it's measured over 3 seconds.
  if ((drv->enable_operation) && (error > 1.0 || error < -1.0)) {
    wheel_angle_large_err_count++;
  } else {
    wheel_angle_large_err_count--;
  }

  if ((wheel_angle_large_err_count < 0) || !(drv->enable_operation)) {
    wheel_angle_large_err_count = 0;
  }

  if (wheel_angle_large_err_count >= 150) {
    wheel_angle_large_err_count = 0;
    // LOG_INF("Steering control not at setpoint.");
    // LOG_HEXDUMP_INF((const void *)drv, sizeof(DRV3946_SW_DRIVER), "DRV3946_SW_DRIVER: ");
    // LOG_HEXDUMP_INF((const void *)&new_steering_control_pid, sizeof(PID), "PID: ");
    // LOG_HEXDUMP_INF((const void *)io, sizeof(STEERING_CONTROL_DATA), "STEERING_CONTROL_DATA: ");
  }
}

void initialize_steering_control(void) {
  if (!drv.is_initialized) {
    drv3946_struct_init(&drv, 1);
  }

  const steering_config_t steering_control_config_default = {
      .kp = 14.0,
      .ki = 0.0,
      .kd = 0.0,
      .integral_limit = 12.0,
      .update_rate_hz = 10,
      .min_steering_valve_current = 7,
      .max_steering_valve_current = 56,
  };

  // Read steering PID config
  steering_config_t steering_control_config;
  int err = config_read_steering_config(&steering_control_config);
  if (!err) // err could be either positive or negative. only err == 0 succeeded.
  {
    LOG_INF("Success reading steering_config_t for PID.");
    new_steering_control_pid.Kp = steering_control_config_default.kp;
    new_steering_control_pid.Ki = steering_control_config_default.ki;
    new_steering_control_pid.Kd = steering_control_config_default.kd;
    new_steering_control_pid.control_signal_min = steering_control_config_default.min_steering_valve_current;
    new_steering_control_pid.control_signal_max = steering_control_config_default.max_steering_valve_current;
  } else {
    LOG_INF("Error reading steering_config_t from eeprom. Use default PID for steering.");
    new_steering_control_pid.Kp = steering_control_config_default.kp;
    new_steering_control_pid.Ki = steering_control_config_default.ki;
    new_steering_control_pid.Kd = steering_control_config_default.kd;
    new_steering_control_pid.control_signal_min = steering_control_config.min_steering_valve_current;
    new_steering_control_pid.control_signal_max = steering_control_config.max_steering_valve_current;
  }

  pid_initialize(&new_steering_control_pid, true);

  pid_steering_angle_data.control_signal = 0;
  pid_steering_angle_data.current_angle = 0;
  pid_steering_angle_data.desired_angle = 0;
}

// Note: This calls hh_steering_control_update()
void drv_update(DRV3946_SW_DRIVER *drv) {
  DRV3946_STATUS status;

  if (drv->state == ds402_SWITCH_ON_DISABLED) {
    if (!drv->enable_operation) {
      // TODO: Since this will cause DRV SPI Watchdog to trip, check that the SPI watchdog trip is reset by
      // drv3946_init().
      return;
    }

    drv3946_init(drv);
    pid_reset(&new_steering_control_pid);
  } else if (drv->state == ds402_FAULT) {
    steering_control_disable_enable_pins();

    // get_status
    uint32_t temp = drv->spi_errs;
    status = drv3946_get_status_regs_pkt(drv);
    if (temp == drv->spi_errs) {
      // No SPI Errors while getting status

      drv->status = status;

      // If the trip was caused by a SPI Watchdog Timeout,
      if (status.status1.bits.SPI_WD_W) {
        drv->state = ds402_SWITCH_ON_DISABLED;
      }
    }

    // Nanopb interface can get status information.
    // Nanopb sends FaultReset to put us back into ds402_SWITCH_ON_DISABLED state.

    // A couple of SPI errors we can try to clear automatically.
    if (drv->spi_errs_cause.data != 0) {
      if (drv->spi_errs_cause.bits.NAD_ERR) {
        drv->state = ds402_SWITCH_ON_DISABLED;
        // Clear the cause of error, so don't end up back in this condition. Do not clear the total number of SPI
        // errors.
        drv->spi_errs_cause.data = 0;
        drv3946_clear_NAD_ERR(drv);
      }

      if (drv->spi_errs_cause.bits.DEV_ERR) {
        // TODO: I don't understand exactly the cause of this error.
        // If you set the PWM frequency high (e.g. 0x1F, 20KHz) it happens every ~30 seconds to 2 minutes or so, at
        // benchtop. The error can be cleared by running through init again. When the PWM frequency is set to the lowest
        // (e.g. 0x00, 500 Hz) if the error every happens, it's infrequent enough that I haven't observed it.
        drv->state = ds402_SWITCH_ON_DISABLED;
        // Clear the cause of error, so don't end up back in this condition. Do not clear the total number of SPI
        // errors.
        drv->spi_errs_cause.data = 0;
      }
    }

    drv->state = ds402_SWITCH_ON_DISABLED; // TODO: Remove debug code.
  } else if (drv->state == ds402_OPERATION_ENABLED) {
    // This is the usual place to be while the tractor is being driven remotely.
    // pid loop
    hh_steering_control_update(drv, &pid_steering_angle, &pid_steering_angle_data);

    // get_status
    uint32_t temp = drv->spi_errs;
    status = drv3946_get_status_regs_pkt(drv);
    if (temp == drv->spi_errs) {
      // No SPI Errors while getting status
      drv->status = status;
    }
  }
}

void steering_control_thread(void) {
  LOG_INF("starting steering_control_thread");

  // steering_enable_valve
  gpio_pin_set(steering_enable_valve_in.port, steering_enable_valve_in.pin, 1);

  k_mutex_init(&drv_mutex);
  initialize_steering_control();

  LOG_INF("starting drv_update");
  while (1) {
    drv_update(&drv);

    k_msleep(10);
  }

  return;
}

void steering_control_init() {
  LOG_INF("steering_control_init");
  k_thread_name_set(steering_control_thread_id, "steering_control_thread");
  k_thread_start(steering_control_thread_id);
}

// TODO: Mutex
void steering_control_get_status(DRV3946_STATUS *out) { *out = drv.status; }

/**
 * @brief Switch steering off, so this thread does not effect the wheel angle.
 *
 * @note This function will Always switch steering off, every time it is called.
 */
void steering_control_disable_operation(void) {
  // Turn OFF autotrac hydraulic enable valve
  gpio_pin_set(steering_enable_valve_in.port, steering_enable_valve_in.pin, 0);

  // Turn off DRV3946
  drv3946_disable_operation(&drv);
}

/**
 * @brief Switch steering on, it will start steering to the setpoint for the wheel angle.
 *
 * @note This function is NOT idenpotent. If you have called it once, any subsequent call will do Nothing
 * until after steering_control_disable_operation() has been called.
 * This is deliberate to prevent inability to start up due to calling this in a loop.
 */
void steering_control_enable_operation(void) {
  if (drv.enable_operation) {
    // If we already have operation enabled, don't re-init the drv chip again.
    // If we keep running re-init of drv3946 in a loop, it won't have time to start up before the next init is called.
    // Call steering_control_disable_operation() to set enable_operation false, if you really want this function to run
    // again.
    return;
  }

  LOG_INF("steering_control_enable_operation");

  // Print tractor_variant
  if (tractor_variant == UNKNOWN) {
    LOG_INF("tractor_variant: UNKNOWN");
  } else if (tractor_variant == JD_6LH) {
    LOG_INF("tractor_variant: JD_6LH");
  } else if (tractor_variant == JD_6LHM) {
    LOG_INF("tractor_variant: JD_6LHM");
  } else if (tractor_variant == JD_6PRO) {
    LOG_INF("tractor_variant: JD_6PRO");
  } else if (tractor_variant == JD_7LH) {
    LOG_INF("tractor_variant: JD_7LH");
  } else if (tractor_variant == JD_7PRO) {
    LOG_INF("tractor_variant: JD_7PRO");
  } else if (tractor_variant == JD_8RH) {
    LOG_INF("tractor_variant: JD_8RH");
  } else {
    LOG_INF("tractor_variant: %d", tractor_variant);
  }

  // Turn ON autotrac hydraulic enable valve
  gpio_pin_set(steering_enable_valve_in.port, steering_enable_valve_in.pin, 1);

  // Turn on DRV3946
  drv3946_enable_operation(&drv);

  // Reset PID
  initialize_steering_control();
}

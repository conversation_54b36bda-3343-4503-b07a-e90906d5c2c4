#include "brake_control.h"
#include <logging/log.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/kernel.h>
LOG_MODULE_REGISTER(brake_control, CONFIG_APP_LOG_LEVEL);
const struct gpio_dt_spec left_brake_isolation_valve = GPIO_DT_SPEC_GET(DT_PATH(gpios, hydraulic_relay1), gpios);
const struct gpio_dt_spec right_brake_isolation_valve = GPIO_DT_SPEC_GET(DT_PATH(gpios, hydraulic_relay2), gpios);
const struct gpio_dt_spec left_brake_activation_valve = GPIO_DT_SPEC_GET(DT_PATH(gpios, hydraulic_relay3), gpios);
const struct gpio_dt_spec right_brake_activation_valve = GPIO_DT_SPEC_GET(DT_PATH(gpios, hydraulic_relay4), gpios);

uint8_t debug_left_brake_isolation = 0;
uint8_t debug_right_brake_isolation = 0;
uint8_t debug_left_brake_activation = 0;
uint8_t debug_right_brake_activation = 0;
uint8_t brake_left_engaged = 0;
uint8_t brake_right_engaged = 0;

void brake_control_thread() {
  LOG_INF("starting brake_control_thread");
  while (1) {
    k_msleep(10);
    if (CONTROL_IS_ENABLED) {
      // Energize both isolation valves
      gpio_pin_set_dt(&left_brake_isolation_valve, 1);
      gpio_pin_set_dt(&right_brake_isolation_valve, 1);

      if (tractor_variant == JD_6LH || tractor_variant == JD_6LHM) {
        if (command_state.brake_left_command != 0) {
          brake_left_engaged = 50;
          spoof_scv2_lever_detent_up();
        } else if (brake_left_engaged > 0) {
          brake_left_engaged -= 1;
          spoof_scv2_lever_detent_down();
        }
        if (command_state.brake_right_command != 0) {
          brake_right_engaged = 50;
          spoof_scv3_lever_detent_up();
        } else if (brake_right_engaged > 0) {
          brake_right_engaged -= 1;
          spoof_scv3_lever_detent_down();
        }
      } else if (tractor_variant == JD_6PRO) {
        if (command_state.brake_left_command != 0) {
          brake_left_engaged = 50;
          spoof_scv2_lever_detent_up();
        } else if (brake_left_engaged > 0) {
          brake_left_engaged -= 1;
          spoof_scv2_lever_detent_down();
        }
        if (command_state.brake_right_command != 0) {
          brake_right_engaged = 50;
          spoof_scv1_lever_detent_up();
        } else if (brake_right_engaged > 0) {
          brake_right_engaged -= 1;
          spoof_scv1_lever_detent_down();
        }
      }

      // if we are sending both brake commands, set the gear to neutral
      if (command_state.brake_left_command != 0 && command_state.brake_right_command != 0) {
        command_state.gear_command = GEAR_COMMAND_NEUTRAL;
        rtc_command_state.gear_command = GEAR_COMMAND_NEUTRAL;
      }
    } else {
      // If we are not in operational or stopped state, de-energize both isolation valves
      gpio_pin_set_dt(&left_brake_isolation_valve, 0);
      gpio_pin_set_dt(&right_brake_isolation_valve, 0);
    }
    /**
    if (debug_left_brake_isolation) {
      gpio_pin_set_dt(&left_brake_isolation_valve, 1);
    } else {
      gpio_pin_set_dt(&left_brake_isolation_valve, 0);
    }
    if (debug_right_brake_isolation) {
      gpio_pin_set_dt(&right_brake_isolation_valve, 1);
    } else {
      gpio_pin_set_dt(&right_brake_isolation_valve, 0);
    }
    if (debug_left_brake_activation) {
      gpio_pin_set_dt(&left_brake_activation_valve, 1);
    } else {
      gpio_pin_set_dt(&left_brake_activation_valve, 0);
    }
    if (debug_right_brake_activation) {
      gpio_pin_set_dt(&right_brake_activation_valve, 1);
    } else {
      gpio_pin_set_dt(&right_brake_activation_valve, 0);
    }
    **/
  }
}

K_THREAD_DEFINE(brake_control_thread_id, 1024, brake_control_thread, NULL, NULL, NULL, BRAKE_CONTROL_THREAD_PRIORITY, 0,
                K_TICKS_FOREVER);

int brake_control_init() {
  k_thread_name_set(brake_control_thread_id, "brake_control_thread");
  k_thread_start(brake_control_thread_id);
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&left_brake_isolation_valve, GPIO_OUTPUT_ACTIVE));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&right_brake_isolation_valve, GPIO_OUTPUT_ACTIVE));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&left_brake_activation_valve, GPIO_OUTPUT_ACTIVE));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&right_brake_activation_valve, GPIO_OUTPUT_ACTIVE));
  register_monitored_thread(brake_control_thread_id, "brake_control_thread", 100);
  return 0;
}
#pragma once
#include "eeprom.h"

typedef struct __attribute__((packed)) wheel_sensor_config_t {
  // what we have
  float center_trim_deg;
  float right_lock_deg;
  uint32_t sensor_full_left_val;
  uint32_t sensor_full_right_val;

  // what we want
  float sensor_deg_per_bit;
  uint32_t sensor_center_val;
} wheel_sensor_config_t;

typedef struct __attribute__((packed)) steering_config_t {
  float kp;                            // Proportional gain
  float ki;                            // Integral gain
  float kd;                            // Derivative gain
  float integral_limit;                // Integral windup limit
  int32_t update_rate_hz;              // Control loop update rate
  uint32_t min_steering_valve_current; // Minimum valve current
  uint32_t max_steering_valve_current; // Maximum valve current
} steering_config_t;

// Add global wheel sensor config
extern wheel_sensor_config_t wheel_sensor_config;

int config_init();
int config_read_wheel_sensor_config(wheel_sensor_config_t *config);
int config_write_wheel_sensor_config(wheel_sensor_config_t *config);
int config_read_tractor_variant(uint8_t *variant);
int config_write_tractor_variant(uint8_t variant);
int config_read_steering_config(steering_config_t *config);
int config_write_steering_config(steering_config_t *config);

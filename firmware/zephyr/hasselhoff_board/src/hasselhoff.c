#include "hasselhoff.h"
#include <logging/log.h>
#include <utils/handle_errors.h>
#include <utils/repeat_counter.h>
LOG_MODULE_REGISTER(has<PERSON><PERSON>, CONFIG_APP_LOG_LEVEL);

static const struct gpio_dt_spec contactor = GPIO_DT_SPEC_GET(DT_PATH(gpios, contactor), gpios);
static const struct gpio_dt_spec safety_mosfet = GPIO_DT_SPEC_GET(DT_PATH(gpios, safety_mosfet), gpios);
static const struct gpio_dt_spec estop1_sense = GPIO_DT_SPEC_GET(DT_PATH(gpios, estop1_sense), gpios);
static const struct gpio_dt_spec estop2_sense = GPIO_DT_SPEC_GET(DT_PATH(gpios, estop2_sense), gpios);
static const struct gpio_dt_spec estop3_sense = GPIO_DT_SPEC_GET(DT_PATH(gpios, estop3_sense), gpios);
static const struct gpio_dt_spec radar1 = GPIO_DT_SPEC_GET(DT_PATH(gpios, radar1), gpios);
static const struct gpio_dt_spec radar2 = GPIO_DT_SPEC_GET(DT_PATH(gpios, radar2), gpios);
static const struct gpio_dt_spec bumper1 = GPIO_DT_SPEC_GET(DT_PATH(gpios, bumper1), gpios);
static const struct gpio_dt_spec bumper2 = GPIO_DT_SPEC_GET(DT_PATH(gpios, bumper2), gpios);
static const struct gpio_dt_spec roof_beacon_left = GPIO_DT_SPEC_GET(DT_PATH(gpios, roof_beacon_left), gpios);
static const struct gpio_dt_spec roof_beacon_right = GPIO_DT_SPEC_GET(DT_PATH(gpios, roof_beacon_right), gpios);
static const struct gpio_dt_spec roof_fan = GPIO_DT_SPEC_GET(DT_PATH(gpios, roof_fan), gpios);
static const struct gpio_dt_spec roof_light = GPIO_DT_SPEC_GET(DT_PATH(gpios, roof_light), gpios);
static const struct gpio_dt_spec lockout_switch = GPIO_DT_SPEC_GET(DT_PATH(gpios, lockout_switch), gpios);
static const struct gpio_dt_spec led_green = GPIO_DT_SPEC_GET(DT_PATH(gpios, led_green), gpios);
static const struct gpio_dt_spec led_blue = GPIO_DT_SPEC_GET(DT_PATH(gpios, led_blue), gpios);
static const struct gpio_dt_spec led_red = GPIO_DT_SPEC_GET(DT_PATH(gpios, led_red), gpios);
static const struct gpio_dt_spec safety_sensor_led = GPIO_DT_SPEC_GET(DT_PATH(gpios, safety_sensor_led), gpios);
static const struct gpio_dt_spec control_active_led = GPIO_DT_SPEC_GET(DT_PATH(gpios, control_active_led), gpios);
static const struct gpio_dt_spec error_led = GPIO_DT_SPEC_GET(DT_PATH(gpios, error_led), gpios);
static const struct gpio_dt_spec spare_gpio_0 = GPIO_DT_SPEC_GET(DT_PATH(gpios, spare_gpio_0), gpios);
static const struct gpio_dt_spec spare_gpio_1 = GPIO_DT_SPEC_GET(DT_PATH(gpios, spare_gpio_1), gpios);
static const struct gpio_dt_spec spare_gpio_2 = GPIO_DT_SPEC_GET(DT_PATH(gpios, spare_gpio_2), gpios);
static const struct gpio_dt_spec spare_gpio_3 = GPIO_DT_SPEC_GET(DT_PATH(gpios, spare_gpio_3), gpios);
static const struct gpio_dt_spec spare_relay_1 = GPIO_DT_SPEC_GET(DT_PATH(gpios, spare_relay_1), gpios);
static const struct gpio_dt_spec spare_relay_2 = GPIO_DT_SPEC_GET(DT_PATH(gpios, spare_relay_2), gpios);
static const struct gpio_dt_spec drv3946_en1 = GPIO_DT_SPEC_GET(DT_PATH(gpios, drv3946_en1), gpios);
static const struct gpio_dt_spec drv3946_en2 = GPIO_DT_SPEC_GET(DT_PATH(gpios, drv3946_en2), gpios);
static const struct gpio_dt_spec steering_enable_valve_in =
    GPIO_DT_SPEC_GET(DT_PATH(gpios, steering_enable_valve_in), gpios);
static const struct gpio_dt_spec drv3946_brakes_en1 = GPIO_DT_SPEC_GET(DT_PATH(gpios, drv3946_brakes_en1), gpios);
static const struct gpio_dt_spec drv3946_brakes_en2 = GPIO_DT_SPEC_GET(DT_PATH(gpios, drv3946_brakes_en2), gpios);
static const struct gpio_dt_spec ignition_off = GPIO_DT_SPEC_GET(DT_PATH(gpios, ignition_off), gpios);

atomic_t hh_state = HH_STATE_DISABLED;
hh_state_flags_t hh_state_flags = {
    .operator_override_pending = 0,
    .rtc_enable_command_pending = 0,
    .rtc_disable_command_pending = 0,
};

static uint64_t estop_assert_time = 0;

bool tractor_is_stopped() {
  if (tractor_state.ground_speed > 0.1) {
    LOG_WRN("Tractor is not stopped because ground speed is greater than 0.1 mph");
    return false;
  }
  if ((tractor_state.gear != GEAR_COMMAND_PARK) && (tractor_state.gear != GEAR_COMMAND_POWER)) {
    LOG_WRN("Tractor is not stopped because gear is not PARK or POWER");
    return false;
  }
  if (!is_can_bus_active()) {
    LOG_WRN("Tractor is not stopped because CAN bus is not active");
    return false;
  }
  return true;
}

bool tractor_is_safe() {
  if (!is_can_bus_active()) {
    LOG_WRN("Tractor is not safe because CAN bus is not active");
    return false;
  }
  if (!tractor_is_stopped()) {
    LOG_WRN("Tractor is not safe because it is not stopped");
    return false;
  }
  // check transmission speed is low
  // TODO: check rpms low
  // TODO: check PTO is off
  return true;
}

void set_command_state_safe() {
  command_state.brake_left_command = 0;
  command_state.brake_right_command = 0;
  command_state.speed_command = 0;
  command_state.gear_command = GEAR_COMMAND_PARK;
  command_state.lift_command = 0x7D;
  command_state.estop_command = false;
  // command_state.steering_command = 0.0;
  rtc_command_state.brake_left_command = 0;
  rtc_command_state.brake_right_command = 0;
  rtc_command_state.speed_command = 0;
  rtc_command_state.gear_command = GEAR_COMMAND_PARK;
  rtc_command_state.lift_command = 0x7D;
  rtc_command_state.estop_command = false;
  // rtc_command_state.steering_command = 0.0;
}

void hh_set_state(hh_state_t state) {
  hh_state_t prev_state = HH_STATE;
  // Return early if already in requested state
  if (prev_state == state) {
    return;
  }

  if (state == HH_STATE_ESTOPPED) {
    atomic_set(&hh_state, state);
    estop_assert_time = k_uptime_get(); // Start timer when entering ESTOPPED state
  } else if (prev_state == HH_STATE_ESTOPPED) {
    // Only allow disabled state and only after minimum time has elapsed
    if (state == HH_STATE_DISABLED && k_uptime_get() - estop_assert_time >= 2000) {
      atomic_set(&hh_state, state);
    }
  } else if (prev_state == HH_STATE_DISABLED) {
    // Transition only allowed to Safe or E-Stop
    gpio_pin_set(safety_mosfet.port, safety_mosfet.pin, 1);
    if (tractor_is_safe() && !safety_state.lockout_switch && state >= HH_STATE_SAFE) {
      atomic_set(&hh_state, HH_STATE_SAFE);
    }
  } else if (prev_state == HH_STATE_SAFE) {
    // Only allow transition if tractor is stopped
    if (tractor_is_stopped() && tractor_is_safe()) {
      set_command_state_safe();
      // pi_controller_reset();
      if (state == HH_STATE_OPERATIONAL) {
        atomic_set(&hh_state, HH_STATE_STOPPED);
      } else {
        atomic_set(&hh_state, state);
      }
    }
  } else if (prev_state == HH_STATE_STOPPED) {
    if (state == HH_STATE_OPERATIONAL) {
      // Only allow transition to Operational if tractor is stopped
      set_command_state_safe();
      if (tractor_is_stopped() && tractor_state.transmission_speed < 100 && tractor_state.transmission_speed != 0) {
        atomic_set(&hh_state, state);
        // Set the desired wheel angle to be the current angle, whenever we transition into Operational state from
        // Stopped state.
        rtc_command_state.steering_command = tractor_state.wheel_angle;
      }
    } else if (state == HH_STATE_DISABLED) {
      // Go to SAFE first
      atomic_set(&hh_state, HH_STATE_SAFE);
    } else {
      // Always allow transition to Safe
      atomic_set(&hh_state, state);
    }
  } else if (prev_state == HH_STATE_OPERATIONAL) {
    if (state == HH_STATE_DISABLED) {
      // Go to SAFE first
      atomic_set(&hh_state, HH_STATE_SAFE);
    } else {
      // Always allow transition to Safe or Stopped
      atomic_set(&hh_state, state);
    }
  }

  // play tone at different pitch based on state
  if (prev_state == HH_STATE_ESTOPPED) {
    tone_start(NOTE_CS8, 500);
  } else if (prev_state == HH_STATE_SAFE) {
    tone_start(NOTE_A5, 500);
  } else if (prev_state == HH_STATE_STOPPED) {
    tone_start(NOTE_A4, 500);
  } else if (prev_state == HH_STATE_OPERATIONAL) {
    tone_start(NOTE_A3, 500);
  } else if (prev_state == HH_STATE_DISABLED) {
    tone_start(NOTE_C4, 500);
  }
  LOG_INF("State changed to %s", state_to_name(prev_state));
}

bool engine_is_on = false;

tractor_variant_t tractor_variant = JD_6LH;

safety_state_t safety_state = {
    .estop1 = false,
    .estop2 = false,
    .estop3 = false,
    .safety_mosfet = false,
    .lockout_switch = false,
    .safety_sensor_tripped = false,
    .safety_bypass = false,
    .radar1 = false,
    .radar2 = false,
    .bumper1 = false,
    .bumper2 = false,
};

command_state_t command_state = {
    .brake_left_command = 0,
    .brake_right_command = 0,
    .speed_command = 0,
    .gear_command = GEAR_COMMAND_PARK,
    .lift_command = 0x7D,
    .estop_command = false,
    .pto_command = 0,
    .ignition_off_command = false,
};

command_state_t rtc_command_state = {
    .brake_left_command = 0,
    .brake_right_command = 0,
    .speed_command = 0,
    .gear_command = GEAR_COMMAND_PARK,
    .lift_command = 0x7D,
    .estop_command = false,
    .pto_command = 0,
    .ignition_off_command = false,
};

void hh_thread() {
  LOG_INF("Starting hh_thread");
  while (1) {
    k_msleep(10);
    switch (HH_STATE) {
    case HH_STATE_DISABLED:
      gpio_pin_set(safety_mosfet.port, safety_mosfet.pin, 1);
      steering_control_disable_operation();
      command_state.gear_command = GEAR_COMMAND_PARK;
      hh_state_flags.operator_override_pending = 0;
      hh_state_flags.rtc_disable_command_pending = 0;
      set_command_state_safe();
      break;
    case HH_STATE_ESTOPPED:
      steering_control_disable_operation();
      gpio_pin_set(safety_mosfet.port, safety_mosfet.pin, 0);

      // Check if all estops are cleared to transition to disabled
      if (!(gpio_pin_get(estop1_sense.port, estop1_sense.pin) || gpio_pin_get(estop2_sense.port, estop2_sense.pin) ||
            gpio_pin_get(estop3_sense.port, estop3_sense.pin) || command_state.estop_command ||
            hh_state_flags.speed_limit_exceeded)) {
        hh_set_state(HH_STATE_DISABLED); // Will only transition after 500ms due to check in hh_set_state
      }
      break;
    case HH_STATE_STOPPED:
      gpio_pin_set(safety_mosfet.port, safety_mosfet.pin, 1);

      steering_control_disable_operation();
      // Send full brake command until ground speed is below 0.5 mph
      if (tractor_state.ground_speed > 0.5) {
        command_state.speed_command = 0;
        command_state.gear_command = GEAR_COMMAND_NEUTRAL;
        command_state.brake_left_command = 100;
        command_state.brake_right_command = 100;
      } else {
        command_state.brake_left_command = 0;
        command_state.brake_right_command = 0;
        command_state.speed_command = 0;
        command_state.gear_command = GEAR_COMMAND_POWER;
      }
      break;
    case HH_STATE_SAFE:
      gpio_pin_set(safety_mosfet.port, safety_mosfet.pin, 1);
      // steering_control_disable_operation();
      set_command_state_safe();
      break;
    case HH_STATE_OPERATIONAL:
      // Pass RTC commands to MITM commands
      gpio_pin_set(safety_mosfet.port, safety_mosfet.pin, 1);
      memcpy(&command_state, &rtc_command_state, sizeof(command_state_t));
      pid_steering_angle_data.desired_angle = rtc_command_state.steering_command;
      hh_state_flags.rtc_enable_command_pending = 0;
      steering_control_enable_operation();
      break;
    default:
      // steering_control_disable_operation();
      hh_set_state(HH_STATE_ESTOPPED);
      break;
    }

    // Check flags
    if (hh_state_flags.operator_override_pending) {
      hh_state_flags.operator_override_pending--;
      hh_set_state(HH_STATE_DISABLED);
    } else if (hh_state_flags.rtc_enable_command_pending) {
      hh_state_flags.rtc_enable_command_pending--;
      hh_set_state(HH_STATE_OPERATIONAL);
    } else if (hh_state_flags.rtc_disable_command_pending) {
      hh_state_flags.rtc_disable_command_pending--;
      hh_set_state(HH_STATE_DISABLED);
    }

    if (rtc_command_state.ignition_off_command) {
      // hh_set_state(HH_STATE_ESTOPPED);
      // gpio_pin_set(ignition_off.port, ignition_off.pin, 1);
    }
  }
}

// Reads safety sensors and asserts stopped state if any are tripped and not bypassed
void safety_sensor_thread() {
  const uint32_t sensor_cnt_threshold = 100;
  LOG_INF("Starting safety_sensor_thread");

  // Add static variables to track previous e-stop states
  static bool prev_estop1 = false;
  static bool prev_estop2 = false;
  static bool prev_estop3 = false;
  static bool prev_estop_command = false;

  while (1) {
    k_msleep(1);

    static uint32_t radar1_cnt = 0;
    static uint32_t radar2_cnt = 0;
    static uint32_t bumper1_cnt = 0;
    static uint32_t bumper2_cnt = 0;

    if (gpio_pin_get(radar1.port, radar1.pin)) {
      radar1_cnt++;
    } else {
      radar1_cnt = 0;
    }

    if (gpio_pin_get(radar2.port, radar2.pin)) {
      radar2_cnt++;
    } else {
      radar2_cnt = 0;
    }

    if (gpio_pin_get(bumper1.port, bumper1.pin)) {
      bumper1_cnt++;
    } else {
      bumper1_cnt = 0;
    }

    if (gpio_pin_get(bumper2.port, bumper2.pin)) {
      bumper2_cnt++;
    } else {
      bumper2_cnt = 0;
    }

    if (radar1_cnt > sensor_cnt_threshold) {
      safety_state.radar1 = true;
    } else {
      safety_state.radar1 = false;
    }
    if (radar2_cnt > sensor_cnt_threshold) {
      safety_state.radar2 = true;
    } else {
      safety_state.radar2 = false;
    }
    if (bumper1_cnt > sensor_cnt_threshold) {
      safety_state.bumper1 = true;
    } else {
      safety_state.bumper1 = false;
    }
    if (bumper2_cnt > sensor_cnt_threshold) {
      safety_state.bumper2 = true;
    } else {
      safety_state.bumper2 = false;
    }

    if ((safety_state.radar1 || safety_state.radar2 || safety_state.bumper1 || safety_state.bumper2) &&
        !safety_state.safety_bypass) {
      if (HH_STATE == HH_STATE_OPERATIONAL) {
        LOG_INF("Safety sensor tripped, going to STOPPED");
        // log safety state radar1, radar2, bumper1, bumper2, !safety bypass
        LOG_INF("Safety state: radar1: %d, radar2: %d, bumper1: %d, bumper2: %d, safety bypass: %d",
                safety_state.radar1, safety_state.radar2, safety_state.bumper1, safety_state.bumper2,
                safety_state.safety_bypass);

        hh_set_state(HH_STATE_STOPPED);
      }
      safety_state.safety_sensor_tripped = true;
    } else {
      safety_state.safety_sensor_tripped = false;
    }

    // Check all estops and only log on transitions to pressed state
    if (gpio_pin_get(estop1_sense.port, estop1_sense.pin)) {
      if (!prev_estop1) {
        LOG_WRN("E-Stop 1 pressed");
      }
      safety_state.estop1 = true;
      prev_estop1 = true;
    } else {
      safety_state.estop1 = false;
      prev_estop1 = false;
    }

    if (gpio_pin_get(estop2_sense.port, estop2_sense.pin)) {
      if (!prev_estop2) {
        LOG_WRN("E-Stop 2 pressed");
      }
      safety_state.estop2 = true;
      prev_estop2 = true;
    } else {
      safety_state.estop2 = false;
      prev_estop2 = false;
    }

    if (gpio_pin_get(estop3_sense.port, estop3_sense.pin)) {
      if (!prev_estop3) {
        LOG_WRN("E-Stop 3 pressed");
      }
      safety_state.estop3 = true;
      prev_estop3 = true;
    } else {
      safety_state.estop3 = false;
      prev_estop3 = false;
    }

    if (command_state.estop_command) {
      if (!prev_estop_command) {
        LOG_WRN("Software E-Stop commanded");
      }
      prev_estop_command = true;
    } else {
      prev_estop_command = false;
    }

    // Check lockout switch
    if (gpio_pin_get(lockout_switch.port, lockout_switch.pin)) {
      safety_state.lockout_switch = true;
      hh_state_flags.speed_limit_exceeded = false;
      if (CONTROL_IS_ENABLED) {
        OPERATOR_OVERRIDE("Lockout switch engaged");
      }
    } else {
      safety_state.lockout_switch = false;
    }

    LOG_ON_CHANGE(estop1, safety_state.estop1);
    LOG_ON_CHANGE(estop2, safety_state.estop2);
    LOG_ON_CHANGE(estop3, safety_state.estop3);
    LOG_ON_CHANGE(estop_command, command_state.estop_command);
    // Add transition to ESTOPPED state if any e-stop is pressed
    if (safety_state.estop1 || safety_state.estop2 || safety_state.estop3 || command_state.estop_command) {
      hh_set_state(HH_STATE_ESTOPPED);
    }
  }
}

void cab_box_thread() {
  LOG_INF("Starting cab_box_thread");
  // Turn on all cab box LEDs for 1 second for user check
  gpio_pin_set(safety_sensor_led.port, safety_sensor_led.pin, 1);
  gpio_pin_set(control_active_led.port, control_active_led.pin, 1);
  gpio_pin_set(error_led.port, error_led.pin, 1);
  k_msleep(1000);
  gpio_pin_set(safety_sensor_led.port, safety_sensor_led.pin, 0);
  gpio_pin_set(control_active_led.port, control_active_led.pin, 0);
  gpio_pin_set(error_led.port, error_led.pin, 0);

  uint32_t last_beacon_toggle = 0;
  bool beacon_on = false;

  uint64_t last_seat_sensor_time = k_uptime_get();
  bool seat_sensor_active = false;

  while (1) {
    k_msleep(10);

    // Set safety sensor led if any safety sensor is tripped
    if (safety_state.safety_sensor_tripped) {
      gpio_pin_set(safety_sensor_led.port, safety_sensor_led.pin, 1);
    } else {
      gpio_pin_set(safety_sensor_led.port, safety_sensor_led.pin, 0);
    }

    // Set control active led and beacons based on control state
    gpio_pin_set(control_active_led.port, control_active_led.pin, CONTROL_IS_ENABLED);

    // Control beacons based on control state
    if (CONTROL_IS_ENABLED) {
      // Check if it's time to toggle beacon
      uint64_t now = k_uptime_get();
      if (!beacon_on && (now - last_beacon_toggle >= 100)) {
        // Turn beacon back on after 100ms off
        gpio_pin_set(roof_beacon_left.port, roof_beacon_left.pin, 1);
        gpio_pin_set(roof_beacon_right.port, roof_beacon_right.pin, 1);
        beacon_on = true;
        last_beacon_toggle = now;
      } else if (beacon_on && (now - last_beacon_toggle >= 5000)) {
        // Turn beacon off every 5 seconds
        gpio_pin_set(roof_beacon_left.port, roof_beacon_left.pin, 0);
        gpio_pin_set(roof_beacon_right.port, roof_beacon_right.pin, 0);
        beacon_on = false;
        last_beacon_toggle = now;
      }
    } else {
      // Turn off beacons when control is not active
      gpio_pin_set(roof_beacon_left.port, roof_beacon_left.pin, 0);
      gpio_pin_set(roof_beacon_right.port, roof_beacon_right.pin, 0);
      beacon_on = false;
    }

    // Set error LED if there are any errors
    if (hh_state_flags.can_error || hh_state_flags.speed_limit_exceeded || hh_state_flags.software_error ||
        (!is_can_bus_active() && HH_STATE != HH_STATE_ESTOPPED)) {
      gpio_pin_set(error_led.port, error_led.pin, 1);
    } else {
      gpio_pin_set(error_led.port, error_led.pin, 0);
    }

    // Handle seat sensor timing
    uint64_t now = k_uptime_get();
    if (seat_sensor_active) {
      if (now - last_seat_sensor_time >= 2000) {                // 2 seconds elapsed
        gpio_pin_set(spare_relay_2.port, spare_relay_2.pin, 1); // Turn off seat sensor
        seat_sensor_active = false;
        last_seat_sensor_time = now;
      }
    } else {
      if (now - last_seat_sensor_time >= 3600000) {             // 1 hour elapsed (3600000 ms)
        gpio_pin_set(spare_relay_2.port, spare_relay_2.pin, 0); // Turn on seat sensor
        seat_sensor_active = true;
        last_seat_sensor_time = now;
      }
    }
  }
}

void hh_watchdog_callback(bool arm) {
  LOG_INF("Watchdog callback: arm=%d", arm);
  if (CONTROL_IS_ENABLED && !arm) {
    LOG_INF("Watchdog expired while enabled, setting SAFE state");
    hh_set_state(HH_STATE_SAFE);
  }
}

void contactor_thread() {
  static vin_state_t vin_state = kVinStateUnknown;
  LOG_INF("Starting contactor_thread");
  gpio_pin_set(contactor.port, contactor.pin, 1);
  while (1) {
    k_msleep(10);

    // Check battery voltage
    vin_state = check_vin();
    // set contactor depending on vin state
    if (vin_state == kVinStateEngineOn) {
      engine_is_on = true;
      gpio_pin_set(contactor.port, contactor.pin, 1);
      gpio_pin_set(spare_gpio_1.port, spare_gpio_1.pin, 1);
    } else {
      engine_is_on = false;
      gpio_pin_set(contactor.port, contactor.pin, 0);
      gpio_pin_set(spare_gpio_1.port, spare_gpio_1.pin, 0);
    }
  }
}

K_THREAD_DEFINE(hh_thread_id, 1024, hh_thread, NULL, NULL, NULL, HH_THREAD_PRIORITY, 0, K_TICKS_FOREVER);
K_THREAD_DEFINE(safety_sensor_thread_id, 1024, safety_sensor_thread, NULL, NULL, NULL, SAFETY_SENSOR_THREAD_PRIORITY, 0,
                K_TICKS_FOREVER);
K_THREAD_DEFINE(contactor_thread_id, 1024, contactor_thread, NULL, NULL, NULL, CONTACTOR_THREAD_PRIORITY, 0,
                K_TICKS_FOREVER);
K_THREAD_DEFINE(cab_box_thread_id, 1024, cab_box_thread, NULL, NULL, NULL, CAB_BOX_THREAD_PRIORITY, 0, K_TICKS_FOREVER);

int hh_init() {
  atomic_set(&hh_state, HH_STATE_DISABLED);

  // configure GPIOs
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&contactor, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&safety_mosfet, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&estop1_sense, GPIO_INPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&estop2_sense, GPIO_INPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&estop3_sense, GPIO_INPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&radar1, GPIO_INPUT | GPIO_PULL_DOWN));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&radar2, GPIO_INPUT | GPIO_PULL_DOWN));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&bumper1, GPIO_INPUT | GPIO_PULL_UP));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&bumper2, GPIO_INPUT | GPIO_PULL_UP));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&lockout_switch, GPIO_INPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&led_green, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&led_blue, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&led_red, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&safety_sensor_led, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&control_active_led, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&error_led, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&spare_gpio_0, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&spare_gpio_1, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&spare_gpio_2, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&spare_gpio_3, GPIO_DISCONNECTED));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&spare_relay_1, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&spare_relay_2, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&drv3946_en1, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&drv3946_en2, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&steering_enable_valve_in, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&drv3946_brakes_en1, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&drv3946_brakes_en2, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&roof_light, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&roof_fan, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&roof_beacon_left, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&roof_beacon_right, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&ignition_off, GPIO_OUTPUT | GPIO_PULL_DOWN));

  gpio_pin_set(roof_light.port, roof_light.pin, 1);
  gpio_pin_set(roof_fan.port, roof_fan.pin, 1);
  gpio_pin_set(roof_beacon_left.port, roof_beacon_left.pin, 0);
  gpio_pin_set(roof_beacon_right.port, roof_beacon_right.pin, 0);

  gpio_pin_set(spare_relay_1.port, spare_relay_1.pin, 1);
  gpio_pin_set(spare_relay_2.port, spare_relay_2.pin, 1);

  gpio_pin_set(safety_mosfet.port, safety_mosfet.pin, 1);

  gpio_pin_set(led_red.port, led_red.pin, 0);
  gpio_pin_set(led_green.port, led_green.pin, 0);
  gpio_pin_set(led_blue.port, led_blue.pin, 1);

  steering_control_enable_operation();

  watchdawg_configure(2000);
  watchdawg_set_cb(hh_watchdog_callback);

  k_thread_name_set(hh_thread_id, "hh_thread");
  k_thread_name_set(safety_sensor_thread_id, "safety_sensor_thread");
  k_thread_name_set(contactor_thread_id, "contactor_thread");
  k_thread_name_set(cab_box_thread_id, "cab_box_thread");

  k_thread_start(hh_thread_id);
  k_thread_start(safety_sensor_thread_id);
  k_thread_start(contactor_thread_id);
  k_thread_start(cab_box_thread_id);

  register_monitored_thread(hh_thread_id, "hh_thread", 100);
  register_monitored_thread(safety_sensor_thread_id, "safety_sensor_thread", 100);
  register_monitored_thread(contactor_thread_id, "contactor_thread", 100);
  register_monitored_thread(cab_box_thread_id, "cab_box_thread", 100);
  return 0;
}
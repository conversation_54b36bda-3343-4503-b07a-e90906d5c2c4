#include "buzzer.h"
#include <logging/log.h>
#include <utils/handle_errors.h>
LOG_MODULE_REGISTER(buzzer, CONFIG_APP_LOG_LEVEL);

#define TONE_PWM_DEVICE DT_ALIAS(pwm1)

#define PWM_CHANNEL 1

static const struct device *pwm_dev = DEVICE_DT_GET(TONE_PWM_DEVICE);

bool CANTINA_MODE = false;

// Timer for duration control
static struct k_timer tone_timer;
static bool tone_playing = false;

// Callback when timer expires
static void tone_timer_handler(struct k_timer *dummy) {
  if (tone_playing) {
    pwm_set(pwm_dev, PWM_CHANNEL, 0, 0, 0); // Stop the tone
    tone_playing = false;
  }
}

/**
 * @brief Generate a tone on the specified PWM output (non-blocking)
 *
 * @param frequency Frequency of the tone in Hz
 * @param duration_ms Duration of the tone in milliseconds
 *
 * If duration is 0, the tone will play indefinitely until stopped.
 * @return int 0 on success, negative errno on failure
 */
int tone_start(uint32_t frequency, uint32_t duration_ms) {
  if (!pwm_dev) {
    LOG_ERR("PWM device not found");
    return -ENODEV;
  }

  if (frequency == 0) {
    LOG_ERR("Frequency must be greater than 0");
    return -EINVAL;
  }

  // Stop any existing tone and timer
  k_timer_stop(&tone_timer);

  uint32_t period_ns = 1000000000U / frequency;
  uint32_t pulse_ns = period_ns / 2; // 50% duty cycle

  int ret = pwm_set(pwm_dev, PWM_CHANNEL, period_ns, pulse_ns, 0);
  if (ret) {
    LOG_ERR("Failed to set PWM: %d", ret);
    return ret;
  }

  tone_playing = true;

  if (duration_ms > 0) {
    k_timer_init(&tone_timer, tone_timer_handler, NULL);
    k_timer_start(&tone_timer, K_MSEC(duration_ms), K_NO_WAIT);
  }

  return 0;
}

/**
 * @brief Stop the currently playing tone
 */
void tone_stop(void) {
  k_timer_stop(&tone_timer);
  if (tone_playing) {
    pwm_set(pwm_dev, PWM_CHANNEL, 0, 0, 0);
    tone_playing = false;
  }
}

/**
 * @brief Check if a tone is currently playing
 *
 * @return true if a tone is playing, false otherwise
 */
bool tone_is_playing(void) { return tone_playing; }

// implement abs function
static inline int abs(int x) { return (x < 0) ? -x : x; }

/**
 * @brief Generate a tone on the specified PWM output
 *
 * @param frequency Frequency of the tone in Hz
 * @param duration_ms Duration of the tone in milliseconds
 *
 * If duration is 0, the tone will play indefinitely until stopped.
 */
void tone(uint32_t frequency, uint32_t duration_ms) {
  if (!pwm_dev) {
    LOG_ERR("PWM device not found");
    return;
  }

  if (frequency == 0) {
    LOG_ERR("Frequency must be greater than 0");
    return;
  }

  uint32_t period_ns = 1000000000U / frequency;
  uint32_t pulse_ns = period_ns / 2; // 50% duty cycle

  if (duration_ms > 0) {
    // Play tone for the specified duration
    int ret = pwm_set(pwm_dev, PWM_CHANNEL, period_ns, pulse_ns, 0);
    if (ret) {
      LOG_ERR("Failed to set PWM: %d", ret);
    } else {
      k_msleep(duration_ms);
      pwm_set(pwm_dev, 1, 0, 0, 0); // Stop the tone
    }
  } else {
    // Play tone indefinitely
    int ret = pwm_set(pwm_dev, PWM_CHANNEL, period_ns, pulse_ns, 0);
    if (ret) {
      LOG_ERR("Failed to set PWM: %d", ret);
    } else {
    }
  }
}

/**
 * @brief Stop the tone on the PWM output
 */
void noTone() {
  if (!pwm_dev) {
    LOG_ERR("PWM device not found");
    return;
  }

  pwm_set(pwm_dev, PWM_CHANNEL, 0, 0, 0); // Stop PWM signal
}

int melody[] = {

    // Cantina BAnd - Star wars
    // Score available at https://musescore.com/user/6795541/scores/1606876
    NOTE_B4,  -4, NOTE_E5, -4, NOTE_B4,  -4, NOTE_E5,  -4, NOTE_B4,  8,  NOTE_E5, -4, NOTE_B4, 8,  REST,     8,
    NOTE_AS4, 8,  NOTE_B4, 8,  NOTE_B4,  8,  NOTE_AS4, 8,  NOTE_B4,  8,  NOTE_A4, 8,  REST,    8,  NOTE_GS4, 8,
    NOTE_A4,  8,  NOTE_G4, 8,  NOTE_G4,  4,  NOTE_E4,  -2, NOTE_B4,  -4, NOTE_E5, -4, NOTE_B4, -4, NOTE_E5,  -4,
    NOTE_B4,  8,  NOTE_E5, -4, NOTE_B4,  8,  REST,     8,  NOTE_AS4, 8,  NOTE_B4, 8,

    NOTE_A4,  -4, NOTE_A4, -4, NOTE_GS4, 8,  NOTE_A4,  -4, NOTE_D5,  8,  NOTE_C5, -4, NOTE_B4, -4, NOTE_A4,  -4,
    NOTE_B4,  -4, NOTE_E5, -4, NOTE_B4,  -4, NOTE_E5,  -4, NOTE_B4,  8,  NOTE_E5, -4, NOTE_B4, 8,  REST,     8,
    NOTE_AS4, 8,  NOTE_B4, 8,  NOTE_D5,  4,  NOTE_D5,  -4, NOTE_B4,  8,  NOTE_A4, -4, NOTE_G4, -4, NOTE_E4,  -2,
    NOTE_E4,  2,  NOTE_G4, 2,  NOTE_B4,  2,  NOTE_D5,  2,

    NOTE_F5,  -4, NOTE_E5, -4, NOTE_AS4, 8,  NOTE_AS4, 8,  NOTE_B4,  4,  NOTE_G4, 4,

};

int notes, wholenote;

int tempo = 140;
int divider = 0, noteDuration = 0;

/**
 * @brief Recalculate the tempo based on the bus load
 *
 * @param bus_load Bus load % in range 0...100
 */
void calculate_tempo_from_bus_load(uint32_t bus_load) {
  // Calculate tempo based on bus load
  notes = sizeof(melody) / sizeof(melody[0]) / 2;
  tempo = 140 + (bus_load / 10);
  wholenote = (60000 * 2) / tempo;
}

/**
 * @brief Play stars wars cantina song on piezo buzzer
 *
 */
void play_cantina() {
  for (int thisNote = 0; thisNote < notes * 2; thisNote = thisNote + 2) {

    calculate_tempo_from_bus_load(0);
    // calculates the duration of each note
    divider = melody[thisNote + 1];
    if (divider > 0) {
      // regular note, just proceed
      noteDuration = (wholenote) / divider;
    } else if (divider < 0) {
      // dotted notes are represented with negative durations!!
      noteDuration = (wholenote) / abs(divider);
      noteDuration *= 1.5; // increases the duration in half for dotted notes
    }

    // we only play the note for 90% of the duration, leaving 10% as a pause
    tone(melody[thisNote], noteDuration * 0.9);

    // stop the waveform generation before the next note.
    noTone();
  }
}

int test_tone = 1000;
uint32_t test_period = 1000000;

void play_cantina_on_loop_thread() {
  while (1) {
    calculate_tempo_from_bus_load(0);
    // tone(test_tone, 1000);
    // uint32_t test_pulse = test_period / 2;
    // int ret = pwm_set(pwm_dev, 0, test_period, test_pulse, 0);
    if (CANTINA_MODE) {
      play_cantina();
      noTone();
    }
    k_msleep(2000);
  }
}

K_THREAD_DEFINE(cantina_thread_id, 1024, play_cantina_on_loop_thread, NULL, NULL, NULL, CANTINA_THREAD_PRIORITY, 0,
                K_TICKS_FOREVER);

void buzzer_init() {
  // setup pwm_dev
  // pwm_dev = DEVICE_DT_GET(DT_NODELABEL(pwm1));
  noTone();
  // start the thread that plays the cantina song
  k_thread_name_set(cantina_thread_id, "cantina_thread");
  k_thread_start(cantina_thread_id);
  // register_monitored_thread(cantina_thread_id, "cantina_thread", 100);
}
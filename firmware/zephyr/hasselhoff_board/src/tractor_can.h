#pragma once

#include "threads.h"
#include "watchdawg.h"
#include <device.h>
#include <devicetree.h>
#include <drivers/can.h>
#include <drivers/gpio.h>
#include <lib/tractor_can/tractor_can_messages.h>
#include <string.h>
#include <utils/handle_errors.h>
#include <zephyr/kernel.h>

typedef void (*can_handler_t)(struct zcan_frame *msg);

// CAN error flags
extern uint32_t tractor_can_tx_error;
extern uint32_t cab_can_tx_error;

int add_tractor_read_handler(can_handler_t handler);
int add_cab_mitm_handler(can_handler_t handler);
int tractor_can_init(void);
void can_loopback_test(void);
bool is_can_bus_active(void);
uint32_t send_tractor_can_frame(struct zcan_frame *frame);
bool tractor_is_stopped(void);

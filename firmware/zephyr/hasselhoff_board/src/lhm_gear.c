#include <logging/log.h>
LOG_MODULE_REGISTER(LHM_gear, CONFIG_APP_LOG_LEVEL);
#include "lhm_gear.h"
#include "tractor_can.h"
#include <utils/handle_errors.h>

void passthrough_gear_pins(void);
void set_all_pins(bool highLow);

static const struct device *const i2c_dev = DEVICE_DT_GET(DT_ALIAS(sx1509b));

void lhm_update_gear(gear_command_t state) {

  if (!CONTROL_IS_ENABLED) {
    passthrough_gear_pins();
    return;
  }

  set_all_pins(true);

  if (state == GEAR_COMMAND_PARK || state == GEAR_COMMAND_POWER) {
    gpio_pin_set_raw(i2c_dev, PARK_PIN_OUT, 0);
  }

  if (state == GEAR_COMMAND_NEUTRAL) {
    gpio_pin_set_raw(i2c_dev, NEUTRAL_PIN_OUT, 0);
  }

  if (state == GEAR_COMMAND_FORWARD) {
    gpio_pin_set_raw(i2c_dev, FORWARD_PIN_OUT, 0);
    gpio_pin_set_raw(i2c_dev, LEFT_PIN_OUT, 0);
    gpio_pin_set_raw(i2c_dev, FWD_REV_PIN_OUT, 0);
  }

  if (state == GEAR_COMMAND_REVERSE) {
    gpio_pin_set_raw(i2c_dev, REVERSE_PIN_OUT, 0);
    gpio_pin_set_raw(i2c_dev, FWD_REV_PIN_OUT, 0);
  }
}

void set_all_pins(bool highLow) {
  gpio_pin_set_raw(i2c_dev, CENTER_PIN_OUT, highLow);
  gpio_pin_set_raw(i2c_dev, PARK_PIN_OUT, highLow);
  gpio_pin_set_raw(i2c_dev, NEUTRAL_PIN_OUT, highLow);
  gpio_pin_set_raw(i2c_dev, RIGHT_PIN_OUT, highLow);
  gpio_pin_set_raw(i2c_dev, FORWARD_PIN_OUT, highLow);
  gpio_pin_set_raw(i2c_dev, LEFT_PIN_OUT, highLow);
  gpio_pin_set_raw(i2c_dev, FWD_REV_PIN_OUT, highLow);
  gpio_pin_set_raw(i2c_dev, REVERSE_PIN_OUT, highLow);
}

void passthrough_gear_pins() {
  gpio_pin_set_raw(i2c_dev, REVERSE_PIN_OUT, gpio_pin_get_raw(i2c_dev, REVERSE_PIN_IN));
  gpio_pin_set_raw(i2c_dev, FWD_REV_PIN_OUT, gpio_pin_get_raw(i2c_dev, FWD_REV_PIN_IN));
  gpio_pin_set_raw(i2c_dev, LEFT_PIN_OUT, gpio_pin_get_raw(i2c_dev, LEFT_PIN_IN));
  gpio_pin_set_raw(i2c_dev, FORWARD_PIN_OUT, gpio_pin_get_raw(i2c_dev, FORWARD_PIN_IN));
  gpio_pin_set_raw(i2c_dev, RIGHT_PIN_OUT, gpio_pin_get_raw(i2c_dev, RIGHT_PIN_IN));
  gpio_pin_set_raw(i2c_dev, NEUTRAL_PIN_OUT, gpio_pin_get_raw(i2c_dev, NEUTRAL_PIN_IN));
  gpio_pin_set_raw(i2c_dev, PARK_PIN_OUT, gpio_pin_get_raw(i2c_dev, PARK_PIN_OUT));
  gpio_pin_set_raw(i2c_dev, CENTER_PIN_OUT, gpio_pin_get_raw(i2c_dev, CENTER_PIN_IN));
}

static uint8_t last_pins = 0;

void lhm_input_polling() {
  uint8_t current_input_pins;

  while (1) {
    current_input_pins = 0;
    current_input_pins |= gpio_pin_get_raw(i2c_dev, CENTER_PIN_IN) << 7;
    current_input_pins |= gpio_pin_get_raw(i2c_dev, PARK_PIN_IN) << 6;
    current_input_pins |= gpio_pin_get_raw(i2c_dev, NEUTRAL_PIN_IN) << 5;
    current_input_pins |= gpio_pin_get_raw(i2c_dev, RIGHT_PIN_IN) << 4;
    current_input_pins |= gpio_pin_get_raw(i2c_dev, FORWARD_PIN_IN) << 3;
    current_input_pins |= gpio_pin_get_raw(i2c_dev, LEFT_PIN_IN) << 2;
    current_input_pins |= gpio_pin_get_raw(i2c_dev, FWD_REV_PIN_IN) << 1;
    current_input_pins |= gpio_pin_get_raw(i2c_dev, REVERSE_PIN_IN) << 0;

    // Compare current input state with last input state
    if (current_input_pins == last_pins && CONTROL_IS_ENABLED) {
      OPERATOR_OVERRIDE("LHM gear changed");
      passthrough_gear_pins(); // Call the passthrough function
    }

    last_pins = current_input_pins;
    k_msleep(50);
  }
}

void lhm_gear_updating() {
  while (1) {
    thread_checkin(k_current_get());
    lhm_update_gear(command_state.gear_command);
    k_msleep(50);
  }
}

K_THREAD_DEFINE(input_polling_thread_id, 1024, lhm_input_polling, NULL, NULL, NULL, LHM_THREAD_PRIORITY, 0,
                K_TICKS_FOREVER);
K_THREAD_DEFINE(gear_updating_thread_id, 1024, lhm_gear_updating, NULL, NULL, NULL, LHM_THREAD_PRIORITY, 0,
                K_TICKS_FOREVER);

int lhm_gear_init() {
  HANDLE_UNLIKELY(gpio_pin_configure(i2c_dev, CENTER_PIN_IN, GPIO_INPUT));
  HANDLE_UNLIKELY(gpio_pin_configure(i2c_dev, PARK_PIN_IN, GPIO_INPUT));
  HANDLE_UNLIKELY(gpio_pin_configure(i2c_dev, NEUTRAL_PIN_IN, GPIO_INPUT));
  HANDLE_UNLIKELY(gpio_pin_configure(i2c_dev, RIGHT_PIN_IN, GPIO_INPUT));
  HANDLE_UNLIKELY(gpio_pin_configure(i2c_dev, FORWARD_PIN_IN, GPIO_INPUT));
  HANDLE_UNLIKELY(gpio_pin_configure(i2c_dev, LEFT_PIN_IN, GPIO_INPUT));
  HANDLE_UNLIKELY(gpio_pin_configure(i2c_dev, FWD_REV_PIN_IN, GPIO_INPUT));
  HANDLE_UNLIKELY(gpio_pin_configure(i2c_dev, REVERSE_PIN_IN, GPIO_INPUT));

  HANDLE_UNLIKELY(gpio_pin_configure(i2c_dev, CENTER_PIN_OUT, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure(i2c_dev, PARK_PIN_OUT, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure(i2c_dev, NEUTRAL_PIN_OUT, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure(i2c_dev, RIGHT_PIN_OUT, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure(i2c_dev, FORWARD_PIN_OUT, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure(i2c_dev, LEFT_PIN_OUT, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure(i2c_dev, FWD_REV_PIN_OUT, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure(i2c_dev, REVERSE_PIN_OUT, GPIO_OUTPUT));

  passthrough_gear_pins();

  k_thread_name_set(input_polling_thread_id, "lhm_input_polling");
  k_thread_name_set(gear_updating_thread_id, "lhm_gear_updating");

  k_thread_start(input_polling_thread_id);
  k_thread_start(gear_updating_thread_id);

  return 0;
}

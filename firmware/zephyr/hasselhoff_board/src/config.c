#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(config, CONFIG_APP_LOG_LEVEL);

#include "config.h"
#include "eeprom.h"
#include "hasselhoff.h"

// Define the global wheel sensor config
wheel_sensor_config_t wheel_sensor_config = {0};

int config_init() {
  // Initialize the EEPROM subsystem
  int ret = eeprom_init();
  if (ret) {
    LOG_ERR("Failed to initialize EEPROM: %d", ret);
    return ret;
  }

  // Load wheel sensor config from EEPROM
  size_t size = sizeof(wheel_sensor_config_t);
  ret = eeprom_read_block(kEepromBlockTypeWheelSensorConfig, &wheel_sensor_config, &size);
  if (ret) {
    LOG_WRN("Failed to load wheel sensor config from EEPROM: %d", ret);
  }

  return 0;
}

int config_read_tractor_variant(uint8_t *variant) {
  if (!variant) {
    return -EINVAL;
  }

  size_t size = sizeof(uint8_t);
  return eeprom_read_block(kEepromBlockTypeTractorVariant, variant, &size);
}

int config_write_tractor_variant(uint8_t variant) {
  // Validate variant value
  if (variant > JD_8RH) {
    return -EINVAL;
  }

  return eeprom_write_block(kEepromBlockTypeTractorVariant, &variant, sizeof(variant));
}

int config_read_wheel_sensor_config(wheel_sensor_config_t *config) {
  if (!config) {
    return -EINVAL;
  }

  size_t size = sizeof(wheel_sensor_config_t);
  return eeprom_read_block(kEepromBlockTypeWheelSensorConfig, config, &size);
}

int config_write_wheel_sensor_config(wheel_sensor_config_t *config) {
  if (!config) {
    return -EINVAL;
  }

  // Update the global config
  memcpy(&wheel_sensor_config, config, sizeof(wheel_sensor_config_t));

  return eeprom_write_block(kEepromBlockTypeWheelSensorConfig, config, sizeof(wheel_sensor_config_t));
}

int config_read_steering_config(steering_config_t *config) {
  if (!config) {
    return -EINVAL;
  }

  size_t size = sizeof(steering_config_t);
  return eeprom_read_block(kEepromBlockTypeSteeringConfig, config, &size);
}

int config_write_steering_config(steering_config_t *config) {
  if (!config) {
    return -EINVAL;
  }

  return eeprom_write_block(kEepromBlockTypeSteeringConfig, config, sizeof(steering_config_t));
}

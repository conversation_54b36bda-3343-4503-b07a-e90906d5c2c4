#include "speed_control.h"
#include "hasselhoff.h"
#include <logging/log.h>
LOG_MODULE_REGISTER(speed_control, CONFIG_APP_LOG_LEVEL);

void speed_control_thread() {
  LOG_INF("starting speed_control_thread");
  while (1) {
    thread_checkin(k_current_get());
    k_msleep(50);
    if (k_mutex_lock(&mitm_state_mutex, K_MSEC(8)) != 0) {
      LOG_ERR("Failed to acquire mitm_state_mutex in speed control");
      continue;
    }
    if (!CONTROL_IS_ENABLED || mitm_state.speed_wheel_rtc_ticks || !tractor_state.transmission_speed_rx_flag ||
        tractor_state.transmission_speed_stale_data_flag) {
      k_mutex_unlock(&mitm_state_mutex);
      continue;
    }

    if (tractor_state.gear == GEAR_COMMAND_REVERSE) {
      tractor_state.transmission_speed = tractor_state.r1_setpoint;
    } else {
      tractor_state.transmission_speed = tractor_state.f1_setpoint;
    }

    int delta_ticks = get_delta_ticks(tractor_state.transmission_speed, command_state.speed_command);

    if (tractor_variant == JD_6PRO || tractor_variant == JD_7PRO) {
      if (delta_ticks > 0) {
        mitm_state.speed_wheel_rtc_ticks = 1;
      } else if (delta_ticks < -3) {
        mitm_state.speed_wheel_rtc_ticks = -3;
      } else {
        mitm_state.speed_wheel_rtc_ticks = delta_ticks;
      }
    } else {
      mitm_state.speed_wheel_rtc_ticks = delta_ticks;
    }
    // LOG_INF("Current speed: %d, Target speed: %d, Delta ticks: %d", transmission_speed,
    //   command_state.speed_command, mitm_state.speed_wheel_rtc_ticks);
    k_mutex_unlock(&mitm_state_mutex);
  }
}

K_THREAD_DEFINE(speed_tid, 1024, speed_control_thread, NULL, NULL, NULL, SPEED_CONTROL_THREAD_PRIORITY, 0,
                K_TICKS_FOREVER);
int speed_control_init() {
  k_thread_name_set(speed_tid, "speed_control_thread");
  k_thread_start(speed_tid);
  register_monitored_thread(speed_tid, "speed_control_thread", 200);
  return 0;
}
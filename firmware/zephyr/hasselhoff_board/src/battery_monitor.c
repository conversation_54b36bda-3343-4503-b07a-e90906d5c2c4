#include "battery_monitor.h"
#include <logging/log.h>
LOG_MODULE_REGISTER(battery_monitor, CONFIG_APP_LOG_LEVEL);

static const struct adc_dt_spec vin_adc = ADC_DT_SPEC_GET_BY_NAME(DT_PATH(analog_sensors), vin);

float battery_voltage = 0.0;

int check_vin() {
  static vin_state_t vin_state = kVinStateUnknown;
  static size_t vin_count = 0;

  // read voltage and convert
  uint16_t rawValue;
  struct adc_sequence seq = {
      .buffer = &rawValue,
      .buffer_size = sizeof(rawValue),
      .resolution = vin_adc.resolution,
      .channels = BIT(vin_adc.channel_id),
  };

  HANDLE_UNLIKELY(adc_read(vin_adc.dev, &seq));

  float rawMv = adc_raw_to_millivolts_dt_float(&vin_adc, rawValue);
  battery_voltage = get_divider_voltage(51100, 10000, rawMv);

  vin_state_t next_state = vin_state;

  switch (vin_state) {
  /*
   * Initial state: use the current voltage to figure out what state we're in; in theory
   * HH should always start up with engine off but for testing we need to handle engine on
   * case as well
   */
  case kVinStateUnknown:
    if (battery_voltage >= 13.0) {
      next_state = kVinStateEngineOn;
    } else {
      next_state = kVinStateEngineOff;
    }
    break;

  /*
   * If engine is on, we can transition to the "off" state if the voltage sags below 13V for
   * more than a few intervals
   */
  case kVinStateEngineOn:
    if (battery_voltage < 13.0) {
      vin_count++;
    } else {
      vin_count = 0;
    }

    // 2.5 sec at 10ms sampling rate
    if (vin_count == 250) {
      next_state = kVinStateEngineOff;
    }

    break;

  /*
   * With engine off, look for the cranking cycle (voltage sags below 10V) for at least 50ms.
   *
   * TODO: this could probably be made more accurate by ensuring the voltage rises to ~13V after
   *       the cranking cycle
   */
  case kVinStateEngineOff:
    if (battery_voltage <= 10.0) {
      vin_count++;
    } else {
      vin_count = 0;
    }

    if (vin_count == 5) {
      next_state = kVinStateEngineOn;
    }
    break;
  }

  if (next_state != vin_state) {
    LOG_INF("vin state changed: %u -> %u", vin_state, next_state);
    vin_state = next_state;

    vin_count = 0;
  }

  return vin_state;
}

int battery_monitor_init() {
  HANDLE_UNLIKELY(!device_is_ready(vin_adc.dev));
  HANDLE_UNLIKELY(adc_channel_setup_dt(&vin_adc));

  return 0;
}

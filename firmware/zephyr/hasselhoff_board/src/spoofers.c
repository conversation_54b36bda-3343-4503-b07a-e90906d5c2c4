#include "spoofers.h"
#include <logging/log.h>
LOG_MODULE_REGISTER(spoofers, LOG_LEVEL_DBG);

void spoof_scv1_lever_detent_down() {
  if (!CONTROL_IS_ENABLED)
    return;
  struct zcan_frame msg = {
      .id_type = CAN_EXTENDED_IDENTIFIER, .rtr = 0, .id = ANNOYING_SCV_ID, .fd = 0, .brs = 0, .dlc = 8};
  msg.data[0] = ANNOYING_SCV_FIRST_BYTE;
  msg.data[1] = ANNOYING_SCV1_SECOND_BYTE;
  msg.data[2] = 0x71;
  msg.data[3] = 0xFF;
  msg.data[4] = 0xFA;
  msg.data[5] = 0x00;
  msg.data[6] = 0x00;
  msg.data[7] = 0x00;
  send_tractor_can_frame(&msg);
  LOG_INF("SCV1 commanded: TX %x : %x %x %x %x %x %x %x %x", msg.id, msg.data[0], msg.data[1], msg.data[2], msg.data[3],
          msg.data[4], msg.data[5], msg.data[6], msg.data[7]);
}

void spoof_scv2_lever_detent_down() {
  if (!CONTROL_IS_ENABLED)
    return;
  struct zcan_frame msg = {
      .id_type = CAN_EXTENDED_IDENTIFIER, .rtr = 0, .id = ANNOYING_SCV_ID, .fd = 0, .brs = 0, .dlc = 8};
  msg.data[0] = ANNOYING_SCV_FIRST_BYTE;
  msg.data[1] = ANNOYING_SCV2_SECOND_BYTE;
  msg.data[2] = 0x71;
  msg.data[3] = 0xFF;
  msg.data[4] = 0xFA;
  msg.data[5] = 0x00;
  msg.data[6] = 0x00;
  msg.data[7] = 0x00;
  send_tractor_can_frame(&msg);
  LOG_INF("SCV2 commanded: TX %x : %x %x %x %x %x %x %x %x", msg.id, msg.data[0], msg.data[1], msg.data[2], msg.data[3],
          msg.data[4], msg.data[5], msg.data[6], msg.data[7]);
}

void spoof_scv3_lever_detent_down() {
  if (!CONTROL_IS_ENABLED)
    return;
  struct zcan_frame msg = {
      .id_type = CAN_EXTENDED_IDENTIFIER, .rtr = 0, .id = ANNOYING_SCV_ID, .fd = 0, .brs = 0, .dlc = 8};
  msg.data[0] = ANNOYING_SCV_FIRST_BYTE;
  msg.data[1] = ANNOYING_SCV3_SECOND_BYTE;
  msg.data[2] = 0x71;
  msg.data[3] = 0xFF;
  msg.data[4] = 0xFA;
  msg.data[5] = 0x00;
  msg.data[6] = 0x00;
  msg.data[7] = 0x00;
  send_tractor_can_frame(&msg);
  LOG_INF("SCV3 commanded: TX %x : %x %x %x %x %x %x %x %x", msg.id, msg.data[0], msg.data[1], msg.data[2], msg.data[3],
          msg.data[4], msg.data[5], msg.data[6], msg.data[7]);
}

void spoof_scv1_lever_detent_up() {
  if (!CONTROL_IS_ENABLED)
    return;
  struct zcan_frame msg = {
      .id_type = CAN_EXTENDED_IDENTIFIER, .rtr = 0, .id = ANNOYING_SCV_ID, .fd = 0, .brs = 0, .dlc = 8};
  msg.data[0] = ANNOYING_SCV_FIRST_BYTE;
  msg.data[1] = ANNOYING_SCV1_SECOND_BYTE;
  msg.data[2] = 0x71;
  msg.data[3] = 0x00;
  msg.data[4] = 0x00;
  msg.data[5] = 0x00;
  msg.data[6] = 0x00;
  msg.data[7] = 0x00;
  send_tractor_can_frame(&msg);
  LOG_INF("SCV1 commanded: TX %x : %x %x %x %x %x %x %x %x", msg.id, msg.data[0], msg.data[1], msg.data[2], msg.data[3],
          msg.data[4], msg.data[5], msg.data[6], msg.data[7]);
}

void spoof_scv2_lever_detent_up() {
  if (!CONTROL_IS_ENABLED)
    return;
  struct zcan_frame msg = {
      .id_type = CAN_EXTENDED_IDENTIFIER, .rtr = 0, .id = ANNOYING_SCV_ID, .fd = 0, .brs = 0, .dlc = 8};
  msg.data[0] = ANNOYING_SCV_FIRST_BYTE;
  msg.data[1] = ANNOYING_SCV2_SECOND_BYTE;
  msg.data[2] = 0x71;
  msg.data[3] = 0x00;
  msg.data[4] = 0x00;
  msg.data[5] = 0x00;
  msg.data[6] = 0x00;
  msg.data[7] = 0x00;
  send_tractor_can_frame(&msg);
  LOG_INF("SCV2 commanded: TX %x : %x %x %x %x %x %x %x %x", msg.id, msg.data[0], msg.data[1], msg.data[2], msg.data[3],
          msg.data[4], msg.data[5], msg.data[6], msg.data[7]);
}

void spoof_scv3_lever_detent_up() {
  if (!CONTROL_IS_ENABLED)
    return;
  struct zcan_frame msg = {
      .id_type = CAN_EXTENDED_IDENTIFIER, .rtr = 0, .id = ANNOYING_SCV_ID, .fd = 0, .brs = 0, .dlc = 8};
  msg.data[0] = ANNOYING_SCV_FIRST_BYTE;
  msg.data[1] = ANNOYING_SCV3_SECOND_BYTE;
  msg.data[2] = 0x71;
  msg.data[3] = 0x00;
  msg.data[4] = 0x00;
  msg.data[5] = 0x00;
  msg.data[6] = 0x00;
  msg.data[7] = 0x00;
  send_tractor_can_frame(&msg);
  LOG_INF("SCV3 commanded: TX %x : %x %x %x %x %x %x %x %x", msg.id, msg.data[0], msg.data[1], msg.data[2], msg.data[3],
          msg.data[4], msg.data[5], msg.data[6], msg.data[7]);
}

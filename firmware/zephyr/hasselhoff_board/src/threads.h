#pragma once

#include <zephyr/kernel.h>

#define SAFETY_MONITOR_THREAD_PRIORITY 0
#define HH_THREAD_PRIORITY 1
#define SAFETY_SENSOR_THREAD_PRIORITY 2
#define TRACTOR_CAN_THREAD_PRIORITY 3
#define CAB_CAN_THREAD_PRIORITY 3
#define CAB_BOX_THREAD_PRIORITY 4
#define BRAKE_CONTROL_THREAD_PRIORITY 5
#define SPEED_CONTROL_THREAD_PRIORITY 2
#define SCV_CONTROL_THREAD_PRIORITY 6
#define PTO_CONTROL_THREAD_PRIORITY 6
#define CONTACTOR_THREAD_PRIORITY 8
#define CAN_MONITOR_THREAD_PRIORITY 8
#define CAN_HACKS_THREAD_PRIORITY 10
#define WATCHDOG_THREAD_PRIORITY 1
#define CANTINA_THREAD_PRIORITY 10
#define LHM_THREAD_PRIORITY 10
#define STEERING_CONTROL_THREAD_PRIORITY 11

// Initialize thread monitoring system
void init_thread_monitoring(void);

// Register a thread to be monitored
int register_monitored_thread(k_tid_t thread_id, const char *name, uint32_t max_interval_ms);

// Thread checkin function
void thread_checkin(k_tid_t thread_id);

extern struct k_mutex monitor_mutex;
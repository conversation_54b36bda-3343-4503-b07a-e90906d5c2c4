#include "scv_control.h"
#include <logging/log.h>
LOG_MODULE_REGISTER(scv_control, CONFIG_APP_LOG_LEVEL);

void scv_control_thread() {
  LOG_INF("starting scv_control_thread");
  while (1) {
    k_msleep(100);
    if (CONTROL_IS_ENABLED) {
      if (command_state.brake_left_command != 0) {
        // send_scv2_command();
      }
      if (command_state.brake_right_command != 0) {
        // send_scv3_command();
      }
    }
  }
}
K_THREAD_DEFINE(scv_control_thread_id, 1024, scv_control_thread, NULL, NULL, NULL, SCV_CONTROL_THREAD_PRIORITY, 0,
                K_TICKS_FOREVER);

void scv_control_init() {
  k_thread_name_set(scv_control_thread_id, "scv_control_thread");
  k_thread_start(scv_control_thread_id);
  register_monitored_thread(scv_control_thread_id, "scv_control_thread", 200);
}
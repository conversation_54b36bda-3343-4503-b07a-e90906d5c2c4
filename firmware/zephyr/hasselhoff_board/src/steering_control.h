#ifndef STEERING_CONTROL_H
#define STEERING_CONTROL_H

// Steering Control requirements:
//     Normal operation:
//      - If the cab box switch to disable remote driving is off, then this thread should Not be driving the solenoids
//      for steering control.
//          This is already enforced by the relays K2 - K6 on the Hasselhoff (HH) Rev D board.
//      - Either Both of the two channels should be actively driven, or neither of them should be.
//          Why: don't want to try continuing steering if it's only half working. It would be better to have steering
//          control disabled than working improperly.

//      - The SPI bus is shared between two solenoid valve driver chips and one PMIC. Multiple software threads access
//      SPI.

//      - Steering control shouldn't physically injure anyone.
//      - Steering control shouldn't damage the tractor.
//          Note: It's unlikely that driving the steering valves even at the fullest they can go would damage the
//          tractor.

//      - Straight steering accuracy. If the steering is set to drive straight ahead, it shouldn't drift by more than 1
//      inch while driving over [TBD] distance and moving at [TBD] miles per hour (MPH) +- [TBD speed tolerance].
//      - Turning angle accuracy. TBD.

//      - The Jetson will provide the <PERSON><PERSON><PERSON> with the steering angle setpoint via nanopb interface.
//          The steering control thread should just move the steering to match that angle.
//          Steering control does not worry about e.g. the current speed of the tractor, or how quickly the measured
//          steering angle reaches the setpoint.
//              The <PERSON>on handles those things.

//      - Availability: The steering should continue working during 24/7 operation (three 8-hr shifts) for at least 46
//      days.
//          The tractor should be capable of staying in the field during this time.

//      Bringup / Maint. / Configuration:
//      - Any per-tractor calibration needs to be capable of being done in the field w/o a software engineer physically
//      present.
//      - If the tractor has stopped due to a steering-related fault, Carbon Robotics needs a way to remotely know what
//      the fault is.
//      - The per-tractor Configuration values must be stored in the Hasselhoff nonvolatile memory (e.g. EEPROM).
//          SOME settings are in the robot's config service. Changes to those settings must get communicated to the HH
//          board and EEPROM will be updated.
//          - Consider/handle configuration read fail. Is the default value OK, else stop.
//      - The Steering Control thread does NOT need to provide documentation of all faults as strings. It should send
//      Binary data about it's status over nanopb. The Jetson will turn that binary data into human-readble strings
//      about faults.

// Steering Control external interfaces:
// - Handlers called for Hasselhoff board state changes
//
// - Nanopb interface, called by Jetson
//    - Steering control cannot go from Fault to OperationEnabled state in less than 10ms.
// - CAN bus provides the current steering angle as absolute encoder ticks. (e.g. maybe values somewhere in the range 0
// to 2000)
// - SPI to DRV3946 chip
// - 100 MHz clock counter register is read by PID control
// - Emulated eeprom for reading configuration PID data

#include "drv3946.h"

// Public definitions
typedef struct _STEERING_CONTROL_DATA {
  // Input
  float current_angle;

  // Setpoint
  float desired_angle;

  // Output
  int32_t control_signal;

} STEERING_CONTROL_DATA;

// Public variables
extern volatile STEERING_CONTROL_DATA pid_steering_angle_data;

extern struct k_mutex drv_mutex;
extern DRV3946_SW_DRIVER drv;
extern DRV3946_SW_DRIVER drv_brakes;

void steering_control_thread(void);

void steering_control_init(void);

void steering_control_get_status(DRV3946_STATUS *out);

void steering_control_disable_operation(void);
void steering_control_enable_operation(void);

#endif // STEERING_CONTROL_H

/*

When control is disabled, stop.
Check Enabled in the global stop.
Query the Estop. Query Enable

tbd steering control stopped state callback
in main, register callback, same way as CAN callbacks.
    Also registered when config service is set


*/
#pragma once

#include <zephyr/kernel.h>
#include <zephyr/sys/atomic.h>

extern struct k_mutex mitm_state_mutex;

// When locking multiple mutexs, to prevent deadlock, always lock in the same order
// If you lock in the order A, B, C. Unlock ordering is C, B, A
// Tractor is A, Cab is B, Mitm is C

typedef enum gear_command_t {
  GEAR_COMMAND_PARK = 0,
  GEAR_COMMAND_REVERSE = 1,
  GEAR_COMMAND_NEUTRAL = 2,
  GEAR_COMMAND_FORWARD = 3,
  GEAR_COMMAND_POWER = 4,
} gear_command_t;

// Canonical state of in-cab HMI
typedef struct cab_state_t {
  uint32_t gear;
  uint32_t speed_wheel_pos;
  uint32_t speed_wheel_neg;
  uint32_t speed_lever;
  uint32_t speed_lever_s1;
  uint32_t lift;
  uint32_t scv1_lever;
  uint32_t scv2_lever;
  uint32_t scv3_lever;
  uint32_t scv4_lever;
  uint32_t speed_lever_f1_f2_value;
} cab_state_t;

// Canonical state of tractor
typedef struct tractor_state_t {
  gear_command_t gear; // symbolic (uses enum pattern)
  float ground_speed;
  float wheel_angle;
  float hitch_lift_percentage;
  uint32_t transmission_speed;
  int transmission_speed_rx_flag;
  int transmission_speed_stale_data_flag;
  bool brake_pedal_pressed; // 0 is no brake, 1 is brake
  uint32_t f1_setpoint;
  uint32_t f2_setpoint;
  uint32_t r1_setpoint;
  uint32_t r2_setpoint;
  uint32_t rpms;
  uint8_t fuel_level;
} tractor_state_t;

typedef struct mitm_state_t {
  uint32_t gear;
  uint32_t speed_wheel_pos;
  uint32_t speed_wheel_neg;
  int speed_wheel_rtc_ticks;
  uint32_t speed_lever;
  uint32_t speed_lever_s1;
  uint32_t speed_lever_f1_f2_value;
  uint32_t hitch_lift_lever;
  uint32_t scv1_lever;
  uint32_t scv2_lever;
  uint32_t scv3_lever;
  uint32_t scv4_lever;
} mitm_state_t;

extern tractor_state_t tractor_state;
extern cab_state_t cab_state;
extern mitm_state_t mitm_state;
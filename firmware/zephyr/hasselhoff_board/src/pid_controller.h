#pragma once
#include <stdbool.h>
#include <stdint.h>

typedef struct _PID_CONTROLLER_CONFIG {
  float kp;
  float ki;
  float kd;
  float integral_limit;

  int32_t control_signal_max; // the DRV3946 hold_current register is an 8-bit register between 0 to 255, the most we
                              // can set is 255.
  int32_t control_signal_min; // minimum current that we always output.

} __attribute__((packed)) PID_CONTROLLER_CONFIG;

// Note: PI Controller parameters are dependent on the processor clock speed.
// Tune new kp, ki, and integral_limit if changing clock speed.
typedef struct _PID_CONTROLLER {
  // Config
  PID_CONTROLLER_CONFIG config;

  // State
  float integral;
  float previous_error;
  uint32_t lastTime;
  uint32_t curTime;
} PID_CONTROLLER;

typedef struct {
  float A0;       /* Derived gain, A0 = Kp + Ki + Kd */
  float A1;       /* Derived gain, A1 = -Kp - 2Kd */
  float A2;       /* Derived gain, A2 = Kd */
  float state[3]; /* State array of length 3 */
  float Kp;       /* Proportional gain */
  float Ki;       /* Integral gain */
  float Kd;       /* Derivative gain */
  int32_t control_signal_max;
  int32_t control_signal_min;
} PID;

void pid_reset(PID *s);
void pid_initialize(PID *s, bool reset);
int32_t pid_execute(PID *s, float e);

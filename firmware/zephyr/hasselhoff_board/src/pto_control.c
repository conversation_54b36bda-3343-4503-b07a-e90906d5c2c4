#include "pto_control.h"
#include <logging/log.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/kernel.h>
LOG_MODULE_REGISTER(pto_control, CONFIG_APP_LOG_LEVEL);

const struct gpio_dt_spec pto_enable = GPIO_DT_SPEC_GET(DT_PATH(gpios, armrest_12v_out1), gpios);

void pto_control_thread() {
  LOG_INF("starting pto_control_thread");
  while (1) {
    k_msleep(100);
    if (CONTROL_IS_ENABLED) {
      if (command_state.pto_command != 0) {
        gpio_pin_set_dt(&pto_enable, 1);
      } else {
        gpio_pin_set_dt(&pto_enable, 0);
      }
    }
  }
}
K_THREAD_DEFINE(pto_control_thread_id, 1024, pto_control_thread, NULL, NULL, NULL, PTO_CONTROL_THREAD_PRIORITY, 0,
                K_TICKS_FOREVER);

int pto_control_init() {
  k_thread_name_set(pto_control_thread_id, "pto_control_thread");
  k_thread_start(pto_control_thread_id);
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&pto_enable, GPIO_OUTPUT_ACTIVE));
  return 0;
}
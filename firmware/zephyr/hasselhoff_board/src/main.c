#include <logging/log.h>
LOG_MODULE_REGISTER(main, CONFIG_APP_LOG_LEVEL);
extern const struct log_backend *log_backend_net_get(void);

#include "battery_monitor.h"
#include "brake_control.h"
#include "buzzer.h"
#include "cab_handlers.h"
#include "config.h"
#include "fs26.h"
#include "hasselhoff.h"
#include "lhm_gear.h"
#include "lib/smp/smp.h"
#include "pto_control.h"
#include "safety_monitor.h"
#include "scv_control.h"
#include "speed_control.h"
#include "steering_control.h"
#include "threads.h"
#include "tractor_can.h"
#include "tractor_handlers.h"
#include "udp_server.h"
#include <lib/watchdog/watchdog.h>
#include <string.h>
#include <utils/adc.h>
#include <utils/handle_errors.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/adc.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/logging/log_backend.h>
#include <zephyr/logging/log_ctrl.h>
#include <zephyr/zephyr.h>

//#include "oss_sensor.h"

static int boot() {
  LOG_INF("Hasselhoff booting");

  const struct log_backend *backend = log_backend_net_get();

  if (!log_backend_is_active(backend)) {
    if (backend->api->init != NULL) {
      backend->api->init(backend);
    }

    log_backend_activate(backend, NULL);
  }

  HANDLE_UNLIKELY(config_init());
  config_read_tractor_variant(&tractor_variant);
  LOG_INF("State changed to %s", state_to_name(HH_STATE));

  // Read wheel sensor config
  // wheel_sensor_config_t wheel_config;
  // HANDLE_UNLIKELY(config_read_wheel_sensor_config(&wheel_config));

  init_thread_monitoring(); // Initialize thread monitoring before other threads

  int ret = 0;

  ret |= safety_monitor_init();
  fs26_init();
  buzzer_init();
  ret |= hh_init();
  ret |= add_tractor_handlers();
  ret |= add_cab_handlers();
  ret |= tractor_can_init();
  ret |= speed_control_init();
  scv_control_init();
  brake_control_init();
  pto_control_init();
  ret |= battery_monitor_init();

  if (ret != 0) {
    LOG_ERR("Error during boot().");
  }

  return 0;
}

void main() {
  start_watchdog();
  start_smp_lib();
  HANDLE_CRITICAL(boot());
  LOG_INF("Starting UDP server");
  start_udp_server();
  k_msleep(5000);
  steering_control_init();
  LOG_INF("Hasselhoff finished booting");
}

#pragma once

#include "hasselhoff.h"
#include <device.h>
#include <drivers/gpio.h>
#include <drivers/i2c.h>
#include <stdint.h>
#include <sys/util.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

#define CENTER_PIN_IN 7
#define PARK_PIN_IN 6
#define NEUTRAL_PIN_IN 5
#define RIGHT_PIN_IN 4
#define FORWARD_PIN_IN 3
#define LEFT_PIN_IN 2
#define FWD_REV_PIN_IN 1
#define REVERSE_PIN_IN 0

#define CENTER_PIN_OUT 8
#define PARK_PIN_OUT 9
#define NEUTRAL_PIN_OUT 10
#define RIGHT_PIN_OUT 11
#define FORWARD_PIN_OUT 12
#define LEFT_PIN_OUT 13
#define FWD_REV_PIN_OUT 14
#define REVERSE_PIN_OUT 15

int lhm_gear_init();

#include <logging/log.h>
LOG_MODULE_REGISTER(speed_wheel, CONFIG_APP_LOG_LEVEL);
#include "speed_wheel.h"
#include <math.h>
#include <stdbool.h>
#include <stdio.h>
#include <stdlib.h>

#define TRANSMISSION_SPEED_COUNT 62

int JOHN_DEERE_TRANSMISSION_SPEED_MPH[] = {
    2,   3,   4,   5,   6,   7,   8,   9,   10,  15,  20,  25,  30,  35,  40,  45,  50,  60,  70,  80,
    90,  100, 110, 120, 130, 140, 150, 160, 170, 180, 190, 200, 210, 220, 230, 240, 250, 260, 270, 280,
    290, 300, 320, 340, 360, 380, 400, 420, 440, 460, 480, 500, 520, 540, 560, 580, 600, 620, 640, 660,
    680, 700, 720, 740, 760, 780, 800, 820, 840, 860, 880, 900, 920, 940, 960, 980, 1000};

int get_closest_transmission_speed(int speed) {
  int closest_speed = JOHN_DEERE_TRANSMISSION_SPEED_MPH[0];
  int min_diff = abs(closest_speed - speed);
  for (int i = 1; i < TRANSMISSION_SPEED_COUNT; i++) {
    int diff = abs(JOHN_DEERE_TRANSMISSION_SPEED_MPH[i] - speed);
    if (diff < min_diff) {
      closest_speed = JOHN_DEERE_TRANSMISSION_SPEED_MPH[i];
      min_diff = diff;
    }
  }
  // LOG_INF("Closest speed to %d is %d", speed, closest_speed);
  return closest_speed;
}

int get_delta_ticks(int current_transmission_speed, int target_ground_speed) {
  int new_index = -1, current_index = -1;
  int closest_target_speed = get_closest_transmission_speed(target_ground_speed);
  int closest_current_speed = get_closest_transmission_speed(current_transmission_speed);

  for (int i = 0; i < TRANSMISSION_SPEED_COUNT; i++) {
    if (closest_target_speed == JOHN_DEERE_TRANSMISSION_SPEED_MPH[i]) {
      new_index = i;
    }
    if (closest_current_speed == JOHN_DEERE_TRANSMISSION_SPEED_MPH[i]) {
      current_index = i;
    }
  }

  if (new_index == -1 || current_index == -1) {
    LOG_ERR("Transmission speed index not found.");
    return 0;
  }

  int ticks = new_index - current_index;
  if (abs(ticks) > MAX_TICKS_DELTA) {
    int direction = (new_index > current_index) ? 1 : -1;
    ticks = direction * MAX_TICKS_DELTA;
  }
  return ticks;
}

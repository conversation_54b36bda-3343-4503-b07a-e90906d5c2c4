#pragma once

#include "battery_monitor.h"
#include "buzzer.h"
#include "steering_control.h"
#include "threads.h"
#include "tractor_state.h"
#include <zephyr/kernel.h>
#include <zephyr/sys/atomic.h>

/*
 Main Hasselhoff State Machine
*/

typedef enum hh_state_t {
  HH_STATE_DISABLED = 0,
  HH_STATE_ESTOPPED = 1,
  HH_STATE_SAFE = 2,
  HH_STATE_STOPPED = 3,
  HH_STATE_OPERATIONAL = 4,
} hh_state_t;

typedef struct hh_state_flags_t {
  uint32_t operator_override_pending;
  uint32_t rtc_enable_command_pending;
  uint32_t rtc_disable_command_pending;
  uint32_t can_error;
  uint32_t software_error;
  uint32_t speed_limit_exceeded;
  uint32_t stop_timeout_exceeded;
  uint32_t tractor_can_tx_error;
  uint32_t cab_can_tx_error;
} hh_state_flags_t;

extern hh_state_flags_t hh_state_flags;
extern atomic_t hh_state;
#define HH_STATE (atomic_get(&hh_state))
#define CONTROL_IS_ENABLED                                                                                             \
  (HH_STATE == HH_STATE_OPERATIONAL || HH_STATE == HH_STATE_STOPPED || HH_STATE == HH_STATE_SAFE)
#define OPERATOR_OVERRIDE(text)                                                                                        \
  {                                                                                                                    \
    LOG_INF("Operator Override: %s", text);                                                                            \
    hh_set_state(HH_STATE_DISABLED);                                                                                   \
    hh_state_flags.operator_override_pending = 500;                                                                    \
  }

void hh_set_state(hh_state_t state);

inline static const char *state_to_name(const hh_state_t state) {
  switch (state) {
  case HH_STATE_ESTOPPED:
    return "ESTOPPED";
  case HH_STATE_SAFE:
    return "SAFE";
  case HH_STATE_STOPPED:
    return "STOPPED";
  case HH_STATE_OPERATIONAL:
    return "OPERATIONAL";
  case HH_STATE_DISABLED:
    return "DISABLED";
  default:
    return "UNKNOWN";
  }
}

/**
 * Tractor Variants
 *
 */
typedef enum tractor_variant_t {
  UNKNOWN = 0,
  JD_6LH = 1,
  JD_6LHM = 2,
  JD_6PRO = 3,
  JD_7LH = 4,
  JD_7PRO = 5,
  JD_8RH = 6,
} tractor_variant_t;

extern tractor_variant_t tractor_variant;

/**
 * Safety State
 *
 * E-Stops: 1, 2, 3
 * Safety Mosfet
 * Lockout Switch
 * Safety Sensors Tripped
 * Safety Sensors Bypassed
 * Radar1, Radar2, Bumper1, Bumper2 tripped
 *
 */
typedef struct safety_state_t {
  bool estop1;
  bool estop2;
  bool estop3;
  bool safety_mosfet;
  bool lockout_switch;
  bool safety_sensor_tripped;
  bool safety_bypass;
  bool radar1;
  bool radar2;
  bool bumper1;
  bool bumper2;
} safety_state_t;
extern safety_state_t safety_state;

/**
 * Command State
 *
 * Brake Left Command
 * Brake Right Command
 * Speed Command
 * Gear Command
 * Lift Command
 * Estop Command
 * PTO Command
 *
 */
typedef struct command_state_t {
  int32_t brake_left_command;
  int32_t brake_right_command;
  uint32_t speed_command;
  gear_command_t gear_command;
  uint32_t lift_command;
  float steering_command;
  bool estop_command;
  bool pto_command;
  bool ignition_off_command;
} command_state_t;
extern command_state_t command_state;
extern command_state_t rtc_command_state;

int hh_init(void);

bool tractor_is_stopped(void);

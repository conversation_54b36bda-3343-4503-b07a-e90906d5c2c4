#include <logging/log.h>
LOG_MODULE_REGISTER(tractor_can, CONFIG_APP_LOG_LEVEL);
#include "hasselhoff.h"
#include "tractor_can.h"
#include <kernel.h>

static const struct gpio_dt_spec can1_silent = GPIO_DT_SPEC_GET(DT_PATH(gpios, can1_silent), gpios);
static const struct gpio_dt_spec can2_silent = GPIO_DT_SPEC_GET(DT_PATH(gpios, can2_silent), gpios);

#define MAILBOX_SIZE (300)
K_MSGQ_DEFINE(tractor_mailbox, sizeof(struct zcan_frame), MAILBOX_SIZE, 4);
K_MSGQ_DEFINE(cab_mailbox, sizeof(struct zcan_frame), MAILBOX_SIZE, 4);

const struct device *tractor_can;
const struct device *cab_can;

#define MAX_HANDLERS (20)
static can_handler_t tractor_read_handlers[MAX_HANDLERS];
static int tractor_read_handler_count = 0;

static can_handler_t cab_mitm_handlers[MAX_HANDLERS];
static int cab_mitm_handler_count = 0;

int tractor_tx_cnt = 0;
int tractor_tx_err_cnt = 0;
int cab_tx_cnt = 0;
int cab_tx_err_cnt = 0;

int tractor_rx_cnt = 0;
int cab_rx_cnt = 0;

static int64_t last_tractor_rx = 0;
static int64_t last_tractor_tx = 0;
static int64_t last_cab_rx = 0;
static int64_t last_cab_tx = 0;

uint32_t tractor_can_tx_error = 0;
uint32_t cab_can_tx_error = 0;

static bool tractor_mailbox_warned = false;
static bool cab_mailbox_warned = false;

#define HANDLER_TIMEOUT_MS 50 // Maximum time a handler can run
#define HANDLER_STACK_SIZE 1024

static void handler_timeout_handler(struct k_timer *timer);
static K_TIMER_DEFINE(tractor_handler_timer, handler_timeout_handler, NULL);
static K_TIMER_DEFINE(cab_handler_timer, handler_timeout_handler, NULL);

// Add these variables to track current handler
static int current_tractor_handler = -1;
static int current_cab_handler = -1;

static void handler_timeout_handler(struct k_timer *timer) {
  if (timer == &tractor_handler_timer && current_tractor_handler >= 0) {
    LOG_ERR("Tractor handler %d timeout - killing thread", current_tractor_handler);
    // k_thread_abort(k_current_get());
  } else if (timer == &cab_handler_timer && current_cab_handler >= 0) {
    LOG_ERR("Cab handler %d timeout - killing thread", current_cab_handler);
    // k_thread_abort(k_current_get());
  }
}

void tractor_tx_cb() { return; }

void cab_tx_cb() { return; }

uint32_t send_tractor_can_frame(struct zcan_frame *frame) {
  int ret = 0;
  ret = can_send(tractor_can, frame, K_MSEC(10), tractor_tx_cb, NULL);
  if (ret == 0) {
    last_tractor_tx = k_uptime_get();
    tractor_can_tx_error = 0;
    return 0;
  } else // can_send() != 0
  {
    // Possible errors from can_send() are:
    /*
     * 0 if successful.
     * -EINVAL if an invalid parameter was passed to the function.
     * -ENETDOWN if the CAN controller is in bus-off state.
     * -EBUSY if CAN bus arbitration was lost (only applicable if automatic
     *                retransmissions are disabled).
     * -EIO if a general transmit error occurred (e.g. missing ACK if
     *              automatic retransmissions are disabled).
     * -EAGAIN on timeout.
     */
    if (ret == -EINVAL) {
      LOG_INF("send_tractor_can_frame, can_send, -EINVAL");
    } else if (ret == -ENETDOWN) {
      LOG_INF("send_tractor_can_frame, can_send, -ENETDOWN");
    } else if (ret == -EBUSY) {
      LOG_INF("send_tractor_can_frame, can_send, -EBUSY");
    } else if (ret == -EIO) {
      LOG_INF("send_tractor_can_frame, can_send, -EIO");
    } else if (ret == -EAGAIN) {
      LOG_INF("send_tractor_can_frame, can_send, -EAGAIN");
    } else // Return value is not something can_send listed.
    {
      LOG_INF("send_tractor_can_frame, can_send, OTHER, %d", ret);
    }
  }
  if (!tractor_can_tx_error) {
    LOG_INF("Failed to send tractor frame");
  }
  tractor_can_tx_error = 1;
  tractor_tx_err_cnt++;
  return 1;
}

uint32_t send_cab_can_frame(struct zcan_frame *frame) {
  int ret = 0;
  ret = can_send(cab_can, frame, K_MSEC(10), cab_tx_cb, NULL);
  if (ret == 0) {
    last_cab_tx = k_uptime_get();
    cab_can_tx_error = 0;
    return 0;
  } else // can_send() != 0
  {
    // Possible errors from can_send() are:
    /*
     * 0 if successful.
     * -EINVAL if an invalid parameter was passed to the function.
     * -ENETDOWN if the CAN controller is in bus-off state.
     * -EBUSY if CAN bus arbitration was lost (only applicable if automatic
     *                retransmissions are disabled).
     * -EIO if a general transmit error occurred (e.g. missing ACK if
     *              automatic retransmissions are disabled).
     * -EAGAIN on timeout.
     */
    if (ret == -EINVAL) {
      LOG_INF("send_cab_can_frame, can_send, -EINVAL");
    } else if (ret == -ENETDOWN) {
      LOG_INF("send_cab_can_frame, can_send, -ENETDOWN");
    } else if (ret == -EBUSY) {
      LOG_INF("send_cab_can_frame, can_send, -EBUSY");
    } else if (ret == -EIO) {
      LOG_INF("send_cab_can_frame, can_send, -EIO");
    } else if (ret == -EAGAIN) {
      LOG_INF("send_cab_can_frame, can_send, -EAGAIN");
    } else // Return value is not something can_send listed.
    {
      LOG_INF("send_cab_can_frame, can_send, OTHER, %d", ret);
    }
  }

  if (!cab_can_tx_error) {
    LOG_INF("Failed to send cab frame");
  }
  cab_can_tx_error = 1;
  cab_tx_err_cnt++;
  return 1;
}

void tractor_rx_callback(const struct device *dev, struct zcan_frame *msg, void *arg) {
  ARG_UNUSED(dev);
  if (HH_STATE != HH_STATE_ESTOPPED) {
    last_tractor_rx = k_uptime_get();
    static int32_t ret1;
    ret1 = k_msgq_put(&tractor_mailbox, msg, K_NO_WAIT);
    tractor_rx_cnt++;
    if (ret1 != 0) {
      if (!tractor_mailbox_warned) {
        LOG_ERR("Tractor mailbox full");
        tractor_mailbox_warned = true;
      }
    }
  }
}

void cab_rx_callback(const struct device *dev, struct zcan_frame *msg, void *arg) {
  ARG_UNUSED(dev);
  if (HH_STATE != HH_STATE_ESTOPPED) {
    last_cab_rx = k_uptime_get();
    static int32_t ret2;
    ret2 = k_msgq_put(&cab_mailbox, msg, K_NO_WAIT);
    cab_rx_cnt++;
    if (ret2 != 0) {
      if (!cab_mailbox_warned) {
        LOG_ERR("Cab mailbox full");
        cab_mailbox_warned = true;
      }
    }
  }
}

void tractor_can_thread() {
  LOG_INF("Starting tractor_can_thread");
  struct zcan_frame msg;
  while (1) {
    // static unsigned int debugtick1 = 0;
    // debugtick1++;

    // static int32_t debugTime1 = 0;
    // static int32_t debugTime1Prev = 0;
    // debugTime1 = k_uptime_get();
    // if (debugtick1 > 0)
    // {
    //   LOG_INF("tractor_can_thread time: %d", debugTime1 - debugTime1Prev);
    //   debugtick1 = 0;
    // }
    // debugTime1Prev = debugTime1;

    thread_checkin(k_current_get());
    if (k_msgq_get(&tractor_mailbox, &msg, K_MSEC(50)) == 0) {
      int debugFifoDepth1 = k_msgq_num_used_get(&tractor_mailbox);
      if (debugFifoDepth1 > 6) // We just pulled one element out of the msgq in the line above, so there was probably 1
                               // more element than this in the queue.
      {
        LOG_INF("k_msgq_num_used_get(&tractor_mailbox): %d", k_msgq_num_used_get(&tractor_mailbox));
      }
      // LOG_INF("%x", msg.id);
      for (int i = 0; i < tractor_read_handler_count; i++) {
        int64_t start_time = k_uptime_get();
        tractor_read_handlers[i](&msg);
        int64_t duration = k_uptime_get() - start_time;
        if (duration > 1) {
          LOG_WRN("Tractor handler %d took %lld ms", i, duration);
        }
      }
      if (HH_STATE != HH_STATE_ESTOPPED) {
        int64_t send_start_time = k_uptime_get();
        if (send_cab_can_frame(&msg) == 1) {
          if (!cab_can_tx_error) {
            LOG_ERR("Failed to send cab frame");
          }
          cab_tx_err_cnt++;
        } else {
          cab_tx_cnt++;
          int64_t send_duration = k_uptime_get() - send_start_time;
          if (send_duration > 5) {
            LOG_WRN("Cab frame send took %lld ms", send_duration);
          }
        }
      }
    }
  }
}

void cab_can_thread() {
  LOG_INF("Starting cab_can_thread");
  struct zcan_frame msg;
  while (1) {
    thread_checkin(k_current_get());

    // This timeout is set to K_MSEC because it looks like some of the CAN bus messages are broadcast each 10ms.
    // We ought to get a CAN message at least every 10ms.
    if (k_msgq_get(&cab_mailbox, &msg, K_MSEC(50)) == 0) {
      // LOG_INF("%x", msg.id);

      for (int i = 0; i < cab_mitm_handler_count; i++) {
        int64_t start_time = k_uptime_get();
        cab_mitm_handlers[i](&msg);
        int64_t duration = k_uptime_get() - start_time;
        if (duration > 1) {
          LOG_WRN("Cab handler %d took %lld ms", i, duration);
        }
      }
      if (HH_STATE != HH_STATE_ESTOPPED) {
        int64_t send_start_time = k_uptime_get();
        if (send_tractor_can_frame(&msg) == 1) {
          if (!tractor_can_tx_error) {
            LOG_ERR("Failed to send tractor frame");
          }
          tractor_tx_err_cnt++;
        } else {
          tractor_tx_cnt++;
          int64_t send_duration = k_uptime_get() - send_start_time;
          if (send_duration > 5) {
            LOG_WRN("Tractor frame send took %lld ms", send_duration);
          }
        }
      }
    }
  }
}

int add_tractor_read_handler(can_handler_t handler) {
  if (tractor_read_handler_count < MAX_HANDLERS) {
    tractor_read_handlers[tractor_read_handler_count++] = handler;
    return 0;
  } else {
    LOG_WRN("Tractor read handler list full");
    return -1;
  }
}

int add_cab_mitm_handler(can_handler_t handler) {
  if (cab_mitm_handler_count < MAX_HANDLERS) {
    cab_mitm_handlers[cab_mitm_handler_count++] = handler;
    return 0;
  } else {
    LOG_WRN("Cab MITM handler list full");
    return -1;
  }
}

void can_monitor_thread() {
  LOG_INF("Starting can_monitor_thread");

  while (1) {
    thread_checkin(k_current_get());
    k_msleep(100);
    const uint32_t fdcan1_psr = *((uint32_t *)0x4000A044);
    const uint32_t fdcan2_psr = *((uint32_t *)0x4000A444);

    bool can_active = is_can_bus_active();

    // Log tx counts every 10 seconds
    static int64_t last_log_time = 0;
    int64_t now = k_uptime_get();
    if (now - last_log_time >= 10000) {
      LOG_INF("CAN counts - tractor: tx=%d rx=%d errors=%d, cab: tx=%d rx=%d errors=%d", tractor_tx_cnt, tractor_rx_cnt,
              tractor_tx_err_cnt, cab_tx_cnt, cab_rx_cnt, cab_tx_err_cnt);
      last_log_time = now;
    }

    // Check and clear mailboxes if full
    if (k_msgq_num_used_get(&tractor_mailbox) == MAILBOX_SIZE || k_msgq_num_used_get(&cab_mailbox) == MAILBOX_SIZE) {
      if (!tractor_mailbox_warned && k_msgq_num_used_get(&tractor_mailbox) == MAILBOX_SIZE) {
        LOG_WRN("Tractor mailbox full - purging");
        tractor_mailbox_warned = true;
      }
      if (!cab_mailbox_warned && k_msgq_num_used_get(&cab_mailbox) == MAILBOX_SIZE) {
        LOG_WRN("Cab mailbox full - purging");
        cab_mailbox_warned = true;
      }
      /**  Quarantine this code for now. Was actually breaking recovery.
      int err = can_recover(tractor_can, K_MSEC(10));
      if (err) {
        LOG_ERR("%s failed: %d", "can_recover", err);
      }
      err = can_recover(cab_can, K_MSEC(10));
      if (err) {
        LOG_ERR("%s failed: %d", "can_recover", err);
      }
      */
      k_msgq_purge(&tractor_mailbox);
      k_msgq_purge(&cab_mailbox);
    } else if (can_active) {
      tractor_mailbox_warned = false;
      cab_mailbox_warned = false;
    }

    if (fdcan1_psr & FDCAN_PSR_BO) {
      LOG_INF("fdcan1 bus off (%08x)", fdcan1_psr);
      int err = can_recover(tractor_can, K_MSEC(10));
      if (err) {
        LOG_ERR("%s failed: %d", "can_recover", err);
      }
    }
    if (fdcan2_psr & FDCAN_PSR_BO) {
      LOG_INF("fdcan2 bus off (%08x)", fdcan2_psr);
      int err = can_recover(cab_can, K_MSEC(10));
      if (err) {
        LOG_ERR("%s failed: %d", "can_recover", err);
      }
    }
  }
}

void log_cab_handler(struct zcan_frame *msg) { LOG_INF("Cab msg id: [%u]", msg->id); }

void can_loopback_test() {
  LOG_INF("Starting can_loopback_test");
  add_cab_mitm_handler(log_cab_handler);
  // Send a CAN message w/ extended id to tractor bus
  struct zcan_frame msg = {.id_type = CAN_EXTENDED_IDENTIFIER,
                           .rtr = CAN_DATAFRAME,
                           .id = 0x12345678,
                           .dlc = 8,
                           .data = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}};
  send_tractor_can_frame(&msg);
}

bool is_can_bus_active(void) {
  int64_t now = k_uptime_get();
  int64_t timeout = 100; // 100ms timeout

  // Check if we've had recent activity on both buses
  bool tractor_active = (now - last_tractor_rx < timeout) && (now - last_tractor_tx < timeout);
  bool cab_active = (now - last_cab_rx < timeout) && (now - last_cab_tx < timeout);

  return tractor_active && cab_active;
}

K_THREAD_DEFINE(tractor_can_thread_id, 1024, tractor_can_thread, NULL, NULL, NULL, TRACTOR_CAN_THREAD_PRIORITY, 0, 0);
K_THREAD_DEFINE(cab_can_thread_id, 1024, cab_can_thread, NULL, NULL, NULL, CAB_CAN_THREAD_PRIORITY, 0, 0);
K_THREAD_DEFINE(can_monitor_thread_id, 1024, can_monitor_thread, NULL, NULL, NULL, CAN_MONITOR_THREAD_PRIORITY, 0, 0);

int tractor_can_init() {
  k_thread_name_set(tractor_can_thread_id, "tractor_can");
  k_thread_start(tractor_can_thread_id);

  k_thread_name_set(cab_can_thread_id, "cab_can");
  k_thread_start(cab_can_thread_id);

  k_thread_name_set(can_monitor_thread_id, "can_monitor");
  k_thread_start(can_monitor_thread_id);

  int filter_id;
  const struct zcan_filter everything_filter = {
      .id_type = CAN_EXTENDED_IDENTIFIER, .rtr = CAN_DATAFRAME, .id = 0x0, .rtr_mask = 0, .id_mask = 0};
  const struct zcan_filter everything_filter2 = {
      .id_type = CAN_EXTENDED_IDENTIFIER, .rtr = CAN_DATAFRAME, .id = 0x0, .rtr_mask = 0, .id_mask = 0};

  tractor_can = DEVICE_DT_GET(DT_ALIAS(tcan));
  HANDLE_UNLIKELY_BOOL(device_is_ready(tractor_can), ENODEV);

  filter_id = can_add_rx_filter(tractor_can, tractor_rx_callback, NULL, &everything_filter2);
  if (filter_id < 0) {
    LOG_ERR("Unable to attach isr [%d]", filter_id);
  }

  cab_can = DEVICE_DT_GET(DT_ALIAS(ccan));
  HANDLE_UNLIKELY_BOOL(device_is_ready(cab_can), ENODEV);
  filter_id = can_add_rx_filter(cab_can, cab_rx_callback, NULL, &everything_filter);
  if (filter_id < 0) {
    LOG_ERR("Unable to attach isr [%d]", filter_id);
  }
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&can1_silent, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&can2_silent, GPIO_OUTPUT));
  gpio_pin_set(can1_silent.port, can1_silent.pin, 0);
  gpio_pin_set(can2_silent.port, can2_silent.pin, 0);

  can_set_mode(cab_can, CAN_MODE_NORMAL);
  can_set_mode(tractor_can, CAN_MODE_NORMAL);

  register_monitored_thread(tractor_can_thread_id, "tractor_can_thread", 100);
  register_monitored_thread(cab_can_thread_id, "cab_can_thread", 100);
  register_monitored_thread(can_monitor_thread_id, "can_monitor_thread", 200);

  return 0;
}

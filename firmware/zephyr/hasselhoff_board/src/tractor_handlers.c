#include "tractor_handlers.h"
#include <logging/log.h>
LOG_MODULE_REGISTER(tractor_handlers, CONFIG_APP_LOG_LEVEL);

void handle_transmission_speed(struct zcan_frame *msg) {
  bool is_valid = false;
  if (COMPARE_MSG_ID(msg->id, LH_TRANSMISSION_INFO_ID) && msg->data[0] == LH_TRANSMISSION_INFO_FIRST_BYTE &&
      tractor_variant == JD_6LH) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, RH_TRANSMISSION_INFO_ID) && msg->data[0] == RH_TRANSMISSION_INFO_FIRST_BYTE &&
             tractor_variant == JD_8RH) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, LHM_TRANSMISSION_INFO_ID) && msg->data[0] == LHM_TRANSMISSION_INFO_FIRST_BYTE &&
             tractor_variant == JD_6LHM) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, CPRO_TRANSMISSION_INFO_ID) && msg->data[0] == CPRO_TRANSMISSION_INFO_FIRST_BYTE &&
             tractor_variant == JD_6PRO) {
    is_valid = true;
  }

  if (!is_valid) {
    return;
  }

  if (k_mutex_lock(&mitm_state_mutex, K_MSEC(8)) != 0) {
    LOG_ERR("Failed to acquire mitm_state_mutex in tractor handlers");
    return;
  }
  if (tractor_state.transmission_speed_stale_data_flag) {
    // This message potentially has stale data, trash this message and pause speed solving
    tractor_state.transmission_speed_rx_flag = 0;
    tractor_state.transmission_speed_stale_data_flag = 0;
    k_mutex_unlock(&mitm_state_mutex);
    return;
  } else {
    tractor_state.transmission_speed_rx_flag = 1;
  }

  static bool speed_init = 0;
  if (!speed_init) {
    speed_init = true;
    LOG_INF("Transmission Speed handler initialized");
  }

  if (tractor_variant == JD_6LHM) {
    tractor_state.f1_setpoint = ((msg->data[5] << 8) | msg->data[4]) / 8;
    tractor_state.r1_setpoint = tractor_state.f1_setpoint;
  } else if (tractor_variant == JD_6PRO) {
    tractor_state.f1_setpoint = ((msg->data[3] << 8) | msg->data[2]) / 8;
    tractor_state.r1_setpoint = tractor_state.f1_setpoint;
  } else {
    tractor_state.f1_setpoint = ((msg->data[2] << 8) | msg->data[1]) / 8;
    tractor_state.r1_setpoint = ((msg->data[6] << 8) | msg->data[5]) / 8;
  }

  k_mutex_unlock(&mitm_state_mutex);
}

void handle_ground_speed(struct zcan_frame *msg) {
  bool is_valid = false;
  if (COMPARE_MSG_ID(msg->id, RH_GROUND_SPEED_ID) && msg->data[0] == RH_GROUND_SPEED_FIRST_BYTE &&
      tractor_variant == JD_8RH) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, CPRO_GROUND_SPEED_ID) && msg->data[0] == CPRO_GROUND_SPEED_FIRST_BYTE &&
             tractor_variant == JD_6PRO) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, LHM_GROUND_SPEED_ID) && msg->data[0] == LHM_GROUND_SPEED_FIRST_BYTE &&
             tractor_variant == JD_6LHM) {
    is_valid = true;
  }

  if (!is_valid) {
    return;
  }

  static bool speed_init = 0;
  if (!speed_init) {
    speed_init = true;
    LOG_INF("Ground Speed handler initialized");
  }

  uint16_t raw_speed = ((uint16_t)msg->data[3]) << 8 | (msg->data[2]);
  tractor_state.ground_speed = raw_speed * 0.00245;

  // LOG_INF("Ground Speed: %d", (int) (tractor_state.ground_speed *1000));
}

void handle_ground_speed_lh(struct zcan_frame *msg) {
  bool is_valid = false;
  if (COMPARE_MSG_ID(msg->id, LH_GROUND_SPEED_ID) && msg->data[0] == LH_GROUND_SPEED_FIRST_BYTE) {
    is_valid = true;
  }

  if (!is_valid) {
    return;
  }

  static bool speed_init = 0;
  if (!speed_init) {
    speed_init = true;
    LOG_INF("Ground Speed handler initialized");
  }

  uint16_t raw_speed = ((uint16_t)msg->data[2]) << 8 | (msg->data[1]);
  tractor_state.ground_speed = raw_speed * 0.003;
}

void handle_wheel_angle(struct zcan_frame *msg) {
  bool is_valid = false;
  if (COMPARE_MSG_ID(msg->id, LHM_WHEEL_ANGLE_ID) && msg->data[0] == WHEEL_ANGLE_FIRST_BYTE &&
      tractor_variant == JD_6LHM) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, WHEEL_ANGLE_ID) && msg->data[0] == WHEEL_ANGLE_FIRST_BYTE) {
    is_valid = true;
  }

  if (!is_valid) {
    return;
  }

  static bool angle_init = 0;
  if (!angle_init) {
    angle_init = true;
    LOG_INF("Wheel Angle handler initialized");
  }

  // Calculate raw angle first
  float raw_angle = (((float)((msg->data[5] << 8) | msg->data[4])) - 32089.0) / (799.0 / 27.0);

  // Apply center trim correction
  tractor_state.wheel_angle = raw_angle - wheel_sensor_config.center_trim_deg;

  // LOG_INF("Wheel Angle: %d", (int) tractor_state.wheel_angle);
}

void handle_lift_angle(struct zcan_frame *msg) {
  bool is_valid = false;
  if (COMPARE_MSG_ID(msg->id, RH_LIFT_ANGLE_ID) && msg->data[0] == RH_LIFT_ANGLE_FIRST_BYTE &&
      tractor_variant == JD_8RH) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, CPRO_LIFT_ANGLE_ID) && msg->data[0] == CPRO_LIFT_ANGLE_FIRST_BYTE &&
             tractor_variant == JD_6PRO) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, LH_LIFT_ANGLE_ID) && msg->data[0] == LH_LIFT_ANGLE_FIRST_BYTE &&
             tractor_variant == JD_6LH) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, LHM_LIFT_ANGLE_ID) && msg->data[0] == LHM_LIFT_ANGLE_FIRST_BYTE &&
             (tractor_variant == JD_6LHM)) {
    is_valid = true;
  }

  if (!is_valid) {
    return;
  }

  static bool lift_init = 0;
  if (!lift_init) {
    lift_init = true;
    LOG_INF("Lift Angle handler initialized");
  }

  if (tractor_variant == JD_6PRO || tractor_variant == JD_6LH || tractor_variant == JD_6LHM) {
    const uint16_t lift_percent_raw = (msg->data[5] << 8) | msg->data[4];
    tractor_state.hitch_lift_percentage = (float)(lift_percent_raw / 100);
  } else {
    const uint8_t lift_percent_raw = msg->data[5];
    tractor_state.hitch_lift_percentage = MIN(MAX(((0.00711 * ((float)lift_percent_raw)) + -0.102), 0.f), 1.f) * 100.f;
  }

  // LOG_INF("Lift Angle: %d", (int)tractor_state.hitch_lift_percentage);
}

void handle_brake_pedal_pressed(struct zcan_frame *msg) {
  bool is_valid = false;
  if (COMPARE_MSG_ID(msg->id, RH_BRAKE_PEDAL_ID) && msg->data[0] == RH_BRAKE_PEDAL_FIRST_BYTE &&
      (tractor_variant == JD_8RH)) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, CPRO_BRAKE_PEDAL_ID) && msg->data[0] == CPRO_BRAKE_PEDAL_FIRST_BYTE &&
             (tractor_variant == JD_6PRO)) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, LH_BRAKE_PEDAL_ID) && msg->data[0] == LH_BRAKE_PEDAL_FIRST_BYTE &&
             (tractor_variant == JD_6LH)) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, LHM_BRAKE_PEDAL_ID) && msg->data[0] == LHM_BRAKE_PEDAL_FIRST_BYTE &&
             (tractor_variant == JD_6LHM)) {
    is_valid = true;
  }

  if (!is_valid) {
    return;
  }

  static bool brake_init = 0;
  if (!brake_init) {
    brake_init = true;
    LOG_INF("Brake Pedal handler initialized");
  }

  switch (tractor_variant) {
  case JD_6LHM:
    // Intentional Fallthrough
  case JD_6LH:
    // Intentional Fallthrough
  case JD_6PRO:
    tractor_state.brake_pedal_pressed = (msg->data[1] & 0xF0) != 0x00;
    if (tractor_state.brake_pedal_pressed) {
      OPERATOR_OVERRIDE("Brake Pedal");
    }
    break;
  case JD_8RH:
    tractor_state.brake_pedal_pressed = msg->data[5] != 0x01;
    if (tractor_state.brake_pedal_pressed) {
      OPERATOR_OVERRIDE("Brake Pedal");
    }
    break;
  default:
    return;
  }
}

void handle_tractor_gear(struct zcan_frame *msg) {
  bool is_valid = false;
  if (COMPARE_MSG_ID(msg->id, CPRO_GEAR_INFO_ID) && (msg->data[0] == CPRO_GEAR_INFO_FIRST_BYTE) &&
      tractor_variant == JD_6PRO) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, LHM_GEAR_INFO_ID) && (msg->data[0] == LHM_GEAR_INFO_FIRST_BYTE) &&
             tractor_variant == JD_6LHM) {
    is_valid = true;
  }

  if (!is_valid) {
    return;
  }

  static bool gear_init = 0;
  if (!gear_init) {
    gear_init = true;
    LOG_INF("Gear handler initialized");
  }

  if (msg->data[2] == 0x01) {
    tractor_state.gear = GEAR_COMMAND_PARK;
  } else if (msg->data[2] == 0x10) {
    tractor_state.gear = GEAR_COMMAND_NEUTRAL;
  } else if (msg->data[3] == 0x01) {
    tractor_state.gear = GEAR_COMMAND_FORWARD;
  } else if (msg->data[3] == 0x10) {
    tractor_state.gear = GEAR_COMMAND_REVERSE;
  }

  // LOG_INF("Gear: %d", tractor_state.gear);
}

void handle_lh_tractor_gear(struct zcan_frame *msg) {
  if (!COMPARE_MSG_ID(msg->id, LH_GEAR_INFO_ID) || !(msg->data[0] == LH_GEAR_INFO_FIRST_BYTE)) {
    return;
  }

  static bool gear_init = 0;
  if (!gear_init) {
    gear_init = true;
    LOG_INF("LH Gear handler initialized");
  }

  if (msg->data[2] == 0xF4) {
    tractor_state.gear = GEAR_COMMAND_PARK;
  } else if (msg->data[2] == 0xF0) {
    tractor_state.gear = GEAR_COMMAND_NEUTRAL;
  } else if (msg->data[2] == 0xF1) {
    tractor_state.gear = GEAR_COMMAND_FORWARD;
  } else if (msg->data[2] == 0xF2) {
    tractor_state.gear = GEAR_COMMAND_REVERSE;
  } else if (msg->data[2] == 0xFF) {
    tractor_state.gear = GEAR_COMMAND_POWER;
  }

  // LOG_INF("Gear: %d", tractor_state.gear);
}

void handle_fuel_sense(struct zcan_frame *msg) {
  bool is_valid = false;
  if (COMPARE_MSG_ID(msg->id, FUEL_SENSE_ID) && msg->data[0] == FUEL_SENSE_FIRST_BYTE) {
    is_valid = true;
  }

  if (!is_valid) {
    return;
  }

  static bool fuel_init = 0;
  if (!fuel_init) {
    fuel_init = true;
    LOG_INF("Fuel handler initialized");
  }

  tractor_state.fuel_level = msg->data[1];
}

/**
 * T2 Handlers:
 * Brake pedal
 * Ground speed
 * Transmission speed
 * Wheel angle
 * Lift angle
 */

int add_tractor_handlers() {
  if (tractor_variant == JD_6PRO) {
    HANDLE_CRITICAL(add_tractor_read_handler(handle_transmission_speed));  // Validated Works
    HANDLE_CRITICAL(add_tractor_read_handler(handle_tractor_gear));        // Validated works
    HANDLE_CRITICAL(add_tractor_read_handler(handle_wheel_angle));         // Validated works
    HANDLE_CRITICAL(add_tractor_read_handler(handle_brake_pedal_pressed)); // Validated works
    HANDLE_CRITICAL(add_tractor_read_handler(handle_ground_speed));        // Validated works
    HANDLE_CRITICAL(add_tractor_read_handler(handle_lift_angle));          // Validated works
    HANDLE_CRITICAL(add_tractor_read_handler(handle_fuel_sense));          // Validated Works
  } else if (tractor_variant == JD_6LH) {
    HANDLE_CRITICAL(add_tractor_read_handler(handle_transmission_speed));  // Validated Works
    HANDLE_CRITICAL(add_tractor_read_handler(handle_lh_tractor_gear));     // Validated Works
    HANDLE_CRITICAL(add_tractor_read_handler(handle_wheel_angle));         // Validated Works
    HANDLE_CRITICAL(add_tractor_read_handler(handle_ground_speed_lh));     // Validated Works
    HANDLE_CRITICAL(add_tractor_read_handler(handle_brake_pedal_pressed)); // Validated Works
    HANDLE_CRITICAL(add_tractor_read_handler(handle_lift_angle));          // Validated Works
    HANDLE_CRITICAL(add_tractor_read_handler(handle_fuel_sense));          // Validated Works
  } else if (tractor_variant == JD_6LHM) {
    HANDLE_CRITICAL(add_tractor_read_handler(handle_transmission_speed));  // Validated Works
    HANDLE_CRITICAL(add_tractor_read_handler(handle_tractor_gear));        // Validated Works
    HANDLE_CRITICAL(add_tractor_read_handler(handle_wheel_angle));         // Validated Works
    HANDLE_CRITICAL(add_tractor_read_handler(handle_ground_speed));        // Validated Works
    HANDLE_CRITICAL(add_tractor_read_handler(handle_brake_pedal_pressed)); // Validated Works
    HANDLE_CRITICAL(add_tractor_read_handler(handle_lift_angle));          // Validated Works
    HANDLE_CRITICAL(add_tractor_read_handler(handle_fuel_sense));          // Validated Works
  }
  return 0;
}
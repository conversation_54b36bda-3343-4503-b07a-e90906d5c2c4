#include "drv3946.h"
#include "drv3946_crc.h"
#include <logging/log.h>
#include <string.h>

LOG_MODULE_REGISTER(drv3946, CONFIG_APP_LOG_LEVEL);

#define HASSELHOFF_DRV3946_DEVICE_ADDRESS 1

// Private variables
static const struct gpio_dt_spec drv3946_en1 = GPIO_DT_SPEC_GET(DT_PATH(gpios, drv3946_en1), gpios);
static const struct gpio_dt_spec drv3946_en2 = GPIO_DT_SPEC_GET(DT_PATH(gpios, drv3946_en2), gpios);
static const struct gpio_dt_spec drv3946_brakes_en1 = GPIO_DT_SPEC_GET(DT_PATH(gpios, drv3946_brakes_en1), gpios);
static const struct gpio_dt_spec drv3946_brakes_en2 = GPIO_DT_SPEC_GET(DT_PATH(gpios, drv3946_brakes_en2), gpios);

// Forward declarations of private functions
static uint8_t make_header(uint8_t spi_addr, uint8_t reg_addr, uint8_t isRead);
static void make_read(uint8_t spi_addr, uint8_t address, uint8_t *txData);
static void make_write(uint8_t spi_addr, uint8_t address, uint16_t val, uint8_t *txData);
static void make_write_cmd(uint8_t spi_addr, uint8_t address, uint16_t val, uint8_t *txData);
static int drv3946_read_reg(DRV3946_SW_DRIVER *drv, uint8_t address, uint16_t *outData);

static int handleSpiReturnCode(DRV3946_SW_DRIVER *drv, uint8_t txData0, uint8_t rxData0);

static void drv3946_clear_faults(DRV3946_SW_DRIVER *drv);

static void drv3946_make_default_configuration(DRV3946_SW_DRIVER *drv);
static void drv3946_make_configuration(DRV3946_SW_DRIVER *drv);
static void drv3946_set_configa(DRV3946_SW_DRIVER *drv);
static void drv3946_set_configb(DRV3946_SW_DRIVER *drv);
static void drv3946_disable_peak_current_output(DRV3946_SW_DRIVER *drv);

// Make a 24-bit spi packet header (TX, for the MOSI line) for the drv3946
// If the packet is a READ cmd, set isRead to 1. Else if it's a write, leave as 0.
static uint8_t make_header(uint8_t spi_addr, uint8_t reg_addr, uint8_t isRead) {
  uint8_t header = 0;
  header = ((spi_addr & 0x3) << 6) | ((reg_addr & 0x1F) << 1) | (isRead & 0x1);
  return header;
}

static void make_read(uint8_t spi_addr, uint8_t address, uint8_t *txData) {
  txData[0] = make_header(spi_addr, address, 1);
  txData[1] = 0;
  txData[2] = 0;
}

static void make_write(uint8_t spi_addr, uint8_t address, uint16_t val, uint8_t *txData) {
  txData[0] = make_header(spi_addr, address, 0);
  txData[1] = (val & 0xFF00U) >> 8;
  txData[2] = (val & 0x00FFU);
}

// Note: ***ASSUME val is pre-shifted for us.***
// The lower byte of val should be zero.
static void make_write_cmd(uint8_t spi_addr, uint8_t address, uint16_t val, uint8_t *txData) {
  make_write(spi_addr, address, val, &txData[0]);
  txData[2] = CRC_CMD(txData[0], ((txData[1] << 8) | txData[2]));
}

// Read a normal register, not a CMD register.
static int drv3946_read_reg(DRV3946_SW_DRIVER *drv, uint8_t address, uint16_t *outData) {
  uint8_t txData[3];
  uint8_t rxData[3];

  make_read(drv->spi_addr, address, &txData[0]);

  int err = drv3946_tx_rx(drv, &txData[0], &rxData[0]);

  *outData = (rxData[1] << 8) | rxData[2];

  return err;
}

/**
 * @brief Check the SPI RX header
 *
 * Record any SPI errors inside the DRV3946_SW_DRIVER so they can be handled by steering control before at the start of
 * the next control loop iteration. Why: It doesn't make sense to handle failed SPI messages with retries and timeouts.
 * The retry would probably fail too, and delaying the control loop doesn't make sense. The missed read or write
 * probably won't have much effect if a similar one is done in the next loop iteration. During init, missed reads/writes
 * DO have a big effect. So we check/retry init until it succeeds.
 *
 * @return sets ds402_FAULT if SPI fail
 */
static int handleSpiReturnCode(DRV3946_SW_DRIVER *drv, uint8_t txData0, uint8_t rxData0) {
  // 0x3F: Mask out upper two MSB.
  //  If there is no MISO pullup resistor, the upper MSB on MISO may be 0b00, 0b01, or 0b11.
  //  If there IS a MISO pullup resistor, they'll be 0b11.
  // Register address bits A4 thru A0 should match, and B21 must be 0.
  if ((rxData0 & 0x3FU) == ((txData0 >> 1) & 0x1FU)) {
    if ((rxData0 & (1 << 5)) == 0) {
      return 0;
    } else {
      drv->spi_errs++;
      drv->spi_errs_cause.bits.SPI_ERR = 1;
      return -2;
    }
  }
  drv->spi_errs++;
  switch (rxData0) {
  case 0x80: // NAD_ERR (no MISO pullup)
  case 0x8F:
  case 0x9F:
  case 0xBF: // NAD_ERR (w/ MISO pullup)
    drv->state = ds402_FAULT;
    drv->spi_errs_cause.bits.NAD_ERR = 1;
    return -3;
    break;
  case 0x7F: // VDD_ERR
    // TODO: Haven't tested VDD_ERR. Might be transient and not require going to Fault state. (Next control loop could
    // correct for the failed read/write here.)
    drv->state = ds402_FAULT;
    drv->spi_errs_cause.bits.VDD_ERR = 1;
    return -1;
    break;
  case 0x00:
  case 0xFF: // DEV_ERR
    drv->state = ds402_FAULT;
    drv->spi_errs_cause.bits.DEV_ERR = 1;
    return -4;
    break;
  default:
    // spi_errs count will catch it.
    drv->spi_errs_cause.bits.DEFAULT_ERR = 1;
    return -1;
    break;
  }
}

/**
 * Only public for debug. Call drv3946_tx_rx() instead.
 *
 * @return sets ds402_FAULT if SPI fail
 */
// Note: "The nSCS pin should be pulled high inbetween each 24-bit frame transmission."
uint32_t drv3946_spi_tx_rx(uint8_t *txData, uint8_t *rxData, uint32_t len) {
  struct spi_buf tx_buf;
  tx_buf.buf = &txData[0];
  tx_buf.len = len;
  struct spi_buf rx_buf;
  rx_buf.buf = &rxData[0];
  rx_buf.len = len;

  struct spi_buf_set tx_buf_set = {.buffers = &tx_buf, .count = 1};
  struct spi_buf_set rx_buf_set = {.buffers = &rx_buf, .count = 1};

  struct spi_dt_spec spispec = SPI_DT_SPEC_GET(DT_ALIAS(drv3946),
                                               (SPI_OP_MODE_MASTER | SPI_TRANSFER_MSB | SPI_WORD_SET(8) |
                                                SPI_LINES_SINGLE /* | SPI_MODE_CPOL */ | SPI_MODE_CPHA),
                                               0);

  int err = spi_transceive_dt(&spispec, &tx_buf_set, &rx_buf_set);

  return err;
}

/**
 * @brief Send SPI to DRV3946
 *
 * Send and receive 3 bytes. (One 24-bit drv3946 SPI packet)
 *
 * @return sets ds402_FAULT if SPI fail
 *
 * @remark Note: "The nSCS pin should be pulled high inbetween each 24-bit frame transmission."
 */
int drv3946_tx_rx(DRV3946_SW_DRIVER *drv, uint8_t *txData, uint8_t *rxData) {
  drv->tx_buf.buf = &txData[0];
  drv->rx_buf.buf = &rxData[0];

  drv3946_spi_tx_rx(&txData[0], &rxData[0], 3);

  return handleSpiReturnCode(drv, txData[0], rxData[0]);
}

void drv3946_struct_init(DRV3946_SW_DRIVER *drv, uint8_t spi_addr) {
  // Zero out everything
  memset(drv, 0, sizeof(DRV3946_SW_DRIVER));

  // SPI tx/rx buffer set
  drv->tx_buf.len = 3;
  drv->rx_buf.len = 3;
  drv->tx_buf_set.buffers = &drv->tx_buf;
  drv->tx_buf_set.count = 1;
  drv->rx_buf_set.buffers = &drv->rx_buf;
  drv->rx_buf_set.count = 1;

  drv->spi_addr = spi_addr;

  drv->state = ds402_SWITCH_ON_DISABLED;

  drv->last_init_return = __LINE__;

  drv->is_initialized = true;
}

void steering_control_set_enable_pins(void) {

  // Set Enable Pins
  // Pin setting to let drv3946 receive cmds to set current over SPI.
  // One pin is high, the other is low. That way if they both get disconnected or
  // shorted to something in the same way (e.g. connector, wiring harness, or processor reboot),
  // At least one of them won't work and we won't keep moving the steering.
  gpio_pin_set(drv3946_en1.port, drv3946_en1.pin, 1);
  gpio_pin_set(drv3946_en2.port, drv3946_en2.pin, 0);
}

// Disable the drv3946 channels
void steering_control_disable_enable_pins(void) {
  gpio_pin_set(drv3946_en1.port, drv3946_en1.pin, 0);
  gpio_pin_set(drv3946_en2.port, drv3946_en2.pin, 1);
}

void brakes_set_enable_pins(void) {
  gpio_pin_set(drv3946_brakes_en1.port, drv3946_brakes_en1.pin, 1);
  gpio_pin_set(drv3946_brakes_en2.port, drv3946_brakes_en2.pin, 0);
}

void brakes_disable_enable_pins(void) {
  gpio_pin_set(drv3946_brakes_en1.port, drv3946_brakes_en1.pin, 0);
  gpio_pin_set(drv3946_brakes_en2.port, drv3946_brakes_en2.pin, 1);
}

DRV3946_STATUS drv3946_get_status_regs(void) {
  // Read the first 4 status registers from device address 01.
  static uint8_t txData[18] = {0};
  static uint8_t rxData[18] = {0};

  txData[0] = (1 << 6) | (1 << 1) | (1 << 0);
  txData[3] = (1 << 6) | (2 << 1) | (1 << 0);
  txData[6] = (1 << 6) | (3 << 1) | (1 << 0);
  txData[9] = (1 << 6) | (4 << 1) | (1 << 0);
  txData[12] = (1 << 6) | (10 << 1) | (1 << 0); // STATUS4 enum is 0xA
  txData[15] = (1 << 6) | (11 << 1) | (1 << 0);

  // Send each SPI packet individually.
  // While this is slow, we CANNOT send multiple packets in one spi_buf_set (even as multiple spi_buf)
  //  Because the drv3946 requires the Chip Select line to be raised after each 24-bits.
  drv3946_spi_tx_rx(&txData[0], &rxData[0], 3);
  drv3946_spi_tx_rx(&txData[3], &rxData[3], 3);
  drv3946_spi_tx_rx(&txData[6], &rxData[6], 3);
  drv3946_spi_tx_rx(&txData[9], &rxData[9], 3);
  drv3946_spi_tx_rx(&txData[12], &rxData[12], 3);
  drv3946_spi_tx_rx(&txData[15], &rxData[15], 3);

  DRV3946_STATUS status1;

  status1.status0.data = (rxData[1] << 8) | rxData[2];
  status1.status1.data = (rxData[4] << 8) | rxData[5];
  status1.status2.data = (rxData[7] << 8) | rxData[8];
  status1.status3.data = (rxData[10] << 8) | rxData[11];
  status1.status4.data = (rxData[13] << 8) | rxData[14];
  status1.status5.data = (rxData[16] << 8) | rxData[17];

  return status1;
}

DRV3946_STATUS drv3946_get_status_regs_pkt(DRV3946_SW_DRIVER *drv) {
  // Read the first 4 status registers from device address 01.
  static uint8_t txData[18] = {0};
  static uint8_t rxData[18] = {0};

  txData[0] = (drv->spi_addr << 6) | (1 << 1) | (1 << 0);
  txData[3] = (drv->spi_addr << 6) | (2 << 1) | (1 << 0);
  txData[6] = (drv->spi_addr << 6) | (3 << 1) | (1 << 0);
  txData[9] = (drv->spi_addr << 6) | (4 << 1) | (1 << 0);
  txData[12] = (drv->spi_addr << 6) | (10 << 1) | (1 << 0); // STATUS4 enum is 0xA
  txData[15] = (drv->spi_addr << 6) | (11 << 1) | (1 << 0);

  // Send each SPI packet individually.
  // While this is slow, we CANNOT send multiple packets in one spi_buf_set (even as multiple spi_buf)
  //  Because the drv3946 requires the Chip Select line to be raised after each 24-bits.
  drv3946_tx_rx(drv, &txData[0], &rxData[0]);
  drv3946_tx_rx(drv, &txData[3], &rxData[3]);
  drv3946_tx_rx(drv, &txData[6], &rxData[6]);
  drv3946_tx_rx(drv, &txData[9], &rxData[9]);
  drv3946_tx_rx(drv, &txData[12], &rxData[12]);
  drv3946_tx_rx(drv, &txData[15], &rxData[15]);

  DRV3946_STATUS status1;

  status1.status0.data = (rxData[1] << 8) | rxData[2];
  status1.status1.data = (rxData[4] << 8) | rxData[5];
  status1.status2.data = (rxData[7] << 8) | rxData[8];
  status1.status3.data = (rxData[10] << 8) | rxData[11];
  status1.status4.data = (rxData[13] << 8) | rxData[14];
  status1.status5.data = (rxData[16] << 8) | rxData[17];

  return status1;
}

static void drv3946_set_configa(DRV3946_SW_DRIVER *drv) {
  uint16_t *CONFIGA_u16s = &drv->config_a.data[0];

  static uint8_t CONFIGA_txData[21] = {0};
  static uint8_t CONFIGA_rxData[21] = {0};
  uint32_t i;
  uint32_t pkt_bytes_idx;
  for (i = 0; i < 7; i++) // Have to be really careful w/ these indexes. 7 Packets. 1 U16 from ConfigA_u16s. 3 bytes in
                          // the Tx packet in total.
  {
    pkt_bytes_idx = 3 * i;
    make_write(drv->spi_addr, DRV3946_REG_CONFIGA0 + i, CONFIGA_u16s[i], &CONFIGA_txData[pkt_bytes_idx]);
    drv3946_tx_rx(drv, &CONFIGA_txData[pkt_bytes_idx], &CONFIGA_rxData[pkt_bytes_idx]);
  }
}

static void drv3946_set_configb(DRV3946_SW_DRIVER *drv) {
  uint16_t *CONFIGB_u16s = &drv->config_b.data[0];

  static uint8_t CONFIGB_txData[15] = {0};
  static uint8_t CONFIGB_rxData[15] = {0};
  uint32_t i;
  uint32_t pkt_bytes_idx;
  for (i = 0; i < 5; i++) {
    pkt_bytes_idx = 3 * i;
    make_write(drv->spi_addr, DRV3946_REG_CONFIGB0 + i, CONFIGB_u16s[i], &CONFIGB_txData[pkt_bytes_idx]);
    drv3946_tx_rx(drv, &CONFIGB_txData[pkt_bytes_idx], &CONFIGB_rxData[pkt_bytes_idx]);
  }
}

static void drv3946_make_default_configuration(DRV3946_SW_DRIVER *drv) {
  // Copy of Default values for Config registers from the datasheet.
  // Default Config A
  drv->config_a.data[0] = 0xC040; // CONFIG_A0
  drv->config_a.data[1] = 0xC040;
  drv->config_a.data[2] = 0x2424;
  drv->config_a.data[3] = 0x0088;
  drv->config_a.data[4] = 0x130C;
  drv->config_a.data[5] = 0x8000;
  drv->config_a.data[6] = 0x0000; // CONFIG_A6

  drv->config_a.data[6] = 0x0005; // known good crc.

  // Default Config B
  drv->config_b.data[0] = 0x2623; // CONFIG_B0
  drv->config_b.data[1] = 0x0040;
  drv->config_b.data[2] = 0x0B0B;
  drv->config_b.data[3] = 0x8000;
  drv->config_b.data[4] = 0x0000; // CONFIG_B4

  drv->config_b.data[4] = 0x0084; // known good crc
}

// POR bit is cleared after running this.
void drv3946_clear_addr1_faults_and_reinit(DRV3946_SW_DRIVER *drv) {
  // Set the CLR_FLT and RE_INIT bits in the register CMD2 of chip with SPI address 0x01.
  // This does not Lock the configuration registers. (It writes to CMD2, not CMD1.)
  uint8_t txData[3] = {0x7C, 0xC0, 0x01};
  uint8_t rxData[3] = {0};

  drv3946_tx_rx(drv, &txData[0], &rxData[0]);
}

/**
 * Clear faults
 * Clear POR bit (necessary once after power up as part of normal boot)
 */
// Clear faults, but not Power On Reset (POR) bit
static void drv3946_clear_faults(DRV3946_SW_DRIVER *drv) {
  uint8_t txData[3] = {0};
  uint8_t rxData[3] = {0};

  make_write_cmd(drv->spi_addr, DRV3946_REG_CMD2, 0x8000, &txData[0]);
  drv3946_tx_rx(drv, &txData[0], &rxData[0]);
}

/**
 * @brief Clear DRV3946 Address Error
 *
 * Send RE_INIT_NAD command
 */
void drv3946_clear_NAD_ERR(DRV3946_SW_DRIVER *drv) {
  uint8_t txData[3] = {0};
  uint8_t rxData[3] = {0};

  // RE_INIT_NAD
  make_write_cmd(drv->spi_addr, DRV3946_REG_CMD2, 0x4000, &txData[0]);
  drv3946_tx_rx(drv, &txData[0], &rxData[0]);
}

void drv3946_set_ch1_ch2(DRV3946_SW_DRIVER *drv, uint8_t ch1_active, uint8_t ch2_active) {
  uint8_t cmdByte = (ch1_active << 4) | (ch2_active << 1);

  uint8_t txData[3] = {0};
  uint8_t rxData[3] = {0};

  make_write_cmd(drv->spi_addr, DRV3946_REG_CMD1, (cmdByte << 8), &txData[0]);
  drv3946_tx_rx(drv, &txData[0], &rxData[0]);
}

/**
 * @brief DRV3946 init
 *
 * This will clear most faults (except e.g. Address Error, or channel output failed open/short).
 *
 * After initi the DRV3946 chip is running (both channels currently off) and calls to drv3946_set_ch1_ch2() and
 * drv3946_set_ch1_current() will work.
 *
 * @remark Details of how to start up are in drv3946-q1.pdf (SLVSGSGA is TI's doc number) page 77 "Initialization
 * Setup". page 26 "Simplified Start-up Sequencing Diagram" is also useful.
 *
 * @return
 *  0 and set ds402_OPERATION_ENABLED state if successful.
 *  negative num if failed. last_init_return has most detailed err info.
 */
int drv3946_init(DRV3946_SW_DRIVER *drv) {
  // Make sure the drv is off during configuration.
  //  Otherwise, could draw high current mid-configuration due to incomplete settings.
  drv3946_set_ch1_ch2(drv, 0, 0);
  steering_control_disable_enable_pins();
  drv3946_set_ch1_current(drv, 0);
  drv3946_set_ch2_current(drv, 0);

  drv3946_make_configuration(drv);
  drv3946_set_configa(drv);
  drv3946_set_configb(drv);
  drv3946_disable_peak_current_output(drv);

  // Read Status0 and Status1. We don't need to read all the Status registers.
  DRV3946_STATUS0_REG status0;
  DRV3946_STATUS1_REG status1;

  int err = drv3946_read_reg(drv, DRV3946_REG_STATUS0, &status0.data);
  // if (err != 0 && err != -2)
  // {
  //     return -4;
  // }
  err = drv3946_read_reg(drv, DRV3946_REG_STATUS1, &status1.data);
  // if (err != 0 && err != -2)
  // {
  //     return -4;
  // }

  if (status0.bits.DEVICE_ERR != 0 ||
      status0.data ==
          0xFFFFU) // There must have been some VDD_ERR, DEV_ERR or NAD_ERR to give us all 1s in the status0 response.
  {
    drv->last_init_return = __LINE__;
    return -1;
  } else if (status0.bits.NAD != drv->spi_addr) {
    // Doesn't have the right address on SPI. So this SPI read didn't return any data.
    // Is it reading the NAD pin resistance correctly on power up?
    drv->last_init_return = __LINE__;
    return -1;
  } else if (status0.bits.nFAULT_PIN_STAT != 1) {
    // There shouldn't be any faults.
    drv->last_init_return = __LINE__;
    return -1;
  } else if (status1.bits.STARTUP_BIST_W != 0) {
    drv->last_init_return = __LINE__;
    return -1;
  }

  // WARNINGS where we can still try starting up
  // Normally, the ONLY warnings we should have on powerup are
  //  POR (just telling us: hey, we powered up)
  //  CONFIG CRC invalid warning (it should've just been cleared by setting the CRCs in CONFIGA and CONFIGB)
  if (status0.bits.WARNINGS == 1) {
    // There shouldn't be any warnings.
    // I can't tell if POR bit rolls up into the warnings. If that's the only warning, it's fine.
    // return -2;
  }
  // These bits SHOULD be zero on first power up.
  // If the processor rebooted it's OK if they have another value
  if (status0.bits.CH1_OFF_DIAG_STAT != 0 || status0.bits.CH1_STAT != 0 || status0.bits.CH2_OFF_DIAG_STAT != 0 ||
      status0.bits.CH2_STAT != 0) {
    // If this happens on 1st power up, there may be an electrical error.
    // return -2;
  }

  // TODO: Read the MEAS registers MEAS5 and MEAS6 to confirm the measured resistance on IPROPIx pin

  // This will CLEAR all faults even if they're legit.
  // Set the POR bit to 0.
  drv3946_clear_faults(drv);

  // Get status after clearing faults
  err = drv3946_read_reg(drv, DRV3946_REG_STATUS0, &status0.data);
  // if (err != 0 && err != -2)
  // {
  //     return -4;
  // }
  if (status0.bits.NAD != drv->spi_addr ||
      status0.bits.POR != 0 // POR should be 0 after clearing faults. This should be the only change in the status since
                            // we set the config registers.
                            //|| status.status0.bits.WARNINGS != 0
  ) {
    drv->last_init_return = __LINE__;
    return -3;
  }

  drv->state = ds402_OPERATION_ENABLED;
  steering_control_set_enable_pins();

  // TODO: Check Enable Pins
  // err = drv3946_read_reg(drv, DRV3946_REG_STATUS0, &status0.data);
  // if (   status0.bits.EN1_PIN_STAT != 1
  //     || status0.bits.EN2_PIN_STAT != 0)
  // {
  //     drv->last_init_return = __LINE__;
  //     return -1;
  // }

  // The drv3946 channels are on.
  drv->last_init_return = 0;
  return 0;
}

static void drv3946_disable_peak_current_output(DRV3946_SW_DRIVER *drv) {
  // Configure drv3946 to go to the Hold Current setting, and not drive at the Peak Current.
  // This is specific to Hasselhoff not part of just initializing the chip.
  //
  // Ordinarily, the drv3946 is used to drive relays.
  //  Relays may want a larger current to be applied initially to get the relay moving.
  //  Once the relay is no longer at rest, a lower current may be used.
  // Since we have proportional control we don't want to drive at the Peak Current level.
  //  So set the CH1_PC_DIS and CH2_PC_DIS bits.
  uint8_t txData[3] = {0};
  uint8_t rxData[3] = {0};

  uint8_t cmd = (0x0 << 5)    // SLOWEST value for Slew Rate
                | (0x1 << 3)  // CH1_PC_DIS
                | (0x1 << 2); // CH2_PC_DIS

  make_write_cmd(drv->spi_addr, DRV3946_REG_CMD0, cmd << 8, &txData[0]);
  drv3946_tx_rx(drv, &txData[0], &rxData[0]);
}

// TODO: Check for SPI faults in header
int drv3946_set_ch1_current(DRV3946_SW_DRIVER *drv, uint8_t ch1_hold_current) {
  uint8_t txData[3] = {0};
  uint8_t rxData[3] = {0};

  txData[0] = make_header(drv->spi_addr, DRV3946_REG_CONFIGA0, 0);
  // NOTE: This is the default value for the Peak Current mode.
  //  This is not a max value for the Hold Current (the normal current setpoint),
  //  'Peak Current' is a separate mode from Hold Current.
  txData[1] = 0x00;
  // This is the setpoint for the current on this channel.
  // The motor driver will try to adjust to this new setpoint right away,
  //  there's no need to toggle enable lines or anything else.
  txData[2] = ch1_hold_current;

  drv3946_tx_rx(drv, &txData[0], &rxData[0]);

  // Do a 2nd write. Since the data that was previously in the register is
  // returned when we do a write, the 16-bit data here should match what we sent out.
  drv3946_tx_rx(drv, &txData[0], &rxData[0]);

  if (!(rxData[1] == txData[1]) || !(rxData[2] == txData[2])) {
    return -1;
  }
  return 0;
}

int drv3946_set_ch2_current(DRV3946_SW_DRIVER *drv, uint8_t ch2_hold_current) {
  uint8_t txData[3] = {0};
  uint8_t rxData[3] = {0};

  txData[0] = make_header(drv->spi_addr, DRV3946_REG_CONFIGA1, 0);
  txData[1] = 0x00;
  txData[2] = ch2_hold_current;

  drv3946_tx_rx(drv, &txData[0], &rxData[0]);

  // Do a 2nd write. Since the data that was previously in the register is
  // returned when we do a write, the 16-bit data here should match what we sent out.
  drv3946_tx_rx(drv, &txData[0], &rxData[0]);

  if (!(rxData[1] == txData[1]) || !(rxData[2] == txData[2])) {
    return -1;
  }
  return 0;
}

static void drv3946_make_configuration(DRV3946_SW_DRIVER *drv) {
  drv3946_make_default_configuration(drv);

  DRV3946_CONFIGA_REGS *configA = &drv->config_a;
  DRV3946_CONFIGB_REGS *configB = &drv->config_b;

  // Hasselhoff drv3946 Configuration Register settings:

  // nFAULT_CONFIG[0] = 0
  // Don't ever need these warnings on
  // nFAULT_CONFIG[1] = 1
  // SPI watchdog On
  // nFAULT_CONFIG[2] = 1
  // Fault at the Voltage and Temperature warning levels rather than the error levels.
  // This one is iffy. You could configure it either way.
  // nFAULT_CONFIG[3] = 1
  configA->bits.nFAULT_CONFIG = 0x0;

  // UCLO_FAULT_FLTR = 7
  // If there's an Undercurrent fault (if we even want that enabled) it will persist for > 1 second probably. It will be
  // a wiring issue.
  configA->bits.UCLO_FAULT_FLTR = 0x7;
  configB->bits.UCLO_EN = 0;       // DEBUG: Disable UCLO trip to confirm it's the cause of trips.
  configB->bits.LOW_CUR_W_DIS = 1; // DEBUG: Disable Low Current Warning

  // Slew rate: Slowest
  // Pros: This probably creates the Least amount of transients or overshoot/ringing.
  // Cons: generates the Most amount of heat.
  // This is in CMD0, not a Config register.

  //  PVDD OC
  // OCP_FLTR = 1
  // Change Overcurrent detection time from 4us -> 8us
  configA->bits.OCP_FLTR = 1;

  // TODO: How to set detection times for:
  //  PVDD OV
  //  PVDD UV
  configA->bits.PVDD_OV_FLTR = 0; // PVDD Overvoltage means the 12V line is above 40V. So set detection to the shortest
                                  // time (between 240us to 315us)
  configA->bits.PVDD_UV_FLTR = 3; // Set Undervoltage detection to the longest time (~1000us).

  configB->bits.VDD_OV_SHUTOFF_EN = 1;

  // PWM_FAULT_FLTR = 7
  // I don't care about PWM anomalies unless they're persistent.
  // Set to the longest detection time (256 PWM cycles)
  configA->bits.UCLO_FAULT_FLTR = 0x7;

  // SPI Watchdog Timeout time
  configB->bits.SPI_WD_SEL = 3; // 2048 ms +- 256 ms
  configB->bits.SPI_WD_SHUTOFF_EN = 1;

  // Quick Turn Off settings:
  // It seems like the defaults allow the widest possible range for how long Quick Turn Off takes.
  // I think we want to RAISE the Minimum turn off time. So that it never happens very quickly. This way, preventing
  // voltage spike from QTO on the PVDD as much as we can. QTOT_MIN_THRSx = 3 QTOT_MAX_THRSx = 3 All that this is
  // guaranteeing is that Quick Turn Off will not take LESS than 224 us.
  //     And has a max of 36 ms.
  // QTO will push switching and voltage around so that Current drops to Zero. The more quickly that happens, it implies
  // the more quickly a change in voltage happens. configA->bits.QTOT_MIN_THRS = 3; configA->bits.QTOT_MAX_THRS = 3;

  // Minimum Off time after QTO:
  //  RETRY_WAIT_SEL
  //  If some fault is causing Quick Turn Off events. This is the minimum time that the output has to stay Off before it
  //  could be enable again.
  //      This can be set anywhere from ~8 ms to ~500ms.
  // Set this to 8ms (the shortest time) so the loop which runs PID (100 Hz, 10ms) can quickly clear faults.
  configB->bits.RETRY_WAIT_SEL = 0;

  // PWM Frequency:
  //  I really don't know how this affects the solenoid valve performance.
  //  Fast PWM Frequency consumes more power because the fet is switch more often, so it heats a bit more.
  //  Lowest PWM Frequency may have the most force (longest continuous On time) but for the same reason has largest
  //  ripple current. Lower PWM Frequency is more likely to be audible. highest frequency least likely to be audible.
  //  Both irrelevant next to noise of tractor. Ripple current should be specified for the board. Some faults/warnings
  //  are triggered in terms of number of consecutive PWM cycles, so setting PWM frequency higher will make those trip
  //  sooner.
  configB->bits.CH1_fC_PWM = 0x00; // 500 Hz
  configB->bits.CH2_fC_PWM = 0x00; // 500 Hz

  // Disable
  configB->bits.RIPPLE_U_W_DIS = 1;
  configB->bits.RIPPLE_L_W_DIS = 1;

  // Set the threshold for detecting Channel failed short / failed open (e.g. CH1_OFF_DIAG_STAT == 1 ) to be the least
  // likely to trigger accidentally.
  configB->bits.OLP_SEL = 0x7;

  // Completely disable the check for Channel failed short / failed open (e.g. CH1_OFF_DIAG_STAT == 1 ).
  //  Todo: While testing on T4 the bti for the fault was set even though both of the channels were usable. So disable
  //  it until fix identified.
  configB->bits.CH1_OLP_DIS = 1;
  configB->bits.CH2_OLP_DIS = 1;
}

uint32_t drv3946_get_spi_errs_count(DRV3946_SW_DRIVER *drv) { return drv->spi_errs; }

// TODO:
// Return Zero: OK to continue
// Return nonzero: Error. drv3946 should stop driving, or the chip is already not driving one or more channels
int drv3946_should_stop(DRV3946_STATUS *p_status) {
  int err = 0;

  // failed Short or failed Open
  if (p_status->status0.bits.CH1_OFF_DIAG_STAT != 0) {
    LOG_INF("DRV3946: CH1_OFF_DIAG_STAT=%d", p_status->status0.bits.CH1_OFF_DIAG_STAT);
    err = -1;
  }
  if (p_status->status0.bits.CH2_OFF_DIAG_STAT != 0) {
    LOG_INF("DRV3946: CH2_OFF_DIAG_STAT=%d", p_status->status0.bits.CH2_OFF_DIAG_STAT);
    err = -2;
  }

  if (p_status->status0.bits.CH1_STAT > 1) {
    LOG_INF("DRV3946: CH1_STAT=%d", p_status->status0.bits.CH1_STAT);
    err = -3;
  }
  if (p_status->status0.bits.CH2_STAT > 1) {
    LOG_INF("DRV3946: CH2_STAT=%d", p_status->status0.bits.CH2_STAT);
    err = -4;
  }

  /*SPI_WD_DIS = 0,
SPI_WD_SHUTOF
F_EN= 1*/
  // not needed. It's in State
  // p_status->

  /*
  SPI Watchdog
      Must set SPI_WD_SHUTOFF_EN
  VDD
  POR
  SM_PVDD_UV_QTO
      Undervoltage dipped enough to cause the driver to stop driving, not just set a warning.
  PVDD_OV
      PVDD Overvoltage so high we stopped driving.
      Yes if
      nFAULT_CONFIG[
      2] = 1 or
      nFAULT_CONFIG[
      3] = 1
  Overcurrent
  Undercurrent
  Overtemp (OT)
  DEV_ERR on SPI
      It COULD indicate:
          5V OV or UV
          Oscillator fail
          Digital BIST fail
          Memory BIST fail

  */

  /*
  Doesn't stop output, but it should
  NAD_ERR
  CHx_RIPROPI_W
  PVDD_UV
      Indicates the 12V rail has dipped, and Fixed Duty Cycle independent from what we set(?).
      Yes if
      nFAULT_CONFIG[
      2] = 1
  Overtemp Warning
  STARTUP_BIST_W
      Analog BIST fail
  */

  /*
  Just interesting:
  CHx_PC_LOW_CURR_W
  CHx_HC_LOW_CURR_W
      Indicates we've railed out at 100% duty cycle, but can't reach target current.
  CHx_PC_PWM_BIST_W
  CHx_HC_PWM_BIST_W
  */
  return err;
}

void drv3946_fault_reset(DRV3946_SW_DRIVER *drv) {
  // Disable output before we send the reset command.
  // Prevents issue where after sending Reset, the chip would draw the default Hold Current (0x40) before the driver
  // re-initializes the chip's configuration.
  steering_control_disable_enable_pins();

  drv3946_clear_addr1_faults_and_reinit(drv);
  // Clear the cause of error, so we don't end up back in Fault condition. Do not clear the total number of SPI errors.
  drv->spi_errs_cause.data = 0;
  drv->state = ds402_SWITCH_ON_DISABLED;
}

/**
 * @brief Switch DRV channels off
 *
 *  Stop the steering from being driven by the board.
 *
 *  Turn off DRV channels. Minimize using the DRV3946 chip (except maybe polling status to pet the watchdog)
 *  drv_init() will not be called again until drv3946_enable_operation() is called.
 */
// TODO: Need to check and handle errors inside here, and return error code.
void drv3946_disable_operation(DRV3946_SW_DRIVER *drv) {
  drv->enable_operation = false;
  steering_control_disable_enable_pins();
  drv3946_set_ch1_ch2(drv, 0, 0);
  drv3946_set_ch1_current(drv, 0);
  drv3946_set_ch2_current(drv, 0);
}

/**
 * @brief Re-init and switch DRV channels on
 */
void drv3946_enable_operation(DRV3946_SW_DRIVER *drv) {
  drv->enable_operation = true;
  drv->state = ds402_SWITCH_ON_DISABLED;
  // Nothing else needs to be done here. drv_update will take care of getting to OPERATION_ENABLED state.
}

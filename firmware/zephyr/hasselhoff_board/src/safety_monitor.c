#include "safety_monitor.h"
#include "tractor_can.h"
#include <logging/log.h>
#include <utils/handle_errors.h>
LOG_MODULE_REGISTER(safety_monitor, LOG_LEVEL_DBG);

uint32_t transmission_speed_limit = 500;
float ground_speed_limit = 5.0f;

void safety_monitor_thread() {
  LOG_INF("Safety monitor thread started");
  uint64_t moving_start_time = 0;
  bool moving_timer_active = false;
  uint64_t neutral_start_time = 0;
  bool neutral_timer_active = false;

  while (1) {
    thread_checkin(k_current_get());
    k_msleep(100);

    // Reset flags in disabled or safe state
    if (HH_STATE == HH_STATE_DISABLED || HH_STATE == HH_STATE_SAFE) {
      hh_state_flags.speed_limit_exceeded = 0;
      hh_state_flags.stop_timeout_exceeded = 0;
      moving_timer_active = false;
      neutral_timer_active = false;
      continue;
    }

    if (!CONTROL_IS_ENABLED) {
      continue;
    }

    // Monitor tractor movement and neutral gear in STOPPED state
    if (HH_STATE == HH_STATE_STOPPED) {
      // Check movement
      if (!tractor_is_stopped()) {
        if (!moving_timer_active) {
          moving_start_time = k_uptime_get();
          moving_timer_active = true;
        }
        // Check if tractor has been moving for more than 2 seconds
        else if (k_uptime_get() - moving_start_time > 1000) {
          LOG_WRN("Tractor moving for more than 2 seconds in STOPPED state - transitioning to SAFE state");
          hh_state_flags.stop_timeout_exceeded = 1;
          hh_set_state(HH_STATE_SAFE);
        }
      } else {
        moving_timer_active = false;
      }

      // Check neutral gear
      if (tractor_state.gear == GEAR_COMMAND_NEUTRAL) {
        if (!neutral_timer_active) {
          neutral_start_time = k_uptime_get();
          neutral_timer_active = true;
        }
        // Check if tractor has been in neutral for more than 2 seconds
        else if (k_uptime_get() - neutral_start_time > 1000) {
          LOG_WRN("Tractor in neutral for more than 2 seconds in STOPPED state - transitioning to SAFE state");
          hh_state_flags.stop_timeout_exceeded = 1;
          hh_set_state(HH_STATE_SAFE);
        }
      } else {
        neutral_timer_active = false;
      }
    } else if (HH_STATE == HH_STATE_OPERATIONAL) {
      // Check neutral gear in operational state
      if (tractor_state.gear == GEAR_COMMAND_NEUTRAL) {
        if (!neutral_timer_active) {
          neutral_start_time = k_uptime_get();
          neutral_timer_active = true;
        }
        // Check if tractor has been in neutral for more than 2 seconds
        else if (k_uptime_get() - neutral_start_time > 1000) {
          LOG_WRN("Tractor in neutral for more than 2 seconds in OPERATIONAL state - transitioning to SAFE state");
          hh_state_flags.stop_timeout_exceeded = 1;
          hh_set_state(HH_STATE_SAFE);
        }
      } else {
        neutral_timer_active = false;
      }
    } else {
      moving_timer_active = false;
      neutral_timer_active = false;
    }

    // Check ground speed in all active states
    if (tractor_state.ground_speed > ground_speed_limit) {
      LOG_INF("Ground speed limit exceeded - speed: %d, limit: %d", (uint32_t)(tractor_state.ground_speed * 100),
              (uint32_t)(ground_speed_limit * 100));
      hh_state_flags.speed_limit_exceeded = 1;
      hh_set_state(HH_STATE_SAFE);
    }

    // Check transmission speed only in operational state
    if (HH_STATE == HH_STATE_OPERATIONAL && tractor_state.gear != GEAR_COMMAND_PARK &&
        tractor_state.transmission_speed > transmission_speed_limit) {
      LOG_INF("Transmission speed limit exceeded - speed: %d, limit: %d", tractor_state.transmission_speed,
              transmission_speed_limit);
      hh_state_flags.speed_limit_exceeded = 1;
      hh_set_state(HH_STATE_SAFE);
    }

    // // If can bus is not active and we are in op or stopped state, estop
    // if (!is_can_bus_active() && (HH_STATE == HH_STATE_OPERATIONAL || HH_STATE == HH_STATE_STOPPED)) {
    //   LOG_INF("CAN bus is not active and we are in op or stopped state, setting safe");
    //   hh_set_state(HH_STATE_SAFE);
    // }

    // Track time in safe state with inactive CAN bus
    static uint64_t can_inactive_start = 0;
    static bool can_inactive_timer_active = false;

    if (HH_STATE == HH_STATE_SAFE) {
      if (!is_can_bus_active()) {
        if (!can_inactive_timer_active) {
          can_inactive_start = k_uptime_get();
          can_inactive_timer_active = true;
        } else if (k_uptime_get() - can_inactive_start > 5000) {
          LOG_INF("CAN bus inactive for 5 seconds in safe state, e-stopping");
          hh_set_state(HH_STATE_ESTOPPED);
        }
      } else {
        can_inactive_timer_active = false;
      }
    } else {
      can_inactive_timer_active = false;
    }
  }
}

K_THREAD_DEFINE(safety_monitor_thread_id, 1024, safety_monitor_thread, NULL, NULL, NULL, SAFETY_MONITOR_THREAD_PRIORITY,
                0, K_TICKS_FOREVER);

int safety_monitor_init() {
  LOG_INF("Safety monitor initialized");

  k_thread_name_set(safety_monitor_thread_id, "safety_monitor_thread");
  k_thread_start(safety_monitor_thread_id);
  register_monitored_thread(safety_monitor_thread_id, "safety_monitor_thread", 1000);
  return 0;
}
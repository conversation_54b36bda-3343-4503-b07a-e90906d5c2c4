/{
    chosen {
        zephyr,code-partition = &slot0_partition;
    };

    aliases {
        watchdog0 = &iwdg1;
        eeprom0 = &eeprom0;
        qdec64k-fr = &qdec64k_fr;
        qdec64k-fl = &qdec64k_fl;
        qdec64k-br = &qdec64k_br;
        qdec64k-bl = &qdec64k_bl;
        zlcb0 = &zlcb0;
    };

    gpios {
        compatible = "gpio-keys";
        pps_in {
            gpios = <&gpioa 4 GPIO_ACTIVE_LOW>;
            label = "PPS_IN";
        };
    };
};

&timers1 {
    status = "okay";
    qdec64k_fl: qdec64k {
        compatible = "st,stm32-qdec64k";
        status = "okay";
        label = "QDEC64K_FL";
        pinctrl-0 = <&tim1_ch1_pe9 &tim1_ch2_pe11>;
        pinctrl-names = "default";
    };
};

&timers2 {
    status = "okay";
    qdec64k_br: qdec64k {
        compatible = "st,stm32-qdec64k";
        status = "okay";
        label = "QDEC64K_BR";
        pinctrl-0 = <&tim2_ch1_pa5 &tim2_ch2_pb3>;
        pinctrl-names = "default";
    };
};

&timers3 {
    status = "okay";
    qdec64k_bl: qdec64k {
        compatible = "st,stm32-qdec64k";
        status = "okay";
        label = "QDEC64K_BL";
        pinctrl-0 = <&tim3_ch1_pc6 &tim3_ch2_pb5>;
        pinctrl-names = "default";
    };
};

&timers4 {
    status = "okay";
    qdec64k_fr: qdec64k {
        compatible = "st,stm32-qdec64k";
        status = "okay";
        label = "QDEC64K_FR";
        pinctrl-0 = <&tim4_ch1_pd12 &tim4_ch2_pd13>;
        pinctrl-names = "default";
    };
};

&timers8 {
    status = "okay";
    st,prescaler = <119>;
    zlcb0: zlcb {
        compatible = "st,stm32-zlcb";
        status = "okay";
        label = "ZLCB0";
    };
};

&iwdg1 {
    status = "okay";
};

&i2c1 {
    /* ATMEL AT24C04D */
    eeprom0: atmel_at24@a0 {
        compatible = "atmel,at24";
        reg = <0x50>;
        /**
         * Whole EEPROM is 512 bytes, but address 0x50 will only access half of it.
         * Use address 0x51 to access the other half.
         */
        size = <256>;
        pagesize = <16>;
        address-width = <8>;
        timeout = <5>;
        label = "ATMEL_AT24";
    };
};

&clk_hse {
    /delete-property/ hse-bypass;
};

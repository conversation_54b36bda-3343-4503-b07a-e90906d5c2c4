# Enable networking
CONFIG_NETWORKING=y
CONFIG_NET_IPV4=y
CONFIG_NET_IPV6=n
CONFIG_NET_TCP=n
CONFIG_NET_UDP=y
CONFIG_NET_SOCKETS=y
CONFIG_NET_SOCKETS_POSIX_NAMES=y
CONFIG_NET_LOG=y
CONFIG_NET_PKT_RX_COUNT=64
CONFIG_NET_BUF_RX_COUNT=256

# embiggen the ARP table
CONFIG_NET_ARP_TABLE_SIZE=64

# Network settings
CONFIG_NET_CONFIG_SETTINGS=y
CONFIG_NET_CONFIG_NEED_IPV4=y
CONFIG_NET_CONFIG_MY_IPV4_ADDR="*********"
CONFIG_NET_CONFIG_PEER_IPV4_ADDR="*********"
CONFIG_NET_CONFIG_MY_IPV4_NETMASK="*************"
CONFIG_BROADCAST_IPV4_ADDR="************"
# Don't wait for link up to start running
CONFIG_NET_CONFIG_INIT_TIMEOUT=-1
CONFIG_NET_CONNECTION_MANAGER=y
CONFIG_NET_BUF_USER_DATA_SIZE=64
CONFIG_MCUMGR_BUF_COUNT=4

# Port
CONFIG_APP_NANOPB_PORT=4243
CONFIG_APP_FASTBIN_PORT=4244
CONFIG_APP_FASTBIN_BROADCAST_PORT=4424
#UDP
CONFIG_LIB_UDP=y
CONFIG_PB_REQUESTS_BUFFER_SIZE=5
CONFIG_FB_REQUESTS_BUFFER_SIZE=32
CONFIG_NET_MAX_CONN=8

# Syslog
CONFIG_LOG=y
CONFIG_LOG_BACKEND_NET=y
CONFIG_LOG_BACKEND_NET_SERVER="*********:2442"
CONFIG_LOG_TIMESTAMP_64BIT=y

# logging via RTT (for debugging)
CONFIG_USE_SEGGER_RTT=y
CONFIG_SEGGER_RTT_SECTION_DTCM=y

# Nanopb
CONFIG_NANOPB=y

# Support reboot
CONFIG_REBOOT=y

# Watchdog
CONFIG_LIB_WATCHDOG=y
CONFIG_WATCHDOG=y
CONFIG_WDT_DISABLE_AT_BOOT=y

# EEPROM
CONFIG_EEPROM=y
CONFIG_EEPROM_AT24=y
CONFIG_I2C=y

# Quadrature decoders
CONFIG_QDEC64K=y
CONFIG_QDEC64K_STM32=y

#History list for encoders
CONFIG_LIB_HISTORY_LIST=y

# Zero latency callbacks
CONFIG_ZLCB=y
CONFIG_ZLCB_STM32=y

# PTP
CONFIG_LIB_PTP=y
CONFIG_CARBON_PTP_USE_INTERCEPT=n

# MCUBoot
CONFIG_BOOTLOADER_MCUBOOT=y

# Enable mcumgr.
CONFIG_MCUMGR=y
CONFIG_MCUMGR_SMP_NOTIFY_CALLBACK=y
CONFIG_LIB_SMP=y
CONFIG_FLASH=y
CONFIG_MCUBOOT_BOOT_MAX_ALIGN=32

# Some command handlers require a large stack.
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=2048

# Enable statistics and statistic names.
CONFIG_STATS=y
CONFIG_STATS_NAMES=y

# Enable most core commands.
CONFIG_MCUMGR_CMD_IMG_MGMT=y
CONFIG_MCUMGR_CMD_OS_MGMT=y
CONFIG_MCUMGR_CMD_STAT_MGMT=y
CONFIG_MCUMGR_SMP_UDP=y
CONFIG_MCUMGR_SMP_UDP_IPV4=y

# Required by the `taskstat` command.
CONFIG_THREAD_MONITOR=y

# use newlib
CONFIG_NEWLIB_LIBC=y
CONFIG_NEWLIB_LIBC_NANO=y

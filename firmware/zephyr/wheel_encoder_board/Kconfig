menuconfig APP_NANOPB_PORT
    int "UDP nanopb server port"
    range 1 65535

menuconfig APP_FASTBIN_PORT
    int "UDP fast bin server port"
    range 1 65535
menuconfig APP_FASTBIN_BROADCAST_PORT
    int "UDP fast bin broadcast server port"
    range 1 65535

menuconfig PB_REQUESTS_BUFFER_SIZE
    int "Number of elements to allow for the PB buffer"
    range 1 65535

menuconfig FB_REQUESTS_BUFFER_SIZE
    int "Number of elements to allow for the FB buffer"
    range 1 65535
menuconfig BROADCAST_IPV4_ADDR
    string "IP address for broadcasting"

module = APP
module-str = APP
source "subsys/logging/Kconfig.template.log_config"

source "Kconfig.zephyr"

/**
 * @file
 * @brief Sensor handler
 *
 * Reads out auxiliary sensors (namely, the environmental sensor) on the encoder board
 */
#pragma once

typedef struct sensors_enviro_data {
  struct net_ptp_time timestamp;

  // Temperature, °C
  float temperature;
  // %RH
  float humidity;
  // Air pressure, Pa
  float pressure;
} sensors_enviro_data_t;

int sensors_start();

int sensors_get_enviro(sensors_enviro_data_t *outData);

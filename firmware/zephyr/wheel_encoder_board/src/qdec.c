#include <logging/log.h>
LOG_MODULE_REGISTER(qdec, CONFIG_APP_LOG_LEVEL);

#include <drivers/qdec64k.h>
#include <lib/ptp/ptp.h>
#include <string.h>
#include <utils/handle_errors.h>

#include "qdec.h"

void qdec_init(struct qdec_state_t *state, history_list *hist, const struct device *qdec64k_fr,
               const struct device *qdec64k_fl, const struct device *qdec64k_br, const struct device *qdec64k_bl) {
  state->hist = hist;
  state->qdec64k_fr = qdec64k_fr;
  state->qdec64k_fl = qdec64k_fl;
#if NUM_QDEC == 4
  state->qdec64k_br = qdec64k_br;
  state->qdec64k_bl = qdec64k_bl;
#endif
}

void qdec_tick(struct qdec_state_t *state) {
  qdec_record record;
  uint64_t usec;
  history_record cur_record;
  cur_record.usec = &usec;
  cur_record.record = (uint8_t *)&record;
  struct net_ptp_time ts;

  HANDLE_CRITICAL(sensor_sample_fetch(state->qdec64k_fr));
  HANDLE_CRITICAL(sensor_sample_fetch(state->qdec64k_fl));
#if NUM_QDEC == 4
  HANDLE_CRITICAL(sensor_sample_fetch(state->qdec64k_br));
  HANDLE_CRITICAL(sensor_sample_fetch(state->qdec64k_bl));
#endif
  HANDLE_CRITICAL(ptp_slave_clk_get(&ts));

  history_record prev;
  history_list_get_latest(state->hist, &prev);
  qdec_record *prev_record = (qdec_record *)prev.record;
  struct sensor_value val;

  HANDLE_CRITICAL(sensor_channel_get(state->qdec64k_fr, SENSOR_CHAN_QDEC64K_TICKS, &val));
  record.fr = qdec64k_adjust_ticks(prev_record->fr, val.val1);
  HANDLE_CRITICAL(sensor_channel_get(state->qdec64k_fl, SENSOR_CHAN_QDEC64K_TICKS, &val));
  record.fl = qdec64k_adjust_ticks(prev_record->fl, val.val1);

#if NUM_QDEC == 4
  HANDLE_CRITICAL(sensor_channel_get(state->qdec64k_br, SENSOR_CHAN_QDEC64K_TICKS, &val));
  record.br = qdec64k_adjust_ticks(prev_record->br, val.val1);
  HANDLE_CRITICAL(sensor_channel_get(state->qdec64k_bl, SENSOR_CHAN_QDEC64K_TICKS, &val));
  record.bl = qdec64k_adjust_ticks(prev_record->bl, val.val1);
#endif

  usec = ptp_ts_net_to_us(&ts);
  history_list_add_record(state->hist, &cur_record);
}

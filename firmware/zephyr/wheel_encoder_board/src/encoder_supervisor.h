/**
 * @file
 * @brief Encoder supervision
 *
 * Handles state changes on the encoder fuse lines, e.g. indicating the encoder power lines have
 * shorted.
 *
 * Faults are automatically cleared after five seconds.
 */
#pragma once

#include <lib/history_list/history_list.h>

typedef enum encoder {
  kEncoderFrontLeft = (1 << 0),
  kEncoderFrontRight = (1 << 1),
  kEncoderBackLeft = (1 << 2),
  kEncoderBackRight = (1 << 3),
} encoder_t;

int encoder_supervisor_start(history_list *qdecList);

int encoder_supervisor_get_faulted(encoder_t *current, encoder_t *latched);
int encoder_supervisor_reset_faulted();

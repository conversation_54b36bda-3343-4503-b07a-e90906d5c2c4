#pragma once

#include <device.h>
#include <lib/history_list/history_list.h>
#include <stdint.h>

#if USE_REAPER
#define NUM_QDEC 2
#else
#define NUM_QDEC 4
#endif

#define NUM_QDEC_RECORDS 1000u

struct qdec_state_t {
  const struct device *qdec64k_fr;
  const struct device *qdec64k_fl;
#if NUM_QDEC == 4
  const struct device *qdec64k_br;
  const struct device *qdec64k_bl;
#endif
  history_list *hist;
};

typedef struct {
  int64_t fr;
  int64_t fl;
#if NUM_QDEC == 4
  int64_t br;
  int64_t bl;
#endif
} qdec_record;

void qdec_init(struct qdec_state_t *state, history_list *hist, const struct device *qdec64k_fr,
               const struct device *qdec64k_fl, const struct device *qdec64k_br, const struct device *qdec64k_bl);

void qdec_tick(struct qdec_state_t *state);

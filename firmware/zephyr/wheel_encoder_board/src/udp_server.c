#include <logging/log.h>
LOG_MODULE_REGISTER(udp_server, CONFIG_APP_LOG_LEVEL);

#include <errno.h>
#include <lib/ptp/ptp.h>
#include <lib/udp/udp.h>
#include <net/net_ip.h>
#include <net/socket.h>
#include <pb_decode.h>
#include <pb_encode.h>
#include <string.h>
#include <sys/reboot.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

#include "fastbin.h"
#include "generated/lib/drivers/nanopb/proto/nofx_board.pb.h"
#include "qdec.h"
#include "udp_server.h"

#if IS_ENABLED(CONFIG_NET_TC_THREAD_COOPERATIVE)
#define THREAD_PRIORITY K_PRIO_COOP(CONFIG_NUM_COOP_PRIORITIES - 1)
#else
#define THREAD_PRIORITY K_PRIO_PREEMPT(0)
#endif

#define TS2INT64(x) ((uint64_t)x.seconds * (uint64_t)(1000 * 1000) + x.micros)
#define INT642sec(x) ((x) / (1000 * 1000))
#define INT642usec(x) ((x) % (1000 * 1000))

static void process_nanopb();
static void process_fastbin();
static void broadcast_fastbin();
static history_list *hist;
static uint8_t fb_buffer[sizeof(fastbin_Header) + sizeof(fastbin_Rotary_Snapshot_Reply)];
static uint8_t broadcast_buffer[sizeof(fastbin_Rotary_Reply) * FASTBIN_BROADCAST_COUNT];

K_THREAD_DEFINE(nanopb_thread_id, 2048, process_nanopb, NULL, NULL, NULL, THREAD_PRIORITY, 0, K_TICKS_FOREVER);
K_THREAD_DEFINE(fastbin_thread_id, 2048, process_fastbin, NULL, NULL, NULL, THREAD_PRIORITY, 0, K_TICKS_FOREVER);
K_THREAD_DEFINE(broadcast_thread_id, 1024, broadcast_fastbin, NULL, NULL, NULL, THREAD_PRIORITY, 0, K_TICKS_FOREVER);
UDP_SERVER_DEFINE(npb_udp_server, CONFIG_PB_REQUESTS_BUFFER_SIZE, nofx_board_Request_size * 2, THREAD_PRIORITY);
UDP_SERVER_DEFINE(fb_udp_server, CONFIG_FB_REQUESTS_BUFFER_SIZE,
                  (sizeof(fastbin_Header) + sizeof(fastbin_Rotary_Snapshot_Request)) * 2, THREAD_PRIORITY);

static void handle_ping(diagnostic_Ping *req, diagnostic_Pong *resp) { resp->x = req->x; }

static void handle_version(version_Version_Reply *resp) {
  resp->major = 0;
  resp->minor = 0;
}
static int fill_timestamp_reply(time_Timestamp *npb_ts) {
  struct net_ptp_time ts;
  HANDLE_CRITICAL(ptp_slave_clk_get(&ts));
  npb_ts->seconds = ts.second;
  npb_ts->micros = ts.nanosecond / NSEC_PER_USEC;
  return 0;
}
static void handle_time_request(time_Request *req, time_Reply *resp) {
  switch (req->which_request) {
  case time_Request_set_tag:
    LOG_WRN("Set time not implemented currently");
    resp->which_reply = time_Reply_ack_tag;
    break;
  case time_Request_get_tag:
    resp->which_reply = time_Reply_timestamp_tag;
    fill_timestamp_reply(&resp->reply.timestamp);
    break;
  case time_Request_debug_tag:
    LOG_WRN("PPS not used so most info not set");
    resp->which_reply = time_Reply_debug_tag;
    fill_timestamp_reply(&resp->reply.debug.timestamp);
    resp->reply.debug.pps_timer_val = 0;
    resp->reply.debug.pps_ticks = 0;
    resp->reply.debug.freq_mul = 0;
    resp->reply.debug.error_ticks = 0;
    resp->reply.debug.error_ticks2 = 0;
    break;
  default:
    break;
  }
}

static void record_2_pbreply(const history_record *rec, rotary_encoder_RotaryEncoder_Reply *reply) {
  qdec_record *qrec = (qdec_record *)rec->record;
  reply->timestamp.seconds = INT642sec(*rec->usec);
  reply->timestamp.micros = INT642usec(*rec->usec);
  reply->has_timestamp = true;
  reply->front_left_ticks = qrec->fl;
  reply->front_right_ticks = qrec->fr;
#if NUM_QDEC == 4
  reply->back_left_ticks = qrec->bl;
  reply->back_right_ticks = qrec->br;
#endif
}

static void record_2_fbreply(const history_record *rec, fastbin_Rotary_Reply *reply) {
  qdec_record *qrec = (qdec_record *)rec->record;
  reply->usec = *rec->usec;
  reply->front_left_ticks = qrec->fl;
  reply->front_right_ticks = qrec->fr;
#if NUM_QDEC == 4
  reply->back_left_ticks = qrec->bl;
  reply->back_right_ticks = qrec->br;
#endif
}

static void handle_latest(rotary_encoder_RotaryEncoder_Reply *reply) {
  history_record r;
  history_list_get_latest(hist, &r);
  record_2_pbreply(&r, reply);
}

static void get_snapshot(uint64_t first, uint64_t last, history_record *records) {
  int index;
  index = history_list_get_best(hist, first, &records[0]);
  history_list_get_next(hist, index, &records[1]);
  index = history_list_get_best(hist, last, &records[2]);
  history_list_get_next(hist, index, &records[3]);
}
static void handle_snapshot(rotary_encoder_RotaryEncoderSnapshot_Request *request,
                            rotary_encoder_RotaryEncoderSnapshot_Reply *reply) {
  uint64_t first = TS2INT64(request->first);
  uint64_t last = TS2INT64(request->last);
  history_record records[4];

  get_snapshot(first, last, records);

  record_2_pbreply(&records[0], &reply->first_before);
  record_2_pbreply(&records[1], &reply->first_after);
  record_2_pbreply(&records[2], &reply->last_before);
  record_2_pbreply(&records[3], &reply->last_after);

  reply->has_first_before = true;
  reply->has_first_after = true;
  reply->has_last_before = true;
  reply->has_last_after = true;

  reply->has_request = true;
  reply->request.first = request->first;
  reply->request.last = request->last;
  reply->request.has_first = true;
  reply->request.has_last = true;
}

static void handle_latest_fb(fastbin_Rotary_Reply *reply) {
  history_record r;
  history_list_get_latest(hist, &r);
  record_2_fbreply(&r, reply);
}
static void handle_snapshot_fb(fastbin_Rotary_Snapshot_Request *req, fastbin_Rotary_Snapshot_Reply *reply) {
  history_record records[4];

  get_snapshot(req->first_us, req->last_us, records);
  reply->request.first_us = req->first_us;
  reply->request.last_us = req->last_us;

  record_2_fbreply(&records[0], &reply->first_before);
  record_2_fbreply(&records[1], &reply->first_after);
  record_2_fbreply(&records[2], &reply->last_before);
  record_2_fbreply(&records[3], &reply->last_after);
}
static void serve_fastbin(uint8_t *data, uint16_t received, udp_msg_metadata *metadata) {
  size_t out_size = 0;
  fastbin_Header *header = (fastbin_Header *)data;
  fastbin_Header *resp_header = (fastbin_Header *)fb_buffer;
  resp_header->request_id = header->request_id;

  uint8_t *pin = (uint8_t *)data + sizeof(fastbin_Header);
  uint8_t *pout = (uint8_t *)fb_buffer + sizeof(fastbin_Header);

  switch (header->opcode) {
  case ROTARY_SNAPSHOT_OPCODE: {
    resp_header->opcode = ROTARY_SNAPSHOT_REPLY_OPCODE;
    fastbin_Rotary_Snapshot_Reply *reply = (fastbin_Rotary_Snapshot_Reply *)pout;
    fastbin_Rotary_Snapshot_Request *req = (fastbin_Rotary_Snapshot_Request *)pin;
    handle_snapshot_fb(req, reply);
    out_size = sizeof(fastbin_Header) + sizeof(fastbin_Rotary_Snapshot_Reply);
  } break;

  case ROTARY_TICKS_OPCODE: {
    resp_header->opcode = ROTARY_TICKS_REPLY_OPCODE;
    fastbin_Rotary_Reply *reply = (fastbin_Rotary_Reply *)pout;
    handle_latest_fb(reply);
    out_size = sizeof(fastbin_Header) + sizeof(fastbin_Rotary_Reply);
  } break;

  default:
    break;
  }
  if (out_size == 0) {
    return;
  }
  udp_tx(&fb_udp_server, fb_buffer, out_size, metadata);
}

static void serve_nanopb(uint8_t *data, uint16_t received, udp_msg_metadata *metadata) {
  nofx_board_Request req = nofx_board_Request_init_zero;
  pb_istream_t istream = pb_istream_from_buffer(data, received);

  if (!pb_decode(&istream, nofx_board_Request_fields, &req)) {
    LOG_WRN("Failed to decode UDP packet with size: %d", received);
    return;
  }
  nofx_board_Reply resp = nofx_board_Reply_init_zero;
  resp.has_header = true;
  resp.header.requestId = req.header.requestId;

  switch (req.which_request) {
  case nofx_board_Request_ping_tag:
    resp.which_reply = nofx_board_Reply_pong_tag;
    handle_ping(&req.request.ping, &resp.reply.pong);
    break;
  case nofx_board_Request_rotary_encoder_tag:
    resp.which_reply = nofx_board_Reply_rotary_encoder_tag;
    switch (req.request.rotary_encoder.which_request) {
    case rotary_encoder_Request_config_tag:
      resp.reply.rotary_encoder.which_reply = rotary_encoder_Reply_config_tag;
      // We don't need configuration here for now, this is implemented for backward compatibility
      break;
    case rotary_encoder_Request_rotary_tag:
      resp.reply.rotary_encoder.which_reply = rotary_encoder_Reply_rotary_tag;
      handle_latest(&resp.reply.rotary_encoder.reply.rotary);
      break;
    case rotary_encoder_Request_rotary_snapshot_tag:
      resp.reply.rotary_encoder.which_reply = rotary_encoder_Reply_rotary_snapshot_tag;
      handle_snapshot(&req.request.rotary_encoder.request.rotary_snapshot,
                      &resp.reply.rotary_encoder.reply.rotary_snapshot);
      break;
    /* TODO: implement other operations */
    default:
      // unknown
      break;
    }
    break;
  /* TODO: implement other operations */
  case nofx_board_Request_version_tag:
    resp.which_reply = nofx_board_Reply_version_tag;
    handle_version(&resp.reply.version);
    break;
  case nofx_board_Request_reset_tag:
    sys_reboot(SYS_REBOOT_COLD);
    break;
  case nofx_board_Request_time_tag:
    resp.which_reply = nofx_board_Reply_time_tag;
    handle_time_request(&req.request.time, &resp.reply.time);
    break;
  default:
    LOG_WRN("Received unknown request with tag: %d", req.which_request);
    return;
  }

  pb_ostream_t ostream = pb_ostream_from_buffer(data, udp_buffer_size(&npb_udp_server));
  if (!pb_encode(&ostream, nofx_board_Reply_fields, &resp)) {
    LOG_WRN("Failed to encode nanoPB response");
    return;
  }
  udp_tx(&npb_udp_server, data, ostream.bytes_written, metadata);
}

static void process_nanopb() {
  for (;;) {
    uint8_t *data;
    udp_msg_metadata metadata;
    uint16_t received = udp_get_data_no_copy(&npb_udp_server, &data, &metadata, 0);
    serve_nanopb(data, received, &metadata);
    udp_get_data_release(&npb_udp_server, data);
  }
}

static void process_fastbin() {
  for (;;) {
    uint8_t *data;
    udp_msg_metadata metadata;
    uint16_t received = udp_get_data_no_copy(&fb_udp_server, &data, &metadata, 0);
    serve_fastbin(data, received, &metadata);
    udp_get_data_release(&fb_udp_server, data);
  }
}

static void broadcast_fastbin() {
  struct sockaddr_in addr;
  memset(&addr, 0, sizeof(addr));
  addr.sin_family = AF_INET;
  addr.sin_port = htons(CONFIG_APP_FASTBIN_BROADCAST_PORT);
  memcpy(&addr.sin_addr, net_ipv4_broadcast_address(), sizeof(addr.sin_addr));
  int sock = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
  history_record r;
  fastbin_Rotary_Reply *data = (fastbin_Rotary_Reply *)broadcast_buffer;
  for (;;) {
    for (size_t i = 0; i < FASTBIN_BROADCAST_COUNT; ++i) {
      k_msleep(FASTBIN_BROADCAST_DELAY);
      history_list_get_latest(hist, &r);
      record_2_fbreply(&r, &data[i]);
    }
    int ret = sendto(sock, broadcast_buffer, sizeof(broadcast_buffer), 0, (struct sockaddr *)&addr, sizeof(addr));
    if (ret < 0) {
      LOG_WRN("Failed to send UDP packet ret: %d, errno: %d", ret, errno);
      k_msleep(100);
    }
  }
}

void start_udp_server(history_list *h) {
  hist = h;
  UDP_SERVER_START(npb_udp_server, CONFIG_APP_NANOPB_PORT);
  UDP_SERVER_START(fb_udp_server, CONFIG_APP_FASTBIN_PORT);
  k_thread_name_set(nanopb_thread_id, "nanopb");
  k_thread_name_set(fastbin_thread_id, "fastbin");
  k_thread_name_set(broadcast_thread_id, "broadcast");
  k_thread_start(nanopb_thread_id);
  k_thread_start(fastbin_thread_id);
  k_thread_start(broadcast_thread_id);
}

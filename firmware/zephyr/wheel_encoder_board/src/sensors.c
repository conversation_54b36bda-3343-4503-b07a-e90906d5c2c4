#include <zephyr/devicetree.h>
#include <zephyr/drivers/sensor.h>
#include <zephyr/logging/log.h>

#include <lib/ptp/ptp.h>
#include <utils/handle_errors.h>

#include "sensors.h"

LOG_MODULE_REGISTER(sensors, CONFIG_APP_LOG_LEVEL);

// How often to read out the sensor data
#define SENSOR_READOUT_INTERVAL K_SECONDS(1)

typedef struct {
  struct k_mutex lock;

  sensors_enviro_data_t enviro;
} sensors_state_t;

static void sensor_timer_expired(struct k_timer *);
static void sensor_work(struct k_work *);
static int enviro_read();

// Enviro sensor device tree handle
static const struct device *const gDevEnviroInternal = DEVICE_DT_GET(DT_NODELABEL(enviro0));

// State for the sensor manager
__dtcm_bss_section static sensors_state_t gState;
// Timer used to trigger readout of sensor data
K_TIMER_DEFINE(gSensorTimer, sensor_timer_expired, NULL);
// Work item used to actually read sensor data
K_WORK_DEFINE(gSensorWork, sensor_work);

/**
 * @brief Initialize the sensor handler
 *
 * Start the periodic readout timer
 */
int sensors_start() {
  k_mutex_init(&gState.lock);

  HANDLE_UNLIKELY_BOOL(device_is_ready(gDevEnviroInternal), ENODEV);
  HANDLE_UNLIKELY(enviro_read());

  k_timer_start(&gSensorTimer, SENSOR_READOUT_INTERVAL, SENSOR_READOUT_INTERVAL);

  return 0;
}

/**
 * @brief Copy out the most recent environmental sensor reading
 */
int sensors_get_enviro(sensors_enviro_data_t *outData) {
  if (!outData) {
    return -EFAULT;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  { *outData = gState.enviro; }
  HANDLE_UNLIKELY(k_mutex_unlock(&gState.lock));

  return 0;
}

/**
 * @brief Sensor readout timer expired
 */
static void sensor_timer_expired(struct k_timer *) { k_work_submit(&gSensorWork); }

/**
 * @brief Read out sensor data
 */
static void sensor_work(struct k_work *) {
  int err;

  err = enviro_read();
  if (err) {
    LOG_WRN("%s failed: %d", "enviro_read", err);
  }
}

/**
 * @brief Read out environmental sensor data
 */
static int enviro_read() {
  struct net_ptp_time now;
  struct sensor_value temp, humidity, pressure;

  HANDLE_UNLIKELY(ptp_slave_clk_get(&now));
  HANDLE_UNLIKELY(sensor_sample_fetch(gDevEnviroInternal));

  HANDLE_UNLIKELY(sensor_channel_get(gDevEnviroInternal, SENSOR_CHAN_AMBIENT_TEMP, &temp));
  HANDLE_UNLIKELY(sensor_channel_get(gDevEnviroInternal, SENSOR_CHAN_HUMIDITY, &humidity));
  HANDLE_UNLIKELY(sensor_channel_get(gDevEnviroInternal, SENSOR_CHAN_PRESS, &pressure));

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    gState.enviro.timestamp = now;
    gState.enviro.temperature = sensor_value_to_double(&temp);
    gState.enviro.humidity = sensor_value_to_double(&humidity);
    gState.enviro.pressure = sensor_value_to_double(&pressure);
  }
  HANDLE_UNLIKELY(k_mutex_unlock(&gState.lock));

#if CONFIG_APP_SENSOR_DUMP_READINGS
  LOG_INF("%s: temp=%d.%u C, humid=%d.%u %%RH, pressure = %d.%u Pa", __FUNCTION__, temp.val1, temp.val2 / 1000000,
          humidity.val1, humidity.val2 / 1000000, pressure.val1, pressure.val2 / 1000000);
#endif

  return 0;
}

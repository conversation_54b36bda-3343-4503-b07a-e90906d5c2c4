#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(blinkenlights, CONFIG_APP_LOG_LEVEL);

#include <math.h>
#include <string.h>

#include <zephyr/drivers/led.h>
#include <zephyr/kernel.h>
#include <zephyr/net/net_if.h>
#include <zephyr/net/net_ip.h>

#include <lib/history_list/history_list.h>
#include <utils/handle_errors.h>

#if IS_ENABLED(CONFIG_LIB_STATUS_LED)
#include <lib/status_led/status_led.h>
#endif

#include "blinkenlights.h"
#include "encoder_supervisor.h"
#include "qdec.h"

#ifndef M_PI
#define M_PI (3.14159265358979323846)
#endif

/**
 * @brief LED brightness curve
 *
 * This corrects for the non-linear perception of brightness of LEDs; it's a sort of S curve deal
 */
static const uint8_t kLedBrightnessLut[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
    0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x03, 0x03, 0x03, 0x03,
    0x03, 0x03, 0x04, 0x04, 0x04, 0x04, 0x04, 0x05, 0x05, 0x05, 0x05, 0x06, 0x06, 0x06, 0x07, 0x07, 0x07, 0x08, 0x08,
    0x08, 0x09, 0x09, 0x0A, 0x0A, 0x0B, 0x0B, 0x0C, 0x0C, 0x0D, 0x0D, 0x0E, 0x0F, 0x0F, 0x10, 0x11, 0x11, 0x12, 0x13,
    0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1F, 0x20, 0x21, 0x23, 0x24, 0x26, 0x27, 0x29, 0x2B,
    0x2C, 0x2E, 0x30, 0x32, 0x34, 0x36, 0x38, 0x3A, 0x3C, 0x3E, 0x40, 0x43, 0x45, 0x47, 0x4A, 0x4C, 0x4F, 0x51, 0x54,
    0x57, 0x59, 0x5C, 0x5F, 0x62, 0x64, 0x67, 0x6A, 0x6D, 0x70, 0x73, 0x76, 0x79, 0x7C, 0x7F, 0x82, 0x85, 0x88, 0x8B,
    0x8E, 0x91, 0x94, 0x97, 0x9A, 0x9C, 0x9F, 0xA2, 0xA5, 0xA7, 0xAA, 0xAD, 0xAF, 0xB2, 0xB4, 0xB7, 0xB9, 0xBB, 0xBE,
    0xC0, 0xC2, 0xC4, 0xC6, 0xC8, 0xCA, 0xCC, 0xCE, 0xD0, 0xD2, 0xD3, 0xD5, 0xD7, 0xD8, 0xDA, 0xDB, 0xDD, 0xDE, 0xDF,
    0xE1, 0xE2, 0xE3, 0xE4, 0xE5, 0xE6, 0xE7, 0xE8, 0xE9, 0xEA, 0xEB, 0xEC, 0xED, 0xED, 0xEE, 0xEF, 0xEF, 0xF0, 0xF1,
    0xF1, 0xF2, 0xF2, 0xF3, 0xF3, 0xF4, 0xF4, 0xF5, 0xF5, 0xF6, 0xF6, 0xF6, 0xF7, 0xF7, 0xF7, 0xF8, 0xF8, 0xF8, 0xF9,
    0xF9, 0xF9, 0xF9, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFB, 0xFB, 0xFB, 0xFB, 0xFB, 0xFB, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC,
    0xFC, 0xFC, 0xFC, 0xFC, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD,
    0xFE, 0xFE, 0xFE, 0xFE, 0xFE, 0xFE, 0xFE, 0xFF, 0xFF};

// Full brightness of the wheel LEDs
#define ANIMATION_MAX_BRIGHTNESS (225)
// How long to wait between ticks of the animation
#define ANIMATION_FRAME_DURATION (K_MSEC(23))
// encoder ticks per full rotation
#define TICKS_PER_ROTATION (1500)

static void blinkenlights_worker();
static void get_ticks(qdec_record *outTicks);
#if IS_ENABLED(CONFIG_LIB_STATUS_LED)
static void handle_status_led();
#endif

typedef struct {
  // history list for qdec
  history_list *qdecList;
  // Frame counter
  size_t frameCount;
} blinkenlights_state_t;

// LED driver for encoder 1/2 LEDs
static const struct device *const gWheelLeds = DEVICE_DT_GET(DT_ALIAS(wheel_leds));
// State for the LED handler
static __dtcm_bss_section blinkenlights_state_t gState;

K_THREAD_DEFINE(gBlinkenlightsWorkerThread, 2048, blinkenlights_worker, NULL, NULL, NULL, K_PRIO_PREEMPT(12), 0,
                K_TICKS_FOREVER);

/**
 * @brief Initialize the LED handler
 *
 * This is responsible for the little task that animates the wheel encoder LEDs.
 */
int blinkenlights_start(history_list *qdecList) {
  gState.qdecList = qdecList;

  // set up LED hardware
  HANDLE_UNLIKELY_BOOL(device_is_ready(gWheelLeds), ENODEV);

  // start the worker task responsible for the animation
  k_thread_name_set(gBlinkenlightsWorkerThread, "blinkenlights");
  k_thread_start(gBlinkenlightsWorkerThread);

  return 0;
}

/**
 * @brief Worker task for LED handler
 *
 * Continuously sample the wheel encoders; based on the delta ticks, animate the wheels.
 */
static void blinkenlights_worker() {
  int err;
  uint8_t brightness[16];
  // normalized wheel angle, where 0-1 maps to 0°-360°
  double angles[2] = {0, 0};

  encoder_t faulted = 0;
  qdec_record prev, current;

  memset(brightness, 0, sizeof(brightness));
  get_ticks(&current);

  // put each channel into PWM mode
  for (size_t i = 0; i < ARRAY_SIZE(brightness); i++) {
    err = led_set_brightness(gWheelLeds, i, 1);
    if (err) {
      LOG_WRN("%s failed: %d", "led_set_brightness", err);
    }
  }

  do {
    // update RGB status led
#if IS_ENABLED(CONFIG_LIB_STATUS_LED)
    handle_status_led();
#endif

    // sample the encoders and calculate tick deltas
    prev = current;
    get_ticks(&current);
    HANDLE_CRITICAL(encoder_supervisor_get_faulted(&faulted, NULL));

    int64_t deltas[] = {
        (current.fl - prev.fl),
        (current.fr - prev.fr),
    };

    for (size_t i = 0; i < ARRAY_SIZE(deltas); i++) {
      angles[i] += ((double)deltas[i]) / ((double)TICKS_PER_ROTATION);
    }

    LOG_DBG("ticks = %lld, %lld, angles %d.%03u, %d.%03u", deltas[0], deltas[1], ((int)angles[0]),
            ((int)(angles[0] * 1000) % 1000), ((int)angles[1]), ((int)(angles[1] * 1000) % 1000));

    // update lights based on wheel angle
    for (size_t i = 0; i < 16; i++) {
      const size_t offset = i % 8;
      const size_t ch = i / 8;

      if ((1 << ch) & faulted) {
        brightness[i] = (gState.frameCount & 0b10000) ? ANIMATION_MAX_BRIGHTNESS / 2 : 0;
      } else {
        brightness[i] =
            kLedBrightnessLut[(int)(fabs(sin(angles[ch] + (M_PI / 8) * ((double)offset))) * ANIMATION_MAX_BRIGHTNESS)];
      }
    }

    err = led_write_channels(gWheelLeds, 0, ARRAY_SIZE(brightness), brightness);
    if (err) {
      LOG_WRN("%s failed: %d", "led_write_channels", err);
      k_sleep(K_SECONDS(1));
    }

    // wait for next frame
    k_sleep(ANIMATION_FRAME_DURATION);
    gState.frameCount++;
  } while (true);
}

/**
 * @brief Helper to get the current encoder ticks
 */
static void get_ticks(qdec_record *outTicks) {
  history_record r;
  history_list_get_latest(gState.qdecList, &r);

  qdec_record *qrec = (qdec_record *)r.record;
  memcpy(outTicks, qrec, sizeof(*qrec));
}

#if IS_ENABLED(CONFIG_LIB_STATUS_LED)
/**
 * @brief Update the RGB status LED
 *
 * This will be in one of the following states:
 *
 * - Green, solid: Link up
 * - Yellow, solid: Ethernet link down
 * - Purple, flashing: Encoder fault (regardless of link state)
 */
static void handle_status_led() {
  struct net_if *intf = net_if_get_default();

  encoder_t faulted = 0;
  HANDLE_CRITICAL(encoder_supervisor_get_faulted(NULL, &faulted));

  if (faulted) {
    leds_set_status((gState.frameCount & 0b10000) ? LED_COLOR_PURPLE : LED_COLOR_OFF);
  } else {
    if (net_if_is_up(intf)) {
      leds_set_status(LED_COLOR_GREEN);
    } else {
      leds_set_status(LED_COLOR_YELLOW);
    }
  }
}
#endif

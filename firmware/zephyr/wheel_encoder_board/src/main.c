#include <logging/log.h>
LOG_MODULE_REGISTER(main, CONFIG_APP_LOG_LEVEL);

#include <devicetree.h>
#include <drivers/eeprom.h>
#include <drivers/gpio.h>
#include <drivers/qdec64k.h>
#include <drivers/zlcb.h>
#include <lib/history_list/history_list.h>
#include <lib/ptp/ptp.h>
#include <lib/watchdog/watchdog.h>
#include <utils/handle_errors.h>
#include <zephyr.h>
#include <zephyr/logging/log_ctrl.h>

#if IS_ENABLED(CONFIG_LIB_STATUS_LED)
#include <lib/status_led/status_led.h>
#endif

#include "lib/smp/smp.h"
#include "qdec.h"
#include "udp_server.h"

#if defined(CONFIG_BOARD_WHEEL_ENCODER) && USE_REAPER
#include "blinkenlights.h"
#include "encoder_supervisor.h"
#include "sensors.h"
#endif

#define LOG_RATE_MS 5000

static const struct device *eeprom = DEVICE_DT_GET(DT_ALIAS(eeprom0));

static struct qdec_state_t qdec_state;
HISTORY_LIST_DEFINE(hist, NUM_QDEC_RECORDS, sizeof(qdec_record));

static int boot() {
  LOG_INF("Wheel encoder board booting");

#if IS_ENABLED(CONFIG_LIB_STATUS_LED)
  HANDLE_UNLIKELY(leds_init());
  HANDLE_UNLIKELY(leds_set_status(LED_COLOR_BLUE));
#endif

  /* configure ptp clock */
  struct net_if *iface = net_if_get_default();
  HANDLE_CRITICAL(ptp_slave_init(iface, 0));
  HANDLE_CRITICAL(ptp_slave_start());

  log_set_timestamp_func(log_get_ptp_timestamp, USEC_PER_SEC);

  /* ensure quadrature decoders are operational */
  const struct device *qdec64k_fr = DEVICE_DT_GET(DT_ALIAS(qdec64k_fr));
  HANDLE_UNLIKELY_BOOL(device_is_ready(qdec64k_fr), ENODEV);
  const struct device *qdec64k_fl = DEVICE_DT_GET(DT_ALIAS(qdec64k_fl));
  HANDLE_UNLIKELY_BOOL(device_is_ready(qdec64k_fl), ENODEV);

#if NUM_QDEC == 4
  const struct device *qdec64k_br = DEVICE_DT_GET(DT_ALIAS(qdec64k_br));
  HANDLE_UNLIKELY_BOOL(device_is_ready(qdec64k_br), ENODEV);
  const struct device *qdec64k_bl = DEVICE_DT_GET(DT_ALIAS(qdec64k_bl));
  HANDLE_UNLIKELY_BOOL(device_is_ready(qdec64k_bl), ENODEV);
#else
  const struct device *qdec64k_br = NULL;
  const struct device *qdec64k_bl = NULL;
#endif

  history_list_init(&hist);

  /* initialize qdec */
  qdec_init(&qdec_state, &hist, qdec64k_fr, qdec64k_fl, qdec64k_br, qdec64k_bl);

  /* setup recording callback */
  const struct device *zlcb = DEVICE_DT_GET(DT_ALIAS(zlcb0));
  HANDLE_UNLIKELY_BOOL(device_is_ready(zlcb), ENODEV);
  HANDLE_UNLIKELY(zlcb_set_callback_usec(zlcb, 250, (zlcb_callback_t)qdec_tick, &qdec_state));

  /* verify EEPROM works */
  HANDLE_UNLIKELY_BOOL(device_is_ready(eeprom), ENODEV);
  char c;
  HANDLE_UNLIKELY(eeprom_read(eeprom, 0, &c, 1));

  return 0;
}

void main() {
  start_watchdog();
  start_smp_lib();
  HANDLE_CRITICAL(boot());

#if defined(CONFIG_BOARD_WHEEL_ENCODER) && USE_REAPER
  HANDLE_CRITICAL(encoder_supervisor_start(&hist));
  HANDLE_CRITICAL(sensors_start());
  HANDLE_CRITICAL(blinkenlights_start(&hist));
#endif

  start_udp_server(&hist);

  /* periodic output */
  for (;;) {
    history_record r;
    history_list_get_latest(&hist, &r);
    qdec_record *qrec = (qdec_record *)r.record;
    uint32_t sec = (*(r.usec)) / 1000000;
    uint32_t usec = (*(r.usec)) % 1000000;
#if NUM_QDEC == 2
    LOG_INF("Time: (%us, %uus), Ticks: FL (%d), FR (%d)", sec, usec, (int32_t)qrec->fl, (int32_t)qrec->fr);
#elif NUM_QDEC == 4
    LOG_INF("Time: (%us, %uus), Ticks: FL (%d), FR (%d), BL (%d), BR (%d)", sec, usec, (int32_t)qrec->fl,
            (int32_t)qrec->fr, (int32_t)qrec->bl, (int32_t)qrec->br);
#endif
    k_msleep(LOG_RATE_MS);
  }
}

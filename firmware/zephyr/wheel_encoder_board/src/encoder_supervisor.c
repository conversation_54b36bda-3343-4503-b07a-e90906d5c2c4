#include <zephyr/devicetree.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/logging/log.h>

#include <utils/handle_errors.h>

#include "encoder_supervisor.h"
#include "qdec.h"

LOG_MODULE_REGISTER(encoder_supervisor, CONFIG_APP_LOG_LEVEL);

typedef struct {
  struct k_mutex lock;

  // Current fault status (from ISR)
  encoder_t actual;
  // Latched fault status
  encoder_t latched;

  // GPIO callback for encoder fuse state change
  struct gpio_callback callbacks[NUM_QDEC];
} encoder_supervisor_state_t;

static void encoder_supervisor_fuse_work(struct k_work *);
static void fuse_gpio_irq_handler(const struct device *dev, struct gpio_callback *cb, uint32_t pins);
static void fuse_latch_timer_expiration(struct k_timer *);

// Mapping of index to encoder bitmask
static const encoder_t kEncoderMap[] = {
    kEncoderFrontLeft,
    kEncoderFrontRight,
    kEncoderBackLeft,
    kEncoderBackRight,
};
// GPIOs for fuse detection (asserted = fuse is tripped)
static const struct gpio_dt_spec gFuseGpios[NUM_QDEC] = {
    GPIO_DT_SPEC_GET_BY_IDX(DT_PATH(wheel_fl), fuse_gpios, 0),
    GPIO_DT_SPEC_GET_BY_IDX(DT_PATH(wheel_fr), fuse_gpios, 0),
};
// current supervisor state
__dtcm_bss_section static encoder_supervisor_state_t gState;
// Work item triggered when the fuse IOs change state
K_WORK_DEFINE(gFuseWork, encoder_supervisor_fuse_work);
// Timer used to reset latched faults if all clear
K_TIMER_DEFINE(gFuseResetTimer, fuse_latch_timer_expiration, NULL);

/**
 * @brief Initialize encoder supervisor
 *
 * Set up the IOs for the fuse detect for each of the encoders.
 */
int encoder_supervisor_start(history_list *qdecList) {
  k_mutex_init(&gState.lock);

  // GPIOs as inputs
  for (size_t i = 0; i < ARRAY_SIZE(gFuseGpios); i++) {
    HANDLE_UNLIKELY_BOOL(device_is_ready(gFuseGpios[i].port), ENODEV);
    HANDLE_UNLIKELY(gpio_pin_configure_dt(&gFuseGpios[i], GPIO_INPUT));
  }

  // read initial state
  k_work_submit(&gFuseWork);

  // set up IRQ callbacks
  for (size_t i = 0; i < ARRAY_SIZE(gFuseGpios); i++) {
    HANDLE_UNLIKELY(gpio_pin_interrupt_configure_dt(&gFuseGpios[i], GPIO_INT_EDGE_BOTH));
    gpio_init_callback(&gState.callbacks[i], fuse_gpio_irq_handler, BIT(gFuseGpios[i].pin));
    gpio_add_callback(gFuseGpios[i].port, &gState.callbacks[i]);
  }

  return 0;
}

/**
 * @brief Get the encoder fault state
 *
 * This allows reading out the current (actual) as well as the latched state. The latched state can
 * be cleared by user request.
 */
int encoder_supervisor_get_faulted(encoder_t *current, encoder_t *latched) {
  if (!current && !latched) {
    return -EFAULT;
  }

  k_mutex_lock(&gState.lock, K_FOREVER);
  {
    if (current) {
      *current = gState.actual;
    }
    if (latched) {
      *latched = gState.latched;
    }
  }
  k_mutex_unlock(&gState.lock);

  return 0;
}

/**
 * @brief Clear latched encoder fault state
 */
int encoder_supervisor_reset_faulted() {
  k_mutex_lock(&gState.lock, K_FOREVER);
  { gState.latched = 0; }
  k_mutex_unlock(&gState.lock);

  return 0;
}

/**
 * @brief Read the state of current fuses
 */
static void encoder_supervisor_fuse_work(struct k_work *) {
  int err;

  // read all IOs
  encoder_t currentState = 0;

  for (size_t i = 0; i < ARRAY_SIZE(gFuseGpios); i++) {
    HANDLE_CRITICAL((err = gpio_pin_get_dt(&gFuseGpios[i])));

    if (err) {
      currentState |= kEncoderMap[i];
    }
  }

  // apply
  k_mutex_lock(&gState.lock, K_FOREVER);
  {
    const encoder_t oldState = gState.actual;
    const encoder_t delta = currentState ^ oldState;
    gState.actual = currentState;
    gState.latched |= currentState;

    if (delta) {
      LOG_INF("Encoder fuse status: %02x -> %02x", oldState, currentState);

      if (!currentState) {
        k_timer_start(&gFuseResetTimer, K_SECONDS(5), K_FOREVER);
      }
    }
  }
  k_mutex_unlock(&gState.lock);
}

/**
 * @brief Fuse GPIO interrupt callback
 */
static void fuse_gpio_irq_handler(const struct device *dev, struct gpio_callback *cb, uint32_t pins) {
  k_work_submit(&gFuseWork);
}

/**
 * @brief Reset fuse latch state
 */
static void fuse_latch_timer_expiration(struct k_timer *) {
  LOG_INF("clearing encoder fuse faults");
  encoder_supervisor_reset_faulted();
}

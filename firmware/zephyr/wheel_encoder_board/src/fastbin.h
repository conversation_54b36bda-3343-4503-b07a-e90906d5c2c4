#pragma once
#include <stdint.h>

#define FASTBIN_BROADCAST_COUNT 5
#define FASTBIN_BROADCAST_DELAY 5

typedef struct __attribute__((packed)) fastbin_Header {
  uint32_t request_id;
  uint32_t opcode;
} fastbin_Header;

typedef struct __attribute__((packed)) fastbin_Rotary_Snapshot_Request {
  uint64_t first_us;
  uint64_t last_us;
} fastbin_Rotary_Snapshot_Request;

typedef struct __attribute__((packed)) fastbin_Rotary_Reply {
  uint64_t usec;
  int32_t front_left_ticks;
  int32_t front_right_ticks;
  int32_t back_left_ticks;
  int32_t back_right_ticks;
} fastbin_Rotary_Reply;

typedef struct __attribute__((packed)) fastbin_Rotary_Snapshot_Reply {
  fastbin_Rotary_Reply first_before;
  fastbin_Rotary_Reply first_after;
  fastbin_Rotary_Reply last_before;
  fastbin_Rotary_Reply last_after;
  fastbin_Rotary_Snapshot_Request request;
} fastbin_Rotary_Snapshot_Reply;

#define ROTARY_SNAPSHOT_OPCODE 0x01
#define ROTARY_SNAPSHOT_REPLY_OPCODE 0x02
#define ROTARY_TICKS_OPCODE 0x03
#define ROTARY_TICKS_REPLY_OPCODE 0x04
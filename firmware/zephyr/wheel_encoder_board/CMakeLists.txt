cmake_minimum_required(VERSION 3.13.1)

find_package(Zephyr REQUIRED HINTS $ENV{ZEPHYR_BASE})
project(wheel_encoder_board)

# build protobuf defs separately
set(ROOT_PATH ../../..)
set(GENERATED_PROTO_PATH ${ROOT_PATH}/generated/lib/drivers/nanopb/proto)
file(GLOB PROTO_SOURCES CONFIGURE_DEPENDS ${GENERATED_PROTO_PATH}/*.c)
file(GLOB PROTO_HEADERS CONFIGURE_DEPENDS ${GENERATED_PROTO_PATH}/*.h)

zephyr_library_named(protobufs)
target_sources(protobufs PRIVATE
    ${PROTO_SOURCES} ${PROTO_HEADERS}
)
target_include_directories(protobufs PUBLIC ${ROOT_PATH})

# build app
target_sources(app PRIVATE
    src/main.c
    src/fastbin.h
    src/qdec.c
    src/qdec.h
    src/udp_server.c
    src/udp_server.h
)

target_link_libraries(app PRIVATE protobufs)

# wheel encoder board with cool lights
if("${CMAKE_C_FLAGS}" MATCHES "USE_REAPER=1")
    target_sources(app PRIVATE
        src/blinkenlights.c
        src/blinkenlights.h
        src/encoder_supervisor.c
        src/encoder_supervisor.h
        src/sensors.c
        src/sensors.h
    )
endif()

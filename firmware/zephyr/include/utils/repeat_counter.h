#pragma once

#include <zephyr/logging/log.h>
#include <zephyr/logging/log_backend.h>
#include <zephyr/logging/log_ctrl.h>

/**
>>>>>>> 7836684d5 (more pr stuff)
 * @brief LOG_INF() a uint32_t variable, but only when it changes
 *
 * @note an initial value was required for the static U32 the macro declares. 0xFFFFFFFF was used.
 *       If the variable which you're loggging on change starts out w/ value 0xFFFFFFFF, this won't log that first
 * setting of the value to all 0xFF.
 */
#define LOG_ON_CHANGE(var, val)                                                                                        \
  do {                                                                                                                 \
    static uint32_t prev_##var = 0xFFFFFFFF;                                                                           \
    if ((val) != prev_##var) {                                                                                         \
      prev_##var = (val);                                                                                              \
      LOG_INF("var: " #var " changed to %d", (val));                                                                   \
    }                                                                                                                  \
  } while (0)

/**
 * @brief Run the statement the first time, and then not on any subsequent calls.
 */
#define DO_ONCE(code)                                                                                                  \
  do {                                                                                                                 \
    static bool done_##__LINE__ = false;                                                                               \
    if (!done_##__LINE__) {                                                                                            \
      done_##__LINE__ = true;                                                                                          \
      code;                                                                                                            \
    }                                                                                                                  \
  } while (0)

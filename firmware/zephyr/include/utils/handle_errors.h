#define HANDLE_UNLIKELY(x)                                                                                             \
  {                                                                                                                    \
    int __ret = (x);                                                                                                   \
    if (__ret < 0) {                                                                                                   \
      LOG_ERR("Unlikely error happened, [%s] @ %s:%d returned error: %d", Z_STRINGIFY(x), __FILE__, __LINE__, __ret);  \
      return __ret;                                                                                                    \
    }                                                                                                                  \
  }

#define HANDLE_UNLIKELY_BOOL(x, err)                                                                                   \
  {                                                                                                                    \
    bool __ret = (x);                                                                                                  \
    if (!__ret) {                                                                                                      \
      LOG_ERR("Unlikely error happened, [%s] @ %s:%d was false", Z_STRINGIFY(x), __FILE__, __LINE__);                  \
      return -err;                                                                                                     \
    }                                                                                                                  \
  }

#define HANDLE_CRITICAL(x)                                                                                             \
  {                                                                                                                    \
    int __ret = (x);                                                                                                   \
    if (__ret < 0) {                                                                                                   \
      LOG_ERR("Critical error happened, [%s] @ %s:%d returned error: %d", Z_STRINGIFY(x), __FILE__, __LINE__, __ret);  \
      k_msleep(3000);                                                                                                  \
      k_panic();                                                                                                       \
    }                                                                                                                  \
  }

#define HANDLE_CRITICAL_BOOL(x)                                                                                        \
  {                                                                                                                    \
    int __ret = (x);                                                                                                   \
    if (!__ret) {                                                                                                      \
      LOG_ERR("Critical error happened, [%s] @ %s:%d was false", Z_STRINGIFY(x), __FILE__, __LINE__);                  \
      k_msleep(3000);                                                                                                  \
      k_panic();                                                                                                       \
    }                                                                                                                  \
  }

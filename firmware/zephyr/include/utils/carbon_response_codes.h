/*
 * This enum should contain all error codes for the firmware for all boards.
 */
#pragma once
typedef enum {
  CARBON_RESPONSE_OK = 0,
  CARBON_ERROR_UNKNOWN = 1,

  CARBON_LASER_FAILED_TO_SET_STATE = 104,
  CARB<PERSON>_LASER_FAILED_TO_SET_INTENSITY = 105,
  CARBON_LASER_INVALID_INTENSITY = 106,

  <PERSON><PERSON><PERSON>_GIMBAL_FAILED_TO_FIND_SERVO = 201,

  CARBON_SERVO_DELAYED_OK = 301,

  CARBON_CAN_ERROR_UNKNOWN = 600,
  CARBON_CAN_OPEN_AWAIT_TIMEOUT = 604,
  CARBON_CAN_OPEN_SDO_DOWNLOAD_BAD_REPLY = 605,
  CARBON_CAN_OPEN_SDO_UPLOAD_BAD_REPLY = 606,
  CARBON_CAN_OPEN_RESET_COMMS_BAD_REPLY = 607,
  CARBON_CAN_OPEN_RESET_TIMEOUT = 609,
  CARBON_CAN_OPEN_LSS_ERROR = 610,

  CARBON_EPOS_ERROR_UNKNOWN = 700,
  CARBON_EPOS_SETTLE_TIMEOUT = 702,
  CARBON_EPOS_FIND_HARD_LIMITS_MAX_RETRIES = 703,
  CARBON_EPOS_HARD_LIMITS_RANGE_TOO_SMALL = 704,
  CARBON_EPOS_FIND_HARD_LIMITS_FAIL = 705,
  CARBON_EPOS_FIND_LIMIT_SWITCH_FAIL = 706,
  CARBON_EPOS_INVALID_HOMING_PARAMS = 707,
  CARBON_EPOS_POS_VEL_TIMEOUT = 709,
  CARBON_EPOS_NOT_SUPPORTED = 710,

  CARBON_EPOS_SERVO_NODE_CONFIGURATION_FAIL = 805,
  CARBON_EPOS_SERVO_MOVE_OUT_OF_RANGE = 806,

  CARBON_FH_SERVO_NODE_CONFIGURATION_FAIL = 905,
  CARBON_FH_SERVO_NODE_CONFIGURATION_INCORRECT = 906,

} CARBON_RESPONSE_CODE;

#define RETURN_CODE_IF_NOT_OK(x)                                                                                       \
  do {                                                                                                                 \
    CARBON_RESPONSE_CODE response = x;                                                                                 \
    if (response != CARBON_RESPONSE_OK) {                                                                              \
      LOG_INF("err %d: line %d", response, __LINE__);                                                                  \
      return response;                                                                                                 \
    }                                                                                                                  \
  } while (0);

#define RETURN_SPECIFIED_IF_NOT_OK(x, code)                                                                            \
  do {                                                                                                                 \
    CARBON_RESPONSE_CODE response = x;                                                                                 \
    if (response != CARBON_RESPONSE_OK) {                                                                              \
      LOG_INF("err %d: line %d", response, __LINE__);                                                                  \
      return code;                                                                                                     \
    }                                                                                                                  \
  } while (0);

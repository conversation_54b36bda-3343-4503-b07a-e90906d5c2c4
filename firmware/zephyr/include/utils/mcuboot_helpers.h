/**
 * @file
 * @brief Helper methods for working with MCUBoot
 */
#pragma once

#include <stddef.h>
#include <stdint.h>

#include <devicetree.h>
#include <dfu/mcuboot.h>
#include <storage/flash_map.h>
#include <utils/handle_errors.h>

/**
 * @brief Get the version of the running firmware from its image header
 */
static inline int mcuboot_get_running_version(unsigned int *outMajor, unsigned int *outMinor, unsigned int *outRev,
                                              unsigned int *outBuild) {
  struct mcuboot_img_header hdr;

  if (!outMajor && !outMinor && !outRev && !outBuild) {
    return -EFAULT;
  }

  // TODO: validate fa_id
  HANDLE_UNLIKELY(boot_read_bank_header(FLASH_AREA_ID(image_0), &hdr, sizeof(hdr)));

  if (outMajor) {
    *outMajor = hdr.h.v1.sem_ver.major;
  }
  if (outMinor) {
    *outMinor = hdr.h.v1.sem_ver.minor;
  }
  if (outRev) {
    *outRev = hdr.h.v1.sem_ver.revision;
  }
  if (outBuild) {
    *outBuild = hdr.h.v1.sem_ver.build_num;
  }

  return 0;
}

/**
 * @brief
 * @file Helper methods to work with floating point numbers
 */
#pragma once

#include <ctype.h>
#include <errno.h>
#include <stddef.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>

#define PRECISION64 (100000000000000000ULL)

/**
 * @brief Convert a string to floating point number
 *
 * @param input Input string, formatted as a decimal floating point number
 * @param out Variable to receive the converted number
 *
 * @return 0 on conversion success or a negative error
 */
static int util_strtod(const char *input, double *out) {
  char *pos, *end, buf[24];
  long val;
  int64_t base = PRECISION64, sign = 1;
  int64_t val1, val2;

  if (!input || !out) {
    return -EINVAL;
  }

  strncpy(buf, input, sizeof(buf) - 1);
  buf[sizeof(buf) - 1] = '\0';

  if (strchr(buf, '-')) {
    sign = -1;
  }

  pos = strchr(buf, '.');
  if (pos) {
    *pos = '\0';
  }

  errno = 0;
  val = strtol(buf, &end, 10);
  if (errno || *end) {
    return -EINVAL;
  }

  val1 = (int64_t)val;
  val2 = 0;

  if (!pos) {
    *out = (double)val1;
    return 0;
  }

  while (*(++pos) && base > 1 && isdigit((unsigned char)*pos)) {
    val2 = val2 * 10 + (*pos - '0');
    base /= 10;
  }

  val2 *= sign * base;

  *out = (double)val1 + (double)val2 / PRECISION64;

  return !*pos || base == 1 ? 0 : -EINVAL;
}

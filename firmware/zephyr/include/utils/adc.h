/**
 * @file
 * @brief Helpers for working with ADC channels
 */
#pragma once

#include <math.h>
#include <stdint.h>

#include <device.h>
#include <devicetree.h>
#include <drivers/adc.h>

// XXX: ugly hack to get ADC_DT_SPEC_GET_BY_NAME
#ifndef ADC_DT_SPEC_GET_BY_NAME
#define ADC_DT_SPEC_GET_BY_NAME(node_id, name)                                                                         \
  ADC_DT_SPEC_STRUCT(DT_IO_CHANNELS_CTLR_BY_NAME(node_id, name), DT_IO_CHANNELS_INPUT_BY_NAME(node_id, name))
#endif

#ifndef ADC_DT_SPEC_INST_GET_BY_NAME
#define ADC_DT_SPEC_INST_GET_BY_NAME(inst, name) ADC_DT_SPEC_GET_BY_NAME(DT_DRV_INST(inst), name)
#endif

// get ADC definition by name or a NULL value
#ifndef ADC_DT_SPEC_GET_BY_NAME_OR_NULL
#define ADC_DT_SPEC_GET_BY_NAME_OR_NULL(node_id, name)                                                                 \
  COND_CODE_1(DT_PROP_HAS_NAME(node_id, io_channels, name), (ADC_DT_SPEC_GET_BY_NAME(node_id, name)), ({NULL}))
#endif

#ifndef ADC_DT_SPEC_INST_GET_BY_NAME_OR_NULL
#define ADC_DT_SPEC_INST_GET_BY_NAME_OR_NULL(inst, name) ADC_DT_SPEC_GET_BY_NAME_OR_NULL(DT_DRV_INST(inst), name)
#endif

/**
 * @brief Convert raw ADC code to millivolts (with floating precision)
 *
 * This is the same as `adc_raw_to_millivolts_dt` but can represent fractional millivolt values to
 * take full advantage of the full resolution of the ADC.
 *
 * @remark This works only for single ended channels.
 */
static inline float adc_raw_to_millivolts_dt_float(const struct adc_dt_spec *spec, int32_t rawCode) {
  double vref;

  // abort if channel config is unsupported
  if (spec->channel_cfg.differential) {
    return __builtin_nanf("");
  } else if (spec->channel_cfg.gain != ADC_GAIN_1) {
    return __builtin_nanf("");
  }

  // figure out vref (in mV)
  if (spec->channel_cfg.reference == ADC_REF_INTERNAL) {
    vref = (double)adc_ref_internal(spec->dev);
  } else {
    vref = spec->vref_mv;
  }

  // convert ADC code into voltage, in mv
  const uint32_t highest = (1 << spec->resolution);
  return (((double)rawCode) / ((double)highest)) * vref;
}

/**
 * @brief Calculate input voltage of resistor divider
 *
 * @param r1 Resistance of high side resistor, ohms
 * @param r2 Resistance of low side resistor, ohms
 * @param mv ADC voltage (mv)
 *
 * @return Input voltage of resistor divider (volts)
 */
static inline float get_divider_voltage(const uint32_t r1, const uint32_t r2, const float mv) {
  return (((double)mv / 1000.0) * ((double)r1 + (double)r2)) / ((double)r2);
}

/**
 * @brief Calculate resistor value, given ADC voltage
 *
 * Given a fixed upper (R1) resistor, calculate the approximate resistance (in ohms) of the low side
 * resistor. This is useful for reading thermistors.
 *
 * @param r1 Upper resistor value (Ohms)
 * @param mv Measured voltage between midpoint and ground, mv
 * @param vref Voltage fed into top of resistor divider, in mv
 */
static inline float get_divider_ohms(const uint32_t r1, const float mv, const float vref) {
  const double vsense = ((double)mv / 1000.0);
  return vsense / ((((double)vref / 1000.0) - vsense) / ((double)r1));
}

/**
 * @brief Calculate thermistor temperature using Steinhart-Hart
 *
 * Given the temperature of the reference point (in °C), its resistance, and B coefficient, this
 * will approximate the temperature measured by the thermistor relatively accurately.
 */
static inline double get_thermistor_temp(const double refTemp, const double refResistance, const double B,
                                         const double Rtherm) {
  // Kelvin temperature at 0°C
  static const double kCToK = 273.15;

  const double T0 = refTemp + kCToK;

  return (B / log(Rtherm / (refResistance * exp((-B) / T0)))) - kCToK;
}

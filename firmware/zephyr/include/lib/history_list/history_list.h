#pragma once
#include <stdint.h>
#include <sys/__assert.h>
#include <utils/alignment.h>

typedef struct {
  uint64_t *usec;
  uint8_t *record;
} history_record;

typedef struct {
  volatile size_t last;
  size_t record_size;
  size_t num_elements;
  uint8_t *records;
} history_list;

#define HISTORY_LIST_DEFINE(name, num_records, size_record)                                                            \
  uint8_t __aligned(4) history_list_buf_##name[(num_records)*WORD_ROUND(sizeof(uint64_t) + (size_record))];            \
  history_list name = {.last = 0,                                                                                      \
                       .record_size = WORD_ROUND(size_record),                                                         \
                       .num_elements = (num_records),                                                                  \
                       .records = history_list_buf_##name};

#define HISTORY_LIST_PAST_HIST -1
#define HISTORY_LIST_FUTURE_HIST -2

void history_list_init(history_list *h);
void history_list_add_record(history_list *h, const history_record *record);
int history_list_get_latest(history_list *h, history_record *output);
int history_list_get_oldest(history_list *h, history_record *output);
int history_list_get_best(history_list *h, uint64_t usec, history_record *output);
int history_list_get_next(history_list *h, int index, history_record *output);
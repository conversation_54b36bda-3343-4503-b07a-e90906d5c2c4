#pragma once

#define COMPARE_MSG_ID(actual, expected) (actual == expected)

// Wheel angle read messages
#define LHM_WHEEL_ANGLE_ID (0x1022F013)
#define WHEEL_ANGLE_ID (0x1023F013)
#define WHEEL_ANGLE_FIRST_BYTE (0x46)

// Ground Speed read Messages
#define LH_GROUND_SPEED_ID (0x18FEF147)
#define LH_GROUND_SPEED_FIRST_BYTE (0xFF)

#define RH_GROUND_SPEED_ID (0x0CFFF413)
#define RH_GROUND_SPEED_FIRST_BYTE (0x63)

#define CPRO_GROUND_SPEED_ID (0x18FFF413)
#define CPRO_GROUND_SPEED_FIRST_BYTE (0x63)

#define LHM_GROUND_SPEED_ID (0x18FFF413)
#define LHM_GROUND_SPEED_FIRST_BYTE (0x63)

// Speed wheel lever write messages
#define SPEED_WHEEL_LEVER_ID (0x0CFFFE8C)
#define SPEED_WHEEL_LEVER_FIRST_BYTE (0x8C)
#define SPEED_WHEEL_OVERFLOW (0xFB)

#define LHM_SPEED_WHEEL_LEVER_ID (0x14FFFE8C)
#define LHM_SPEED_WHEEL_LEVER_FIRST_BYTE (0x8C)

// Hitch write messages
#define HITCH_LIFT_ID (0x0CFFFE8C)
#define HITCH_LIFT_FIRST_BYTE (0x8D)

#define LHM_HITCH_LIFT_ID (0x14FFFE8C)
#define LHM_HITCH_LIFT_FIRST_BYTE (0x8D)

#define CPRO_HITCH_LIFT_ID (0x14FFFE8C)
#define CPRO_HITCH_LIFT_FIRST_BYTE (0x8D)

// SCV write messages
#define ANNOYING_SCV_ID (0x1CE6FF8C)
#define ANNOYING_SCV_FIRST_BYTE (0x26)
#define ANNOYING_SCV1_SECOND_BYTE (0x4D)
#define ANNOYING_SCV2_SECOND_BYTE (0x4E)
#define ANNOYING_SCV3_SECOND_BYTE (0x4F)
#define SCV_LOCK_ID (0x08FFF88C)
#define SCV_LOCK_FIRST_BYTE (0x92)

#define RH_SCV_ID (0x08FFFF8C)
#define RH_SCV_FIRST_BYTE (0x55)

// #define CPRO_SCV_ID (0x1CE6FFF6)
// #define CPRO_SCV_FIRST_BYTE (0x26)
// #define CPRO_SCV1_SECOND_BYTE (0x3E)
// #define CPRO_SCV2_SECOND_BYTE (0x3F)

// Gear write messages
#define LH_GEAR_SHIFT_ID (0xCFFFE91) // Why is this only 7 digits? And no first byte?
#define LH_GEAR_SELECT_PARK (0xF4)
#define LH_GEAR_SELECT_FWD (0xF2)
#define LH_GEAR_SELECT_RVR (0xF3)
#define LH_GEAR_SELECT_SCROLL (0xFC)
#define LH_GEAR_SELECT_NEUTRAL (0xF1)
#define LH_GEAR_FWDRVR_NONE (0xF0)
#define LH_GEAR_FWDRVR_FWD (0xF4)
#define LH_GEAR_FWDRVR_RVR (0xF1)

#define RH_GEAR_SHIFT_ID (0x0CFFFE8C)
#define RH_GEAR_SHIFT_FIRST_BYTE (0x8C)
#define RH_GEAR_SELECT_PARK (0xF4)
#define RH_GEAR_SELECT_ZERO (0xFC)
#define RH_GEAR_SELECT_F1 (0x92)
#define RH_GEAR_SELECT_RVR (0x93)
#define RH_GEAR_SELECT_NEUTRAL (0xF7)

// Transmission read messages
#define LH_TRANSMISSION_INFO_ID (0x18FFFF05)
#define LH_TRANSMISSION_INFO_FIRST_BYTE (0x20)

#define RH_TRANSMISSION_INFO_ID (0x18FFFF05)
#define RH_TRANSMISSION_INFO_FIRST_BYTE (0x20)

#define LHM_TRANSMISSION_INFO_ID (0x18FFF805)
#define LHM_TRANSMISSION_INFO_FIRST_BYTE (0xB7)

#define CPRO_TRANSMISSION_INFO_ID (0x18FFF805)
#define CPRO_TRANSMISSION_INFO_FIRST_BYTE (0xB7)

// Gear read messages
#define LH_GEAR_INFO_ID (0x0CFFFB05)
#define LH_GEAR_INFO_FIRST_BYTE (0x00)

#define CPRO_GEAR_INFO_ID (0x0CFFF805)
#define CPRO_GEAR_INFO_FIRST_BYTE (0xAE)

#define LHM_GEAR_INFO_ID (0x0CFFF805)
#define LHM_GEAR_INFO_FIRST_BYTE (0xAE)

// Brake read messages
#define LH_BRAKE_PEDAL_ID (0x0CFFFF31)
#define LH_BRAKE_PEDAL_FIRST_BYTE (0x5E)

#define RH_BRAKE_PEDAL_ID (0x08EFFF05)
#define RH_BRAKE_PEDAL_FIRST_BYTE (0x6E)

#define CPRO_BRAKE_PEDAL_ID (0x08FFFF31)
#define CPRO_BRAKE_PEDAL_FIRST_BYTE (0x5E)

#define LHM_BRAKE_PEDAL_ID (0x08FFFF31)
#define LHM_BRAKE_PEDAL_FIRST_BYTE (0x5E)

#define JD_8XPRO_BRAKE_PEDAL_ID (0x0CEFFF05)
#define JD_8XPRO_BRAKE_PEDAL_FIRST_BYTE (0x6E)

// Lift read messages
#define LH_LIFT_ANGLE_ID (0x1023F023)
#define LH_LIFT_ANGLE_FIRST_BYTE (0x43)

#define RH_LIFT_ANGLE_ID (0x18FFFF23)
#define RH_LIFT_ANGLE_FIRST_BYTE (0x5F)

#define CPRO_LIFT_ANGLE_ID (0x1023F023)
#define CPRO_LIFT_ANGLE_FIRST_BYTE (0x43)

#define LHM_LIFT_ANGLE_ID (0x1023F023)
#define LHM_LIFT_ANGLE_FIRST_BYTE (0x43)

// Fuel read message
#define FUEL_SENSE_ID (0x18FEFC47)
#define FUEL_SENSE_FIRST_BYTE (0xFF)

// CPRO specific messages
#define CPRO_JOYSTICK_ID (0x0CFFF8F6)
#define CPRO_JOYSTICK_FIRST_BYTE (0xB6)
#define CPRO_CHECKSUM_ID (0x0CFFF8F6)
#define CPRO_CHECKSUM_FIRST_BYTE (0x7E)
#define CPRO_JOYSTICK_COPY_ID (0x0CFFF88C)
#define CPRO_JOYSTICK_COPY_FIRST_BYTE (0xB5)

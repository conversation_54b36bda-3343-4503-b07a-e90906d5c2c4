/**
 * @file
 *
 * @brief Status indicator management library
 *
 * Helpers to set the status of the indicator LEDs standard on new Carbon boards: this consists of
 * a primary RGB status indicator, and an optional debug/heartbeat single color LED.
 */
#pragma once

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

#define LED_COLOR_OFF 0x000000
#define LED_COLOR_RED 0xff0000
#define LED_COLOR_YELLOW 0xffff00
#define LED_COLOR_PURPLE 0xff00ff
#define LED_COLOR_BLUE 0x0000ff
#define LED_COLOR_AQUA 0x00ffff
#define LED_COLOR_GREEN 0x00ff00
#define LED_COLOR_WHITE 0xffffff

typedef struct leds_pattern {
  uint32_t reserved : 7;
  // When set, the color is updated; otherwise, it's just a delay
  uint32_t setColor : 1;
  // RGB color to set on the LED
  uint32_t color : 24;
  // time delay, in msec, before advancing to next pattern element
  uint32_t delay;
} leds_pattern_t;

#define LEDS_MAKE_PATTERN_COLOR(color, delay)                                                                          \
  { 0, 1, color, delay }
#define LEDS_MAKE_PATTERN_DELAY(delay)                                                                                 \
  { 0, 0, 0, delay }

int leds_init();

int leds_set_debug(const bool on);
int leds_set_status(const uint32_t color);

int leds_status_pattern_start(const leds_pattern_t *start, const size_t length, const bool repeat);
int leds_status_pattern_stop();

#if IS_ENABLED(CONFIG_LIB_STATUS_LED_COLOR_CONVERSION)
uint32_t leds_hsi_to_rgb(float h, float s, float i);
#endif

#pragma once
#include <net/socket.h>
#include <sys/__assert.h>
#include <utils/alignment.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

#pragma pack(1)
typedef struct {
  struct sockaddr client_addr;
  socklen_t client_addr_len;
} udp_msg_metadata;
typedef struct {
  udp_msg_metadata metadata;
  uint16_t len;
} udp_buf_header;
#pragma pack(0)

/*
 * Note: Do not directly interact with the message queues, as they are designed to only be modified
 * via the existing API, as this should allow for always being able to put msgs into the queues when needed.
 */
typedef struct udp_server {
  struct k_msgq *rx_avail_q; // Store available buffers to write from wire
  struct k_msgq *rx_used_q;  // Store buffers with content to be read
  uint8_t *rx_data;          // buffer to hold all rx data
  uint16_t rx_size;          // Max size of each element in the rx buffer
  int sock;                  // Store the socket while running
} udp_server;

#define UDP_SERVER_DEFINE(server_name, rx_count, rx_elem_size, thread_priority)                                        \
  K_MSGQ_DEFINE(udp_rx_avail_msgq_##server_name, sizeof(uint8_t *), rx_count, sizeof(uint8_t *));                      \
  K_MSGQ_DEFINE(udp_rx_used_msgq_##server_name, sizeof(uint8_t *), rx_count, sizeof(uint8_t *));                       \
  uint8_t __aligned(4) udp_rx_buf_##server_name[(rx_count)*WORD_ROUND((rx_elem_size) + sizeof(udp_buf_header))];       \
  udp_server server_name = {.rx_avail_q = &udp_rx_avail_msgq_##server_name,                                            \
                            .rx_used_q = &udp_rx_used_msgq_##server_name,                                              \
                            .rx_data = udp_rx_buf_##server_name,                                                       \
                            .rx_size = WORD_ROUND((rx_elem_size) + sizeof(udp_buf_header))};                           \
  K_THREAD_DEFINE(udp_rx_thread_id_##server_name, 1024, udp_rx, &server_name, NULL, NULL, thread_priority, 0,          \
                  K_TICKS_FOREVER);

#define UDP_SERVER_START(server_name, port)                                                                            \
  HANDLE_CRITICAL(udp_init(&server_name, port));                                                                       \
  k_thread_name_set(udp_rx_thread_id_##server_name, "udp_rx_##server_name");                                           \
  k_thread_start(udp_rx_thread_id_##server_name);

int udp_init(udp_server *server, int port);
void udp_rx(void *arg);
// Copies data out of rx buffer
uint16_t udp_get_data(udp_server *server, uint8_t *buf, uint16_t buf_size, udp_msg_metadata *metadata,
                      uint32_t timeout_ms); // 0 timeout means forever
// Reads data without copying most of data(metadata is copied)
uint16_t udp_get_data_no_copy(udp_server *server, uint8_t **buf_ptr, udp_msg_metadata *metadata,
                              uint32_t timeout_ms); // 0 timeout means forever

// Must be called after udp_get_data_no_copy to release data for use again
int udp_get_data_release(udp_server *server, uint8_t *buf);
// Send data over network socket
int udp_tx(udp_server *server, uint8_t *buf, uint16_t buf_len, udp_msg_metadata *metadata);

inline uint16_t udp_buffer_size(udp_server *server) { return server->rx_size - sizeof(udp_buf_header); }
#pragma once

#include <device.h>

typedef void (*zlcb_callback_t)(const void *arg);

typedef int (*zlcb_set_callback_t)(const struct device *dev, uint32_t period_cycles, zlcb_callback_t callback,
                                   void *arg);

typedef int (*zlcb_get_cycles_per_sec_t)(const struct device *dev, uint64_t *cycles);

__subsystem struct zlcb_driver_api {
  zlcb_set_callback_t set_callback;
  zlcb_get_cycles_per_sec_t get_cycles_per_sec;
};

/**
 * @brief Set the callback to be called with zero latency every N timer cycles.
 * *
 * @param dev Pointer to the device structure for the driver instance.
 * @param period Period (in clock cycle) to call the callback. HW specific.
 * @param callback Callback to call.
 * @param arg Argument to the callback.
 *
 * @retval 0 If successful.
 * @retval Negative errno code if failure.
 */
__syscall int zlcb_set_callback_cycles(const struct device *dev, uint32_t period, zlcb_callback_t callback, void *arg);

static inline int z_impl_zlcb_set_callback_cycles(const struct device *dev, uint32_t period, zlcb_callback_t callback,
                                                  void *arg) {
  struct zlcb_driver_api *api;

  api = (struct zlcb_driver_api *)dev->api;
  return api->set_callback(dev, period, callback, arg);
}

/**
 * @brief Get the clock rate (timer cycles per second) for a single zero latency callback.
 *
 * @param dev Pointer to the device structure for the driver instance.
 * @param cycles Pointer to the memory to store clock rate (cycles per sec).
 *		 HW specific.
 *
 * @retval 0 If successful.
 * @retval Negative errno code if failure.
 */
__syscall int zlcb_get_cycles_per_sec(const struct device *dev, uint64_t *cycles);

static inline int z_impl_zlcb_get_cycles_per_sec(const struct device *dev, uint64_t *cycles) {
  struct zlcb_driver_api *api;

  api = (struct zlcb_driver_api *)dev->api;
  return api->get_cycles_per_sec(dev, cycles);
}

/**
 * @brief Set the callback to be called with zero latency every N us.
 * *
 * @param dev Pointer to the device structure for the driver instance.
 * @param period Period (in us) to call the callback.
 * @param callback Callback to call.
 * @param arg Argument to the callback.
 *
 * @retval 0 If successful.
 * @retval Negative errno code if failure.
 */
__syscall int zlcb_set_callback_usec(const struct device *dev, uint32_t period, zlcb_callback_t callback, void *arg);

static inline int z_impl_zlcb_set_callback_usec(const struct device *dev, uint32_t period, zlcb_callback_t callback,
                                                void *arg) {
  uint64_t period_cycles, cycles_per_sec;

  if (zlcb_get_cycles_per_sec(dev, &cycles_per_sec) != 0) {
    return -EIO;
  }

  period_cycles = (period * cycles_per_sec) / USEC_PER_SEC;
  if (period_cycles >= ((uint64_t)1 << 32)) {
    return -ENOTSUP;
  }

  if (period > 0 && period_cycles == 0) {
    /* nonzero period was rounded down to zero */
    return -ENOTSUP;
  }

  return zlcb_set_callback_cycles(dev, period_cycles, callback, arg);
}

#include <syscalls/zlcb.h>

#pragma once
#include <stdint.h>
#include <zephyr/device.h>

typedef int (*lpm_get_power)(const struct device *dev, float *output);
typedef int (*lpm_get_raw_readings)(const struct device *dev, int32_t *reading1, int32_t *reading2);
typedef int (*lpm_get_current)(const struct device *dev, float *output);
typedef int (*lpm_get_status)(const struct device *dev, bool *output);

struct laser_power_meter_api {
  lpm_get_power get_power;
  lpm_get_raw_readings get_raw_readings;
  lpm_get_current get_current;
  lpm_get_status get_status;

  int (*get_raw_photodiode)(const struct device *dev, uint32_t *outRaw);
};

static inline int laser_power_meter_get_power(const struct device *dev, float *output) {
  struct laser_power_meter_api *api;

  api = (struct laser_power_meter_api *)dev->api;
  return api->get_power(dev, output);
}

static inline int laser_power_meter_get_current(const struct device *dev, float *output) {
  struct laser_power_meter_api *api;

  api = (struct laser_power_meter_api *)dev->api;
  return api->get_current(dev, output);
}

static inline int laser_power_meter_get_status(const struct device *dev, bool *output) {
  struct laser_power_meter_api *api;

  api = (struct laser_power_meter_api *)dev->api;
  return api->get_status(dev, output);
}

static inline int laser_power_meter_get_raw_readings(const struct device *dev, int32_t *reading1, int32_t *reading2) {
  struct laser_power_meter_api *api;

  api = (struct laser_power_meter_api *)dev->api;
  return api->get_raw_readings(dev, reading1, reading2);
}

/**
 * @brief Get the voltage on the photodiode input pin
 *
 * @param outVoltage Variable to receive voltage, in mV
 */
static inline int laser_power_meter_get_raw_photodiode(const struct device *dev, uint32_t *outVoltage) {
  const struct laser_power_meter_api *api = (struct laser_power_meter_api *)dev->api;
  return api->get_raw_photodiode(dev, outVoltage);
}

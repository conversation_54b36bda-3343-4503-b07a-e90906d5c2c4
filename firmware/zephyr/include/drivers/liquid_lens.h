#pragma once
#include <device.h>
#include <stdint.h>

typedef int (*liquid_lens_set)(const struct device *dev, uint8_t channel, uint16_t input);
typedef int (*liquid_lens_get)(const struct device *dev, uint8_t channel, uint16_t *output);

struct liquid_lens_api {
  liquid_lens_set set;
  liquid_lens_get get;
};

static inline int liquid_lens_set_value(const struct device *dev, uint8_t channel, uint16_t input) {
  struct liquid_lens_api *api;

  api = (struct liquid_lens_api *)dev->api;
  return api->set(dev, channel, input);
}

static inline int liquid_lens_get_value(const struct device *dev, uint8_t channel, uint16_t *output) {
  struct liquid_lens_api *api;

  api = (struct liquid_lens_api *)dev->api;
  return api->get(dev, channel, output);
}
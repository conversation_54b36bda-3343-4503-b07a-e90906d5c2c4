#pragma once

#include <device.h>
#include <devicetree.h>
#include <dt-bindings/pwm/pwm.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef uint8_t combo_pwm_flags_t;

typedef int (*combo_pwm_pin_set_t)(const struct device *dev, uint32_t pwm, uint32_t period_cycles,
                                   uint32_t pulse_on_cycle, uint32_t pulse_off_cycle, combo_pwm_flags_t flags);

typedef int (*combo_pwm_get_cycles_per_sec_t)(const struct device *dev, uint32_t pwm, uint64_t *cycles);

__subsystem struct combo_pwm_driver_api {
  combo_pwm_pin_set_t pin_set;
  combo_pwm_get_cycles_per_sec_t get_cycles_per_sec;
};

/**
 * @brief Set the period and pulse on and off cycles for a single combined PWM output.
 * *
 * @param dev Pointer to the device structure for the driver instance.
 * @param pwm PWM pin.
 * @param period Period (in clock cycle) set to the PWM. HW specific.
 * @param pulse_on Pulse on (in clock cycle) set to the PWM. HW specific.
 * @param pulse_off Pulse off (in clock cycle) set to the PWM. HW specific.
 * @param flags Flags for pin configuration (polarity).
 *
 * @retval 0 If successful.
 * @retval Negative errno code if failure.
 */
__syscall int combo_pwm_pin_set_cycles(const struct device *dev, uint32_t pwm, uint32_t period, uint32_t pulse_on,
                                       uint32_t pulse_off, combo_pwm_flags_t flags);

static inline int z_impl_combo_pwm_pin_set_cycles(const struct device *dev, uint32_t pwm, uint32_t period,
                                                  uint32_t pulse_on, uint32_t pulse_off, combo_pwm_flags_t flags) {
  struct combo_pwm_driver_api *api;

  api = (struct combo_pwm_driver_api *)dev->api;
  return api->pin_set(dev, pwm, period, pulse_on, pulse_off, flags);
}

/**
 * Set the period and pulse on and off times for a single combined PWM output.
 * Picks channel and flags from combo-pwms child node passed as an input.
 * *
 * @param node Device tree node (combo-pwms child).
 * @param period Period (in clock cycle) set to the PWM. HW specific.
 * @param pulse_on Pulse on (in clock cycle) set to the PWM. HW specific.
 * @param pulse_off Pulse off (in clock cycle) set to the PWM. HW specific.
 *
 * @retval 0 If successful.
 * @retval Negative errno code if failure.
 */
#define DT_COMBO_PWM_PIN_SET_CYCLES(node, period, pulse_on, pulse_off)                                                 \
  safe_combo_pwm_pin_set_cycles(DEVICE_DT_GET(DT_PWMS_CTLR(node)), DT_PWMS_CHANNEL(node), period, pulse_on, pulse_off, \
                                DT_PWMS_FLAGS(node))

static inline int safe_combo_pwm_pin_set_cycles(const struct device *dev, uint32_t pwm, uint32_t period,
                                                uint32_t pulse_on, uint32_t pulse_off, uint8_t flags) {
  if (!device_is_ready(dev)) {
    return -ENODEV;
  }
  return combo_pwm_pin_set_cycles(dev, pwm, period, pulse_on, pulse_off, flags);
}

/**
 * @brief Get the clock rate (cycles per second) for a single combined PWM output.
 *
 * @param dev Pointer to the device structure for the driver instance.
 * @param pwm PWM pin.
 * @param cycles Pointer to the memory to store clock rate (cycles per sec).
 *		 HW specific.
 *
 * @retval 0 If successful.
 * @retval Negative errno code if failure.
 */
__syscall int combo_pwm_get_cycles_per_sec(const struct device *dev, uint32_t pwm, uint64_t *cycles);

static inline int z_impl_combo_pwm_get_cycles_per_sec(const struct device *dev, uint32_t pwm, uint64_t *cycles) {
  struct combo_pwm_driver_api *api;

  api = (struct combo_pwm_driver_api *)dev->api;
  return api->get_cycles_per_sec(dev, pwm, cycles);
}

/**
 * @brief Get the clock rate (cycles per second) for a single combined PWM output.
 * Picks channel from combo-pwms child node passed as an input.
 * *
 * @param node Device tree node (combo-pwms child).
 * @param cycles Pointer to the memory to store clock rate (cycles per sec).
 *		 HW specific.
 *
 * @retval 0 If successful.
 * @retval Negative errno code if failure.
 */
#define DT_COMBO_PWM_GET_CYCLES_PER_SEC(node, cycles)                                                                  \
  safe_combo_pwm_get_cycles_per_sec(DEVICE_DT_GET(DT_PWMS_CTLR(node)), DT_PWMS_CHANNEL(node), cycles)

static inline int safe_combo_pwm_get_cycles_per_sec(const struct device *dev, uint32_t pwm, uint64_t *cycles) {
  if (!device_is_ready(dev)) {
    return -ENODEV;
  }
  return combo_pwm_get_cycles_per_sec(dev, pwm, cycles);
}

/**
 * @brief Set the period and pulse on and off times for a single combined PWM output.
 * Returns an error if specified period results in fractional number of cycles. This
 * helps prevent desynchronization between timers.
 * *
 * @param dev Pointer to the device structure for the driver instance.
 * @param pwm PWM pin.
 * @param period Period (in microseconds) set to the PWM.
 * @param pulse_on Pulse on (in microseconds) set to the PWM.
 * @param pulse_off Pulse off (in microseconds) set to the PWM.
 * @param flags Flags for pin configuration (polarity).
 *
 * @retval 0 If successful.
 * @retval Negative errno code if failure.
 */
static inline int combo_pwm_pin_set_usec(const struct device *dev, uint32_t pwm, uint32_t period, uint32_t pulse_on,
                                         uint32_t pulse_off, combo_pwm_flags_t flags) {
  uint64_t period_cycles, pulse_on_cycle, pulse_off_cycle, cycles_per_sec;

  if (combo_pwm_get_cycles_per_sec(dev, pwm, &cycles_per_sec) != 0) {
    return -EIO;
  }

  period_cycles = (period * cycles_per_sec) / USEC_PER_SEC;
  if (period_cycles >= ((uint64_t)1 << 32)) {
    return -ENOTSUP;
  }

  if (period_cycles * USEC_PER_SEC != period * cycles_per_sec) {
    return -EINVAL;
  }

  pulse_on_cycle = (pulse_on * cycles_per_sec) / USEC_PER_SEC;
  if (pulse_on_cycle >= ((uint64_t)1 << 32)) {
    return -ENOTSUP;
  }

  pulse_off_cycle = (pulse_off * cycles_per_sec) / USEC_PER_SEC;
  if (pulse_off_cycle >= ((uint64_t)1 << 32)) {
    return -ENOTSUP;
  }

  return combo_pwm_pin_set_cycles(dev, pwm, (uint32_t)period_cycles, (uint32_t)pulse_on_cycle,
                                  (uint32_t)pulse_off_cycle, flags);
}

/**
 * Set the period and pulse on and off times for a single combined PWM output.
 * Picks channel and flags from combo-pwms child node passed as an input.
 * *
 * @param node Device tree node (combo-pwms child).
 * @param period Period (in microseconds) set to the PWM.
 * @param pulse_on Pulse on (in microseconds) set to the PWM.
 * @param pulse_off Pulse off (in microseconds) set to the PWM.
 *
 * @retval 0 If successful.
 * @retval Negative errno code if failure.
 */
#define DT_COMBO_PWM_PIN_SET_USEC(node, period, pulse_on, pulse_off)                                                   \
  safe_combo_pwm_pin_set_usec(DEVICE_DT_GET(DT_PWMS_CTLR(node)), DT_PWMS_CHANNEL(node), period, pulse_on, pulse_off,   \
                              DT_PWMS_FLAGS(node))

static inline int safe_combo_pwm_pin_set_usec(const struct device *dev, uint32_t pwm, uint32_t period,
                                              uint32_t pulse_on, uint32_t pulse_off, uint8_t flags) {
  if (!device_is_ready(dev)) {
    return -ENODEV;
  }
  return combo_pwm_pin_set_usec(dev, pwm, period, pulse_on, pulse_off, flags);
}

#ifdef __cplusplus
}
#endif

#include <syscalls/combo_pwm.h>

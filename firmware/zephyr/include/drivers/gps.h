#pragma once

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

#include <zephyr/device.h>

struct gps_query_result {
  bool have_fix;
  bool have_approx_fix;
  double latitude;
  double longitude;
  int height_mm;
  int num_sats;
  double hdop;
  uint64_t timestamp_ms;
  bool time_valid;
  uint8_t fix_type;
  uint8_t fix_flags;
};

typedef struct gps_relposned_result {
  // reference station id
  uint16_t ref_station_id;
  // GPS iTOW (in msec)
  uint32_t gps_itow;

  // North component of relative position vector (mm)
  double rel_pos_north, rel_pos_north_accuracy;
  // East component of relative position vector (mm)
  double rel_pos_east, rel_pos_east_accuracy;
  // Down component of relative position vector (mm)
  double rel_pos_down, rel_pos_down_accuracy;
  // Length of relative position vector (mm)
  double rel_pos_length, rel_pos_length_accuracy;
  // Heading of relative position vector (°)
  double rel_pos_heading, rel_pos_heading_accuracy;

  // A valid GNSS fix is available and was used to generate this data
  uint16_t gnss_valid : 1;
  // Differential corrections were applied for this solution
  uint16_t diff_corrections_applied : 1;
  // Whether relative position vector/accuracies are valid
  uint16_t rel_pos_valid : 1;
  // Whether relative position heading are valid
  uint16_t rel_pos_heading_valid : 1;
  // Receiver is operating in moving base mode
  uint16_t is_moving_base : 1;
  // carrier phase solution available
  uint16_t has_carrier_phase_soln : 1;
  // when set, carrier phase solution is fixed; floating otherwise
  uint16_t carrier_phase_soln_fixed : 1;
} gps_relposned_result_t;

// Config values may be 1, 2, 4, or 8 bytes
typedef enum gps_config_item_data_length {
  kGpsConfigValueLength1,
  kGpsConfigValueLength2,
  kGpsConfigValueLength4,
  kGpsConfigValueLength8,
} gps_config_item_data_length_t;

// A single config key and associated value
typedef struct gps_config_item {
  gps_config_item_data_length_t data_length;
  uint32_t key;

  union {
    uint8_t byte;
    uint16_t word;
    uint32_t dword;
    uint64_t qword;
  } data;
} gps_config_item_t;

typedef void (*gps_pps_callback_t)(void *ctx);

typedef int (*gps_query_t)(const struct device *dev, struct gps_query_result *r);
typedef int (*gps_spartn_t)(const struct device *dev, const uint8_t *data, const size_t size);
typedef int (*gps_rtcm_t)(const struct device *dev, const uint8_t *data, const size_t size);
typedef int (*gps_set_pps_callback_t)(const struct device *dev, gps_pps_callback_t callback, void *callbackCtx);
typedef int (*gps_set_config_t)(const struct device *dev, const gps_config_item_t *items, const size_t numItems);
typedef int (*gps_query_relpos_t)(const struct device *dev, gps_relposned_result_t *outResult);
typedef int (*gps_query_nmea_gga_t)(const struct device *dev, char *outBuffer, const size_t outBufferSize);

__subsystem struct gps_driver_api {
  gps_query_t query_pvt;
  gps_spartn_t spartn;
  gps_rtcm_t rtcm;
  gps_set_pps_callback_t set_pps_callback;
  gps_set_config_t set_config;
  gps_query_relpos_t query_relpos;
  gps_query_nmea_gga_t query_nmea_gga;
};

__syscall int gps_query(const struct device *dev, struct gps_query_result *r);
__syscall int gps_spartn(const struct device *dev, const uint8_t *data, const size_t size);
__syscall int gps_rtcm(const struct device *dev, const uint8_t *data, const size_t size);
__syscall int gps_set_pps_callback(const struct device *dev, gps_pps_callback_t callback, void *callbackCtx);
__syscall int gps_set_config(const struct device *dev, const gps_config_item_t *items, const size_t numItems);
__syscall int gps_query_relpos(const struct device *dev, gps_relposned_result_t *outResult);
__syscall int gps_query_nmea_gga(const struct device *dev, char *outBuffer, const size_t outBufferSize);

static inline int z_impl_gps_query(const struct device *dev, struct gps_query_result *r) {
  struct gps_driver_api *api = (struct gps_driver_api *)dev->api;
  return api->query_pvt(dev, r);
}

static inline int z_impl_gps_spartn(const struct device *dev, const uint8_t *data, const size_t size) {
  struct gps_driver_api *api = (struct gps_driver_api *)dev->api;
  return api->spartn(dev, data, size);
}

static inline int z_impl_gps_rtcm(const struct device *dev, const uint8_t *data, const size_t size) {
  struct gps_driver_api *api = (struct gps_driver_api *)dev->api;
  return api->rtcm(dev, data, size);
}

static inline int z_impl_gps_set_pps_callback(const struct device *dev, gps_pps_callback_t callback,
                                              void *callbackCtx) {
  struct gps_driver_api *api = (struct gps_driver_api *)dev->api;
  return api->set_pps_callback(dev, callback, callbackCtx);
}

static inline int z_impl_gps_set_config(const struct device *dev, const gps_config_item_t *items,
                                        const size_t numItems) {
  struct gps_driver_api *api = (struct gps_driver_api *)dev->api;
  return api->set_config(dev, items, numItems);
}

static inline int z_impl_gps_query_relpos(const struct device *dev, gps_relposned_result_t *outResult) {
  struct gps_driver_api *api = (struct gps_driver_api *)dev->api;
  return api->query_relpos(dev, outResult);
}

static inline int z_impl_gps_query_nmea_gga(const struct device *dev, char *outBuffer, const size_t outBufferSize) {
  struct gps_driver_api *api = (struct gps_driver_api *)dev->api;
  return api->query_nmea_gga(dev, outBuffer, outBufferSize);
}

#include <syscalls/gps.h>

#pragma once

#include <drivers/sensor.h>

#define QDEC64K_NUM_TICKS 65536U

static inline int64_t qdec64k_adjust_ticks(int64_t prev, uint16_t curr_rem) {
  /* do math in unsigned integers as it's easier */
  uint16_t prev_rem = (uint64_t)prev % QDEC64K_NUM_TICKS;
  uint64_t prev_core = (uint64_t)prev - prev_rem;
  if (prev_rem > QDEC64K_NUM_TICKS * 3 / 4 && curr_rem < QDEC64K_NUM_TICKS / 4) {
    /* forward wrap around */
    return prev_core + QDEC64K_NUM_TICKS + curr_rem;
  }
  if (curr_rem > QDEC64K_NUM_TICKS * 3 / 4 && prev_rem < QDEC64K_NUM_TICKS / 4) {
    /* backward wrap around */
    return prev_core - QDEC64K_NUM_TICKS + curr_rem;
  }
  return (int64_t)(prev_core + curr_rem);
}

enum sensor_channel_qdec64k {
  SENSOR_CHAN_QDEC64K_TICKS = SENSOR_CHAN_PRIV_START,
};

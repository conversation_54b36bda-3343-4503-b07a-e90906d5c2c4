#pragma once

#include <stdbool.h>
#include <stdint.h>

#include <utils/carbon_response_codes.h>

#include "laser/types.h"

#ifdef USE_REAPER
#include "laser/reaper.h"
#endif

int laser_init();
CARBON_RESPONSE_CODE laser_set_state(bool state);
CARBON_RESPONSE_CODE laser_get_state(bool *state);
CARBON_RESPONSE_CODE laser_set_intensity(int32_t intensity);
CARBON_RESPONSE_CODE laser_get_lpsu_state(bool *state);
CARBON_RESPONSE_CODE laser_get_lpsu_current(float *current);
CARBON_RESPONSE_CODE laser_get_power(float *power);
CARBON_RESPONSE_CODE laser_get_raw_readings(int32_t *reading1, int32_t *reading2);
void laser_stf_state_change(bool safe);

CARBON_RESPONSE_CODE laser_get_arc_detector_status(laser_arc_detector_status_t *outStatus);
CARBON_RESPONSE_CODE laser_get_arc_detector_config(laser_arc_detector_config_t *outConfig);
CARBON_RESPONSE_CODE laser_set_arc_detector_config(const laser_arc_detector_config_t *newConfig);
CARBON_RESPONSE_CODE laser_arc_detector_reset();

int laser_get_type(laser_type_t *outType);
int laser_is_connected(bool *outIsConnected);

int laser_get_slayer_therm(float *ambient, float *beam);

#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(can_open, CONFIG_APP_LOG_LEVEL);

#include "can_open.h"
#include "can_open_msg.h"
#include "sensors.h"

#include <string.h>
#include <utils/handle_errors.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/can.h>

#define CANOPEN_NODE_COUNT 3
#define CANOPEN_NODE_ID_START 1
#define MAILBOX_SIZE 10

#define CHECK_MIN_ID(node_id) (node_id > 0)
#define CHECK_MAX_ID(node_id) (node_id < CANOPEN_NODE_COUNT + CANOPEN_NODE_ID_START)
#define IS_VALID_ID(node_id) (CHECK_MIN_ID(node_id) && CHECK_MAX_ID(node_id))

#define GET_MBOX_FOR_NODE(node_id) co.mailbox[node_id - CANOPEN_NODE_ID_START]
#define GET_MSGQ_FOR_NODE(node_id) GET_MBOX_FOR_NODE(node_id).msgq
#define GET_LOCK_FOR_NODE(node_id) GET_MBOX_FOR_NODE(node_id).nodeLock

/**
 * @brief How often SYNC messages are transmitted on the bus
 *
 * SYNC messages are used to drive periodic synchronous events on devices; currently this
 * is only used for FH controllers to transmit their current status information.
 */
#define CANOPEN_SYNC_INTERVAL K_MSEC(500)

typedef struct {
  char __aligned(4) buffer[MAILBOX_SIZE * sizeof(CAN_Open_Message_t)];
  struct k_msgq msgq;

  /**
   * @brief Node access lock
   *
   * Only a single thread may talk to a node at a given time; this lock guarantees that.
   *
   * Due to the way the CANOpen stack is structured, there's several entry points into logic that
   * communicates with a node: therefore, this lock is acquired from various places in the code
   * below.
   *
   * Zephyr mutexes are reentrant, meaning that if a thread has already acquired the lock, it can
   * re-acquire the lock safely as long as it unlocks the lock an equal number of times. Therefore
   * the lock is taken in high level functions (NMT resets, SDOs upload/download) as well as the
   * lower level functions (such as awaits and raw sends) that those high level functions call into
   * for their functionality.
   */
  struct k_mutex nodeLock;
} CanOpenMailbox;

typedef struct CanOpen_t {
  const struct device *can;

  // Per node mailboxes
  CanOpenMailbox mailbox[CANOPEN_NODE_COUNT];

  // LSS mailbox
  CanOpenMailbox lss;

  /*
   * @brief NMT heartbeat semaphore
   *
   * This dude is given every time an NMT heartbeat is received; it can be used to wait for NMT
   * responses from any number of nodes.
   */
  struct k_sem nmtSema;
} CanOpen;

typedef union {
  CAN_Open_EMCY_packet_t *emcy;
  CAN_Open_SDO_packet_t *sdo;
  CAN_Open_PDO_packet_t *pdo;
  CAN_Open_NMT_packet_t *nmt;
  CAN_Open_LSS_packet_t *lss;
  uint8_t *raw;
} packet_buffer;

static void init_mailbox(CanOpenMailbox *);

static bool can_to_can_open_emcy(const packet_buffer *in, CAN_Open_Message_t *out);
static bool can_to_can_open_sdo(const packet_buffer *in, CAN_Open_Message_t *out);
inline bool can_to_can_open_pdo(const packet_buffer *in, CAN_Open_Message_t *out);
inline bool can_to_can_open_nmt(const packet_buffer *in, CAN_Open_Message_t *out);
static bool can_to_can_open_lss(const packet_buffer *in, CAN_Open_Message_t *out);
static bool can_to_can_open(const struct zcan_frame *in, CAN_Open_Message_t *out);

static void can_rx_callback(const struct device *dev, struct zcan_frame *msg, void *arg);
static void send_can_frame(struct zcan_frame *frame);

static int await_reply_internal(CanOpenMailbox *mbox, CAN_Open_Message_t *outMsg, const k_timeout_t timeout,
                                uint8_t func);

static void canopen_sync_timer_expired(struct k_timer *);
static void canopen_sync_sender_work(struct k_work *);

static int nmt_await_heartbeat(const size_t numExpected, const k_timeout_t timeout);
static int nmt_reset_all(const uint8_t type, const k_timeout_t timeout);
static int nmt_reset_node_all();
static int nmt_reset_comms_all();

static int await_lss_reply(CAN_Open_Message_t *outReply, const uint8_t cs, const k_timeout_t timeout);
static void LSS_Set_Global_State(const LSS_State state);
static int LSS_Set_Config_State_By_Serial(const uint32_t serial);
static int LSS_Get_Serials(uint32_t *serial_buf, const size_t len);
static int LSS_Set_ID(const uint8_t id);
static int LSS_Store_Config();

static int Get_FH_Axis(const uint8_t node_id, bool *isPan);
static CARBON_RESPONSE_CODE Check_FH_Configured();

// This timer is used to send the SYNC frames
K_TIMER_DEFINE(gCanOpenSyncSenderTimer, canopen_sync_timer_expired, NULL);
K_WORK_DEFINE(gCanOpenSyncSenderWork, canopen_sync_sender_work);

// Global CANOpen stack state
CanOpen co;

bool canopen_is_valid_id(uint8_t node_id) { return IS_VALID_ID(node_id); }

static bool can_to_can_open_emcy(const packet_buffer *in, CAN_Open_Message_t *out) {
  out->pkt_type = CAN_OPEN_EMCY_PKT_TYPE;
  out->pkt.emcy = *in->emcy;
  return true;
}

static bool can_to_can_open_sdo(const packet_buffer *in, CAN_Open_Message_t *out) {
  out->pkt_type = CAN_OPEN_SDO_PKT_TYPE;
  out->pkt.sdo = *in->sdo;
  switch (CAN_OPEN_SDO_SPEC_CS(out->pkt.sdo.spec)) {
  case CAN_OPEN_CS_SDO_UL:
  case CAN_OPEN_CS_SDO_ABORT:
  case CAN_OPEN_CS_SDO_DL_RESPONSE:
    return true;
  case CAN_OPEN_CS_SDO_DL_REQUEST: // We don't have a dictionary to answer
  default:
    return false;
  }
}

inline bool can_to_can_open_pdo(const packet_buffer *in, CAN_Open_Message_t *out) {
  out->pkt_type = CAN_OPEN_PDO_PKT_TYPE;
  out->pkt.pdo = *in->pdo;
  return true;
}

inline bool can_to_can_open_nmt(const packet_buffer *in, CAN_Open_Message_t *out) {
  out->pkt_type = CAN_OPEN_NMT_PKT_TYPE;
  out->pkt.nmt = *in->nmt;
  return true;
}

static bool can_to_can_open_lss(const packet_buffer *in, CAN_Open_Message_t *out) {
  out->pkt_type = CAN_OPEN_LSS_PKT_TYPE;
  out->pkt.lss = *in->lss;
  return true;
}

/**
 * @brief Decode raw CAN frame
 *
 * Decode a raw CAN frame off the wire into a CANOpen message.
 */
static bool can_to_can_open(const struct zcan_frame *in, CAN_Open_Message_t *out) {
  out->func = CAN_OPEN_COB_ID_FUNC(in->id);
  out->node_id = CAN_OPEN_COB_ID_NODE_ID(in->id);

  packet_buffer buf;
  buf.raw = in->data;
  bool should_send = false;
  switch (out->func) {
  case CAN_OPEN_FUNC_CODE_EMERGENCY:
    should_send = can_to_can_open_emcy(&buf, out);
    break;
  case CAN_OPEN_FUNC_CODE_SDO_TX:
    should_send = can_to_can_open_sdo(&buf, out);
    break;
  case CAN_OPEN_FUNC_CODE_PDO1_TX:
  case CAN_OPEN_FUNC_CODE_PDO2_TX:
  case CAN_OPEN_FUNC_CODE_PDO3_TX:
  case CAN_OPEN_FUNC_CODE_PDO4_TX:
    should_send = can_to_can_open_pdo(&buf, out);
    break;
  case CAN_OPEN_BROADCAST_FUNC_CODE_NMT:
  case CAN_OPEN_FUNC_CODE_NMT_HB:
    should_send = can_to_can_open_nmt(&buf, out);
    break;
  case CAN_OPEN_FUNC_CODE_LSS:
    should_send = can_to_can_open_lss(&buf, out);
    break;
  case CAN_OPEN_FUNC_CODE_SDO_RX: // We don't currently do anything about other people trying to talk to the thing we
                                  // are talking to.
  case CAN_OPEN_FUNC_CODE_PDO1_RX:
  case CAN_OPEN_FUNC_CODE_PDO2_RX:
  case CAN_OPEN_FUNC_CODE_PDO3_RX:
  case CAN_OPEN_FUNC_CODE_PDO4_RX:
    break;

  default:
    break;
  }
  return should_send;
}

/**
 * @brief CAN receive ISR
 *
 * Push the received message into the appropriate message queue based on node id; if the message
 * queue is full, any previous messages are dropped.
 */
static void can_rx_callback(const struct device *dev, struct zcan_frame *msg, void *arg) {
  ARG_UNUSED(dev);
  int err;
  CAN_Open_Message_t co_msg;

  // drop messages from node id's we don't care about
  const uint32_t node_id = CAN_OPEN_COB_ID_NODE_ID(msg->id);

  if (msg->id != LSS_SLAVE_ID && node_id != 0 && !CHECK_MAX_ID(node_id)) {
    LOG_ERR("Dropping frame from unexpected node %u (id=%04x)!", node_id, msg->id);
    return;
  }

  // convert/parse the message, and determine where to put it
  if (can_to_can_open(msg, &co_msg)) {
    // special case for LSS
    if (msg->id == LSS_SLAVE_ID) {
      while (k_msgq_put(&co.lss.msgq, &co_msg, K_NO_WAIT) != 0) {
        LOG_WRN("%s mailbox overflow", "LSS");
        k_msgq_purge(&co.lss.msgq);
      }
    }
    // ID 0 is broadcast: shall go to all nodes
    else if (node_id == 0) {
      // TODO: ignore errors for node 3? idk how often they'll be
      for (int i = 0; i < CANOPEN_NODE_COUNT; ++i) {
        while (k_msgq_put(&co.mailbox[i].msgq, &co_msg, K_NO_WAIT) != 0) {
          LOG_WRN("mailbox overflow node %i", (i + 1));
          k_msgq_purge(&co.mailbox[i].msgq);
        }
      }
    }
    // unicast message
    else {
      // special case for TXPDO 3+4 (status reporting)
      if (co_msg.pkt_type == CAN_OPEN_PDO_PKT_TYPE &&
          (co_msg.func == CAN_OPEN_FUNC_CODE_PDO3_TX || co_msg.func == CAN_OPEN_FUNC_CODE_PDO4_TX)) {
        err = sensors_handle_status_pdo(node_id, &co_msg);
        if (err) {
          LOG_WRN("%s failed for node %u: %d", "sensors_handle_status_pdo", node_id, err);
        }
      }
      // emergency messages
      else if (co_msg.pkt_type == CAN_OPEN_EMCY_PKT_TYPE) {
#if USE_FAULHABER
        LOG_ERR("EMCY node%u: error=%04x:%02x, FH error=%04x", node_id, co_msg.pkt.emcy.code, co_msg.pkt.emcy.fh.error,
                co_msg.pkt.emcy.fh.fh_error);
#else
        LOG_ERR("EMCY node%u: error=%04x", node_id, co_msg.pkt.emcy.code);
#endif
      }
      // otherwise, place on node's receive queue
      else {
        // give semaphore for NMT heartbeat packets
        if (co_msg.pkt_type == CAN_OPEN_NMT_PKT_TYPE && co_msg.func == CAN_OPEN_FUNC_CODE_NMT_HB) {
          k_sem_give(&co.nmtSema);
        }

        // then place on msg queue
        while (k_msgq_put(&GET_MSGQ_FOR_NODE(node_id), &co_msg, K_NO_WAIT) != 0) {
          LOG_WRN("mailbox overflow node %i", node_id);
          k_msgq_purge(&GET_MSGQ_FOR_NODE(node_id));
        }
      }
    }
  }
}

/**
 * @brief Initialize CANOpen stack
 */
int can_open_init() {
  int err;

  // set up mailboxes and other kernel objects
  for (int i = 0; i < CANOPEN_NODE_COUNT; ++i) {
    init_mailbox(&co.mailbox[i]);
  }

  init_mailbox(&co.lss);

  k_sem_init(&co.nmtSema, 0, CANOPEN_NODE_COUNT);

  // ensure CAN device is ready and set up filter
  co.can = DEVICE_DT_GET(DT_CHOSEN(zephyr_can_primary));
  HANDLE_UNLIKELY_BOOL(device_is_ready(co.can), ENODEV);

  const struct zcan_filter gNoneFilter = {
      .id_type = CAN_STANDARD_IDENTIFIER, .rtr = CAN_DATAFRAME, .id = 0x0, .rtr_mask = 1, .id_mask = 0x000};

  HANDLE_UNLIKELY(can_add_rx_filter(co.can, can_rx_callback, NULL, &gNoneFilter));
  can_set_mode(co.can, CAN_MODE_NORMAL);

#if USE_FAULHABER
  // TODO: send some empty/harmless frames to "wake up" autobaud in the FH's
#endif

  // start SYNC frame transmission
#if USE_FAULHABER
  k_timer_start(&gCanOpenSyncSenderTimer, CANOPEN_SYNC_INTERVAL, CANOPEN_SYNC_INTERVAL);
#endif

  // perform NMT reset of all nodes
  LOG_INF("Resetting all nodes!");
  HANDLE_UNLIKELY(nmt_reset_node_all());

  return 0;
}

/**
 * @brief Initialize CANOpen mailbox object
 */
static void init_mailbox(CanOpenMailbox *mbox) {
  k_msgq_init(&mbox->msgq, mbox->buffer, sizeof(CAN_Open_Message_t), MAILBOX_SIZE);
  k_mutex_init(&mbox->nodeLock);
}

static void canopen_sync_timer_expired(struct k_timer *) { k_work_submit(&gCanOpenSyncSenderWork); }

/**
 * @brief Send the SYNC frame
 *
 * Send it as a raw CAN frame; there's no payload in it.
 */
static void canopen_sync_sender_work(struct k_work *) {
  struct zcan_frame frame = {.id_type = CAN_STANDARD_IDENTIFIER,
                             .rtr = 0,
                             .id = CAN_OPEN_COB_ID_CONSTRUCT(0, CAN_OPEN_BROADCAST_FUNC_CODE_SYNC),
                             .fd = 0,
                             .brs = 0,
                             .dlc = 0};

  send_can_frame(&frame);
}

static void send_can_frame(struct zcan_frame *frame) {
  int ret = 0;
  for (int i = 0; i < 5; i++) {
    ret = can_send(co.can, frame, K_MSEC(100), NULL, NULL);
    if (ret == 0) {
      return;
    }
  }
  LOG_ERR("Sending msg id: [%u] failed, err %d", frame->id, ret);
}

void Send_SDO(uint8_t node_id, uint16_t index, uint8_t subindex, uint32_t value, uint8_t cs, uint8_t expedited) {
  struct zcan_frame frame = {.id_type = CAN_STANDARD_IDENTIFIER,
                             .rtr = 0,
                             .id = CAN_OPEN_COB_ID_CONSTRUCT(node_id, CAN_OPEN_FUNC_CODE_SDO_RX),
                             .fd = 0,
                             .brs = 0,
                             .dlc = sizeof(CAN_Open_SDO_packet_t)};
  packet_buffer buf;
  buf.raw = frame.data;
  buf.sdo->index = index;
  buf.sdo->subindex = subindex;
  buf.sdo->spec = CAN_OPEN_SDO_SPEC_XLOAD_CONSTRUCT(cs, 0, 0, expedited, 0);
  memcpy(buf.sdo->data, &value, sizeof(uint32_t));
  send_can_frame(&frame);
}
void Send_PDO(uint8_t node_id, uint8_t func, uint8_t data[8], uint8_t size) {
  struct zcan_frame frame = {.id_type = CAN_STANDARD_IDENTIFIER,
                             .rtr = 0,
                             .id = CAN_OPEN_COB_ID_CONSTRUCT(node_id, func),
                             .fd = 0,
                             .brs = 0,
                             .dlc = size};
  packet_buffer buf;
  buf.raw = frame.data;
  memcpy(buf.pdo->data, data, sizeof(uint8_t) * size);
  send_can_frame(&frame);
}
void Send_RTR_PDO(uint8_t node_id, uint8_t func) {
  struct zcan_frame frame = {.id_type = CAN_STANDARD_IDENTIFIER,
                             .rtr = 1,
                             .id = CAN_OPEN_COB_ID_CONSTRUCT(node_id, func),
                             .fd = 0,
                             .brs = 0,
                             .dlc = 0};
  send_can_frame(&frame);

/**
 * @brief Transmit NMT request
 *
 * @param node_id Node to affect, or 0 for all nodes
 * @param state New NMT state of node
 */}
void Send_NMT(uint8_t node_id, uint8_t state) {
  k_sem_reset(&co.nmtSema);

  struct zcan_frame frame = {.id_type = CAN_STANDARD_IDENTIFIER,
                             .rtr = 0,
                             .id = CAN_OPEN_COB_ID_CONSTRUCT(0x00, CAN_OPEN_BROADCAST_FUNC_CODE_NMT),
                             .fd = 0,
                             .brs = 0,
                             .dlc = sizeof(CAN_Open_NMT_packet_t)};
  packet_buffer buf;
  buf.raw = frame.data;
  buf.nmt->node_id = node_id;
  buf.nmt->state = state;
  send_can_frame(&frame);
}

CARBON_RESPONSE_CODE Await_Reply(uint8_t node_id, CAN_Open_Message_t *msg, uint16_t timeout_ms, uint8_t func) {
  int err = await_reply_internal(&GET_MBOX_FOR_NODE(node_id), msg, K_MSEC(timeout_ms), func);

  if (err) {
    return CARBON_CAN_OPEN_AWAIT_TIMEOUT;
  } else {
    return CARBON_RESPONSE_OK;
  }
}

/**
 * @brief Wait for a CANOpen message on a queue
 *
 * @param whence Message queue to pull from
 * @param msg Output buffer to receive the message, if one is found
 * @param timeout How long to wait for a message to arrive
 * @param func Function code of the desired message
 */
int Await_Reply_Ex(const can_open_msgq_t whence, CAN_Open_Message_t *msg, const k_timeout_t timeout, uint8_t func) {
  CanOpenMailbox *mbox = NULL;
  switch (whence) {
  case kCanOpenMsgQueuePan:
    mbox = &GET_MBOX_FOR_NODE(NODE_ID_PAN_SERVO);
    break;
  case kCanOpenMsgQueueTilt:
    mbox = &GET_MBOX_FOR_NODE(NODE_ID_TILT_SERVO);
    break;
  case kCanOpenMsgQueueBonusNode:
    mbox = &GET_MBOX_FOR_NODE(NODE_ID_BONUS);
    break;

  case kCanOpenMsgQueueLss:
    mbox = &co.lss;
    break;

  default:
    return -EINVAL;
  }

  return await_reply_internal(mbox, msg, timeout, func);
}

/**
 * @brief Wait for a CANOpen message on a queue (internal)
 *
 * @param mbox Mailbox queue to pull from
 * @param msg Output buffer to receive the message, if one is found
 * @param timeout How long to wait for a message to arrive
 * @param func Function code of the desired message
 *
 * @return 0 on success or a negative error code
 */
static int await_reply_internal(CanOpenMailbox *mbox, CAN_Open_Message_t *outMsg, const k_timeout_t timeout,
                                uint8_t func) {
  int err = 0;

  if (K_TIMEOUT_EQ(timeout, K_NO_WAIT)) {
    err = -ENOMSG;
  } else if (!K_TIMEOUT_EQ(timeout, K_FOREVER)) {
    err = -ETIMEDOUT;
  }

  if (!mbox || !outMsg) {
    return -EFAULT;
  }

  // set up timeout
  int64_t start_time = k_uptime_get();
  int64_t remaining = (int64_t)k_ticks_to_ms_ceil64(timeout.ticks);

  HANDLE_UNLIKELY(k_mutex_lock(&mbox->nodeLock, K_FOREVER));
  {
    while (remaining > 0) {
      if (k_msgq_get(&mbox->msgq, outMsg, K_MSEC(remaining)) == 0) {
        if (outMsg->func == func) {
          err = 0;
          goto end;
        } else {
          LOG_DBG("Function mismatch: got %x, expected %x", outMsg->func, func);
        }
      }

      int64_t now = k_uptime_get();
      remaining -= (now - start_time);
      start_time = now;
    }

  end:;
  }
  k_mutex_unlock(&mbox->nodeLock);

  return err;
}

CARBON_RESPONSE_CODE Send_SDO_Download(uint8_t node_id, uint16_t index, uint8_t subindex, uint32_t value) {
  CARBON_RESPONSE_CODE ret;
  CAN_Open_Message_t msg;

  HANDLE_UNLIKELY(k_mutex_lock(&GET_LOCK_FOR_NODE(node_id), K_FOREVER));
  {
    k_msgq_purge(&GET_MSGQ_FOR_NODE(node_id));
    Send_SDO(node_id, index, subindex, value, CAN_OPEN_CS_SDO_DL_REQUEST, 1);
    ret = Await_Reply(node_id, &msg, CAN_OPEN_DEFAULT_TIMEOUT_MS, CAN_OPEN_FUNC_CODE_SDO_TX);
    if (ret != CARBON_RESPONSE_OK) {
      goto end;
    }

    if (msg.pkt_type != CAN_OPEN_SDO_PKT_TYPE || msg.pkt.sdo.index != index || msg.pkt.sdo.subindex != subindex ||
        CAN_OPEN_SDO_SPEC_CS(msg.pkt.sdo.spec) != CAN_OPEN_CS_SDO_DL_RESPONSE) {
      LOG_WRN("SDO download fail(node id %u): type %02x,%02x, index %04x,%04x, subindex %02x,%02x, spec %u,%u", node_id,
              msg.pkt_type, CAN_OPEN_SDO_PKT_TYPE, msg.pkt.sdo.index, index, msg.pkt.sdo.subindex, subindex,
              CAN_OPEN_SDO_SPEC_CS(msg.pkt.sdo.spec), CAN_OPEN_CS_SDO_DL_RESPONSE);
      ret = CARBON_CAN_OPEN_SDO_DOWNLOAD_BAD_REPLY;
      goto end;
    }
  end:;
  }
  k_mutex_unlock(&GET_LOCK_FOR_NODE(node_id));

  return ret;
}

CARBON_RESPONSE_CODE Send_SDO_Upload_Request(uint8_t node_id, CAN_Open_Message_t *msg, uint16_t index,
                                             uint8_t subindex) {
  CARBON_RESPONSE_CODE ret;

  HANDLE_UNLIKELY(k_mutex_lock(&GET_LOCK_FOR_NODE(node_id), K_FOREVER));
  {
    k_msgq_purge(&GET_MSGQ_FOR_NODE(node_id));
    Send_SDO(node_id, index, subindex, 0x00, CAN_OPEN_CS_SDO_UL, 0);
    ret = Await_Reply(node_id, msg, CAN_OPEN_DEFAULT_TIMEOUT_MS, CAN_OPEN_FUNC_CODE_SDO_TX);
    if (ret != CARBON_RESPONSE_OK) {
      goto end;
    }

    if (msg->pkt_type != CAN_OPEN_SDO_PKT_TYPE || msg->pkt.sdo.index != index || msg->pkt.sdo.subindex != subindex ||
        CAN_OPEN_SDO_SPEC_CS(msg->pkt.sdo.spec) != CAN_OPEN_CS_SDO_UL) {
      LOG_WRN("SDO upload fail(node id %u): type %02x,%02x, index %04x,%04x, subindex %02x,%02x, spec %u,%u", node_id,
              msg->pkt_type, CAN_OPEN_SDO_PKT_TYPE, msg->pkt.sdo.index, index, msg->pkt.sdo.subindex, subindex,
              CAN_OPEN_SDO_SPEC_CS(msg->pkt.sdo.spec), CAN_OPEN_CS_SDO_UL);
      ret = CARBON_CAN_OPEN_SDO_UPLOAD_BAD_REPLY;
      goto end;
    }

  end:;
  }
  k_mutex_unlock(&GET_LOCK_FOR_NODE(node_id));

  return ret;
}

CARBON_RESPONSE_CODE NMT_Reset_Node(uint8_t node_id) {
  CAN_Open_Message_t msg;
  CARBON_RESPONSE_CODE ret;

  HANDLE_UNLIKELY(k_mutex_lock(&GET_LOCK_FOR_NODE(node_id), K_FOREVER));
  {
    k_msgq_purge(&GET_MSGQ_FOR_NODE(node_id));
    // Reset Node, on average takes 1.5 seconds
    Send_NMT(node_id, CAN_OPEN_NMT_RESET_NODE);
    ret = Await_Reply(node_id, &msg, 3000, CAN_OPEN_FUNC_CODE_NMT_HB);
    if (ret != CARBON_RESPONSE_OK || msg.pkt_type != CAN_OPEN_NMT_PKT_TYPE || msg.pkt.nmt.state != 0x00) {
      // Try To Keep Going Anyway
      LOG_INF("node %d: not in correct state, attempting reset", node_id);
    }

    // Reset Communications, on average takes a few ms
    Send_NMT(node_id, CAN_OPEN_NMT_RESET_COMMUNICATION);
    ret = Await_Reply(node_id, &msg, 3000, CAN_OPEN_FUNC_CODE_NMT_HB);
    if (ret != CARBON_RESPONSE_OK) {
      if (ret == CARBON_CAN_OPEN_AWAIT_TIMEOUT) {
        ret = CARBON_CAN_OPEN_RESET_TIMEOUT;
      }
      goto end;
    }

    if (msg.pkt_type != CAN_OPEN_NMT_PKT_TYPE || msg.pkt.nmt.state != 0x00) {
      ret = CARBON_CAN_OPEN_RESET_COMMS_BAD_REPLY;
      goto end;
    }

    // No NMT Response Expected
    Send_NMT(node_id, CAN_OPEN_NMT_ENTER_PRE_OPERATIONAL);
    k_sleep(K_MSEC(100));

  end:;
  }
  k_mutex_unlock(&GET_LOCK_FOR_NODE(node_id));

  return ret;
}

CARBON_RESPONSE_CODE NMT_Start_Node(uint8_t node_id) {
  HANDLE_UNLIKELY(k_mutex_lock(&GET_LOCK_FOR_NODE(node_id), K_FOREVER));
  {
    Send_NMT(node_id, CAN_OPEN_NMT_ENTER_OPERATIONAL);
    k_sleep(K_MSEC(10));
  }
  k_mutex_unlock(&GET_LOCK_FOR_NODE(node_id));

  return CARBON_RESPONSE_OK;
}

CARBON_RESPONSE_CODE NMT_Stop_Node(uint8_t node_id) {
  HANDLE_UNLIKELY(k_mutex_lock(&GET_LOCK_FOR_NODE(node_id), K_FOREVER));
  {
    // No NMT Response Expected
    Send_NMT(node_id, CAN_OPEN_NMT_ENTER_STOP);
    k_sleep(K_MSEC(10));

    // No NMT Response Expected
    Send_NMT(node_id, CAN_OPEN_NMT_ENTER_PRE_OPERATIONAL);
    k_sleep(K_MSEC(10));
  }
  k_mutex_unlock(&GET_LOCK_FOR_NODE(node_id));

  return CARBON_RESPONSE_OK;
}

/**
 * @brief Perform broadcast NMT operation
 *
 * Request all nodes on the bus (which should be two) perform a given action. All of them should
 * respond with a heartbeat packet.
 */
static int nmt_reset_all(const uint8_t type, const k_timeout_t timeout) {
  Send_NMT(0, type);

  return nmt_await_heartbeat(2, timeout);
}

/**
 * @brief Reset all nodes
 *
 * Settings are restored from nonvolatile power-on defaults and the node will return to the
 * initialization state. Heartbeat will follow shortly thereafter.
 */
static int nmt_reset_node_all() {
  // observed time is about 1-2 sec
  return nmt_reset_all(CAN_OPEN_NMT_RESET_NODE, K_SECONDS(5));
}

/**
 * @brief Reset communications of all nodes
 *
 * This will use the currently stored node id (set via LSS or from poweer up) for communication.
 */
static int nmt_reset_comms_all() {
  // observed time is about 50-100ms
  return nmt_reset_all(CAN_OPEN_NMT_RESET_COMMUNICATION, K_MSEC(500));
}

void clear_mailbox(uint8_t node_id) {
  if (IS_VALID_ID(node_id)) {
    k_msgq_purge(&GET_MSGQ_FOR_NODE(node_id));
  }
}

CARBON_RESPONSE_CODE can_open_to_npb_msg(const CAN_Open_Message_t *in, can_open_Message_Reply *out) {
  out->func = in->func;
  out->node_id = in->node_id;
  switch (in->pkt_type) {
  case CAN_OPEN_SDO_PKT_TYPE:
    out->which_pkt = can_open_Message_Reply_sdo_tag;
    out->pkt.sdo.index = in->pkt.sdo.index;
    out->pkt.sdo.subindex = in->pkt.sdo.subindex;
    out->pkt.sdo.spec = in->pkt.sdo.spec;
    memcpy(out->pkt.sdo.data, in->pkt.sdo.data, CAN_OPEN_SDO_PKT_DATA_SIZE);
    break;
  case CAN_OPEN_PDO_PKT_TYPE:
    out->which_pkt = can_open_Message_Reply_pdo_tag;
    memcpy(out->pkt.pdo.data, in->pkt.pdo.data, CAN_OPEN_PDO_PKT_DATA_SIZE);
    break;
  case CAN_OPEN_NMT_PKT_TYPE:
    out->which_pkt = can_open_Message_Reply_nmt_tag;
    out->pkt.nmt.node_id = in->pkt.nmt.node_id;
    out->pkt.nmt.state = in->pkt.nmt.state;
    break;
  default:
    return CARBON_CAN_ERROR_UNKNOWN;
    break;
  }
  return CARBON_RESPONSE_OK;
}

/**
 * @brief Wait for an NMT heartbeat
 *
 * @param numExpected Number of heartbeats to wait for
 * @param timeout Total time to wait for that number of heartbeats
 */
static int nmt_await_heartbeat(const size_t numExpected, const k_timeout_t timeout) {
  size_t found = 0;

  // set up timeout
  int64_t start_time = k_uptime_get();
  int64_t remaining = (int64_t)k_ticks_to_ms_ceil64(timeout.ticks);

  LOG_DBG("waiting for %u NMT heartbeats", numExpected);
  while (remaining > 0) {
    if (k_sem_take(&co.nmtSema, K_MSEC(remaining)) == 0) {
      // got all the responses
      LOG_DBG("got NMT heartbeat (%u/%u)", (found + 1), numExpected);
      if (++found == numExpected) {
        return 0;
      }
    }

    int64_t now = k_uptime_get();
    remaining -= (now - start_time);
    start_time = now;
  }

  // if get here, timed out waiting
  return -ETIMEDOUT;
}

/**
 * @brief Wait for an LSS slave response
 *
 * @param cs LSS command specifier to wait for
 */
static int await_lss_reply(CAN_Open_Message_t *outReply, const uint8_t cs, const k_timeout_t timeout) {
  int err;

  err = Await_Reply_Ex(kCanOpenMsgQueueLss, outReply, timeout, CAN_OPEN_FUNC_CODE_LSS);
  if (err) {
    return err;
  }

  if (outReply->pkt.lss.cs != cs) {
    LOG_WRN("Invalid LSS reply (CS = %x, expected %x)", outReply->pkt.lss.cs, cs);
    return -EINVAL;
  }

  return 0;
}

/**
 * @brief Switch all nodes' LSS state
 *
 * Execute the Switch Mode Global protocol. Does not expect any response from nodes.
 */
static void LSS_Set_Global_State(const LSS_State state) {
  struct zcan_frame frame = {
      .id_type = CAN_STANDARD_IDENTIFIER,
      .rtr = 0,
      .id = LSS_MASTER_ID,
      .fd = 0,
      .brs = 0,
      .dlc = 8,
      .data = {0},
  };

  packet_buffer buf;
  buf.raw = frame.data;
  buf.lss->cs = LSS_SWITCH_STATE_GLOBAL_CS;
  buf.lss->data[0] = state;
  send_can_frame(&frame);

  // load bearing delay to make sure nodes have time to switch state
  k_sleep(K_MSEC(10));
}

/**
 * @brief Put a single node into LSS mode
 *
 * Execute the Switch Mode Selective protocol, to put a single node into LSS state based on its
 * serial number. (The vendor, product, and rev are all hard-coded)
 *
 * Once all four messages (with vendor id, product id, rev, S/N) have been sent, the node will
 * respond with current mode.
 */
static int LSS_Set_Config_State_By_Serial(const uint32_t serial) {
  CAN_Open_Message_t reply;
  uint32_t specifier;

  struct zcan_frame frame = {
      .id_type = CAN_STANDARD_IDENTIFIER, .rtr = 0, .id = LSS_MASTER_ID, .fd = 0, .brs = 0, .dlc = 8, .data = {0}};

  packet_buffer buf;
  buf.raw = frame.data;

  buf.lss->cs = LSS_SWITCH_STATE_SELECTIVE_VENDOR_CS;
  specifier = FH_VENDOR_ID;
  memcpy(buf.lss->data, &specifier, sizeof(uint32_t));
  send_can_frame(&frame);
  k_sleep(K_MSEC(10));

  buf.lss->cs = LSS_SWITCH_STATE_SELECTIVE_PRODUCT_CS;
  specifier = FH_PRODUCT_CODE;
  memcpy(buf.lss->data, &specifier, sizeof(uint32_t));
  send_can_frame(&frame);
  k_sleep(K_MSEC(10));

  buf.lss->cs = LSS_SWITCH_STATE_SELECTIVE_REVISION_CS;
  specifier = FH_REVISION_NUMBER;
  memcpy(buf.lss->data, &specifier, sizeof(uint32_t));
  send_can_frame(&frame);
  k_sleep(K_MSEC(10));

  buf.lss->cs = LSS_SWITCH_STATE_SELECTIVE_SERIAL_CS;
  memcpy(buf.lss->data, &serial, sizeof(uint32_t));
  send_can_frame(&frame);
  k_sleep(K_MSEC(10));

  // get the response: indicates current mode
  HANDLE_UNLIKELY(await_lss_reply(&reply, LSS_SWITCH_STATE_SELECTIVE_REPLY, K_MSEC(500)));
  LOG_DBG("Switched node with SN %09u to state %u", serial, reply.pkt.lss.switch_mode_selective_rx.mode);

  return 0;
}

/**
 * @brief Execute "Inquire LSS Address" protocol
 *
 * All nodes on the bus will respond with their serial number.
 *
 * @param serial_buf Buffer to receive all of the device serial numbers
 * @param len Capacity of the serial number buffer, in entries
 */
static int LSS_Get_Serials(uint32_t *serial_buf, const size_t len) {
  int ret;
  CAN_Open_Message_t reply;
  uint32_t temp;

  memset(serial_buf, 0, sizeof(*serial_buf) * len);

  // purge any pending LSS messages
  k_msgq_purge(&co.lss.msgq);

  // Send global serial query
  struct zcan_frame frame = {
      .id_type = CAN_STANDARD_IDENTIFIER, .rtr = 0, .id = LSS_MASTER_ID, .fd = 0, .brs = 0, .dlc = 8, .data = {0}};

  packet_buffer buf;
  buf.raw = frame.data;
  buf.lss->cs = LSS_INQUIRE_SERIAL_CS;
  send_can_frame(&frame);

  // read out the requested number of sn's
  for (size_t i = 0; i < len; i++) {
    HANDLE_UNLIKELY(await_lss_reply(&reply, LSS_INQUIRE_SERIAL_CS, K_MSEC(500)));

    memcpy(&temp, reply.pkt.lss.data, sizeof(temp));
    serial_buf[i] = temp;
  }

  return 0;
}

/**
 * @brief Set nodeid for device in LSS state
 */
static int LSS_Set_ID(const uint8_t id) {
  CAN_Open_Message_t reply;
  struct zcan_frame frame = {
      .id_type = CAN_STANDARD_IDENTIFIER, .rtr = 0, .id = LSS_MASTER_ID, .fd = 0, .brs = 0, .dlc = 8, .data = {0}};

  packet_buffer buf;
  buf.raw = frame.data;

  // purge any pending LSS messages
  k_msgq_purge(&co.lss.msgq);

  // set node id; wait for reply
  buf.lss->cs = LSS_CONFIGURE_NODE_ID_CS;
  buf.lss->configure_node_id_tx.nid = id;
  send_can_frame(&frame);

  LOG_DBG("Setting current LSS slave to node id %u", id);

  HANDLE_UNLIKELY(await_lss_reply(&reply, LSS_CONFIGURE_NODE_ID_CS, K_MSEC(500)));
  if (reply.pkt.lss.configure_node_id_rx.error) {
    LOG_WRN("LSS %s failed: %02x,%02x", "LSS_CONFIGURE_NODE_ID", reply.pkt.lss.configure_node_id_rx.error,
            reply.pkt.lss.configure_node_id_rx.specific_error);
    return -EIO;
  }

  return 0;
}

/**
 * @brief Save configuration of current LSS slave
 */
static int LSS_Store_Config() {
  CAN_Open_Message_t reply;
  struct zcan_frame frame = {
      .id_type = CAN_STANDARD_IDENTIFIER, .rtr = 0, .id = LSS_MASTER_ID, .fd = 0, .brs = 0, .dlc = 8, .data = {0}};

  packet_buffer buf;
  buf.raw = frame.data;

  // purge any pending LSS messages before sending store request
  k_msgq_purge(&co.lss.msgq);

  buf.lss->cs = LSS_STORE_CONFIG_CS;
  send_can_frame(&frame);

  HANDLE_UNLIKELY(await_lss_reply(&reply, LSS_STORE_CONFIG_CS, K_SECONDS(1)));
  if (reply.pkt.lss.configure_node_id_rx.error) {
    LOG_WRN("LSS %s failed: %02x,%02x", "LSS_STORE_CONFIG", reply.pkt.lss.configure_node_id_rx.error,
            reply.pkt.lss.configure_node_id_rx.specific_error);
    return -EIO;
  }

  return 0;
}

/**
 * @brief Validate whether both FH nodes are initialized correctly
 *
 * Ensures that node id's are assigned correctly (nodes 1 and 2) and that each of them have the
 * correct state on the digital input lines.
 */
static CARBON_RESPONSE_CODE Check_FH_Configured() {
  bool node_1_exists = false;
  bool node_2_exists = false;
  bool node_1_digital_state = false;
  bool node_2_digital_state = false;
  CARBON_RESPONSE_CODE response;
  CAN_Open_Message_t reply;

  response = Send_SDO_Upload_Request(0x01, &reply, 0x2311, 0x01);
  if (response == CARBON_RESPONSE_OK) {
    node_1_exists = true;
    node_1_digital_state = !!(reply.pkt.sdo.data[0] & BIT(0));
  } else {
    node_1_exists = false;
  }

  response = Send_SDO_Upload_Request(0x02, &reply, 0x2311, 0x01);
  if (response == CARBON_RESPONSE_OK) {
    node_2_exists = true;

    node_2_digital_state = !!(reply.pkt.sdo.data[0] & BIT(0));
  } else {
    node_2_exists = false;
  }

  if (node_1_exists && node_2_exists && node_1_digital_state == 1 && node_2_digital_state == 0) {
    return CARBON_RESPONSE_OK;
  } else {
    LOG_WRN("FH node config invalid (pan: %u, %u; tilt: %u, %u)", node_1_exists, node_1_digital_state, node_2_exists,
            node_2_digital_state);
    return CARBON_FH_SERVO_NODE_CONFIGURATION_INCORRECT;
  }
}

/**
 * @brief Read the digital IO used to determine the axis
 *
 * Digital input 1 is used to determine what axis the drive is controlling; it's tied high for pan,
 * and low for tilt.
 */
static int Get_FH_Axis(const uint8_t node_id, bool *isPan) {
  CARBON_RESPONSE_CODE response;
  CAN_Open_Message_t reply;

  if (!isPan) {
    return -EFAULT;
  }

  // read digital inputs register
  response = Send_SDO_Upload_Request(node_id, &reply, 0x2311, 0x01);
  if (response == CARBON_RESPONSE_OK) {
    *isPan = !!(reply.pkt.sdo.data[0] & BIT(0));
  } else {
    LOG_WRN("failed to read input pins: %d", response);
    return -ENODEV;
  }

  return 0;
}

/**
 * @brief Assign Faulhaber drive node IDs
 *
 * Using LSS, ensure each of the FH drives has the correct node id specified. This is done by
 * sensing the digital input for each of the drives.
 */
int Configure_FH_Node_IDs() {
  int err;

  // clear message queues
  k_msgq_purge(&co.lss.msgq);
  k_msgq_purge(&GET_MSGQ_FOR_NODE(NODE_ID_BONUS));

  // get node serial numbers
  size_t attempt = 0;
  uint32_t serials[2];
  uint32_t highest_serial = 0;
  uint32_t lowest_serial = 0;
  bool changed = false;
  bool isPan;

  /*
   * XXX: I think this silly loop was required because the FH's start up in autobaud mode…
   *      so they need to see some messages to switch to the correct rate. Apparently they
   *      do not start with 1Mbps. FH suggests disabling autobaud; we should send some useless
   *      frames before doing anything here, then we can disable autobaud via SDO if needed
   */
  do {
    // place all nodes into config state and acquire serial numbers
    LSS_Set_Global_State(Config);
    k_sleep(CANOPEN_SYNC_INTERVAL);

    err = LSS_Get_Serials(serials, ARRAY_SIZE(serials));
    if (err) {
      LOG_ERR("Failed to get faulhaber serials: %d (attempt %u)", err, attempt + 1);
      continue;
    }

    if (serials[1] > serials[0]) {
      highest_serial = serials[1];
      lowest_serial = serials[0];
    } else {
      highest_serial = serials[0];
      lowest_serial = serials[1];
    }

    LOG_INF("FH serials: [%09u, %09u]", serials[0], serials[1]);
    break;
  } while (++attempt < 3);

  if (attempt >= 3) {
    LOG_ERR("all attempts to get Faulhaber serials failed");
    return CARBON_CAN_ERROR_UNKNOWN;
  }

  // put nodes back into operational state, so we can test if they're already configured right
  LSS_Set_Global_State(Wait);

  err = Check_FH_Configured();
  if (err == CARBON_RESPONSE_OK) {
    goto success;
  }

  LOG_INF("FH nodes not properly configured. Configuring w/ LSS now...");

  /*
   * Node IDs are assigned as such:
   *
   * 1. Set highest S/N node to ID 3
   * 2. Read digital input to determine if it's pan axis (high) or tilt (low)
   * 3. Assign corresponding node ID
   * 4. Set the lowest S/N node to the opposite node ID
   * 5. Commit node IDs to NVRAM
   */
  // (1)
  HANDLE_UNLIKELY(LSS_Set_Config_State_By_Serial(highest_serial));
  HANDLE_UNLIKELY(LSS_Set_ID(NODE_ID_BONUS));
  LSS_Set_Global_State(Wait);
  HANDLE_UNLIKELY(nmt_reset_comms_all());

  // (2)
  HANDLE_UNLIKELY(Get_FH_Axis(NODE_ID_BONUS, &isPan));
  LOG_DBG("Highest S/N node (%09u) is %s", highest_serial, isPan ? "pan" : "tilt");

  // (3)
  const uint8_t highest_node_id = isPan ? NODE_ID_PAN_SERVO : NODE_ID_TILT_SERVO;

  HANDLE_UNLIKELY(LSS_Set_Config_State_By_Serial(highest_serial));
  HANDLE_UNLIKELY(LSS_Set_ID(highest_node_id));
  LSS_Set_Global_State(Wait);
  HANDLE_UNLIKELY(nmt_reset_comms_all());

  // (4)
  const uint8_t lowest_node_id = isPan ? NODE_ID_TILT_SERVO : NODE_ID_PAN_SERVO;

  HANDLE_UNLIKELY(LSS_Set_Config_State_By_Serial(lowest_serial));
  HANDLE_UNLIKELY(LSS_Set_ID(lowest_node_id));
  LSS_Set_Global_State(Wait);
  HANDLE_UNLIKELY(nmt_reset_comms_all());

  // (5)
  changed = true;

  // sanity check that nodes are configured right
  err = Check_FH_Configured();
  if (err != CARBON_RESPONSE_OK) {
    LOG_ERR("Config check after ID assignment failed: %d", err);
    return CARBON_FH_SERVO_NODE_CONFIGURATION_FAIL;
  }

success:;
  LOG_INF("FH node id assignment complete%s", !changed ? "; no changes necessary" : "");

  // commit to NVRAM if we changed node IDs
  if (changed) {
    LOG_INF("Committing node IDs to FH NVRAM!");

    // put all nodes back in idle state
    LSS_Set_Global_State(Wait);

    // save highest SN node's config
    HANDLE_UNLIKELY(LSS_Set_Config_State_By_Serial(highest_serial));
    err = LSS_Store_Config();
    if (err) {
      LOG_ERR("Failed to commit node id in drive %09u: %d", highest_serial, err);
    }

    // put all nodes to active state
    LSS_Set_Global_State(Wait);

    // save lowest SN node's config
    HANDLE_UNLIKELY(LSS_Set_Config_State_By_Serial(lowest_serial));
    err = LSS_Store_Config();
    if (err) {
      LOG_ERR("Failed to commit node id in drive %09u: %d", lowest_serial, err);
    }
  }

  // restore all nodes to idle
  LSS_Set_Global_State(Wait);
  return 0;
}

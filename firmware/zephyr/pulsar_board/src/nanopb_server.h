#pragma once
#include "generated/lib/drivers/nanopb/proto/pulczar_board.pb.h"
#include <lib/udp/udp.h>

typedef struct {
  // released when it hits 0
  size_t refCount;
  udp_msg_metadata metadata;
  pulczar_board_Request request;
} npb_request_data;

void start_nanopb_server();
void npb_send_reply(pulczar_board_Reply *reply, udp_msg_metadata *metadata);

int npb_get_last_request_time(int64_t *outTimestamp);

npb_request_data *npb_retain(npb_request_data *in);
npb_request_data *npb_release(npb_request_data *in);

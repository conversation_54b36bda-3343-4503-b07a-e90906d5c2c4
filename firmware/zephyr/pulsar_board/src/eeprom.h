#pragma once
#include "specific_controller.h"

#include <stdint.h>
#define SERIAL_NUM_SIZE 32
#define SCANNER_ASSY_SN_LENGTH 16
#define HW_REV_MAXON_SERVOS 0
#define HW_REV_FH_SERVOS 100

int eeprom_init();

int eeprom_save_color_cal(float red, float green, float blue);
int eeprom_load_color_cal(float *red, float *green, float *blue);

int eeprom_save_skew_cal(float pan_skew, float tilt_skew);
int eeprom_load_skew_cal(float *pan_skew, float *tilt_skew);

int eeprom_save_camera_sn(const char *serial_number);
int eeprom_load_camera_sn(char *serial_number);

int eeprom_save_hw_rev(uint32_t *rev);
int eeprom_load_hw_rev(uint32_t *rev);

int eeprom_save_pid(uint8_t node_id, struct pid_config_s *pid);
int eeprom_load_pid(uint8_t node_id, struct pid_config_s *pid);

int eeprom_save_scanner_sn(const char *serial_number);
int eeprom_load_scanner_sn(char *serial_number);

int eeprom_erase_config();

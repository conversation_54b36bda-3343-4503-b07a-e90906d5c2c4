#pragma once

#include <stdbool.h>

#include "can_open.h"
#include "generated/lib/drivers/nanopb/proto/epos.pb.h"
#include <utils/carbon_response_codes.h>

#define MC_DEFAULT_SETTLE_TIMEOUT_MS 250
#define MC_DEFAULT_SETTLE_WINDOW 50
#define MCEPOS_MINIMUM_SETTLE_WINDOW 5

typedef struct pid_config_s pid_config; // will be defined in the appropriate hearder file
typedef struct {
  CARBON_RESPONSE_CODE (*Enable_Node)(uint8_t node_id);
  CARBON_RESPONSE_CODE (*Disable_Node)(uint8_t node_id);
  CARBON_RESPONSE_CODE (*Setup_PDOs)(uint8_t node_id);

  // Status API
  CARBON_RESPONSE_CODE(*Await_Status_PDO)
  (uint8_t node_id, CAN_Open_Message_t *msg, uint16_t timeout_ms, uint16_t expected, uint16_t expected_neg);
  CARBON_RESPONSE_CODE (*Await_Status)(uint8_t node_id, uint16_t timeout_ms, uint16_t expected, uint16_t expected_neg);
  CARBON_RESPONSE_CODE(*Control_Word_Await_Status)
  (uint8_t node_id, uint16_t control_word, uint16_t expected_status, uint16_t expected_neg_status);

  // Profile Position Mode API
  CARBON_RESPONSE_CODE (*Go_To_Position)(uint8_t node_id, int32_t position, uint32_t velocity);
  CARBON_RESPONSE_CODE (*Get_Actual_Position_Velocity)(uint8_t node_id, int32_t *position, int32_t *velocity);
  CARBON_RESPONSE_CODE(*Await_Settling)
  (uint8_t node_id, int32_t target_position, uint16_t window, uint16_t timeout_ms, uint16_t *settle_time_ms_out);
  CARBON_RESPONSE_CODE (*Await_Target_Reached)(uint8_t node_id, uint16_t timeout_ms, uint16_t *reach_time_ms_out);
  CARBON_RESPONSE_CODE(*Calibration_Settle)
  (uint8_t node_id, int32_t target_position, uint16_t window, uint16_t time_window_ms, uint16_t timeout_ms,
   uint8_t period_ms, uint16_t *settle_time_ms_out);

  // Homing API
  CARBON_RESPONSE_CODE (*Home_With_Method)(uint8_t node_id, uint32_t method);
  CARBON_RESPONSE_CODE(*Hard_Stop_Home)
  (uint8_t node_id, int16_t step_size, uint16_t offset, int32_t min_position, int32_t max_position,
   uint32_t profile_velocity, uint16_t settle_timeout_ms, int32_t *out_limit, uint8_t enc_id, uint32_t tick_scale);
  CARBON_RESPONSE_CODE(*Actual_Position_Home)
  (uint8_t node_id, uint16_t range, uint32_t profile_velocity, int32_t *out_limit, uint8_t enc_id, uint32_t tick_scale);

  // Request API
  CARBON_RESPONSE_CODE(*Home_From_Params)
  (uint8_t node_id, epos_Home_Params *params, uint16_t settle_timeout_ms, int32_t *out_limit, uint8_t enc_id);
  CARBON_RESPONSE_CODE (*Handle_Request)(uint8_t node_id, epos_Request *request, epos_Reply *reply, uint8_t enc_id);

  // Config API
  CARBON_RESPONSE_CODE (*set_pid)(uint8_t node_id, pid_config *cfg);
  CARBON_RESPONSE_CODE (*handle_pid)(uint8_t node_id, epos_Set_PID_V2_Request *request);
  CARBON_RESPONSE_CODE (*handle_get_pid)(uint8_t node_id, epos_Get_PID_Reply *reply);
  CARBON_RESPONSE_CODE (*set_inverted)(uint8_t node_id, uint8_t inverted);
  uint32_t velocity_scaler;
  uint32_t tick_scale;

  // whether motor controller status PDOs
  bool statusPdoEnabled;
} MotionController;

void MC_Init(uint32_t tick_scale);
MotionController *MC_get();

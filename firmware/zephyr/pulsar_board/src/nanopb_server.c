#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(nanopb_server, CONFIG_APP_LOG_LEVEL);

#include <pb_decode.h>
#include <pb_encode.h>
#include <zephyr/sys/reboot.h>

#include <drivers/hw_rev_straps.h>
#include <drivers/liquid_lens.h>
#include <lib/ptp/ptp.h>
#include <lib/udp/udp.h>

#include "eeprom.h"
#include "generated/lib/drivers/nanopb/proto/pulczar_board.pb.h"
#include "gimbal_handler.h"
#include "laser.h"
#include "lens_handler.h"
#include "nanopb_server.h"
#include "power.h"
#include "sensors.h"
#include "watchdawg.h"

#if USE_REAPER
#include "laser/bwt_transport.h"
#include "laser/nanopb.h"
#include "laser/reaper.h"
#endif

#if USE_FAULHABER
#include "drive/faulhaber/ambient_temp.h"
#endif

#define THREAD_PRIORITY K_PRIO_PREEMPT(8)
#ifndef CONFIG_PB_REQUESTS_BUFFER_SIZE
#define CONFIG_PB_REQUESTS_BUFFER_SIZE 1
#endif

static npb_request_data request_buffer[CONFIG_PB_REQUESTS_BUFFER_SIZE];
K_MSGQ_DEFINE(request_ptrs, sizeof(npb_request_data *), CONFIG_PB_REQUESTS_BUFFER_SIZE, sizeof(npb_request_data *));

static void process_nanopb();
K_THREAD_DEFINE(nanopb_thread_id, 2560, process_nanopb, NULL, NULL, NULL, THREAD_PRIORITY, 0, K_TICKS_FOREVER);

UDP_SERVER_DEFINE(npb_udp_server, CONFIG_PB_REQUESTS_BUFFER_SIZE, sizeof(pulczar_board_Request) * 2, THREAD_PRIORITY);

// Timestamp of last inbound request
static int64_t gLastRequestTime = 0;

void start_nanopb_server() {
  for (uint8_t i = 0; i < CONFIG_PB_REQUESTS_BUFFER_SIZE; ++i) {
    npb_request_data *ptr = &request_buffer[i];
    k_msgq_put(&request_ptrs, &ptr, K_FOREVER);
  }
  UDP_SERVER_START(npb_udp_server, CONFIG_APP_UDP_PORT);
  k_thread_name_set(nanopb_thread_id, "nanopb_thread");
  k_thread_start(nanopb_thread_id);
  gimbal_start();
}
void npb_send_reply(pulczar_board_Reply *reply, udp_msg_metadata *metadata) {
  uint8_t buffer[sizeof(pulczar_board_Reply) * 2];
  pb_ostream_t stream = pb_ostream_from_buffer(buffer, sizeof(buffer));
  bool status = pb_encode(&stream, pulczar_board_Reply_fields, reply);
  if (!status) {
    LOG_WRN("Failed to encode packet");
    return;
  }
  udp_tx(&npb_udp_server, buffer, stream.bytes_written, metadata);
}

/**
 * @brief Get packet structure for receive
 */
static inline npb_request_data *npb_request_alloc() {
  npb_request_data *req;
  k_msgq_get(&request_ptrs, &req, K_FOREVER);

  // initialize the packet
  req->refCount = 1;

  return req;
}

/**
 * @brief Increase the packet ref count
 */
npb_request_data *npb_retain(npb_request_data *in) {
  in->refCount++;
  return in;
}

/**
 * @brief Decrement packet ref count
 *
 * Release request packet for reuse if it hits zero.
 */
npb_request_data *npb_release(npb_request_data *in) {
  // release back to queue
  if (!--in->refCount) {
    k_msgq_put(&request_ptrs, &in, K_FOREVER);
    return NULL;
  }
  return in;
}

static void handle_ping(npb_request_data *req) {
  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = req->request.header.requestId;
  reply.which_reply = pulczar_board_Reply_pong_tag;
  reply.reply.pong.x = req->request.request.ping.x;
  npb_send_reply(&reply, &req->metadata);
}
static void handle_version(npb_request_data *req) {
  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = req->request.header.requestId;
  reply.reply.version.major = 0;
  reply.reply.version.minor = 0;
  npb_send_reply(&reply, &req->metadata);
}

static int fill_timestamp_reply(time_Timestamp *npb_ts) {
  struct net_ptp_time ts;
  HANDLE_CRITICAL(ptp_slave_clk_get(&ts));
  npb_ts->seconds = ts.second;
  npb_ts->micros = ts.nanosecond / NSEC_PER_USEC;
  return 0;
}
static void handle_time_request(npb_request_data *req) {
  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = req->request.header.requestId;
  reply.which_reply = pulczar_board_Reply_time_tag;
  time_Request *treq = &req->request.request.time;
  switch (treq->which_request) {
  case time_Request_set_tag:
    // Set time not implemented currently
    reply.reply.time.which_reply = time_Reply_ack_tag;
    break;
  case time_Request_get_tag:
    reply.reply.time.which_reply = time_Reply_timestamp_tag;
    fill_timestamp_reply(&reply.reply.time.reply.timestamp);
    break;
  case time_Request_debug_tag:
    // PPS not used so most info not set
    reply.reply.time.which_reply = time_Reply_debug_tag;
    fill_timestamp_reply(&reply.reply.time.reply.debug.timestamp);
    reply.reply.time.reply.debug.pps_timer_val = 0;
    reply.reply.time.reply.debug.pps_ticks = 0;
    reply.reply.time.reply.debug.freq_mul = 0;
    reply.reply.time.reply.debug.error_ticks = 0;
    reply.reply.time.reply.debug.error_ticks2 = 0;
    break;
  default:
    break;
  }

  npb_send_reply(&reply, &req->metadata);
}
static void handle_clear_config(npb_request_data *req) {
  // TODO clear config
  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = req->request.header.requestId;
  reply.which_reply = pulczar_board_Reply_pulczar_tag;
  reply.reply.pulczar.which_reply = pulczar_Reply_ack_tag;
  npb_send_reply(&reply, &req->metadata);
}
static void handle_status(npb_request_data *req) {
  laser_arc_detector_status_t status;
  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = req->request.header.requestId;
  reply.which_reply = pulczar_board_Reply_pulczar_tag;
  reply.reply.pulczar.which_reply = pulczar_Reply_status_tag;
  bool armed, petted, firing;
  watchdawg_get_state(&armed, &petted);
  laser_get_state(&firing);
  reply.reply.pulczar.reply.status.status = (uint32_t)armed;
  reply.reply.pulczar.reply.status.status |= (uint32_t)firing << 4;
  reply.reply.pulczar.reply.status.status |= (uint32_t)petted << 5;
  reply.reply.pulczar.reply.status.has_laser_status = true;
  laser_get_lpsu_state(&(reply.reply.pulczar.reply.status.laser_status.lpsu_state));
  laser_get_lpsu_current(&(reply.reply.pulczar.reply.status.laser_status.lpsu_current));
  laser_get_power(&(reply.reply.pulczar.reply.status.laser_status.power));
  laser_get_arc_detector_status(&status);
  reply.reply.pulczar.reply.status.laser_status.arc_detected = status.alarm;

  npb_send_reply(&reply, &req->metadata);
}

static void handle_override(npb_request_data *req) {
  // TODO handle override
  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = req->request.header.requestId;
  reply.which_reply = pulczar_board_Reply_pulczar_tag;
  reply.reply.pulczar.which_reply = pulczar_Reply_ack_tag;
  npb_send_reply(&reply, &req->metadata);
}

static void handle_conf(npb_request_data *req) {
  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = req->request.header.requestId;
  reply.which_reply = pulczar_board_Reply_pulczar_tag;
  reply.reply.pulczar.which_reply = pulczar_Reply_conf_tag;
  scanner_config_Request *creq = &req->request.request.pulczar.request.conf;
  switch (creq->which_request) {
  case (scanner_config_Request_set_color_tag):
    eeprom_save_color_cal(creq->request.set_color.config.red, creq->request.set_color.config.green,
                          creq->request.set_color.config.blue);
    reply.reply.pulczar.reply.conf.which_reply = scanner_config_Reply_ack_tag;
    break;
  case (scanner_config_Request_get_color_tag):
    reply.reply.pulczar.reply.conf.which_reply = scanner_config_Reply_color_tag;
    scanner_config_Color_Config *color_cfg = &reply.reply.pulczar.reply.conf.reply.color.config;
    eeprom_load_color_cal(&color_cfg->red, &color_cfg->green, &color_cfg->blue);
    reply.reply.pulczar.reply.conf.reply.color.has_config = true;
    break;
  case (scanner_config_Request_set_dt_tag):
    eeprom_save_skew_cal(creq->request.set_dt.config.pan_skew, creq->request.set_dt.config.tilt_skew);
    reply.reply.pulczar.reply.conf.which_reply = scanner_config_Reply_ack_tag;
    break;
  case (scanner_config_Request_get_dt_tag):
    reply.reply.pulczar.reply.conf.which_reply = scanner_config_Reply_dt_tag;
    scanner_config_Delta_Target_Config *skew_cfg = &reply.reply.pulczar.reply.conf.reply.dt.config;
    eeprom_load_skew_cal(&skew_cfg->pan_skew, &skew_cfg->tilt_skew);
    reply.reply.pulczar.reply.conf.reply.dt.has_config = true;
    break;
  case (scanner_config_Request_set_sn_tag):
    eeprom_save_camera_sn(creq->request.set_sn.config.serial_number);
    reply.reply.pulczar.reply.conf.which_reply = scanner_config_Reply_ack_tag;
    break;
  case (scanner_config_Request_get_sn_tag):
    reply.reply.pulczar.reply.conf.which_reply = scanner_config_Reply_sn_tag;
    scanner_config_Camera_Serial_Number_Config *sn_cfg = &reply.reply.pulczar.reply.conf.reply.sn.config;
    eeprom_load_camera_sn(sn_cfg->serial_number);
    reply.reply.pulczar.reply.conf.reply.sn.has_config = true;
    break;
  case (scanner_config_Request_set_hw_tag):
    eeprom_save_hw_rev(&creq->request.set_hw.revision);
    reply.reply.pulczar.reply.conf.which_reply = scanner_config_Reply_ack_tag;
    break;
  case (scanner_config_Request_get_hw_tag):
    reply.reply.pulczar.reply.conf.which_reply = scanner_config_Reply_hw_tag;
    eeprom_load_hw_rev(&reply.reply.pulczar.reply.conf.reply.hw.revision);
    break;
  case (scanner_config_Request_set_bc_tag):
    eeprom_save_scanner_sn(creq->request.set_bc.config.barcode);
    reply.reply.pulczar.reply.conf.which_reply = scanner_config_Reply_ack_tag;
    break;
  case (scanner_config_Request_get_bc_tag):
    reply.reply.pulczar.reply.conf.which_reply = scanner_config_Reply_bc_tag;
    if (eeprom_load_scanner_sn(reply.reply.pulczar.reply.conf.reply.bc.config.barcode) != 0) {
      reply.reply.pulczar.reply.conf.reply.bc.has_config = false;
    } else {
      reply.reply.pulczar.reply.conf.reply.bc.has_config = true;
    }
    break;
  }
  npb_send_reply(&reply, &req->metadata);
}

static void handle_dawg(npb_request_data *req) {
  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = req->request.header.requestId;
  reply.which_reply = pulczar_board_Reply_pulczar_tag;
  reply.reply.pulczar.which_reply = pulczar_Reply_dawg_tag;
  dawg_Request *dreq = &req->request.request.pulczar.request.dawg;
  switch (dreq->which_request) {
  case dawg_Request_config_tag:
    reply.reply.pulczar.reply.dawg.which_reply = dawg_Reply_ack_tag;
    watchdawg_configure(dreq->request.config.timeout_ms);
    break;
  case dawg_Request_arm_tag:
    reply.reply.pulczar.reply.dawg.which_reply = dawg_Reply_ack_tag;
    watchdawg_arm(dreq->request.arm.armed);
    break;
  case dawg_Request_pet_tag:
    reply.reply.pulczar.reply.dawg.which_reply = dawg_Reply_ack_tag;
    watchdawg_pet(dreq->request.pet.firing);
    break;
  case dawg_Request_state_tag:
    reply.reply.pulczar.reply.dawg.which_reply = dawg_Reply_state_tag;
    watchdawg_get_state(&reply.reply.pulczar.reply.dawg.reply.state.armed,
                        &reply.reply.pulczar.reply.dawg.reply.state.petted);
    break;
  default:
    reply.reply.pulczar.reply.dawg.which_reply = dawg_Reply_error_tag;
    break;
  }
  npb_send_reply(&reply, &req->metadata);
}

#if USE_REAPER
/**
 * @brief Handle BWT laser requests
 *
 * These use the UART communication with the BWT controller to query information; or rather, this
 * copies previously queried information (during initialization or polling) from memory.
 */
static int handle_laser_bwt(npb_request_data *, laser_Request *lreq, laser_Reply *lreply) {
  laser_type_t type;
  HANDLE_UNLIKELY(laser_get_type(&type));
  if (type != kLaserTypeDiodeBWT) {
    LOG_ERR("invalid request: got request for %s laser, but have %s", "BWT", laser_get_type_name(type));
    return -ENXIO;
  }

  switch (lreq->which_request) {
  case laser_Request_bwt_passthrough_tag:
    lreply->which_reply = laser_Reply_bwt_passthrough_tag;

    HANDLE_UNLIKELY(laser_bwt_fill_nanopb_passthrough(&lreq->request.bwt_passthrough, &lreply->reply.bwt_passthrough));
    break;

  case laser_Request_bwt_set_config_tag:
    const laser_Bwt_Transport_Config *newConfig = &lreq->request.bwt_set_config;
    bwt_debug_flags_t newFlags = 0;

    if (newConfig->log_messages) {
      newFlags |= (kBwtDebugFlagLogRx | kBwtDebugFlagLogTx);
    }
    if (newConfig->intercommand_delay) {
      newFlags |= kBwtDebugFlagAddCommandDelay;
    }

    HANDLE_UNLIKELY(laser_bwt_transport_set_debug_flags(newFlags));

    // NOTE: intentional fallthrough to "get config" to return current config

  case laser_Request_bwt_get_config_tag:
    lreply->which_reply = laser_Reply_bwt_config_tag;
    HANDLE_UNLIKELY(laser_bwt_fill_nanopb_transport_config(&lreply->reply.bwt_config));
    break;

  // we really should never get here, lol
  default:
    return -EINVAL;
  }

  return 0;
}
#endif

static void handle_laser(npb_request_data *req) {
  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = req->request.header.requestId;
  reply.which_reply = pulczar_board_Reply_pulczar_tag;
  reply.reply.pulczar.which_reply = pulczar_Reply_laser_tag;
  laser_Request *lreq = &req->request.request.pulczar.request.laser;
  laser_Reply *lreply = &reply.reply.pulczar.reply.laser;
  int ret = CARBON_ERROR_UNKNOWN;
  switch (lreq->which_request) {
  case laser_Request_laser_tag:
    lreply->which_reply = laser_Reply_laser_reply_tag;
    ret = laser_set_state(lreq->request.laser.on);
    laser_get_raw_readings(&lreply->reply.laser_reply.raw_therm1_reading_mv,
                           &lreply->reply.laser_reply.raw_therm2_reading_mv);
    laser_get_state(&lreply->reply.laser_reply.on);
    laser_get_lpsu_state(&lreply->reply.laser_reply.lpsu_state);
    bool armed, petted;
    watchdawg_get_state(&armed, &petted);
    lreply->reply.laser_reply.fireable = armed && petted;
    break;
  case laser_Request_get_laser_tag:
    lreply->which_reply = laser_Reply_laser_tag;
    ret = laser_get_state(&lreply->reply.laser.on);
    break;
  case laser_Request_intensity_tag:
    lreply->which_reply = laser_Reply_ack_tag;
    ret = laser_set_intensity(lreq->request.intensity.intensity);
    break;

  // query raw sensor readings (for testing)
  case laser_Request_raw_data_tag: {
    lreply->which_reply = laser_Reply_raw_data_tag;

    // get thermistor readings
    int32_t therm1 = 0, therm2 = 0;
    ret = laser_get_raw_readings(&therm1, &therm2);
    if (ret) {
      LOG_WRN("%s failed: %d", "laser_get_raw_readings", ret);
    } else {
      lreply->reply.raw_data._therm1_raw.therm1_raw = therm1;
      lreply->reply.raw_data.which__therm1_raw = laser_Raw_Data_Reply_therm1_raw_tag;

      lreply->reply.raw_data._therm2_raw.therm2_raw = therm2;
      lreply->reply.raw_data.which__therm2_raw = laser_Raw_Data_Reply_therm2_raw_tag;
    }

    // get photodiode readings
#if USE_REAPER
    uint32_t diode = 0;
    ret = laser_get_raw_photodiode(&diode);
    if (ret) {
      LOG_WRN("%s failed: %d", "laser_get_raw_photodiode", ret);
    } else {
      lreply->reply.raw_data._photodiode_raw.photodiode_raw = diode;
      lreply->reply.raw_data.which__photodiode_raw = laser_Raw_Data_Reply_photodiode_raw_tag;
    }
#endif

    // request always succeeds overall, even if querying some data fails
    ret = CARBON_RESPONSE_OK;
    break;
  }

  // generic laser stuff
  case laser_Request_laser_inventory_tag:
    lreply->which_reply = laser_Reply_laser_inventory_tag;

    ret = laser_fill_nanopb_inventory(&lreply->reply.laser_inventory);
    if (ret) {
      LOG_WRN("%s failed: %d", "laser_fill_nanopb_inventory", ret);
    }
    break;

  case laser_Request_diode_status_tag:
    lreply->which_reply = laser_Reply_diode_status_tag;

    ret = laser_fill_nanopb_status(&lreply->reply.diode_status);
    if (ret) {
      LOG_WRN("%s failed: %d", "laser_fill_nanopb_status", ret);
    }
    break;

  case laser_Request_laser_reset_tag:
    ret = laser_handle_nanopb_reset(&lreq->request.laser_reset);
    if (ret) {
      LOG_WRN("%s failed: %d", "laser_handle_nanopb_reset", ret);
    } else {
      lreply->which_reply = laser_Reply_ack_tag;
    }
    break;

  case laser_Request_diode_set_current_tag:
    ret = laser_handle_nanopb_set_current(&lreq->request.diode_set_current);
    if (ret) {
      LOG_WRN("%s failed: %d", "laser_handle_nanopb_set_current", ret);
    } else {
      lreply->which_reply = laser_Reply_ack_tag;
    }
    break;

  case laser_Request_set_type_tag:
    ret = laser_handle_nanopb_set_type(&lreq->request.set_type);
    if (ret) {
      LOG_WRN("%s failed: %d", "laser_handle_nanopb_set_type", ret);
    } else {
      lreply->which_reply = laser_Reply_ack_tag;
    }
    break;

  // BWT specific stuff
  case laser_Request_bwt_passthrough_tag:
  case laser_Request_bwt_set_config_tag:
  case laser_Request_bwt_get_config_tag:
#if USE_REAPER
    ret = handle_laser_bwt(req, lreq, lreply);
    if (ret) {
      LOG_WRN("%s failed: %d", "handle_laser_bwt", ret);
    }
#else
    ret = -ENOTSUP;
#endif
    break;

  default:
    lreply->which_reply = laser_Reply_error_tag;
    break;
  }

  if (ret != CARBON_RESPONSE_OK) {
    reply.reply.pulczar.reply.laser.which_reply = laser_Reply_error_tag;
    reply.reply.pulczar.reply.laser.reply.error.code = ret;
  }
  npb_send_reply(&reply, &req->metadata);
}

/**
 * @brief Process arc detector requests and generate response
 */
static void handle_arc_detector(npb_request_data *req) {
  CARBON_RESPONSE_CODE ret;

  // fill the request header
  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = req->request.header.requestId;
  reply.which_reply = pulczar_board_Reply_pulczar_tag;
  reply.reply.pulczar.which_reply = pulczar_Reply_arc_tag;

  // handle the contained message; reply defaults to an ack (unless ret != success later)
  const arc_detector_Request *arcReq = &req->request.request.pulczar.request.arc;

  arc_detector_Reply *arcReply = &reply.reply.pulczar.reply.arc;
  arcReply->which_reply = arc_detector_Reply_ack_tag;

  switch (arcReq->which_request) {
  // Update the configuration
  case arc_detector_Request_setConf_tag: {
    const arc_detector_Config *setConf = &arcReq->request.setConf.newConfig;
    laser_arc_detector_config_t conf;

    conf.enabled = setConf->enabled;
    conf.highCurrentLimit = setConf->upperLimit;
    conf.lowCurrentLimit = setConf->lowerLimit;
    conf.alarmPeriod = setConf->alarmPeriod;
    conf.alarmThreshold = setConf->alarmThreshold;

    if (setConf->initialDelay > UINT16_MAX) {
      ret = CARBON_ERROR_UNKNOWN;
      break;
    }
    conf.initialDelay = setConf->initialDelay;

    if (setConf->sampleInterval > UINT16_MAX) {
      ret = CARBON_ERROR_UNKNOWN;
      break;
    }
    conf.sampleInterval = setConf->sampleInterval;

    // apply the configuration
    ret = laser_set_arc_detector_config(&conf);
    if (ret != CARBON_RESPONSE_OK) {
      break;
    }

    // NOTE: fall through to send current configuration
  }

  // Retrieve current configuration
  case arc_detector_Request_getConf_tag: {
    laser_arc_detector_config_t conf;
    ret = laser_get_arc_detector_config(&conf);
    if (ret != CARBON_RESPONSE_OK) {
      break;
    }

    // fill in response
    arcReply->which_reply = arc_detector_Reply_conf_tag;
    arcReply->reply.conf.has_conf = true;
    arc_detector_Config *confReply = &arcReply->reply.conf.conf;

    confReply->enabled = conf.enabled;
    confReply->upperLimit = conf.highCurrentLimit;
    confReply->lowerLimit = conf.lowCurrentLimit;
    confReply->alarmPeriod = conf.alarmPeriod;
    confReply->alarmThreshold = conf.alarmThreshold;
    confReply->initialDelay = conf.initialDelay;
    confReply->sampleInterval = conf.sampleInterval;

    break;
  }

  // Retrieve current detector status
  case arc_detector_Request_status_tag: {
    laser_arc_detector_config_t conf;
    laser_arc_detector_status_t status;

    ret = laser_get_arc_detector_config(&conf);
    if (ret != CARBON_RESPONSE_OK) {
      break;
    }

    ret = laser_get_arc_detector_status(&status);
    if (ret != CARBON_RESPONSE_OK) {
      break;
    }

    arcReply->which_reply = arc_detector_Reply_status_tag;

    arcReply->reply.status.enabled = conf.enabled;
    arcReply->reply.status.alarm = status.alarm;
    arcReply->reply.status.minCurrent = status.minCurrent;
    arcReply->reply.status.maxCurrent = status.maxCurrent;

    break;
  }

  // Reset any previous arc detector alarms
  case arc_detector_Request_reset_tag:
    ret = laser_arc_detector_reset();
    break;

  // unknown tag (e.g. new message type we don't know)
  default:
    ret = CARBON_ERROR_UNKNOWN;
    break;
  }

  // if any API calls failed return an error then send reply
  if (ret != CARBON_RESPONSE_OK) {
    arcReply->which_reply = arc_detector_Reply_error_tag;
    arcReply->reply.error.code = ret;
  }

  npb_send_reply(&reply, &req->metadata);
}

/**
 * @brief Handle power control endpoint messages
 *
 * Only certain Slayer scanners have the ability to power cycle the target camera.
 *
 * All features are available for all Reaper scanners.
 */
static void handle_power(npb_request_data *req) {
  int err;
  power_channel_t current = 0, toEnable = 0, toDisable = 0;

  // fill the request header
  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = req->request.header.requestId;
  reply.which_reply = pulczar_board_Reply_pulczar_tag;
  reply.reply.pulczar.which_reply = pulczar_Reply_power_tag;

  const pulczar_Power_Request *pwrReq = &req->request.request.pulczar.request.power;
  pulczar_Power_Reply *pwrReply = &reply.reply.pulczar.reply.power;

  // process the requested power state changes
  if (pwrReq->which__targetCam) {
    if (pwrReq->_targetCam.targetCam) {
      toEnable |= kPowerTargetCam;
    } else {
      toDisable |= kPowerTargetCam;
    }
  }

#if USE_REAPER
  if (pwrReq->which__firingBoard) {
    if (pwrReq->_firingBoard.firingBoard) {
      toEnable |= kPowerFiringBoard;
    } else {
      toDisable |= kPowerFiringBoard;
    }
  }
#endif

  if (toEnable || toDisable) {
    LOG_INF("update pwr state: en=%04x, off=%04x", toEnable, toDisable);

    err = power_update(toEnable, toDisable);
    if (err) {
      LOG_WRN("%s failed: %d", "power_update", err);
    }
  }

  // respond with the current power state
  power_get_state(&current);

  pwrReply->targetCam = (current & kPowerTargetCam);
#if USE_REAPER
  pwrReply->firingBoard = (current & kPowerFiringBoard);
#endif

  // send reply message
  npb_send_reply(&reply, &req->metadata);
}

/**
 * @brief Respond to hwinfo request
 *
 * This is used to get the board revision (from dts/hw rev straps, if available) as well as the
 * scanner serial number.
 */
static void handle_hwinfo_request(npb_request_data *req) {
  int err;

  // prepare reply
  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = req->request.header.requestId;
  reply.which_reply = pulczar_board_Reply_hwinfo_tag;

  // process response
  switch (req->request.request.hwinfo.which_request) {
  /*
   * Retrieve PCB version information
   *
   * This includes the hardware revision (from the GPIO strap pins) as well as the board
   * configuration, taken from the dts.
   */
  case hwinfo_Request_version_tag: {
    reply.reply.hwinfo.which_reply = hwinfo_Reply_version_tag;
    hwinfo_BoardVersionReply *msg = &reply.reply.hwinfo.reply.version;

    // board rev (from dts)
    const char *compatible = DT_PROP(DT_ROOT, compatible);
    if (compatible) {
      strncpy(msg->model, compatible, sizeof(msg->model));
    }

    // get board rev: this may not be supported so handle that gracefully; default is -1
    msg->rev = UINT32_MAX;

    static const struct device *gHwRevs = DEVICE_DT_GET_OR_NULL(DT_PATH(hw_rev));
    if (!gHwRevs) {
      break;
    }

    if (!device_is_ready(gHwRevs)) {
      LOG_WRN("HW rev straps device not ready");
      break;
    }
    if ((err = hw_rev_straps_get(gHwRevs, &msg->rev))) {
      LOG_WRN("failed to get hw rev: %d", err);
      break;
    }

    break;
  }

  default:
    LOG_WRN("%s: unsupported request %u", __FUNCTION__, req->request.request.hwinfo.which_request);
    break;
  }

  // send reply message
  npb_send_reply(&reply, &req->metadata);
}

static void handle_pulczar_request(npb_request_data *req) {
  switch (req->request.request.pulczar.which_request) {
  case pulczar_Request_reset_tag:
    sys_reboot(SYS_REBOOT_COLD);
    break;
  case pulczar_Request_clear_tag:
    handle_clear_config(req);
    break;
  case pulczar_Request_status_tag:
    handle_status(req);
    break;
  case pulczar_Request_dawg_tag:
    handle_dawg(req);
    break;
  case pulczar_Request_laser_tag:
    handle_laser(req);
    break;
  case pulczar_Request_lens_tag:
    handle_lens(req);
    break;
  case pulczar_Request_gimbal_tag:
    handle_gimbal_request(req);
    break;
  case pulczar_Request_override_tag:
    handle_override(req);
    break;
  case pulczar_Request_conf_tag:
    handle_conf(req);
    break;
  case pulczar_Request_arc_tag:
    handle_arc_detector(req);
    break;
  case pulczar_Request_power_tag:
    handle_power(req);
    break;

  // hardware status requests
  case pulczar_Request_hw_status_tag:
    sensors_handle_nanopb(req);
    break;

#if USE_FAULHABER
  case pulczar_Request_ambient_temp_tag:
    fh_ambient_handle_nanopb(req);
    break;
#endif

  default:
    LOG_WRN("%s: unsupported request %u", __FUNCTION__, req->request.request.pulczar.which_request);
    break;
  }
}

static void process_nanopb() {
  for (;;) {
    uint8_t *udp_data;

    // get request structure
    npb_request_data *request_data = npb_request_alloc();

    // get request from packet, then decode
    uint16_t len = udp_get_data_no_copy(&npb_udp_server, &udp_data, &request_data->metadata, 0);

    pb_istream_t stream = pb_istream_from_buffer(udp_data, len);
    bool status = pb_decode(&stream, pulczar_board_Request_fields, &request_data->request);
    udp_get_data_release(&npb_udp_server, udp_data);

    if (!status) {
      LOG_WRN("Failed to decode UDP packet with size: %d", len);
      k_msgq_put(&request_ptrs, &request_data, K_FOREVER);
      continue;
    }

    // once decoded, invoke handler (which fills out the response)
    gLastRequestTime = k_uptime_get();

    switch (request_data->request.which_request) {
    case pulczar_board_Request_ping_tag:
      handle_ping(request_data);
      break;
    case pulczar_board_Request_version_tag:
      handle_version(request_data);
      break;
    case pulczar_board_Request_pulczar_tag:
      handle_pulczar_request(request_data);
      break;
    case pulczar_board_Request_time_tag:
      handle_time_request(request_data);
      break;
    case pulczar_board_Request_hwinfo_tag:
      handle_hwinfo_request(request_data);
      break;
    default:
      LOG_WRN("%s: unsupported request %u", __FUNCTION__, request_data->request.which_request);
      break;
    }

    // ensure message always gets released
    npb_release(request_data);
  }
}

/**
 * @brief Retrieve timestamp of last inbound request
 *
 * Any incoming packet (as long as it could be decoded as a protobuf, even if the request it
 * spawned fails) will update this timestamp.
 */
int npb_get_last_request_time(int64_t *outTimestamp) {
  if (!outTimestamp) {
    return -EFAULT;
  }

  *outTimestamp = gLastRequestTime;

  return 0;
}

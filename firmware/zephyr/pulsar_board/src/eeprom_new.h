/**
 * @file
 * @brief New and Improved™ EEPROM/config storage
 *
 * Handles storage of the scanner's configuration, but in the internal flash of the microcontroller
 * rather than an external EEPROM. Data is stored somewhat more robustly than the previous
 * implementation and can gracefully recover from missing/corrupted data.
 */
#pragma once

#include <stdint.h>

// Max length of camera serial number
#define CAMERA_SN_LENGTH (32)

// also implement the "old" EEPROM API
#include "eeprom.h"

int eeprom_save_laser_type(const uint32_t type);
int eeprom_load_laser_type(uint32_t *outType);

int eeprom_save_bwt_debug_flags(const uint32_t flags);
int eeprom_load_bwt_debug_flags(uint32_t *flags);

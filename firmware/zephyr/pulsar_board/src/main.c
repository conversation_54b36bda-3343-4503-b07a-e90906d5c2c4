#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(main, CONFIG_APP_LOG_LEVEL);

#include <zephyr.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/eeprom.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/logging/log_backend.h>
#include <zephyr/logging/log_ctrl.h>

#include <drivers/hw_rev_straps.h>
#include <drivers/laser_power_meter.h>
#include <drivers/zlcb.h>
#include <lib/gpio_ip/gpio_ip.h>
#include <lib/history_list/history_list.h>
#if CONFIG_LIB_STATUS_LED
#include <lib/status_led/status_led.h>
#endif
#include <lib/ptp/ptp.h>
#include <utils/handle_errors.h>

#include "can_open.h"
#include "eeprom.h"
#include "gimbal_handler.h"
#include "laser.h"
#include "lens_handler.h"
#include "motion_controller.h"
#include "nanopb_server.h"
#include "power.h"
#include "qdec.h"
#include "sensors.h"
#include "servo.h"

#if USE_NEW_EEPROM
#include "eeprom_new.h"
#endif

#include "lib/smp/smp.h"
#include "nanopb_server.h"
#include <lib/watchdog/watchdog.h>

#if IS_ENABLED(CONFIG_LOG_BACKEND_NET)
extern const struct log_backend *log_backend_net_get(void);
#endif

static const struct device *gHwRevs = DEVICE_DT_GET_OR_NULL(DT_PATH(hw_rev));

HISTORY_LIST_DEFINE(hist, NUM_QDEC_RECORDS, sizeof(qdec_record));

static int net_init();
static int motors_init();
static int boot();
#if CONFIG_LIB_STATUS_LED
static void handle_blinkenlights();
#endif

/**
 * @brief Set up the network settings for scanner board
 *
 * Apply the correct IP address (based on the external address pins) and start up early network
 * services.
 */
static int net_init() {
  // reaper has two fixed addresses, selected between by a single address pin
#if CONFIG_BOARD_SCANNER_H753_REAPER
  const gpio_ip_data gpio_cfg[] = {
      {.label = "GPIOD", .pin = 0},
  };
  const char *addresses[] = {"*********", "*********"};

  HANDLE_UNLIKELY_BOOL(gpio_ip_set_ip_list(addresses, gpio_cfg, 1, NULL), 1);
#else
  // non-reaper sets address directly from binary value
#if CONFIG_BOARD_SCANNER_H753_SLAYER
#define GPIO_IP_LABEL "GPIOD"
  gpio_ip_data gpio_cfg[] = {{.label = GPIO_IP_LABEL, .pin = 0},
                             {.label = GPIO_IP_LABEL, .pin = 1},
                             {.label = GPIO_IP_LABEL, .pin = 2},
                             {.label = GPIO_IP_LABEL, .pin = 3}};
#elif CONFIG_BOARD_SCANNER_GD
#define GPIO_IP_LABEL "GPIOG"
  gpio_ip_data gpio_cfg[] = {{.label = GPIO_IP_LABEL, .pin = 4},
                             {.label = GPIO_IP_LABEL, .pin = 5},
                             {.label = GPIO_IP_LABEL, .pin = 6},
                             {.label = GPIO_IP_LABEL, .pin = 7}};
#else
#define GPIO_IP_LABEL "GPIOE"
  gpio_ip_data gpio_cfg[] = {{.label = GPIO_IP_LABEL, .pin = 0},
                             {.label = GPIO_IP_LABEL, .pin = 1},
                             {.label = GPIO_IP_LABEL, .pin = 2},
                             {.label = GPIO_IP_LABEL, .pin = 3}};
#endif
  HANDLE_UNLIKELY_BOOL(gpio_ip_set_ip(CONFIG_NET_CONFIG_MY_IPV4_ADDR, CONFIG_NET_CONFIG_MY_IPV4_ADDR, gpio_cfg,
                                      sizeof(gpio_cfg) / sizeof(gpio_cfg[0])),
                       1);
#endif

  // remote management via mcumgr
  start_smp_lib();

  /*
   * Start the syslog backend
   *
   * For Reaper, we need to set the server IP manually as well before starting the backend; this
   * should be the same as the gateway IP.
   */
  if (!IS_ENABLED(CONFIG_LOG_BACKEND_NET_AUTOSTART)) {
    const struct log_backend *backend = log_backend_net_get();

#ifdef USE_REAPER
    struct net_if_ipv4 *config;
    struct sockaddr syslog;
    memset(&syslog, 0, sizeof(syslog));

    struct net_if *intf = net_if_get_default();
    HANDLE_UNLIKELY(net_if_config_ipv4_get(intf, &config));

    syslog.sa_family = AF_INET;
    net_sin(&syslog)->sin_port = htons(2442);
    net_sin(&syslog)->sin_addr = config->gw;

    HANDLE_UNLIKELY(log_backend_net_set_server(&syslog));
#endif

    if (!log_backend_is_active(backend)) {
      if (backend->api->init != NULL) {
        backend->api->init(backend);
      }

      log_backend_activate(backend, NULL);
    }
  }

  // set up the PTP clock client
  struct net_if *iface = net_if_get_default();
  struct net_if_config *iface_cfg = net_if_get_config(iface);

  /* offset PTP requests by board index defined by last bit of IP address */
  uint8_t last_ip_digit = iface_cfg->ip.ipv4->unicast[0].address.in_addr.s4_addr[3];
  HANDLE_CRITICAL(ptp_slave_init(iface, last_ip_digit));
  HANDLE_CRITICAL(ptp_slave_start());
  log_set_timestamp_func(log_get_ptp_timestamp, 1000000U);

  return 0;
}

/**
 * @brief Initialize motor controllers and servo communication
 *
 * Set up the CANOpen stack and assign node IDs to motor controllers. Additionally assign some on-boot
 * configuration.
 */
static int motors_init() {
  // determine tick scale: based on encoder type
  uint32_t tick_scale;
  uint32_t hw_rev;

#if CONFIG_BOARD_SCANNER_H753_REAPER
  // Reaper scanner uses FH exclusively
  hw_rev = HW_REV_FH_SERVOS;
  tick_scale = 4;
#else
  HANDLE_UNLIKELY(eeprom_load_hw_rev(&hw_rev));
  tick_scale = hw_rev == HW_REV_FH_SERVOS ? 4 : 1;
#endif

  LOG_INF("Servo config: Motor controller = %s, tick scale = %u, motors = %s",
#if USE_FAULHABER
          "Faulhaber",
#else
          "EPOS",
#endif
          tick_scale, (hw_rev == HW_REV_FH_SERVOS) ? "Faulhaber" : "Maxon");

  // set up CANOpen stack, assign node IDs to FH controllers
  HANDLE_UNLIKELY(can_open_init());
#if USE_FAULHABER
  HANDLE_UNLIKELY(Configure_FH_Node_IDs());
#endif

  // set up MCU-based encoder reading and callback to poll this periodically
  HANDLE_UNLIKELY(qdec_init(&hist, tick_scale));

  const struct device *zlcb = DEVICE_DT_GET(DT_ALIAS(zlcb0));
  HANDLE_UNLIKELY_BOOL(device_is_ready(zlcb), ENODEV);
  HANDLE_UNLIKELY(zlcb_set_callback_usec(zlcb, 100, (zlcb_callback_t)qdec_tick, NULL));

  // initialize motor control subsystem
  MC_Init(tick_scale);

  return 0;
}

/**
 * @brief Boot scanner board firmware
 *
 * Start all services in the firmware. If this fails (returns nonzero) it resets
 */
static int boot() {
  LOG_INF("Pulsar board booting");

#if CONFIG_LIB_STATUS_LED
  HANDLE_UNLIKELY(leds_init());
  HANDLE_UNLIKELY(leds_set_status(LED_COLOR_BLUE));
#endif

  // target cam power on boot
  HANDLE_UNLIKELY(power_init());
  HANDLE_UNLIKELY(power_enable(kPowerTargetCam));

  // set up network and EEPROM
  HANDLE_UNLIKELY(net_init());
  LOG_INF("network init complete");

  if (gHwRevs) {
    uint32_t rev = 0;

    HANDLE_UNLIKELY_BOOL(device_is_ready(gHwRevs), ENODEV);
    HANDLE_UNLIKELY(hw_rev_straps_get(gHwRevs, &rev));

    LOG_INF("hw rev = %u", rev);
  }

  HANDLE_UNLIKELY(eeprom_init());

  // set up servos, then the lens handler and laser stuff
  HANDLE_UNLIKELY(motors_init());
  HANDLE_UNLIKELY(lens_handler_init());
  HANDLE_UNLIKELY(laser_init());

  // start supervisory
  HANDLE_UNLIKELY(sensors_init(&hist));

  // start nanopb server last
  start_nanopb_server();

  LOG_INF("Finished Pulsar board booting.");
  return 0;
}

void main() {
  start_watchdog();
  HANDLE_CRITICAL(boot());

  // after boot, the main thread turns into a LED blinkenlights handler
  k_thread_priority_set(k_current_get(), (K_LOWEST_APPLICATION_THREAD_PRIO - 1));

#if CONFIG_LIB_STATUS_LED
  handle_blinkenlights();
#endif

  /* periodic output */
  /**
  epos_servo* pan = get_pan_servo();
  epos_servo* tilt = get_tilt_servo();
  for (;;) {
    history_record r;
    history_list_get_latest(&hist, &r);
    qdec_record *qrec = (qdec_record *)r.record;
    uint32_t sec = (*(r.usec)) / 1000000;
    uint32_t usec = (*(r.usec)) % 1000000;
    LOG_INF("Time: (%us, %uus), Ticks: pan (%d), tilt (%d)", sec, usec, (int32_t)servo_get_position(pan, qrec),
            (int32_t)servo_get_position(tilt, qrec));
    k_msleep(1000);
  }
  */
}

#if CONFIG_LIB_STATUS_LED
typedef enum blinky_state {
  kBlinkyStateOff,
  // link down
  kBlinkyStateLinkDown,
  // link up and received frame recently
  kBlinkyStateLinkUp,
  // link up and no frame recently received
  kBlinkyStateLinkUpInactive,
  // motor control fault
  kBlinkyStateMCFault,
} blinky_state_t;

static const leds_pattern_t gBlinkLinkDown[] = {
    LEDS_MAKE_PATTERN_COLOR(LED_COLOR_YELLOW, 1000),
    LEDS_MAKE_PATTERN_COLOR(LED_COLOR_OFF, 1000),
};
static const leds_pattern_t gBlinkLinkUpInactive[] = {
    LEDS_MAKE_PATTERN_COLOR(LED_COLOR_GREEN, 1000),
    LEDS_MAKE_PATTERN_COLOR(LED_COLOR_OFF, 1000),
};
static const leds_pattern_t gBlinkMCFault[] = {
    LEDS_MAKE_PATTERN_COLOR(0xff4000, 333),
    LEDS_MAKE_PATTERN_COLOR(LED_COLOR_OFF, 333),
};

/**
 * @brief Status LED handler
 *
 * This runs in an infinite loop on the main task to update the color of the RGB status LED on
 * the scanner. It has one of several states:
 *
 * - Yellow, flashing: Link down
 * - Green, flashing: Link up, no connectivity
 * - Rainbow: Link up, connectivity
 * - Orange, flashing: Motor control fault
 */
static void handle_blinkenlights() {
  int err;
  blinky_state_t state = kBlinkyStateLinkDown, nextState = kBlinkyStateOff;
  float hue = 0;

  struct net_if *intf = net_if_get_default();

  // Time duration of a single blinky tick, in msec
  const size_t kTickInterval = 20;
  // Time interval between packets until link is considered "alive, but inactive" (in msec)
  const size_t kPacketTimeout = 10000;

  // duration of the "connected" hue cycle, in msec
  const size_t kConnectedCycleLength = 15 * MSEC_PER_SEC;

  do {
    // handle state transition(s)
    switch (state) {
    case kBlinkyStateLinkUp: {
      int64_t timestamp;
      struct net_ptp_time ptpTime;
      if (ptp_slave_clk_get(&ptpTime)) {
        break;
      }

      uint64_t msec = ptp_ts_net_to_ms(&ptpTime) % kConnectedCycleLength;
      double hue = (((double)msec) / (double)kConnectedCycleLength) * 360.0;

      leds_set_status(leds_hsi_to_rgb(hue, 1, 1));

      err = npb_get_last_request_time(&timestamp);
      if (err) {
        LOG_WRN("%s failed: %d", "udp_get_last_request_time", err);
      }

      // timed out waiting for a packet?
      if (err || k_uptime_delta(&timestamp) > kPacketTimeout) {
        nextState = kBlinkyStateLinkUpInactive;
      }
      // link went down?
      else if (!net_if_is_up(intf)) {
        nextState = kBlinkyStateLinkDown;
      }
      break;
    }

    case kBlinkyStateLinkUpInactive:
      int64_t timestamp;

      err = npb_get_last_request_time(&timestamp);
      if (err) {
        LOG_WRN("%s failed: %d", "udp_get_last_request_time", err);
      }

      // received packet recently?
      if (!err && timestamp && k_uptime_delta(&timestamp) <= kPacketTimeout) {
        nextState = kBlinkyStateLinkUp;
      }
      // link went down?
      else if (!net_if_is_up(intf)) {
        nextState = kBlinkyStateLinkDown;
      }
      break;

    case kBlinkyStateLinkDown:
      if (net_if_is_up(intf)) {
        nextState = kBlinkyStateLinkUpInactive;
      }
      break;

    case kBlinkyStateMCFault:
      break;

    default:
      break;
    }

    // transition to next state
    if (state != nextState) {
      LOG_DBG("%u -> %u", state, nextState);

      hue = 0.f;
      state = nextState;

      // apply the LED blinking pattern
      leds_status_pattern_stop();

      switch (nextState) {
      case kBlinkyStateLinkDown:
        leds_status_pattern_start(gBlinkLinkDown, sizeof(gBlinkLinkDown) / sizeof(leds_pattern_t), true);
        break;
      case kBlinkyStateLinkUpInactive:
        leds_status_pattern_start(gBlinkLinkUpInactive, sizeof(gBlinkLinkUpInactive) / sizeof(leds_pattern_t), true);
        break;
      case kBlinkyStateMCFault:
        leds_status_pattern_start(gBlinkMCFault, sizeof(gBlinkMCFault) / sizeof(leds_pattern_t), true);
        break;

      default:
        break;
      }
    }

    k_sleep(K_MSEC(kTickInterval));
  } while (true);
}
#endif

#include <logging/log.h>
LOG_MODULE_REGISTER(watchdawg, CONFIG_APP_LOG_LEVEL);
#include "watchdawg.h"
#include <sys/atomic.h>
#include <zephyr.h>

#define ARMED_BIT 0
#define PETTED_BIT 1
#define PETTING_FILTER (1 << ARMED_BIT | 1 << PETTED_BIT)
atomic_t flags = ATOMIC_INIT(0x0);
static uint32_t timeout_ms = 1000;
static watchdawg_cb dawg_callback = NULL;
static watchdawg_cb dawg_pet_callback = NULL;

static void dawg_expiry_function(struct k_timer *timer_id);
K_TIMER_DEFINE(dawg_timer, dawg_expiry_function, NULL);

static void dawg_expiry_function(struct k_timer *timer_id) {
  atomic_clear_bit(&flags, PETTED_BIT);
  if (dawg_callback != NULL) {
    dawg_callback(false);
  }
}
void watchdawg_arm(bool arm) {
  if (arm) {
    atomic_set(&flags, PETTING_FILTER);
    k_timer_start(&dawg_timer, K_MSEC(timeout_ms), K_NO_WAIT);
  } else {
    k_timer_stop(&dawg_timer);
    atomic_clear_bit(&flags, ARMED_BIT);
    atomic_clear_bit(&flags, PETTED_BIT);
  }
  if (dawg_callback != NULL) {
    dawg_callback(arm);
  }
}
void watchdawg_pet(bool firing) {
  if (dawg_pet_callback != NULL) {
    dawg_pet_callback(firing);
  }
  if (atomic_test_bit(&flags, ARMED_BIT)) {
    if (!atomic_test_and_set_bit(&flags, PETTED_BIT)) {
      if (dawg_callback != NULL) {
        dawg_callback(true);
      }
    }
    k_timer_start(&dawg_timer, K_MSEC(timeout_ms), K_NO_WAIT);
  }
}

void watchdawg_get_state(bool *armed, bool *petted) {
  atomic_t state = atomic_get(&flags);
  *armed = (state >> ARMED_BIT) & 1;
  *petted = (state >> PETTED_BIT) & 1;
}
void watchdawg_configure(uint32_t to_ms) { timeout_ms = to_ms; }
void watchdawg_set_cb(watchdawg_cb callback) { dawg_callback = callback; }
void watchdawg_set_pet_cb(watchdawg_cb callback) { dawg_pet_callback = callback; }
bool watchdawg_stf() {
  atomic_t state = atomic_get(&flags);
  return (state & PETTING_FILTER) == PETTING_FILTER;
}
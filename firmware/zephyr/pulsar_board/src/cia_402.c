/* ========================================
 *
 * Carbon Robotics, 2022
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "cia_402.h"

const uint8_t CIA402_SAVE_PARAM_VALUE[4] = {'s', 'a', 'v', 'e'};
const uint8_t CIA402_RESTORE_PARAM_VALUE[4] = {'l', 'o', 'a', 'd'};
const uint32_t *CIA402_SAVE_PARAM_VALUE_POINTER = (uint32_t *)&CIA402_SAVE_PARAM_VALUE;
const uint32_t *CIA402_RESTORE_PARAM_VALUE_POINTER = (uint32_t *)&CIA402_RESTORE_PARAM_VALUE;
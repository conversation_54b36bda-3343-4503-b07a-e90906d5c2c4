#include <logging/log.h>
LOG_MODULE_REGISTER(eeprom, CONFIG_APP_LOG_LEVEL);

#include <drivers/eeprom.h>
#include <math.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

#include "eeprom.h"

static const struct device *eeprom = DEVICE_DT_GET(DT_ALIAS(eeprom0));

#define EEPROM_MAGIC 0xdeadbeefu

typedef struct __attribute__((packed)) color_settings {
  uint32_t magic;
  float red;
  float green;
  float blue;
} color_settings;
typedef struct __attribute__((packed)) skew_settings {
  uint32_t magic;
  float pan_skew;
  float tilt_skew;
} skew_settings;
typedef struct __attribute__((packed)) serial_num {
  uint32_t magic;
  uint8_t serial[SERIAL_NUM_SIZE];
} serial_num;
typedef struct __attribute__((packed)) hw_rev {
  uint32_t magic;
  uint32_t rev;
} hw_rev;
typedef struct __attribute__((packed)) pid_settings {
  uint32_t magic;
  struct pid_config_s pid;
} pid_settings;
typedef struct __attribute__((packed)) barcode_str {
  uint32_t magic;
  uint8_t barcode[SCANNER_ASSY_SN_LENGTH];
} barcode_str;

const static uint32_t color_offset = 0;
const static uint32_t skew_offset = sizeof(color_settings);
const static uint32_t serial_offset = skew_offset + sizeof(skew_settings);
const static uint32_t hw_offset = serial_offset + sizeof(serial_num);
// Note need to finalize pid object for FH before placing anything after this or it will get lost
const static uint32_t pid_1_offset = hw_offset + sizeof(hw_rev);
const static uint32_t pid_2_offset = pid_1_offset + sizeof(pid_settings);
const static uint32_t barcode_offset = pid_2_offset + sizeof(pid_settings);

#define EEPROM_NODE DT_ALIAS(eeprom0)

static bool check_magic(uint32_t magic, uint32_t line_no) {
  if (magic != EEPROM_MAGIC) {
    LOG_INF("Invalid magic number (%d) line: %u", magic, line_no);
    return false;
  }
  return true;
}

/**
 * @brief Initialize legacy EEPROM
 *
 * This ensures the I2C peripheral was initialized and that we can read from it.
 */
int eeprom_init() {
  HANDLE_UNLIKELY_BOOL(device_is_ready(eeprom), ENODEV);

  char c;
  HANDLE_UNLIKELY(eeprom_read(eeprom, 0, &c, 1));

  return 0;
}

int eeprom_save_color_cal(float red, float green, float blue) {
  const struct device *dev = DEVICE_DT_GET(EEPROM_NODE);
  HANDLE_UNLIKELY_BOOL(device_is_ready(dev), ENODEV);
  struct color_settings s;
  s.magic = EEPROM_MAGIC;
  s.red = red;
  s.green = green;
  s.blue = blue;
  HANDLE_UNLIKELY(eeprom_write(dev, color_offset, &s, sizeof(s)));
  return 0;
}
int eeprom_load_color_cal(float *red, float *green, float *blue) {
  const struct device *dev = DEVICE_DT_GET(EEPROM_NODE);
  HANDLE_UNLIKELY_BOOL(device_is_ready(dev), ENODEV);

  struct color_settings s;
  HANDLE_UNLIKELY(eeprom_read(dev, color_offset, &s, sizeof(s)));

  if (!check_magic(s.magic, __LINE__)) {
    *red = NAN;
    *green = NAN;
    *blue = NAN;
    return 0;
  }
  *red = s.red;
  *green = s.green;
  *blue = s.blue;
  return 0;
}

int eeprom_save_skew_cal(float pan_skew, float tilt_skew) {
  const struct device *dev = DEVICE_DT_GET(EEPROM_NODE);
  HANDLE_UNLIKELY_BOOL(device_is_ready(dev), ENODEV);
  struct skew_settings s;
  s.magic = EEPROM_MAGIC;
  s.pan_skew = pan_skew;
  s.tilt_skew = tilt_skew;
  HANDLE_UNLIKELY(eeprom_write(dev, skew_offset, &s, sizeof(s)));
  return 0;
}
int eeprom_load_skew_cal(float *pan_skew, float *tilt_skew) {
  const struct device *dev = DEVICE_DT_GET(EEPROM_NODE);
  HANDLE_UNLIKELY_BOOL(device_is_ready(dev), ENODEV);

  struct skew_settings s;
  HANDLE_UNLIKELY(eeprom_read(dev, skew_offset, &s, sizeof(s)));

  if (!check_magic(s.magic, __LINE__)) {
    *pan_skew = 0.0f;
    *tilt_skew = 0.0f;
    return 0;
  }
  *pan_skew = s.pan_skew;
  *tilt_skew = s.tilt_skew;
  return 0;
}

int eeprom_save_camera_sn(const char *serial_number) {
  const struct device *dev = DEVICE_DT_GET(EEPROM_NODE);
  HANDLE_UNLIKELY_BOOL(device_is_ready(dev), ENODEV);
  struct serial_num s;
  s.magic = EEPROM_MAGIC;
  memcpy(s.serial, serial_number, SERIAL_NUM_SIZE);
  HANDLE_UNLIKELY(eeprom_write(dev, serial_offset, &s, sizeof(s)));
  return 0;
}
int eeprom_load_camera_sn(char *serial_number) {
  const struct device *dev = DEVICE_DT_GET(EEPROM_NODE);
  HANDLE_UNLIKELY_BOOL(device_is_ready(dev), ENODEV);

  struct serial_num s;
  HANDLE_UNLIKELY(eeprom_read(dev, serial_offset, &s, sizeof(s)));

  if (!check_magic(s.magic, __LINE__)) {
    memset(serial_number, 0, SERIAL_NUM_SIZE);
    return 0;
  }
  memcpy(serial_number, s.serial, SERIAL_NUM_SIZE);
  return 0;
}

/**
 * @brief Write hardware/motor control revision data
 */
int eeprom_save_hw_rev(uint32_t *rev) {
#if CONFIG_BOARD_SCANNER_H753_REAPER
  return -ENOTSUP;
#else
  const struct device *dev = DEVICE_DT_GET(EEPROM_NODE);
  HANDLE_UNLIKELY_BOOL(device_is_ready(dev), ENODEV);
  struct hw_rev h;
  h.magic = EEPROM_MAGIC;
  h.rev = *rev;
  HANDLE_UNLIKELY(eeprom_write(dev, hw_offset, &h, sizeof(h)));
  return 0;
#endif
}

/*
 * @brief Get hardware/motor config
 *
 * This may be hard-coded based on the board version; the new H753 scanners support only Faulhaber
 * motors. Otherwise, it is read from EEPROM config.
 */
int eeprom_load_hw_rev(uint32_t *rev) {
#if CONFIG_BOARD_SCANNER_H753_REAPER
  *rev = HW_REV_FH_SERVOS;
#else
  const struct device *dev = DEVICE_DT_GET(EEPROM_NODE);
  HANDLE_UNLIKELY_BOOL(device_is_ready(dev), ENODEV);

  struct hw_rev h;
  HANDLE_UNLIKELY(eeprom_read(dev, hw_offset, &h, sizeof(h)));

  if (!check_magic(h.magic, __LINE__)) {
    *rev = 0;
    return 0;
  }
  *rev = h.rev;
#endif
  return 0;
}
int eeprom_save_pid(uint8_t node_id, struct pid_config_s *pid) {
  if (node_id != 1 && node_id != 2) {
    return -1;
  }
  const struct device *dev = DEVICE_DT_GET(EEPROM_NODE);
  HANDLE_UNLIKELY_BOOL(device_is_ready(dev), ENODEV);
  struct pid_settings s;
  s.magic = EEPROM_MAGIC;
  memcpy(&s.pid, pid, sizeof(struct pid_config_s));
  HANDLE_UNLIKELY(eeprom_write(dev, node_id == 1 ? pid_1_offset : pid_2_offset, &s, sizeof(s)));
  return 0;
}
int eeprom_load_pid(uint8_t node_id, struct pid_config_s *pid) {
  if (node_id != 1 && node_id != 2) {
    return -1;
  }
  const struct device *dev = DEVICE_DT_GET(EEPROM_NODE);
  HANDLE_UNLIKELY_BOOL(device_is_ready(dev), ENODEV);
  struct pid_settings s;
  HANDLE_UNLIKELY(eeprom_read(dev, node_id == 1 ? pid_1_offset : pid_2_offset, &s, sizeof(s)));
  if (!check_magic(s.magic, __LINE__)) {
    return -1;
  }
  memcpy(pid, &s.pid, sizeof(struct pid_config_s));
  return 0;
}

int eeprom_save_scanner_sn(const char *barcode_str) {
  const struct device *dev = DEVICE_DT_GET(EEPROM_NODE);
  HANDLE_UNLIKELY_BOOL(device_is_ready(dev), ENODEV);
  struct barcode_str s;
  s.magic = EEPROM_MAGIC;
  memcpy(s.barcode, barcode_str, SCANNER_ASSY_SN_LENGTH);
  s.barcode[SCANNER_ASSY_SN_LENGTH - 1] = 0;
  HANDLE_UNLIKELY(eeprom_write(dev, barcode_offset, &s, sizeof(s)));
  return 0;
}

int eeprom_load_scanner_sn(char *barcode_str) {
  const struct device *dev = DEVICE_DT_GET(EEPROM_NODE);
  HANDLE_UNLIKELY_BOOL(device_is_ready(dev), ENODEV);

  struct barcode_str s;
  HANDLE_UNLIKELY(eeprom_read(dev, barcode_offset, &s, sizeof(s)));

  if (!check_magic(s.magic, __LINE__)) {
    return -1;
  }
  memcpy(barcode_str, s.barcode, SCANNER_ASSY_SN_LENGTH);
  barcode_str[SCANNER_ASSY_SN_LENGTH - 1] = 0; // null terminator

  return 0;
}

// TODO: implement eeprom_erase_config()

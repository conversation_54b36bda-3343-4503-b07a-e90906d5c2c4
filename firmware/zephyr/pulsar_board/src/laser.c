#define LASER_PRIVATE 1

#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(laser, CONFIG_APP_LOG_LEVEL);

#include <zephyr/device.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/drivers/pwm.h>
#include <zephyr/kernel.h>

#include <stddef.h>
#include <stdint.h>
#include <string.h>
#include <zephyr/device.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/drivers/pwm.h>
#include <zephyr/kernel.h>

#include <drivers/laser_power_meter.h>
#include <utils/adc.h>
#include <utils/handle_errors.h>

#include "laser.h"
#include "laser/internal.h"
#include "watchdawg.h"

#ifdef USE_REAPER
#include "laser/reaper.h"
#endif

/**
 * @brief LPSU current sanity limit (A)
 *
 * Currents above this threshold are discarded as being invalid. This is used to guard against
 * spurious ADC glitches on the current readings.
 *
 * Set at 100mA: the LPSU current feedback is scaled such that 1mA ≅ 0.1V. The maximum current
 * the LPSU can output is around 45-50mA, and the ADC on the scanner board will saturate above
 * this point which leads to nonsense current data.
 */
#define LPSU_SANITY_THRESHOLD (0.10f)

static void arc_detector_start();
static void arc_detector_stop();
static void arc_detector_tick(struct k_timer *);
static void arc_detector_tick_work(struct k_work *);
static void arc_detector_process();
static void arc_detector_alarm();

static void arc_detector_reset_internal();
static size_t arc_num_pending();
static bool arc_add_event();

K_TIMER_DEFINE(gArcTimer, arc_detector_tick, NULL);
static K_WORK_DEFINE(gArcWorkItem, arc_detector_tick_work);

/// Laser subsystem state
laser_state_t gLaserState;

int laser_apply_state() {
  const bool actualState = (gpio_pin_get_dt(&gLaserConfig.laserFire) == 1);
  int value = gLaserState.enabled ? 1 : 0;

#ifndef USE_REAPER
  // rising edge: laser is enabled for the first time this shot
  if (!actualState && value) {
    arc_detector_start();
  }
  // falling edge: laser is being disabled (due to user request, fault, dawg, etc.)
  else if (actualState && !value) {
    arc_detector_stop();
  }
#endif

#if defined(USE_REAPER) && IS_ENABLED(CONFIG_BWT_UART_FIRING)
  return laser_bwt_set_firing(value);
#else
  return gpio_pin_set_dt(&gLaserConfig.laserFire, value);
#endif
}

void laser_dawg_callback(bool expected_state) {
  if (!expected_state && gLaserState.enabled) {
    // avoid unnecessary lock by double check
    k_mutex_lock(&gLaserState.mut, K_FOREVER);
    if (gLaserState.enabled) {
      gLaserState.enabled = false;
      laser_apply_state();
    }
    k_mutex_unlock(&gLaserState.mut);
  }
}

/**
 * @brief Initialize laser control subsystem
 */
int laser_init() {
  k_mutex_init(&gLaserState.mut);

  // set up PWM laser intensity control (for slayer only)
  if (gLaserConfig.laserIntensity.dev) {
    HANDLE_UNLIKELY_BOOL(device_is_ready(gLaserConfig.laserIntensity.dev), ENODEV);
  }

  // set up laser power meter
  if (gLaserConfig.powerMeter) {
    HANDLE_UNLIKELY_BOOL(device_is_ready(gLaserConfig.powerMeter), ENODEV);
  } else {
    LOG_WRN("no %s laser power meter!", "thermistor");
  }

  gLaserState.stf = watchdawg_stf();

  // set up laser fire GPIO
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&gLaserConfig.laserFire, GPIO_OUTPUT | GPIO_OUTPUT_INACTIVE));

  // perform laser init specific to the machine type
#ifdef USE_REAPER
  HANDLE_UNLIKELY(laser_reaper_init());
#else
  gLaserState.laserType = kLaserTypeCO2Generic;
#endif

  // apply laser state
  laser_set_state(false);

  // set up laser watchdog
  watchdawg_set_cb(laser_stf_state_change);
  watchdawg_set_pet_cb(laser_dawg_callback);

  // reset arc detector state and apply default configuration
#ifndef USE_REAPER
  static const laser_arc_detector_config_t kDefaultArcConfig = {
      .enabled = false,
      .highCurrentLimit = 0.042,
      .lowCurrentLimit = 0.020,

      // at least 30 arc events in an hour
      .alarmPeriod = (60 * 60),
      .alarmThreshold = 30,

      // wait 10ms for laser to strike after firing
      .initialDelay = 10,
      // then sample current every 15ms thereafter
      .sampleInterval = 15,
  };
  gLaserState.arc.config = kDefaultArcConfig;

  arc_detector_reset_internal();
#endif

  return 0;
}

CARBON_RESPONSE_CODE laser_set_state(bool state) {
  k_mutex_lock(&gLaserState.mut, K_FOREVER);
  gLaserState.enabled = state && gLaserState.stf && !(gLaserState.arc.config.enabled && gLaserState.arc.status.alarm);
  CARBON_RESPONSE_CODE resp = laser_apply_state() == 0 ? CARBON_RESPONSE_OK : CARBON_LASER_FAILED_TO_SET_STATE;
  k_mutex_unlock(&gLaserState.mut);
  return resp;
}
CARBON_RESPONSE_CODE laser_get_state(bool *state) {
  *state = gLaserState.enabled;
  return CARBON_RESPONSE_OK;
}

/**
 * @brief Set the laser intensity/strength
 *
 * On Slayer, this sets the duty cycle of the PWM output sent to the LPSU.
 *
 * On Reaper, this sends a command to the BWT laser via UART.
 */
CARBON_RESPONSE_CODE laser_set_intensity(int32_t intensity) {
  // validate intensity
  if (intensity > 1000 || intensity < 0) {
    return CARBON_LASER_INVALID_INTENSITY;
  }

#ifdef USE_REAPER
  return laser_reaper_set_intensity(intensity);
#else
  if (!gLaserConfig.laserIntensity.dev) {
    LOG_WRN("failed to set intensity to %d (no pwm)", intensity);
    return CARBON_ERROR_UNKNOWN;
  }

  intensity = 1000 - intensity; // Invert pwm as it gets re-inverted by LPSU board.
  const static uint32_t period_20khz_ns = (NSEC_PER_SEC) / (20000);
  if (pwm_set_dt(&gLaserConfig.laserIntensity, period_20khz_ns, intensity * period_20khz_ns / 1000) != 0) {
    return CARBON_LASER_FAILED_TO_SET_INTENSITY;
  }
  return CARBON_RESPONSE_OK;
#endif
}
void laser_stf_state_change(bool safe) {
  gLaserState.stf = safe;
  gLaserState.enabled &= safe;
  laser_apply_state();
}
CARBON_RESPONSE_CODE laser_get_lpsu_state(bool *state) {
  if (!gLaserConfig.powerMeter) {
    return CARBON_ERROR_UNKNOWN;
  }

  int status = laser_power_meter_get_status(gLaserConfig.powerMeter, state);
  if (status == 0) {
    return CARBON_RESPONSE_OK;
  }
  return CARBON_ERROR_UNKNOWN;
}
CARBON_RESPONSE_CODE laser_get_lpsu_current(float *current) {
  if (!gLaserConfig.powerMeter) {
    return CARBON_ERROR_UNKNOWN;
  }

  int status = laser_power_meter_get_current(gLaserConfig.powerMeter, current);
  if (status == 0) {
    return CARBON_RESPONSE_OK;
  }
  return CARBON_ERROR_UNKNOWN;
}
CARBON_RESPONSE_CODE laser_get_raw_readings(int32_t *reading1, int32_t *reading2) {
  if (!gLaserConfig.powerMeter) {
    return CARBON_ERROR_UNKNOWN;
  }

  int status = laser_power_meter_get_raw_readings(gLaserConfig.powerMeter, reading1, reading2);
  if (status == 0) {
    return CARBON_RESPONSE_OK;
  }
  return CARBON_ERROR_UNKNOWN;
}
CARBON_RESPONSE_CODE laser_get_power(float *power) {
  if (!gLaserConfig.powerMeter) {
    return CARBON_ERROR_UNKNOWN;
  }

  int status = laser_power_meter_get_power(gLaserConfig.powerMeter, power);
  if (status == 0) {
    return CARBON_RESPONSE_OK;
  }
  return CARBON_ERROR_UNKNOWN;
}

/**
 * @brief Read out Slayer power meter thermistor values as temperature
 *
 * Convert the laser power meter values to temperatures. This is only supported for some PCB
 * generations where the analog frontend allows for absolute conversions like this.
 *
 * Conversions from Amphenol doc for Type F material, as used in the thermistors; see
 * https://www.amphenol-sensors.com/hubfs/Documents/AAS-913-318C-Temperature-resistance-curves-071816-web.pdf
 */
int laser_get_slayer_therm(float *ambient, float *beam) {
#ifdef USE_REAPER
  return -ENOTSUP;
#else
  int err;

  // capture raw ADC voltage
  int32_t raw[2];

  err = laser_power_meter_get_raw_readings(gLaserConfig.powerMeter, &raw[0], &raw[1]);
  if (err) {
    return err;
  }

  // convert each to a temperature in °C
  float temps[2];

  for (size_t i = 0; i < 2; i++) {
    // using β for 25-85°C range
    const double ohms = get_divider_ohms(10000, raw[i], 3300);
    // TODO: validate the 25°C point
    temps[i] = get_thermistor_temp(25, 10000, 3969, ohms);
  }

  if (ambient) {
    *ambient = temps[0];
  }
  if (beam) {
    *beam = temps[1];
  }

  return err;
#endif
}

/**
 * @brief Reset the state of the arc detector
 *
 * Any pending/previously detected events are clared, and if laser firing is inhibited, it is
 * re-enabled.
 */
static void arc_detector_reset_internal() {
  gLaserState.arc.status.minCurrent = __builtin_inff();
  gLaserState.arc.status.maxCurrent = 0.f;
  gLaserState.arc.status.alarm = false;

  gLaserState.arc.emitted = false;

  gLaserState.arc.eventWritePtr = gLaserState.arc.eventReadPtr = 0;
  memset(gLaserState.arc.eventStorage, 0, NUM_ARC_EVENTS * sizeof(gLaserState.arc.eventStorage[0]));
}

/**
 * @brief Return the number of pending elements in the arc detection buffer
 */
static size_t arc_num_pending() {
  if (gLaserState.arc.eventWritePtr >= gLaserState.arc.eventReadPtr) {
    return (gLaserState.arc.eventWritePtr - gLaserState.arc.eventReadPtr);
  } else {
    return NUM_ARC_EVENTS - (gLaserState.arc.eventReadPtr - gLaserState.arc.eventWritePtr);
  }
}

/**
 * @brief Log a new arc event
 *
 * Store a detected arcing event in the detector state queue, with the current system timestamp.
 *
 * @return Whether the event queue may contain enough events to trigger an alarm
 *
 * @remark Caller must hold gLaserState lock
 */
static bool arc_add_event() {
  laser_arc_event_t event;
  event.timestamp = k_uptime_get();

  // insert into list and check if alarm conditions met
  gLaserState.arc.eventStorage[gLaserState.arc.eventWritePtr] = event;
  gLaserState.arc.eventWritePtr = (gLaserState.arc.eventWritePtr + 1) % NUM_ARC_EVENTS;

  return (arc_num_pending() >= gLaserState.arc.config.alarmThreshold);
}

/**
 * @brief Arc detector timer callback
 *
 * Invoked periodically via a kernel timer as long as the laser fire signal is asserted, defer
 * the work to read the currrent and do detection to a work queue. This is required to avoid
 * overflowing the idle/timer stack
 */
static void arc_detector_tick(struct k_timer *) { k_work_submit(&gArcWorkItem); }

/**
 * @brief Arc detector work callback
 *
 * Checks sensed current against configurable minimum and maximum thresholds and increment an arc
 * counter if conditions match.
 *
 * After a certain number of arc detections, a fault is triggered and laser firing is inhibited
 * until the error state is cleared.
 */
static void arc_detector_tick_work(struct k_work *) {
  CARBON_RESPONSE_CODE err;
  float current = 0;
  bool detected = false;

  k_mutex_lock(&gLaserState.mut, K_FOREVER);
  {
    // if the laser has been turned off now, do nothing
    if (gpio_pin_get_dt(&gLaserConfig.laserFire) != 1) {
      goto beach;
    }

    // read out the laser current
    err = laser_get_lpsu_current(&current);
    if (err != CARBON_RESPONSE_OK) {
      LOG_ERR("%s failed (%d)", "laser_get_lpsu_current", err);
      goto beach;
    }

    // sanity check the current
    if (current >= LPSU_SANITY_THRESHOLD) {
      int ma = (int)(current * 1000.f);

      LOG_WRN("ignoring preposterous LPSU current %d mA", ma);
      goto beach;
    }

    if (current > gLaserState.arc.status.maxCurrent) {
      gLaserState.arc.status.maxCurrent = current;
    }
    if (current > 0.f && current < gLaserState.arc.status.minCurrent) {
      gLaserState.arc.status.minCurrent = current;
    }

    // then check it against the various current limits
    if (current >= gLaserState.arc.config.highCurrentLimit) {
      detected = true;
    } else if (current <= gLaserState.arc.config.lowCurrentLimit) {
      detected = true;
    }

    if (detected) {
      if (!gLaserState.arc.emitted) {
        gLaserState.arc.emitted = true;

        if (arc_add_event()) {
          arc_detector_process();
        }
      }
    }
  }
beach:;
  k_mutex_unlock(&gLaserState.mut);
}

/**
 * @brief Process all pending arc events
 *
 * Iterate through all of the pending arc detections; if enough events occurred in the time
 * period configured, an arc alarm is triggered. Regardless, this routine will discard any
 * events that fall outside the configured window.
 *
 * @remark Caller must hold gLaserState lock
 */
static void arc_detector_process() {
  laser_arc_event_t *eventPtr;

  /*
   * Since events are ordered oldest to newest in the buffer, if the event we're currently
   * looking at is too old, increment the read pointer. This will continue until we either
   * find an entry that's not too old (1 or more entries remain) or it's the same as the
   * write pointer (no entries remain)
   */
  const int64_t now = k_uptime_get();
  const int64_t ageThreshold = now - (gLaserState.arc.config.alarmPeriod * 1000);

  while (arc_num_pending()) {
    eventPtr = &(gLaserState.arc.eventStorage[gLaserState.arc.eventReadPtr]);

    // if this eveent's timestamp is ≥ the threshold, it and all subsequent events will be kept
    if (eventPtr->timestamp >= ageThreshold) {
      break;
    }

    // otherwise, drop this event
    gLaserState.arc.eventReadPtr = (gLaserState.arc.eventReadPtr + 1) % NUM_ARC_EVENTS;
  }

  /*
   * Because we dropped all events that are out of range previously, the only events that
   * remain are arc detections that match all criteria.
   */
  if (arc_num_pending() >= gLaserState.arc.config.alarmThreshold) {
    arc_detector_alarm();
  }
}

/**
 * @brief Handle arc detector alarm
 *
 * Invoked when enough arc events in a given time period take place.
 *
 * @remark Caller must hold gLaserState lock
 */
static void arc_detector_alarm() {
  LOG_WRN("arc alarm triggered (min=%d mA, max=%d mA)", (int)(gLaserState.arc.status.minCurrent * 1000.f),
          (int)(gLaserState.arc.status.maxCurrent * 1000.f));

  // set the alarm flag (to inhibit further laser firing)
  gLaserState.arc.status.alarm = true;

  // then force the laser off
  gLaserState.enabled = false;
  laser_apply_state();
}

/**
 * @brief Activate arc detector
 *
 * Invoked when the laser fire signal changes from inactive to asserted.
 *
 * This triggers the arc detector work timer with an interval based on the configuration.
 *
 * @remark Caller must hold gLaserState lock
 */
static void arc_detector_start() {
  if (!gLaserState.arc.config.enabled) {
    return;
  }

  gLaserState.arc.emitted = false;

  k_timer_start(&gArcTimer, K_MSEC(gLaserState.arc.config.initialDelay), K_MSEC(gLaserState.arc.config.sampleInterval));
}

/**
 * @brief Finish arc detection cycle
 *
 * Invoked when laser fire signal is deasserted.
 *
 * @remark Caller must hold gLaserState lock
 */
static void arc_detector_stop() { k_timer_stop(&gArcTimer); }

/**
 * @brief Get current arc detector status
 */
CARBON_RESPONSE_CODE laser_get_arc_detector_status(laser_arc_detector_status_t *outStatus) {
  if (!outStatus) {
    return CARBON_ERROR_UNKNOWN;
  }

  k_mutex_lock(&gLaserState.mut, K_FOREVER);
  { memcpy(outStatus, &gLaserState.arc.status, sizeof(*outStatus)); }
  k_mutex_unlock(&gLaserState.mut);

  return CARBON_RESPONSE_OK;
}

/**
 * @brief Get the current configuration for the arc detector
 */
CARBON_RESPONSE_CODE laser_get_arc_detector_config(laser_arc_detector_config_t *outConfig) {
  if (!outConfig) {
    return CARBON_ERROR_UNKNOWN;
  }

  k_mutex_lock(&gLaserState.mut, K_FOREVER);
  { memcpy(outConfig, &gLaserState.arc.config, sizeof(*outConfig)); }
  k_mutex_unlock(&gLaserState.mut);

  return CARBON_RESPONSE_OK;
}

/**
 * @brief Update the arc detector configuration
 *
 * The new configuration will apply the next time the laser is fired.
 */
CARBON_RESPONSE_CODE laser_set_arc_detector_config(const laser_arc_detector_config_t *newConfig) {
  if (!newConfig) {
    return CARBON_ERROR_UNKNOWN;
  } else if (newConfig->alarmThreshold > NUM_ARC_EVENTS || !newConfig->alarmThreshold) {
    return CARBON_ERROR_UNKNOWN;
  } else if (newConfig->lowCurrentLimit > newConfig->highCurrentLimit) {
    return CARBON_ERROR_UNKNOWN;
  } else if (newConfig->lowCurrentLimit < 0 || newConfig->highCurrentLimit < 0) {
    return CARBON_ERROR_UNKNOWN;
  }

  k_mutex_lock(&gLaserState.mut, K_FOREVER);
  {
    const bool oldEnabled = gLaserState.arc.config.enabled;

    memcpy(&gLaserState.arc.config, newConfig, sizeof(*newConfig));

    // enable -> disable
    if (oldEnabled && !newConfig->enabled) {
      arc_detector_stop();
    }

    // reset internal state
    arc_detector_reset_internal();
  }
  k_mutex_unlock(&gLaserState.mut);

  return CARBON_RESPONSE_OK;
}

/**
 * @brief Reset the arc detector state
 */
CARBON_RESPONSE_CODE laser_arc_detector_reset() {
  k_mutex_lock(&gLaserState.mut, K_FOREVER);
  { arc_detector_reset_internal(); }
  k_mutex_unlock(&gLaserState.mut);

  return CARBON_RESPONSE_OK;
}

/**
 * @brief Get the type of laser the scanner is connected to
 *
 * This is primarily for Reaper (where smart lasers are used and the scanner communicates with
 * them directly) use, but still works for Slayer, where it returns a fixed value.
 */
int laser_get_type(laser_type_t *outType) {
  if (!outType) {
    return -EFAULT;
  }

#ifdef USE_REAPER
  *outType = gLaserState.laserType;
#else
  *outType = kLaserTypeCO2Generic;
#endif
  return 0;
}

/**
 * @brief Check whether laser is currently connected
 */
int laser_is_connected(bool *outIsConnected) {
  if (!outIsConnected) {
    return -EFAULT;
  }

#ifdef USE_REAPER
  switch (gLaserState.laserType) {
  case kLaserTypeDiodeBWT: {
    laser_bwt_flags_t flags = 0;
    HANDLE_UNLIKELY(laser_bwt_get_status_bits(&flags));
    *outIsConnected = !!(flags & kBwtConnected);
    break;
  }

  case kLaserTypeDiodeJlight: {
    laser_jlight_flags_t flags = 0;
    HANDLE_UNLIKELY(laser_jlight_get_status_bits(&flags));
    *outIsConnected = !!(flags & kJlightConnected);
    break;
  }

  default:
    LOG_WRN("%s not supported for laser type %s", __FUNCTION__, laser_get_type_name(gLaserState.laserType));
    return -EINVAL;
  }
#else
  *outIsConnected = true;
#endif

  return 0;
}

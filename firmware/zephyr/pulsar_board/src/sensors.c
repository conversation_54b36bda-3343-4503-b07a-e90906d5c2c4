#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(sensors, CONFIG_APP_LOG_LEVEL);

#include <zephyr/net/ptp_time.h>
#include <zephyr/sys/byteorder.h>
#include <zephyr/zephyr.h>

#include <lib/history_list/history_list.h>
#include <lib/ptp/ptp.h>
#include <utils/handle_errors.h>

#include "can_open.h"
#include "can_open_msg.h"
#include "cia_402.h"
#include "gimbal_handler.h"
#include "laser.h"
#include "power.h"
#include "qdec.h"
#include "sensors.h"
#include "servo.h"

#if USE_REAPER
#include "laser/bwt.h"
#include "laser/nanopb.h"
#endif

#if USE_FAULHABER
#include "drive/faulhaber/ambient_temp.h"
#include "drive/faulhaber/faulhaber.h"
#endif

// PDO flag bit index indicating PDOs can be accepted
#define PDO_FLAGS_ACCEPT (0)

// Depth of the sensor work queue
#define WORK_QUEUE_SIZE (8)

typedef enum {
  kSensorWorkTypeStatusPdo,
  kSensorWorkTypeUpdateAmbient,
} sensor_work_type_t;

// Work item
typedef struct {
  // type of item
  sensor_work_type_t type;

  // payload, depends upon the item
  union {
    struct {
      // underlying CANOpen message
      CAN_Open_Message_t msg;
      // set if this came from pan servo, cleared otherwise
      bool isPan;
    } statusPdo;
  };
} sensor_work_t;

typedef struct {
  // Timestamp at which this data was updated
  struct net_ptp_time timestamp;

  // Whether the last poll of the below data was successful
  bool connected;

  // Device serial number
  uint32_t sn;

  // Rated continuous current, in amps
  float ratedCurrent;

  // Motor supply voltage
  float motorVolts;
  // Motor current
  float motorAmps;
  // Temperature of output stage
  float outputTemp;
  // Model calculated motor winding temperature
  float windingsTemp;
} servo_status_t;

static struct {
  // Lock to guard the data in the state struct
  struct k_mutex lock;
  // Semaphore signalled whenever the initial data has been read out
  struct k_sem initSem;

  // history list for the recording of encoder values
  history_list *qdecList;

  // servo state
  servo_status_t servos[2];
  // PDO processing flags
  atomic_t pdoFlags;
} gState;

static void sensors_worker();
static int handle_servo_status_pdo(const bool isPan, const CAN_Open_Message_t *msg);

#if USE_FAULHABER
static int get_servo_info_fh(const size_t nodeId, CAN_Open_Message_t *message);
static int handle_servo_status_pdo_fh(const bool isPan, const CAN_Open_Message_t *msg);
#endif

#if USE_REAPER
static int fill_reaper_status(pulczar_HwStatus_Reaper_Reply *);
#else
static int fill_slayer_status(pulczar_HwStatus_Slayer_Reply *);
#endif

static void fill_servo_status(const servo_status_t *, pulczar_HwStatus_Servo *);
static void fill_servo_encoder(pulczar_HwStatus_Servo *pan, pulczar_HwStatus_Servo *tilt);

// Worker thread to use for sensor readout
K_THREAD_DEFINE(gSensorsWorkerThread, 1536, sensors_worker, NULL, NULL, NULL, K_PRIO_PREEMPT(10), 0, K_TICKS_FOREVER);
// Message queue for work items relating to the sensors
K_MSGQ_DEFINE(gSensorsWorkQueue, sizeof(sensor_work_t), WORK_QUEUE_SIZE, 8);

/**
 * @brief Initialize the sensor subsystem
 *
 * Ensure all device nodes are set up and then start the worker task that periodically reads out
 * relevant sensors.
 */
int sensors_init(history_list *qdecList) {
  int err;

#if USE_FAULHABER
  // set up FH ambient temperature reporting also
  HANDLE_UNLIKELY(fh_ambient_init());
#endif

  // default servo data as NaN
  for (size_t i = 0; i < ARRAY_SIZE(gState.servos); i++) {
    servo_status_t *servo = &gState.servos[i];
    servo->motorVolts = __builtin_nan("0");
    servo->motorAmps = __builtin_nan("0");
    servo->outputTemp = __builtin_nan("0");
  }

  // set up state struct
  k_mutex_init(&gState.lock);
  k_sem_init(&gState.initSem, 0, 1);

  gState.qdecList = qdecList;

  // now we can start the worker
  LOG_INF("starting sensor worker");

  k_thread_name_set(gSensorsWorkerThread, "sensors_worker");
  k_thread_start(gSensorsWorkerThread);

  // wait some time for worker to finish reading out motor controller data
  err = k_sem_take(&gState.initSem, K_SECONDS(2));

  if (err == -EAGAIN) {
    LOG_WRN("Timeout reading controller data (something is really broken!)");
  } else if (err) {
    LOG_ERR("%s failed: %d", "k_sem_take", err);
    return err;
  }

  return 0;
}

/**
 * @brief Handle the `hw_info` nanopb endpoint
 *
 * This is used to read out the current sensor values.
 */
void sensors_handle_nanopb(npb_request_data *req) {
  int err;

  // fill the request header
  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = req->request.header.requestId;
  reply.which_reply = pulczar_board_Reply_pulczar_tag;
  reply.reply.pulczar.which_reply = pulczar_Reply_hw_status_tag;

  pulczar_HwStatus_Reply *statusReply = &reply.reply.pulczar.reply.hw_status;

#if USE_REAPER
  statusReply->which_reply = pulczar_HwStatus_Reply_reaper_tag;

  err = fill_reaper_status(&statusReply->reply.reaper);
  if (err) {
    LOG_ERR("%s failed: %d", "fill_reaper_status", err);
  }
#else
  statusReply->which_reply = pulczar_HwStatus_Reply_slayer_tag;

  err = fill_slayer_status(&statusReply->reply.slayer);
  if (err) {
    LOG_ERR("%s failed: %d", "fill_slayer_status", err);
  }
#endif

  // send reply message
  npb_send_reply(&reply, &req->metadata);
}

/**
 * @brief Process a status PDO from the motor controller
 *
 * Invoked directly from the CAN receive handler; should place the message on our work queue and
 * return.
 */
int sensors_handle_status_pdo(const uint8_t node_id, const CAN_Open_Message_t *msg) {
  sensor_work_t work;
  memset(&work, 0, sizeof(work));

  // drop the PDO if the worker isn't ready
  if (!atomic_test_bit(&gState.pdoFlags, PDO_FLAGS_ACCEPT)) {
    return 0;
  }

  // determine the servo from which this message originated based on nodeid
  if (node_id != NODE_ID_PAN_SERVO && node_id != NODE_ID_TILT_SERVO) {
    return -EINVAL;
  }
  bool isPan = (node_id == NODE_ID_PAN_SERVO);

  // place on work queue
  work.type = kSensorWorkTypeStatusPdo;
  work.statusPdo.msg = *msg;
  work.statusPdo.isPan = isPan;

  // we musn't block: called from CAN RX ISR!
  HANDLE_UNLIKELY(k_msgq_put(&gSensorsWorkQueue, &work, K_NO_WAIT));

  return 0;
}

/**
 * @brief Trigger an update of ambient temperature on MCs
 */
int sensors_request_ambient_update() {
  sensor_work_t work;
  memset(&work, 0, sizeof(work));

  work.type = kSensorWorkTypeUpdateAmbient;

  return k_msgq_put(&gSensorsWorkQueue, &work, K_NO_WAIT);
}

/**
 * @brief Sensor worker main loop
 *
 * Fetch initial (constant) information from the controllers (serial numbers, rated currents, etc.)
 * and then process work items.
 */
static void sensors_worker() {
  int err;
  CAN_Open_Message_t msg;
  size_t servoCounter = 0;

  // read out motor controller constants
  LOG_INF("Reading motor controller info...");

#if USE_FAULHABER
  for (size_t i = 0; i < ARRAY_SIZE(gState.servos); i++) {
    err = get_servo_info_fh((i == 0) ? NODE_ID_PAN_SERVO : NODE_ID_TILT_SERVO, &msg);
    if (err) {
      LOG_ERR("%s failed (for %s): %d", "get_servo_info_fh", (i == 0) ? "pan servo" : "tilt servo", err);
    }

    gState.servos[i].connected = !err;
  }
#endif

  atomic_set_bit(&gState.pdoFlags, PDO_FLAGS_ACCEPT);
  k_sem_give(&gState.initSem);

  // poll on the work queue
  do {
    sensor_work_t work;
    err = k_msgq_get(&gSensorsWorkQueue, &work, K_FOREVER);
    if (err) {
      LOG_WRN("%s failed: %d", "k_msgq_get", err);
      continue;
    }

    switch (work.type) {
    case kSensorWorkTypeStatusPdo:
      err = handle_servo_status_pdo(work.statusPdo.isPan, &work.statusPdo.msg);
      break;

#if USE_FAULHABER
    case kSensorWorkTypeUpdateAmbient:
      err = fh_ambient_update();
      break;
#endif

    default:
      LOG_WRN("unknown work item type %u", work.type);
      continue;
    }

    if (err) {
      LOG_WRN("failed to process work item (type=%u): %d", work.type, err);
    }
  } while (true);
}

/**
 * @brief Handle status report message from MC
 *
 * Handles the PDO(s) sent by the controllers.
 */
static int handle_servo_status_pdo(const bool isPan, const CAN_Open_Message_t *msg) {
#if USE_FAULHABER
  return handle_servo_status_pdo_fh(isPan, msg);
#else
  // NOTE: not supported for EPOS, but we should never get here
  return -ENOTSUP;
#endif
}

/**
 * @brief Fill the nanopb structure for a servo
 */
static void fill_servo_status(const servo_status_t *servo, pulczar_HwStatus_Servo *msg) {
  if (servo->connected) {
    msg->connected = true;
    msg->controller_sn = servo->sn;

    msg->sensor_time_ms = ptp_ts_net_to_ms((struct net_ptp_time *)&servo->timestamp);
    msg->motor_supply_v = servo->motorVolts;
    msg->motor_current_a = servo->motorAmps;
    msg->output_stage_temp_c = servo->outputTemp;
  } else {
    msg->connected = false;
  }
}
/**
 * @brief Fill in the encoder position of the servo structs
 *
 * This is a separate call so it can be done right before sending the nanopb message such that the
 * position data is accurate
 */
static void fill_servo_encoder(pulczar_HwStatus_Servo *pan, pulczar_HwStatus_Servo *tilt) {
  // get qdec
  history_record r;
  history_list_get_latest(gState.qdecList, &r);
  qdec_record *qrec = (qdec_record *)r.record;
  const uint64_t timestamp = (*r.usec) / USEC_PER_MSEC;

  // convert pan
  pan->encoder_time_ms = timestamp;
  pan->encoder_ticks = servo_get_position(get_pan_servo(), qrec);

  // convert tilt
  tilt->encoder_time_ms = timestamp;
  tilt->encoder_ticks = servo_get_position(get_tilt_servo(), qrec);
}

#if USE_FAULHABER
/**
 * @brief Read out the Faulhaber controller general info
 *
 * This consists of the rated current and serial number via the identity SDO.
 */
static int get_servo_info_fh(const size_t nodeId, CAN_Open_Message_t *message) {
  uint32_t sn;
  float ratedCurrent;

  const size_t servoIdx = (nodeId == NODE_ID_PAN_SERVO) ? 0 : 1;
  servo_status_t *servo = &gState.servos[servoIdx];

  // Get serial number from identity object
  HANDLE_UNLIKELY(CIA402_Read_Last_Serial_Number(nodeId, message));
  do {
    uint32_t u32;
    memcpy(&u32, message->pkt.sdo.data, sizeof(u32));

    sn = sys_le32_to_cpu(u32);
  } while (0);

  // get the rated current/voltage
  HANDLE_UNLIKELY(Send_SDO_Upload_Request(nodeId, message, FH_SDO_DEVICE_RATINGS, FH_SDO_DEVICE_RATINGS_I_CONTINUOUS));
  do {
    uint16_t u16;
    memcpy(&u16, message->pkt.sdo.data, sizeof(u16));

    ratedCurrent = ((float)sys_le16_to_cpu(u16)) / 1000.f;
  } while (0);

  // then store this information in the servo struct
  k_mutex_lock(&gState.lock, K_FOREVER);
  do {
    servo->sn = sn;
    servo->ratedCurrent = ratedCurrent;

    LOG_INF("Servo %u (%s): sn=%09u, I_cont = %u mA", nodeId, (nodeId == NODE_ID_PAN_SERVO) ? "pan" : "tilt", servo->sn,
            (unsigned int)(servo->ratedCurrent * 1000.f));
  } while (0);
  k_mutex_unlock(&gState.lock);

  return 0;
}

/**
 * @brief Decode FH servo status PDO
 *
 * FH controllers use TXPDO 3 to communicate status information, encoded as follows:
 *
 * 0           2           4           6           8
 * +-----------+-----------+-----------+-----------+
 * |  Motor V  |  Motor I  | Out Temp  | Motor Temp|
 * |    U16    |    U16    |    U16    |    U16    |
 * +-----------+-----------+-----------+-----------+
 */
static int handle_servo_status_pdo_fh(const bool isPan, const CAN_Open_Message_t *msg) {
  struct net_ptp_time now;
  HANDLE_UNLIKELY(ptp_slave_clk_get(&now));

  const size_t servoIdx = isPan ? 0 : 1;
  servo_status_t *servo = &gState.servos[servoIdx];

  const uint8_t *pdoPayload = msg->pkt.pdo.data;

  if (msg->func == CAN_OPEN_FUNC_CODE_PDO3_TX) {
    k_mutex_lock(&gState.lock, K_FOREVER);
    do {
      servo->timestamp = now;
      servo->motorVolts = ((float)sys_get_le16(pdoPayload + 0)) / 100.f;
      servo->motorAmps = servo->ratedCurrent * (((float)((int16_t)sys_get_le16(pdoPayload + 2))) / 1000.f);
      servo->outputTemp = (float)((int16_t)sys_get_le16(pdoPayload + 4));
      servo->windingsTemp = (float)((int16_t)sys_get_le16(pdoPayload + 6));
    } while (0);
    k_mutex_unlock(&gState.lock);
  } else {
    LOG_WRN("Got unknown FH PDO (func = %x)", msg->func);
  }

  return 0;
}
#endif

#if USE_REAPER
/**
 * @brief Fill in Reaper sensor request
 */
static int fill_reaper_status(pulczar_HwStatus_Reaper_Reply *reply) {
  int err;

  // get internal state
  k_mutex_lock(&gState.lock, K_FOREVER);
  {
    for (size_t i = 0; i < ARRAY_SIZE(gState.servos); i++) {
      pulczar_HwStatus_Servo *servoReply = (i == 0) ? &reply->_servo_pan.servo_pan : &reply->_servo_tilt.servo_tilt;
      fill_servo_status(&gState.servos[i], servoReply);
    }

    reply->which__servo_pan = pulczar_HwStatus_Reaper_Reply_servo_pan_tag;
    reply->which__servo_tilt = pulczar_HwStatus_Reaper_Reply_servo_tilt_tag;
  }
  k_mutex_unlock(&gState.lock);

  // get laser status
  HANDLE_UNLIKELY(laser_is_connected(&reply->laser_connected));

  err = laser_fill_nanopb_status(&reply->laser_status);
  if (err) {
    LOG_WRN("%s failed: %d", "laser_fill_nanopb_status", err);
  } else {
    reply->has_laser_status = true;
  }

  err = laser_fill_nanopb_inventory(&reply->laser_inventory);
  if (err) {
    LOG_WRN("%s failed: %d", "laser_fill_nanopb_inventory", err);
  } else {
    reply->has_laser_inventory = true;
  }

  // thermistor and power meter
  uint32_t temp;

  HANDLE_UNLIKELY(laser_get_reaper_therm(&reply->laser_therm_temp_c[0], &reply->laser_therm_temp_c[1]));
  reply->laser_therm_temp_c_count = 2;

  HANDLE_UNLIKELY(laser_get_raw_photodiode(&temp));
  reply->laser_power_raw_mv = temp;

  // TODO: laser power meter, as watts

  // target cam power flags
  power_channel_t power_state = 0;
  HANDLE_UNLIKELY(power_get_state(&power_state));

  reply->target_cam_power_on = (power_state & kPowerTargetCam);

  // servo encoder pos
  fill_servo_encoder(&reply->_servo_pan.servo_pan, &reply->_servo_tilt.servo_tilt);

  return 0;
}
#else
/**
 * @brief Fill in Slayer sensor request
 */
static int fill_slayer_status(pulczar_HwStatus_Slayer_Reply *reply) {
  // get internal state
  k_mutex_lock(&gState.lock, K_FOREVER);
  {
    for (size_t i = 0; i < ARRAY_SIZE(gState.servos); i++) {
      pulczar_HwStatus_Servo *servoReply = (i == 0) ? &reply->_servo_pan.servo_pan : &reply->_servo_tilt.servo_tilt;
      fill_servo_status(&gState.servos[i], servoReply);
    }

    reply->which__servo_pan = pulczar_HwStatus_Slayer_Reply_servo_pan_tag;
    reply->which__servo_tilt = pulczar_HwStatus_Slayer_Reply_servo_tilt_tag;
  }
  k_mutex_unlock(&gState.lock);

  // thermistors
  uint32_t temp[2];

  HANDLE_UNLIKELY(laser_get_raw_readings(&temp[0], &temp[1]));
  reply->lpm_thermistor_beam_raw_mv = temp[1];
  reply->lpm_thermistor_ambient_raw_mv = temp[0];

  HANDLE_UNLIKELY(laser_get_slayer_therm(&reply->lpm_thermistor_ambient_temp_c, &reply->lpm_thermistor_beam_temp_c));

  // LPSU state
  HANDLE_UNLIKELY(laser_get_lpsu_current(&reply->lpsu_current_ma));
  HANDLE_UNLIKELY(laser_get_lpsu_state(&reply->lpsu_status));

  // target cam power flags
  power_channel_t power_state = 0;
  HANDLE_UNLIKELY(power_get_state(&power_state));

  reply->target_cam_power_on = (power_state & kPowerTargetCam);

  // servo encoder pos
  fill_servo_encoder(&reply->_servo_pan.servo_pan, &reply->_servo_tilt.servo_tilt);

  return 0;
}

#endif

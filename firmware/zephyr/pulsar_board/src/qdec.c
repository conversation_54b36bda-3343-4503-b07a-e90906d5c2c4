#include <logging/log.h>
LOG_MODULE_REGISTER(qdec, CONFIG_APP_LOG_LEVEL);

#include <devicetree.h>
#include <drivers/qdec64k.h>
#include <lib/ptp/ptp.h>
#include <string.h>
#include <utils/handle_errors.h>

#include "qdec.h"

typedef struct {
  const struct device *qdec64k_pan;
  const struct device *qdec64k_tilt;
  history_list *hist;
} qdec_state;

qdec_state state;
uint32_t qdec_tick_scale = 1;

int qdec_init(history_list *hist, uint32_t tick_scale) {
  state.hist = hist;
  qdec_tick_scale = tick_scale;
  /* ensure quadrature decoders are operational */
  state.qdec64k_pan = DEVICE_DT_GET(DT_NODELABEL(qdec64k_pan));
  HANDLE_UNLIKELY_BOOL(device_is_ready(state.qdec64k_pan), ENODEV);
  state.qdec64k_tilt = DEVICE_DT_GET(DT_NODELABEL(qdec64k_tilt));
  HANDLE_UNLIKELY_BOOL(device_is_ready(state.qdec64k_tilt), ENODEV);
  return 0;
}

history_list *qdec_get_history_list() { return state.hist; }

int64_t qdec_get_latest(uint8_t id) {
  history_record r;
  history_list_get_latest(state.hist, &r);
  qdec_record *qrec = (qdec_record *)r.record;
  return qrec->ticks[id];
}

void qdec_tick(void *arg) {
  qdec_record record;
  uint64_t usec;
  history_record cur_record;
  cur_record.usec = &usec;
  cur_record.record = (uint8_t *)&record;
  struct net_ptp_time ts;

  HANDLE_CRITICAL(sensor_sample_fetch(state.qdec64k_pan));
  HANDLE_CRITICAL(sensor_sample_fetch(state.qdec64k_tilt));
  HANDLE_CRITICAL(ptp_slave_clk_get(&ts));

  history_record prev;
  history_list_get_latest(state.hist, &prev);
  qdec_record *prev_record = (qdec_record *)prev.record;
  struct sensor_value val;

  HANDLE_CRITICAL(sensor_channel_get(state.qdec64k_pan, SENSOR_CHAN_QDEC64K_TICKS, &val));
  record.ticks[0] = qdec64k_adjust_ticks(prev_record->ticks[0], val.val1) * qdec_tick_scale;
  HANDLE_CRITICAL(sensor_channel_get(state.qdec64k_tilt, SENSOR_CHAN_QDEC64K_TICKS, &val));
  record.ticks[1] = qdec64k_adjust_ticks(prev_record->ticks[1], val.val1) * qdec_tick_scale;

  usec = ptp_ts_net_to_us(&ts);
  history_list_add_record(state.hist, &cur_record);
}

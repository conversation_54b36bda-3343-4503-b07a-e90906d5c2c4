#pragma once

#include <stdint.h>
#include <utils/carbon_response_codes.h>
#include <zephyr/device.h>
#include <zephyr/zephyr.h>

#include "can_open_msg.h"
#include "generated/lib/drivers/nanopb/proto/can_open.pb.h"

/**
 * @brief Message queue (for awaiting)
 */
typedef enum {
  // Node ID 1
  kCanOpenMsgQueuePan,
  // Node ID 2
  kCanOpenMsgQueueTilt,
  // Node ID 3
  kCanOpenMsgQueueBonusNode,
  // Global queue for LSS messages
  kCanOpenMsgQueueLss,
} can_open_msgq_t;

#define NODE_ID_PAN_SERVO (1)
#define NODE_ID_TILT_SERVO (2)
// used for LSS assignment
#define NODE_ID_BONUS (3)

int can_open_init();

void clear_mailbox(uint8_t node_id);

bool canopen_is_valid_id(uint8_t node_id);
void Send_SDO(uint8_t node_id, uint16_t index, uint8_t subindex, uint32_t value, uint8_t cs, uint8_t expedited);
void Send_PDO(uint8_t node_id, uint8_t func, uint8_t data[8], uint8_t size);
void Send_RTR_PDO(uint8_t node_id, uint8_t func);
void Send_NMT(uint8_t node_id, uint8_t state);

CARBON_RESPONSE_CODE Await_Reply(uint8_t node_id, CAN_Open_Message_t *msg, uint16_t timeout_ms, uint8_t func);
int Await_Reply_Ex(const can_open_msgq_t whence, CAN_Open_Message_t *msg, const k_timeout_t timeout, uint8_t func);

CARBON_RESPONSE_CODE Send_SDO_Download(uint8_t node_id, uint16_t index, uint8_t subindex, uint32_t value);
CARBON_RESPONSE_CODE Send_SDO_Upload_Request(uint8_t node_id, CAN_Open_Message_t *msg, uint16_t index,
                                             uint8_t subindex);
// NMT API
CARBON_RESPONSE_CODE NMT_Reset_Node(uint8_t node_id);
CARBON_RESPONSE_CODE NMT_Start_Node(uint8_t node_id);
CARBON_RESPONSE_CODE NMT_Stop_Node(uint8_t node_id);
CARBON_RESPONSE_CODE can_open_to_npb_msg(const CAN_Open_Message_t *in, can_open_Message_Reply *out);

// LSS API
int Configure_FH_Node_IDs();

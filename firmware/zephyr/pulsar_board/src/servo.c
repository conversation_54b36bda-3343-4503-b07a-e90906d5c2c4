#include <logging/log.h>
LOG_MODULE_REGISTER(servo, CONFIG_APP_LOG_LEVEL);
#include "can_open.h"
#include "eeprom.h"
#include "motion_controller.h"
#include "qdec.h"
#include "servo.h"
#include <lib/ptp/ptp.h>
#include <stdlib.h>
#include <utils/carbon_response_codes.h>
#include <utils/handle_errors.h>

#define NPB_TS_TO_USEC(ts) (uint64_t) ts.seconds * 1000000 + (uint64_t)ts.micros
#define USEC_TO_NPB_TS(ts, usecs)                                                                                      \
  ts.seconds = usecs / 1000000;                                                                                        \
  ts.micros = usecs % 1000000;
int32_t int_div_round(int32_t numerator, int32_t denomenator) {
  if (numerator == 0) {
    return 0;
  }
  if (numerator < 0) {
    if (denomenator < 0) {
      if (denomenator == -1) {
        return -numerator;
      } else {
        return (numerator + (-denomenator + 1) / 2) / denomenator + 1;
      }
    } else {
      if (denomenator == 1) {
        return numerator;
      } else {
        return (numerator + (denomenator + 1) / 2) / denomenator - 1;
      }
    }
  } else {
    if (denomenator < 0) {
      return (numerator - (-denomenator + 1) / 2) / denomenator - 1;
    } else {
      return (numerator - (denomenator + 1) / 2) / denomenator + 1;
    }
  }
}

void servo_init(epos_servo *servo) { eeprom_load_pid(servo->opencan_id, &servo->pid_cfg); }
struct pid_config_s *servo_get_pid_cfg(epos_servo *servo) {
  return &servo->pid_cfg;
}
void servo_add_msg(epos_servo *servo, servo_Request *req, servo_Reply *resp, uint32_t timeout_ms) {
  servo_msg msg;
  msg.req = req;
  msg.resp = resp;
  servo->config.settle_timeout = MC_DEFAULT_SETTLE_TIMEOUT_MS;
  servo->config.settle_window = MC_DEFAULT_SETTLE_WINDOW;
  k_msgq_put(servo->input, &msg, K_MSEC(timeout_ms));
}
void servo_await_resp(epos_servo *servo, uint32_t timeout_ms) { k_sem_take(servo->ready, K_MSEC(timeout_ms)); }

static CARBON_RESPONSE_CODE servo_go_to(epos_servo *servo, int32_t position, uint32_t velocity, bool await,
                                        uint16_t timeout_ms) {
  if (!(position >= 0 && position <= servo->limit && velocity <= servo->config.max_profile_velocity)) {
    return CARBON_EPOS_SERVO_MOVE_OUT_OF_RANGE;
  }
  RETURN_CODE_IF_NOT_OK(MC_get()->Go_To_Position(servo->opencan_id, position, velocity));

  if (await) {
    if (timeout_ms == 0) {
      timeout_ms = servo->config.settle_timeout;
    }
    RETURN_CODE_IF_NOT_OK(
        MC_get()->Await_Settling(servo->opencan_id, position, servo->config.settle_window, timeout_ms, NULL));
  }

  return CARBON_RESPONSE_OK;
}

uint64_t get_timestamp_us() {
  struct net_ptp_time ts;
  HANDLE_CRITICAL(ptp_slave_clk_get(&ts));
  return ptp_ts_net_to_us(&ts);
}
uint64_t get_timestamp_ms() { return get_timestamp_us() / 1000; }

static CARBON_RESPONSE_CODE servo_calibration_settle(epos_servo *servo, int32_t target_position, uint16_t window,
                                                     uint16_t time_window_ms, uint16_t timeout_ms,
                                                     uint16_t *settle_time_ms_out) {
  history_record r;
  history_list_get_latest(qdec_get_history_list(), &r);
  uint64_t start_time = *(r.usec);
  uint64_t current_time = start_time;
  uint64_t prev_time = start_time;
  int32_t current_position = 0;
  int32_t current_error = 0;
  uint64_t time_window_us = time_window_ms * 1000;
  uint64_t timeout_us = timeout_ms * 1000;

  bool found_first = false;
  uint64_t first_in_window_time = 0;

  do {
    history_list_get_latest(qdec_get_history_list(), &r);
    current_time = *(r.usec);
    if (current_time < (prev_time + 100)) {
      k_yield();
      continue; // Need to wait a bit for new data
    }
    current_position = (int32_t)servo_get_position(servo, (qdec_record *)r.record);
    current_error = abs(target_position - current_position);

    if (current_error < window) {
      if (!found_first) {
        first_in_window_time = current_time;
        found_first = true;
      } else if (current_time - first_in_window_time > time_window_us) {
        *settle_time_ms_out = (first_in_window_time - start_time) / 1000;
        return CARBON_RESPONSE_OK;
      }
    } else {
      found_first = false;
    }
    prev_time = current_time;
  } while ((current_time - start_time) < timeout_us);
  return CARBON_EPOS_SETTLE_TIMEOUT;
}
static CARBON_RESPONSE_CODE servo_go_to_calibrate(epos_servo *servo, int32_t position, uint32_t velocity,
                                                  uint16_t window, uint16_t time_window_ms, uint16_t timeout_ms,
                                                  uint8_t period_ms, uint16_t *settle_time_ms_out) {
  if (!(position >= 0 && position <= servo->limit && velocity <= servo->config.max_profile_velocity)) {
    return CARBON_EPOS_SERVO_MOVE_OUT_OF_RANGE;
  }
  RETURN_CODE_IF_NOT_OK(MC_get()->Go_To_Position(servo->opencan_id, position, velocity))
  RETURN_CODE_IF_NOT_OK(
      servo_calibration_settle(servo, position, window, time_window_ms, timeout_ms, settle_time_ms_out))

  return CARBON_RESPONSE_OK;
}

CARBON_RESPONSE_CODE servo_follow_timestamp(epos_servo *servo, uint64_t *timestamp, int32_t follow_velocity,
                                            int32_t follow_accel, int32_t *out_p1, uint64_t *out_t1,
                                            int32_t extra_time_diff_ms) {
  history_record r;
  history_list_get_latest(qdec_get_history_list(), &r);
  *out_p1 = (int32_t)servo_get_position(servo, (qdec_record *)r.record);
  *out_t1 = *r.usec;

  int32_t diff_millis = 0;
  if (*out_t1 >= *timestamp) {
    // If PTP is not functioning we start at time 0 and could have very different times enough to overflow signed int
    diff_millis = *out_t1 - *timestamp;
    diff_millis = int_div_round(diff_millis, 1000);
  }

  if (diff_millis > servo->config.max_diff_millis + extra_time_diff_ms) {
    // TODO: Until We Figure out the 808 issue, we don't return an error here but simply presume no time has elapsed
    // return M_ERROR_CODE_SERVO_FOLLOW_TIME_DIFF_TOO_BIG;
  } else {
    follow_velocity = follow_velocity + int_div_round(follow_accel * diff_millis, 1000);
  }

  if (follow_velocity > 0 || follow_velocity < 0) {
    int32_t follow_position = follow_velocity > 0 ? servo->limit : 0;
    // perform division as 64 bit such that it does not overflow
    const uint64_t follow_velocity_sec = ((uint64_t)abs(follow_velocity)) * 60ULL * 1000ULL;
    const uint64_t resolution = servo->resolution;
    const uint32_t follow_velocity_mrpm = (uint32_t)(((follow_velocity_sec) + (resolution - 1ULL)) / resolution);

    // Follow
    if (!(follow_position >= 0 && follow_position <= servo->limit &&
          follow_velocity_mrpm <= servo->config.max_profile_velocity)) {
      return CARBON_EPOS_SERVO_MOVE_OUT_OF_RANGE;
    }
    RETURN_CODE_IF_NOT_OK(MC_get()->Go_To_Position(servo->opencan_id, follow_position, follow_velocity_mrpm))
  }

  return CARBON_RESPONSE_OK;
}

int32_t tilt_error = 0;
int32_t pan_error = 0;

CARBON_RESPONSE_CODE servo_go_to_trajectory(epos_servo *servo, uint64_t *timestamp, int32_t position,
                                            uint32_t velocity_mrpm, int32_t follow_velocity, uint16_t timeout,
                                            int32_t *out_p1, int32_t *out_p2, uint64_t *out_t1, uint64_t *out_t2) {
  const int32_t period = 1;                  // ms
  const int32_t move_delay = 1;              // ms
  const int32_t follow_tolerance = 40;       // ticks
  const int32_t follow_return_threshold = 5; // counts
  const uint64_t timestamp_offset = 35000;   // us
#ifdef BENCH_TEST
  const uint64_t target_timestamp = get_timestamp_ms() + 35;
#else
  const uint64_t target_timestamp = (*timestamp + timestamp_offset) / 1000; // ms
#endif
  const int32_t target_pos = position; // ticks
  const int32_t target_vel_mrpm = int_div_round(abs(follow_velocity) * 60 * 1000, 262144);
  const int32_t slow_speed = 5000; // mrpm

  uint64_t cur_timestamp = get_timestamp_ms(); // ms
  int32_t cur_error = 0;                       // Current error (traj_pos - cur_pos)
  int32_t cur_pos = 0;                         // Current position (ticks)
  int32_t cur_vel = 0;                         // Current velocity (ticks / sec)
  int32_t traj_pos = 0;                        // Trajectory position (ticks)
  int32_t traj_vel = follow_velocity;          // Trajectory velocity (ticks / sec)
  int32_t cmd_vel = velocity_mrpm;             // mrpm

  int32_t follow_count = 0; // Number of cycles following error has been less than follow_tolerance

  do {
    // Get current gimbal position & velocity
    if (MC_get()->Get_Actual_Position_Velocity(servo->opencan_id, &cur_pos, &cur_vel) != CARBON_RESPONSE_OK) {
      k_sleep(K_MSEC(period));
      continue;
    }

    // Update trajectory
    cur_timestamp = get_timestamp_ms();
    traj_pos = target_pos - int_div_round((target_timestamp - cur_timestamp) * traj_vel, 1000);
    cur_error = traj_pos - cur_pos;

    // Check if within following distance
    if (abs(cur_error) < follow_tolerance) {
      follow_count++;
    }

    // Write to global vars for debugging
    if (servo->opencan_id == 1) {
      pan_error = cur_error;
    } else {
      tilt_error = cur_error;
    }

    // Check if we've followed long enough to return
    if (follow_count >= follow_return_threshold ||
        (servo->opencan_id == 1 && abs(cur_error) < 10 && follow_count > 1)) {
#ifdef BENCH_TEST
      int32_t early = target_timestamp - cur_timestamp;
      LOG_INF("Node %u returning %i ms early w/ %i tick following error", servo->opencan_id, early, cur_error);
#endif
      return servo_follow_timestamp(servo, timestamp, follow_velocity, 0, out_p2, out_t2,
                                    30 + servo->config.settle_timeout);
    }

    // Determine next command velocity
    if (follow_count > 0 && (abs(cur_error) < follow_tolerance)) {
      // Taper command velocity off to the follow velocity
      cmd_vel = target_vel_mrpm +
                (slow_speed - slow_speed * (follow_return_threshold - follow_count + 1) / follow_return_threshold);
      //  ^^ Looks weird but goal is to have:
      // (1) cmd_vel = slow_speed when follow_count is 1
      // (2) cmd_vel = target_vel_mrpm when follow_count is at threshold
      // (3) cmd_vel linear in between (1) and (2)
    } else {
      cmd_vel = velocity_mrpm;
    }

    // Send next command
    RETURN_CODE_IF_NOT_OK(MC_get()->Go_To_Position(servo->opencan_id, traj_pos, cmd_vel))

    k_sleep(K_MSEC(period));

  } while (cur_timestamp < target_timestamp);

  // If we've failed to follow trajectory and return, then just send a follow velocity command
  LOG_INF("Node %u failed to converge to trajectory (cur_pos: %i, cur_vel: %i, traj_pos: %i, traj_vel: %i)",
          servo->opencan_id, cur_pos, cur_vel, traj_pos, traj_vel);
  return servo_follow_timestamp(servo, timestamp, follow_velocity, 0, out_p2, out_t2,
                                30 + servo->config.settle_timeout);
}

CARBON_RESPONSE_CODE servo_go_to_timestamp(epos_servo *servo, uint64_t *timestamp, servo_GoToMode mode,
                                           int32_t position, uint32_t velocity_mrpm, int32_t follow_velocity,
                                           int32_t follow_accel, uint16_t interval_sleep_time_ms, int32_t *out_p1,
                                           int32_t *out_p2, uint64_t *out_t1, uint64_t *out_t2) {

  history_record r;
  history_list_get_latest(qdec_get_history_list(), &r);
  *out_p1 = (int32_t)servo_get_position(servo, (qdec_record *)r.record);
  *out_t1 = *r.usec;

  int32_t diff_millis = 0;
  if (*out_t1 >= *timestamp) {
    // If PTP is not functioning we start at time 0 and could have very different times enough to overflow signed int
    diff_millis = *out_t1 - *timestamp;
    diff_millis = int_div_round(diff_millis, 1000);
  }

  if (diff_millis > servo->config.max_diff_millis) {
    // TODO: Until We Figure out the 808 issue, we don't return an error here but simply presume no time has elapsed
    // return M_ERROR_CODE_SERVO_TIME_DIFF_TOO_BIG;
  } else {
    position = position + int_div_round(diff_millis * follow_velocity, 1000) +
               int_div_round(int_div_round(follow_accel * diff_millis * diff_millis, 1000000), 2);
  }

  if (!(position >= 0 && position <= servo->limit && velocity_mrpm <= servo->config.max_profile_velocity)) {
    return CARBON_EPOS_SERVO_MOVE_OUT_OF_RANGE;
  }

  switch (mode) {
  case servo_GoToMode_IMMEDIATE:
    RETURN_CODE_IF_NOT_OK(MC_get()->Go_To_Position(servo->opencan_id, position, velocity_mrpm))
    break;
  case servo_GoToMode_REACHED:
    RETURN_CODE_IF_NOT_OK(MC_get()->Go_To_Position(servo->opencan_id, position, velocity_mrpm))
    RETURN_CODE_IF_NOT_OK(MC_get()->Await_Target_Reached(servo->opencan_id, servo->config.settle_timeout, NULL))
    break;
  case servo_GoToMode_SETTLED:
    RETURN_CODE_IF_NOT_OK(MC_get()->Go_To_Position(servo->opencan_id, position, velocity_mrpm))
    RETURN_CODE_IF_NOT_OK(MC_get()->Await_Settling(servo->opencan_id, position, servo->config.settle_window,
                                                   servo->config.settle_timeout, NULL))
    break;
  case servo_GoToMode_TRAJECTORY:
#ifdef USE_FAULHABER
    return servo_go_to_trajectory(servo, timestamp, position, velocity_mrpm, follow_velocity, interval_sleep_time_ms,
                                  out_p1, out_p2, out_t1, out_t2);
#else
    RETURN_CODE_IF_NOT_OK(MC_get()->Go_To_Position(servo->opencan_id, position, velocity_mrpm))
#endif
    break;
  default:
    return CARBON_EPOS_ERROR_UNKNOWN;
  }

  // Arbitrary sleep
  k_sleep(K_MSEC(interval_sleep_time_ms));

  return servo_follow_timestamp(servo, timestamp, follow_velocity, follow_accel, out_p2, out_t2,
                                interval_sleep_time_ms + servo->config.settle_timeout);
}
CARBON_RESPONSE_CODE servo_go_to_follow(epos_servo *servo, uint64_t *timestamp, int32_t position,
                                        uint32_t velocity_mrpm, int32_t follow_velocity, int32_t follow_accel,
                                        uint16_t interval_sleep_time_ms, int32_t *out_p, uint64_t *out_t) {
  history_record r;
  history_list_get_latest(qdec_get_history_list(), &r);
  *out_p = (int32_t)servo_get_position(servo, (qdec_record *)r.record);
  *out_t = *r.usec;

  int32_t diff_millis = 0;
  if (*out_t >= *timestamp) {
    // If PTP is not functioning we start at time 0 and could have very different times enough to overflow signed int
    diff_millis = *out_t - *timestamp;
    diff_millis = int_div_round(diff_millis, 1000);
  }

  if (diff_millis > servo->config.max_diff_millis) {
    // TODO: Until We Figure out the 808 issue, we don't return an error here but simply presume no time has elapsed
    // return M_ERROR_CODE_SERVO_TIME_DIFF_TOO_BIG;
  } else {
    position = position + int_div_round(diff_millis * follow_velocity, 1000) +
               int_div_round(int_div_round(follow_accel * diff_millis * diff_millis, 1000000), 2);
  }

  if (!(position >= 0 && position <= servo->limit && velocity_mrpm <= servo->config.max_profile_velocity)) {
    return CARBON_EPOS_SERVO_MOVE_OUT_OF_RANGE;
  }

  RETURN_CODE_IF_NOT_OK(MC_get()->Go_To_Position(servo->opencan_id, position, velocity_mrpm))
  int64_t start_time = k_uptime_get();

  RETURN_CODE_IF_NOT_OK(MC_get()->Await_Target_Reached(servo->opencan_id, servo->config.settle_timeout, NULL))

  // Indicate that the inital move is complete, while we still wait
  k_sem_give(servo->ready);
  // Arbitrary sleep
  int32_t sleep_time_ms = (int32_t)interval_sleep_time_ms - (int32_t)(k_uptime_get() - start_time);
  if (sleep_time_ms > 0) {
    k_sleep(K_MSEC(sleep_time_ms));
  }

  int32_t out_p2;
  uint64_t out_t2;
  servo_follow_timestamp(servo, timestamp, follow_velocity, follow_accel, &out_p2, &out_t2,
                         interval_sleep_time_ms + servo->config.settle_timeout);
  return CARBON_SERVO_DELAYED_OK;
}

CARBON_RESPONSE_CODE servo_go_to_delta(epos_servo *servo, int32_t delta_position, uint32_t velocity,
                                       servo_GoToMode mode, int32_t *out_position) {
  history_record r;
  history_list_get_latest(qdec_get_history_list(), &r);
  int32_t current_position = (int32_t)servo_get_position(servo, (qdec_record *)r.record);

  if (delta_position > 4 || delta_position < -4) {
    // Now we have the latest position, we add the delta
    current_position += delta_position;
    if (!(current_position >= 0 && current_position <= servo->limit &&
          velocity <= servo->config.max_profile_velocity)) {
      return CARBON_EPOS_SERVO_MOVE_OUT_OF_RANGE;
    }
    RETURN_CODE_IF_NOT_OK(MC_get()->Go_To_Position(servo->opencan_id, current_position, velocity))

    switch (mode) {
    case servo_GoToMode_IMMEDIATE:
      break;
    case servo_GoToMode_REACHED:
      RETURN_CODE_IF_NOT_OK(MC_get()->Await_Target_Reached(servo->opencan_id, servo->config.settle_timeout, NULL))
      break;
    case servo_GoToMode_SETTLED:
      RETURN_CODE_IF_NOT_OK(MC_get()->Await_Settling(servo->opencan_id, current_position, servo->config.settle_window,
                                                     servo->config.settle_timeout, NULL))
      break;
    default:
      return CARBON_EPOS_ERROR_UNKNOWN;
    }
  }

  if (out_position != NULL) {
    *out_position = current_position;
  }

  return CARBON_RESPONSE_OK;
}

CARBON_RESPONSE_CODE servo_go_to_delta_follow(epos_servo *servo, int32_t delta_position, uint32_t velocity,
                                              int32_t follow_velocity_vector, uint32_t follow_velocity_mrpm,
                                              uint16_t interval_sleep_time_ms, servo_GoToMode mode, bool fast_return,
                                              int32_t *out_position) {
  int32_t current_position = 0;
  RETURN_CODE_IF_NOT_OK(servo_go_to_delta(servo, delta_position, velocity, mode, &current_position))
  if (out_position != NULL) {
    *out_position = current_position;
  }

  if (follow_velocity_vector > 4 || follow_velocity_vector < -4) {
    int32_t follow_position = current_position + follow_velocity_vector;

    // Limit Conditions Handling
    if (follow_velocity_vector > 0 && follow_position > servo->limit) {
      follow_position = servo->limit;
    } else if (follow_velocity_vector < 0 && follow_position < 0) {
      follow_position = 0;
    }

    if (!(follow_position >= 0 && follow_position <= servo->limit &&
          follow_velocity_mrpm <= servo->config.max_profile_velocity)) {
      return CARBON_EPOS_SERVO_MOVE_OUT_OF_RANGE;
    }
    // Arbitrary sleep
    if (fast_return) {
      k_sem_give(servo->ready);
    }
    k_sleep(K_MSEC(interval_sleep_time_ms));

    // Follow
    RETURN_CODE_IF_NOT_OK(MC_get()->Go_To_Position(servo->opencan_id, follow_position, follow_velocity_mrpm))
  } else {
    // For Now Throttle with that, we may want to revisit in the future
    k_sleep(K_MSEC(interval_sleep_time_ms));
    fast_return = false;
  }

  if (fast_return) {
    return CARBON_SERVO_DELAYED_OK;
  }
  return CARBON_RESPONSE_OK;
}

static CARBON_RESPONSE_CODE set_encoder(epos_servo *servo) {
  int32_t position;
  RETURN_CODE_IF_NOT_OK(MC_get()->Get_Actual_Position_Velocity(servo->opencan_id, &position, NULL));
  if (servo->invert) {
    servo->enc_offset = qdec_get_latest(servo->enc_id) - (int64_t)position;
  } else {
    servo->enc_offset = qdec_get_latest(servo->enc_id) + (int64_t)position;
  }
  return CARBON_RESPONSE_OK;
}
int64_t servo_get_position(epos_servo *servo, qdec_record *qrec) {
  if (servo->invert) {
    return qrec->ticks[servo->enc_id] - servo->enc_offset;
  }
  return servo->enc_offset - qrec->ticks[servo->enc_id];
}

static CARBON_RESPONSE_CODE handle_boot(epos_servo *servo, epos_Home_Params *params) {
  LOG_INF("Booting servo %d", servo->opencan_id);
  servo->invert = params->invert;
  RETURN_CODE_IF_NOT_OK(NMT_Reset_Node(servo->opencan_id));
  RETURN_CODE_IF_NOT_OK(MC_get()->Setup_PDOs(servo->opencan_id));

  RETURN_CODE_IF_NOT_OK(NMT_Start_Node(servo->opencan_id));
  RETURN_CODE_IF_NOT_OK(MC_get()->Enable_Node(servo->opencan_id));
  RETURN_CODE_IF_NOT_OK(MC_get()->set_inverted(servo->opencan_id, servo->invert));
  servo->booted = true;
  // Reseting epos clears pids, so need to resend before homing.
  RETURN_CODE_IF_NOT_OK(MC_get()->set_pid(servo->opencan_id, &(servo->pid_cfg)));
  RETURN_CODE_IF_NOT_OK(
      MC_get()->Home_From_Params(servo->opencan_id, params, servo->config.settle_timeout, &servo->limit, servo->enc_id))
  k_sleep(K_MSEC(100)); // Give some extra settle time
  RETURN_CODE_IF_NOT_OK(set_encoder(servo));

  // Give additional time as post hard stop can cause issues
  RETURN_CODE_IF_NOT_OK(servo_go_to(servo, servo->limit / 2, servo->config.max_profile_velocity, true, 5000));

  return CARBON_RESPONSE_OK;
}
static CARBON_RESPONSE_CODE handle_stop(epos_servo *servo) {
  RETURN_CODE_IF_NOT_OK(MC_get()->Disable_Node(servo->opencan_id))
  return NMT_Stop_Node(servo->opencan_id);
}

static CARBON_RESPONSE_CODE servo_configure(epos_servo *servo, servo_Config_Request *req) {
  if (!canopen_is_valid_id(req->node_id)) {
    return CARBON_EPOS_SERVO_NODE_CONFIGURATION_FAIL;
  }
  servo->opencan_id = req->node_id;
  if (req->has_config) {
    servo->config.max_diff_millis = req->config.max_diff_millis;
    servo->config.max_profile_velocity = req->config.max_profile_velocity;
    servo->config.settle_timeout = req->config.settle_timeout;
    servo->config.settle_window = req->config.settle_window;
  }
  return CARBON_RESPONSE_OK;
}

static CARBON_RESPONSE_CODE handle_request(epos_servo *servo, servo_msg *msg) {
  CARBON_RESPONSE_CODE result = CARBON_EPOS_ERROR_UNKNOWN;
  servo_Request *req = msg->req;
  uint64_t time_in, time_pre, time_post;
  switch (req->which_request) {
  case servo_Request_config_tag:
    msg->resp->which_reply = servo_Reply_ack_tag;
    result = servo_configure(servo, &req->request.config);
    break;
  case servo_Request_boot_tag:
    msg->resp->which_reply = servo_Reply_ack_tag;
    result = handle_boot(servo, &req->request.boot.params);
    break;
  case servo_Request_stop_tag:
    msg->resp->which_reply = servo_Reply_ack_tag;
    result = handle_stop(servo);
    break;
  case servo_Request_go_to_tag:
    msg->resp->which_reply = servo_Reply_ack_tag;
    result = servo_go_to(servo, req->request.go_to.position, req->request.go_to.velocity,
                         req->request.go_to.await_settle, 0);
    break;
  case servo_Request_limit_tag:
    msg->resp->which_reply = servo_Reply_limit_tag;
    msg->resp->reply.limit.min = 0;
    msg->resp->reply.limit.max = servo->limit;
    result = CARBON_RESPONSE_OK;
    break;
  case servo_Request_epos_tag:
    msg->resp->which_reply = servo_Reply_epos_tag;
    result = MC_get()->Handle_Request(servo->opencan_id, &req->request.epos, &msg->resp->reply.epos, servo->enc_id);
    break;
  case servo_Request_delta_tag:
    msg->resp->which_reply = servo_Reply_pos_tag;
    result = servo_go_to_delta(servo, req->request.delta.delta_position, req->request.delta.velocity,
                               req->request.delta.mode, &msg->resp->reply.pos.position);
    break;
  case servo_Request_follow_tag:
    msg->resp->which_reply = servo_Reply_pos_tag;
    result =
        servo_go_to_delta_follow(servo, req->request.follow.delta.delta_position, req->request.follow.delta.velocity,
                                 req->request.follow.follow_velocity_vector, req->request.follow.follow_velocity_mrpm,
                                 req->request.follow.interval_sleep_time_ms, req->request.follow.delta.mode,
                                 req->request.follow.fast_return, &msg->resp->reply.pos.position);
    break;
  case servo_Request_calibrate_tag:
    msg->resp->which_reply = servo_Reply_settle_tag;
    result = servo_go_to_calibrate(servo, req->request.calibrate.position, req->request.calibrate.velocity,
                                   req->request.calibrate.window, req->request.calibrate.time_window_ms,
                                   req->request.calibrate.timeout_ms, req->request.calibrate.period_ms,
                                   (uint16_t *)&msg->resp->reply.settle.settle_time);
    break;
  case servo_Request_go_to_timestamp_tag:
    msg->resp->which_reply = servo_Reply_go_to_timestamp_tag;
    time_in = NPB_TS_TO_USEC(req->request.go_to_timestamp.timestamp);
    result = servo_go_to_timestamp(
        servo, &time_in, req->request.go_to_timestamp.mode, req->request.go_to_timestamp.position,
        req->request.go_to_timestamp.velocity_mrpm, req->request.go_to_timestamp.follow_velocity,
        req->request.go_to_timestamp.follow_accel, req->request.go_to_timestamp.interval_sleep_time_ms,
        &msg->resp->reply.go_to_timestamp.pre_position, &msg->resp->reply.go_to_timestamp.post_position, &time_pre,
        &time_post);
    USEC_TO_NPB_TS(msg->resp->reply.go_to_timestamp.pre_timestamp, time_pre);
    USEC_TO_NPB_TS(msg->resp->reply.go_to_timestamp.post_timestamp, time_post);
    msg->resp->reply.go_to_timestamp.has_post_timestamp = true;
    msg->resp->reply.go_to_timestamp.has_pre_timestamp = true;
    break;
  case servo_Request_follow_timestamp_tag:
    msg->resp->which_reply = servo_Reply_follow_timestamp_tag;
    time_in = NPB_TS_TO_USEC(req->request.follow_timestamp.timestamp);
    result = servo_follow_timestamp(servo, &time_in, req->request.follow_timestamp.follow_velocity,
                                    req->request.follow_timestamp.follow_accel,
                                    &msg->resp->reply.follow_timestamp.pre_position, &time_pre, 0);
    USEC_TO_NPB_TS(msg->resp->reply.follow_timestamp.pre_timestamp, time_pre);
    msg->resp->reply.go_to_timestamp.has_pre_timestamp = true;
    break;
  case servo_Request_go_to_follow_tag:
    msg->resp->which_reply = servo_Reply_go_to_follow_tag;
    time_in = NPB_TS_TO_USEC(req->request.go_to_follow.timestamp);
    result = servo_go_to_follow(
        servo, &time_in, req->request.go_to_follow.position, req->request.go_to_follow.velocity_mrpm,
        req->request.go_to_follow.follow_velocity, req->request.go_to_follow.follow_accel,
        req->request.go_to_follow.interval_sleep_time_ms, &msg->resp->reply.go_to_follow.pre_position, &time_pre);
    USEC_TO_NPB_TS(msg->resp->reply.go_to_follow.pre_timestamp, time_pre);
    break;
  default:
    break;
  }
  return result;
}

static void handle_msg(epos_servo *servo, servo_msg *msg) {
  CARBON_RESPONSE_CODE ret = handle_request(servo, msg);
  if (ret == CARBON_SERVO_DELAYED_OK) {
    // Do nothing we don't need to respond
    return;
  }
  if (ret != CARBON_RESPONSE_OK) {
    msg->resp->which_reply = servo_Reply_error_tag;
    msg->resp->reply.error.code = ret;
  }
  k_sem_give(servo->ready);
}
void servo_run(void *arg) {
  epos_servo *servo = (epos_servo *)arg;
  for (;;) {
    servo_msg msg;
    k_msgq_get(servo->input, &msg, K_FOREVER);
    handle_msg(servo, &msg);
  }
}

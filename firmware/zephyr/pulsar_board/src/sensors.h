/**
 * @brief Sensor and status reporting
 *
 * <PERSON>les periodically reading out various sensors and collating them in one place for readout by
 * supervisory software. Data being collected consists of:
 *
 * - Encoder positions
 * - Motor voltage and currents
 * - Motor controller output stage temperatures
 */
#pragma once

#include <stdint.h>

#include <lib/history_list/history_list.h>

#include "can_open_msg.h"
#include "nanopb_server.h"

int sensors_init(history_list *qdec_list);

void sensors_handle_nanopb(npb_request_data *req);
int sensors_handle_status_pdo(const uint8_t node_id, const CAN_Open_Message_t *msg);

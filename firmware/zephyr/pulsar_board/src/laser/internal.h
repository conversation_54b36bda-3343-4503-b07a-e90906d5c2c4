/**
 * @file
 * @brief Internal laser subsystem definitions
 */
#pragma once

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>
#include <zephyr/device.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/drivers/pwm.h>

#include "../laser.h"

/**
 * @brief Maximum number of arc events to reserve memory for
 *
 * Sizes the capacity of the queue used to store arc events. This puts an upper bound on the
 * `alarmThreshold` value.
 */
#define NUM_ARC_EVENTS 40

/**
 * @brief All IOs from the laser
 *
 * @seeAlso gLaserConfig
 */
typedef struct {
  // Laser fire GPIO
  struct gpio_dt_spec laserFire;
  // Laser intensity PWM configuration
  struct pwm_dt_spec laserIntensity;
  // Serial port used to communicate with laser (via firing board)
  const struct device *laserUart;

  // Power meter device
  const struct device *powerMeter;
} laser_config_t;

/**
 * @brief Structure representing an arc event
 *
 * One of these is pushed into the arc event queue whenever an event is detected.
 */
typedef struct laser_arc_event {
  // timestamp this event was detected at
  int64_t timestamp;
} laser_arc_event_t;

/**
 * @brief Laser subsystem state
 */
typedef struct {
  bool enabled;
  bool stf;
  struct k_mutex mut;

  /// Data for the arc detector
  struct {
    /// Requested configuration (for intervals, thresholds, etc)
    laser_arc_detector_config_t config;
    /// Current detector status
    laser_arc_detector_status_t status;

    /// Index of next record to be written in storage buffer
    uint16_t eventWritePtr;
    /// Index of the next record to be read in storage buffer
    uint16_t eventReadPtr;

    /// Buffer of detected events
    laser_arc_event_t eventStorage[NUM_ARC_EVENTS];

    /// Set when an event has been emitted for the current firing cycle
    bool emitted;
  } arc;

  // Currently enabled laser driver type
  laser_type_t laserType;
} laser_state_t;

/// State of laser subsystem
extern laser_state_t gLaserState;
/// Laser configuration, as specified in the dts
extern const laser_config_t gLaserConfig;

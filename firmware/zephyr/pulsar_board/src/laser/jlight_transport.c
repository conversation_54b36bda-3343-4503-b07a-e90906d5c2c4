#include <zephyr/logging/log.h>
LOG_MODULE_DECLARE(laser_jlight, CONFIG_APP_LOG_LEVEL);

#include <zephyr/device.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/uart.h>
#include <zephyr/kernel.h>

#include <utils/handle_errors.h>

#include <nanomodbus.h>

#include "internal.h"
#include "jlight_registers.h"
#include "jlight_transport.h"

// Timeout for MODBUS transactions
#define MODBUS_TIMEOUT_MS (500)

// Extra logging for UART IO
#define LOG_EXTRA_UART (0)
// Extra logging for modbus RX
#define LOG_EXTRA_MODBUS_RX (0)
// Extra logging for modbus TX
#define LOG_EXTRA_MODBUS_TX (0)

// size of RX ring buffer to hold messages
#define RX_RING_BUF_SIZE (512)

/**
 * @brief Modbus device address of laser
 *
 * This is the RTU device address of the laser; fixed at 0x01 at Jlight mfg
 */
#define JLIGHT_RTU_ID (0x01)

/**
 * @brief Size of the UART receive buffer
 *
 * This is the maximum length of a Modbus message from the device.
 */
#define MAX_RX_BUF_SIZE 512

/**
 * @brief Maximum number of pending receive commands
 */
#define RX_QUEUE_SIZE 3
/**
 * @brief Maximum number of pending DMA receive chunks
 */
#define RX_CHUNK_QUEUE_SIZE (RX_QUEUE_SIZE * 2)

/// Size of the UART receive buffer
#define UART_RX_DMA_BUF_LEN (MAX_RX_BUF_SIZE)
/// Size of the UART transmit DMA buffer
#define UART_TX_DMA_BUF_LEN (MAX_RX_BUF_SIZE)
/**
 * @brief Receive timeout (e.g. maximum time between characters) in msec
 *
 * The Modbus standard specifies that the maximum inter-character delay in RTU mode (which is used
 * for Jlight lasers) is a few bit times.
 */
#define UART_RX_TIMEOUT (5)

// State for the transport
typedef struct jlight_transport_state {
  // DMA driven UART reception/transmission stuff
  struct {
    // Signalled by the UART handler when the TX DMA completes
    struct k_sem txCompleteSignal;
    // which buffer to use next DMA receive
    size_t rxBuffer;
  } dma;

  // Command reply reception buffer/handling
  struct {
    // circular buffer for received UART characters
    uint8_t buf[MAX_RX_BUF_SIZE];
    // write pointer for next byte
    size_t writePtr;
    // read pointer for message (start of message)
    size_t readPtr;
  } rx;

  // Modbus client
  struct {
    nmbs_t client;
  } modbus;
} jlight_transport_state_t;

static void handle_uart_event(const struct device *dev, struct uart_event *evt, void *);
static int uart_send(const uint8_t *msg, const size_t msgLen);

static int modbus_client_init();
static int modbus_client_tx(const uint8_t *bytes, uint16_t count, int32_t byte_timeout_ms, void *ctx);
static int modbus_client_rx(uint8_t *bytes, uint16_t count, int32_t byte_timeout_ms, void *ctx);
static int modbus_convert_error(const nmbs_error);

/// Jlight transport state (also includes modbus)
__dtcm_bss_section static jlight_transport_state_t gState;
/// UART configuration for the Modbus interface to the laser
static const struct uart_config kUartConfig = {
    .baudrate = 115200,
    .parity = UART_CFG_PARITY_NONE,
    .stop_bits = UART_CFG_STOP_BITS_1,
    .data_bits = UART_CFG_DATA_BITS_8,
    .flow_ctrl = UART_CFG_FLOW_CTRL_NONE,
};

/// Message receive buffer (musn't be in CCM such that DMA can access it)
__nocache static uint8_t gRxBuffer[2][UART_RX_DMA_BUF_LEN];
/// DMA transmit buffer (musn't be in CCM such that DMA can access it)
__nocache static uint8_t gTxBuffer[UART_TX_DMA_BUF_LEN];

/// Received message bytes
K_PIPE_DEFINE(gJlightMessageRxBuf, RX_RING_BUF_SIZE, 4);

/**
 * @brief Initialize the Jlight laser transport
 *
 * Also sets up UART and the MODBUS client that runs over the UART.
 */
int laser_jlight_transport_init() {
  int err;

  memset(&gState, 0, sizeof(gState));

  // set up kernel objects
  k_sem_init(&gState.dma.txCompleteSignal, 1, 1);

  // set up UART for DMA driven operation
  HANDLE_UNLIKELY_BOOL(device_is_ready(gLaserConfig.laserUart), ENODEV);

  HANDLE_UNLIKELY(uart_configure(gLaserConfig.laserUart, &kUartConfig));

  HANDLE_UNLIKELY(uart_callback_set(gLaserConfig.laserUart, handle_uart_event, NULL));
  HANDLE_UNLIKELY(uart_rx_enable(gLaserConfig.laserUart, gRxBuffer[0], UART_RX_DMA_BUF_LEN, (UART_RX_TIMEOUT * 1000)));

  // set up the modbus client
  HANDLE_UNLIKELY(modbus_client_init());

  return 0;
}

/**
 * @brief Initialize modbus client
 */
static int modbus_client_init() {
  nmbs_error err;
  nmbs_platform_conf config;

  // set up platform config
  nmbs_platform_conf_create(&config);
  config.transport = NMBS_TRANSPORT_RTU;
  config.read = modbus_client_rx;
  config.write = modbus_client_tx;

  // set up the modbus client
  err = nmbs_client_create(&gState.modbus.client, &config);
  if (err != NMBS_ERROR_NONE) {
    LOG_ERR("modbus client init failed: %s (%d)", nmbs_strerror(err), err);
    return -EIO;
  }

  nmbs_set_destination_rtu_address(&gState.modbus.client, JLIGHT_RTU_ID);
  nmbs_set_read_timeout(&gState.modbus.client, MODBUS_TIMEOUT_MS);
  nmbs_set_byte_timeout(&gState.modbus.client, MODBUS_TIMEOUT_MS);

  return 0;
}

/**
 * @brief Modbus client wishes to transmit data
 */
static int modbus_client_tx(const uint8_t *bytes, uint16_t count, int32_t byte_timeout_ms, void *) {
#if LOG_EXTRA_MODBUS_TX
  LOG_DBG("%s: %u bytes@%p, timeout %d ms", __FUNCTION__, count, bytes, byte_timeout_ms);
#endif
  ARG_UNUSED(byte_timeout_ms);

  HANDLE_UNLIKELY(uart_send(bytes, count));
  return count;
}

/**
 * @brief Attempt to receive data from modbus UART
 *
 * this shall pull the requested number of bytes out of the receive queue, blocking for up to
 * `byte_timeout_ms` msec. Data may be retrieved in multiple reads from the RX queue for one
 * call to this function.
 *
 * @return Number of bytes read or negative error code
 */
static int modbus_client_rx(uint8_t *bytes, uint16_t count, int32_t byte_timeout_ms, void *ctx) {
  int err, numRead = 0;
  k_timeout_t timeout;

  if (byte_timeout_ms > 0) {
    timeout = K_MSEC(byte_timeout_ms);
  } else if (byte_timeout_ms == 0) {
    // really means no block, but wait some time as if the received message spanned across
    // buffers, as it takes some time for the UART driver to flush the remaining bytes
    // timeout = K_NO_WAIT;
    timeout = K_MSEC(25);
  } else if (byte_timeout_ms < 0) {
    timeout = K_FOREVER;
  }

  err = k_pipe_get(&gJlightMessageRxBuf, bytes, count, &numRead, 1, timeout);
#if LOG_EXTRA_MODBUS_RX
  LOG_DBG("%s: %u bytes@%p, timeout %d ms: ret=%d, got %u", __FUNCTION__, count, bytes, byte_timeout_ms, err, numRead);
  if (numRead) {
    LOG_HEXDUMP_DBG(bytes, numRead, "RX data");
  }
#endif

  // TODO: if no wait, is the return value different, e.g. timeout errors ignored?
  if (err < 0) {
    return err;
  }
  return numRead;
}

/**
 * @brief Convert nanoMODBUS library error to zephyr error
 */
static int modbus_convert_error(const nmbs_error inErr) {
  switch (inErr) {
  // invalid request or unit id
  case NMBS_ERROR_INVALID_REQUEST:
  case NMBS_ERROR_INVALID_ARGUMENT:
    return -EINVAL;

  case NMBS_ERROR_INVALID_UNIT_ID:
    return -EDOM;

  // checksum validation failure
  case NMBS_ERROR_CRC:
    return -EBADMSG;

  // timeout on IO
  case NMBS_ERROR_TIMEOUT:
    return -EAGAIN;

  // generic error
  default:
    return -EIO;
  }
}

/**
 * @brief Send raw bytes via UART to the laser
 */
static int uart_send(const uint8_t *msg, const size_t msgLen) {
#if LOG_EXTRA_MODBUS_TX
  // XXX: debugging
  LOG_HEXDUMP_DBG(msg, msgLen, "UART TX");
#endif

  // TODO: bounds check
  memcpy(gTxBuffer, msg, msgLen);

  // do DMA transmit
  HANDLE_UNLIKELY(uart_tx(gLaserConfig.laserUart, gTxBuffer, msgLen, SYS_FOREVER_US));
  // and block waiting for TX complete
  HANDLE_UNLIKELY(k_sem_take(&gState.dma.txCompleteSignal, K_FOREVER));

  return 0;
}

/**
 * @brief Process UART driver event
 */
static void handle_uart_event(const struct device *dev, struct uart_event *evt, void *) {
  int err, bytesWritten;
  switch (evt->type) {
  // provide the next RX buffer
  case UART_RX_BUF_REQUEST:
    uart_rx_buf_rsp(gLaserConfig.laserUart, gRxBuffer[++gState.dma.rxBuffer % 2], UART_RX_DMA_BUF_LEN);
    break;
  // buffer no longer in use; don't do anything here
  case UART_RX_BUF_RELEASED:
    break;

  // Receive complete (e.g. timeout, buffer full, receiver disabled/stopped)
  case UART_RX_RDY: {
    const char *data = (evt->data.rx.buf + evt->data.rx.offset);
    const size_t length = evt->data.rx.len;

#if LOG_EXTRA_UART
    LOG_DBG("rx: buf %p, off %u, len %u (total %u)", evt->data.rx.buf, evt->data.rx.offset, evt->data.rx.len,
            evt->data.rx.offset + evt->data.rx.len);
    LOG_HEXDUMP_DBG(data, length, "UART RX");
#endif

    // TODO: for long term this needs to be "stateful" and push an entire modbus meessage into the
    //       pipe at once

    // TODO: need handle wraparound?
    err = k_pipe_put(&gJlightMessageRxBuf, data, length, &bytesWritten, length, K_NO_WAIT);
    if (err) {
      LOG_WRN("k_pipe_put failed: %d", err);
    }

    break;
  }

  // Likely due to overrun or other framing error
  case UART_RX_STOPPED:
    // TODO: handle this (reset receive state machine)
    break;

  // finished transmitting most recent buffer
  case UART_TX_DONE:
    k_sem_give(&gState.dma.txCompleteSignal);
    break;
  // transmission failed/aborted, reset state
  case UART_TX_ABORTED:
    // TODO: need we do anything here?
    break;

  // ignore all other events types
  default:
    break;
  }
}

/**
 * @brief Read the discretes from the laser
 *
 * Jlight laser has only 32 bits of discretes, so they are all read in one go and output here.
 */
int laser_jlight_transport_read_discretes(uint32_t *out) {
  if (!out) {
    return -EFAULT;
  }

  nmbs_error err;
  nmbs_bitfield bits;
  memset(&bits, 0, sizeof(bits));

  err = nmbs_read_discrete_inputs(&gState.modbus.client, 0, 32, bits);
  if (err != NMBS_ERROR_NONE) {
    LOG_ERR("modbus %s failed: %s (%d)", "read single input register", nmbs_strerror(err), err);
    return modbus_convert_error(err);
  }

  memcpy(out, &bits, sizeof(*out));

  return 0;
}

/**
 * @brief Read a single input register
 *
 * @param reg Input holding register to be read out
 * @param out Variable to store output
 */
int laser_jlight_transport_read_input(const jlight_reg_input_t reg, uint16_t *out) {
  if (!out) {
    return -EFAULT;
  }

  nmbs_error err;
  err = nmbs_read_input_registers(&gState.modbus.client, reg, 1, out);
  if (err != NMBS_ERROR_NONE) {
    LOG_ERR("modbus %s failed: %s (%d)", "read single input register", nmbs_strerror(err), err);
    return modbus_convert_error(err);
  }

  return 0;
}

/**
 * @brief Read multiple input registers
 *
 * @param reg First register to read out
 * @param num Number of registers to read
 * @param out Buffer to receive the register data in consecutive order
 */
int laser_jlight_transport_read_input_multi(const jlight_reg_input_t reg, const size_t num, uint16_t *out) {
  if (!out) {
    return -EFAULT;
  } else if (!num) {
    return -EINVAL;
  }

  nmbs_error err;
  err = nmbs_read_input_registers(&gState.modbus.client, reg, num, out);
  if (err != NMBS_ERROR_NONE) {
    LOG_ERR("modbus %s failed: %s (%d)", "read multiple input register", nmbs_strerror(err), err);
    return modbus_convert_error(err);
  }

  return 0;
}

/**
 * @brief Read a single holding register
 *
 * @TODO: allow to read multiple in one IO
 */
int laser_jlight_transport_read_hold(const jlight_reg_hold_t reg, uint16_t *out) {
  if (!out) {
    return -EFAULT;
  }

  nmbs_error err;
  err = nmbs_read_holding_registers(&gState.modbus.client, reg, 1, out);
  if (err != NMBS_ERROR_NONE) {
    LOG_ERR("modbus %s failed: %s (%d)", "read single hold register", nmbs_strerror(err), err);
    return modbus_convert_error(err);
  }

  return 0;
}

/**
 * @brief Write Jlight holding register
 */
int laser_jlight_transport_write_hold(const jlight_reg_hold_t reg, const uint16_t value) {
  nmbs_error err;
  err = nmbs_write_single_register(&gState.modbus.client, reg, value);
  if (err != NMBS_ERROR_NONE) {
    LOG_ERR("modbus %s failed: %s (%d)", "write single register", nmbs_strerror(err), err);
    return modbus_convert_error(err);
  }

  return 0;
}

/**
 * @brief Write a single coil bit
 */
int laser_jlight_transport_write_coil(const jlight_write_coils_t coil, const bool state) {
  nmbs_error err;
  err = nmbs_write_single_coil(&gState.modbus.client, coil, state);
  if (err != NMBS_ERROR_NONE) {
    LOG_ERR("modbus %s failed: %s (%d)", "write single coil", nmbs_strerror(err), err);
    return modbus_convert_error(err);
  }

  return 0;
}

/**
 * @file
 * @brief Helpers for laser nanopb stuff
 */
#pragma once

#include "generated/lib/drivers/nanopb/proto/pulczar_board.pb.h"

int laser_handle_nanopb_reset(laser_Laser_Reset_Request *request);
int laser_handle_nanopb_set_current(laser_Diode_Set_Current_Request *request);
int laser_handle_nanopb_set_type(laser_Laser_Set_Type_Request *request);

int laser_fill_nanopb_inventory(laser_Laser_Inventory_Reply *outInventory);
int laser_fill_nanopb_status(laser_Diode_Status_Reply *outStatus);

#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(laser_bwt, CONFIG_APP_LOG_LEVEL);

#include <zephyr/device.h>
#include <zephyr/devicetree.h>
#include <zephyr/kernel.h>
#include <zephyr/sys/util.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include <utils/handle_errors.h>

#include "../power.h"
#include "bwt.h"
#include "bwt_transport.h"

/// Limits range (lower, upper) for coolant inlet temperature (in °C)
static const float kTempLimitsRangeInlet[2] = {10.0, 35.0};
/// Limits range (lower, upper) for diode chip temperature (in °C)
static const float kTempLimitsRangeChip[2] = {10.0, 40.0};

/**
 * @brief Debug option to disable all command timeouts
 *
 * Useful for debugging, e.g. if BWT is being simulated via serial terminal.
 */
#define CONFIG_BWT_TIMEOUT_INFINITE 0

/**
 * @brief Length of the BWT poll worker queue
 */
#define BWT_WORK_QUEUE_SIZE (5)

/**
 * @brief After how many polls BWT sensors are read out
 */
#define BWT_POLL_SENSOR_COUNT (4)

/**
 * @brief Maximum delay to wait for BWT init during boot
 *
 * This sets the upper bound on how long the main task (in `laser_bwt_init()`) will be blocked
 * waiting for the polling/worker task to transition to the "connected" state.
 */
#define BWT_INIT_TIMEOUT K_SECONDS(10)

/**
 * @brief How often we should try handshaking with BWT
 *
 * This is the delay between repeated handshakes in the "disconnected" state
 */
#define BWT_HANDSHAKE_INTERVAL K_SECONDS(2)

/**
 * @brief Time delay after successful handshake to initialization
 *
 * After a RBWT handshake succeeds, wait this amount of time before performing any further
 * laser initialization.
 */
#define BWT_HANDSHAKE_SUCCESS_DELAY K_SECONDS(3)

/**
 * @brief How often the BWT should be polled for status
 */
#define BWT_POLL_INTERVAL K_MSEC(666)

/**
 * @brief Time delay before re-polling laser after disconnected
 *
 * If the laser becomes disconnected (due to some error) during normal operation, this is the
 * time delay applied before re-polling the laser for presence.
 */
#define BWT_DISCONNECTED_COOLDOWN K_MSEC(1500)

#if CONFIG_BWT_TIMEOUT_INFINITE
#define BWT_TIMEOUT_HANDSHAKE K_FOREVER
#define BWT_TIMEOUT_ENV_SENSE K_FOREVER
#define BWT_TIMEOUT_CURRENT K_FOREVER
#define BWT_TIMEOUT_FAULTS K_FOREVER
#define BWT_TIMEOUT_INVENTORY K_FOREVER
#define BWT_TIMEOUT_INTENSITY K_FOREVER
#define BWT_TIMEOUT_SETUP K_FOREVER
#define BWT_TIMEOUT_LOGIN K_FOREVER
#define BWT_TIMEOUT_NVRAM_SETUP K_FOREVER
#else
// Timeout for BWT handshake
#define BWT_TIMEOUT_HANDSHAKE K_MSEC(500)
// Timeout for temperature/humidity polling
#define BWT_TIMEOUT_ENV_SENSE K_MSEC(500)
// Timeout for diode current polling
#define BWT_TIMEOUT_CURRENT K_MSEC(500)
// Timeout for fetching fault  records
#define BWT_TIMEOUT_FAULTS K_MSEC(500)
// Timeout for fetching inventory data (sn/model)
#define BWT_TIMEOUT_INVENTORY K_MSEC(1000)
// Timeout for setting laser intensity
#define BWT_TIMEOUT_INTENSITY K_MSEC(500)
// Timeout for setup commands (general)
#define BWT_TIMEOUT_SETUP K_MSEC(500)
// Timeout for OLOGIN (activate bonus commands)
#define BWT_TIMEOUT_LOGIN K_MSEC(1000)
// Timeout for EEPROM writing setup command
#define BWT_TIMEOUT_NVRAM_SETUP K_MSEC(1000)
#endif

/**
 * @brief Maximum number of BWT temperature readings to allocate space for
 */
#define BWT_MAX_NUM_TEMPS (4)

/**
 * @brief Maximum number of BWT humidity readings to allocate space for
 *
 * We only have one humidity sensor, but the RHUMI command returns two values, so we need to
 * reserve space for at least two elements.
 */
#define BWT_MAX_NUM_HUMIDITY (2)

/**
 * @brief Maximum number of BWT current readings to allocate space for
 */
#define BWT_MAX_NUM_CURRENT (8)

/**
 * @brief Maximum number of fault codes to allocate space for
 */
#define BWT_MAX_NUM_FAULTS (4)

// Bit in gConnectionFlags indicating BWT is connected
#define FLAG_CONNECTED_BIT (0)

// Known BWT temperature range indices
typedef enum {
  // Coolant inlet temperature range
  kBwtTempConfigInlet = 3,
  // Laser diode chip temperature
  kBwtTempConfigChip = 4,
} bwt_temp_config_index_t;

// States of the poll state machine
typedef enum bwt_task_state {
  // Unknown BWT state, keep trying handshake until success (initial state)
  kBwtTaskStateDisconnected = 0,
  // Got handshake success, try to initialize by setting configuration
  kBwtTaskStateInitializing = 1,
  // All configuration has been applied, poll status periodically
  kBwtTaskStateConnected = 2,
} bwt_task_state_t;

// Type of BWT work item
typedef enum bwt_poll_work_type {
  // The timer used to poll BWT state (presence/sensors) expired
  kBwtWorkPollTimerExpired,

  // Update laser intensity
  kBwtWorkSetIntensity,
  // Command firing via UART
  kBwtWorkSetFiring,
  // Run a passthrough command (e.g. send command w/o parsing response)
  kBwtWorkPassthrough,

  // Reset the laser state machine
  kBwtWorkResetState,
} bwt_poll_work_type_t;

/**
 * @brief Information used to notify caller when command completes
 *
 * Callers should allocate one of these dudes on the stack: its address must NOT change for the
 * entire time the command is being executed.
 *
 * This structure contains a poll signal, which is automagically managed by the command submission
 * stub and used to wait for the worker task to complete execution of the request. Clients should
 * not attempt to modify this flag.
 *
 * On return, it will be updated with the return code/results of the command.
 */
typedef struct bwt_poll_cmd_result {
  // stuff used to handle notifications
  struct {
    // Notified when command completes
    struct k_poll_signal signal;
  } _notify;

  // Return code of command
  int ret;
} bwt_poll_cmd_result_t;

// Work item for the poll task
typedef struct bwt_poll_work {
  bwt_poll_work_type_t type;

  union {
    // set the laser modulation intensity
    struct {
      // intensity value to apply
      uint16_t value;
      // how long to wait for a response
      k_timeout_t timeout;
    } setIntensity;
    // command laser firing via UART
    struct {
      bool enabled;
      k_timeout_t timeout;
    } setFiring;
    // Execute passthrough command
    struct {
      const char *command;

      char *responseBuffer;
      size_t responseBufferLength;

      k_timeout_t timeout;
    } passthrough;
  };

  // command result structure (used only for certain commands)
  bwt_poll_cmd_result_t *result;
} bwt_poll_work_t;

// Structure holding the state for the entire BWT handler
typedef struct laser_bwt_state {
  // telemetry/statistics read from BWT
  struct {
    // protection for concurrent access of data in here
    struct k_mutex lock;

    // temperature readings
    float temps[BWT_MAX_NUM_TEMPS];
    volatile size_t numTemps;

    // humidity reading
    float humidity[BWT_MAX_NUM_HUMIDITY];
    volatile size_t numHumidity;

    // current reading
    float current[BWT_MAX_NUM_CURRENT];
    volatile size_t numCurrent;

    // fault list
    int faults[BWT_MAX_NUM_FAULTS];
    volatile size_t numFaults;

    // is laser in faulted/alarm state?
    uint8_t fault : 1;
    uint8_t reserved : 7;
  } sensors;

  // Inventory information (model/sn)
  struct {
    // protection for concurrent access of data in here
    struct k_mutex lock;

    // Length of serial number
    uint8_t serialLen;
    // Laser supply serial number (from `RSN`)
    char serial[16];

    // Model number length
    uint8_t modelLen;
    // Model number of laser (from `RPN`)
    char model[16];

    // Rated laser power (from `RRP`)
    uint16_t ratedPower;
  } inventory;

  // task's state
  struct {
    // state of the communication with the laser
    bwt_task_state_t state;
    // Semaphore signalled whenever the laser is initialized
    struct k_sem initSem;

    // Number of poll cycles since last sensor/status query
    size_t pollCount;
  } task;

  // Most recently applied volatile laser state
  struct {
    // Laser output intensity ([0, 1000] as for `laser_set_intensity`)
    uint16_t intensity;
    // Whether the once per boot initialization has taken place
    bool oneTimeSetupComplete;
  } lastState;
} laser_bwt_state_t;

static void bwt_task_main();
static void bwt_task_handle_disconnect();
static int bwt_task_submit_work(const bwt_poll_work_t *, const k_timeout_t);
static int bwt_task_submit_work_await(bwt_poll_work_t *, const k_timeout_t);
static int bwt_task_do_work(const bwt_poll_work_t *, bwt_task_state_t *);
static int bwt_task_poll_status();

static int bwt_do_handshake(const k_timeout_t timeout);
static int bwt_do_set_intensity(const uint16_t newIntensity, const k_timeout_t timeout);
static int bwt_do_set_firing(const bool enabled, const k_timeout_t timeout);

static int bwt_setup();
static int bwt_init_temp_thresholds();
static int bwt_get_temp_range(const bwt_temp_config_index_t which, float outRange[static 2]);
static int bwt_set_temp_range(const bwt_temp_config_index_t which, const float range[2]);
static int bwt_update_inventory();

static void poll_timer_tick(struct k_timer *);

__dtcm_bss_section static laser_bwt_state_t gState;

// requests to the poll task (incl. from periodic timer)
K_MSGQ_DEFINE(gBwtWorkQueue, sizeof(bwt_poll_work_t), BWT_WORK_QUEUE_SIZE, 2);
// worker thread that services BWT communications and polling its state
K_THREAD_DEFINE(gBwtTask, 2048, bwt_task_main, NULL, NULL, NULL, K_PRIO_PREEMPT(9), 0, K_TICKS_FOREVER);
// timer used to trigger periodic polling
K_TIMER_DEFINE(gBwtPollTimer, poll_timer_tick, NULL);

// flag indicating whether BWT is connected
__dtcm_data_section static atomic_t gConnectionFlags = ATOMIC_INIT(0);

// Test whether BWT is connected
static inline bool bwt_is_connected() { return atomic_test_bit(&gConnectionFlags, FLAG_CONNECTED_BIT); }

/**
 * @brief Initialize the BWT laser subsystem
 *
 * This sets up the underlying hardware (the UART) and enables power to the firing control board;
 * then the high level initialization (via the serial commands) takes place.
 */
int laser_bwt_init() {
  int err;

  memset(&gState, 0, sizeof(gState));

  // turn on firing board power and configure the BWT laser transport
  HANDLE_UNLIKELY(power_enable(kPowerFiringBoard));
  HANDLE_UNLIKELY(laser_bwt_transport_init());

  // start the worker: he will initialize laser, do handshaking; then wait for init complete
  LOG_INF("starting laser worker: %s", "BWT");

  k_mutex_init(&gState.sensors.lock);
  k_mutex_init(&gState.inventory.lock);
  k_sem_init(&gState.task.initSem, 0, 1);

  k_thread_name_set(gBwtTask, "Laser.BWT Worker");
  k_thread_start(gBwtTask);

  err = k_sem_take(&gState.task.initSem, BWT_INIT_TIMEOUT);

  if (err == -EAGAIN) {
    LOG_WRN("laser init timeout; will continue to retry in background (machine probably estopped)");
  } else if (err) {
    LOG_ERR("%s failed: %d", "k_sem_take", err);
    return err;
  }

  return 0;
}

/**
 * @brief BWT polling task main loop
 *
 * This dude is responsible for periodically poking the BWT via UART to see if it's still alive,
 * and retrieving various metrics (temperature, humidity, current, faults) from it. These are then
 * stored for later retrieval.
 */
static void bwt_task_main() {
  int err;
  bwt_task_state_t nextState;
  bool addHandshakeDelay = false;

  // prepare initial state
  gState.task.state = kBwtTaskStateDisconnected;

  gState.lastState.intensity = 1000;

  // process events forever
  do {
    nextState = gState.task.state;

    // process the current state of the state machine
    switch (gState.task.state) {
    /*
     * No connection to BWT established
     *
     * This is the initial state, or where the state machine transitions if any of the BWT
     * commands fail due to timeout.
     *
     * During this state we'll periodically try to handshake with the BWT. If successful, we
     * move on to performing the rest of the initialization. Otherwise, this will keep
     * retrying forever.
     */
    case kBwtTaskStateDisconnected:
      if (addHandshakeDelay) {
        k_sleep(BWT_DISCONNECTED_COOLDOWN);
        addHandshakeDelay = false;
      }

      err = bwt_do_handshake(BWT_TIMEOUT_HANDSHAKE);
      if (!err) {
        LOG_INF("BWT handshake success");

        nextState = kBwtTaskStateInitializing;
        k_sleep(BWT_HANDSHAKE_SUCCESS_DELAY);
      } else {
        LOG_DBG("%s failed: %d", "bwt_do_handshake", err);

        // wait a bit for next handshake attempt
        k_sleep(BWT_HANDSHAKE_INTERVAL);
      }

      break;

    /*
     * Detected laser (handshake ok) but not initialized yet
     *
     * Perform all fo the initialization of the laser (applying the configuration for
     * modulation, previous intensity setting, etc.) and read out inventory information
     * before advancing to the "connected" state.
     */
    case kBwtTaskStateInitializing:
      err = bwt_setup();
      if (err) {
        LOG_WRN("%s failed: %d", "bwt_setup", err);
        nextState = kBwtTaskStateDisconnected;
      } else {
        LOG_INF("BWT init complete!");
        nextState = kBwtTaskStateConnected;

        // purge any existing work items
        k_msgq_purge(&gBwtWorkQueue);
      }

      break;

    /*
     * BWT connection established
     *
     * The laser has been successfully initialized. At this stage, we can service
     * requests to send commands to the laser, as well as periodically polling statistic
     * information.
     *
     * If any of the polling requests result in a reply timeout (or handshake detection
     * fails) the state will revert to disconnected.
     */
    case kBwtTaskStateConnected:
      bwt_poll_work_t work;
      err = k_msgq_get(&gBwtWorkQueue, &work, K_FOREVER);
      if (err) {
        LOG_WRN("%s failed: %d", "k_msgq_get", err);
        continue;
      }

      err = bwt_task_do_work(&work, &nextState);
      if (err) {
        LOG_WRN("failed to process work item (type=%u): %d", work.type, err);
        nextState = kBwtTaskStateDisconnected;
      }

      // if response struct specified, notify it
      if (work.result) {
        work.result->ret = err;
        __DSB();

        k_poll_signal_raise(&work.result->_notify.signal, 0);
      }
      break;
    }

    // handle state transition
    if (gState.task.state != nextState) {
      LOG_DBG("state transition: %u -> %u", gState.task.state, nextState);

      // manage the poll timer for the connected state
      if (nextState == kBwtTaskStateConnected) {
        LOG_INF("BWT connected");

        gState.task.pollCount = 0;
        k_timer_start(&gBwtPollTimer, BWT_POLL_INTERVAL, BWT_POLL_INTERVAL);

        atomic_set_bit(&gConnectionFlags, FLAG_CONNECTED_BIT);
        k_sem_give(&gState.task.initSem);
      } else {
        k_timer_stop(&gBwtPollTimer);
      }

      // clear sensor/inventory data if disconnecting
      if (nextState == kBwtTaskStateDisconnected) {
        LOG_INF("BWT disconnected");
        bwt_task_handle_disconnect();

        addHandshakeDelay = true;
      }

      // update state
      gState.task.state = nextState;
    }
  } while (true);
}

/**
 * @brief Process BWT disconnection
 *
 * All stored inventory and sensor data is cleared.
 */
static void bwt_task_handle_disconnect() {
  atomic_clear_bit(&gConnectionFlags, FLAG_CONNECTED_BIT);

  k_mutex_lock(&gState.sensors.lock, K_FOREVER);
  {
    gState.sensors.numTemps = 0;
    gState.sensors.numHumidity = 0;
    gState.sensors.numCurrent = 0;
    gState.sensors.numFaults = 0;

    gState.sensors.fault = 0;
  }
  k_mutex_unlock(&gState.sensors.lock);

  k_mutex_lock(&gState.inventory.lock, K_FOREVER);
  {
    gState.inventory.serialLen = 0;
    memset(gState.inventory.serial, 0, sizeof(gState.inventory.serial));
    gState.inventory.modelLen = 0;
    memset(gState.inventory.model, 0, sizeof(gState.inventory.model));
    gState.inventory.ratedPower = 0;
  }
  k_mutex_unlock(&gState.inventory.lock);
}

/**
 * @brief Enqueue work item for BWT work task
 *
 * Push the provided work item onto the BWT worker task queue. If the request expects a reply, the
 * reply structure is initialized. However, this call will NOT block waiting for a reply.
 *
 * @param timeout How long to block if work queue is full
 */
static int bwt_task_submit_work(const bwt_poll_work_t *work, const k_timeout_t timeout) {
  if (!work) {
    return -EFAULT;
  }

  // initialize reply structure
  if (work->result) {
    work->result->ret = -1;
    k_poll_signal_init(&work->result->_notify.signal);
  }

  // then submit command
  return k_msgq_put(&gBwtWorkQueue, work, timeout);
}

/**
 * @brief Enqueue work item for BWT work task and wait for response
 *
 * This is the same as `bwt_task_submit_work` but it internally handles blocking for the
 * command to complete.
 *
 * @return Return code of underlying command, or any errors that occurred during submission
 */
static int bwt_task_submit_work_await(bwt_poll_work_t *work, const k_timeout_t timeout) {
  int err;

  if (!work) {
    return -EFAULT;
  }

  // prepare the result structure (iff not already specified)
  bwt_poll_cmd_result_t result;

  if (!work->result) {
    memset(&result, 0, sizeof(result));

    work->result = &result;
  }

  // timeout of 1 sec for task to make the work queue not full
  err = bwt_task_submit_work(work, K_MSEC(1000));
  if (err) {
    LOG_WRN("%s failed: %d", "bwt_task_submit_work", err);
    return err;
  }

  // now wait for the result
  struct k_poll_event events[1] = {
      K_POLL_EVENT_INITIALIZER(K_POLL_TYPE_SIGNAL, K_POLL_MODE_NOTIFY_ONLY, &work->result->_notify.signal)};

  err = k_poll(events, 1, timeout);
  if (err) {
    LOG_WRN("%s failed: %d", "k_poll", err);
    return err;
  }

  return work->result->ret;
}

/**
 * @brief Process a BWT task work item
 *
 * This implements the logic to handle communicating with the BWT laser once connected; e.g. the
 * polling of status/handshaking.
 */
static int bwt_task_do_work(const bwt_poll_work_t *work, bwt_task_state_t *nextState) {
  int err;

  switch (work->type) {
  /*
   * BWT polling timer fired
   *
   * This event is periodically generated from a timer callback.
   *
   * Attempt to handshake with the laser to ensure it's still powered. If successful, and
   * it has been sufficient time, we'll also perform polling of the laser status.
   */
  case kBwtWorkPollTimerExpired:
    // check laser still powered/connected by means of handshake
    err = bwt_do_handshake(BWT_TIMEOUT_HANDSHAKE);
    if (err) {
      LOG_DBG("%s failed: %d", "bwt_do_handshake", err);
      return err;
    }

    // then get the sensor readings/status
    if (++gState.task.pollCount == BWT_POLL_SENSOR_COUNT) {
      gState.task.pollCount = 0;

      HANDLE_UNLIKELY(bwt_task_poll_status());
    }

    break;

  /*
   * Update BWT laser intensity value
   */
  case kBwtWorkSetIntensity:
    HANDLE_UNLIKELY(bwt_do_set_intensity(work->setIntensity.value, work->setIntensity.timeout));
    break;

  /*
   * Set laser firing via UART
   */
  case kBwtWorkSetFiring:
    HANDLE_UNLIKELY(bwt_do_set_firing(work->setFiring.enabled, work->setFiring.timeout));
    break;

  /*
   * Execute generic BWT command
   */
  case kBwtWorkPassthrough:
    HANDLE_UNLIKELY(laser_bwt_transport_send_await(work->passthrough.command, work->passthrough.responseBuffer,
                                                   work->passthrough.responseBufferLength, work->passthrough.timeout));
    break;

  /*
   * Reset the BWT connectivity state
   */
  case kBwtWorkResetState:
    LOG_WRN("Laser disconnection by user request");

    *nextState = kBwtTaskStateDisconnected;
    break;

  default:
    LOG_WRN("unknown work type %d", work->type);
    return -EINVAL;
  }

  return 0;
}

/**
 * @brief Poll BWT laser for status
 *
 * @return 0 on success, or error code of first failing call
 */
static int bwt_task_poll_status() {
  int err;

  // storage for reply data
  union {
    // ensure this is large enough for all of the data storage float arrays
    float num[MAX(MAX(MAX(BWT_MAX_NUM_TEMPS, BWT_MAX_NUM_HUMIDITY), BWT_MAX_NUM_CURRENT), BWT_MAX_NUM_FAULTS)];
    char str[16];
  } buf;

  // read out temperatures
  err = laser_bwt_transport_command_numeric("RT", BWT_TIMEOUT_ENV_SENSE, buf.num, BWT_MAX_NUM_TEMPS);
  if (err < 0) {
    LOG_WRN("%s failed: %d", "RT", err);
    return err;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.sensors.lock, K_FOREVER));
  {
    memcpy(&gState.sensors.temps, &buf.num, sizeof(float) * MIN(err, BWT_MAX_NUM_TEMPS));
    gState.sensors.numTemps = err;
  }
  k_mutex_unlock(&gState.sensors.lock);

  // read out humidity: [0 = temp, 1 = humidity]
  err = laser_bwt_transport_command_numeric("RHUMI", BWT_TIMEOUT_ENV_SENSE, buf.num, BWT_MAX_NUM_HUMIDITY);
  if (err < 0) {
    LOG_WRN("%s failed: %d", "RHUMI", err);
    return err;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.sensors.lock, K_FOREVER));
  {
    gState.sensors.humidity[0] = buf.num[1];
    gState.sensors.numHumidity = 1;
  }
  k_mutex_unlock(&gState.sensors.lock);

  // current
  err = laser_bwt_transport_command_numeric("RI", BWT_TIMEOUT_CURRENT, buf.num, BWT_MAX_NUM_CURRENT);
  if (err < 0) {
    LOG_WRN("%s failed: %d", "RI", err);
    return err;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.sensors.lock, K_FOREVER));
  {
    memcpy(&gState.sensors.current, &buf.num, sizeof(float) * MIN(err, BWT_MAX_NUM_CURRENT));
    gState.sensors.numCurrent = err;
  }
  k_mutex_unlock(&gState.sensors.lock);

  // fault list
  err = laser_bwt_transport_command_numeric("RFS", BWT_TIMEOUT_FAULTS, buf.num, BWT_MAX_NUM_FAULTS);
  if (err < 0) {
    LOG_WRN("%s failed: %d", "RFS", err);
    return err;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.sensors.lock, K_FOREVER));
  {
    // special case for numFaults = 1, fault[0] = 0 (not actually a fault)
    if (err == 1 && buf.num[0] == 0) {
      gState.sensors.numFaults = 0;
    } else {
      for (size_t i = 0; i < err; i++) {
        gState.sensors.faults[i] = buf.num[i];
      }
      gState.sensors.numFaults = err;
    }

    gState.sensors.fault = (gState.sensors.numFaults > 0);
  }
  k_mutex_unlock(&gState.sensors.lock);

  // if we get here, reading all of the information was success
  return 0;
}

/**
 * @brief Periodic polling timer expiration
 *
 * Submit a request on the work queue to the polling task to poll for new sensor data. Any errors,
 * such as a full queue, are ignored.
 */
static void poll_timer_tick(struct k_timer *) {
  int err;
  bwt_poll_work_t work = {kBwtWorkPollTimerExpired};

  err = bwt_task_submit_work(&work, K_NO_WAIT);
  if (err && err != -ENOMSG) {
    LOG_WRN("%s failed: %d", "bwt_task_submit_work", err);
  }
}

/**
 * @brief Perform BWT handshake
 *
 * This is used to confirm communication with the laser is working successfully.
 */
static int bwt_do_handshake(const k_timeout_t timeout) {
  int err;
  char reply[16];

  err = laser_bwt_transport_send_await("RBWT", reply, sizeof(reply), timeout);
  if (err < 0) {
    return err;
  }

  // expect reply `BWTLASER` on success
  if (strncmp(reply, "BWTLASER", err) == 0) {
    return 0;
  } else {
    LOG_HEXDUMP_WRN(reply, err, "invalid BWT handshake response");
    return -EIO;
  }
}

/**
 * @brief Internal "set intensity" method
 *
 * This sends the command directly to the BWT rather than queuing a request to the worker task;
 * used when intensity is set directly from the work task to avoid deadlock.
 */
static int bwt_do_set_intensity(const uint16_t newIntensity, const k_timeout_t timeout) {
  int err;

  if (newIntensity > 1000) {
    return -EDOM;
  }

  char result[12];
  memset(result, 0, sizeof(result));

  snprintf(result, sizeof(result), "WCDC %d", ((newIntensity + 9) / 10));

  err = laser_bwt_transport_command_generic(result, timeout);
  if (!err) {
    gState.lastState.intensity = newIntensity;
  }

  return err;
}

/**
 * @brief Internal "set firing" method
 *
 * Commands firing via UART.
 */
static int bwt_do_set_firing(const bool enabled, const k_timeout_t timeout) {
  return laser_bwt_transport_command_generic(enabled ? "WEN Y" : "WEN N", timeout);
}

/**
 * @brief Write configuration required for operation to BWT
 *
 * @return return value on success
 */
static int bwt_setup() {
  // perform one time laser setup
  HANDLE_UNLIKELY(bwt_init_temp_thresholds());

  // disable red (targeting) light
  HANDLE_UNLIKELY(laser_bwt_transport_command_generic("WRALR 0", BWT_TIMEOUT_SETUP));

#if IS_ENABLED(CONFIG_BWT_UART_FIRING)
  // set internal enable control
  HANDLE_UNLIKELY(laser_bwt_transport_command_generic("WENMODE 1", BWT_TIMEOUT_SETUP));
#else
  // set external enable control
  HANDLE_UNLIKELY(laser_bwt_transport_command_generic("WENMODE 0", BWT_TIMEOUT_SETUP));
#endif

  // set internal modulation control
  HANDLE_UNLIKELY(laser_bwt_transport_command_generic("WMMODE 1", BWT_TIMEOUT_SETUP));
  // set frequency for internal modulation
  HANDLE_UNLIKELY(laser_bwt_transport_command_generic("WCMF 5000", BWT_TIMEOUT_SETUP));
  // configure modulation duty cycle
  HANDLE_UNLIKELY(bwt_do_set_intensity(gState.lastState.intensity, BWT_TIMEOUT_SETUP));

  // internal laser power control
  HANDLE_UNLIKELY(laser_bwt_transport_command_generic("WPIMODE 1", BWT_TIMEOUT_SETUP));
  // set power to 100%
  HANDLE_UNLIKELY(laser_bwt_transport_command_generic("WCPR 100.0", BWT_TIMEOUT_SETUP));

  // fetch the inventory information now
  HANDLE_UNLIKELY(bwt_update_inventory());

  return 0;
}

/**
 * @brief Set up the laser temperature ranges
 */
static int bwt_init_temp_thresholds() {
  float limits[2];
  bool setInlet = false, setChip = false;

  // TODO: better comparison (float comparison is inexact)

  // Inlet temperature
  HANDLE_UNLIKELY(bwt_get_temp_range(kBwtTempConfigInlet, limits));
  LOG_DBG("%s temp limits: %d, %d", "Inlet", ((int)limits[0]), ((int)limits[1]));

  setInlet = (limits[0] != kTempLimitsRangeInlet[0]) || (limits[1] != kTempLimitsRangeInlet[1]);

  // Laser diode/chip temperature
  HANDLE_UNLIKELY(bwt_get_temp_range(kBwtTempConfigChip, limits));
  LOG_DBG("%s temp limits: %d, %d", "Chip", ((int)limits[0]), ((int)limits[1]));

  setChip = (limits[0] != kTempLimitsRangeChip[0]) || (limits[1] != kTempLimitsRangeChip[1]);

  // need to enable special commands to set expected ranges
  if (setInlet || setChip) {
    LOG_INF("Updating limits: inlet=%c, chip=%c", setInlet ? 'y' : 'n', setChip ? 'y' : 'n');
    HANDLE_UNLIKELY(laser_bwt_transport_command_generic("OLOGIN", BWT_TIMEOUT_LOGIN));

    if (setInlet) {
      HANDLE_UNLIKELY(bwt_set_temp_range(kBwtTempConfigInlet, kTempLimitsRangeInlet));
    }
    if (setChip) {
      HANDLE_UNLIKELY(bwt_set_temp_range(kBwtTempConfigChip, kTempLimitsRangeChip));
    }
  } else {
    LOG_DBG("temp limits ok, not changing");
  }

  return 0;
}

/**
 * @brief Read out temperature range config
 *
 * Executes an `RTLH` command.
 */
static int bwt_get_temp_range(const bwt_temp_config_index_t which, float outRange[static 2]) {
  char cmdBuf[16];
  snprintf(cmdBuf, sizeof(cmdBuf), "RTLH %u", which);

  float temp[2] = {};
  int err = laser_bwt_transport_command_numeric(cmdBuf, BWT_TIMEOUT_NVRAM_SETUP, temp, ARRAY_SIZE(temp));
  if (err < 0) {
    return err;
  }

  for (size_t i = 0; i < ARRAY_SIZE(temp); i++) {
    outRange[i] = temp[i] / 10.f;
  }

  return 0;
}

/**
 * @brief Write temperature range config
 *
 * This is used to write the temperature range limits using `WTLH` command.
 */
static int bwt_set_temp_range(const bwt_temp_config_index_t which, const float range[2]) {
  char cmdBuf[32];
  snprintf(cmdBuf, sizeof(cmdBuf), "WTLH %u %d %d", which, ((int)(range[0] * 10.f)), ((int)(range[1] * 10.f)));

  return laser_bwt_transport_command_generic(cmdBuf, BWT_TIMEOUT_NVRAM_SETUP);
}

/**
 * @brief Update BWT inventory information
 *
 * Fetch the serial number, model number, and rating information.
 */
static int bwt_update_inventory() {
  int err;
  char snStr[16], modelStr[16];
  size_t snLen, modelLen;
  float tempNum;

  // get serial number
  err = laser_bwt_transport_command_string("RSN", BWT_TIMEOUT_INVENTORY, snStr, sizeof(snStr));
  if (err < 0) {
    LOG_WRN("%s failed: %d", "RSN", err);
    return err;
  } else {
    snLen = err;
  }

  // get part number/model
  err = laser_bwt_transport_command_string("RPN", BWT_TIMEOUT_INVENTORY, modelStr, sizeof(modelStr));
  if (err < 0) {
    LOG_WRN("%s failed: %d", "RPN", err);
    return err;
  } else {
    modelLen = err;
  }

  // acquire rated power
  err = laser_bwt_transport_command_numeric("RRP", BWT_TIMEOUT_INVENTORY, &tempNum, 1);
  if (err < 0) {
    LOG_WRN("%s failed: %d", "RRP", err);
    return err;
  }

  // if all of them succeeded, copy all
  HANDLE_UNLIKELY(k_mutex_lock(&gState.inventory.lock, K_FOREVER));
  {
    strncpy(gState.inventory.serial, snStr, sizeof(snStr));
    gState.inventory.serialLen = snLen;

    strncpy(gState.inventory.model, modelStr, sizeof(modelStr));
    gState.inventory.modelLen = modelLen;

    gState.inventory.ratedPower = tempNum;
  }
  k_mutex_unlock(&gState.inventory.lock);

  return 0;
}

/**
 * @brief Reset the BWT state machine
 *
 * Reset the laser state machine to the init state, which will force re-initialization of the
 * laser.
 *
 * This is implemented by submitting a command onto the BWT worker task work queue; this queue is
 * only serviced when the laser is connected. If it's disconnected (or in the process of being
 * initialized) this is effectively a no-op.
 */
int laser_bwt_reset() {
  int err;
  bwt_poll_work_t work;

  memset(&work, 0, sizeof(work));
  work.type = kBwtWorkResetState;

  // EAGAIN indicates timeout expired; we can safely ignore that
  err = bwt_task_submit_work_await(&work, K_SECONDS(1));
  if (err < 0 && err != -EAGAIN) {
    LOG_WRN("failed to submit BWT reset command: %d", err);
  }

  return 0;
}

/**
 * @brief Update laser intensity setting
 *
 * If the laser is connected, an UART command is transmitted to it to immediately update the
 * laser intensity. Otherwise, the intensity value is stored and will be applied during the next
 * handshake/init process.
 *
 * @param newIntensity Intensity to set laser to ([0, 1000])
 */
int laser_bwt_set_intensity(const int newIntensity) {
  if (newIntensity < 0 || newIntensity > 1000) {
    LOG_WRN("invalid intensity (%u)", newIntensity);
    return -EDOM;
  } else if (!bwt_is_connected()) {
    LOG_WRN("set intensity(%u) but laser is not connected!", newIntensity);

    gState.lastState.intensity = newIntensity;
    return 0;
  }

  bwt_poll_work_t work = {kBwtWorkSetIntensity};

  work.setIntensity.value = newIntensity;
  work.setIntensity.timeout = BWT_TIMEOUT_INTENSITY;

  return bwt_task_submit_work_await(&work, work.setIntensity.timeout);
}

/**
 * @brief Command BWT firing via UART
 *
 * @remark This is only supported with the `CONFIG_BWT_UART_FIRING` Kconfig enabled, as this
 *         changes the initialization commands of the laser.
 */
int laser_bwt_set_firing(const bool enabled) {
#if IS_ENABLED(CONFIG_BWT_UART_FIRING)
  if (!bwt_is_connected()) {
    LOG_WRN("requested BWT firing(%u) but laser not connected!", enabled);
    return -ENODEV;
  }

  bwt_poll_work_t work = {kBwtWorkSetFiring};

  work.setFiring.enabled = enabled;
  work.setFiring.timeout = BWT_TIMEOUT_ENV_SENSE;

  return bwt_task_submit_work_await(&work, work.setFiring.timeout);

  return 0;
#else
  return -ENOTSUP;
#endif
}

/**
 * @brief Execute passthrough BWT command
 *
 * Send the specified command to the BWT laser and return the response. No decoding or
 * other introspection is performed.
 *
 * @param command Command string to send; should NOT have CR+LF
 *
 * @return Number of bytes received from BWT laser (may be larger than written), or negative error code
 */
int laser_bwt_run_passthrough(const char *command, char *responseBuf, const size_t responseBufSize,
                              k_timeout_t timeout) {
  if (!command || !responseBuf) {
    return -EFAULT;
  } else if (!responseBufSize) {
    return -EINVAL;
  } else if (!bwt_is_connected()) {
    LOG_WRN("Request to execute BWT command `%s`, but laser not connected", command);
    return -ENXIO;
  }

  bwt_poll_work_t work = {kBwtWorkPassthrough};

  work.passthrough.command = command;
  work.passthrough.responseBuffer = responseBuf;
  work.passthrough.responseBufferLength = responseBufSize;

  // XXX: Hack alert, ensure the command times out BEFORE our wait here!
  // this works around an issue where the command times out (due to error) and the wait also
  // times out but then the worker thread signals a nonexistent await structure
  work.passthrough.timeout = timeout;
  work.passthrough.timeout.ticks /= 2;

  return bwt_task_submit_work_await(&work, timeout);
}

/**
 * @brief Retrieve the latest BWT temperatures
 *
 * @param outBuf Storage area temperature data is copied out to
 * @param outBufSize Capacity of buffer (in elements)
 *
 * @return Negative error code, or the number of _total_ temperature readings available
 *
 * @remark If more readings are available than there is space in the buffer, only up to
 *         `outBufSize` elements are copied. However, the true number of readings is
 *         returned so the caller can detect that the provided buffer is insufficient.
 */
int laser_bwt_get_temperature(float *outBuf, const size_t outBufSize) {
  int ret;

  if (!outBuf) {
    return -EFAULT;
  } else if (!bwt_is_connected()) {
    return -ENODEV;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.sensors.lock, K_FOREVER));
  {
    if (outBufSize) {
      memcpy(outBuf, &gState.sensors.temps, sizeof(*outBuf) * MIN(BWT_MAX_NUM_TEMPS, outBufSize));
    }
    ret = gState.sensors.numTemps;
  }
  k_mutex_unlock(&gState.sensors.lock);

  return ret;
}

/**
 * @brief Retrieve the latest BWT humidity values
 *
 * @param outBuf Storage area humidity data is copied out to
 * @param outBufSize Capacity of buffer (in elements)
 *
 * @return Negative error code, or the number of _total_ humidity readings available
 *
 * @remark If more readings are available than there is space in the buffer, only up to
 *         `outBufSize` elements are copied. However, the true number of readings is
 *         returned so the caller can detect that the provided buffer is insufficient.
 */
int laser_bwt_get_humidity(float *outBuf, const size_t outBufSize) {
  int ret;

  if (!outBuf) {
    return -EFAULT;
  } else if (!bwt_is_connected()) {
    return -ENODEV;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.sensors.lock, K_FOREVER));
  {
    if (outBufSize) {
      memcpy(outBuf, &gState.sensors.humidity, sizeof(*outBuf) * MIN(BWT_MAX_NUM_HUMIDITY, outBufSize));
    }
    ret = gState.sensors.numHumidity;
  }
  k_mutex_unlock(&gState.sensors.lock);

  return ret;
}

/**
 * @brief Retrieve the latest diode current readings
 *
 * @param outBuf Storage area diode current reading data is copied out to
 * @param outBufSize Capacity of buffer (in elements)
 *
 * @return Negative error code, or the number of _total_ diode current readings available
 *
 * @remark If more readings are available than there is space in the buffer, only up to
 *         `outBufSize` elements are copied. However, the true number of readings is
 *         returned so the caller can detect that the provided buffer is insufficient.
 */
int laser_bwt_get_output_current(float *outBuf, const size_t outBufSize) {
  int ret;

  if (!outBuf) {
    return -EFAULT;
  } else if (!bwt_is_connected()) {
    return -ENODEV;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.sensors.lock, K_FOREVER));
  {
    if (outBufSize) {
      memcpy(outBuf, &gState.sensors.current, sizeof(*outBuf) * MIN(BWT_MAX_NUM_CURRENT, outBufSize));
    }
    ret = gState.sensors.numCurrent;
  }
  k_mutex_unlock(&gState.sensors.lock);

  return ret;
}

/**
 * @brief Get BWT status bits
 *
 * These bits indicate the general status of the BWT laser and the state machine that controls/
 * supervises it.
 */
int laser_bwt_get_status_bits(laser_bwt_flags_t *outStatus) {
  laser_bwt_flags_t status = 0;

  if (!outStatus) {
    return -EFAULT;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.sensors.lock, K_FOREVER));
  {
    if (bwt_is_connected()) {
      status |= kBwtConnected;
    }
    if (gState.sensors.fault) {
      status |= kBwtFaulted;
    }
  }
  k_mutex_unlock(&gState.sensors.lock);

  *outStatus = status;
  return 0;
}

/**
 * @brief Retrieve pending faults
 *
 * @param outBuf Storage area fault codes are copied out to
 * @param outBufSize Capacity of buffer (in elements)
 *
 * @return Negative error code, or the number of _total_ fault codes available
 *
 * @remark If more readings are available than there is space in the buffer, only up to
 *         `outBufSize` elements are copied. However, the true number of readings is
 *         returned so the caller can detect that the provided buffer is insufficient.
 */
int laser_bwt_get_faults(int *outBuf, const size_t outBufSize) {
  int ret;

  if (!outBuf) {
    return -EFAULT;
  } else if (!bwt_is_connected()) {
    return -ENODEV;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.sensors.lock, K_FOREVER));
  {
    if (outBufSize) {
      memcpy(outBuf, &gState.sensors.faults, sizeof(*outBuf) * MIN(BWT_MAX_NUM_FAULTS, outBufSize));
    }
    ret = gState.sensors.numFaults;
  }
  k_mutex_unlock(&gState.sensors.lock);

  return ret;
}

/**
 * @brief Retrieve BWT serial number
 *
 * Get the serial number of the currently connected laser.
 *
 * @return Negative error code or zero on success
 */
int laser_bwt_get_serial(char *outBuf, const size_t outBufSize) {
  int err = 0;

  if (!outBuf) {
    return -EFAULT;
  } else if (!outBufSize) {
    return -EINVAL;
  } else if (!bwt_is_connected()) {
    return -ENODEV;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.inventory.lock, K_FOREVER));
  {
    if (gState.inventory.serialLen) {
      strncpy(outBuf, gState.inventory.serial, outBufSize);
    }
  }
  k_mutex_unlock(&gState.inventory.lock);

  return err;
}

/**
 * @brief Retrieve BWT model number
 *
 * Get the model of the currently connected laser.
 *
 * @return Negative error code or zero on success
 */
int laser_bwt_get_model(char *outBuf, const size_t outBufSize) {
  int err = 0;

  if (!outBuf) {
    return -EFAULT;
  } else if (!outBufSize) {
    return -EINVAL;
  } else if (!bwt_is_connected()) {
    return -ENODEV;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.inventory.lock, K_FOREVER));
  {
    if (gState.inventory.modelLen) {
      strncpy(outBuf, gState.inventory.model, outBufSize);
    }
  }
  k_mutex_unlock(&gState.inventory.lock);

  return err;
}

/**
 * @brief Retrieve BWT rated power
 *
 * Get the rated power (in Watts) of the laser
 *
 * @return Negative error code or zero on success
 */
int laser_bwt_get_rated_power(int *outPower) {
  if (!outPower) {
    return -EFAULT;
  } else if (!bwt_is_connected()) {
    return -ENODEV;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.inventory.lock, K_FOREVER));
  { *outPower = gState.inventory.ratedPower; }
  k_mutex_unlock(&gState.inventory.lock);

  return 0;
}

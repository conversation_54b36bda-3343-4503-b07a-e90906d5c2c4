/**
 * @file
 * @brief Reaper-specific laser stuff
 */
#pragma once

#include <stdint.h>

#include "bwt.h"
#include "jlight.h"
#include "types.h"

// Public API for Reaper sensors
int laser_get_raw_photodiode(uint32_t *reading);
int laser_get_reaper_therm(float *therm1, float *therm2);

// Internal laser API
#if LASER_PRIVATE
int laser_reaper_init();

int laser_reaper_set_intensity(const uint32_t newIntensity);
int laser_reaper_set_type(const laser_type_t newType);
#endif

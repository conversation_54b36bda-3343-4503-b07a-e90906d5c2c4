#include <zephyr/logging/log.h>
LOG_MODULE_DECLARE(laser, CONFIG_APP_LOG_LEVEL);

#include <utils/handle_errors.h>

#if USE_REAPER
#include "reaper.h"
#endif

#include "../laser.h"
#include "internal.h"
#include "nanopb.h"

/**
 * @brief Handle request to reset laser
 *
 * This will reset various pieces of the laser subsystem depending on the connected laser type.
 */
int laser_handle_nanopb_reset(laser_Laser_Reset_Request *request) {
  laser_type_t type;

  if (!request) {
    return -EFAULT;
  }

  HANDLE_UNLIKELY(laser_get_type(&type));

  switch (type) {
#if USE_REAPER
  case kLaserTypeDiodeBWT:
    // BWT can't clear faults via software currently
    if (request->faults) {
      return -ENOTSUP;
    }
    // reset the underlying transport
    if (request->transport) {
      HANDLE_UNLIKELY(laser_bwt_reset());
    }
    break;

  case kLaserTypeDiodeJlight:
    // TODO: implement this
    return -ENOTSUP;
#endif

  default:
    LOG_WRN("%s not supported for laser type %s", __FUNCTION__, laser_get_type_name(type));
    return -EINVAL;
  }

  return 0;
}

/**
 * @brief Set the laser output drive current
 */
int laser_handle_nanopb_set_current(laser_Diode_Set_Current_Request *request) {
  laser_type_t type;

  if (!request) {
    return -EFAULT;
  }

  HANDLE_UNLIKELY(laser_get_type(&type));

  switch (type) {
#if USE_REAPER
    // TODO: implement for BWT

  case kLaserTypeDiodeJlight:
    return laser_jlight_set_current(request->current, request->current_count, request->commit);
#endif

  default:
    LOG_WRN("%s not supported for laser type %s", __FUNCTION__, laser_get_type_name(type));
    return -EINVAL;
  }

  return 0;
}

/**
 * @brief Update the laser type configuration
 *
 * @remark Currently supported only on Reaper
 */
int laser_handle_nanopb_set_type(laser_Laser_Set_Type_Request *request) {
  if (!request) {
    return -EFAULT;
  }

#if USE_REAPER
  // validate type
  laser_type_t type;

  switch (request->type) {
  case laser_LaserType_LASERTYPE_UNKNOWN:
    type = kLaserTypeUnknown;
    break;
  case laser_LaserType_LASERTYPE_DIODE_BWT:
    type = kLaserTypeDiodeBWT;
    break;
  case laser_LaserType_LASERTYPE_DIODE_JLIGHT:
    type = kLaserTypeDiodeJlight;
    break;

  default:
    LOG_WRN("invalid laser type from nanopb: %u", request->type);
    return -EINVAL;
  }

  // set in EEPROM
  HANDLE_UNLIKELY(laser_reaper_set_type(type));

  return 0;
#else
  return -ENOTSUP;
#endif
}

/**
 * @brief Fill a laser inventory struct
 */
int laser_fill_nanopb_inventory(laser_Laser_Inventory_Reply *outInventory) {
  laser_type_t type;

  if (!outInventory) {
    return -EFAULT;
  }

  HANDLE_UNLIKELY(laser_get_type(&type));

  switch (type) {
#if USE_REAPER
  case kLaserTypeDiodeBWT:
    outInventory->type = laser_LaserType_LASERTYPE_DIODE_BWT;
    return laser_bwt_fill_nanopb_inventory(outInventory);

  case kLaserTypeDiodeJlight:
    outInventory->type = laser_LaserType_LASERTYPE_DIODE_JLIGHT;
    return laser_jlight_fill_nanopb_inventory(outInventory);
#endif

  default:
    LOG_WRN("%s not supported for laser type %s", __FUNCTION__, laser_get_type_name(type));
    return -EINVAL;
  }
}

/**
 * @brief Fill laser state struct for connected laser type
 */
int laser_fill_nanopb_status(laser_Diode_Status_Reply *outStatus) {
  if (!outStatus) {
    return -EFAULT;
  }

#if USE_REAPER
  int err;
  laser_type_t type;

  // thermistors: common for all
  err = laser_get_reaper_therm(&outStatus->thermistors[0], &outStatus->thermistors[1]);
  if (!err) {
    outStatus->thermistors_count = 2;
  } else {
    LOG_WRN("%s failed: %d", "laser_get_reaper_therm", err);
  }

  // type-specific data
  HANDLE_UNLIKELY(laser_get_type(&type));

  switch (type) {
  case kLaserTypeDiodeBWT:
    return laser_bwt_fill_nanopb_status(outStatus);

  case kLaserTypeDiodeJlight:
    return laser_jlight_fill_nanopb_status(outStatus);

  default:
    LOG_WRN("%s not supported for laser type %s", __FUNCTION__, laser_get_type_name(type));
    return -EINVAL;
  }
#else
  return -ENOTSUP;
#endif
}

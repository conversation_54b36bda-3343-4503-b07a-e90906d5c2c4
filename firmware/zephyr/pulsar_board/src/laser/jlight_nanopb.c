#include <zephyr/logging/log.h>
LOG_MODULE_DECLARE(laser_jlight, CONFIG_APP_LOG_LEVEL);

#include <string.h>
#include <utils/handle_errors.h>

#include "jlight.h"
#include "jlight_transport.h"

/**
 * @brief Fill out the laser's inventory data
 *
 * Basically a no-op since we can't read any of this info from the laser…
 */
int laser_jlight_fill_nanopb_inventory(laser_Laser_Inventory_Reply *outInventory) {
  strncpy(outInventory->serial, "N/A", sizeof(outInventory->serial));
  strncpy(outInventory->model, "Jlight", sizeof(outInventory->model));
  outInventory->power = 270;

  return 0;
}

/**
 * @brief Fill out the current Jlight laser status
 */
int laser_jlight_fill_nanopb_status(laser_Diode_Status_Reply *outStatus) {
  int err;
  laser_jlight_flags_t jlightState = 0;

  // status flags
  HANDLE_UNLIKELY(laser_jlight_get_status_bits(&jlightState));

  if (!(jlightState & kJlightConnected)) {
    return -ENXIO;
  }

  // set up the message for jlight aux data
  outStatus->which_extra = laser_Diode_Status_Reply_extra_jlight_tag;
  laser_Jlight_Status *js = &outStatus->extra.extra_jlight;

  // laser temperatures (from NTCs)
  const size_t kProtoNumTemps = ARRAY_SIZE(outStatus->temp);
  err = laser_jlight_get_temperature(outStatus->temp, kProtoNumTemps);

  if (err >= 0) {
    outStatus->temp_count = MIN(err, kProtoNumTemps);
  } else {
    LOG_WRN("%s failed: %d", "laser_jlight_get_temps", err);
  }

  // diode currents
  const size_t kProtoNumCurrent = ARRAY_SIZE(outStatus->current);
  err = laser_jlight_get_output_current(outStatus->current, kProtoNumCurrent);

  if (err >= 0) {
    outStatus->current_count = MIN(err, kProtoNumCurrent);
  } else {
    LOG_WRN("%s failed: %d", "laser_jlight_get_output_current", err);
  }

  // fault flag
  outStatus->faulted = !!(jlightState & kJlightAlarmMask);
  // TODO: additional fault state bits

  // power supply input voltage
  err = laser_jlight_get_psu_input_voltage(&js->psuInputVolts);
  if (err) {
    LOG_WRN("%s failed: %d", "laser_jlight_get_psu_input_voltage", err);
  }

  err = laser_jlight_get_psu_temperature(&js->psuTemp);
  if (err) {
    LOG_WRN("%s failed: %d", "laser_jlight_get_psu_temperature", err);
  }

  // laser diode voltages per ch
  const size_t kProtoNumVoltage = ARRAY_SIZE(js->psuOutputVolts);
  err = laser_jlight_get_output_voltage(js->psuOutputVolts, kProtoNumVoltage);

  if (err >= 0) {
    js->psuOutputVolts_count = MIN(err, kProtoNumVoltage);
  } else {
    LOG_WRN("%s failed: %d", "laser_jlight_get_output_voltage", err);
  }

  return 0;
}

/**
 * @file
 * @brief Physical transport to Jlight laser
 */
#pragma once

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

#include <zephyr/kernel.h>

#include "jlight_registers.h"

int laser_jlight_transport_init();
int laser_jlight_transport_process_rx();

int laser_jlight_transport_read_discretes(uint32_t *out);
int laser_jlight_transport_read_input(const jlight_reg_input_t reg, uint16_t *out);
int laser_jlight_transport_read_input_multi(const jlight_reg_input_t reg, const size_t num, uint16_t *out);
int laser_jlight_transport_read_hold(const jlight_reg_hold_t reg, uint16_t *out);
int laser_jlight_transport_write_hold(const jlight_reg_hold_t reg, const uint16_t value);
int laser_jlight_transport_write_coil(const jlight_write_coils_t which, const bool state);

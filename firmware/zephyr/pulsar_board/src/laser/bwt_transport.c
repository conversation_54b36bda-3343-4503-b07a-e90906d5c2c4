// for _strtok_r
#define _BSD_SOURCE 1

#include <zephyr/logging/log.h>
LOG_MODULE_DECLARE(laser_bwt);

#include <zephyr/device.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/uart.h>
#include <zephyr/kernel.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include <utils/float_helpers.h>
#include <utils/handle_errors.h>

// NOTE: BWT laser only on Reaper, which only uses new EEPROM
#include "../eeprom_new.h"
#include "bwt_transport.h"
#include "internal.h"

/**
 * @brief Size of the BWT UART receive buffer
 *
 * This sets the upper bound on the maximum length of a message received from the laser.
 */
#define MAX_RX_BUF_SIZE 128

/**
 * @brief Maximum number of pending BWT receive commands
 *
 * Sets the size of the receive message queue. If a command response is received while the queue is
 * full, all subsequent messages are dropped.
 */
#define RX_BWT_QUEUE_SIZE 3
/**
 * @brief Maximum number of pending DMA receive chunks
 *
 * A single BWT response may be spread over multiple DMA buffers.
 */
#define RX_CHUNK_QUEUE_SIZE (RX_BWT_QUEUE_SIZE * 2)

/// Size of the UART receive buffer
#define UART_RX_DMA_BUF_LEN (MAX_RX_BUF_SIZE)
/// Size of the UART transmit DMA buffer
#define UART_TX_DMA_BUF_LEN (MAX_RX_BUF_SIZE)
/// Receive timeout (e.g. maximum time between characters) in msec
#define UART_RX_TIMEOUT (50)

// Status of the receive state machine in detecting end-of-message
typedef enum bwt_rx_state {
  // Waiting for CR
  kRxStateIdle,
  // Waiting for LF
  kRxStateCr,
  // Got CR+LF; process command
  kRxStateLf,
} bwt_rx_state_t;

// A single chunk of DMA buffer data that's been received
typedef struct bwt_rx_chunk {
  // First character of message
  const char *data;
  // Number of characters received
  size_t length;
} bwt_rx_chunk_t;

// State for the transport
typedef struct bwt_transport_state {
  // DMA driven UART reception/transmission stuff
  struct {
    // Signalled by the UART handler when the TX DMA completes
    struct k_sem txCompleteSignal;
    // which buffer to use next DMA receive
    size_t rxBuffer;
  } dma;

  // Command reply reception buffer/handling
  struct {
    // Current state of the end-of-command detection machinery
    bwt_rx_state_t state;
    // circular buffer for received UART characters
    uint8_t buf[MAX_RX_BUF_SIZE];
    // write pointer for next byte
    size_t writePtr;
    // read pointer for message (start of message)
    size_t readPtr;
  } rx;

  // Flags for debugging BWT transport
  atomic_t flags;
} bwt_transport_state_t;

static void handle_uart_event(const struct device *dev, struct uart_event *evt, void *);
static void uart_rx_work(struct k_work *);
static int handle_uart_rx(const char *data, const size_t length);

static bool bwt_reply_is_ok(const char *reply);
static bool bwt_reply_is_error(const char *reply);

__dtcm_bss_section static bwt_transport_state_t gState;

/// Message receive buffer (musn't be in CCM such that DMA can access it)
__nocache static uint8_t gRxBuffer[2][UART_RX_DMA_BUF_LEN];
/// DMA transmit buffer (musn't be in CCM such that DMA can access it)
__nocache static uint8_t gTxBuffer[UART_TX_DMA_BUF_LEN];

// this queue contains chunks of received DMA stuff
K_MSGQ_DEFINE(gBwtDmaRxQueue, sizeof(bwt_rx_chunk_t), RX_CHUNK_QUEUE_SIZE, 2);
// work item to process received DMA buffer chunks into messages
K_WORK_DEFINE(gDmaRxWork, uart_rx_work);

// this queue contains fully received, deframed messages from BWT
K_MSGQ_DEFINE(gRxQueue, sizeof(bwt_rx_command_t), RX_BWT_QUEUE_SIZE, 2);

/**
 * @brief Initialize BWT transport
 *
 * This sets up the UART for DMA driven reception.
 */
int laser_bwt_transport_init() {
  int err;

  memset(&gState, 0, sizeof(gState));
  gState.rx.state = kRxStateIdle;

  // set up kernel objects
  k_sem_init(&gState.dma.txCompleteSignal, 1, 1);

  // restore debug settings from EEPROM
  uint32_t temp;
  err = eeprom_load_bwt_debug_flags(&temp);
  if (err) {
    LOG_WRN("%s failed: %d", "eeprom_load_bwt_debug_flags", err);
    atomic_clear(&gState.flags);
  } else {
    if (temp) {
      LOG_INF("Restoring BWT transport flags: %08x", temp);
    }
    atomic_set(&gState.flags, temp);
  }

  // set up UART for DMA driven operation
  if (!gLaserConfig.laserUart) {
    LOG_ERR("No laser UART configured");
    return -EINVAL;
  }
  HANDLE_UNLIKELY_BOOL(device_is_ready(gLaserConfig.laserUart), ENODEV);

  HANDLE_UNLIKELY(uart_callback_set(gLaserConfig.laserUart, handle_uart_event, NULL));
  HANDLE_UNLIKELY(uart_rx_enable(gLaserConfig.laserUart, gRxBuffer[0], UART_RX_DMA_BUF_LEN, (UART_RX_TIMEOUT * 1000)));

  return 0;
}

/**
 * @brief Send command to BWT
 *
 * Transmit the given NULL-terminated string over the UART to the BWT; a terminating CR+LF is
 * added automatically.
 *
 * @remark Blocks until the command has been sent completely
 */
int laser_bwt_transport_send(const char *str) {
  size_t len = strlen(str);
#if CONFIG_LOG_BWT_UART
  LOG_HEXDUMP_DBG(str, len, "BWT TX");
#endif

  // ensure it fits in the TX DMA buffer before copying
  if ((len + 2) > UART_TX_DMA_BUF_LEN) {
    return -ENOMEM;
  }

  memcpy(gTxBuffer, str, len);
  gTxBuffer[len++] = '\r';
  gTxBuffer[len++] = '\n';

  if (atomic_get(&gState.flags) & kBwtDebugFlagLogTx) {
    LOG_HEXDUMP_INF(gTxBuffer, len, ">>>");
  }

  // do DMA transmit
  HANDLE_UNLIKELY(uart_tx(gLaserConfig.laserUart, gTxBuffer, len, SYS_FOREVER_US));
  // and block waiting for TX complete
  HANDLE_UNLIKELY(k_sem_take(&gState.dma.txCompleteSignal, K_FOREVER));

  return 0;
}

/**
 * @brief Send command to BWT and wait for respons
 *
 * Transmit the given NULL-terminated string over UART to the BWT. Then wait (up to the given
 * timeout) for a response, which is copied into the provided buffer.
 *
 * @param cmd Command string to send
 * @param outBuf Buffer to receive the response message
 * @param outBufLen Capacity of the output buffer, in bytes
 * @param timeout Maximum time to wait for a reply, in msec
 *
 * @return A negative error code, or the number of bytes received in response and written to the
 *         receive buffer
 *
 * @remark If the provided buffer is not large enough to fit the entire message, only `outBufLen`
 *         bytes are copied. However, the actual full length of the received message is returned.
 */
int laser_bwt_transport_send_await(const char *cmd, char *outBuf, const size_t outBufLen, const k_timeout_t timeout) {
  int err;
  bwt_rx_command_t msg;

  if (!cmd || (outBuf && !outBufLen)) {
    return -EFAULT;
  }

  // remove any pending replies before transmitting request
  const size_t numPending = k_msgq_num_used_get(&gRxQueue);
  if (numPending) {
    LOG_WRN("purging %u unclaimed BWT messages", numPending);
  }

  k_msgq_purge(&gRxQueue);

  HANDLE_UNLIKELY(laser_bwt_transport_send(cmd));

  // wait for reply
  err = k_msgq_get(&gRxQueue, &msg, timeout);
  if (err) {
    return err;
  }

  // copy out the message data
  size_t writeOffset = 0, length = MIN(msg.length, outBufLen);

  while (length--) {
    const size_t readOffset = (msg.start + writeOffset) % MAX_RX_BUF_SIZE;
    outBuf[writeOffset++] = gState.rx.buf[readOffset];
  }

  // always NULL terminate string; may overwrite the last char if buffer is insufficient
  if (writeOffset < outBufLen) {
    outBuf[writeOffset++] = '\0';
  } else {
    outBuf[outBufLen - 1] = '\0';
  }

  if (atomic_get(&gState.flags) & kBwtDebugFlagLogRx) {
    LOG_HEXDUMP_INF(outBuf, writeOffset, "<<<");
  }

#if CONFIG_LOG_BWT_UART
  LOG_HEXDUMP_DBG(outBuf, writeOffset, "BWT RX");
#endif

  // additional delay between commands!
  if (atomic_get(&gState.flags) & kBwtDebugFlagAddCommandDelay) {
    k_sleep(K_MSEC(50));
  }

  return msg.length;
}

/**
 * @brief Check if the BWT response indicates success
 *
 * For many commands the laser will return just the string "OK" to indicate success; this
 * checks a response to see if that's what it contains.
 */
static bool bwt_reply_is_ok(const char *reply) {
  const size_t len = strlen(reply);
  if (len >= 2) {
    return (reply[0] == 'O') && (reply[1] == 'K');
  }

  return false;
}

/**
 * @brief Check if the BWT response indicates error
 *
 * For most commands the BWT can return the text string "ERROR" if something goes wrong.
 */
static bool bwt_reply_is_error(const char *reply) {
  const size_t len = strlen(reply);
  if (len >= 5) {
    return (strncmp(reply, "ERROR", len) == 0);
  }

  return false;
}

/**
 * @brief Send generic BWT command and await response
 *
 * A generic command is one that has a response of either `OK` or `ERROR` from the laser.
 */
int laser_bwt_transport_command_generic(const char *cmd, const k_timeout_t timeout) {
  int err;
  char reply[16];

  err = laser_bwt_transport_send_await(cmd, reply, sizeof(reply), timeout);
  if (err < 0) {
    return err;
  } else if (err > sizeof(reply)) {
    return -E2BIG;
  }

  if (bwt_reply_is_ok(reply)) {
    return 0;
  } else if (bwt_reply_is_error(reply)) {
    return -ENOMSG;
  } else {
    LOG_HEXDUMP_WRN(reply, err, "invalid command reply");
    return -EIO;
  }
}

/**
 * @brief Send BWT command and await response
 *
 * The response of the command is written into the specified string buffer. We'll also check if
 * the command returned an "ERROR" reply.
 */
int laser_bwt_transport_command_string(const char *cmd, const k_timeout_t timeout, char *outBuffer,
                                       const size_t outBufferSize) {
  int err;

  err = laser_bwt_transport_send_await(cmd, outBuffer, outBufferSize, timeout);
  if (err < 0) {
    return err;
  } else if (err > outBufferSize) {
    return -E2BIG;
  }

  // check for error reply
  if (bwt_reply_is_error(outBuffer)) {
    return -ENOMSG;
  }

  // otherwise, return the total message length
  return err;
}

/**
 * @brief Send a command and decode a response of space-separated numbers
 *
 * Numbers must be represented in decimal (x.y format) and may have an optional sign character.
 *
 * @param cmd Command string to send to BWT
 * @param timeout How long to wait for a reply
 * @param outBuffer Storage for decoded numbers
 * @param outBufferSize Total capacity of output buffer
 *
 * @return Negative error code, or the number of numeric values stored
 */
int laser_bwt_transport_command_numeric(const char *cmd, const k_timeout_t timeout, float *outBuffer,
                                        const size_t outBufferSize) {
  int err;
  char reply[32];

  if (!outBuffer || !outBufferSize) {
    return -EFAULT;
  }

  // send command and handle error case
  err = laser_bwt_transport_send_await(cmd, reply, sizeof(reply), timeout);
  if (err < 0) {
    return err;
  } else if (err > sizeof(reply)) {
    return -E2BIG;
  }

  if (bwt_reply_is_error(reply)) {
    return -ENOMSG;
  }

  // split string by spaces, decode floating point numbers
  char *tokenCtx;
  char *token = strtok_r(reply, " ", &tokenCtx);
  size_t outIndex = 0;

  while (token) {
    // convert to floating point
    double temp;
    err = util_strtod(token, &temp);

    if (!err) {
      if (outIndex < outBufferSize) {
        outBuffer[outIndex] = temp;
      }
    }
    // failed to convert number
    else {
      LOG_WRN("failed to convert `%s`: %d", token, err);
    }

    // get next token
    token = strtok_r(NULL, " ", &tokenCtx);
    outIndex++;
  }

  return outIndex;
}

/**
 * @brief Set the enabled BWT debug flags
 *
 * The flags are written to EEPROM as well to be restored on next boot.
 */
int laser_bwt_transport_set_debug_flags(const bwt_debug_flags_t newFlags) {
  int err;

  const bwt_debug_flags_t old = atomic_set(&gState.flags, newFlags);
  const bool changed = (old != newFlags);

  if (changed) {
    LOG_INF("BWT debug flags changed: %08x -> %08x", old, newFlags);

    err = eeprom_save_bwt_debug_flags(newFlags);
    if (err) {
      LOG_WRN("%s failed: %d", "eeprom_save_bwt_debug_flags", err);
    }
  }

  return 0;
}

/**
 * @brief Get currently active BWT debug flags
 */
int laser_bwt_transport_get_debug_flags(bwt_debug_flags_t *flags) {
  if (!flags) {
    return -EFAULT;
  }

  *flags = atomic_get(&gState.flags);

  return 0;
}

/**
 * @brief Process UART driver event
 *
 * UART driver asynchronously calls this (which may happen from other contexts/threads) with
 * various events, such as receive complete.
 */
static void handle_uart_event(const struct device *dev, struct uart_event *evt, void *) {
  switch (evt->type) {
  // provide the next RX buffer
  case UART_RX_BUF_REQUEST:
    uart_rx_buf_rsp(gLaserConfig.laserUart, gRxBuffer[++gState.dma.rxBuffer % 2], UART_RX_DMA_BUF_LEN);
    break;
  // buffer no longer in use; don't do anything here
  case UART_RX_BUF_RELEASED:
    break;

  // Receive complete (e.g. timeout, buffer full, receiver disabled/stopped)
  case UART_RX_RDY: {
    bwt_rx_chunk_t chunk;
    chunk.data = (evt->data.rx.buf + evt->data.rx.offset);
    chunk.length = evt->data.rx.len;

    int err = k_msgq_put(&gBwtDmaRxQueue, &chunk, K_NO_WAIT);
    if (err) {
      LOG_WRN("failed to submit RX DMA chunk: %d", err);
    } else {
      k_work_submit(&gDmaRxWork);
    }
    break;
  }

  // Likely due to overrun or other framing error
  case UART_RX_STOPPED:
    // TODO: handle this (reset receive state machine)
    break;

  // finished transmitting most recent buffer
  case UART_TX_DONE:
    k_sem_give(&gState.dma.txCompleteSignal);
    break;
  // transmission failed/aborted, reset state
  case UART_TX_ABORTED:
    // TODO: need we do anything here?
    break;

  // ignore all other events types
  default:
    break;
  }
}

/**
 * @brief Deferred UART RX handling
 *
 * Process all received UART message chunks. This executes on the system work queue. It must be on
 * the work queue so we don't deadlock the worker task when it blocks waiting for a BWT response.
 */
static void uart_rx_work(struct k_work *) {
  int err;
  bwt_rx_chunk_t chunk;

  while (!k_msgq_get(&gBwtDmaRxQueue, &chunk, K_NO_WAIT)) {
    err = handle_uart_rx(chunk.data, chunk.length);
    if (err) {
      LOG_WRN("%s failed: %d", "handle_uart_rx", err);
    }
  }
}

/**
 * @brief Process a deferred UART receive event
 *
 * This parses out received responses in the buffer and pushes them onto the receive queue.
 */
static int handle_uart_rx(const char *data, const size_t length) {
#if CONFIG_LOG_BWT_UART
  LOG_HEXDUMP_DBG(data, length, "UART RX");
#endif

  for (size_t i = 0; i < length; i++) {
    const char scratch = data[i];

    // discard non-printable data
    if (!isprint(scratch)) {
      if (scratch != '\r' && scratch != '\n') {
        LOG_WRN("discarding non-printable character: %02x (index %u)", scratch, i);
        continue;
      }
    }

    // store received byte
    gState.rx.buf[gState.rx.writePtr] = scratch;
    gState.rx.writePtr = (gState.rx.writePtr + 1) % MAX_RX_BUF_SIZE;

    // check if we've received end-of-command
    switch (gState.rx.state) {
    // receive any character: if CR transition to next state
    case kRxStateIdle:
      if (scratch == '\r') {
        gState.rx.state = kRxStateCr;
      }
      break;

    // received CR; must receive LF to advance
    case kRxStateCr:
      if (scratch == '\n') {
        gState.rx.state = kRxStateLf;
        // fall through to the appropriate case below
      }
      // wrong character, back to idle
      else {
        gState.rx.state = kRxStateIdle;
        break;
      }

    // handle the fall-through from if we transitioned from CR -> LF state above
    case kRxStateLf: {
      // drop message into processing queue after dropping CR+LF
      size_t msgLen;
      int err;

      if (gState.rx.writePtr >= gState.rx.readPtr) {
        msgLen = (gState.rx.writePtr - gState.rx.readPtr);
      } else {
        msgLen = MAX_RX_BUF_SIZE - (gState.rx.readPtr - gState.rx.writePtr);
      }

      bwt_rx_command_t msg = {.start = gState.rx.readPtr, .length = (msgLen - 2)};

      err = k_msgq_put(&gRxQueue, &msg, K_NO_WAIT);
      if (err) {
        LOG_WRN("failed to enqueue BWT command: %d", err);
      }

      // reset the buffer
      gState.rx.readPtr = gState.rx.writePtr;
      gState.rx.state = kRxStateIdle;
      break;
    }
    }
  }

  return 0;
}

/**
 * @file
 * @brief BWT laser communication
 *
 * Implementation of communication with the BWT laser via the serial interface. This is only used
 * on Reaper.
 */
#pragma once

#include <stdbool.h>
#include <stddef.h>

#include <zephyr/kernel.h>

#include "generated/lib/drivers/nanopb/proto/pulczar_board.pb.h"

// Bitwise OR of status flags for BWT
typedef enum laser_bwt_flags {
  kBwtConnected = (1 << 0),
  kBwtFaulted = (1 << 1),
} laser_bwt_flags_t;

#ifdef LASER_PRIVATE
int laser_bwt_init();
#endif

int laser_bwt_reset();

int laser_bwt_set_intensity(const int newIntensity);
int laser_bwt_set_firing(const bool enabled);
int laser_bwt_run_passthrough(const char *command, char *responseBuf, const size_t responseBufSize,
                              k_timeout_t timeout);

int laser_bwt_get_temperature(float *outBuf, const size_t outBufSize);
int laser_bwt_get_humidity(float *outBuf, const size_t outBufSize);
int laser_bwt_get_output_current(float *outBuf, const size_t outBufSize);
int laser_bwt_get_status_bits(laser_bwt_flags_t *outStatus);
int laser_bwt_get_faults(int *outBuf, const size_t outBufSize);

int laser_bwt_get_serial(char *outBuf, const size_t outBufSize);
int laser_bwt_get_model(char *outBuf, const size_t outBufSize);
int laser_bwt_get_rated_power(int *outPower);

int laser_bwt_fill_nanopb_inventory(laser_Laser_Inventory_Reply *outInventory);
int laser_bwt_fill_nanopb_status(laser_Diode_Status_Reply *outStatus);
int laser_bwt_fill_nanopb_passthrough(const laser_Bwt_Passthrough_Request *request,
                                      laser_Bwt_Passthrough_Reply *outStatus);
int laser_bwt_fill_nanopb_transport_config(laser_Bwt_Transport_Config *outConfig);

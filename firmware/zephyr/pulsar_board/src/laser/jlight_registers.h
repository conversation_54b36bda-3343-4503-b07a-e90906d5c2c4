/**
 * @file
 * @brief Register numbering/layout for JLight laser
 *
 * Defines the MODBUS coils and holding registers used by JLight lasers.
 */
#pragma once

#include <stddef.h>
#include <stdint.h>

/// Number of laser output channels
#define JLIGHT_NUM_OUT_CH (2)

/// Readable discrete signals
typedef enum {
  kJlightReadDiscreteCh1Light = 0,
  kJlightReadDiscreteCh2Light = 1,
  kJlightReadDiscreteReady = 4,
  kJlightReadDiscreteSystemAlarm = 16,
  kJlightReadDiscreteCh1OvercurrentAlarm = 17,
  kJlightReadDiscreteCh2OvercurrentAlarm = 18,
  kJlightReadDiscreteCh1OvervoltageAlarm = 19,
  kJlightReadDiscreteCh2OvervoltageAlarm = 20,
  kJlightReadDiscreteNtc1OvertempAlarm = 21,
  kJlightReadDiscreteNtc2OvertempAlarm = 22,
  kJlightReadDiscreteDriverOvertempAlarm = 23,
} jlight_read_discretes_t;

/// Writeable coils (use "write single coil" command)
typedef enum {
  kJlightWriteCoilCh1Enable = 2,
  kJlightWriteCoilCh2Enable = 3,
  // Reset stored alarms
  kJlightWriteCoilAlarmReset = 5,
  // Commit output params (current, etc.) to NVRAM
  kJlightWriteCoilSaveOutputParams = 8,

  // TODO: add other bits
} jlight_write_coils_t;

/// Readable holding registers (use "read single hold" or "read multi hold" command)
typedef enum {
  kJlightReadHoldCh1LoadCurrent = 0,
  kJlightReadHoldCh2LoadCurrent = 1,
  kJlightReadHoldCh1LoadVoltage = 2,
  kJlightReadHoldCh2LoadVoltage = 3,
  kJlightReadHoldTempNtc1 = 4,
  kJlightReadHoldTempNtc2 = 5,
  kJlightReadHoldTempPsu = 6,
  kJlightReadHoldInputVoltage = 7,
} jlight_reg_input_t;

/// Writeable holding registers (use "write single hold" command)
typedef enum {
  // Ch1 power supply peak current, in 0.1A units
  kJlightWriteHoldCh1PeakCurrent = 0,
  // Ch1 power supply peak current, in 0.1A units
  kJlightWriteHoldCh2PeakCurrent = 1,

  kJlightWriteHoldNtc1UpperLimit = 12,
  kJlightWriteHoldNtc2UpperLimit = 13,
  // TODO: add other stuff
} jlight_reg_hold_t;

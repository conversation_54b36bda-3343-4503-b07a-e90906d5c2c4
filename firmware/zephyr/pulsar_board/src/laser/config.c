#include <zephyr/device.h>

#include "internal.h"

// Laser IO configuration from dts
const laser_config_t gLaserConfig = {
    .laserFire = GPIO_DT_SPEC_GET(DT_ALIAS(laser), fire_gpios),
    .laserIntensity = PWM_DT_SPEC_GET_BY_NAME_OR(DT_ALIAS(laser), intensity, {NULL}),
    .laserUart = DEVICE_DT_GET_OR_NULL(DT_PROP(DT_ALIAS(laser), laser_uart)),

    .powerMeter = DEVICE_DT_GET_OR_NULL(DT_PROP(DT_ALIAS(laser), power_meter)),
};

/**
 * @file
 * @brief Jlight laser interfacing
 */
#pragma once

#include <stdbool.h>
#include <stddef.h>

#include <zephyr/kernel.h>

#include "generated/lib/drivers/nanopb/proto/pulczar_board.pb.h"

// Bitwise OR of status flags for JLight
typedef enum laser_jlight_flags {
  kJlightConnected = (1 << 0),

  // laser system ready for IO
  kJlightReady = (1 << 1),

  // system alarm
  kJlightAlarmSystem = (1 << 2),
  // output ch1/2 overcurrent
  kJlightAlarmOutputOvercurrent = (1 << 3),
  // output ch1/2 overvoltage
  kJlightAlarmOutputOvervoltage = (1 << 4),
  // NTC temperature out of range
  kJlightAlarmOutputNtcTemp = (1 << 5),
  // Diode module temp out of range
  kJlightAlarmOutputDiodeTemp = (1 << 6),
  // Bit mask for all alarms
  kJlightAlarmMask = (kJlightAlarmSystem | kJlightAlarmOutputOvercurrent | kJlightAlarmOutputOvervoltage |
                      kJlightAlarmOutputNtcTemp | kJlightAlarmOutputDiodeTemp),

  // Interlock fault
  kJlightInterlock = (1 << 7),

  // CH1 optical output enabled
  kJlightCh1Output = (1 << 8),
  // CH2 optical output enabled
  kJlightCh2Output = (1 << 9),
} laser_jlight_flags_t;

#ifdef LASER_PRIVATE
int laser_jlight_init();
#endif

int laser_jlight_set_current(const uint32_t *currentBuf, const size_t numCurrents, const bool save);
int laser_jlight_set_intensity(const int newIntensity);
// TODO: reset transport/alarms

int laser_jlight_get_temperature(float *outBuf, const size_t outBufSize);
int laser_jlight_get_output_current(float *outBuf, const size_t outBufSize);
int laser_jlight_get_output_voltage(float *outBuf, const size_t outBufSize);
int laser_jlight_get_psu_input_voltage(float *outVolts);
int laser_jlight_get_psu_temperature(float *outTemp);
int laser_jlight_get_status_bits(laser_jlight_flags_t *outStatus);

// TODO: can we get serial, model, rated power?

int laser_jlight_fill_nanopb_inventory(laser_Laser_Inventory_Reply *outInventory);
int laser_jlight_fill_nanopb_status(laser_Diode_Status_Reply *outStatus);

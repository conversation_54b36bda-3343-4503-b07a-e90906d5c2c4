/**
 * @file
 * @brief BWT laser low level IO communication
 *
 * Implements control for the low level transport (via UART) including framing messages.
 */
#pragma once

#include <stddef.h>
#include <stdint.h>

#include <zephyr/kernel.h>

// Bitfield of debugging flags
typedef enum {
  // Print all received BWT messages to syslog
  kBwtDebugFlagLogRx = (1 << 0),
  // Print all transmitted BWT messages to syslog
  kBwtDebugFlagLogTx = (1 << 1),
  // Add some fixed delay between BWT commands
  kBwtDebugFlagAddCommandDelay = (1 << 2),
} bwt_debug_flags_t;

// Defines the extents of a message; length may wrap around buffer
typedef struct bwt_rx_command {
  // offset of first character in RX circular buffer
  size_t start;
  // total number of characters received
  size_t length;
} bwt_rx_command_t;

int laser_bwt_transport_init();

int laser_bwt_transport_send(const char *str);
int laser_bwt_transport_send_await(const char *cmd, char *outBuf, const size_t outBufLen, const k_timeout_t timeout);

int laser_bwt_transport_command_generic(const char *cmd, const k_timeout_t timeout);
int laser_bwt_transport_command_string(const char *cmd, const k_timeout_t timeout, char *outBuffer,
                                       const size_t outBufferSize);
int laser_bwt_transport_command_numeric(const char *cmd, const k_timeout_t timeout, float *outBuffer,
                                        const size_t outBufferSize);

int laser_bwt_transport_set_debug_flags(const bwt_debug_flags_t flags);
int laser_bwt_transport_get_debug_flags(bwt_debug_flags_t *flags);

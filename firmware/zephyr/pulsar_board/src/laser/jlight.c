#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(laser_jlight, CONFIG_APP_LOG_LEVEL);

#include <zephyr/device.h>
#include <zephyr/devicetree.h>
#include <zephyr/kernel.h>
#include <zephyr/net/ptp_time.h>
#include <zephyr/sys/util.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include <lib/ptp/ptp.h>
#include <utils/handle_errors.h>

#include "../power.h"
#include "jlight.h"
#include "jlight_registers.h"
#include "jlight_transport.h"

// Output raw register values from polling
#define JLIGHT_LOG_POLL_VALUES (0)

/**
 * @brief Upper limit for temperature valid range
 *
 * Used to discard an invalid temperature (e.g. disconnected thermistor)
 */
#define JLIGHT_MAX_TEMP_VALID 200.f

/**
 * @brief Maximum delay to wait for laser init during boot
 */
#define JLIGHT_INIT_TIMEOUT K_SECONDS(5)

/**
 * @brief Size of work queue
 */
#define JLIGHT_WORK_QUEUE_SIZE (4)

/// Interval for polling
#define JLIGHT_POLL_INTERVAL K_MSEC(2500)
/// Interval for detecting laser
#define JLIGHT_DETECT_INTERVAL K_MSEC(5000)

/// Timeout for the "set current" work command
#define WORK_SET_CURRENT_TIMEOUT K_SECONDS(3)

/// Work item type
typedef enum {
  // Poll sensors via holding registers
  kJlightWorkPollTimerExpired,
  // Execute a MODBUS IO directly
  kJlightWorkPassthrough,
  // Program the laser peak current
  kJlightWorkSetCurrent,
  // Reset the laser communications and/or faults
  kJlightWorkReset,
} laser_jlight_work_type_t;

/**
 * @brief Information used to notify caller when command completes
 *
 * Callers should allocate one of these dudes on the stack: its address must NOT change for the
 * entire time the command is being executed.
 *
 * On return, it will be updated with the return code/results of the command.
 */
typedef struct laser_jlight_cmd_result {
  // stuff used to handle notifications
  struct {
    // Notified when command completes
    struct k_poll_signal signal;
  } _notify;

  // Return code of command
  int ret;
} laser_jlight_cmd_result_t;

// Payload for a work item sent to the worker thread
typedef struct laser_jlight_work {
  laser_jlight_work_type_t type;

  union {
    // TODO: passthrough command

    struct {
      // Channel currents, in mA; will round down to 0.1A increment. 0 = do not change
      uint32_t currents[2];
      // Commit to NVRAM after applying
      bool save;
    } setCurrent;

    // TODO: reset command
  };

  // command result pointer
  laser_jlight_cmd_result_t *result;
} laser_jlight_work_t;

// Structure holding the state for the Jlight laser handler
typedef struct laser_jlight_state {
  // worker thread
  struct {
    // Semaphore signalled whenever the laser is initialized
    struct k_sem initSem;

    // Number of poll cycles since last sensor/status query
    size_t pollCount;
  } task;

  // current laser state
  struct {
    struct k_mutex lock;
    // Timestamp of last successful poll
    struct net_ptp_time timestamp;
    // Whether laser is currently connected
    bool isConnected;
    // Status flags
    laser_jlight_flags_t flags;

    // Output channel voltages and currents
    float outCurrent[2], outVolts[2];
    // Temperature readings from NTCs
    float ntcTemps[2];
    // PSU temperature
    float psuTemp;
    // PSU input voltage
    float psuInputVolts;
  } state;
} laser_jlight_state_t;

static void jlight_task_main();
static int jlight_task_main_test_2();

static int submit_work(const laser_jlight_work_t *, const k_timeout_t);
static int submit_work_await(laser_jlight_work_t *, const k_timeout_t);
static int do_work(laser_jlight_work_t *, bool *);
static void handle_connection();
static void handle_disconnection();
static void poll_timer_tick(struct k_timer *);

static int jlight_poll_presence(bool *);
static int jlight_config_init();
static int jlight_poll_status();
static int jlight_set_current(const float *inCurrent, const size_t numInCurrentm, const bool save);

/// Jlight laser subsystem state
__dtcm_bss_section static laser_jlight_state_t gState;

// requests to the worker thread (incl. from periodic timer)
K_MSGQ_DEFINE(gJlightWorkQueue, sizeof(laser_jlight_work_t), JLIGHT_WORK_QUEUE_SIZE, 2);
// worker thread that services jlight communications and polling its state
K_THREAD_DEFINE(gJlightTask, 2048, jlight_task_main, NULL, NULL, NULL, K_PRIO_PREEMPT(9), 0, K_TICKS_FOREVER);
// timer used to trigger periodic polling
K_TIMER_DEFINE(gJlightPollTimer, poll_timer_tick, NULL);

/**
 * @brief Initialize the Jlight driver
 *
 * Set up the underlying Modbus transport and start the worker thread. It will then perform the
 * laser initialization as it becomes available.
 */
int laser_jlight_init() {
  int err;

  memset(&gState, 0, sizeof(gState));
  handle_disconnection();

  // turn on firing board power and configure the laser transport
  HANDLE_UNLIKELY(power_enable(kPowerFiringBoard));
  HANDLE_UNLIKELY(laser_jlight_transport_init());

  // start the worker: he will initialize laser, do handshaking; then wait for init complete
  LOG_INF("starting laser worker: %s", "Jlight");

  k_sem_init(&gState.task.initSem, 0, 1);

  k_thread_name_set(gJlightTask, "Laser.jlight Worker");
  k_thread_start(gJlightTask);

  // submit a poll request: start checking for laser connection
  poll_timer_tick(NULL);

  err = k_sem_take(&gState.task.initSem, JLIGHT_INIT_TIMEOUT);

  if (err == -EAGAIN) {
    LOG_WRN("laser init timeout; will continue to retry in background (machine probably estopped)");
  } else if (err) {
    LOG_ERR("%s failed: %d", "k_sem_take", err);
    return err;
  }

  return 0;
}

/**
 * @brief Jlight worker task
 */
static void jlight_task_main() {
  enum event_idx {
    kEventWorkQueue = 0,
  };

  int err;
  struct k_poll_event events[] = {
      K_POLL_EVENT_INITIALIZER(K_POLL_TYPE_MSGQ_DATA_AVAILABLE, K_POLL_MODE_NOTIFY_ONLY, &gJlightWorkQueue),
  };

  // main loop process events
  LOG_INF("JLight worker ready");

  do {
    err = k_poll(events, ARRAY_SIZE(events), K_FOREVER);
    if (err) {
      if (err != -EAGAIN) {
        LOG_WRN("%s failed: %d", "k_poll", err);
      }
      continue;
    }

    // TODO: proper locking on state

    // Work queue event
    if (events[kEventWorkQueue].state == K_POLL_STATE_MSGQ_DATA_AVAILABLE) {
      // drain da work queue
      do {
        bool newIsConnected = gState.state.isConnected;
        laser_jlight_work_t work;

        // get next work item; bail out of this loop if all has been slurped up
        err = k_msgq_get(&gJlightWorkQueue, &work, K_NO_WAIT);
        if (err) {
          if (err != -ENOMSG) {
            LOG_WRN("%s failed: %d", "k_msgq_get", err);
          }
          continue;
        }

        err = do_work(&work, &newIsConnected);
        if (err) {
          LOG_WRN("failed to process work item (type=%u): %d", work.type, err);
        }

        // handle laser state transitions
        if (gState.state.isConnected != newIsConnected) {
          LOG_INF("connection changed: %u -> %u", gState.state.isConnected, newIsConnected);

          // laser disconnected
          if (!newIsConnected) {
            handle_disconnection();
          }
          // laser connected
          else {
            handle_connection();
          }
          gState.state.isConnected = newIsConnected;
        }

        // if response struct specified, notify it
        if (work.result) {
          work.result->ret = err;
          __DSB();

          k_poll_signal_raise(&work.result->_notify.signal, 0);
        }
      } while (!err);

      events[kEventWorkQueue].state = K_POLL_STATE_NOT_READY;
    }
  } while (true);
}

/**
 * @brief Handle laser connecting
 */
static void handle_connection() {
  gState.state.flags = 0;
  // all other data should still be zeroed from disconnect
}

/**
 * @brief Clear all laser state on disconnection
 */
static void handle_disconnection() {
  gState.state.flags = 0;
  for (size_t i = 0; i < JLIGHT_NUM_OUT_CH; i++) {
    gState.state.outCurrent[i] = NAN;
    gState.state.outVolts[i] = NAN;
    gState.state.ntcTemps[i] = NAN;
  }
  gState.state.psuTemp = NAN;
  gState.state.psuInputVolts = NAN;
}

/**
 * @brief Enqueue work item to worker thread
 *
 * @param timeout Time to block waiting for queue to have space available
 */
static int submit_work(const laser_jlight_work_t *work, const k_timeout_t timeout) {
  if (!work) {
    return -EFAULT;
  }

  // initialize reply structure
  if (work->result) {
    work->result->ret = -1;
    k_poll_signal_init(&work->result->_notify.signal);
  }

  // then submit command
  return k_msgq_put(&gJlightWorkQueue, work, timeout);
}

/**
 * @brief Enqueue work item and wait for completion
 *
 * @param timeout How long to wait for command completion
 *
 * @return Command result code, or errors during submission
 */
static int submit_work_await(laser_jlight_work_t *work, const k_timeout_t timeout) {
  int err;

  if (!work) {
    return -EFAULT;
  }

  // prepare the result structure (iff not already specified)
  laser_jlight_cmd_result_t result;

  if (!work->result) {
    memset(&result, 0, sizeof(result));

    work->result = &result;
  }

  // timeout of 1 sec for task to make the work queue not full
  err = submit_work(work, K_SECONDS(1));
  if (err) {
    LOG_WRN("%s failed: %d", "submit_work", err);
    return err;
  }

  // now wait for the result
  struct k_poll_event events[1] = {
      K_POLL_EVENT_INITIALIZER(K_POLL_TYPE_SIGNAL, K_POLL_MODE_NOTIFY_ONLY, &work->result->_notify.signal)};

  err = k_poll(events, 1, timeout);
  if (err) {
    LOG_WRN("%s failed: %d", "k_poll", err);
    return err;
  }

  return work->result->ret;
}

/**
 * @brief Process work item
 */
static int do_work(laser_jlight_work_t *werk, bool *isConnected) {
  int err = 0;

  switch (werk->type) {
  /*
   * Status polling timer expired
   *
   * Read out laser state via the MODBUS interface; this will also detect whether the device has
   * gone away.
   */
  case kJlightWorkPollTimerExpired: {
    bool present = false;

    // poll sensors if connected
    if (*isConnected) {
      // then poll for it
      err = jlight_poll_status();

      // polling failure = disconnection
      if (err) {
        LOG_WRN("%s failed: %d", "jlight_poll_status", err);
        *isConnected = false;
      }
    }
    // if disconnected, check for presence and init config if so
    else {
      err = jlight_poll_presence(&present);

      if (err) {
        LOG_WRN("%s failed: %d", "jlight_poll_presence", err);
      }

      // if we've just become preesent, try to initialize laser
      if (present) {
        LOG_INF("laser presence check passed, attempting init");

        err = jlight_config_init();
        if (err) {
          LOG_WRN("%s failed: %d", "jlight_config_init", err);
        } else {
          *isConnected = true;
          k_sem_give(&gState.task.initSem);

          // initial sensor data poll
          k_sleep(K_MSEC(500));
          err = jlight_poll_status();
        }
      }
    }

    // restart timer with appropriate interval
    k_timer_start(&gJlightPollTimer, *isConnected ? JLIGHT_POLL_INTERVAL : JLIGHT_DETECT_INTERVAL, K_NO_WAIT);
    break;
  }

  /*
   * Set the laser peak currents for each of the channels
   */
  case kJlightWorkSetCurrent: {
    // convert to floats (A)
    float currents[2] = {0, 0};
    for (size_t i = 0; i < ARRAY_SIZE(werk->setCurrent.currents); i++) {
      currents[i] = ((float)werk->setCurrent.currents[i]) / 1000.f;
    }

    HANDLE_UNLIKELY(jlight_set_current(currents, ARRAY_SIZE(currents), werk->setCurrent.save));
    break;
  }

  // shouldn't get here
  default:
    LOG_WRN("unknown work type %d", werk->type);
    return -EINVAL;
  }

  return err;
}

/**
 * @brief Periodic polling timer expiration
 */
static void poll_timer_tick(struct k_timer *) {
  int err;
  laser_jlight_work_t work = {kJlightWorkPollTimerExpired};

  err = submit_work(&work, K_NO_WAIT);
  if (err && err != -ENOMSG) {
    LOG_WRN("%s failed: %d", "submit_work", err);
  }
}

/**
 * @brief Probe for Jlight laser presence
 *
 * This attempts to read one of the input hold registers. The value is discarded, but if the
 * operation succeeds we assume the laser is present. Timeouts are treated as laser absence, and
 * any other error is propagated.
 */
static int jlight_poll_presence(bool *isPresent) {
  int err;
  uint16_t temp = 0;

  err = laser_jlight_transport_read_input(kJlightReadHoldCh1LoadCurrent, &temp);
  if (!err) {
    *isPresent = true;
    return 0;
  } else if (err == -EAGAIN) {
    *isPresent = false;
    return 0;
  } else {
    return err;
  }
}

/**
 * @brief Perform laser initialization
 *
 * Applies the following settings:
 * - Use external trigger/enable control (via GPIO)
 */
static int jlight_config_init() {
  // enable both channels
  HANDLE_UNLIKELY(laser_jlight_transport_write_coil(kJlightWriteCoilCh1Enable, true));
  HANDLE_UNLIKELY(laser_jlight_transport_write_coil(kJlightWriteCoilCh2Enable, true));

  // set NTC1 limit to 65°C
  HANDLE_UNLIKELY(laser_jlight_transport_write_hold(kJlightWriteHoldNtc1UpperLimit, 650));

  return 0;
}

/**
 * @brief Reconfigure diode power supply output current
 *
 * @param save If set, currents are committed to nonvolatile memory
 */
static int jlight_set_current(const float *inCurrent, const size_t numInCurrent, const bool save) {
  if (!inCurrent) {
    return -EFAULT;
  } else if (numInCurrent > 2) {
    return -EINVAL;
  }
  // TODO: should bounds check current values

  // apply current values
  // TODO: clean up with a nice little loop
  if (numInCurrent >= 1) {
    const uint16_t temp = (inCurrent[0]) * 10.f;
    LOG_INF("set ch%u current = %u", 1, temp);
    HANDLE_UNLIKELY(laser_jlight_transport_write_hold(kJlightWriteHoldCh1PeakCurrent, temp));
  }
  if (numInCurrent >= 2) {
    const uint16_t temp = (inCurrent[1]) * 10.f;
    LOG_INF("set ch%u current = %u", 2, temp);
    HANDLE_UNLIKELY(laser_jlight_transport_write_hold(kJlightWriteHoldCh2PeakCurrent, temp));
  }

  // commit current values to nonvolatile memory
  if (save) {
    LOG_INF("Saving laser currents to nonvolatile memory!");
    HANDLE_UNLIKELY(laser_jlight_transport_write_coil(kJlightWriteCoilSaveOutputParams, true));
  }

  return 0;
}

/**
 * @brief Poll current laser status
 *
 * This reads out currents, voltages, and temperatures from the laser. Additionally fault info is
 * decoded.
 */
static int jlight_poll_status() {
  int err;
  uint32_t discretes = 0;
  uint16_t tempRegs[8];
  struct net_ptp_time now;

  // read laser status: discrete bits (flags) and all input hold registers at once
  HANDLE_UNLIKELY(laser_jlight_transport_read_discretes(&discretes));
  if (JLIGHT_LOG_POLL_VALUES) {
    LOG_INF("Discrete bits = %08x", discretes);
  }

  memset(tempRegs, 0, sizeof(tempRegs));
  HANDLE_UNLIKELY(
      laser_jlight_transport_read_input_multi(kJlightReadHoldCh1LoadCurrent, ARRAY_SIZE(tempRegs), tempRegs));

  HANDLE_UNLIKELY(ptp_slave_clk_get(&now));

  // store it all under lock
  // TODO: log when a value is preposterous/discarded as out of range?
  HANDLE_UNLIKELY(k_mutex_lock(&gState.state.lock, K_FOREVER));
  {
    float temp;

    // timestamp of readings
    gState.state.timestamp = now;

    // faults and overall status: convert to flag bits
    laser_jlight_flags_t flags = 0;

    if (discretes & BIT(kJlightReadDiscreteCh1Light)) {
      flags |= kJlightCh1Output;
    }
    if (discretes & BIT(kJlightReadDiscreteCh2Light)) {
      flags |= kJlightCh2Output;
    }
    if (discretes & BIT(kJlightReadDiscreteReady)) {
      flags |= kJlightReady;
    }
    if (discretes & BIT(kJlightReadDiscreteSystemAlarm)) {
      flags |= kJlightAlarmSystem;
    }
    if (discretes & (BIT(kJlightReadDiscreteCh1OvercurrentAlarm) | BIT(kJlightReadDiscreteCh2OvercurrentAlarm))) {
      flags |= kJlightAlarmOutputOvercurrent;
    }
    if (discretes & (BIT(kJlightReadDiscreteCh1OvervoltageAlarm) | BIT(kJlightReadDiscreteCh2OvervoltageAlarm))) {
      flags |= kJlightAlarmOutputOvervoltage;
    }
    if (discretes & (BIT(kJlightReadDiscreteNtc1OvertempAlarm) | BIT(kJlightReadDiscreteNtc2OvertempAlarm))) {
      flags |= kJlightAlarmOutputNtcTemp;
    }
    if (discretes & BIT(kJlightReadDiscreteDriverOvertempAlarm)) {
      flags |= kJlightAlarmOutputDiodeTemp;
    }
    if (discretes & 0xff000000) {
      flags |= kJlightInterlock;
    }

    if (flags & kJlightAlarmMask) {
      LOG_WRN("Alarms set: %08x (discretes %08x)", flags, discretes);
    }

    gState.state.flags = flags;

    // temperatures
    temp = ((float)tempRegs[kJlightReadHoldTempNtc1]) * 0.1;
    if (JLIGHT_LOG_POLL_VALUES) {
      LOG_INF("%s = %u", "ch1 temp", tempRegs[kJlightReadHoldTempNtc1]);
    }
    gState.state.ntcTemps[0] = (temp <= JLIGHT_MAX_TEMP_VALID) ? temp : NAN;

    temp = ((float)tempRegs[kJlightReadHoldTempNtc2]) * 0.1;
    if (JLIGHT_LOG_POLL_VALUES) {
      LOG_INF("%s = %u", "ch2 temp", tempRegs[kJlightReadHoldTempNtc2]);
    }
    gState.state.ntcTemps[1] = (temp <= JLIGHT_MAX_TEMP_VALID) ? temp : NAN;

    // laser chip voltages
    gState.state.outVolts[0] = ((float)tempRegs[kJlightReadHoldCh1LoadVoltage]) * 0.1;
    if (JLIGHT_LOG_POLL_VALUES) {
      LOG_INF("%s = %u", "ch1 volts", tempRegs[kJlightReadHoldCh1LoadVoltage]);
    }

    gState.state.outVolts[1] = ((float)tempRegs[kJlightReadHoldCh2LoadVoltage]) * 0.1;
    if (JLIGHT_LOG_POLL_VALUES) {
      LOG_INF("%s = %u", "ch2 volts", tempRegs[kJlightReadHoldCh2LoadVoltage]);
    }

    // laser chip currents
    gState.state.outCurrent[0] = ((float)tempRegs[kJlightReadHoldCh1LoadCurrent]) * 100.f;
    if (JLIGHT_LOG_POLL_VALUES) {
      LOG_INF("%s = %u", "ch1 current", tempRegs[kJlightReadHoldCh1LoadCurrent]);
    }

    gState.state.outCurrent[1] = ((float)tempRegs[kJlightReadHoldCh2LoadCurrent]) * 100.f;
    if (JLIGHT_LOG_POLL_VALUES) {
      LOG_INF("%s = %u", "ch2 current", tempRegs[kJlightReadHoldCh2LoadCurrent]);
    }

    // Laser PSU temp and input voltage
    gState.state.psuTemp = ((float)tempRegs[kJlightReadHoldTempPsu]) * 0.1;
    gState.state.psuInputVolts = ((float)tempRegs[kJlightReadHoldInputVoltage]) * 0.1;

    // fetching all data successful = scanner connected
    gState.state.isConnected = true;
  }
  k_mutex_unlock(&gState.state.lock);

  return 0;
}

/**
 * @brief Get laser temps, in °C
 */
int laser_jlight_get_temperature(float *outBuf, const size_t outBufSize) {
  size_t toCopy;

  if (!outBuf) {
    return -EFAULT;
  } else if (!outBufSize) {
    return -EINVAL;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.state.lock, K_FOREVER));
  {
    toCopy = MIN(outBufSize, JLIGHT_NUM_OUT_CH);
    memcpy(outBuf, gState.state.ntcTemps, toCopy * sizeof(*outBuf));
  }
  k_mutex_unlock(&gState.state.lock);

  return toCopy;
}

/**
 * @brief Get laser output currents, in mA
 */
int laser_jlight_get_output_current(float *outBuf, const size_t outBufSize) {
  size_t toCopy;

  if (!outBuf) {
    return -EFAULT;
  } else if (!outBufSize) {
    return -EINVAL;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.state.lock, K_FOREVER));
  {
    toCopy = MIN(outBufSize, JLIGHT_NUM_OUT_CH);
    memcpy(outBuf, gState.state.outCurrent, toCopy * sizeof(*outBuf));
  }
  k_mutex_unlock(&gState.state.lock);

  return toCopy;
}

/**
 * @brief Get laser supply output voltages
 */
int laser_jlight_get_output_voltage(float *outBuf, const size_t outBufSize) {
  size_t toCopy;

  if (!outBuf) {
    return -EFAULT;
  } else if (!outBufSize) {
    return -EINVAL;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.state.lock, K_FOREVER));
  {
    toCopy = MIN(outBufSize, JLIGHT_NUM_OUT_CH);
    memcpy(outBuf, gState.state.outVolts, toCopy * sizeof(*outBuf));
  }
  k_mutex_unlock(&gState.state.lock);

  return toCopy;
}

/**
 * @brief Get the power supply's input voltage
 */
int laser_jlight_get_psu_input_voltage(float *outVolts) {
  if (!outVolts) {
    return -EFAULT;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.state.lock, K_FOREVER));
  { *outVolts = gState.state.psuInputVolts; }
  k_mutex_unlock(&gState.state.lock);

  return 0;
}

/**
 * @brief Get power supply temperature, in °C
 */
int laser_jlight_get_psu_temperature(float *outTemp) {
  if (!outTemp) {
    return -EFAULT;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.state.lock, K_FOREVER));
  { *outTemp = gState.state.psuTemp; }
  k_mutex_unlock(&gState.state.lock);

  return 0;
}

/**
 * @brief Get current laser status flags
 */
int laser_jlight_get_status_bits(laser_jlight_flags_t *outStatus) {
  if (!outStatus) {
    return -EFAULT;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.state.lock, K_FOREVER));
  {
    if (gState.state.isConnected) {
      *outStatus = gState.state.flags | kJlightConnected;
    } else {
      *outStatus = 0;
    }
  }
  k_mutex_unlock(&gState.state.lock);

  return 0;
}

/**
 * @brief Send request to set laser current
 *
 * @param currentBuf Laser current values for each channel, in mA
 * @param numCurrents Number of current settings to set
 * @param save When set, currents written to nonvolatile memory on laser
 */
int laser_jlight_set_current(const uint32_t *currentBuf, const size_t numCurrents, const bool save) {
  int err;

  if (!currentBuf) {
    return -EFAULT;
  } else if (!numCurrents && !save) {
    return -EINVAL;
  } else if (numCurrents > JLIGHT_NUM_OUT_CH) {
    return -E2BIG;
  }

  // TODO: validate current values?

  // send worker message
  laser_jlight_work_t work = {kJlightWorkSetCurrent};
  for (size_t i = 0; i < MIN(ARRAY_SIZE(work.setCurrent.currents), numCurrents); i++) {
    // TODO: convert or validate current?
    work.setCurrent.currents[i] = currentBuf[i];
  }
  work.setCurrent.save = save;

  return submit_work_await(&work, WORK_SET_CURRENT_TIMEOUT);
}

/**
 * @brief Set Jlight laser intensity
 *
 * JLight doesn't support intensity setting, so commands are ignored; Reaper currently always sends
 * 100% intensity (for normal weeding use) so this is fine for now.
 */
int laser_jlight_set_intensity(const int newIntensity) {
  // log if aimbot ever sends not 100% intensity
  if (newIntensity != 1000) {
    LOG_WRN("%s(%d) not implemented", __FUNCTION__, newIntensity);
  }
  return 0;
}

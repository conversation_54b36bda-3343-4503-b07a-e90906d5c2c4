#define LASER_PRIVATE 1

#include <zephyr/device.h>
#include <zephyr/kernel.h>
#include <zephyr/logging/log.h>

LOG_MODULE_DECLARE(laser, CONFIG_APP_LOG_LEVEL);

#include <drivers/laser_power_meter.h>
#include <utils/adc.h>
#include <utils/handle_errors.h>

#include "../eeprom_new.h"
#include "../laser.h"
#include "bwt.h"
#include "bwt_transport.h"
#include "internal.h"
#include "jlight.h"
#include "reaper.h"

/**
 * @brief Initialize Reaper laser subsystem
 *
 * This sets up the UART transport to the laser.
 */
int laser_reaper_init() {
  uint32_t temp = 0;
  int err;

  // read laser type from EEPROM
  err = eeprom_load_laser_type(&temp);
  if (err) {
    LOG_WRN("failed to get laser type from EEPROM (%d); assuming BWT!", err);
    gLaserState.laserType = kLaserTypeDiodeBWT;
  } else {
    if (temp == kLaserTypeDiodeBWT || temp == kLaserTypeDiodeJlight) {
      gLaserState.laserType = temp;
    } else {
      LOG_WRN("invalid laser type (%u); assuming BWT!", temp);
      gLaserState.laserType = kLaserTypeDiodeBWT;
    }
  }

  // TODO: implement laser type autodetection here
  LOG_INF("Reaper laser type: %s", laser_get_type_name(gLaserState.laserType));

  switch (gLaserState.laserType) {
  case kLaserTypeDiodeBWT:
    HANDLE_UNLIKELY(laser_bwt_init());
#if IS_ENABLED(CONFIG_BWT_UART_FIRING)
    LOG_WRN("BWT firing via UART!");
#endif
    break;

  case kLaserTypeDiodeJlight:
    HANDLE_UNLIKELY(laser_jlight_init());
    break;

  default:
    LOG_WRN("unsupported laser type %u", gLaserState.laserType);
    return -EINVAL;
  }

  return 0;
}

/**
 * @brief Get raw ADC readings for laser power meter photodiode
 */
int laser_get_raw_photodiode(uint32_t *reading) {
  if (!reading) {
    return -EFAULT;
  } else if (!gLaserConfig.powerMeter) {
    return -ENXIO;
  }

  return laser_power_meter_get_raw_photodiode(gLaserConfig.powerMeter, reading);
}

/**
 * @brief Read out Reaper thermistor values
 *
 * Convert the thermistor values to a temperature, in °C. This is for use on the Reaper scanner
 * where thermistors are attached to the laser collimators.
 *
 * These thermistors are TEWA TT05-10KC8-1S-T105-1500:
 *
 * - 10kΩ ±1% @ 25°C
 * - β = 3435K ±1% @ 25°C
 */
int laser_get_reaper_therm(float *therm1, float *therm2) {
  int err;
  if (!therm1 && !therm2) {
    return -EFAULT;
  } else if (!gLaserConfig.powerMeter) {
    return -ENXIO;
  }

  // capture raw ADC voltage
  int32_t raw[2];

  err = laser_power_meter_get_raw_readings(gLaserConfig.powerMeter, &raw[0], &raw[1]);
  if (err) {
    return err;
  }

  // convert each to a temperature in °C
  float temps[2];

  for (size_t i = 0; i < 2; i++) {
    const double ohms = get_divider_ohms(10000, raw[i], 3300);
    temps[i] = get_thermistor_temp(25, 10000, 3435, ohms);
  }

  if (therm1) {
    *therm1 = temps[0];
  }
  if (therm2) {
    *therm2 = temps[1];
  }

  return 0;
}

/**
 * @brief Set laser output intensity
 *
 * This will send a configuration command via the serial interface.
 */
int laser_reaper_set_intensity(const uint32_t newIntensity) {
  switch (gLaserState.laserType) {
  case kLaserTypeDiodeBWT:
    HANDLE_UNLIKELY(laser_bwt_set_intensity(newIntensity));
    break;

  case kLaserTypeDiodeJlight:
    HANDLE_UNLIKELY(laser_jlight_set_intensity(newIntensity));
    break;

  default:
    LOG_WRN("unsupported laser type %u", gLaserState.laserType);
    return -EINVAL;
  }

  return 0;
}

/**
 * @brief Update laser type config
 *
 * This is stored in EEPROM and will take effect at the next boot.
 */
int laser_reaper_set_type(const laser_type_t newType) {
  // validate input
  if (newType != kLaserTypeDiodeJlight && newType != kLaserTypeDiodeBWT) {
    return -EINVAL;
  }

  LOG_INF("Writing laser type: %s (%u)", laser_get_type_name(newType), newType);
  HANDLE_UNLIKELY(eeprom_save_laser_type(newType));

  return 0;
}

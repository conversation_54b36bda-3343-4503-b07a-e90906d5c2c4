#include <zephyr/logging/log.h>
LOG_MODULE_DECLARE(laser_bwt);

#include <utils/handle_errors.h>

#include "../laser.h"
#include "bwt.h"
#include "bwt_transport.h"

/**
 * @brief Fill nanopb BWT inventory struct
 */
int laser_bwt_fill_nanopb_inventory(laser_Laser_Inventory_Reply *outInventory) {
  int err;

  // serial number
  err = laser_bwt_get_serial(outInventory->serial, sizeof(outInventory->serial));
  if (err < 0) {
    LOG_WRN("%s failed: %d", "laser_bwt_get_serial", err);
  }

  // driver model
  err = laser_bwt_get_model(outInventory->model, sizeof(outInventory->model));
  if (err < 0) {
    LOG_WRN("%s failed: %d", "laser_bwt_get_model", err);
  }

  // rated power
  int temp;
  err = laser_bwt_get_rated_power(&temp);
  if (!err) {
    outInventory->power = temp;
  } else {
    LOG_WRN("%s failed: %d", "laser_bwt_get_inventory", err);
  }

  return 0;
}

/**
 * @brief Fill nanopb BWT status struct
 */
int laser_bwt_fill_nanopb_status(laser_Diode_Status_Reply *outStatus) {
  int err;
  laser_bwt_flags_t bwtState = 0;

  // status flags
  HANDLE_UNLIKELY(laser_bwt_get_status_bits(&bwtState));

  if (!(bwtState & kBwtConnected)) {
    return -ENXIO;
  }

  // set up the message for BWT aux data
  outStatus->which_extra = laser_Diode_Status_Reply_extra_bwt_tag;

  // BWT reported temperatures
  const size_t kProtoNumTemps = ARRAY_SIZE(outStatus->temp);
  err = laser_bwt_get_temperature(outStatus->temp, kProtoNumTemps);

  if (err >= 0) {
    outStatus->temp_count = MIN(err, kProtoNumTemps);
  } else {
    LOG_WRN("%s failed: %d", "laser_bwt_get_temps", err);
  }

  // BWT reported humidity
  const size_t kProtoNumHumidity = ARRAY_SIZE(outStatus->humidity);
  err = laser_bwt_get_humidity(outStatus->humidity, kProtoNumHumidity);

  if (err >= 0) {
    outStatus->humidity_count = MIN(err, kProtoNumHumidity);
  } else {
    LOG_WRN("%s failed: %d", "laser_bwt_get_humidity", err);
  }

  // BWT reported diode current
  const size_t kProtoNumCurrent = ARRAY_SIZE(outStatus->current);
  err = laser_bwt_get_output_current(outStatus->current, kProtoNumCurrent);

  if (err >= 0) {
    outStatus->current_count = MIN(err, kProtoNumCurrent);
  } else {
    LOG_WRN("%s failed: %d", "laser_bwt_get_output_current", err);
  }

  // get fault list and related flags
  const size_t kProtoNumFaults = ARRAY_SIZE(outStatus->extra.extra_bwt.faults);
  err = laser_bwt_get_faults(outStatus->extra.extra_bwt.faults, kProtoNumFaults);

  if (err >= 0) {
    outStatus->extra.extra_bwt.faults_count = MIN(err, kProtoNumFaults);
  } else {
    LOG_WRN("%s failed: %d", "laser_bwt_get_faults", err);
  }

  outStatus->faulted = bwtState & kBwtFaulted;

  return 0;
}

/**
 * @brief Execute a BWT passthrough command
 *
 * Forward the specified command to the BWT laser, then return its response.
 */
int laser_bwt_fill_nanopb_passthrough(const laser_Bwt_Passthrough_Request *request,
                                      laser_Bwt_Passthrough_Reply *outStatus) {
  int err;

  LOG_INF("Sending BWT command: `%s`", request->command);

  // TODO: allow specifying timeout via the nanopb request?
  err = laser_bwt_run_passthrough(request->command, outStatus->response, sizeof(outStatus->response), K_SECONDS(1));
  if (err < 0) {
    LOG_WRN("%s(%s) failed: %d", "laser_bwt_run_passthrough", request->command, err);
    return err;
  } else {
    LOG_INF("Got BWT reply: `%s`", outStatus->response);
  }

  return 0;
}

/**
 * @brief Get the current BWT transport configuration
 */
int laser_bwt_fill_nanopb_transport_config(laser_Bwt_Transport_Config *outConfig) {
  bwt_debug_flags_t flags = 0;
  HANDLE_UNLIKELY(laser_bwt_transport_get_debug_flags(&flags));

  outConfig->log_messages = !!(flags & (kBwtDebugFlagLogRx | kBwtDebugFlagLogTx));
  outConfig->intercommand_delay = !!(flags & kBwtDebugFlagAddCommandDelay);

  return 0;
}

/**
 * @file
 * @brief Laser related type definitions
 */
#pragma once

#include <stddef.h>
#include <stdint.h>

/**
 * @brief Supported laser types
 *
 * This is primarily used on Reaper to differentiate the connected type of diode laser, as on
 * Slayer the lasers are really dumb and don't need to be spoken to in a very particular way.
 */
typedef enum laser_type {
  /// Default/unknown type: requires configuration
  kLaserTypeUnknown = 0,
  /// Generic Slayer (CO2) laser
  kLaserTypeCO2Generic = 1,
  /// BWT laser: Custom protocol via RS232
  kLaserTypeDiodeBWT = 2,
  /// Diode laser (Jlight): Modbus via RS232, default address 0x01
  kLaserTypeDiodeJlight = 3,
} laser_type_t;

/**
 * @brief Laser arc detector configuration
 *
 * In essence, the arc detector counts the number of times that the LPSU output current is either
 * above or below a configurable threshold. If we exceed a certain number of such events in a
 * given time period, an alarm is raised and laser firing is inhibited.
 *
 * Sampling of LPSU currents takes place via a periodic timer: the delay between the laser firing
 * and the first sample (used to ignore current variations until the laser ignites) as well as
 * the period for subsequent samples are configurable.
 *
 * @seeAlso laser_set_arc_detector_config
 */
typedef struct laser_arc_detector_config {
  /**
   * @brief Configure whether arc detector is enabled
   *
   * When enabled, it can inhibit laser firing and raise an alarm when enough arc events are
   * detected. Disabling the arc detector will also reset any alarms and re-enable laser firing
   * if the arc detector was the reason why it was locked out.
   */
  bool enabled;

  /**
   * @brief LPSU high current threshold (amps)
   *
   * Laser current readings at or above this value are considered as a potential arc event.
   */
  float highCurrentLimit;

  /**
   * @brief LPSU low current threshold (amps)
   *
   * Laser current readings that consistently stay below this threshold for the duration of
   * the laser firing are interpreted as an arc.
   */
  float lowCurrentLimit;

  /**
   * @brief Arc alarm period (seconds)
   *
   * This is the time period during which `alarmThreshold` arc events must be detected to
   * raise an arc alarm. Any events that are older than this are discarded.
   *
   * @seeAlso alarmThreshold
   */
  uint32_t alarmPeriod;

  /**
   * @brief Number of arc events before alarm is triggered
   *
   * This is the number of times the arc detector needs to activate before an alarm is raised
   * and firing is inhibited.
   *
   * @seeAlso alarmPeriod
   */
  uint16_t alarmThreshold;

  /**
   * @brief Delay between laser trigger and first current measurement (msec)
   *
   * Defines the amount of time to wait after the laser is fired before the arc detector timer
   * takes the first reading.
   */
  uint16_t initialDelay;

  /**
   * @brief Delay between subsequent current measurements (msec)
   *
   * After the initial current measurement has been taken, the arc detector continues to sample
   * laser current at the specified interval. This continues until the laser is no longer
   * firing.
   */
  uint16_t sampleInterval;
} laser_arc_detector_config_t;

/**
 * @brief Laser arc detector status
 */
typedef struct laser_arc_detector_status {
  /// Set when the alarm conditions are met; locks out laser firing
  bool alarm;

  /// Lowest current value detected since last reset
  float minCurrent;
  /// Maximum current value detected since last reset
  float maxCurrent;
} laser_arc_detector_status_t;

/**
 * @brief Get string display name for laser type enum value
 */
static inline const char *laser_get_type_name(const laser_type_t type) {
  switch (type) {
  case kLaserTypeCO2Generic:
    return "CO2 (Generic)";
  case kLaserTypeDiodeBWT:
    return "Diode (BWT)";
  case kLaserTypeDiodeJlight:
    return "Diode (Jlight)";

  default:
    return "Unknown";
  }
}

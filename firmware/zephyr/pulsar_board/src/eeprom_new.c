#include <math.h>
#include <string.h>
#include <zephyr/device.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/eeprom.h>
#include <zephyr/logging/log.h>
#include <zephyr/sys/crc.h>

#include <utils/handle_errors.h>

#include "eeprom.h"
#include "eeprom_new.h"

LOG_MODULE_REGISTER(eeprom, CONFIG_APP_LOG_LEVEL);

// Magic value for EEPROM data structure
#define EEPROM_DATA_MAGIC (0xbadc0ffe)
// Byte offset in EEPROM to store data at
#define EEPROM_DATA_OFFSET (0)
// Current EEPROM data structure version
#define EEPROM_DATA_VERSION (0x0010)

/**
 * @brief Config data structure
 *
 * This is stored in the EEPROM and read on boot, and written back whenever the data is changed.
 *
 * All multi-byte values are stored in native byte order. Strings are NULL-padded up to their
 * maximum length;
 */
typedef struct {
  // Magic value (for checking existence)
  uint32_t magic;
  // Version (used for data migration in the future)
  uint16_t version;
  // Length of structure
  uint16_t length;

  // Camera color calibration (no longer used?)
  union {
    float raw[3];
    struct {
      float red;
      float green;
      float blue;
    };
  } color;

  // Positioning skews (per axis)
  union {
    float raw[2];
    struct {
      float pan;
      float tilt;
    };
  } skews;

  // Camera serial
  char cameraSn[CAMERA_SN_LENGTH];
  // Scanner assembly serial
  char assemblySn[SCANNER_ASSY_SN_LENGTH];
  // DO NOT REMOVE: assembly SN used to be 24 bytes, this space is required
  uint8_t reserved1[8];

  // Motor type (HW_REV_MAXON_SERVOS or HW_REV_FH_SERVOS)
  uint16_t motorType;

  // BWT transport debug flags
  uint32_t bwtDebugFlags;
  // What type of laser is connected (see `laser_type_t`)
  uint32_t laserType;

  // reserved for future expansion
  uint8_t reserved2[32];

  // CRC16 over all previous data
  uint16_t crc;
} __attribute__((packed)) eeprom_data_t;
BUILD_ASSERT(sizeof(eeprom_data_t) == 128, "EEPROM data size incorrect");

typedef struct {
  struct k_mutex lock;

  // Set if the loaded EEPROM data is valid
  bool dataValid;
  // Data loaded from EEPROM (in-memory cache)
  eeprom_data_t data;
} eeprom_state_t;

static void apply_defaults();
static int read_data(eeprom_data_t *);
static int write_data(eeprom_data_t *);
static int validate_data(const eeprom_data_t *);

// Device that implements the EEPROM nonvolatile storage API
static const struct device *gStorage = DEVICE_DT_GET(DT_ALIAS(eeprom0));
// Overall EEPROM manager state
__dtcm_bss_section static eeprom_state_t gState;

/**
 * @brief Initialize nonvolatile storage driver
 *
 * Attempt to read out the current data in flash and validate it.
 */
int eeprom_init() {
  int err;

  // set up hardware
  HANDLE_UNLIKELY_BOOL(device_is_ready(gStorage), ENODEV);

  k_mutex_init(&gState.lock);

  // try to read out data from EEPROM
  eeprom_data_t data;
  HANDLE_CRITICAL(read_data(&data));
  LOG_HEXDUMP_DBG(&data, sizeof(data), "read data");

  err = validate_data(&data);
  if (!err) {
    LOG_INF("EEPROM data valid");

    gState.data = data;
    gState.dataValid = true;
  }
  // failed to validate EEPROM data; restore defaults if not programmed
  else {
    LOG_WRN("%s failed: %d", "validate_data", err);

    // if unprogrammed (all 1 bits) restore defaults
    if (data.magic == 0xffffffff) {
      LOG_WRN("EEPROM unprogrammed - committing defaults at next write");

      apply_defaults();
      gState.dataValid = true;
    }
    // otherwise, the EEPROM is corrupt :(
    else {
      LOG_HEXDUMP_ERR(&data, sizeof(data), "invalid EEPROM data");
      return -EINVAL;
    }
  }

  return 0;
}

/**
 * @brief Save camera color calibration coefficients
 */
int eeprom_save_color_cal(float red, float green, float blue) {
  int err;

  k_mutex_lock(&gState.lock, K_FOREVER);
  {
    gState.data.color.red = red;
    gState.data.color.green = green;
    gState.data.color.blue = blue;

    // write to EEPROM
    gState.dataValid = true;

    err = write_data(&gState.data);
    if (err) {
      LOG_WRN("%s failed: %d", "write_data", err);
    }
  }
  k_mutex_unlock(&gState.lock);

  return err;
}

/**
 * @brief Get camera color calibration coefficients
 */
int eeprom_load_color_cal(float *red, float *green, float *blue) {
  k_mutex_lock(&gState.lock, K_FOREVER);
  {
    if (gState.dataValid) {
      *red = gState.data.color.red;
      *green = gState.data.color.green;
      *blue = gState.data.color.blue;
    } else {
      *red = NAN;
      *green = NAN;
      *blue = NAN;
    }
  }
  k_mutex_unlock(&gState.lock);

  return 0;
}

/**
 * @brief Set camera skew coefficients
 */
int eeprom_save_skew_cal(float pan_skew, float tilt_skew) {
  int err;

  k_mutex_lock(&gState.lock, K_FOREVER);
  {
    gState.data.skews.pan = pan_skew;
    gState.data.skews.tilt = tilt_skew;

    // write to EEPROM
    gState.dataValid = true;

    err = write_data(&gState.data);
    if (err) {
      LOG_WRN("%s failed: %d", "write_data", err);
    }
  }
  k_mutex_unlock(&gState.lock);

  return err;
}

/**
 * @brief Get skew compensation settings
 */
int eeprom_load_skew_cal(float *pan_skew, float *tilt_skew) {
  k_mutex_lock(&gState.lock, K_FOREVER);
  {
    if (gState.dataValid) {
      *pan_skew = gState.data.skews.pan;
      *tilt_skew = gState.data.skews.tilt;
    } else {
      *pan_skew = 0.f;
      *tilt_skew = 0.f;
    }
  }
  k_mutex_unlock(&gState.lock);

  return 0;
}

/**
 * @brief Set target cam serial number
 */
int eeprom_save_camera_sn(const char *serial_number) {
  int err;

  k_mutex_lock(&gState.lock, K_FOREVER);
  {
    strncpy(gState.data.cameraSn, serial_number, CAMERA_SN_LENGTH);

    // write to EEPROM
    gState.dataValid = true;

    err = write_data(&gState.data);
    if (err) {
      LOG_WRN("%s failed: %d", "write_data", err);
    }
  }
  k_mutex_unlock(&gState.lock);

  return err;
}

/**
 * @brief Get target cam serial number
 */
int eeprom_load_camera_sn(char *serial_number) {
  k_mutex_lock(&gState.lock, K_FOREVER);
  {
    if (gState.dataValid) {
      strncpy(serial_number, gState.data.cameraSn, CAMERA_SN_LENGTH);
    } else {
      memset(serial_number, 0, CAMERA_SN_LENGTH);
    }
  }
  k_mutex_unlock(&gState.lock);

  return 0;
}

/**
 * @brief Set scanner assembly serial number
 */
int eeprom_save_scanner_sn(const char *serial_number) {
  int err;

  k_mutex_lock(&gState.lock, K_FOREVER);
  {
    strncpy(gState.data.assemblySn, serial_number, SCANNER_ASSY_SN_LENGTH);

    // write to EEPROM
    gState.dataValid = true;

    err = write_data(&gState.data);
    if (err) {
      LOG_WRN("%s failed: %d", "write_data", err);
    }
  }
  k_mutex_unlock(&gState.lock);

  return err;
}

/**
 * @brief Get scanner assembly serial number
 */
int eeprom_load_scanner_sn(char *serial_number) {
  k_mutex_lock(&gState.lock, K_FOREVER);
  {
    if (gState.dataValid) {
      strncpy(serial_number, gState.data.assemblySn, SCANNER_ASSY_SN_LENGTH);
    } else {
      memset(serial_number, 0, SCANNER_ASSY_SN_LENGTH);
    }
  }
  k_mutex_unlock(&gState.lock);

  return 0;
}

/**
 * @brief Set motor type
 *
 * This is supported only for Slayer scanners
 */
int eeprom_save_hw_rev(uint32_t *_rev) {
#if USE_REAPER
  return -ENOTSUP;
#endif

  if (!_rev) {
    return -EFAULT;
  }

  const uint32_t rev = *_rev;
  if (rev != HW_REV_FH_SERVOS && rev != HW_REV_MAXON_SERVOS) {
    LOG_WRN("%s: invalid motor type %u", __FUNCTION__, rev);
    return -EINVAL;
  }

  int err;

  k_mutex_lock(&gState.lock, K_FOREVER);
  {
    gState.data.motorType = rev;

    // write to EEPROM
    gState.dataValid = true;

    err = write_data(&gState.data);
    if (err) {
      LOG_WRN("%s failed: %d", "write_data", err);
    }
  }
  k_mutex_unlock(&gState.lock);

  return err;
}

/**
 * @brief Get motor type
 *
 * For Reaper this is fixed at Faulhaber
 */
int eeprom_load_hw_rev(uint32_t *rev) {
#if USE_REAPER
  *rev = HW_REV_FH_SERVOS;
  return 0;
#endif

  k_mutex_lock(&gState.lock, K_FOREVER);
  {
    if (gState.dataValid) {
      *rev = gState.data.motorType;
    } else {
      *rev = HW_REV_FH_SERVOS;
    }
  }
  k_mutex_unlock(&gState.lock);

  return 0;
}

int eeprom_save_pid(uint8_t node_id, struct pid_config_s *pid) {
  // TODO: implement this

  return -ENOTSUP;
}
int eeprom_load_pid(uint8_t node_id, struct pid_config_s *pid) {
  // TODO: implement this

  return -ENOTSUP;
}

/**
 * @brief Save the specified BWT debug flags
 */
int eeprom_save_bwt_debug_flags(const uint32_t flags) {
#if !USE_REAPER
  return -ENOTSUP;
#endif

  int err;

  k_mutex_lock(&gState.lock, K_FOREVER);
  {
    gState.data.bwtDebugFlags = flags;

    // write to EEPROM
    gState.dataValid = true;

    err = write_data(&gState.data);
    if (err) {
      LOG_WRN("%s failed: %d", "write_data", err);
    }
  }
  k_mutex_unlock(&gState.lock);

  return err;
}

/**
 * @brief Load the BWT debug flags
 *
 * @remark If no flags are set, the output variable remains unchanged
 */
int eeprom_load_bwt_debug_flags(uint32_t *flags) {
#if !USE_REAPER
  return -ENOTSUP;
#endif

  k_mutex_lock(&gState.lock, K_FOREVER);
  {
    if (gState.dataValid) {
      *flags = gState.data.bwtDebugFlags;
    }
  }
  k_mutex_unlock(&gState.lock);

  return 0;
}

/**
 * @brief Save the connected laser type
 */
int eeprom_save_laser_type(const uint32_t type) {
#if !USE_REAPER
  return -ENOTSUP;
#endif

  int err;

  k_mutex_lock(&gState.lock, K_FOREVER);
  {
    gState.data.laserType = type;

    // write to EEPROM
    gState.dataValid = true;

    err = write_data(&gState.data);
    if (err) {
      LOG_WRN("%s failed: %d", "write_data", err);
    }
  }
  k_mutex_unlock(&gState.lock);

  return err;
}

/**
 * @brief Load the BWT debug flags
 *
 * @remark If no flags are set, the output variable remains unchanged
 */
int eeprom_load_laser_type(uint32_t *outType) {
#if !USE_REAPER
  return -ENOTSUP;
#endif

  k_mutex_lock(&gState.lock, K_FOREVER);
  {
    if (gState.dataValid) {
      *outType = gState.data.laserType;
    }
  }
  k_mutex_unlock(&gState.lock);

  return 0;
}
/**
 * @brief Erase EEPROM config/calibration data
 *
 * This is used by validation to erase all calibration data: color/skew calibrations as well as
 * motor configurations. Scanner assembly and camera serial numbers will not be reset.
 */
int eeprom_erase_config() {
  int err;

  k_mutex_lock(&gState.lock, K_FOREVER);
  {
    if (!gState.dataValid) {
      LOG_INF("EEPROM data invalid, nothing to erase");
      err = 0;
      goto beach;
    }

    // clear DT skews
    for (size_t i = 0; i < ARRAY_SIZE(gState.data.skews.raw); i++) {
      gState.data.skews.raw[i] = 0.f;
    }

    // clear color calibration
    for (size_t i = 0; i < ARRAY_SIZE(gState.data.color.raw); i++) {
      gState.data.color.raw[i] = NAN;
    }

    // reset other configs
    gState.data.motorType = 0;
    gState.data.bwtDebugFlags = 0;
    gState.data.laserType = 0;

    // write to EEPROM
    gState.dataValid = true;

    err = write_data(&gState.data);
    if (err) {
      LOG_WRN("%s failed: %d", "write_data", err);
    }
  }
beach:;
  k_mutex_unlock(&gState.lock);

  return err;
}

/**
 * @brief Apply the default settings to the in-memory shadow of config data
 */
static void apply_defaults() {
  memset(&gState.data, 0, sizeof(gState.data));

  gState.data.motorType = HW_REV_FH_SERVOS;
}

/**
 * @brief Read data structure from EEPROM
 *
 * This does NOT validate that it is valid; see `validate_data()` for this
 */
static int read_data(eeprom_data_t *data) { return eeprom_read(gStorage, EEPROM_DATA_OFFSET, data, sizeof(*data)); }

/**
 * @brief Write EEPROM data
 *
 * Update the header structure and checksums before writing the data to nonvolatile storage.
 */
static int write_data(eeprom_data_t *data) {
  int err;

  // perform setup
  data->magic = EEPROM_DATA_MAGIC;
  data->version = EEPROM_DATA_VERSION;
  data->length = sizeof(*data);

  data->crc = crc16_ccitt(0, (const uint8_t *)data, offsetof(eeprom_data_t, crc));

  // validate the data: this MUST pass
  if ((err = validate_data(data))) {
    LOG_WRN("pre-write data failed validation: %d", err);
    return err;
  }

  // write it
  return eeprom_write(gStorage, EEPROM_DATA_OFFSET, data, sizeof(*data));
}

/**
 * @brief Validate EEPROM data structure
 *
 * Ensure the magic value, size, and version are sane; as well as that the stored checksum matches
 * what's expected.
 */
static int validate_data(const eeprom_data_t *data) {
  if (data->magic != EEPROM_DATA_MAGIC) {
    LOG_WRN("invalid %s (expected %08x, got %08x)", "magic", EEPROM_DATA_MAGIC, data->magic);
    return -EINVAL;
  }
  if (data->version < EEPROM_DATA_VERSION) {
    LOG_WRN("invalid %s (expected %04x, got %04x)", "version", EEPROM_DATA_VERSION, data->version);
    return -EINVAL;
  }
  if (data->length < sizeof(*data)) {
    LOG_WRN("invalid %s (expected %u, got %u)", "length", sizeof(*data), data->length);
    return -EINVAL;
  }

  const uint16_t expected = crc16_ccitt(0, (const uint8_t *)data, offsetof(eeprom_data_t, crc));
  if (expected != data->crc) {
    LOG_WRN("invalid %s (expected %04x, got %04x)", "checksum", expected, data->crc);
    return -EINVAL;
  }

  return 0;
}

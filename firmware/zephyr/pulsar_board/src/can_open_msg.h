#pragma once

#include <stdint.h>

// CAN Open Broadcast Function Codes
#define CAN_OPEN_BROADCAST_FUNC_CODE_NMT 0b0000
#define CAN_OPEN_BROADCAST_FUNC_CODE_SYNC 0b0001
#define CAN_OPEN_BROADCAST_FUNC_CODE_TIME_STAMP 0b0010

// CAN Open Function Codes
#define CAN_OPEN_FUNC_CODE_EMERGENCY 0b0001
#define CAN_OPEN_FUNC_CODE_PDO1_TX 0b0011
#define CAN_OPEN_FUNC_CODE_PDO1_RX 0b0100
#define CAN_OPEN_FUNC_CODE_PDO2_TX 0b0101
#define CAN_OPEN_FUNC_CODE_PDO2_RX 0b0110
#define CAN_OPEN_FUNC_CODE_PDO3_TX 0b0111
#define CAN_OPEN_FUNC_CODE_PDO3_RX 0b1000
#define CAN_OPEN_FUNC_CODE_PDO4_TX 0b1001
#define CAN_OPEN_FUNC_CODE_PDO4_RX 0b1010
#define CAN_OPEN_FUNC_CODE_SDO_TX 0b1011
#define CAN_OPEN_FUNC_CODE_SDO_RX 0b1100
#define CAN_OPEN_FUNC_CODE_NMT_HB 0b1110
#define CAN_OPEN_FUNC_CODE_LSS 0b1111

// CAN Open Command Specifier
#define CAN_OPEN_CS_SDO_DL_REQUEST 1
#define CAN_OPEN_CS_SDO_UL 2
#define CAN_OPEN_CS_SDO_DL_RESPONSE 3
#define CAN_OPEN_CS_SDO_ABORT 4

#define CAN_OPEN_SDO_SPEC_CS(spec) (((spec)&0b11100000) >> 5)      // Command Specifier
#define CAN_OPEN_SDO_SPEC_XLOAD_T(spec) (((spec)&0b00010000) >> 4) // Toggle bit
#define CAN_OPEN_SDO_SPEC_XLOAD_N(spec) (((spec)&0b00001100) >> 2) //
#define CAN_OPEN_SDO_SPEC_XLOAD_E(spec) (((spec)&0b00000010) >> 1) // Expedited Transfer
#define CAN_OPEN_SDO_SPEC_XLOAD_S(spec) ((spec)&0b00000001)        // Size Indicator

#define CAN_OPEN_SDO_SPEC_XLOAD_CONSTRUCT(cs, t, n, e, s)                                                              \
  ((s & 0b00000001) | ((e << 1) & 0b00000010) | ((n << 2) & 0b00001100) | ((t << 4) & 0b00010000) |                    \
   ((cs << 5) & 0b11100000))

#define CAN_OPEN_DEFAULT_TIMEOUT_MS 500
#define CAN_OPEN_DEFAULT_PDO_RTR_TIMEOUT_MS 5

// COB-ID
#define CAN_OPEN_COB_ID_FUNC(id) (id >> 7)
#define CAN_OPEN_COB_ID_NODE_ID(id) (id & 0x7F)
#define CAN_OPEN_COB_ID_CONSTRUCT(node_id, func) (((node_id)&0x7F) | (((func)&0xF) << 7))

// NMT Commands
#define CAN_OPEN_NMT_ENTER_OPERATIONAL 0x01
#define CAN_OPEN_NMT_ENTER_STOP 0x02
#define CAN_OPEN_NMT_ENTER_PRE_OPERATIONAL 0x80
#define CAN_OPEN_NMT_RESET_NODE 0x81
#define CAN_OPEN_NMT_RESET_COMMUNICATION 0x82

// NMT State
#define CAN_OPEN_NMT_BOOT_STATE = 0x00
#define CAN_OPEN_NMT_STOPPED_STATE = 0x04
#define CAN_OPEN_NMT_OPERATIONAL_STATE = 0x05
#define CAN_OPEN_NMT_PRE_OPERATIONAL_STATE = 0x7f

#define CAN_OPEN_SDO_PKT_DATA_SIZE 4
#define CAN_OPEN_PDO_PKT_DATA_SIZE 8
#define CAN_OPEN_LSS_PKT_DATA_SIZE 7

// LSS
#define LSS_MASTER_ID (0x7E5)
#define LSS_SLAVE_ID (0x7E4)
#define LSS_SWITCH_STATE_GLOBAL_CS (0x04)
#define LSS_SWITCH_STATE_SELECTIVE_VENDOR_CS (0x40)
#define LSS_SWITCH_STATE_SELECTIVE_PRODUCT_CS (0x41)
#define LSS_SWITCH_STATE_SELECTIVE_REVISION_CS (0x42)
#define LSS_SWITCH_STATE_SELECTIVE_SERIAL_CS (0x43)
#define LSS_SWITCH_STATE_SELECTIVE_REPLY (0x44)
#define LSS_STORE_CONFIG_CS (0x17)
#define LSS_INQUIRE_SERIAL_CS (0x5D)
#define LSS_CONFIGURE_NODE_ID_CS (0x11)

#define FH_VENDOR_ID (327)
#define FH_PRODUCT_CODE (48)
#define FH_REVISION_NUMBER (0x10000)

typedef enum LSS_State { Wait = 0x00, Config = 0x01 } LSS_State;

typedef struct LSS_Node {
  uint32_t serial;
  uint32_t id;
} LSS_Node;

#pragma pack(1)
typedef struct {
  uint16_t code;
  union {
    struct {
      // object 0x1001
      uint8_t error;
      // object 0x2320
      uint16_t fh_error;
    } fh;
    uint8_t raw_data[6];
  };
} CAN_Open_EMCY_packet_t;

typedef struct {
  uint8_t spec;
  uint16_t index;
  uint8_t subindex;
  uint8_t data[CAN_OPEN_SDO_PKT_DATA_SIZE];
} CAN_Open_SDO_packet_t;

typedef struct {
  uint8_t data[CAN_OPEN_PDO_PKT_DATA_SIZE];
} CAN_Open_PDO_packet_t;

typedef struct {
  uint8_t state;
  uint8_t node_id;
} CAN_Open_NMT_packet_t;

// Generic LSS error response
typedef struct {
  /**
   * @brief Generic error code
   *
   * The following values are defined:
   *
   * - 0: Success
   *
   * Depending on the protocol, additional error codes may be defined.
   */
  uint8_t error;
  /// Implementation specific error code
  uint8_t specific_error;
} CAN_Open_LSS_error_t;

typedef struct {
  uint8_t cs;
  union {
    uint8_t data[CAN_OPEN_LSS_PKT_DATA_SIZE];

    /// Configure Node-ID Protocol: message to slave
    struct {
      /// Node ID to assign
      uint8_t nid;
    } configure_node_id_tx;

    /**
     * @brief Configure Node-ID Protocol: response from slave
     *
     * The following additional error codes are defined:
     *
     * - 1 Node ID out of range
     */
    CAN_Open_LSS_error_t configure_node_id_rx;

    /**
     * @brief Store Configuration Protocol: response from slave
     *
     * The following additional error codes are defined:
     *
     * - 1: Store configuration not supported
     * - 2: Storage media access error
     */
    CAN_Open_LSS_error_t store_config_rx;

    /**
     * @brief Switch Mode Selective Protocol: response from slave
     */
    struct {
      uint8_t mode;
    } switch_mode_selective_rx;
  };
} CAN_Open_LSS_packet_t;
#pragma pack(0)

#define CAN_OPEN_SDO_PKT_TYPE 1
#define CAN_OPEN_PDO_PKT_TYPE 2
#define CAN_OPEN_NMT_PKT_TYPE 3
#define CAN_OPEN_PDO_RTR_PKT_TYPE 4
#define CAN_OPEN_LSS_PKT_TYPE 5
#define CAN_OPEN_EMCY_PKT_TYPE 6

typedef struct {
  uint8_t func;
  uint8_t node_id;
  uint8_t pkt_type;
  uint8_t size;
  union {
    CAN_Open_SDO_packet_t sdo;
    CAN_Open_PDO_packet_t pdo;
    CAN_Open_NMT_packet_t nmt;
    CAN_Open_LSS_packet_t lss;
    CAN_Open_EMCY_packet_t emcy;
  } pkt;
} __attribute__((aligned(4))) CAN_Open_Message_t;

/// Generic CANOpen identity object
#define GENERIC_SDO_IDENTITY (0x1018)
/// Vendor identifier (32 bit, assigned by CiA)
#define GENERIC_SDO_IDENTITY_VENDOR (0x01)
/// Product code (32 bit)
#define GENERIC_SDO_IDENTITY_PRODUCT (0x02)
/// Serial number (32 bit)
#define GENERIC_SDO_IDENTITY_REVISION (0x03)
/// Serial number (32 bit)
#define GENERIC_SDO_IDENTITY_SN (0x04)

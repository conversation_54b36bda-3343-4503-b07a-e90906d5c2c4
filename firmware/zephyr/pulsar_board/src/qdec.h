#pragma once

#include <device.h>
#include <lib/history_list/history_list.h>
#include <stdint.h>

#if CONFIG_BOARD_SCANNER_GD
#define NUM_QDEC_RECORDS 5000u
#else
// STM32 board
#define NUM_QDEC_RECORDS 7500u
#endif

typedef struct {
  int64_t ticks[2];
} qdec_record;

int qdec_init(history_list *hist, uint32_t tick_scale);
history_list *qdec_get_history_list();
int64_t qdec_get_latest(uint8_t id);

void qdec_tick(void *arg);

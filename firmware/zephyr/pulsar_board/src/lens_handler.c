#include <logging/log.h>
LOG_MODULE_REGISTER(lens_handler, CONFIG_APP_LOG_LEVEL);
#include "lens_handler.h"

#include <device.h>
#include <devicetree.h>
#include <drivers/liquid_lens.h>
#include <errno.h>

#include "generated/lib/drivers/nanopb/proto/pulczar_board.pb.h"

static uint8_t CHANNEL = 1;

const static struct device *ll_dev;

bool get_14515() {
#if CONFIG_BOARD_NUCLEO_H753ZI || CONFIG_BOARD_NUCLEO_H745ZI_Q_M7
  ll_dev = DEVICE_DT_GET(DT_NODELABEL(ll_max14515));
  if (device_is_ready(ll_dev)) {
    return true;
  } else {
    LOG_INF("Failed to find MAX14515 ll driver");
    return false;
  }
#endif
  return false;
}
bool get_14574() {
  ll_dev = DEVICE_DT_GET(DT_NODELABEL(ll_max14574));
  if (device_is_ready(ll_dev)) {
    return true;
  } else {
    LOG_INF("Failed to find MAX14574 ll driver");
    return false;
  }
}
int lens_handler_init() {
#ifdef ALT_CHANNEL
  CHANNEL = ALT_CHANNEL;
#endif
#if CONFIG_PREFER_MAX14574_DRIVER
  bool found = get_14574() || get_14515();
#else
  bool found = get_14515() || get_14574();
#endif
  if (!found) {
    LOG_ERR("Failed to find any liquid lens driver");
    return -ENODEV;
  }
  return 0;
}

void handle_lens(npb_request_data *req) {

  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = req->request.header.requestId;
  reply.which_reply = pulczar_board_Reply_pulczar_tag;
  reply.reply.pulczar.which_reply = pulczar_Reply_lens_tag;
  lens_Request *lreq = &req->request.request.pulczar.request.lens;
  int ret = 1;
  switch (lreq->which_request) {
  case lens_Request_set_tag:
    reply.reply.pulczar.reply.lens.which_reply = lens_Reply_ack_tag;
    ret = liquid_lens_set_value(ll_dev, CHANNEL, lreq->request.set.value);
    break;
  case lens_Request_get_tag:
    reply.reply.pulczar.reply.lens.which_reply = lens_Reply_get_tag;
    ret = liquid_lens_get_value(ll_dev, CHANNEL, (uint16_t *)&(reply.reply.pulczar.reply.lens.reply.get.value));
    break;
  default:
    reply.reply.pulczar.reply.lens.which_reply = lens_Reply_error_tag;
    break;
  }
  if (ret != 0) {
    reply.reply.pulczar.reply.lens.which_reply = lens_Reply_error_tag;
    reply.reply.pulczar.reply.lens.reply.error.code = ret;
  }
  npb_send_reply(&reply, &req->metadata);
}

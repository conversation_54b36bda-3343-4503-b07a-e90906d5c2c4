#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(gimbal_handler, CONFIG_APP_LOG_LEVEL);
#include <zephyr/zephyr.h>

#include "can_open.h"
#include "gimbal_handler.h"
#include "qdec.h"
#include "servo.h"

#define THREAD_PRIORITY K_PRIO_PREEMPT(8)

#define DEFAULT_SERVO_REQUEST_TIMEOUT 1000
#define DEFAULT_SERVO_REPLY_TIMEOUT 10000

K_MSGQ_DEFINE(gimbal_req, sizeof(npb_request_data *), CONFIG_PB_REQUESTS_BUFFER_SIZE, sizeof(npb_request_data *));
SERVO_DEFINE(pan_servo, NODE_ID_PAN_SERVO, 0);
SERVO_DEFINE(tilt_servo, NODE_ID_TILT_SERVO, 1);

static void process_gimbal_requests();
K_THREAD_DEFINE(gimbal_npb_thread_id, 4096, process_gimbal_requests, NULL, NULL, NULL, THREAD_PRIORITY, 0,
                K_TICKS_FOREVER);

void handle_gimbal_request(npb_request_data *req) {
  npb_request_data *dataRetained = npb_retain(req);
  k_msgq_put(&gimbal_req, &dataRetained, K_FOREVER);
}
void gimbal_start() {
  SERVO_START(pan_servo);
  SERVO_START(tilt_servo);
  k_thread_name_set(gimbal_npb_thread_id, "gimbal_npb_thread");
  k_thread_start(gimbal_npb_thread_id);
}

static void handle_servos(gimbal_Request *req, gimbal_Reply *reply) {
  reply->which_reply = gimbal_Reply_servos_tag;
  reply->reply.servos.has_pan = true;
  reply->reply.servos.has_tilt = true;
  servo_add_msg(&pan_servo, &req->request.servos.pan, &reply->reply.servos.pan, DEFAULT_SERVO_REQUEST_TIMEOUT);
  servo_add_msg(&tilt_servo, &req->request.servos.tilt, &reply->reply.servos.tilt, DEFAULT_SERVO_REQUEST_TIMEOUT);
  servo_await_resp(&pan_servo, DEFAULT_SERVO_REPLY_TIMEOUT);
  servo_await_resp(&tilt_servo, DEFAULT_SERVO_REPLY_TIMEOUT);
}
static void handle_boot(gimbal_Request *req, gimbal_Reply *reply) {
  req->which_request = gimbal_Request_servos_tag;
  epos_Home_Params pan_params = req->request.boot.pan_params;
  epos_Home_Params tilt_params = req->request.boot.tilt_params;
  req->request.servos.pan.which_request = servo_Request_boot_tag;
  req->request.servos.tilt.which_request = servo_Request_boot_tag;
  req->request.servos.pan.request.boot.params = pan_params;
  req->request.servos.tilt.request.boot.params = tilt_params;
  handle_servos(req, reply);
  if (reply->reply.servos.pan.which_reply != servo_Reply_error_tag &&
      reply->reply.servos.tilt.which_reply != servo_Reply_error_tag) {
    reply->which_reply = gimbal_Reply_ack_tag;
  }
}
static void handle_stop(gimbal_Request *req, gimbal_Reply *reply) {
  req->which_request = gimbal_Request_servos_tag;
  req->request.servos.pan.which_request = servo_Request_stop_tag;
  req->request.servos.tilt.which_request = servo_Request_stop_tag;
  handle_servos(req, reply);
  if (reply->reply.servos.pan.which_reply != servo_Reply_error_tag &&
      reply->reply.servos.tilt.which_reply != servo_Reply_error_tag) {
    reply->which_reply = gimbal_Reply_ack_tag;
  }
}
static void fill_position(history_record *record, gimbal_PositionAt *pos) {
  if (!record) {
    pos->valid = false;
    return;
  }
  pos->timestamp_us = *(record->usec);
  qdec_record *qrec = (qdec_record *)record->record;
  pos->pan = (int32_t)servo_get_position(&pan_servo, qrec);
  pos->tilt = (int32_t)servo_get_position(&tilt_servo, qrec);
  pos->valid = true;
}
static void handle_position(gimbal_Request *req, gimbal_Reply *reply) {
  reply->which_reply = gimbal_Reply_position_tag;
  history_record output;
  reply->reply.position.has_current = true;
  reply->reply.position.has_requested = true;
  int resp;
  resp = history_list_get_best(qdec_get_history_list(), req->request.position.timestamp_us, &output);
  fill_position(resp >= 0 ? &output : NULL, &(reply->reply.position.requested));
  resp = history_list_get_latest(qdec_get_history_list(), &output);
  fill_position(resp >= 0 ? &output : NULL, &(reply->reply.position.current));
}
static void handle_request(npb_request_data *req) {
  gimbal_Request *greq = &req->request.request.pulczar.request.gimbal;
  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = req->request.header.requestId;
  reply.which_reply = pulczar_board_Reply_pulczar_tag;
  reply.reply.pulczar.which_reply = pulczar_Reply_gimbal_tag;
  gimbal_Reply *greply = &reply.reply.pulczar.reply.gimbal;
  switch (greq->which_request) {
  case gimbal_Request_boot_tag:
    greply->which_reply = gimbal_Reply_ack_tag;
    handle_boot(greq, greply);
    break;
  case gimbal_Request_stop_tag:
    greply->which_reply = gimbal_Reply_ack_tag;
    handle_stop(greq, greply);
    break;
  case gimbal_Request_servos_tag:
    handle_servos(greq, greply);
    break;
  case gimbal_Request_position_tag:
    handle_position(greq, greply);
    break;
  default:
    greply->which_reply = gimbal_Reply_error_tag;
    break;
  }

  npb_send_reply(&reply, &req->metadata);
  npb_release(req);
}
static void process_gimbal_requests() {
  npb_request_data *req;
  for (;;) {
    k_msgq_get(&gimbal_req, &req, K_FOREVER);
    handle_request(req);
  }
}
epos_servo *get_pan_servo() { return &pan_servo; }
epos_servo *get_tilt_servo() { return &tilt_servo; }
epos_servo *get_servo_by_can_id(uint8_t id) {
  if (pan_servo.opencan_id == id) {
    return &pan_servo;
  }
  if (tilt_servo.opencan_id == id) {
    return &tilt_servo;
  }
  return NULL;
}

struct pid_config_s *gimbal_get_pid_cfg(uint8_t can_id) {
  epos_servo *servo = get_servo_by_can_id(can_id);
  if (servo == NULL) {
    return NULL;
  }
  return servo_get_pid_cfg(servo);
}
bool gimbal_servo_booted(uint8_t can_id) {
  epos_servo *servo = get_servo_by_can_id(can_id);
  if (servo == NULL) {
    return false;
  }
  return servo->booted;
}

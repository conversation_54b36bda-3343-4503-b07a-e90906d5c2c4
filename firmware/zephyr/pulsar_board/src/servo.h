#pragma once
#include "generated/lib/drivers/nanopb/proto/gimbal.pb.h"
#include "qdec.h"
#include "specific_controller.h"
#include <zephyr.h>
#define SERVO_THREAD_PRIORITY K_PRIO_PREEMPT(8)
#define SERVO_THREAD_STACK_SIZE 1024

typedef struct {
  servo_Request *req;
  servo_Reply *resp;
} servo_msg;

typedef struct {
  uint32_t max_profile_velocity;
  uint16_t settle_window;
  uint16_t settle_timeout;
  uint16_t max_diff_millis;
} epos_servo_config;
typedef struct {
  uint8_t opencan_id;
  struct k_msgq *input;
  struct k_sem *ready;
  uint32_t enc_id;
  int64_t enc_offset;
  int32_t limit;
  int32_t resolution;
  epos_servo_config config;
  struct pid_config_s pid_cfg;
  bool booted;
  bool invert;
} epos_servo;
#define SERVO_DEFINE(servo_name, node_id, encoder_id)                                                                  \
  K_MSGQ_DEFINE(servo_inq_##servo_name, sizeof(servo_msg), CONFIG_PB_REQUESTS_BUFFER_SIZE, 4);                         \
  K_SEM_DEFINE(servo_ready_sem_##servo_name, 0, 1)                                                                     \
  epos_servo servo_name = {                                                                                            \
      .opencan_id = node_id,                                                                                           \
      .input = &servo_inq_##servo_name,                                                                                \
      .ready = &servo_ready_sem_##servo_name,                                                                          \
      .enc_id = encoder_id,                                                                                            \
      .enc_offset = 0,                                                                                                 \
      .resolution = 262144,                                                                                            \
      .config = {.max_profile_velocity = 0, .settle_window = 0, .settle_timeout = 0, .max_diff_millis = 0},            \
      .pid_cfg = DEFAULT_PID,                                                                                          \
      .booted = false,                                                                                                 \
      .invert = false};                                                                                                \
  K_THREAD_DEFINE(servo_thread_id_##servo_name, SERVO_THREAD_STACK_SIZE, servo_run, &servo_name, NULL, NULL,           \
                  SERVO_THREAD_PRIORITY, 0, K_TICKS_FOREVER);

#define SERVO_START(servo_name)                                                                                        \
  servo_init(&servo_name);                                                                                             \
  k_thread_name_set(servo_thread_id_##servo_name, "servo_##servo_name");                                               \
  k_thread_start(servo_thread_id_##servo_name);

void servo_init(epos_servo *servo);
void servo_add_msg(epos_servo *servo, servo_Request *req, servo_Reply *resp, uint32_t timeout_ms);
void servo_await_resp(epos_servo *servo, uint32_t timeout_ms);
int64_t servo_get_position(epos_servo *servo, qdec_record *qrec);
struct pid_config_s *servo_get_pid_cfg(epos_servo *servo);

void servo_run(void *arg);
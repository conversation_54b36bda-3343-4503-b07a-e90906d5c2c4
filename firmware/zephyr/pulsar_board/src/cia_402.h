
/* ========================================
 *
 * Carbon Robotics, 2022
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

/*
 * CIA 402 defines standards for can in automation. currently all our can based
 * motor controllers follow this standard so all standard commands are defined here.
 */
#pragma once

#include "can_open.h"
#include "stdint.h"
#include <utils/carbon_response_codes.h>

#define CIA402_STATUS_FOLLOWING_ERROR 0x2000
#define CIA402_STATUS_SETPOINT_AWKNOWLEDGED 0x1000
#define CIA402_STATUS_INTERNAL_LIMIT_ACTIVE 0x800
#define CIA402_STATUS_TARGET_REACHED 0x400
#define CIA402_STATUS_WARNING 0x80
#define CIA402_STATUS_SWITCH_ON_DISABLED 0x40
#define CIA402_STATUS_QUICK_STOP 0x20
#define CIA402_STATUS_VOLTAGE_ENABLED 0x10
#define CIA402_STATUS_FAULT 0x08
#define CIA402_STATUS_OPERATIONS_ENABLED 0x04
#define CIA402_STATUS_SWITCHED_ON 0x02
#define CIA402_STATUS_READY_TO_SWITCH_ON 0x01

#define CIA402_CONTROL_HALT 0x100
#define CIA402_CONTROL_FAULT_RESET 0x80
#define CIA402_CONTROL_ABS_REL 0x40
#define CIA402_CONTROL_CHANGE_SET_IMMEDIATELY 0x20
#define CIA402_CONTROL_START_OPERATION 0x10
#define CIA402_CONTROL_ENABLE_OPERATION 0x08
#define CIA402_CONTROL_QUICK_STOP 0x04
#define CIA402_CONTROL_ENABLE_VOLTAGE 0x02
#define CIA402_CONTROL_SWITCHED_ON 0x01

#define CIA402_CONTROL_COMMAND_SHUTDOWN CIA402_CONTROL_ENABLE_VOLTAGE | CIA402_CONTROL_QUICK_STOP
#define CIA402_CONTROL_COMMAND_SWITCH_ON                                                                               \
  CIA402_CONTROL_ENABLE_VOLTAGE | CIA402_CONTROL_QUICK_STOP | CIA402_CONTROL_SWITCHED_ON
#define CIA402_CONTROL_COMMAND_SWITCH_ON_ENABLE                                                                        \
  CIA402_CONTROL_ENABLE_VOLTAGE | CIA402_CONTROL_QUICK_STOP | CIA402_CONTROL_SWITCHED_ON |                             \
      CIA402_CONTROL_ENABLE_OPERATION
#define CIA402_CONTROL_COMMAND_DISABLE                                                                                 \
  CIA402_CONTROL_ENABLE_VOLTAGE | CIA402_CONTROL_QUICK_STOP | CIA402_CONTROL_SWITCHED_ON
#define CIA402_CONTROL_COMMAND_FAULT_RESET CIA402_CONTROL_FAULT_RESET
#define CIA402_CONTROL_COMMAND_START_OPERATION                                                                         \
  CIA402_CONTROL_COMMAND_SWITCH_ON_ENABLE | CIA402_CONTROL_CHANGE_SET_IMMEDIATELY | CIA402_CONTROL_START_OPERATION

// **** PDO ****

#define CIA402_PDO_TRANSMISSION_SYNC 1
#define CIA402_PDO_TRANSMISSION_ASYNC_RTR 253
#define CIA402_PDO_TRANSMISSION_ASYNC 255

#define CIA402_PDO_COBID_VALID (1 << 31)
#define CIA402_PDO_COBID_RTR (1 << 30)

#define CIA402_PDO_BASE_COBID_RX1 0x200
#define CIA402_PDO_BASE_COBID_RX2 0x300
#define CIA402_PDO_BASE_COBID_RX3 0x400
#define CIA402_PDO_BASE_COBID_RX4 0x500
#define CIA402_PDO_BASE_COBID_TX1 0x180
#define CIA402_PDO_BASE_COBID_TX2 0x280
#define CIA402_PDO_BASE_COBID_TX3 0x380
#define CIA402_PDO_BASE_COBID_TX4 0x480

/*
 * @brief Helper macro to build TXPDO mapping
 */
#define MAKE_TXPDO_MAPPING_OBJ(index, subindex, numBits)                                                               \
  (((index & 0xffff) << 16) | ((subindex & 0xff) << 8) | (numBits & 0xff))

// **** Configuration ****
extern const uint8_t CIA402_SAVE_PARAM_VALUE[];
extern const uint8_t CIA402_RESTORE_PARAM_VALUE[];
extern const uint32_t *CIA402_SAVE_PARAM_VALUE_POINTER;
extern const uint32_t *CIA402_RESTORE_PARAM_VALUE_POINTER;
static inline CARBON_RESPONSE_CODE CIA402_Save_All_Parameters(uint8_t node_id) {
  return Send_SDO_Download(node_id, 0x1010, 0x01, *CIA402_SAVE_PARAM_VALUE_POINTER);
}
static inline CARBON_RESPONSE_CODE CIA402_Restore_Default_Parameters(uint8_t node_id) {
  return Send_SDO_Download(node_id, 0x1011, 0x01, *CIA402_RESTORE_PARAM_VALUE_POINTER);
}
static inline CARBON_RESPONSE_CODE CIA402_Read_Vendor_ID(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, GENERIC_SDO_IDENTITY, GENERIC_SDO_IDENTITY_VENDOR);
} // uint32 return value
static inline CARBON_RESPONSE_CODE CIA402_Read_Product_Code(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, GENERIC_SDO_IDENTITY, GENERIC_SDO_IDENTITY_PRODUCT);
} // uint32 (uint16/uint16) return value
static inline CARBON_RESPONSE_CODE CIA402_Read_Revision_Number(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, GENERIC_SDO_IDENTITY, GENERIC_SDO_IDENTITY_REVISION);
} // uint32 (uint16/uint16) return value
static inline CARBON_RESPONSE_CODE CIA402_Read_Last_Serial_Number(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, GENERIC_SDO_IDENTITY, GENERIC_SDO_IDENTITY_SN);
} // uint32 (8 4 bits digits) return value

static inline CARBON_RESPONSE_CODE CIA301_Write_EMCY_COBID(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x1014, 0x00, value);
}

static inline CARBON_RESPONSE_CODE CIA402_Write_RXPDO1_COBID(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x1400, 0x01, value);
} // 32 bit word
static inline CARBON_RESPONSE_CODE CIA402_Write_RXPDO1_Transmission(uint8_t node_id, uint8_t value) {
  return Send_SDO_Download(node_id, 0x1400, 0x02, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_RXPDO2_COBID(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x1401, 0x01, value);
} // 32 bit word
static inline CARBON_RESPONSE_CODE CIA402_Write_RXPDO2_Transmission(uint8_t node_id, uint8_t value) {
  return Send_SDO_Download(node_id, 0x1401, 0x02, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_RXPDO3_COBID(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x1402, 0x01, value);
} // 32 bit word
static inline CARBON_RESPONSE_CODE CIA402_Write_RXPDO3_Transmission(uint8_t node_id, uint8_t value) {
  return Send_SDO_Download(node_id, 0x1402, 0x02, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_RXPDO4_COBID(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x1403, 0x01, value);
} // 32 bit word
static inline CARBON_RESPONSE_CODE CIA402_Write_RXPDO4_Transmission(uint8_t node_id, uint8_t value) {
  return Send_SDO_Download(node_id, 0x1403, 0x02, value);
}

static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO1_COBID(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x1800, 0x01, value);
} // 32 bit word
static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO1_Transmission(uint8_t node_id, uint8_t value) {
  return Send_SDO_Download(node_id, 0x1800, 0x02, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO1_Inhibit(uint8_t node_id, uint16_t value) {
  return Send_SDO_Download(node_id, 0x1800, 0x03, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO2_COBID(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x1801, 0x01, value);
} // 32 bit word
static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO2_Transmission(uint8_t node_id, uint8_t value) {
  return Send_SDO_Download(node_id, 0x1801, 0x02, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO2_Inhibit(uint8_t node_id, uint16_t value) {
  return Send_SDO_Download(node_id, 0x1801, 0x03, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO3_COBID(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x1802, 0x01, value);
} // 32 bit word
static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO3_Transmission(uint8_t node_id, uint8_t value) {
  return Send_SDO_Download(node_id, 0x1802, 0x02, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO3_Inhibit(uint8_t node_id, uint16_t value) {
  return Send_SDO_Download(node_id, 0x1802, 0x03, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO4_COBID(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x1803, 0x01, value);
} // 32 bit word
static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO4_Transmission(uint8_t node_id, uint8_t value) {
  return Send_SDO_Download(node_id, 0x1803, 0x02, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO4_Inhibit(uint8_t node_id, uint16_t value) {
  return Send_SDO_Download(node_id, 0x1803, 0x03, value);
}

static inline CARBON_RESPONSE_CODE CIA402_Write_RXPDO1_Mapping_Number(uint8_t node_id, uint8_t value) {
  return Send_SDO_Download(node_id, 0x1600, 0x00, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_RXPDO1_Mapping_Object(uint8_t node_id, uint16_t index, uint32_t value) {
  return Send_SDO_Download(node_id, 0x1600, index, value);
} // 32 bit word
static inline CARBON_RESPONSE_CODE CIA402_Write_RXPDO2_Mapping_Number(uint8_t node_id, uint8_t value) {
  return Send_SDO_Download(node_id, 0x1601, 0x00, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_RXPDO2_Mapping_Object(uint8_t node_id, uint16_t index, uint32_t value) {
  return Send_SDO_Download(node_id, 0x1601, index, value);
} // 32 bit word
static inline CARBON_RESPONSE_CODE CIA402_Write_RXPDO3_Mapping_Number(uint8_t node_id, uint8_t value) {
  return Send_SDO_Download(node_id, 0x1602, 0x00, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_RXPDO3_Mapping_Object(uint8_t node_id, uint16_t index, uint32_t value) {
  return Send_SDO_Download(node_id, 0x1602, index, value);
} // 32 bit word
static inline CARBON_RESPONSE_CODE CIA402_Write_RXPDO4_Mapping_Number(uint8_t node_id, uint8_t value) {
  return Send_SDO_Download(node_id, 0x1603, 0x00, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_RXPDO4_Mapping_Object(uint8_t node_id, uint16_t index, uint32_t value) {
  return Send_SDO_Download(node_id, 0x1603, index, value);
} // 32 bit word

static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO1_Mapping_Number(uint8_t node_id, uint8_t value) {
  return Send_SDO_Download(node_id, 0x1A00, 0x00, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO1_Mapping_Object(uint8_t node_id, uint16_t index, uint32_t value) {
  return Send_SDO_Download(node_id, 0x1A00, index, value);
} // 32 bit word
static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO2_Mapping_Number(uint8_t node_id, uint8_t value) {
  return Send_SDO_Download(node_id, 0x1A01, 0x00, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO2_Mapping_Object(uint8_t node_id, uint16_t index, uint32_t value) {
  return Send_SDO_Download(node_id, 0x1A01, index, value);
} // 32 bit word
static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO3_Mapping_Number(uint8_t node_id, uint8_t value) {
  return Send_SDO_Download(node_id, 0x1A02, 0x00, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO3_Mapping_Object(uint8_t node_id, uint16_t index, uint32_t value) {
  return Send_SDO_Download(node_id, 0x1A02, index, value);
} // 32 bit word
static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO4_Mapping_Number(uint8_t node_id, uint8_t value) {
  return Send_SDO_Download(node_id, 0x1A03, 0x00, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_TXPDO4_Mapping_Object(uint8_t node_id, uint16_t index, uint32_t value) {
  return Send_SDO_Download(node_id, 0x1A03, index, value);
} // 32 bit word

// **** Homing ****
static inline CARBON_RESPONSE_CODE CIA402_Write_Homing_Method(uint8_t node_id, int8_t value) {
  return Send_SDO_Download(node_id, 0x6098, 0x00, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_Homing_Switch_Search_Speed(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x6099, 0x01, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_Homing_Zero_Search_Speed(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x6099, 0x02, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_Homing_Acceleration(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x609A, 0x00, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_Home_Offset(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x607C, 0x00, value);
}

static inline CARBON_RESPONSE_CODE CIA402_Read_Homing_Method(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x6098, 0x00);
} // int8 return value
static inline CARBON_RESPONSE_CODE CIA402_Read_Homing_Switch_Search_Speed(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x6099, 0x01);
} // uint32 return value
static inline CARBON_RESPONSE_CODE CIA402_Read_Homing_Zero_Search_Speed(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x6099, 0x02);
} // uint32 return value
static inline CARBON_RESPONSE_CODE CIA402_Read_Homing_Acceleration(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x609A, 0x00);
} // uint32 return value

#define CIA402_HOMING_METHOD_ACTUAL_POSITION 37
#define CIA402_HOMING_METHOD_INDEX_POSITIVE_SPEED 34
#define CIA402_HOMING_METHOD_INDEX_NEGATIVE_SPEED 33
#define CIA402_HOMING_METHOD_HOME_SWITCH_NEGATIVE_SPEED 27
#define CIA402_HOMING_METHOD_HOME_SWITCH_POSITIVE_SPEED 23
#define CIA402_HOMING_METHOD_POSITIVE_LIMIT_SWITCH 18
#define CIA402_HOMING_METHOD_NEGATIVE_LIMIT_SWITCH 17
#define CIA402_HOMING_METHOD_HOME_SWITCH_NEGATIVE_SPEED_INDEX 11
#define CIA402_HOMING_METHOD_HOME_SWITCH_POSITIVE_SPEED_INDEX 7
#define CIA402_HOMING_METHOD_POSITIVE_LIMIT_SWITCH_INDEX 2
#define CIA402_HOMING_METHOD_NEGATIVE_LIMIT_SWITCH_INDEX 1
#define CIA402_HOMING_METHOD_CURRENT_POSITIVE_SPEED_INDEX -1
#define CIA402_HOMING_METHOD_CURRENT_NEGATIVE_SPEED_INDEX -2
#define CIA402_HOMING_METHOD_CURRENT_POSITIVE_SPEED -3
#define CIA402_HOMING_METHOD_CURRENT_NEGATIVE_SPEED -4

// **** Modes of Operations ****
#define CIA402_PROFILE_POSITION_MODE 1
#define CIA402_PROFILE_VELOCITY_MODE 3
#define CIA402_HOMING_MODE 6
#define CIA402_CSP_MODE 8
#define CIA402_CSV_MODE 9
#define CIA402_CST_MODE 10

static inline CARBON_RESPONSE_CODE CIA402_Write_Mode_Of_Operation(uint8_t node_id, int8_t value) {
  return Send_SDO_Download(node_id, 0x6060, 0x00, value);
}

static inline CARBON_RESPONSE_CODE CIA402_Read_Mode_Of_Operation(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x6061, 0x00);
} // int8 return value

// **** Profile Position Mode Commands ****
static inline CARBON_RESPONSE_CODE CIA402_Write_Target_Position(uint8_t node_id, int32_t value) {
  return Send_SDO_Download(node_id, 0x607A, 0x00, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_Profile_Velocity(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x6081, 0x00, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_Profile_Acceleration(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x6083, 0x00, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_Profile_Deceleration(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x6084, 0x00, value);
}

static inline CARBON_RESPONSE_CODE CIA402_Read_Target_Position(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x607A, 0x00);
} // int32 return value
static inline CARBON_RESPONSE_CODE CIA402_Read_Profile_Velocity(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x6081, 0x00);
} // uint32 return value
static inline CARBON_RESPONSE_CODE CIA402_Read_Profile_Acceleration(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x6083, 0x00);
} // uint32 return value
static inline CARBON_RESPONSE_CODE CIA402_Read_Profile_Deceleration(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x6084, 0x00);
} // uint32 return value

// **** Profile Position Mode Configuration ****
static inline CARBON_RESPONSE_CODE CIA402_Write_Software_Position_Min_Limit(uint8_t node_id, int32_t value) {
  return Send_SDO_Download(node_id, 0x607D, 0x01, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_Software_Position_Max_Limit(uint8_t node_id, int32_t value) {
  return Send_SDO_Download(node_id, 0x607D, 0x02, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_Max_Profile_Velocity(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x607f, 0x00, value);
}
static inline CARBON_RESPONSE_CODE CIA402_Write_Max_Acceleration(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x60C5, 0x00, value);
}

static inline CARBON_RESPONSE_CODE CIA402_Read_Software_Position_Min_Limit(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x607D, 0x01);
} // int32 return value
static inline CARBON_RESPONSE_CODE CIA402_Read_Software_Position_Max_Limit(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x607D, 0x01);
} // int32 return value
static inline CARBON_RESPONSE_CODE CIA402_Read_Max_Profile_Velocity(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x607f, 0x00);
} // uint32 return value
static inline CARBON_RESPONSE_CODE CIA402_Read_Max_Acceleration(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x60C5, 0x00);
} // uint32 return value

// **** General controller ****
static inline CARBON_RESPONSE_CODE CIA402_Write_Control_Word(uint8_t node_id, uint16_t word) {
  return Send_SDO_Download(node_id, 0x6040, 0x00, word);
} // 16 bits word

static inline CARBON_RESPONSE_CODE CIA402_Read_Control_Word(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x6040, 0x00);
} // 16 bits word return value
static inline CARBON_RESPONSE_CODE CIA402_Read_Status_Word(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x6041, 0x00);
} // 16 bits word return value

// **** Errors ****
static inline CARBON_RESPONSE_CODE CIA402_Read_Error_Register(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x1001, 0x00);
} // 8 bits word return value
static inline CARBON_RESPONSE_CODE CIA402_Read_Error_Code(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x603F, 0x00);
} // uint16 word return value
static inline CARBON_RESPONSE_CODE CIA402_Read_Following_Error_Value(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x60F4, 0x00);
} // int32 word return value

// **** Units ****
static inline CARBON_RESPONSE_CODE CIA402_Write_Unit_Velocity(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x60A9, 0x00, value);
}

static inline CARBON_RESPONSE_CODE CIA402_Read_Unit_Velocity(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x60A9, 0x00);
} // uint32 return value

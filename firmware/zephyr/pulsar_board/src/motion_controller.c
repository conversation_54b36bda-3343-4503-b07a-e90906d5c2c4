#include <logging/log.h>
LOG_MODULE_REGISTER(motion_controller, CONFIG_APP_LOG_LEVEL);

#include "can_open.h"
#include "can_open_msg.h"
#include "cia_402.h"
#include "gimbal_handler.h"
#include "motion_controller.h"

#include "specific_controller.h"

#if USE_FAULHABER
#include "drive/faulhaber/faulhaber.h"
#include "drive/faulhaber/fh_config.h"
#endif

#include <stdlib.h>
#include <string.h>
#include <zephyr.h>

#define DELTA_TIME(start_time) (k_uptime_get() - start_time)

MotionController mc;

static CARBON_RESPONSE_CODE Await_Status_PDO(uint8_t node_id, CAN_Open_Message_t *msg, uint16_t timeout_ms,
                                             uint16_t expected, uint16_t expected_neg) {
  int64_t start_time = k_uptime_get();
  uint16_t *pdo_data_ptr = (uint16_t *)msg->pkt.pdo.data;
  int64_t remaining = timeout_ms;
  while (remaining > 0) {
    // Verify expected 1 bits and 0 bits. Definition of which bit means what is in the epos firmware spec.
    RETURN_CODE_IF_NOT_OK(Await_Reply(node_id, msg, remaining, CAN_OPEN_FUNC_CODE_PDO1_TX))
    if (msg->pkt_type == CAN_OPEN_PDO_PKT_TYPE && ((*pdo_data_ptr) & expected) == expected &&
        (~(*pdo_data_ptr) & expected_neg) == expected_neg) {
      return CARBON_RESPONSE_OK;
    }
    remaining -= k_uptime_delta(&start_time);
  }
  return CARBON_CAN_OPEN_AWAIT_TIMEOUT;
}

inline static CARBON_RESPONSE_CODE Await_Status(uint8_t node_id, uint16_t timeout_ms, uint16_t expected,
                                                uint16_t expected_neg) {
  CAN_Open_Message_t msg;
  return Await_Status_PDO(node_id, &msg, timeout_ms, expected, expected_neg);
}

static CARBON_RESPONSE_CODE Control_Word_Await_Status(uint8_t node_id, uint16_t control_word, uint16_t expected_status,
                                                      uint16_t expected_neg_status) {
  CAN_Open_Message_t msg;
  Send_PDO(node_id, CAN_OPEN_FUNC_CODE_PDO1_RX, (uint8_t *)&control_word, sizeof(control_word));
  return Await_Status_PDO(node_id, &msg, CAN_OPEN_DEFAULT_TIMEOUT_MS, expected_status, expected_neg_status);
}
static CARBON_RESPONSE_CODE Enable_Node(uint8_t node_id) {
  // Shutdown Command Actually gets the node in a state closer to operational
  RETURN_CODE_IF_NOT_OK(Control_Word_Await_Status(node_id, CIA402_CONTROL_COMMAND_SHUTDOWN,
                                                  CIA402_STATUS_QUICK_STOP | CIA402_STATUS_READY_TO_SWITCH_ON,
                                                  CIA402_STATUS_SWITCH_ON_DISABLED | CIA402_STATUS_SWITCHED_ON |
                                                      CIA402_STATUS_OPERATIONS_ENABLED | CIA402_STATUS_FAULT))

  // Switch On Enable, get the node in its operational state, Set Immediate makes the position request immediate instead
  // of queued
  return Control_Word_Await_Status(node_id,
                                   CIA402_CONTROL_COMMAND_SWITCH_ON_ENABLE | CIA402_CONTROL_CHANGE_SET_IMMEDIATELY,
                                   CIA402_STATUS_QUICK_STOP | CIA402_STATUS_READY_TO_SWITCH_ON |
                                       CIA402_STATUS_SWITCHED_ON | CIA402_STATUS_OPERATIONS_ENABLED,
                                   CIA402_STATUS_SWITCH_ON_DISABLED | CIA402_STATUS_FAULT);
}

static CARBON_RESPONSE_CODE Disable_Node(uint8_t node_id) {
  return Control_Word_Await_Status(
      node_id, CIA402_CONTROL_COMMAND_DISABLE,
      CIA402_STATUS_QUICK_STOP | CIA402_STATUS_READY_TO_SWITCH_ON | CIA402_STATUS_SWITCHED_ON,
      CIA402_STATUS_SWITCH_ON_DISABLED | CIA402_STATUS_OPERATIONS_ENABLED | CIA402_STATUS_FAULT);
}

// Must be called in Profile Position Mode Only
// Profile velocity is unsigned
static CARBON_RESPONSE_CODE Go_To_Position(uint8_t node_id, int32_t position, uint32_t velocity) {
  position /= mc.tick_scale;
  CAN_Open_Message_t msg;
  const uint16_t start_word = CIA402_CONTROL_COMMAND_START_OPERATION;
  const uint16_t reset_word = CIA402_CONTROL_COMMAND_SWITCH_ON_ENABLE | CIA402_CONTROL_CHANGE_SET_IMMEDIATELY;
  uint8_t data[8];
  velocity /= mc.velocity_scaler;
  memcpy(data, &position, sizeof(int32_t));
  memcpy(data + sizeof(int32_t), &velocity, sizeof(uint32_t));
  Send_PDO(node_id, CAN_OPEN_FUNC_CODE_PDO2_RX, data, 8);
  CIA402_Write_Control_Word(node_id, start_word);

  // The below may fail for near zero movements
  // I found it preferable to ignore the result if it times out
  CARBON_RESPONSE_CODE resp = mc.Await_Status_PDO(node_id, &msg, 10, CIA402_STATUS_SETPOINT_AWKNOWLEDGED,
                                                  0x00); // TODO determine how this changes
  if (resp != CARBON_RESPONSE_OK) {
    LOG_INF("node %d: failed to start moving err %u", node_id, resp);
  }
  // Reset Command Control Word so next Go To works
  // The servo is already moving when we make that call
  CIA402_Write_Control_Word(node_id, reset_word);

  return CARBON_RESPONSE_OK;
}

static CARBON_RESPONSE_CODE Setup_PDOs(uint8_t node_id) {
  // Control RX PDO
  RETURN_CODE_IF_NOT_OK(CIA402_Write_RXPDO1_COBID(node_id, CIA402_PDO_COBID_VALID))
  RETURN_CODE_IF_NOT_OK(CIA402_Write_RXPDO1_Mapping_Number(node_id, 0))
  RETURN_CODE_IF_NOT_OK(CIA402_Write_RXPDO1_Transmission(node_id, CIA402_PDO_TRANSMISSION_ASYNC))
  RETURN_CODE_IF_NOT_OK(
      CIA402_Write_RXPDO1_Mapping_Object(node_id, 0x01, (0x6040 << 16) + (0x00 << 8) + 16)) // Control Word
  RETURN_CODE_IF_NOT_OK(CIA402_Write_RXPDO1_Mapping_Number(node_id, 1))
  RETURN_CODE_IF_NOT_OK(CIA402_Write_RXPDO1_COBID(node_id, CIA402_PDO_BASE_COBID_RX1 | node_id))

  // Go To and Move RX PDO
  RETURN_CODE_IF_NOT_OK(CIA402_Write_RXPDO2_COBID(node_id, CIA402_PDO_COBID_VALID))
  RETURN_CODE_IF_NOT_OK(CIA402_Write_RXPDO2_Mapping_Number(node_id, 0))
  RETURN_CODE_IF_NOT_OK(CIA402_Write_RXPDO2_Transmission(node_id, CIA402_PDO_TRANSMISSION_ASYNC))
  RETURN_CODE_IF_NOT_OK(
      CIA402_Write_RXPDO2_Mapping_Object(node_id, 0x01, (0x607A << 16) + (0x00 << 8) + 32)) // Target Position
  RETURN_CODE_IF_NOT_OK(
      CIA402_Write_RXPDO2_Mapping_Object(node_id, 0x02, (0x6081 << 16) + (0x00 << 8) + 32)) // Profile Velocity
  RETURN_CODE_IF_NOT_OK(CIA402_Write_RXPDO2_Mapping_Number(node_id, 2))
  RETURN_CODE_IF_NOT_OK(CIA402_Write_RXPDO2_COBID(node_id, CIA402_PDO_BASE_COBID_RX2 | node_id))

  // Disabled RX PDO
  RETURN_CODE_IF_NOT_OK(
      CIA402_Write_RXPDO3_COBID(node_id, CIA402_PDO_BASE_COBID_RX3 | node_id | CIA402_PDO_COBID_VALID))
  RETURN_CODE_IF_NOT_OK(CIA402_Write_RXPDO3_Mapping_Number(node_id, 0))

  // Disabled RX PDO
  RETURN_CODE_IF_NOT_OK(
      CIA402_Write_RXPDO4_COBID(node_id, CIA402_PDO_BASE_COBID_RX4 | node_id | CIA402_PDO_COBID_VALID))
  RETURN_CODE_IF_NOT_OK(CIA402_Write_RXPDO4_Mapping_Number(node_id, 0))

  // Status TX PDO
  RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO1_COBID(node_id, CIA402_PDO_COBID_VALID))
  RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO1_Mapping_Number(node_id, 0))
  RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO1_Transmission(node_id, CIA402_PDO_TRANSMISSION_ASYNC))
  RETURN_CODE_IF_NOT_OK(
      CIA402_Write_TXPDO1_Mapping_Object(node_id, 0x01, (0x6041 << 16) + (0x00 << 8) + 16)) // Status Word
  RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO1_Mapping_Number(node_id, 1))
  RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO1_COBID(node_id, CIA402_PDO_BASE_COBID_TX1 | node_id))

  // Actual Position/Velocity RTR TX PDO
  RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO2_COBID(node_id, CIA402_PDO_COBID_VALID))
  RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO2_Mapping_Number(node_id, 0))
  RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO2_Transmission(node_id, CIA402_PDO_TRANSMISSION_ASYNC_RTR))
  RETURN_CODE_IF_NOT_OK(
      CIA402_Write_TXPDO2_Mapping_Object(node_id, 0x01, (0x6064 << 16) + (0x00 << 8) + 32)) // Position Actual
  RETURN_CODE_IF_NOT_OK(
      CIA402_Write_TXPDO2_Mapping_Object(node_id, 0x02, (0x606C << 16) + (0x00 << 8) + 32)) // Velocity Actual
  RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO2_Mapping_Number(node_id, 2))
  RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO2_COBID(node_id, CIA402_PDO_BASE_COBID_TX2 | node_id))

  // use TXPDOs for status reporting
  if (MC_get()->statusPdoEnabled) {
    // Configure the FH TX PDOs for status reporting
#if USE_FAULHABER
    // TX PDO 3: motor supply voltage, motor current, output stage temp
    RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO3_COBID(node_id, CIA402_PDO_COBID_VALID));
    RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO3_Mapping_Number(node_id, 0));

    RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO3_Transmission(node_id, CIA402_PDO_TRANSMISSION_SYNC));
    RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO3_Mapping_Object(
        node_id, 0x01, MAKE_TXPDO_MAPPING_OBJ(FH_SDO_VOLTAGE_MONITOR, FH_SDO_VOLTAGE_MONITOR_MOTOR, 16)));
    RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO3_Mapping_Object(
        node_id, 0x02, MAKE_TXPDO_MAPPING_OBJ(FH_SDO_ACTUAL, FH_SDO_ACTUAL_MOTOR_IDC, 16)));
    RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO3_Mapping_Object(
        node_id, 0x03, MAKE_TXPDO_MAPPING_OBJ(FH_SDO_DEVICE_TEMP, FH_SDO_DEVICE_TEMP_OUTPUT, 16)));
    RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO3_Mapping_Object(
        node_id, 0x04, MAKE_TXPDO_MAPPING_OBJ(FH_SDO_DEVICE_TEMP, FH_SDO_DEVICE_TEMP_WINDINGS, 16)));

    RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO3_Mapping_Number(node_id, 3));
    RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO3_COBID(node_id, CIA402_PDO_BASE_COBID_TX3 | node_id));
#else
    // TX PDO 3: Unused
    RETURN_CODE_IF_NOT_OK(
        CIA402_Write_TXPDO3_COBID(node_id, CIA402_PDO_BASE_COBID_TX3 | node_id | CIA402_PDO_COBID_VALID));
    RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO3_Mapping_Number(node_id, 0));
#endif

    // TX PDO 4: Unused
    RETURN_CODE_IF_NOT_OK(
        CIA402_Write_TXPDO4_COBID(node_id, CIA402_PDO_BASE_COBID_TX4 | node_id | CIA402_PDO_COBID_VALID));
    RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO4_Mapping_Number(node_id, 0));
  }
  // disable status reporting TXPDOs
  else {
    // TX PDO 3: disabled
    RETURN_CODE_IF_NOT_OK(
        CIA402_Write_TXPDO3_COBID(node_id, CIA402_PDO_BASE_COBID_TX3 | node_id | CIA402_PDO_COBID_VALID));
    RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO3_Mapping_Number(node_id, 0));

    // TX PDO 4: disabled
    RETURN_CODE_IF_NOT_OK(
        CIA402_Write_TXPDO4_COBID(node_id, CIA402_PDO_BASE_COBID_TX4 | node_id | CIA402_PDO_COBID_VALID));
    RETURN_CODE_IF_NOT_OK(CIA402_Write_TXPDO4_Mapping_Number(node_id, 0));
  }

  // ensure FH config is valid
#if USE_FAULHABER
  RETURN_CODE_IF_NOT_OK(FH_Config_Validate(node_id));
#endif

  return CARBON_RESPONSE_OK;
}

// Actual Velocity is signed
static CARBON_RESPONSE_CODE Get_Actual_Position_Velocity(uint8_t node_id, int32_t *position, int32_t *velocity) {
  CAN_Open_Message_t msg;
  clear_mailbox(node_id);
  Send_RTR_PDO(node_id, CAN_OPEN_FUNC_CODE_PDO2_TX);
  RETURN_SPECIFIED_IF_NOT_OK(
      Await_Reply(node_id, &msg, CAN_OPEN_DEFAULT_PDO_RTR_TIMEOUT_MS, CAN_OPEN_FUNC_CODE_PDO2_TX),
      CARBON_EPOS_POS_VEL_TIMEOUT)
  uint32_t *pdo_data_ptr = (uint32_t *)msg.pkt.pdo.data;
  if (position) {
    *position = *(int32_t *)pdo_data_ptr;
    *position *= mc.tick_scale;
  }
  if (velocity) {
    *velocity = *(int32_t *)(pdo_data_ptr + 1);
    *velocity *= mc.velocity_scaler;
    *velocity *= mc.tick_scale;
  }
  return CARBON_RESPONSE_OK;
}

static CARBON_RESPONSE_CODE Await_Target_Reached(uint8_t node_id, uint16_t timeout_ms, uint16_t *reach_time_ms_out) {
  int64_t start_time = k_uptime_get();
  RETURN_CODE_IF_NOT_OK(mc.Await_Status(node_id, timeout_ms, CIA402_STATUS_TARGET_REACHED, 0x00));
  if (reach_time_ms_out != NULL) {
    *reach_time_ms_out = (uint16_t)DELTA_TIME(start_time);
  }
  return CARBON_RESPONSE_OK;
}

static CARBON_RESPONSE_CODE Await_Settling(uint8_t node_id, int32_t target_position, uint16_t window,
                                           uint16_t timeout_ms, uint16_t *settle_time_ms_out) {
  int64_t start_time = k_uptime_get();
  const static int64_t period = 3; // TODO make this configurable
  int32_t current_position = 0;
  int32_t last_error = 0;
  int32_t current_error = 0;
  int32_t last_velocity = 0;
  int32_t current_velocity = 0;

  do {
    if (mc.Get_Actual_Position_Velocity(node_id, &current_position, &current_velocity) != CARBON_RESPONSE_OK) {
      k_sleep(K_MSEC(period));
      continue;
    }

    current_error = abs(target_position - current_position);

    // Settling Conditions
    // Standstill Condition Test First (No Velocity + positions within window)
    // Derivative Condition Test Second (Velocity sign change meaning peak + positions within window)
    if (current_error < window && last_error < window &&
        ((last_velocity == 0 && current_velocity == 0) || (last_velocity ^ current_velocity) < 0)) {
      if (settle_time_ms_out != NULL) {
        *settle_time_ms_out = (uint16_t)DELTA_TIME(start_time);
      }
      return CARBON_RESPONSE_OK;
    }

    last_error = current_error;
    last_velocity = current_velocity;

    k_sleep(K_MSEC(period));

  } while (DELTA_TIME(start_time) < (uint64_t)timeout_ms);
  return CARBON_EPOS_SETTLE_TIMEOUT;
}

static CARBON_RESPONSE_CODE Calibration_Settle(uint8_t node_id, int32_t target_position, uint16_t window,
                                               uint16_t time_window_ms, uint16_t timeout_ms, uint8_t period_ms,
                                               uint16_t *settle_time_ms_out) {
  int64_t start_time = k_uptime_get();
  int64_t current_time = 0;
  int32_t current_position = 0;
  int32_t current_error = 0;
  int32_t current_velocity = 0;

  bool found_first = false;
  int64_t first_in_window_time = 0;

  do {
    RETURN_CODE_IF_NOT_OK(Get_Actual_Position_Velocity(node_id, &current_position, &current_velocity));

    current_error = abs(target_position - current_position);

    if (current_error < window) {
      current_time = k_uptime_get();
      if (!found_first) {
        first_in_window_time = current_time;
        found_first = true;
      } else if (current_time - first_in_window_time > time_window_ms) {
        *settle_time_ms_out = (first_in_window_time - start_time);
        return CARBON_RESPONSE_OK;
      }
    } else {
      found_first = false;
    }
    k_sleep(K_MSEC(period_ms));

  } while ((uint16_t)DELTA_TIME(start_time) < timeout_ms);
  return CARBON_EPOS_SETTLE_TIMEOUT;
}

static CARBON_RESPONSE_CODE Home_From_Params(uint8_t node_id, epos_Home_Params *params, uint16_t settle_timeout_ms,
                                             int32_t *out_limit, uint8_t enc_id) {
  switch (params->which_params) {
  case epos_Home_Params_hard_stop_tag:
    return mc.Hard_Stop_Home(node_id, params->params.hard_stop.step_size, params->params.hard_stop.offset,
                             params->min_position, params->max_position, params->profile_velocity, settle_timeout_ms,
                             out_limit, enc_id, mc.tick_scale);
  case epos_Home_Params_limit_switch_tag:
    return CARBON_EPOS_NOT_SUPPORTED;
  case epos_Home_Params_actual_position_tag:
    return mc.Actual_Position_Home(node_id, params->params.actual_position.range, params->profile_velocity, out_limit,
                                   enc_id, mc.tick_scale);
  default:
    break;
  }
  return CARBON_EPOS_INVALID_HOMING_PARAMS;
}

static CARBON_RESPONSE_CODE handle_can_request(uint8_t node_id, can_open_Request *request, can_open_Reply *out_reply) {
  CARBON_RESPONSE_CODE ret = CARBON_RESPONSE_OK;
  CAN_Open_Message_t msg;
  out_reply->which_reply = can_open_Reply_ack_tag;
  switch (request->which_request) {
  case can_open_Request_sdo_tag:
    Send_SDO(node_id, request->request.sdo.index, request->request.sdo.subindex, request->request.sdo.value,
             request->request.sdo.cs, request->request.sdo.expedited);
    break;
  case can_open_Request_pdo_tag:
    Send_PDO(node_id, request->request.pdo.func, request->request.pdo.data, request->request.pdo.size);
    break;
  case can_open_Request_rtr_tag:
    Send_RTR_PDO(node_id, request->request.rtr.func);
    break;
  case can_open_Request_nmt_tag:
    Send_NMT(node_id, request->request.nmt.state);
    break;
  case can_open_Request_await_tag:
    out_reply->which_reply = can_open_Reply_msg_tag;
    ret = Await_Reply(node_id, &msg, request->request.await.timeout_ms, request->request.await.func);
    if (ret == CARBON_RESPONSE_OK) {
      ret = can_open_to_npb_msg(&msg, &out_reply->reply.msg);
    }
    break;
  case can_open_Request_sdo_download_tag:
    Send_SDO_Download(node_id, (uint16_t)request->request.sdo_download.index,
                      (uint8_t)request->request.sdo_download.subindex, request->request.sdo_download.value);
    break;
  case can_open_Request_sdo_upload_tag:
    out_reply->which_reply = can_open_Reply_msg_tag;
    ret =
        Send_SDO_Upload_Request(node_id, &msg, request->request.sdo_upload.index, request->request.sdo_upload.subindex);
    if (ret == CARBON_RESPONSE_OK) {
      ret = can_open_to_npb_msg(&msg, &out_reply->reply.msg);
    }
    break;
  case can_open_Request_reset_tag:
    ret = NMT_Reset_Node(node_id);
    break;
  case can_open_Request_start_tag:
    ret = NMT_Start_Node(node_id);
    break;
  case can_open_Request_stop_tag:
    ret = NMT_Stop_Node(node_id);
    break;
  default:
    ret = CARBON_EPOS_ERROR_UNKNOWN;
    break;
  }
  if (ret != CARBON_RESPONSE_OK) {
    out_reply->which_reply = epos_Reply_error_tag;
    out_reply->reply.error.code = ret;
  }
  return CARBON_RESPONSE_OK;
}

static CARBON_RESPONSE_CODE Handle_Request(uint8_t node_id, epos_Request *request, epos_Reply *out_reply,
                                           uint8_t enc_id) {
  CARBON_RESPONSE_CODE ret = CARBON_RESPONSE_OK;
  switch (request->which_request) {
  case epos_Request_can_tag:
    out_reply->which_reply = epos_Reply_can_tag;
    ret = handle_can_request(node_id, &request->request.can, &out_reply->reply.can);
    break;
  case epos_Request_setup_pdos_tag:
    out_reply->which_reply = epos_Reply_ack_tag;
    ret = mc.Setup_PDOs(node_id);
    break;
  case epos_Request_enable_tag:
    out_reply->which_reply = epos_Reply_ack_tag;
    ret = mc.Enable_Node(node_id);
    break;
  case epos_Request_disable_tag:
    out_reply->which_reply = epos_Reply_ack_tag;
    ret = mc.Disable_Node(node_id);
    break;
  case epos_Request_home_tag:
    out_reply->which_reply = epos_Reply_limit_tag;
    ret = mc.Home_From_Params(node_id, &request->request.home.params, MC_DEFAULT_SETTLE_TIMEOUT_MS,
                              &out_reply->reply.limit.limit, enc_id);
    break;
  case epos_Request_settle_tag:
    out_reply->which_reply = epos_Reply_settle_tag;
    ret = mc.Await_Settling(node_id, request->request.settle.target_position, request->request.settle.window,
                            request->request.settle.timeout_ms, (uint16_t *)&out_reply->reply.settle.duration);
    break;
  case epos_Request_pos_vel_tag:
    out_reply->which_reply = epos_Reply_pos_vel_tag;
    ret = mc.Get_Actual_Position_Velocity(node_id, &out_reply->reply.pos_vel.position,
                                          &out_reply->reply.pos_vel.velocity);
    break;
  case epos_Request_go_to_tag:
    out_reply->which_reply = epos_Reply_settle_tag;
    ret = mc.Go_To_Position(node_id, request->request.go_to.position, request->request.go_to.velocity);
    if (ret == CARBON_RESPONSE_OK && request->request.go_to.timeout_ms != 0) {
      ret = mc.Await_Settling(node_id, request->request.go_to.position, request->request.go_to.window,
                              request->request.go_to.timeout_ms, (uint16_t *)&out_reply->reply.settle.duration);
    }
    break;
  case epos_Request_status_tag:
    out_reply->which_reply = epos_Reply_ack_tag;
    ret = mc.Await_Status(node_id, request->request.status.timeout_ms, request->request.status.expected,
                          request->request.status.expected_neg);
    break;
  case epos_Request_positional_pid_tag:
    ret = CARBON_EPOS_NOT_SUPPORTED;
    break;
  case epos_Request_pid_tag:
    ret = CARBON_EPOS_NOT_SUPPORTED;
    break;
  case epos_Request_pid_v2_tag:
    out_reply->which_reply = epos_Reply_ack_tag;
    ret = mc.handle_pid(node_id, &request->request.pid_v2);
    break;
  case epos_Request_get_pid_tag:
    out_reply->which_reply = epos_Reply_pid_tag;
    mc.handle_get_pid(node_id, &out_reply->reply.pid);
    break;
  default:
    ret = CARBON_EPOS_ERROR_UNKNOWN;
    break;
  }
  if (ret != CARBON_RESPONSE_OK) {
    out_reply->which_reply = epos_Reply_error_tag;
    out_reply->reply.error.code = ret;
  }

  return CARBON_RESPONSE_OK;
}

static CARBON_RESPONSE_CODE Home_With_Method(uint8_t node_id, uint32_t method) {
  CAN_Open_Message_t msg;
  CARBON_RESPONSE_CODE result = CARBON_RESPONSE_OK;
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Homing_Method(node_id, method))
  RETURN_CODE_IF_NOT_OK(CIA402_Read_Homing_Method(node_id, &msg))
  RETURN_CODE_IF_NOT_OK(
      CIA402_Write_Control_Word(node_id, CIA402_CONTROL_START_OPERATION | CIA402_CONTROL_COMMAND_SWITCH_ON_ENABLE))

  result = mc.Await_Status_PDO(node_id, &msg, 3000, CIA402_STATUS_SETPOINT_AWKNOWLEDGED, 0x00);
  if (result != CARBON_RESPONSE_OK) {
    CIA402_Read_Status_Word(node_id, &msg);
    // This is an error case where we can't find the switch (Started already past the switch)
    // We halt the homing to avoid having the servo apply pressure against the hard stop
    // We then reboot the node, ready to receive new commands
    LOG_ERR("node %d: waiting for %u got %u", node_id, CIA402_STATUS_SETPOINT_AWKNOWLEDGED,
            (uint32_t)*msg.pkt.sdo.data);
  }

  // We need to reset the control word after homing is done
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Control_Word(node_id, CIA402_CONTROL_COMMAND_SWITCH_ON_ENABLE |
                                                               CIA402_CONTROL_CHANGE_SET_IMMEDIATELY))
  return result;
}
static CARBON_RESPONSE_CODE set_inverted(uint8_t node_id, uint8_t inverted) {
  if (inverted) {
    return CARBON_EPOS_NOT_SUPPORTED;
  }
  return CARBON_RESPONSE_OK;
}

void MC_Init(uint32_t tick_scale) {
  mc.Await_Status = &Await_Status;
  mc.Await_Status_PDO = &Await_Status_PDO;
  mc.Control_Word_Await_Status = &Control_Word_Await_Status;
  mc.Enable_Node = &Enable_Node;
  mc.Disable_Node = &Disable_Node;
  mc.Setup_PDOs = &Setup_PDOs;
  mc.Go_To_Position = &Go_To_Position;
  mc.Get_Actual_Position_Velocity = &Get_Actual_Position_Velocity;
  mc.Await_Target_Reached = &Await_Target_Reached;
  mc.Await_Settling = &Await_Settling;
  mc.Calibration_Settle = &Calibration_Settle;
  mc.Home_From_Params = &Home_From_Params;
  mc.Handle_Request = &Handle_Request;
  mc.Home_With_Method = &Home_With_Method;
  mc.tick_scale = tick_scale;
  mc.set_inverted = &set_inverted;
#ifdef USE_MAXON
  // MAXON EPOS 4 controller
  LOG_INF("using maxon");
  mc.Hard_Stop_Home = &EPOS_Hard_Stop_Home;
  mc.Actual_Position_Home = &EPOS_Actual_Position_Home;
  mc.set_pid = &EPOS_set_pid;
  mc.handle_pid = &EPOS_handle_pid;
  mc.handle_get_pid = &EPOS_handle_get_pid;
  mc.velocity_scaler = 1;
#endif
#ifdef USE_FAULHABER
  // Faulhaber 3001P (modified) controller
  LOG_INF("using faulhaber");
  mc.Hard_Stop_Home = &FH_Hard_Stop_Home;
  mc.Actual_Position_Home = &FH_Actual_Position_Home;
  mc.set_pid = &FH_set_pid;
  mc.handle_pid = &FH_handle_pid;
  mc.handle_get_pid = &FH_handle_get_pid;
  mc.velocity_scaler = 4; // currently cannot have milli-rpm for faulhaber smallest we can get is 0.004 rpms
  mc.set_inverted = &FH_set_inverted;
#endif

  // Status PDO reporting is enabled by default for new Slayer and Reaper scanners only
#if USE_REAPER || CONFIG_BOARD_SCANNER_H753_SLAYER
  mc.statusPdoEnabled = true;
#else
  mc.statusPdoEnabled = false;
#endif
}

MotionController *MC_get() { return &mc; }

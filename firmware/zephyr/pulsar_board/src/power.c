#include <logging/log.h>
LOG_MODULE_REGISTER(power, CONFIG_APP_LOG_LEVEL);

#include <device.h>
#include <devicetree.h>
#include <drivers/regulator.h>
#include <stdint.h>
#include <sys/atomic.h>

#include <utils/handle_errors.h>

#include "power.h"

static int update_state();

/// Current (actually written to regulators) power output state
__dtcm_data_section static atomic_t gActualState = ATOMIC_INIT(0);
/// Desired power output state
__dtcm_data_section static atomic_t gDesiredState = ATOMIC_INIT(0);

// laser firing board +24V
#if DT_NODE_EXISTS(DT_PATH(firing_board, power))
static const struct device *gFiringBoard = DEVICE_DT_GET(DT_PATH(firing_board, power));
#else
static const struct device *gFiringBoard = NULL;
#endif

// predict camera +24V
#if DT_NODE_EXISTS(DT_PATH(target_cam, power))
static const struct device *gTargetCam = DEVICE_DT_GET(DT_PATH(target_cam, power));
#else
static const struct device *gTargetCam = NULL;
#endif

/**
 * @brief Initialize the power output control
 *
 * This sets up all the regulators specified in device tree. They will all be off by default.
 */
int power_init() {
  atomic_clear(&gActualState);
  atomic_clear(&gDesiredState);

  if (gFiringBoard) {
    HANDLE_UNLIKELY(!device_is_ready(gFiringBoard));
  }
  if (gTargetCam) {
    HANDLE_UNLIKELY(!device_is_ready(gTargetCam));
  }

  return 0;
}

/**
 * @brief Get the actual state of switched power rails
 */
int power_get_state(power_channel_t *outState) {
  if (!outState) {
    return -EFAULT;
  }

  *outState = atomic_get(&gActualState);

  return 0;
}

/**
 * @brief Enable and/or disable power channels
 */
int power_update(const power_channel_t enable, const power_channel_t disable) {
  atomic_or(&gDesiredState, enable);
  atomic_and(&gDesiredState, ~disable);

  return update_state();
}

/**
 * @brief Power manager callback when rail is enabled
 *
 * This doesn't do anything
 */
static void regulator_callback(struct onoff_manager *, struct onoff_client *, uint32_t, int) {}

/**
 * @brief Update the physical state of output lines
 *
 * Determine which IOs changed and then update them synchronously
 */
static int update_state() {
  const power_channel_t state = atomic_get(&gDesiredState), actualState = atomic_get(&gActualState),
                        changed = state ^ actualState;

  LOG_DBG("power update: %04x (%04x -> %04x)", changed, actualState, state);

  uintptr_t irqFlag = irq_lock();
  {
    if (gTargetCam && (changed & kPowerTargetCam)) {
      __dtcm_bss_section static struct onoff_client cb;
      memset(&cb, 0, sizeof(cb));

      sys_notify_init_callback(&cb.notify, regulator_callback);

      if (state & kPowerTargetCam) {
        HANDLE_UNLIKELY(regulator_enable(gTargetCam, &cb));
      } else {
        HANDLE_UNLIKELY(regulator_disable(gTargetCam));
      }
    }

    if (gFiringBoard && (changed & kPowerFiringBoard)) {
      __dtcm_bss_section static struct onoff_client cb;
      memset(&cb, 0, sizeof(cb));

      sys_notify_init_callback(&cb.notify, regulator_callback);

      if (state & kPowerFiringBoard) {
        HANDLE_UNLIKELY(regulator_enable(gFiringBoard, &cb));
      } else {
        HANDLE_UNLIKELY(regulator_disable(gFiringBoard));
      }
    }

    atomic_set(&gActualState, state);
  }
  irq_unlock(irqFlag);

  return 0;
}

menuconfig APP_UDP_PORT
    int "UDP server port"
    range 1 65535

menuconfig PB_REQUESTS_BUFFER_SIZE
    int "Number elements to allow in queue at once"
    range 1 15

menuconfig PREFER_MAX14574_DRIVER
    bool "Try to use the MAX14574 chip as primary LL driver"
    default y

menuconfig LOG_BWT_UART
    bool "Output debug logging for all BWT laser UART messages"
    default n
    depends on BOARD_SCANNER_H753

menuconfig BWT_UART_FIRING
    bool "Use UART to fire BWT (for testing)"
    default n
    depends on BOARD_SCANNER_H753

module = APP
module-str = APP
source "subsys/logging/Kconfig.template.log_config"

source "Kconfig.zephyr"

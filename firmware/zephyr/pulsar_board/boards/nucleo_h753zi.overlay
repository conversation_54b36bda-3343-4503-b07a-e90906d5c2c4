#include <zephyr/dt-bindings/adc/adc.h>

/ {
    chosen {
        zephyr,code-partition = &slot0_partition;
        zephyr,can-primary = &spi_can;
    };

    aliases {
        watchdog0 = &iwdg1;
        qdec64k-pan = &qdec64k_pan;
        qdec64k-tilt = &qdec64k_tilt;
        zlcb0 = &zlcb0;
        eeprom0 = &eeprom0;
        laser = &laser;
    };

    gpios {
        compatible = "gpio-keys";
        pps_in {
            gpios = <&gpiod 13 GPIO_ACTIVE_HIGH>;
            label = "PPS_IN";
        };
    };

    lpm: lpm {
        compatible = "cr,therm-laser-power-meter";
        status-in-gpios = <&gpioc 9 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;

        io-channels = <&adc3 3>, <&adc3 7>, <&adc3 2>;
        io-channel-names = "current", "therm1", "therm2";
    };

    // laser control interface
    laser: laser {
        compatible = "cr,laser-interface";

        power-meter = <&lpm>;

        fire-gpios = <&gpioc 0 GPIO_ACTIVE_HIGH>;
        pwms = <&laser_pwm_intensity 1 PWM_KHZ(20) PWM_POLARITY_NORMAL>;
        pwm-names = "intensity";
    };
};

&iwdg1 {
    status = "okay";
};

&spi1 {
    pinctrl-0 = <&spi1_sck_pa5 &spi1_miso_pa6 &spi1_mosi_pb5 &spi1_nss_pa4>;
    pinctrl-names = "default";
    cs-gpios = <&gpioa 4 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
    status = "okay";
    spi_can: mcp2515@0 {
        compatible = "microchip,mcp2515";
        spi-max-frequency = < 4000000 >;
        int-gpios = <&gpioa 10 GPIO_ACTIVE_LOW>;
        status = "okay";
        label = "SPI_CAN";
        reg = < 0x0 >;
        osc-freq = <16000000>;
        bus-speed = < 1000000 >;
        sjw = < 0x1 >;
        prop-seg = < 1 >;
        phase-seg1 = < 4 >;
        phase-seg2 = < 2 >;
        #address-cells = < 0x1 >;
        #size-cells = < 0x0 >;
    };
};

&can1 {
    status = "disabled";
};

&timers16 {
    status = "okay";
    st,prescaler = <119>;
    laser_pwm_intensity: pwm {
        status = "okay";
        pinctrl-0 = <&tim16_ch1_pf6>;
        pinctrl-names = "default";
    };
};

&timers3 {
    status = "okay";
    qdec64k_pan: qdec64k {
        compatible = "st,stm32-qdec64k";
        status = "okay";
        label = "QDEC64K_PAN";
        pinctrl-0 = <&tim3_ch1_pc6 &tim3_ch2_pc7>;
        pinctrl-names = "default";
    };
};

&timers4 {
    status = "okay";
    qdec64k_tilt: qdec64k {
        compatible = "st,stm32-qdec64k";
        status = "okay";
        label = "QDEC64K_tilt";
        pinctrl-0 = <&tim4_ch1_pb6 &tim4_ch2_pb7>;
        pinctrl-names = "default";
    };
};

&timers8 {
    status = "okay";
    st,prescaler = <119>;
    zlcb0: zlcb {
        compatible = "st,stm32-zlcb";
        status = "okay";
        label = "ZLCB0";
    };
};
&adc3 {
    status = "okay";
    pinctrl-0 = <&adc3_inp3_pf7 &adc3_inp7_pf8 &adc3_inp2_pf9>;
    pinctrl-names = "default";
    #address-cells = <1>;
    #size-cells = <0>;

    // LPSU current
    channel@3 {
        reg = <3>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME_DEFAULT>;
        zephyr,resolution = <12>;
    };

    // thermistor 1
    channel@7 {
        reg = <7>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME_DEFAULT>;
        zephyr,resolution = <12>;
    };

    // thermistor 2
    channel@2 {
        reg = <2>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME_DEFAULT>;
        zephyr,resolution = <12>;
    };
};

&i2c4 {
    /* ATMEL AT24C04D */
    status = "okay";
    clock-frequency = <I2C_BITRATE_FAST>;
    pinctrl-0 = <&i2c4_sda_pf15 &i2c4_scl_pf14>;
    pinctrl-names = "default";
    eeprom0: atmel_at24@a0 {
        compatible = "atmel,at24";
        reg = <0x50>;
        /**
         * Whole EEPROM is 512 bytes, but address 0x50 will only access half of it.
         * Use address 0x51 to access the other half.
         */
        size = <256>;
        pagesize = <16>;
        address-width = <8>;
        timeout = <5>;
        label = "ATMEL_AT24";
    };
};

&i2c2 {
    status = "okay";
    clock-frequency = <I2C_BITRATE_STANDARD>;
    pinctrl-0 = <&i2c2_sda_pb11 &i2c2_scl_pb10>;
    pinctrl-names = "default";
    ll_max14574: ll_max1457@77 {
        compatible = "eo,liquid-lens-max14574";
        reg = < 0x77 >; /*EE >> */
        status = "okay";
    };
    ll_max14515: ll_max1415@76 {
        compatible = "eo,liquid-lens-max14515";
        reg = < 0x76 >; /* EC >> 1 */
        status = "okay";
    };
};
&uart4 {
    pinctrl-0 = <&uart4_rx_pb8 &uart4_tx_pb9>;
    pinctrl-names = "default";
    current-speed = <115200>;
    status = "okay";
};
&clk_hse {
    /delete-property/ hse-bypass;
};

&usart3 {
    status = "disabled";
};

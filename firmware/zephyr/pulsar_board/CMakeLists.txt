cmake_minimum_required(VERSION 3.13.1)

find_package(Zephyr REQUIRED HINTS $ENV{ZEPHYR_BASE})
project(pulsar_board)

# build protobuf defs separately
set(ROOT_PATH ../../..)
set(GENERATED_PROTO_PATH ${ROOT_PATH}/generated/lib/drivers/nanopb/proto)
file(GLOB PROTO_SOURCES CONFIGURE_DEPENDS ${GENERATED_PROTO_PATH}/*.c)
file(GLOB PROTO_HEADERS CONFIGURE_DEPENDS ${GENERATED_PROTO_PATH}/*.h)

zephyr_library_named(protobufs)
target_sources(protobufs PRIVATE
    ${PROTO_SOURCES} ${PROTO_HEADERS}
)
target_include_directories(protobufs PUBLIC ${ROOT_PATH})

# build app
target_sources(app PRIVATE
    src/can_open_msg.h
    src/can_open.c
    src/can_open.h
    src/cia_402.c
    src/cia_402.h
    src/gimbal_handler.c
    src/gimbal_handler.h
    src/laser.c
    src/laser.h
    src/lens_handler.c
    src/lens_handler.h
    src/main.c
    src/motion_controller.c
    src/motion_controller.h
    src/nanopb_server.c
    src/nanopb_server.h
    src/power.c
    src/power.h
    src/qdec.c
    src/qdec.h
    src/servo.c
    src/servo.h
    src/specific_controller.h
    src/watchdawg.c
    src/watchdawg.h
    src/sensors.c
    src/sensors.h
    src/laser/nanopb.c
    src/laser/nanopb.h
    src/laser/config.c
    src/laser/internal.h
)

target_include_directories(app PRIVATE src)
target_compile_options(app PRIVATE -Wno-unused-function -Wno-unused-variable -Wno-implicit-function-declaration)
target_link_libraries(app PRIVATE protobufs)

# zephyr-cli passes compile options as part of the CFLAGS, so to check if a particular option is
# set, we check if OPTION=1 is present in CFLAGS.

# include either the Faulhaber or Maxon motor control stuff
if("${CMAKE_C_FLAGS}" MATCHES "USE_FAULHABER=1")
    target_sources(app PRIVATE
        src/drive/faulhaber/faulhaber.h
        src/drive/faulhaber/fh_controls.c
        src/drive/faulhaber/fh_controls.h
        src/drive/faulhaber/fh_config.c
        src/drive/faulhaber/fh_config.h
        src/drive/faulhaber/ambient_temp.c
        src/drive/faulhaber/ambient_temp.h
    )
elseif("${CMAKE_C_FLAGS}" MATCHES "USE_MAXON=1")
    target_sources(app PRIVATE
        src/drive/epos/epos.c
        src/drive/epos/epos.h
        src/drive/epos/epos_controls.c
        src/drive/epos/epos_controls.h
    )
endif()

# reaper-specific sources
if("${CMAKE_C_FLAGS}" MATCHES "USE_REAPER=1")
    target_sources(app PRIVATE
        src/laser/reaper.c
        src/laser/reaper.h
        src/laser/bwt.c
        src/laser/bwt.h
        src/laser/bwt_transport.c
        src/laser/bwt_transport.h
        src/laser/bwt_nanopb.c
        src/laser/jlight.c
        src/laser/jlight.h
        src/laser/jlight_transport.c
        src/laser/jlight_transport.h
        src/laser/jlight_nanopb.c
    )
endif()

# use new EEPROM on Reaper (and future Slayer scanners) with H753
if("${CMAKE_C_FLAGS}" MATCHES "USE_NEW_EEPROM=1")
    target_sources(app PRIVATE
        src/eeprom_new.c
        src/eeprom_new.h
    )
# use old EEPROM (external I2C) implementation otherwise
else()
    target_sources(app PRIVATE
        src/eeprom.c
        src/eeprom.h
    )
endif()

####### Link against nanoMODBUS package for Reaper scanners
if("${CMAKE_C_FLAGS}" MATCHES "USE_REAPER=1")
    target_link_libraries(app PRIVATE nanomodbus)
endif()

# Enable networking
CONFIG_NETWORKING=y
CONFIG_NET_IPV4=y
CONFIG_NET_IPV6=n
CONFIG_NET_TCP=n
CONFIG_NET_UDP=y
CONFIG_NET_ARP_TABLE_SIZE=24
CONFIG_NET_SOCKETS=y
CONFIG_NET_SOCKETS_POSIX_NAMES=y
CONFIG_NET_LOG=y
CONFIG_NET_PKT_TX_COUNT=32
CONFIG_NET_BUF_TX_COUNT=64
CONFIG_NET_PKT_RX_COUNT=64
CONFIG_NET_BUF_RX_COUNT=256
CONFIG_NET_L2_ETHERNET_MGMT=y

# accept broadcast pings
CONFIG_NET_ICMPV4_ACCEPT_BROADCAST=y

# Network settings
CONFIG_NET_CONFIG_SETTINGS=y
CONFIG_NET_CONFIG_NEED_IPV4=y
CONFIG_NET_CONFIG_MY_IPV4_ADDR="*********"
CONFIG_NET_CONFIG_PEER_IPV4_ADDR="*********"
CONFIG_NET_CONFIG_MY_IPV4_NETMASK="*************"

# Don't wait for link up to start running
CONFIG_NET_CONFIG_INIT_TIMEOUT=-1
CONFIG_NET_CONNECTION_MANAGER=y
CONFIG_NET_BUF_USER_DATA_SIZE=64

# Port
CONFIG_APP_UDP_PORT=4243

# Syslog
CONFIG_LOG=y
CONFIG_LOG_BACKEND_NET=y
CONFIG_LOG_BACKEND_NET_SERVER="*********:2442"

# Support reboot
CONFIG_REBOOT=y

# Watchdog
CONFIG_LIB_WATCHDOG=y
CONFIG_WATCHDOG=y
CONFIG_WDT_DISABLE_AT_BOOT=n

# drivers used for all types of boards
CONFIG_PINCTRL=y
CONFIG_GPIO=y
CONFIG_ADC=y
CONFIG_CAN=y
CONFIG_I2C=y
CONFIG_FLASH=y
CONFIG_PWM=y
CONFIG_SENSOR=y

# CAN SPI (via MCP2515)
CONFIG_SPI=y
CONFIG_CAN_MCP2515=y
# Initialize CAN after SPI
CONFIG_CAN_INIT_PRIORITY=80

#UDP
CONFIG_LIB_UDP=y
CONFIG_PB_REQUESTS_BUFFER_SIZE=10
# Nanopb
CONFIG_NANOPB=y
# PTP
CONFIG_LIB_PTP=y

CONFIG_LIB_GPIO_IP=y

# Quadrature decoders
CONFIG_QDEC64K=y

#History list for encoders
CONFIG_LIB_HISTORY_LIST=y

# Zero latency callbacks
CONFIG_ZLCB=y

CONFIG_LASER_POWER_METER_THERM=y
CONFIG_LASER_POWER_METER=y

CONFIG_LIQUID_LENS_MAX14515=y
CONFIG_LIQUID_LENS_MAX14574=y
CONFIG_LIQUID_LENS=y

# MCUBoot
CONFIG_BOOTLOADER_MCUBOOT=y

# Enable mcumgr.
CONFIG_MCUMGR=y
CONFIG_MCUMGR_SMP_NOTIFY_CALLBACK=y
CONFIG_LIB_SMP=y
CONFIG_MCUBOOT_BOOT_MAX_ALIGN=32
CONFIG_MCUMGR_BUF_COUNT=4
CONFIG_MCUMGR_BUF_USER_DATA_SIZE=24

# Some command handlers require a large stack.
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=2048

# Enable statistics and statistic names.
CONFIG_STATS=y
CONFIG_STATS_NAMES=y

# Enable most core commands.
CONFIG_MCUMGR_CMD_IMG_MGMT=y
CONFIG_MCUMGR_CMD_OS_MGMT=y
CONFIG_MCUMGR_CMD_STAT_MGMT=y
CONFIG_MCUMGR_SMP_UDP=y
CONFIG_MCUMGR_SMP_UDP_IPV4=y

# Required by the `taskstat` command.
CONFIG_THREAD_MONITOR=y

# use newlib (for more math stuff)
CONFIG_NEWLIB_LIBC=y
CONFIG_NEWLIB_LIBC_NANO=y

# enable printing floating point numbers
CONFIG_CBPRINTF_FP_SUPPORT=y

# EEPROM
CONFIG_EEPROM=y
CONFIG_EEPROM_AT24=y

CONFIG_PREFER_MAX14574_DRIVER=y

CONFIG_ADC_SHELL=n
CONFIG_FLASH_SHELL=n
CONFIG_EEPROM_SHELL=n
CONFIG_PWM_SHELL=n

CONFIG_LOG_BACKEND_UART=n
CONFIG_LOG_BACKEND_UART_ASYNC=n
CONFIG_UART_CONSOLE=n

CONFIG_LOG_TIMESTAMP_64BIT=y

# SEGGER RTT
CONFIG_USE_SEGGER_RTT=y
CONFIG_CONSOLE=n

# build fix for the overlay-based H7xx boards
# TODO: split those to use their own dts
CONFIG_QDEC64K_STM32=y
CONFIG_ZLCB_STM32=y

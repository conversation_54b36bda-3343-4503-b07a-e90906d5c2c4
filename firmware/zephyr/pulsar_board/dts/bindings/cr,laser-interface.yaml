description: Scanner board laser interface pseudo-device

compatible: "cr,laser-interface"

include: [base.yaml]
properties:
  fire-gpios:
    required: true
    type: phandle-array
    description: GPIO to assert to fire the laser
  power-meter:
    required: true
    type: phandle
    description: Laser power meter device (for thermistor, photodiode, etc.)
  pwms:
    required: false
    type: phandle-array
    specifier-space: pwm
    description: PWM device and channel to use for setting laser intensity
  pwm-names:
    required: false
    type: string-array
    description: "PWM channel names, supported options are: `intensity`"
  laser-uart:
    required: false
    type: phandle
    description: UART device used to communicate with "smart" laser

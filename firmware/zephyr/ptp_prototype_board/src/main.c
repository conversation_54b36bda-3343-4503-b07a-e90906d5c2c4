#include <logging/log.h>
LOG_MODULE_REGISTER(main, CONFIG_APP_LOG_LEVEL);

#include <lib/ptp/ptp.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

void main() {
  struct net_if *iface = net_if_get_default();

  /* Master */
  HANDLE_CRITICAL(ptp_master_init(iface));
  struct net_ptp_time ts;
  ts.second = 1600000000;
  ts.nanosecond = 0;
  HANDLE_CRITICAL(ptp_master_clk_set(&ts));
  HANDLE_CRITICAL(ptp_master_start());

  /* Slave */
  /*
  HANDLE_CRITICAL(ptp_slave_init(iface, 0));
  HANDLE_CRITICAL(ptp_slave_start());
  */
}

///dts-v1/;
//#include <dt-bindings/pinctrl/gd32f450z(e-g-i-k)xx-pinctrl.h>
#include <st/h7/stm32h753Xi.dtsi>
#include <st/h7/stm32h753zitx-pinctrl.dtsi>

&pinctrl {
    can1_default: can1_default {
        group1 {
            pinmux = <CAN1_RX_PA11>, <CAN1_TX_PA12>;
        };
    };

    can2_default: can2_default {
        group1 {
            pinmux = <CAN2_RX_PB12>, <CAN2_TX_PB13>;
        };
    };

    spi1_default: spi1_default {
        group1 {
            pinmux = <SPI1_NSS_PG10>, <SPI1_MISO_PG9>,
                <SPI1_MOSI_PD7>, <SPI1_SCK_PG11>;
        };
    };

    spi2_default: spi2_default {
        group1 {
            pinmux = <SPI2_NSS_PB9>, <SPI2_MISO_PB14>,
                <SPI2_MOSI_PB15>, <SPI2_SCK_PB10>;
        };
    };

    spi3_default: spi3_default {
        group1 {
            pinmux = <SPI3_NSS_PA6>, <SPI3_MISO_PC11>,
                <SPI3_MOSI_PB2>, <SPI3_SCK_PC10>;
        };
    };

    spi4_default: spi4_default {
        group1 {
            pinmux = <SPI4_NSS_PE4>, <SPI4_MISO_PE5>,
                <SPI4_MOSI_PE6>, <SPI4_SCK_PE2>;
        };
    };

    enet_default: enet_default {
        group1 {
            pinmux = <ETH_RMII_REF_CLK_PA1>, <ETH_MDIO_PA2>,
                 <ETH_RMII_CRS_DV_PA7>, <ETH_MDC_PC1>,
                 <ETH_RMII_RXD0_PC4>, <ETH_RMII_RXD1_PC5>,
                 <ETH_RMII_TX_EN_PB11>, <ETH_RMII_TXD0_PG13>,
                 <ETH_RMII_TXD1_PG12>;
        };
    };

    tim1_ch1_pe9: tim1_ch1_pe9 {
        pins {
            pinmux = <TIM1_CH1_PE9>
            bias-disable;
            drive-push-pull;
            slew-rate = "very-high-speed";
        };
    };

    /**
    pwm0_default: pwm0_default {
        group1 {
            pinmux = <TIMER0_CH0_PA8>;
        };
    };
    */
};

source [find interface/jlink.cfg]
transport select swd

set CHIPNAME stm32h753zi
set CPUTAPID 0x790007a3

source [find target/stm32h7x.cfg]

reset_config trst_and_srst separate

$_TARGETNAME configure -event gdb-attach {
	echo "Debugger attaching: halting execution"
	reset halt
	gdb_breakpoint_override hard
}

$_TARGETNAME configure -event gdb-detach {
	echo "Debugger detaching: resuming execution"
	resume
}
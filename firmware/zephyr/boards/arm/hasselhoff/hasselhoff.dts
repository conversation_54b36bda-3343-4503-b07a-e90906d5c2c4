/dts-v1/;
#include <st/h7/stm32h753Xi.dtsi>
#include <st/h7/stm32h753zitx-pinctrl.dtsi>

#include <zephyr/dt-bindings/adc/adc.h>
#include <mem.h>

//#include "hasselhoff-pinctrl.dtsi"

/ {
    model = "Hasselhoff Board";
    compatible = "carbon,hasselhoff";

    chosen {
        zephyr,sram = &sram0;
        zephyr,flash = &flash0;
        zephyr,dtcm = &dtcm;
        zephyr,code-partition = &slot0_partition;
    };

    aliases {
        watchdog0 = &iwdg1;
        zlcb0 = &zlcb0;
        pwm1 = &pwm1;
        sx1509b = &sx1509b;
        drv3946 = &drv3946;
        ccan = &can1;
        tcan = &can2;
    };
    emu_eeprom: emu_eeprom {
        status = "okay";
        compatible = "zephyr,emu-eeprom";
        label = "emulated eeprom";

        size = <DT_SIZE_K(4)>;
        pagesize = <DT_SIZE_K(8)>;
        partition = <&storage_partition>;

        // shadow in RAM; also delay erase until entire block is used
        rambuf;
        partition-erase;
    };

    gpios {
        compatible = "gpio-keys";
        led_green {
            label = "LED_GREEN";
            gpios = <&gpiog 1 GPIO_ACTIVE_HIGH>;
        };
        led_blue {
            label = "LED_BLUE";
            gpios = <&gpiof 15 GPIO_ACTIVE_HIGH>;
        };
        led_red {
            label = "LED_RED";
            gpios = <&gpiog 0 GPIO_ACTIVE_HIGH>;
        };

        // Armrest connection
        can1_silent {
            label = "CAN1_SILENT";
            gpios = <&gpioa 8 GPIO_ACTIVE_HIGH>;
        };
        can2_silent {
            label = "CAN2_SILENT";
            gpios = <&gpiob 14 GPIO_ACTIVE_HIGH>;
        };
        armrest_5v_out {
            label = "ARMREST_5V_OUT";
            gpios = <&gpioc 9 GPIO_ACTIVE_HIGH>;
        };
        armrest_12v_out1 {
            label = "ARMREST_12V_OUT1";
            gpios = <&gpioc 6 GPIO_ACTIVE_HIGH>;
        };
        armrest_12v_out2 {
            label = "ARMREST_12V_OUT2";
            gpios = <&gpioc 7 GPIO_ACTIVE_HIGH>;
        };

        // Roof box connection
        contactor {
            label = "CONTACTOR";
            gpios = <&gpioe 4 GPIO_ACTIVE_HIGH>;
        };
        radar1 {
            label = "SENSOR1";
            gpios = <&gpiod 15 GPIO_ACTIVE_LOW>;
        };
        radar2 {
            label = "SENSOR2";
            gpios = <&gpiod 14 GPIO_ACTIVE_LOW>;
        };
        bumper1 {
            label = "SENSOR3";
            gpios = <&gpiod 13 GPIO_ACTIVE_LOW>;
        };
        bumper2 {
            label = "SENSOR4";
            gpios = <&gpiod 12 GPIO_ACTIVE_LOW>;
        };
        roof_beacon_right {
            label = "ROOF_BEACON_RIGHT";
            gpios = <&gpiof 3 GPIO_ACTIVE_HIGH>;
        };
        roof_beacon_left {
            label = "ROOF_BEACON_LEFT";
            gpios = <&gpiof 2 GPIO_ACTIVE_HIGH>;
        };
        roof_fan {
            label = "ROOF_FAN";
            gpios = <&gpiof 4 GPIO_ACTIVE_HIGH>;
        };
        roof_light {
            label = "ROOF_LIGHT";
            gpios = <&gpiof 5 GPIO_ACTIVE_HIGH>;
        };

        // Auxillary connection
        aux_interrupt {
            label = "AUX_INTERRUPT";
            gpios = <&gpiob 8 GPIO_ACTIVE_HIGH>;
        };
        lh_park {
            label = "LH_PARK";
            gpios = <&gpiob 9 GPIO_ACTIVE_LOW>;
        };
        ignition_off {
            label = "IGNITION_OFF";
            gpios = <&gpiob 5 GPIO_ACTIVE_HIGH>;
        };

        // Cab box connection
        control_active_led {
            label = "CONTROL_ACTIVE_LED";
            gpios = <&gpioe 2 GPIO_ACTIVE_HIGH>;
        };
        safety_sensor_led {
            label = "SAFETY_SENSOR_LED";
            gpios = <&gpioe 0 GPIO_ACTIVE_HIGH>;
        };
        error_led {
            label = "ERROR_LED";
            gpios = <&gpioe 1 GPIO_ACTIVE_HIGH>;
        };
        buzzer {
            label = "BUZZER";
            gpios = <&gpioe 9 GPIO_ACTIVE_HIGH>;
        };
        lockout_switch {
            label = "LOCKOUT_SWITCH";
            gpios = <&gpioe 3 GPIO_ACTIVE_HIGH>;
        };

        // Steering
        drv3946_en1 {
            label = "DRV3946_EN1";
            gpios = <&gpiof 13 GPIO_ACTIVE_HIGH>;
        };
        drv3946_en2 {
            label = "DRV3946_EN2";
            gpios = <&gpiof 14 GPIO_ACTIVE_HIGH>;
        };
        drv3946_brakes_en1 {
            label = "DRV3946_BRAKES_EN1";
            gpios = <&gpioa 5 GPIO_ACTIVE_HIGH>;
        };
        drv3946_brakes_en2 {
            label = "DRV3946_BRAKES_EN2";
            gpios = <&gpiog 15 GPIO_ACTIVE_HIGH>;
        };
        steering_enable_valve_in {
            label = "STEERING_ENABLE_VALVE_IN";
            gpios = <&gpiof 8 GPIO_ACTIVE_HIGH>;
        };

        // Hydraulic connection
        hydraulic_relay1 {
            label = "HYDRAULIC_RELAY1";
            gpios = <&gpioe 10 GPIO_ACTIVE_HIGH>;
        };
        hydraulic_relay2 {
            label = "HYDRAULIC_RELAY2";
            gpios = <&gpioe 11 GPIO_ACTIVE_HIGH>;
        };
        hydraulic_relay3 {
            label = "HYDRAULIC_RELAY3";
            gpios = <&gpioe 12 GPIO_ACTIVE_HIGH>;
        };
        hydraulic_relay4 {
            label = "HYDRAULIC_RELAY4";
            gpios = <&gpioe 13 GPIO_ACTIVE_HIGH>;
        };

        // Safety circuit
        safety_mosfet {
            label = "SAFETY_MOSFET";
            gpios = <&gpioe 5 GPIO_ACTIVE_HIGH>;
        };
        estop1_sense {
            label = "ESTOP1_SENSE";
            gpios = <&gpioe 7 GPIO_ACTIVE_LOW>;
        };
        estop2_sense {
            label = "ESTOP2_SENSE";
            gpios = <&gpioe 8 GPIO_ACTIVE_LOW>;
        };
        estop3_sense {
            label = "ESTOP3_SENSE";
            gpios = <&gpioe 6 GPIO_ACTIVE_LOW>;
        };

        spi3_cs {
            label = "SPI3_CS";
            gpios = <&gpioa 6 GPIO_ACTIVE_LOW>;
        };
        spare_gpio_0 {
            label = "SPARE_GPIO_0";
            gpios = <&gpiod 10 GPIO_ACTIVE_HIGH>;
        };
        spare_gpio_1 {
            label = "SPARE_GPIO_1";
            gpios = <&gpiod 11 GPIO_ACTIVE_HIGH>;
        };
        spare_gpio_2 {
            label = "SPARE_GPIO_2";
            gpios = <&gpioa 9 GPIO_ACTIVE_HIGH>;
        };
        spare_gpio_3 {
            label = "SPARE_GPIO_3";
            gpios = <&gpioa 10 GPIO_ACTIVE_HIGH>;
        };

        spare_relay_1 {
            label = "SPARE_RELAY_1";
            gpios = <&gpioe 15 GPIO_ACTIVE_HIGH>;
        };
        spare_relay_2 {
            label = "SPARE_RELAY_2";
            gpios = <&gpioe 14 GPIO_ACTIVE_HIGH>;
        };

    };

    soc {
    };

    analog_sensors {
        compatible = "hh-analog-sensors";

        io-channels = <&adc3 8>;
        io-channel-names = "vin";
    };
};

&clk_hse {
    clock-frequency = <DT_FREQ_M(8)>; /* STLink 8MHz clock */
    status = "okay";
};

&pll {
    div-m = <1>;
    mul-n = <24>;
    div-p = <2>;
    div-q = <4>;
    div-r = <2>;
    clocks = <&clk_hse>;
    status = "okay";
};

&rcc {
    clocks = <&pll>;
    clock-frequency = <DT_FREQ_M(96)>;
    d1cpre = <1>;
    hpre = <1>;
    d1ppre = <1>;
    d2ppre1 = <1>;
    d2ppre2 = <1>;
    d3ppre = <1>;
};

&mac {
    status = "okay";
    pinctrl-0 = <&eth_mdc_pc1
             &eth_rxd0_pc4
             &eth_rxd1_pc5
             &eth_ref_clk_pa1
             &eth_mdio_pa2
             &eth_crs_dv_pa7
             &eth_tx_en_pb11
             &eth_txd0_pg13
             &eth_txd1_pg12>;
    pinctrl-names = "default";
};

&rtc {
    status = "okay";
};

&rng {
    status = "okay";
};

&iwdg1 {
    status = "okay";
};

&gpioa {
    status = "okay";
};

&gpiob {
    status = "okay";
};

&gpioc {
    status = "okay";
};

&gpiod {
    status = "okay";
};

&gpioe {
    status = "okay";
};

&gpiof {
    status = "okay";
};

&gpiog {
    status = "okay";
};

// Cab CAN
&can1 {
    pinctrl-0 = <&fdcan1_rx_pa11 &fdcan1_tx_pa12>;
    pinctrl-names = "default";
    bus-speed = <500000>;
    bus-speed-data = <1000000>;
    status = "okay";
};

// Tractor CAN
&can2 {
    pinctrl-0 = <&fdcan2_rx_pb12 &fdcan2_tx_pb13>;
    pinctrl-names = "default";
    bus-speed = <500000>;
    bus-speed-data = <1000000>;
    status = "okay";
};

&spi1 {
    label = "SPI_1";
    status = "okay";
    pinctrl-0 = <&spi1_sck_pg11 &spi1_miso_pg9 &spi1_mosi_pd7 &spi1_nss_pg10>;
    pinctrl-names = "default";

        drv3946: drv3946@0 {
                compatible = "vnd,spi-device";
                reg = <0>;
                spi-max-frequency = <8000000>;
                label = "drv3946";
        };
};

// FS26 Safety PMIC SPI
&spi3 {
    // PA6 is the chip select for the FS26
    status = "okay";
    pinctrl-0 = <&spi3_sck_pc10 &spi3_miso_pc11 &spi3_mosi_pb2>;
    pinctrl-names = "default";
    clock-frequency = <1000000>;
    cs-gpios = <&gpioa 6 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;

    gendev: gendev@0 {
			compatible = "vnd,spi-device";
            reg = <0>;
            spi-max-frequency = <1600000>;
            label = "GenDev";
    };
};


&timers1 {
    status = "okay";
    st,prescaler = <119>;
            
    pwm1: pwm {
        label = "PWM1";
        compatible = "st,stm32-pwm";
        status = "okay";
        #pwm-cells = <3>;
        pinctrl-0 = <&tim1_ch1_pe9>;
        pinctrl-names = "default";
        //st,period = <65535>;
    };
};

/**
&timers1 {
    status = "okay";
    st,prescaler = <1199>;
    combo_pwm1: combo_pwm {
        compatible = "st,stm32-combo-pwm";
        status = "okay";
        label = "PWM1";
        pinctrl-0 = <&tim1_ch1_pe9>;
        pinctrl-names = "default";
        sync = "MASTER";
        #pwm-cells = <2>;
    };
};
*/


/**
&pinctrl {
    tim1_ch1_pe9: tim1_ch1_pe9 {
        pins {
            pinmux = <STM32_PINMUX('E', 8, AF1)>;
            bias-disable;
            drive-push-pull;
            slew-rate = "very-high-speed";
        };
    };
};
*/

&timers8 {
    status = "okay";
    st,prescaler = <119>;
    zlcb0: zlcb {
        compatible = "st,stm32-zlcb";
        status = "okay";
        label = "ZLCB0";
    };
};

&flash0 {
    partitions {
        compatible = "fixed-partitions";
        #address-cells = <1>;
        #size-cells = <1>;

        // bootloader
        /**
        boot_partition: partition@0 {
            label = "mcuboot";
            reg = <0x00000000 DT_SIZE_K(256)>;
            read-only;
        };
        */

        // priamry app (640K)
        slot0_partition: partition@0 {
            label = "image-0";
            reg = <0x00000000 DT_SIZE_K(640)>;
        };

        // (128K unused)

        // backup app (640K)
        slot1_partition: partition@100000 {
            label = "image-1";
            reg = <0x00100000 DT_SIZE_K(640)>;
        };

        // (128K unused)

        // EEPROM emulation (128K)
        storage_partition: partition@1c0000 {
            label = "storage";
            reg = <0x001c0000 DT_SIZE_K(128)>;
        };

        // swap slot (128K)
        scratch_partition: partition@1e0000 {
            label = "image-scratch";
            reg = <0x001e0000 DT_SIZE_K(128)>;
        };
    };
};



&adc3 {
    status = "okay";

    pinctrl-0 = <&adc3_inp8_pf6>;
    pinctrl-names = "default";

    #address-cells = <1>;
    #size-cells = <0>;

    // battery/main supply sense input
    vin_sense: channel@8 {
        reg = <8>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME(ADC_ACQ_TIME_TICKS, 33)>;
        zephyr,resolution = <12>;
        zephyr,oversampling = <4>;
    };
};

&i2c4 {
    status = "okay";
    clock-frequency = <I2C_BITRATE_STANDARD>;
    pinctrl-0 = <&i2c4_sda_pb7 &i2c4_scl_pb6>;
    pinctrl-names = "default";
    sx1509b: sx1509b@3e {
        compatible = "semtech,sx1509b";
        reg = <0x3E>;
        label = "breakout";
        #gpio-cells = <2>;
        ngpios = <16>;
        gpio-controller;
    };
};

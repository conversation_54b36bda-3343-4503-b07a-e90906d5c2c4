CONFIG_SOC_SERIES_STM32H7X=y
CONFIG_SOC_STM32H753XX=y
CONFIG_BOARD_WHEEL_ENCODER=y

# remove some stuff not really needed
CONFIG_EEPROM_AT24=n

# enable hardware stack overflow protection (via MPU)
CONFIG_ARM_MPU=y
CONFIG_HW_STACK_PROTECTION=y

CONFIG_CORTEX_M_SYSTICK=y
CONFIG_SYS_CLOCK_HW_CYCLES_PER_SEC=320000000

# built-in hardware peripherals
CONFIG_CLOCK_CONTROL=y
CONFIG_PINCTRL=y
CONFIG_GPIO=y
CONFIG_FLASH=y
CONFIG_DMA=y

# ethernet driver parts
CONFIG_PTP_CLOCK_STM32_HAL=y

# ethernet PHY config: MDIO addr 0, RMII interface
# CONFIG_ETH_STM32_RESET_ON_INIT=n
CONFIG_ETH_STM32_HAL_MII=n
CONFIG_ETH_STM32_HAL_PHY_ADDRESS=0

# I2C (for sensors, etc.)
CONFIG_I2C=y

CONFIG_SENSOR=y
CONFIG_BME680=y

# watchdog support
CONFIG_WATCHDOG=y
CONFIG_WDT_DISABLE_AT_BOOT=y

# include LED subsystem (for status LED + external) and status LED library
CONFIG_LED=y
CONFIG_LED_GPIO=y
CONFIG_LED_TLC59116=y

CONFIG_LIB_STATUS_LED=y

# EEPROM emulation
#
# The initialization priority is set to the lowest possible (e.g. highest numerical value) such that
# the flash controller the EEPROM emulation depends on will already have been initialized.
# Otherwise we are beholden to the behavior of the linker on how it will order the initialization
# functions for the EEPROM emulation and internal flash which may result in the flash controller not
# being initialized when the EEPROM emulation is.
CONFIG_EEPROM=y
CONFIG_EEPROM_EMULATOR=y
# CONFIG_EEPROM_EMULATOR_INIT_PRIORITY=99
CONFIG_EEPROM_INIT_PRIORITY=99

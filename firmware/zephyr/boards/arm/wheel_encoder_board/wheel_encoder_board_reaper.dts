/*
 * @file
 * @brief Reaper wheel encoder board
 */
/dts-v1/;

#include <mem.h>
#include <zephyr/dt-bindings/adc/adc.h>

#include <st/h7/stm32h743Xi.dtsi>
#include <st/h7/stm32h743zitx-pinctrl.dtsi>

/ {
    model = "Reaper Wheel Encoder Board";
    compatible = "cr,reaper_wheel_encoder";

    chosen {
        zephyr,sram = &sram0;
        zephyr,flash = &flash0;
        zephyr,dtcm = &dtcm;
        zephyr,code-partition = &slot0_partition;
    };

    aliases {
        eeprom0 = &emu_eeprom;
        watchdog0 = &iwdg1;

        zlcb0 = &zlcb0;
        qdec64k-fr = &qdec64k_fr;
        qdec64k-fl = &qdec64k_fl;

        // indicator LEDs
        led-status = &status_led;
        led-debug = &dbg_led;

        wheel-leds = &spinnyboi;
    };

    // emulated EEPROM (in flash)
    emu_eeprom: emu_eeprom {
        status = "okay";
        compatible = "zephyr,emu-eeprom";
        label = "emulated eeprom";

        size = <DT_SIZE_K(4)>;
        pagesize = <DT_SIZE_K(8)>;
        partition = <&storage_partition>;

        // shadow in RAM; also delay erase until entire block is used
        rambuf;
        partition-erase;
    };

    // Board revision via GPIO input straps
    // for prod revision, the presence of a strap resistor indicates a 0 bit
    hw_rev {
        compatible = "cr,hw-rev-straps";

        gpios =
            <&gpiog 2 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>,
            <&gpiog 3 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>,
            <&gpiog 4 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>,
            <&gpiog 5 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>;
    };

    // Simple binary LEDs
    dbg_led: leds {
        compatible = "gpio-leds";

        // Green debugging LED (single color)
        debug_led {
            gpios = <&gpioa 11 GPIO_ACTIVE_HIGH>;
        };
    };

    status_led: rgb_leds {
        compatible = "gpio-leds";

        // Status LED, red
        status_r {
            gpios = <&gpioe 12 GPIO_ACTIVE_HIGH>;
        };
        // Status LED, green
        status_g {
            gpios = <&gpioe 14 GPIO_ACTIVE_HIGH>;
        };
        // Status LED, blue
        status_b {
            gpios = <&gpioe 13 GPIO_ACTIVE_HIGH>;
        };
    };

    // Discrete (external) watchdog
    ext_watchdog {
        compatible = "cr,stwd100";

        enable-gpios = <&gpioa 0 GPIO_ACTIVE_LOW>;
        pet-gpios = <&gpioa 3 GPIO_ACTIVE_HIGH>;
    };

    // each of the wheel encoders
    wheel_fl {
        compatible = "cr,wheel-encoder";

        fuse-gpios = <&gpiof 9 GPIO_ACTIVE_HIGH>;
        qdec = <&qdec64k_fl>;
    };

    wheel_fr {
        compatible = "cr,wheel-encoder";

        fuse-gpios = <&gpiof 7 GPIO_ACTIVE_HIGH>;
        qdec = <&qdec64k_fr>;
    };
};

// 8MHz crystal on PH0/PH1
&clk_hse {
	status = "okay";
	clock-frequency = <DT_FREQ_M(8)>;
};

// 32.768kHz crystal on PC14/PC15
&clk_lse {
	// status = "okay";
	status = "disabled";
};

&pll {
    status = "okay";

    // input: 8MHz / 1
    clocks = <&clk_hse>;
    div-m = <1>;

    // PLL frequency = 960MHz
    mul-n = <120>;
    // PLL1.P = 320MHz
    div-p = <3>;
    // PLL1.Q = 240MHz
    div-q = <4>;
    // PLL1.R = 320MHz
    div-r = <3>;
};

&pll3 {
    // input: 8MHz / 1
    clocks = <&clk_hse>;
    div-m = <1>;

    // PLL frequency = 960MHz
    mul-n = <120>;
};

&rcc {
    clocks = <&pll>;

    // SYSCLK = 320MHz
    clock-frequency = <DT_FREQ_M(320)>;

    d1cpre = <1>;
    // AXI = 160MHz
    hpre = <2>;
    // APB3 = 80MHz
    d1ppre = <2>;
    // APB1 = 80MHz
    d2ppre1 = <2>;
    // APB2 = 80MHz
    d2ppre2 = <2>;
    // APB4 = 80MHz
    d3ppre = <2>;
};

// MCU-internal watchdog
&iwdg1 {
    status = "okay";
};

// enable all GPIO banks
&gpioa {
    status = "okay";
};

&gpiob {
    status = "okay";
};

&gpioc {
    status = "okay";
};

&gpiod {
    status = "okay";
};

&gpioe {
    status = "okay";
};

&gpiof {
    status = "okay";
};

&gpiog {
    status = "okay";
};

// more built in peripherals
&rtc {
    status = "okay";
};

&rng {
    status = "okay";
};

// flash partitions
&flash0 {
    partitions {
        compatible = "fixed-partitions";
        #address-cells = <1>;
        #size-cells = <1>;

        // bootloader
        boot_partition: partition@0 {
            label = "mcuboot";
            reg = <0x00000000 DT_SIZE_K(256)>;
            read-only;
        };

        // priamry app (640K)
        slot0_partition: partition@40000 {
            label = "image-0";
            reg = <0x00040000 DT_SIZE_K(640)>;
        };

        // (128K unused)

        // backup app (640K)
        slot1_partition: partition@100000 {
            label = "image-1";
            reg = <0x00100000 DT_SIZE_K(640)>;
        };

        // (128K unused)

        // EEPROM emulation zone
        storage_partition: partition@1c0000 {
            label = "storage";
            reg = <0x001c0000 DT_SIZE_K(128)>;
        };

        // swap slot (128K)
        scratch_partition: partition@1e0000 {
            label = "image-scratch";
            reg = <0x001e0000 DT_SIZE_K(128)>;
        };
    };
};

// dedicate sram1 (128K) as a non-cached area for DMA buffers
&sram1 {
    zephyr,memory-region = "NOCACHE_REGION";
    //zephyr,memory-attr = <( DT_MEM_ARM(ATTR_MPU_RAM_NOCACHE) )>;
    zephyr,memory-region-mpu = "RAM_NOCACHE";
};

// enable all DMA controllers
&dma1 {
    status = "okay";
};

&dma2 {
    status = "okay";
};

&dmamux1 {
    status = "okay";
};

// Ethernet MAC
&mac {
	status = "okay";
    pinctrl-0 = <&eth_mdc_pc1
                 &eth_rxd0_pc4
                 &eth_rxd1_pc5
                 &eth_ref_clk_pa1
                 &eth_mdio_pa2
                 &eth_crs_dv_pa7
                 &eth_tx_en_pg11
                 &eth_txd0_pg13
                 &eth_txd1_pb13>;
    pinctrl-names = "default";

    // PHY reset lines
    // reset-gpios = <&gpiof 2 (GPIO_ACTIVE_LOW | GPIO_OPEN_DRAIN)>;
};

&timers1 {
    status = "okay";

    qdec64k_fl: qdec64k {
        compatible = "st,stm32-qdec64k";
        status = "okay";
        label = "QDEC64K_FL";
        pinctrl-0 = <&tim1_ch1_pe9 &tim1_ch2_pe11>;
        pinctrl-names = "default";
    };
};

&timers4 {
    status = "okay";

    qdec64k_fr: qdec64k {
        compatible = "st,stm32-qdec64k";
        status = "okay";
        label = "QDEC64K_FR";
        pinctrl-0 = <&tim4_ch1_pd12 &tim4_ch2_pd13>;
        pinctrl-names = "default";
    };
};

&timers8 {
    status = "okay";

    st,prescaler = <200>;

    zlcb0: zlcb {
        compatible = "st,stm32-zlcb";
        status = "okay";
        label = "ZLCB0";
    };
};

// I2C controller for sensors
&i2c1 {
    status = "okay";
    label = "Sensor I2C";

    clock-frequency = <I2C_BITRATE_FAST>;

    pinctrl-0 = <
        &i2c1_sda_pb7
        &i2c1_scl_pb6
    >;
    pinctrl-names = "default";

    enviro0: bme688@76 {
        compatible = "bosch,bme688", "bosch,bme680";
        label = "Environmental (Internal)";

        reg = <0x76>;
    };
};

// I2C controller for LED driver
&i2c2 {
    status = "okay";
    label = "Blinky I2C";

    clock-frequency = <I2C_BITRATE_FAST>;

    pinctrl-0 = <
        &i2c2_sda_pf0
        &i2c2_scl_pf1
    >;
    pinctrl-names = "default";

    spinnyboi: tlc59116@60 {
        compatible = "ti,tlc59116";
        label = "Front wheels encoder driver";

        reg = <0x60>;

        reset-gpios = <&gpioc 13 (GPIO_ACTIVE_LOW | GPIO_OPEN_DRAIN | GPIO_PULL_UP)>;
    };
};


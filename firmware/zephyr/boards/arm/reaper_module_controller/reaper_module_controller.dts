/**
 * @file
 *
 * @brief Module Control Board (Production)
 *
 * This device tree is used for the rev 1 and rev 2 production boards. Rev 2 adds the following
 * changes:
 *
 * - Swap scanner A/B power
 * - Scanner current sense
 * - Scanner fuse blown detection
 *
 * \section{DMA Assignments}
 *
 * DMAMUX1:
 *  0. USART1 TX
 *  1. USART1 RX
 *  2. UART7 RX
 *  3. UART7 TX
 *
 * DMAMUX2:
 *  (None)
 */
/dts-v1/;

#include <mem.h>
#include <st/h7/stm32h753Xi.dtsi>
#include <st/h7/stm32h753zitx-pinctrl.dtsi>

#include <zephyr/dt-bindings/adc/adc.h>

#include "common.dtsi"

/ {
    model = "Reaper Module Controller";
    compatible = "cr,reaper_module_controller";

    chosen {
		zephyr,sram = &sram0;
		zephyr,flash = &flash0;
		zephyr,dtcm = &dtcm;
		zephyr,code-partition = &slot0_partition;
    };

    aliases {
        watchdog0 = &iwdg1;
        strobe-zlcb = &zlcb0;

        // UART used to communicate with PC (via RS232)
        pc-serial = &usart1;

        // indicator LEDs
        led-status = &status_led;
        led-debug = &dbg_led;
    };
};

// I2C controller for sensors
&i2c1 {
    status = "okay";
    label = "Sensor-I2C";

    clock-frequency = <I2C_BITRATE_FAST>;

    pinctrl-0 = <
        &i2c1_sda_pb7
        &i2c1_scl_pb6
    >;
    pinctrl-names = "default";

    // 6 axis IMU
    imu0: bmi270@68 {
        compatible = "cr,bmi270";
        label = "IMU";

        reg = <0x68>;
    };

    // Environmental sensor (internal, on board)
    enviro0: bme688@76 {
        compatible = "bosch,bme688", "bosch,bme680";
        label = "Environmental (Internal)";

        reg = <0x76>;
    };

    enviro1: bme680@77 {
        compatible = "bosch,bme680";
        label = "Environmental (External)";

        reg = <0x77>;
    };
};

// UART to PC (via RS232 transceiver)
&usart1 {
    status = "okay";
    label = "PC-UART";

    current-speed = <9600>;
    parity = "none";

    pinctrl-0 = <
        &usart1_tx_pa9
        &usart1_rx_pa10
    >;
    pinctrl-names = "default";

	dmas = <&dmamux1 1 42 0x20440>,
		<&dmamux1 2 41 0x20480>;
	dma-names = "tx", "rx";
};

/ {
    // Indicator LEDs
    dbg_led: leds {
        compatible = "gpio-leds";

        // Green debug LED
        debug_led {
            gpios = <&gpioa 11 GPIO_ACTIVE_HIGH>;
        };
    };

    // status LED (RGB via PWM)
    status_led: status {
        compatible = "pwm-leds";

        status_r {
            pwms = <&pwm_status 2 PWM_MSEC(5) PWM_POLARITY_NORMAL>;
            label = "Status LED (Red)";
        };

        status_g {
            pwms = <&pwm_status 4 PWM_MSEC(5) PWM_POLARITY_NORMAL>;
            label = "Status LED (Green)";
        };

        status_b {
            pwms = <&pwm_status 3 PWM_MSEC(5) PWM_POLARITY_NORMAL>;
            label = "Status LED (Blue)";
        };
    };

    // Discrete (external) watchdog
    ext_watchdog {
        compatible = "cr,stwd100";

        enable-gpios = <&gpioa 0 GPIO_ACTIVE_LOW>;
        pet-gpios = <&gpioa 3 GPIO_ACTIVE_HIGH>;
    };

    // +24V power for Ethernet switch
    eth_switch_24v: eth_switch_power {
        compatible = "regulator-fixed";
        label = "eth switch 24V";
        regulator-name = "eth switch 24V";
        enable-gpios = <&gpiob 15 GPIO_ACTIVE_HIGH>;
        regulator-boot-on;
    };

    // Relay board
    relay_board {
        compatible = "cr,relay-board";

        gpios = <&gpiob 1 GPIO_ACTIVE_LOW>,
                  <&gpiob 10 GPIO_ACTIVE_LOW>,
                  <&gpiob 11 GPIO_ACTIVE_LOW>;
        gpio-names = "ldd", "btl", "pc";

        // +24V power for relay board
        relay_board_24v: power {
            compatible = "regulator-fixed";
            label = "relay board 24V";
            regulator-name = "relay board 24V";
            enable-gpios = <&gpioe 15 GPIO_ACTIVE_HIGH>;
        };
    };

    // Strobe board
    strobe_board {
        compatible = "cr,strobe-board";

        io-channels = <&adc1 3>, <&adc1 19>;
        io-channel-names = "amps", "volts";

        ready-gpios = <&gpioa 8 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>;

        // temperature feedback
        thermistor {
            compatible = "adc-thermistors";
            status = "okay";

            io-channels = <&adc3 4>;
            io-channel-names = "temp";
        };

        // HRTIM outputs
        hrtim {
            compatible = "cr,hrtim";
            status = "okay";

            // TODO: define clocks

            pinctrl-0 = <
                /* strobe trigger */
                /* NOTE: if changing this pin, the code in strobe/worker.c needs updating! */
                &hrtim_cha2_pc7
                /* predict trigger */
                &hrtim_chd2_pa12
                /* target trigger */
                &hrtim_chb1_pc8
            >;
            pinctrl-names = "default";
        };

        // +24V power for strobe board
        strobe_board_24v: power {
            compatible = "regulator-fixed";
            label = "strobe board 24V";
            regulator-name = "strobe board 24V";
            enable-gpios = <&gpioc 6 GPIO_ACTIVE_HIGH>;
        };
    };

    // Predict cam
    predict_cam {
        // +24V power for predict cam
        predict_24v: power {
            compatible = "regulator-fixed";
            label = "predict cam 24V";
            regulator-name = "predict cam 24V";
            enable-gpios = <&gpiob 12 GPIO_ACTIVE_HIGH>;
        };
    };

    // Scanners
    scanners {
        // NOTE: for rev 1 boards, A/B power pins are swapped. this is corrected for by the
        //       power control software based on the hardware rev.
        a {
            compatible = "cr,scanner";
            label = "Scanner A";

            enable-gpios = <&gpiog 7 GPIO_ACTIVE_HIGH>;
            fuse-gpios = <&gpiod 14 GPIO_ACTIVE_HIGH>;

            io-channels = <&adc1 2>;
            io-channel-names = "amps";
        };

        b {
            compatible = "cr,scanner";
            label = "Scanner B";

            enable-gpios = <&gpiog 6 GPIO_ACTIVE_HIGH>;
            fuse-gpios = <&gpiod 15 GPIO_ACTIVE_HIGH>;

            io-channels = <&adc1 6>;
            io-channel-names = "amps";
        };
    };

    // Discrete sensors
    sensors {
        // Leak sensors
        leak {
            compatible = "gpio-leak-sensor";
            gpios = <&gpiob 0 (GPIO_PULL_UP | GPIO_ACTIVE_LOW)>,
                <&gpiob 14 (GPIO_PULL_UP | GPIO_ACTIVE_LOW)>;
        };

        // external pressure sensors
        pressure {
            compatible = "adc-pressure-sensor";

            io-channels = <&adc2 2>, <&adc2 6>;
            io-channel-names = "press1", "press2";
        };

        // external thermistors (for temp)
        thermistors {
            compatible = "adc-thermistors";
            status = "okay";

            io-channels = <&adc3 0>, <&adc3 1>, <&adc3 5>, <&adc3 9>;
            io-channel-names = "therm1", "therm2", "press1", "press2";
        };
    };

    // Off board 24V fans (GPIO control)
    fans {
        fan1 {
            compatible = "gpio-fan";
            gpios = <&gpioc 10 GPIO_ACTIVE_HIGH>;
        };

        fan2 {
            compatible = "gpio-fan";
            gpios = <&gpioc 11 GPIO_ACTIVE_HIGH>;
        };
    };
};

// Analog inputs: Strobe board analog inputs, scanner current sense
&adc1 {
    status = "okay";

    pinctrl-0 = <&adc1_inp2_pf11>, <&adc1_inp3_pa6>, <&adc1_inp6_pf12>, <&adc1_inp19_pa5>;
    pinctrl-names = "default";

    #address-cells = <1>;
    #size-cells = <0>;

    // INP2: Current sense for scanner A
    scanner_a_amps: channel@2 {
        reg = <2>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME(ADC_ACQ_TIME_TICKS, 811)>;
        zephyr,resolution = <12>;
        zephyr,oversampling = <4>;
    };

    // INP3: Capacitor current feedback
    strobe_amps: channel@3 {
        reg = <3>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME(ADC_ACQ_TIME_TICKS, 811)>;
        zephyr,resolution = <12>;
        zephyr,oversampling = <4>;
    };

    // INP6: Current sense for scanner B
    scanner_b_amps: channel@6 {
        reg = <6>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME(ADC_ACQ_TIME_TICKS, 811)>;
        zephyr,resolution = <12>;
        zephyr,oversampling = <4>;
    };

    // INP19: Capacitor voltage feedback
    strobe_volts: channel@19 {
        reg = <19>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME(ADC_ACQ_TIME_TICKS, 65)>;
        zephyr,resolution = <12>;
        zephyr,oversampling = <2>;
    };
};

// Analog inputs: pressure sensors
&adc2 {
    status = "okay";

    pinctrl-0 = <&adc2_inp2_pf13>, <&adc2_inp6_pf14>;
    pinctrl-names = "default";

    #address-cells = <1>;
    #size-cells = <0>;

    // external pressure sensor channels
    press1: channel@2 {
        reg = <2>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME(ADC_ACQ_TIME_TICKS, 65)>;
        zephyr,resolution = <12>;
        zephyr,oversampling = <4>;
    };

    press2: channel@6 {
        reg = <6>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME(ADC_ACQ_TIME_TICKS, 65)>;
        zephyr,resolution = <12>;
        zephyr,oversampling = <4>;
    };
};

// Analog inputs: thermistors
&adc3 {
    status = "okay";

    pinctrl-0 = <&adc3_inp0_pc2>, <&adc3_inp1_pc3>, <&adc3_inp4_pf5>, <&adc3_inp5_pf3>,
        <&adc3_inp9_pf4>;
    pinctrl-names = "default";

    #address-cells = <1>;
    #size-cells = <0>;

    // external thermistor channels
    therm1: channel@0 {
        reg = <0>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME(ADC_ACQ_TIME_TICKS, 33)>;
        zephyr,resolution = <12>;
        zephyr,oversampling = <4>;
    };

    therm0: channel@1 {
        reg = <1>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME(ADC_ACQ_TIME_TICKS, 33)>;
        zephyr,resolution = <12>;
        zephyr,oversampling = <4>;
    };

    // strobe board temperature (NTC)
    strobe_temp: channel@4 {
        reg = <4>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME(ADC_ACQ_TIME_TICKS, 33)>;
        zephyr,resolution = <12>;
        zephyr,oversampling = <4>;
    };

    // thermistors on pressure sensors
    press1_therm: channel@5 {
        reg = <5>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME(ADC_ACQ_TIME_TICKS, 33)>;
        zephyr,resolution = <12>;
        zephyr,oversampling = <4>;
    };

    press2_therm: channel@9 {
        reg = <9>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME(ADC_ACQ_TIME_TICKS, 33)>;
        zephyr,resolution = <12>;
        zephyr,oversampling = <4>;
    };
};

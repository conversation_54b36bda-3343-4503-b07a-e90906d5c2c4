/**
 * @file
 *
 * @brief Module Control Board (Dev Proto)
 */
/dts-v1/;

#include <mem.h>
#include <st/h7/stm32h743Xi.dtsi>
#include <st/h7/stm32h743zitx-pinctrl.dtsi>

#include <zephyr/dt-bindings/adc/adc.h>

#include "common.dtsi"
#include "base-dev.dtsi"

/ {
    model = "Reaper Module Controller (Dev)";
    compatible = "cr,reaper_module_controller_dev";

    chosen {
		zephyr,sram = &sram0;
		zephyr,flash = &flash0;
		zephyr,dtcm = &dtcm;
		zephyr,code-partition = &slot0_partition;
    };

    aliases {
        watchdog0 = &iwdg1;
        zlcb = &zlcb0;

        // UART used to communicate with PC (via RS232)
        pc-serial = &usart1;

        // indicator LEDs
        led-status = &status_led;
        led-debug = &dbg_led;
    };

    hw_rev {
        gpios =
            <&gpiod 10 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>,
            <&gpiod 11 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>,
            <&gpiod 12 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>,
            <&gpiod 13 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>;
    };
};

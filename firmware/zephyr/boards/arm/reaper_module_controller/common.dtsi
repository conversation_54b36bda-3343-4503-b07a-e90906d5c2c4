/*
 * @file
 * @brief Shared device tree stuff
 * 
 * This defines stuff (RAM allocations, flash partitioning, internal-only peripherals like clocks)
 * which do not vary based on the pinout.
 */
#include <mem.h>
#include <zephyr/dt-bindings/adc/adc.h>

/ {
    // emulated EEPROM (in flash)
    emu_eeprom: emu_eeprom {
        status = "okay";
        compatible = "zephyr,emu-eeprom";
        label = "emulated eeprom";

        size = <DT_SIZE_K(4)>;
        pagesize = <DT_SIZE_K(8)>;
        partition = <&storage_partition>;

        // shadow in RAM; also delay erase until entire block is used
        rambuf;
        partition-erase;
    };

    // Board revision via GPIO input straps
    // for prod revision, the presence of a strap resistor indicates a 0 bit
    hw_rev {
        compatible = "cr,hw-rev-straps";

        gpios =
            <&gpiod 10 (GPIO_ACTIVE_LOW | GPIO_PULL_DOWN)>,
            <&gpiod 11 (GPIO_ACTIVE_LOW | GPIO_PULL_DOWN)>,
            <&gpiod 12 (GPIO_ACTIVE_LOW | GPIO_PULL_DOWN)>,
            <&gpiod 13 (GPIO_ACTIVE_LOW | GPIO_PULL_DOWN)>;
    };

    // Core dump device
    coredump: dumper {
        compatible = "zephyr,coredump";
        label = "Bonus coredump device";

        coredump-type = "COREDUMP_TYPE_MEMCPY";
    };
};

// pin definitions for HRTIM
&pinctrl {
    /omit-if-no-ref/ hrtim_cha2_pc7: hrtim_cha2_pc7 {
        pinmux = <STM32_PINMUX('C', 7, AF1)>;
        slew-rate = "very-high-speed";
    };

    /omit-if-no-ref/ hrtim_chb1_pc8: hrtim_chb1_pc8 {
        pinmux = <STM32_PINMUX('C', 8, AF1)>;
        slew-rate = "very-high-speed";
    };

    /omit-if-no-ref/ hrtim_chb2_pa8: hrtim_chb2_pa8 {
        pinmux = <STM32_PINMUX('A', 8, AF2)>;
        slew-rate = "very-high-speed";
    };

    /omit-if-no-ref/ hrtim_chd2_pa12: hrtim_chd2_pa12 {
        pinmux = <STM32_PINMUX('A', 12, AF2)>;
        slew-rate = "very-high-speed";
    };
};

// 8MHz crystal on PH0/PH1
&clk_hse {
	status = "okay";
	clock-frequency = <DT_FREQ_M(8)>;
};

// 32.768kHz crystal on PC14/PC15
&clk_lse {
	// status = "okay";
	status = "disabled";
};

&pll {
    status = "okay";

    // input: 8MHz / 1
    clocks = <&clk_hse>;
    div-m = <1>;

    // PLL frequency = 960MHz
    mul-n = <120>;
    // PLL1.P = 240MHz
    div-p = <4>;
    // PLL1.Q = 240MHz
    div-q = <4>;
    // PLL1.R = 120MHz
    div-r = <8>;
};

&pll3 {
    // input: 8MHz / 1
    clocks = <&clk_hse>;
    div-m = <1>;

    // PLL frequency = 960MHz
    mul-n = <120>;

    // PLL3.P = 120MHz
    div-p = <8>;
    // PLL3.Q = 120MHz
    div-q = <8>;
    // PLL3.R = 96MHz
    div-r = <10>;
};

/*
 * Note: APB2 is slowed way down (D2PPRE2 could be 2) to allow for the APB2 timer clock to be at
 * 15MHz. This is used directly by HRTIM as its timebase; since all the timers are 16 bits only,
 * this is the only way to really get the desired accuracy.
 *
 * The only other peripherals that are affected by this (which we're using) are USART1, TIM1.
 */
&rcc {
    clocks = <&pll>;

    // SYSCLK = 240MHz
    clock-frequency = <DT_FREQ_M(240)>;

    d1cpre = <1>;
    // AXI = 240MHz
    hpre = <2>;
    // APB3 = 120MHz
    d1ppre = <2>;
    // APB1 = 120MHz
    d2ppre1 = <2>;
    // APB2 = 15MHz
    d2ppre2 = <16>;
    // APB4 = 120MHz
    d3ppre = <2>;
};

// MCU-internal watchdog
&iwdg1 {
    status = "okay";
};

// enable all GPIO banks
&gpioa {
    status = "okay";
};

&gpiob {
    status = "okay";
};

&gpioc {
    status = "okay";
};

&gpiod {
    status = "okay";
};

&gpioe {
    status = "okay";
};

&gpiof {
    status = "okay";
};

&gpiog {
    status = "okay";
};

// more built in peripherals
&rtc {
    status = "okay";
};

&rng {
    status = "okay";
};

// flash partitions
&flash0 {
    partitions {
        compatible = "fixed-partitions";
        #address-cells = <1>;
        #size-cells = <1>;

        // bootloader
        boot_partition: partition@0 {
            label = "mcuboot";
            reg = <0x00000000 DT_SIZE_K(256)>;
            read-only;
        };

        // priamry app (640K)
        slot0_partition: partition@40000 {
            label = "image-0";
            reg = <0x00040000 DT_SIZE_K(640)>;
        };

        // (128K unused)

        // backup app (640K)
        slot1_partition: partition@100000 {
            label = "image-1";
            reg = <0x00100000 DT_SIZE_K(640)>;
        };

        // (128K unused)

        // EEPROM emulation zone
        storage_partition: partition@1c0000 {
            label = "storage";
            reg = <0x001c0000 DT_SIZE_K(128)>;
        };

        // swap slot (128K)
        scratch_partition: partition@1e0000 {
            label = "image-scratch";
            reg = <0x001e0000 DT_SIZE_K(128)>;
        };
    };
};

// dedicate sram1 (128K) as a non-cached area for DMA buffers
&sram1 {
    zephyr,memory-region = "NOCACHE_REGION";
    //zephyr,memory-attr = <( DT_MEM_ARM(ATTR_MPU_RAM_NOCACHE) )>;
    zephyr,memory-region-mpu = "RAM_NOCACHE";
};

// enable all DMA controllers
&dma1 {
    status = "okay";
};

&dma2 {
    status = "okay";
};

&dmamux1 {
    status = "okay";
};

// Ethernet MAC
&mac {
	status = "okay";
    pinctrl-0 = <&eth_mdc_pc1
                 &eth_rxd0_pc4
                 &eth_rxd1_pc5
                 &eth_ref_clk_pa1
                 &eth_mdio_pa2
                 &eth_crs_dv_pa7
                 &eth_tx_en_pg11
                 &eth_txd0_pg13
                 &eth_txd1_pb13>;
    pinctrl-names = "default";

    // PHY reset lines
    reset-gpios = <&gpiof 2 (GPIO_ACTIVE_LOW | GPIO_OPEN_DRAIN)>;
};

// status LED (PWM)
&timers1 {
    status = "okay";
    st,prescaler = <1000>;

    pwm_status: pwm {
        status = "okay";

        pinctrl-0 = <&tim1_ch2_pe11 &tim1_ch3_pe13 &tim1_ch4_pe14>;
        pinctrl-names = "default";
    };
};

// timer used for triggering based on PTP clock
&timers2 {
    status = "okay";
    st,prescaler = <1>;
};

// timer used as the strobe trigger timebase
&timers8 {
    status = "okay";
    st,prescaler = <119>;

    zlcb0: zlcb {
        compatible = "st,stm32-zlcb";
        status = "okay";
        label = "ZLCB0";
    };
};

// I2C controller for Ethernet switch
&i2c2 {
    status = "okay";
    label = "Eth-I2C";

    clock-frequency = <I2C_BITRATE_FAST>;

    pinctrl-0 = <
        &i2c2_sda_pf0
        &i2c2_scl_pf1
    >;
    pinctrl-names = "default";
};

/*
 * @file
 * @brief Device tree base for dev boards
 *
 * Define pinout for the dev proto boards.
 *
 * \section{DMA Assignments}
 *
 * STM32H7 does not have fixed DMA channel assignments like previous generations; instead, a DMA
 * request multiplexer (DMAMUX) is used to assign peripheral interrupt requests to the underlying
 * physical DMA channels. DMAMUX channels are allocated sequentially for each of the two units:
 *
 * DMAMUX1:
 *  0. USART1 TX
 *  1. USART1 RX
 *  2. UART7 RX
 *  3. UART7 TX
 *
 * DMAMUX2:
 *  (None)
 */
#include <mem.h>
#include <zephyr/dt-bindings/adc/adc.h>
#include <zephyr/dt-bindings/pwm/pwm.h>

/ {
    // on dev board, the GPIO rev straps' _absence_ is used to encode a 0
    hw_rev {
        gpios =
            <&gpiod 10 (GPIO_ACTIVE_LOW | GPIO_PULL_DOWN)>,
            <&gpiod 11 (GPIO_ACTIVE_LOW | GPIO_PULL_DOWN)>,
            <&gpiod 12 (GPIO_ACTIVE_LOW | GPIO_PULL_DOWN)>,
            <&gpiod 13 (GPIO_ACTIVE_LOW | GPIO_PULL_DOWN)>;
    };

    // external watchdog
    ext_watchdog {
        compatible = "cr,stwd100";

        enable-gpios = <&gpioa 0 GPIO_ACTIVE_LOW>;
        pet-gpios = <&gpioa 3 GPIO_ACTIVE_HIGH>;
    };

    // +24V power for Ethernet switch
    eth_switch_24v: eth_switch_power {
        compatible = "regulator-fixed";
        label = "eth switch 24V";
        regulator-name = "eth switch 24V";
        enable-gpios = <&gpiob 15 GPIO_ACTIVE_HIGH>;
        regulator-boot-on;
    };

    // Simple binary LEDs
    dbg_led: leds {
        compatible = "gpio-leds";

        // Green debugging LED (single color)
        debug_led {
            gpios = <&gpioa 11 GPIO_ACTIVE_HIGH>;
        };
    };

    // status LED (RGB via PWM)
    status_led: status {
        compatible = "pwm-leds";

        status_r {
            pwms = <&pwm_status 2 PWM_MSEC(5) PWM_POLARITY_NORMAL>;
            label = "Status LED (Red)";
        };

        status_g {
            pwms = <&pwm_status 4 PWM_MSEC(5) PWM_POLARITY_NORMAL>;
            label = "Status LED (Green)";
        };

        status_b {
            pwms = <&pwm_status 3 PWM_MSEC(5) PWM_POLARITY_NORMAL>;
            label = "Status LED (Blue)";
        };
    };

    // Off board 24V fans (GPIO control)
    fans {
        fan1 {
            compatible = "gpio-fan";
            gpios = <&gpioc 10 GPIO_ACTIVE_HIGH>;
        };

        fan2 {
            compatible = "gpio-fan";
            gpios = <&gpioc 11 GPIO_ACTIVE_HIGH>;
        };
    };

    // Relay board
    relay_board {
        compatible = "cr,relay-board";

        gpios = <&gpiob 1 GPIO_ACTIVE_HIGH>,
                  <&gpiob 10 GPIO_ACTIVE_HIGH>,
                  <&gpiob 11 GPIO_ACTIVE_HIGH>,
                  <&gpiob 12 GPIO_ACTIVE_HIGH>;
        gpio-names = "ldd", "btl", "pc", "spare";
    };

    // Strobe control board
    strobe_board {
        compatible = "cr,strobe-board";

        ready-gpios = <&gpioa 8 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>;

        io-channels = <&adc1 3>, <&adc1 19>;
        io-channel-names = "amps", "volts";

        // no thermistor here

        // HRTIM outputs
        hrtim {
            compatible = "cr,hrtim";
            status = "okay";

            // TODO: define clocks

            pinctrl-0 = <
                &hrtim_chb2_pa8
                &hrtim_chd2_pa12
            >;
            pinctrl-names = "default";
        };
    };

    sensors {
        // Leak sensors
        leak {
            compatible = "gpio-leak-sensor";
            gpios = <&gpiob 0 (GPIO_PULL_UP | GPIO_ACTIVE_LOW)>,
                <&gpiob 14 (GPIO_PULL_UP | GPIO_ACTIVE_LOW)>;
        };

        // external pressure sensors not available (not on ADC pin)

        // external thermistors (for temp)
        thermistors {
            compatible = "adc-thermistors";
            status = "okay";

            io-channels = <&adc3 0>, <&adc3 1>;
            io-channel-names = "therm1", "therm2";
        };
    };
};

// I2C controller for sensors
&i2c1 {
    status = "okay";
    label = "Sensor-I2C";

    clock-frequency = <I2C_BITRATE_FAST>;

    pinctrl-0 = <
        &i2c1_sda_pb7
        &i2c1_scl_pb6
    >;
    pinctrl-names = "default";

    // 6 axis IMU
    imu0: bmi270@68 {
        compatible = "cr,bmi270";
        label = "IMU";

        reg = <0x68>;
    };

    // Environmental sensor (internal, on board)
    enviro0: bme688@76 {
        compatible = "bosch,bme688", "bosch,bme680";
        label = "Environmental (Internal)";

        reg = <0x76>;
    };

    enviro1: bme680@77 {
        compatible = "bosch,bme680";
        label = "Environmental (External)";

        reg = <0x77>;
    };
};

// UART to PC (via RS232 transceiver)
&usart1 {
    status = "okay";
    label = "PC-UART";

    current-speed = <9600>;
    parity = "none";

    pinctrl-0 = <
        &usart1_tx_pa9
        &usart1_rx_pa10
    >;
    pinctrl-names = "default";

	dmas = <&dmamux1 1 42 0x20440>,
		<&dmamux1 2 41 0x20480>;
	dma-names = "tx", "rx";
};


// bonus UART (on FTDI-compatible header)
&uart7 {
    status = "okay";
    label = "bonus-uart";

    current-speed = <115200>;
    parity = "none";

    pinctrl-0 = <
        &uart7_tx_pf7
        &uart7_rx_pf6
        &uart7_cts_pf9
        &uart7_rts_pf8
    >;
    pinctrl-names = "default";

    dmas = <&dmamux1 2 80 0x20440>,
        <&dmamux1 3 79 0x20480>;
    dma-names = "tx", "rx";
};

// Analog inputs: Strobe board analog inputs
&adc1 {
    status = "okay";

    pinctrl-0 = <&adc1_inp3_pa6>, <&adc1_inp19_pa5>;
    pinctrl-names = "default";

    #address-cells = <1>;
    #size-cells = <0>;

    // INP3
    strobe_amps: channel@3 {
        reg = <3>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME(ADC_ACQ_TIME_TICKS, 811)>;
        zephyr,resolution = <12>;
        zephyr,oversampling = <4>;
    };

    // INP19
    strobe_volts: channel@19 {
        reg = <19>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME(ADC_ACQ_TIME_TICKS, 65)>;
        zephyr,resolution = <12>;
        zephyr,oversampling = <2>;
    };
};

// Analog inputs: thermistors
&adc3 {
    status = "okay";

    pinctrl-0 = <&adc3_inp0_pc2>, <&adc3_inp1_pc3>;
    pinctrl-names = "default";

    #address-cells = <1>;
    #size-cells = <0>;

    therm0: channel@0 {
        reg = <0>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME(ADC_ACQ_TIME_TICKS, 33)>;
        zephyr,resolution = <12>;
        zephyr,oversampling = <4>;
    };

    therm1: channel@1 {
        reg = <1>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME(ADC_ACQ_TIME_TICKS, 33)>;
        zephyr,resolution = <12>;
        zephyr,oversampling = <4>;
    };
};

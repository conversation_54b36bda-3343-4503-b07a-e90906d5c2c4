CONFIG_SOC_SERIES_STM32H7X=y
CONFIG_SOC_STM32H743XX=y
CONFIG_BOARD_REAPER_MODULE_CONTROLLER=y

################################################################################
## Shared board configuration
################################################################################
# enable hardware stack overflow protection (via MPU)
CONFIG_ARM_MPU=y
CONFIG_HW_STACK_PROTECTION=y

CONFIG_CORTEX_M_SYSTICK=y
CONFIG_SYS_CLOCK_HW_CYCLES_PER_SEC=240000000

# built-in hardware peripherals
CONFIG_CLOCK_CONTROL=y
CONFIG_PINCTRL=y
CONFIG_GPIO=y
CONFIG_FLASH=y
CONFIG_ADC=y
CONFIG_DMA=y
CONFIG_REGULATOR=y
CONFIG_PWM=y

# we use both ADC1/2 which share an interrupt
CONFIG_ADC_STM32_SHARED_IRQS=y

# ethernet driver parts
CONFIG_PTP_CLOCK_STM32_HAL=y

# ethernet PHY config: MDIO addr 0, RMII interface
CONFIG_ETH_STM32_RESET_ON_INIT=n
CONFIG_ETH_STM32_HAL_MII=n
CONFIG_ETH_STM32_HAL_PHY_ADDRESS=0

# ethernet PHY config: force 100M/full
CONFIG_ETH_STM32_AUTO_NEGOTIATION_ENABLE=n
CONFIG_ETH_STM32_SPEED_10M=n
CONFIG_ETH_STM32_MODE_HALFDUPLEX=n

CONFIG_ETH_STM32_MDI_X=n
CONFIG_ETH_STM32_MDI_CH_SWAP=n

# enable UART support, but no TTY
CONFIG_SERIAL=y
CONFIG_CONSOLE=n
CONFIG_UART_CONSOLE=n
CONFIG_UART_STM32=y

# RNG via hardware entropy generator
CONFIG_ENTROPY_STM32_RNG=y

# I2C (for sensors, etc.)
CONFIG_I2C=y

CONFIG_SENSOR=y
CONFIG_BME680=y

# watchdog support
CONFIG_WATCHDOG=y
CONFIG_WDT_DISABLE_AT_BOOT=y

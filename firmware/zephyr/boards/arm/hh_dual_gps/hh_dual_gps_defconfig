CONFIG_SOC_SERIES_STM32H7X=y
CONFIG_SOC_STM32H753XX=y

CONFIG_BOARD_HH_DUAL_GPS=y

CONFIG_ARM_MPU=y
CONFIG_HW_STACK_PROTECTION=y

CONFIG_CORTEX_M_SYSTICK=y

# RTT block stored in DTCM
CONFIG_SEGGER_RTT_SECTION_DTCM=y

# CPU core is downclocked to 320MHz
CONFIG_SYS_CLOCK_HW_CYCLES_PER_SEC=320000000

# built-in hardware peripherals
CONFIG_CLOCK_CONTROL=y
CONFIG_DMA=y
CONFIG_SPI=y
CONFIG_GPIO=y
CONFIG_PINCTRL=y

# need to read out hw rev straps
CONFIG_HW_REV_STRAPS=y

# hardware info driver (reading out unique id)
CONFIG_HWINFO=y

# support LED subsystem (for status indicators)
CONFIG_LED=y
CONFIG_LED_GPIO=y
CONFIG_LIB_STATUS_LED=y

# ethernet configuration
CONFIG_ETH_STM32_HAL=y
CONFIG_PTP_CLOCK_STM32_HAL=y

# assert PHY hw reset line on init
CONFIG_ETH_STM32_RESET_ON_INIT=y

# ethernet PHY config: MDIO addr 0, RMII interface
CONFIG_ETH_STM32_HAL_MII=n
CONFIG_ETH_STM32_HAL_PHY_ADDRESS=0

# use newlib (for better math support)
CONFIG_NEWLIB_LIBC=y
CONFIG_NEWLIB_LIBC_NANO=y

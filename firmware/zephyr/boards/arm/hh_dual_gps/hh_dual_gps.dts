/**
 * @file
 *
 * @brief <PERSON><PERSON>hoff Dual GPS board
 */
/dts-v1/;

#include <mem.h>
#include <st/h7/stm32h753Xi.dtsi>
#include <st/h7/stm32h753zitx-pinctrl.dtsi>

/ {
    model = "<PERSON>selhoff Dual GPS Board";
    compatible = "carbon,hh_dual_gps";

    chosen {
		zephyr,sram = &sram0;
		zephyr,flash = &flash0;
		zephyr,dtcm = &dtcm;
		zephyr,code-partition = &slot0_partition;
    };

    aliases {
        watchdog0 = &iwdg1;

        eeprom0 = &i2c_eeprom;
        rtc0 = &i2c_rtc;

        gps0 = &gps_primary;
        gps1 = &gps_secondary;

        // indicator LEDs
        led-status = &status_led;
    };

    gpios {
        compatible = "gpio-keys";

        rtc_nrst {
            gpios = <&gpioc 8 (GPIO_PULL_UP | GPIO_ACTIVE_LOW)>;
            label = "RTC_nRST";
        };
    };

    // status indicators used by GPS firmware
    leds {
        compatible = "gpio-leds";
        run {
            gpios = <&gpioc 0 GPIO_ACTIVE_HIGH>;
            label = "RUN_LED";
        };
        tm_vld {
            gpios = <&gpioc 2 GPIO_ACTIVE_HIGH>;
            label = "TM_VLD_LED";
        };
        pps {
            gpios = <&gpioc 3 GPIO_ACTIVE_HIGH>;
            label = "PPS_LED";
        };
        pos_vld {
            gpios = <&gpioc 6 GPIO_ACTIVE_HIGH>;
            label = "POS_VLD_LED";
        };
        udp {
            gpios = <&gpioc 7 GPIO_ACTIVE_HIGH>;
            label = "UDP_LED";
        };
    };

    // RGB status LED
    status_led: status {
        compatible = "gpio-leds";

        status_r {
            gpios = <&gpiof 11 GPIO_ACTIVE_HIGH>;
            label = "Status LED (Red)";
        };

        status_g {
            gpios = <&gpiof 12 GPIO_ACTIVE_HIGH>;
            label = "Status LED (Green)";
        };

        status_b {
            gpios = <&gpiof 13 GPIO_ACTIVE_HIGH>;
            label = "Status LED (Blue)";
        };
    };

    // board revision resistor straps
    hw_rev {
        compatible = "cr,hw-rev-straps";

        gpios =
            <&gpiod 7 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>,
            <&gpiog 9 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>,
            <&gpiog 10 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>,
            <&gpiog 12 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>;
    };
};

// Ethernet MAC
&mac {
	status = "okay";
    pinctrl-0 = <&eth_mdc_pc1
                 &eth_rxd0_pc4
                 &eth_rxd1_pc5
                 &eth_ref_clk_pa1
                 &eth_mdio_pa2
                 &eth_crs_dv_pa7
                 &eth_tx_en_pg11
                 &eth_txd0_pg13
                 &eth_txd1_pb13>;
    pinctrl-names = "default";

    // PHY reset lines
    // reset-gpios = <&gpiof 2 (GPIO_ACTIVE_LOW | GPIO_OPEN_DRAIN)>;
};

// Primary GPS module
&spi6 {
    status = "okay";
    label = "GPS1-SPI";

    pinctrl-0 = <
        &spi6_sck_pa5
        &spi6_miso_pa6
        &spi6_mosi_pb5
    >;
    pinctrl-names = "default";

    cs-gpios = <&gpioa 4 GPIO_ACTIVE_LOW>;

    gps_primary: gps_zed_f9p@0 {
        compatible = "cr,gps-zed-f9p";
        label = "GPS_ZED-F9P Primary";
        reg = <0>;
        spi-max-frequency = <1000000>;

        pps-gpios = <&gpiof 1 GPIO_ACTIVE_HIGH>;
        reset-gpios = <&gpioa 3 (GPIO_ACTIVE_LOW | GPIO_OPEN_DRAIN)>;
        interrupt-gpios = <&gpioa 10 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
    };
};

// Secondary GPS module (SPI6)
&spi5 {
    status = "okay";
    label = "GPS2-SPI";

    pinctrl-0 = <
        &spi5_sck_pf7
        &spi5_miso_pf8
        &spi5_mosi_pf9
    >;
    pinctrl-names = "default";

    cs-gpios = <&gpiof 10 GPIO_ACTIVE_LOW>;

    gps_secondary: gps_zed_f9p@0 {
        compatible = "cr,gps-zed-f9p";
        label = "GPS_ZED-F9P";
        reg = <0>;
        spi-max-frequency = <1000000>;

        pps-gpios = <&gpiof 14 GPIO_ACTIVE_HIGH>;
        reset-gpios = <&gpiof 6 (GPIO_ACTIVE_LOW | GPIO_OPEN_DRAIN)>;
        interrupt-gpios = <&gpiob 1 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
    };
};

// Main I2C bus (EEPROM + RTC)
&i2c2 {
    status = "okay";

    pinctrl-0 = <
        &i2c2_sda_pb11
        &i2c2_scl_pb10
    >;
    pinctrl-names = "default";

    clock-frequency = <I2C_BITRATE_FAST>;

    i2c_eeprom: atmel_at24@a0 {
        compatible = "atmel,at24";
        label = "I2C EEPROM";
        reg = <0x50>;

        size = <256>;
        pagesize = <16>;
        address-width = <8>;
        timeout = <5>;
    };

    i2c_rtc: ds3231@68 {
        compatible = "maxim,ds3231";
        label = "I2C RTC";
        reg = <0x68>;

        isw-gpios = <&gpiob 7 (GPIO_PULL_UP | GPIO_ACTIVE_LOW)>;
    };
};

// Auxiliary (sensor) I2C bus
&i2c4 {
    status = "okay";

    pinctrl-0 = <
        &i2c4_sda_pd13
        &i2c4_scl_pd12
    >;
    pinctrl-names = "default";

    clock-frequency = <I2C_BITRATE_FAST>;

    // 6 axis IMU
    imu0: bmi270@68 {
        compatible = "cr,bmi270";
        label = "IMU";

        reg = <0x68>;
    };
};


// 8MHz crystal on PH0/PH1
&clk_hse {
	status = "okay";
	clock-frequency = <DT_FREQ_M(8)>;
};

// 32.768kHz crystal on PC14/PC15
&clk_lse {
	// status = "okay";
	status = "disabled";
};

&pll {
    status = "okay";

    // input: 8MHz / 1
    clocks = <&clk_hse>;
    div-m = <1>;

    // PLL frequency = 960MHz
    mul-n = <120>;
    // PLL1.P = 320MHz
    div-p = <3>;
    // PLL1.Q = 240MHz
    div-q = <4>;
    // PLL1.R = 320MHz
    div-r = <3>;
};

&pll3 {
    // input: 8MHz / 1
    clocks = <&clk_hse>;
    div-m = <1>;

    // PLL frequency = 960MHz
    mul-n = <120>;
};

&rcc {
    clocks = <&pll>;

    // SYSCLK = 320MHz
    clock-frequency = <DT_FREQ_M(320)>;

    d1cpre = <1>;
    // AXI = 160MHz
    hpre = <2>;
    // APB3 = 80MHz
    d1ppre = <2>;
    // APB1 = 80MHz
    d2ppre1 = <2>;
    // APB2 = 80MHz
    d2ppre2 = <2>;
    // APB4 = 80MHz
    d3ppre = <2>;
};

// MCU-internal watchdog
&iwdg1 {
    status = "okay";
};

// enable all GPIO banks
&gpioa {
    status = "okay";
};

&gpiob {
    status = "okay";
};

&gpioc {
    status = "okay";
};

&gpiod {
    status = "okay";
};

&gpioe {
    status = "okay";
};

&gpiof {
    status = "okay";
};

&gpiog {
    status = "okay";
};

// more built in peripherals
&rtc {
    status = "okay";
};

&rng {
    status = "okay";
};

// flash partitions
&flash0 {
    partitions {
        compatible = "fixed-partitions";
        #address-cells = <1>;
        #size-cells = <1>;

        // bootloader
        boot_partition: partition@0 {
            label = "mcuboot";
            reg = <0x00000000 DT_SIZE_K(256)>;
            read-only;
        };

        // priamry app (640K)
        slot0_partition: partition@40000 {
            label = "image-0";
            reg = <0x00040000 DT_SIZE_K(640)>;
        };

        // (128K unused)

        // backup app (640K)
        slot1_partition: partition@100000 {
            label = "image-1";
            reg = <0x00100000 DT_SIZE_K(640)>;
        };

        // (128K unused)

        // EEPROM emulation zone
        storage_partition: partition@1c0000 {
            label = "storage";
            reg = <0x001c0000 DT_SIZE_K(128)>;
        };

        // swap slot (128K)
        scratch_partition: partition@1e0000 {
            label = "image-scratch";
            reg = <0x001e0000 DT_SIZE_K(128)>;
        };
    };
};

// enable all DMA controllers
&dma1 {
    status = "okay";
};

&dma2 {
    status = "okay";
};

&dmamux1 {
    status = "okay";
};

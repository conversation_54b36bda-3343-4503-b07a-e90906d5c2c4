#include <dt-bindings/pinctrl/gd32f450z(e-g-i-k)xx-pinctrl.h>

&pinctrl {
    adc0_default: adc0_default {
        group1 {
            pinmux = <ADC012_IN3_PA3>, <ADC01_IN4_PA4>;
        };
    };

    enet_default: enet_default {
        group1 {
            pinmux = <ETH_RMII_REF_CLK_PA1>, <ETH_MDIO_PA2>,
                 <ETH_RMII_CRS_DV_PA7>, <ETH_MDC_PC1>,
                 <ETH_RMII_RXD0_PC4>, <ETH_RMII_RXD1_PC5>,
                 <ETH_RMII_TX_EN_PB11>, <ETH_RMII_TXD0_PB12>,
                 <ETH_RMII_TXD1_PB13>;
        };
    };

    timer1_default: timer1_default {
        group1 {
            pinmux = <TIMER1_CH0_PA5>, <TIMER1_CH1_PB9>;
        };
    };

    i2c1_default: i2c1_default {
        group1 {
            pinmux = <I2C1_SDA_PF0>, <I2C1_SCL_PF1>;
        };
    };

    // Laser intensity is PA8 / TIM0_CH0 on Rev X1 Scanner
    // Testing PWM with PA6 / TIM2_CH0 on gigalite
	pwm2_default: pwm2_default {
		group1 {
			pinmux = <TIMER2_CH3_PB1>;
		};
	};

};

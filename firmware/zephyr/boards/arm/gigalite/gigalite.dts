/dts-v1/;

#include <mem.h>
#include <gigadevice/gd32f4xx/gd32f450.dtsi>
#include "gigalite-pinctrl.dtsi"
#include <zephyr/dt-bindings/adc/adc.h>
#include <zephyr/dt-bindings/pwm/pwm.h>

/ {
    model = "GigaLite";
    compatible = "carbon,gigalite";

    chosen {
        zephyr,sram = &addsram;
        zephyr,dtcm = &sram2;
        zephyr,flash = &flash0;
        zephyr,flash-controller = &fmc;
        zephyr,code-partition = &slot0_partition;
    };

    aliases {
        eeprom0 = &eeprom0;
    };

    soc {
        sram1: memory@2001c000 {
            compatible = "mmio-sram";
            reg = <0x2001c000 DT_SIZE_K(16)>;
        };

        sram2: memory@20020000 {
            compatible = "zephyr,memory-region", "mmio-sram";
            reg = <0x20020000 DT_SIZE_K(64)>;
            // SRAM2 will double down as DTCM which is used by RTT
            zephyr,memory-region = "DTCM";
        };

        addsram: memory@20030000 {
            compatible = "mmio-sram";
            reg = <0x20030000 DT_SIZE_K(256)>;
        };
    };
};

&flash0 {
    reg = <0x08000000 DT_SIZE_K(2048)>;

    partitions {
        compatible = "fixed-partitions";
        #address-cells = <1>;
        #size-cells = <1>;

        boot_partition: partition@0 {
            label = "mcuboot";
            reg = <0x0 0x20000>;
        };

        slot0_partition: partition@20000 {
            label = "image-0";
            reg = <0x20000 0x60000>;
        };

        slot1_partition: partition@80000 {
            label = "image-1";
            reg = <0x80000 0x60000>;
        };

        scratch_partition: partition@e0000 {
            label = "image-scratch";
            reg = <0xe0000 0x20000>;
        };
    };
};

&i2c1 {
    // ATMEL AT24C04D
    status = "okay";
    clock-frequency = <I2C_BITRATE_FAST>;
    pinctrl-0 = <&i2c1_default>;
    pinctrl-names = "default";

    eeprom0: atmel_at24@a0 {
        compatible = "atmel,at24";
        reg = <0x50>;
        size = <256>;
        pagesize = <16>;
        address-width = <8>;
        timeout = <5>;
        label = "ATMEL_AT24";
    };
};

&gpioa {
	status = "okay";
};

&gpiob {
	status = "okay";
};

&gpioc {
	status = "okay";
};

&gpiod {
	status = "okay";
};

&adc0 {
    status = "okay";
    pinctrl-0 = <&adc0_default>;
    pinctrl-names = "default";

    #address-cells = <1>;
    #size-cells = <0>;

    channel@3 {
        reg = <3>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME_DEFAULT>;
        zephyr,resolution = <12>;
    };

    channel@4 {
        reg = <4>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME_DEFAULT>;
        zephyr,resolution = <12>;
    };
};

&enet {
    status = "okay";
    pinctrl-0 = <&enet_default>;
    pinctrl-names = "default";
};

&timer1 {
    status = "okay";
    qdec64k_tilt: qdec64k {
        compatible = "gd,gd32-qdec64k";
        status = "okay";
        label = "QDEC64K_TILT";
        pinctrl-0 = <&timer1_default>;
        pinctrl-names = "default";
    };
};


&timer2 {
    status = "okay";
    prescaler = <119>;
	pwm_laser: pwm {
		status = "okay";
		pinctrl-0 = <&pwm2_default>;
		pinctrl-names = "default";
	};
};
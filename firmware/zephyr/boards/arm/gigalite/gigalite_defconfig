CONFIG_SOC_SERIES_GD32F4XX=y
CONFIG_SOC_GD32F450=y
CONFIG_BOARD_GIGALITE=y

CONFIG_ARM_MPU=y
CONFIG_HW_STACK_PROTECTION=y
CONFIG_CORTEX_M_SYSTICK=y

# SEGGER RTT
CONFIG_SEGGER_RTT_SECTION_DTCM=y
CONFIG_USE_SEGGER_RTT=y
CONFIG_CONSOLE=y
CONFIG_RTT_CONSOLE=y
CONFIG_SHELL_BACKEND_RTT=y

# no serial port
CONFIG_SERIAL=n

CONFIG_GPIO=y

# enable pin controller
CONFIG_PINCTRL=y

# random generator is not implemented yet
CONFIG_ENTROPY_GENERATOR=y
CONFIG_TEST_RANDOM_GENERATOR=y

# ethernet configuration
CONFIG_ETH_GD32_HAL_PHY_ADDRESS=0
CONFIG_ETH_GD32_HAL_PHY_LAN8700=y

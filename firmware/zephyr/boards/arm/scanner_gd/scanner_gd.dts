/dts-v1/;

#include <mem.h>
#include <gigadevice/gd32f4xx/gd32f450.dtsi>
#include "scanner_gd-pinctrl.dtsi"
#include <zephyr/dt-bindings/adc/adc.h>
#include <zephyr/dt-bindings/pwm/pwm.h>

/ {
    model = "Scanner GD";
    compatible = "carbon,scanner_gd";

    chosen {
        zephyr,sram = &addsram;
        zephyr,dtcm = &sram2;
        zephyr,flash = &flash0;
        zephyr,flash-controller = &fmc;
        zephyr,code-partition = &slot0_partition;
        zephyr,can-primary = &spi_can;
    };

    aliases {
        watchdog0 = &fwdgt;
        qdec64k-pan = &qdec64k_pan;
        qdec64k-tilt = &qdec64k_tilt;
        zlcb0 = &zlcb0;
        eeprom0 = &eeprom0;

        laser = &laser;
    };

    lpm: lpm {
        compatible = "cr,therm-laser-power-meter";
        status-in-gpios = <&gpiog 2 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;

        io-channels = <&adc1 6>, <&adc1 4>, <&adc1 5>;
        io-channel-names = "current", "therm1", "therm2";
    };

    // laser control interface
    laser: laser {
        compatible = "cr,laser-interface";

        power-meter = <&lpm>;

        fire-gpios = <&gpiog 0 (GPIO_ACTIVE_HIGH)>;
        pwms = <&laser_pwm_intensity 0 PWM_KHZ(20) PWM_POLARITY_INVERTED>;
        pwm-names = "intensity";
    };

    soc {
        sram1: memory@2001c000 {
            compatible = "mmio-sram";
            reg = <0x2001c000 DT_SIZE_K(16)>;
        };

        sram2: memory@20020000 {
            compatible = "zephyr,memory-region", "mmio-sram";
            reg = <0x20020000 DT_SIZE_K(64)>;
            // SRAM2 will double down as DTCM which is used by RTT
            zephyr,memory-region = "DTCM";
        };

        addsram: memory@20030000 {
            compatible = "mmio-sram";
            reg = <0x20030000 DT_SIZE_K(256)>;
        };
    };
};

&fwdgt {
    status = "okay";
};

&gpioa {
    status = "okay";
};

&gpiob {
    status = "okay";
};

&gpioc {
    status = "okay";
};

&gpiod {
    status = "okay";
};

&gpioe {
    status = "okay";
};

&gpiof {
    status = "okay";
};

&gpiog {
    status = "okay";
};

&flash0 {
    reg = <0x08000000 DT_SIZE_K(2048)>;

    partitions {
        compatible = "fixed-partitions";
        #address-cells = <1>;
        #size-cells = <1>;

        enterboot_partition: partition@0 {
            label = "enter-mcuboot";
            reg = <0x0 0x04000>;
        };

        slot0_partition: partition@04000 {
            label = "image-0";
            reg = <0x04000 0x3C000>;
        };

        boot_partition: partition@80000 {
            label = "mcuboot";
            reg = <0x80000 0x40000>;
        };

        scratch_partition: partition@e0000 {
            label = "image-scratch";
            reg = <0xe0000 0x20000>;
        };

        slot1_partition: partition@104000 {
            label = "image-1";
            reg = <0x104000 0x3C000>;
        };
    };
};

&adc1 {
    status = "okay";
    pinctrl-0 = <&adc1_default>;
    pinctrl-names = "default";
    #address-cells = <1>;
    #size-cells = <0>;
    channel@4 {
        reg = <4>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME_DEFAULT>;
        zephyr,resolution = <12>;
    };

    channel@5 {
        reg = <5>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME_DEFAULT>;
        zephyr,resolution = <12>;
    };

    channel@6 {
        reg = <6>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME_DEFAULT>;
        zephyr,resolution = <12>;
    };
};

&spi1 {
    pinctrl-0 = <&spi1_default>;
    pinctrl-names = "default";
    status = "okay";
    spi_can: mcp2515@0 {
        compatible = "microchip,mcp2515";
        spi-max-frequency = < 4000000 >;
        int-gpios = <&gpiob 6 GPIO_ACTIVE_LOW>;
        status = "okay";
        label = "SPI_CAN";
        reg = < 0x0 >;
        osc-freq = <16000000>;
        bus-speed = < 1000000 >;
        sjw = < 0x1 >;
        prop-seg = < 1 >;
        phase-seg1 = < 4 >;
        phase-seg2 = < 2 >;
        #address-cells = < 0x1 >;
        #size-cells = < 0x0 >;
    };
};

&i2c0 {
    // ATMEL AT24C04D
    status = "okay";
    clock-frequency = <I2C_BITRATE_FAST>;
    pinctrl-0 = <&i2c0_default>;
    pinctrl-names = "default";
    eeprom0: atmel_at24@a0 {
        compatible = "atmel,at24";
        reg = <0x50>;
        size = <256>;
        pagesize = <16>;
        address-width = <8>;
        timeout = <5>;
        label = "ATMEL_AT24";
    };
};

&i2c1 {
    status = "okay";
    clock-frequency = <I2C_BITRATE_STANDARD>;
    pinctrl-0 = <&i2c1_default>;
    pinctrl-names = "default";
    ll_max14574: ll_max1457@77 {
        compatible = "eo,liquid-lens-max14574";
        reg = < 0x77 >; /*EE >> */
        status = "okay";
    };
};

&enet {
    status = "okay";
    pinctrl-0 = <&enet_default>;
    pinctrl-names = "default";
    fixed-link {
        speed = <100>;
        full-duplex;
    };
};

&timer0 {
    status = "okay";
    prescaler = <119>;
    laser_pwm_intensity: pwm {
        status = "okay";
        pinctrl-0 = <&pwm0_default>;
        pinctrl-names = "default";
    };
};

&timer2 {
    status = "okay";
    qdec64k_tilt: qdec64k {
        compatible = "gd,gd32-qdec64k";
        status = "okay";
        label = "QDEC64K_TILT";
        pinctrl-0 = <&timer2_default>;
        pinctrl-names = "default";
    };
};

&timer3 {
    status = "okay";
    qdec64k_pan: qdec64k {
        compatible = "gd,gd32-qdec64k";
        status = "okay";
        label = "QDEC64K_PAN";
        pinctrl-0 = <&timer3_default>;
        pinctrl-names = "default";
    };
};

&timer7 {
    status = "okay";
    prescaler = <119>;
    zlcb0: zlcb {
        compatible = "gd,gd32-zlcb";
        status = "okay";
        label = "ZLCB0";
    };
};

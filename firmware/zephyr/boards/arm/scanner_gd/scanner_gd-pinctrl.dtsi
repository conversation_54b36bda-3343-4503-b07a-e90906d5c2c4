#include <dt-bindings/pinctrl/gd32f450z(e-g-i-k)xx-pinctrl.h>

&pinctrl {
    adc1_default: adc1_default {
        group1 {
            pinmux = <ADC01_IN4_PA4>, <ADC01_IN5_PA5>, <ADC01_IN6_PA6>;
        };
    };

    spi1_default: spi1_default {

        group1 {
            pinmux = <SPI1_NSS_PB9>, <SPI1_MISO_PB14>,
                <SPI1_MOSI_PB15>, <SPI1_SCK_PB10>;
        };
    };

    i2c0_default: i2c0_default {
        group1 {
            pinmux = <I2C0_SDA_PB7>, <I2C0_SCL_PB8>;
        };
    };

    i2c1_default: i2c1_default {
        group1 {
            pinmux = <I2C1_SDA_PC12>, <I2C1_SCL_PF1>;
        };
    };

    enet_default: enet_default {
        group1 {
            pinmux = <ETH_RMII_REF_CLK_PA1>, <ETH_MDIO_PA2>,
                 <ETH_RMII_CRS_DV_PA7>, <ETH_MDC_PC1>,
                 <ETH_RMII_RXD0_PC4>, <ETH_RMII_RXD1_PC5>,
                 <ETH_RMII_TX_EN_PB11>, <ETH_RMII_TXD0_PB12>,
                 <ETH_RMII_TXD1_PB13>;
        };
    };

    pwm0_default: pwm0_default {
        group1 {
            pinmux = <TIMER0_CH0_PA8>;
        };
    };

    timer2_default: timer2_default {
        group1 {
            pinmux = <TIMER2_CH0_PC6>, <TIMER2_CH1_PC7>; // Tilt Encoder
        };
    };

    timer3_default: timer3_default {
        group1 {
            pinmux = <TIMER3_CH0_PD12>, <TIMER3_CH1_PD13>; // Pan Encoder
        };
    };

};

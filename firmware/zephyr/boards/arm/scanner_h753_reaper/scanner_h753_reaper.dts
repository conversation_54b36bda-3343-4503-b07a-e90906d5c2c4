/**
 * @file
 * @brief STM32H753 scanner device tree
 *
 * This is for the modern board revision (1001150) for Reaper
 *
 * \section{DMA Assignments}
 *
 * DMAMUX1:
 *  0. UART4 TX
 *  1. UART4 RX
 */
/dts-v1/;

#include <st/h7/stm32h753Xi.dtsi>
#include <st/h7/stm32h753zitx-pinctrl.dtsi>

#include <mem.h>
#include <zephyr/dt-bindings/adc/adc.h>

/ {
    model = "Laser Scanner for Reaper";
    compatible = "carbon,scanner_reaper";

    chosen {
        zephyr,can-primary = &can2;

		zephyr,sram = &sram0;
		zephyr,flash = &flash0;
		zephyr,dtcm = &dtcm;
		zephyr,code-partition = &slot0_partition;
    };

    aliases {
        watchdog0 = &iwdg1;
        qdec64k-pan = &qdec64k_pan;
        qdec64k-tilt = &qdec64k_tilt;
        zlcb0 = &zlcb0;
        eeprom0 = &emu_eeprom;

        // indicator LEDs
        led-status = &status_led;
        led-debug = &dbg_led;

        // laser IO
        laser = &laser;

        die-temp = &die_temp;
    };

    // die temp sensor (via ADC 3)
    die_temp: dietemp {
        compatible = "st,stm32-temp-cal";
        status = "okay";
        label = "die temp";

        ts-cal1-addr = <0x1FF1E820>;
        ts-cal2-addr = <0x1FF1E840>;
        ts-cal1-temp = <30>;
        ts-cal2-temp = <110>;
        ts-cal-vrefanalog = <3300>;
        io-channels = <&adc3 18>;
    };

    // emulated EEPROM (in flash)
    emu_eeprom: emu_eeprom {
        status = "okay";
        compatible = "zephyr,emu-eeprom";
        label = "emulated eeprom";

        size = <DT_SIZE_K(4)>;
        pagesize = <DT_SIZE_K(8)>;
        partition = <&storage_partition>;

        // shadow in RAM; also delay erase until entire block is used
        rambuf;
        partition-erase;
    };

    // firing board
    firing_board {
        // switchable +24V power
        firing_board_24v: power {
            compatible = "regulator-fixed";
            label = "firing board 24V";
            regulator-name = "firing board 24V";
            enable-gpios = <&gpiof 6 GPIO_ACTIVE_HIGH>;
        };
    };

    target_cam {
        // switchable +24V power
        target_cam_24v: power {
            compatible = "regulator-fixed";
            label = "target cam 24V";
            regulator-name = "target cam 24V";
            enable-gpios = <&gpiof 0 GPIO_ACTIVE_HIGH>;
        };
    };

    // Board revision via GPIO input straps
    hw_rev {
        compatible = "cr,hw-rev-straps";

        gpios =
            <&gpioa 3 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>,
            <&gpioa 4 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>,
            <&gpioa 5 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>,
            <&gpioa 6 (GPIO_ACTIVE_HIGH | GPIO_PULL_DOWN)>;
    };

    // Discrete (external) watchdog
    ext_watchdog {
        compatible = "cr,stwd100";

        enable-gpios = <&gpioe 0 GPIO_ACTIVE_LOW>;
        pet-gpios = <&gpioe 1 GPIO_ACTIVE_HIGH>;
    };

    // laser power meter
    lpm: lpm {
        compatible = "cr,therm-laser-power-meter";

        io-channels = <&adc3 9>, <&adc1 2>, <&adc1 6>;
        io-channel-names = "photodiode", "therm1", "therm2";
    };

    // laser control interface
    laser: laser {
        compatible = "cr,laser-interface";

        power-meter = <&lpm>;

        fire-gpios = <&gpiof 3 GPIO_ACTIVE_HIGH>;
        pwms = <&laser_pwm_intensity 1 PWM_KHZ(20) PWM_POLARITY_NORMAL>;
        pwm-names = "intensity";

        // laser communications interface
        laser-uart = <&uart4>;
    };

    // Indicator LEDs
    dbg_led: leds {
        compatible = "gpio-leds";

        // Green debug LED
        debug_led {
            gpios = <&gpioa 11 GPIO_ACTIVE_HIGH>;
        };
    };

    // status LED (RGB via PWM)
    status_led: status {
        compatible = "pwm-leds";

        status_r {
            pwms = <&pwm_status 2 PWM_MSEC(5) PWM_POLARITY_NORMAL>;
            label = "Status LED (Red)";
        };

        status_g {
            pwms = <&pwm_status 4 PWM_MSEC(5) PWM_POLARITY_NORMAL>;
            label = "Status LED (Green)";
        };

        status_b {
            pwms = <&pwm_status 3 PWM_MSEC(5) PWM_POLARITY_NORMAL>;
            label = "Status LED (Blue)";
        };
    };
};

// 8MHz crystal on PH0/PH1
&clk_hse {
	status = "okay";
	clock-frequency = <DT_FREQ_M(8)>;

    /delete-property/ hse-bypass;
};

// 32.768kHz crystal on PC14/PC15
&clk_lse {
	// status = "okay";
	status = "disabled";
};

&pll {
    status = "okay";

    // input: 8MHz / 1
    clocks = <&clk_hse>;
    div-m = <1>;

    // PLL frequency = 960MHz
    mul-n = <120>;
    // PLL1.P = 480MHz
    div-p = <2>;
    // PLL1.Q = 240MHz
    div-q = <4>;
    // PLL1.R = 480MHz
    div-r = <2>;
};

&pll3 {
    // input: 8MHz / 1
    clocks = <&clk_hse>;
    div-m = <1>;

    // PLL frequency = 960MHz
    mul-n = <120>;
};

&rcc {
    clocks = <&pll>;

    // SYSCLK = 480MHz
    clock-frequency = <DT_FREQ_M(480)>;

    d1cpre = <1>;
    // AXI = 240MHz
    hpre = <2>;
    // APB3 = 120MHz
    d1ppre = <2>;
    // APB1 = 120MHz
    d2ppre1 = <2>;
    // APB2 = 120MHz
    d2ppre2 = <2>;
    // APB4 = 120MHz
    d3ppre = <2>;
};

// MCU-internal watchdog
&iwdg1 {
    status = "okay";
};

// enable all GPIO banks
&gpioa {
    status = "okay";
};

&gpiob {
    status = "okay";
};

&gpioc {
    status = "okay";
};

&gpiod {
    status = "okay";
};

&gpioe {
    status = "okay";
};

&gpiof {
    status = "okay";
};

&gpiog {
    status = "okay";
};

// more built in peripherals
&rtc {
    status = "okay";
};

&rng {
    status = "okay";
};

// flash partitions
&flash0 {
    partitions {
        compatible = "fixed-partitions";
        #address-cells = <1>;
        #size-cells = <1>;

        // bootloader
        boot_partition: partition@0 {
            label = "mcuboot";
            reg = <0x00000000 DT_SIZE_K(256)>;
            read-only;
        };

        // priamry app (640K)
        slot0_partition: partition@40000 {
            label = "image-0";
            reg = <0x00040000 DT_SIZE_K(640)>;
        };

        // (128K unused)

        // backup app (640K)
        slot1_partition: partition@100000 {
            label = "image-1";
            reg = <0x00100000 DT_SIZE_K(640)>;
        };

        // (128K unused)

        // EEPROM emulation zone
        storage_partition: partition@1c0000 {
            label = "storage";
            reg = <0x001c0000 DT_SIZE_K(128)>;
        };

        // swap slot (128K)
        scratch_partition: partition@1e0000 {
            label = "image-scratch";
            reg = <0x001e0000 DT_SIZE_K(128)>;
        };
    };
};

// dedicate sram1 (128K) as a non-cached area for DMA buffers
&sram1 {
    zephyr,memory-region = "NOCACHE_REGION";
    //zephyr,memory-attr = <( DT_MEM_ARM(ATTR_MPU_RAM_NOCACHE) )>;
    zephyr,memory-region-mpu = "RAM_NOCACHE";
};

// enable all DMA controllers
&dma1 {
    status = "okay";
};

&dma2 {
    status = "okay";
};

&dmamux1 {
    status = "okay";
};

// Ethernet MAC
&mac {
	status = "okay";

    pinctrl-0 = <&eth_mdc_pc1
                 &eth_mdio_pa2
                 &eth_rxd0_pc4
                 &eth_rxd1_pc5
                 &eth_ref_clk_pa1
                 &eth_crs_dv_pa7
                 &eth_tx_en_pg11
                 &eth_txd0_pg13
                 &eth_txd1_pg12>;
    pinctrl-names = "default";

    // PHY reset line
    reset-gpios = <&gpiof 2 (GPIO_ACTIVE_LOW | GPIO_OPEN_DRAIN)>;
};

// status LED (PWM)
&timers1 {
    status = "okay";
    st,prescaler = <1000>;

    pwm_status: pwm {
        status = "okay";

        pinctrl-0 = <&tim1_ch2_pe11 &tim1_ch3_pe13 &tim1_ch4_pe14>;
        pinctrl-names = "default";
    };
};

// pan encoder
&timers3 {
    status = "okay";
    qdec64k_pan: qdec64k {
        compatible = "st,stm32-qdec64k";
        status = "okay";
        label = "QDEC64K_PAN";
        pinctrl-0 = <&tim3_ch1_pc6 &tim3_ch2_pc7>;
        pinctrl-names = "default";
    };
};

// tilt encoder
&timers4 {
    status = "okay";
    qdec64k_tilt: qdec64k {
        compatible = "st,stm32-qdec64k";
        status = "okay";
        label = "QDEC64K_tilt";
        pinctrl-0 = <&tim4_ch1_pb6 &tim4_ch2_pb7>;
        pinctrl-names = "default";
    };
};

// timer direct irq callback
&timers8 {
    status = "okay";
    st,prescaler = <119>;
    zlcb0: zlcb {
        compatible = "st,stm32-zlcb";
        status = "okay";
        label = "ZLCB0";
    };
};

// liquid lens driver I2C bus
&i2c2 {
    status = "okay";
    clock-frequency = <I2C_BITRATE_FAST>;
    pinctrl-0 = <&i2c2_sda_pb11 &i2c2_scl_pb10>;
    pinctrl-names = "default";

    ll_max14574: ll_max1457@77 {
        compatible = "eo,liquid-lens-max14574";
        reg = < 0x77 >; /*EE >> */
        status = "okay";
    };
    ll_max14515: ll_max1415@76 {
        compatible = "eo,liquid-lens-max14515";
        reg = < 0x76 >; /* EC >> 1 */
        status = "okay";
    };

};

// EEPROM I2C bus
&i2c4 {
    status = "okay";
    clock-frequency = <I2C_BITRATE_FAST>;
    pinctrl-0 = <&i2c4_sda_pd13 &i2c4_scl_pd12>;
    pinctrl-names = "default";

    // NOTE: EEPROM is DNP
    eeprom0: atmel_at24@a0 {
        status = "disabled";

        compatible = "atmel,at24";
        reg = <0x50>;
        /**
         * Whole EEPROM is 512 bytes, but address 0x50 will only access half of it.
         * Use address 0x51 to access the other half.
         */
        size = <256>;
        pagesize = <16>;
        address-width = <8>;
        timeout = <5>;
        label = "ATMEL_AT24";
    };
};

// motor controller CAN
&can2 {
    status = "okay";

    pinctrl-0 = <&fdcan2_rx_pb12 &fdcan2_tx_pb13>;
    pinctrl-names = "default";

    // FHs default to 1Mbps after NMT
    bus-speed = <1000000>;
    bus-speed-data = <1000000>;
};

// firing board UART
&uart4 {
    status = "okay";
    label = "BWT-UART";

    pinctrl-0 = <&uart4_rx_pb8 &uart4_tx_pb9>;
    pinctrl-names = "default";

    current-speed = <115200>;
    parity = "none";

    dmas = <&dmamux1 1 64 0x20440>,
        <&dmamux1 2 63 0x20480>;
    dma-names = "tx", "rx";
};

// laser intensity GPIO
&timers13 {
    status = "okay";
    st,prescaler = <1000>;

    laser_pwm_intensity: pwm {
        status = "okay";

        pinctrl-0 = <&tim13_ch1_pf8>;
        pinctrl-names = "default";
    };
};

&adc1 {
    status = "okay";

    pinctrl-0 = <&adc1_inp2_pf11>, <&adc1_inp6_pf12>;
    pinctrl-names = "default";

    #address-cells = <1>;
    #size-cells = <0>;

    // thermistor 1
    therm1: channel@2 {
        reg = <2>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME(ADC_ACQ_TIME_TICKS, 388)>;

        zephyr,resolution = <12>;
        zephyr,oversampling = <4>;
    };

    // thermistor 2
    therm2: channel@6 {
        reg = <6>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME(ADC_ACQ_TIME_TICKS, 388)>;

        zephyr,resolution = <12>;
        zephyr,oversampling = <4>;
    };
};

// we don't use ADC2, but ADC1/2 share interrupts so we must enable both
&adc2 {
    status = "okay";

    pinctrl-0 = <>;
    pinctrl-names = "default";
};

&adc3 {
    status = "okay";

    pinctrl-0 = <&adc3_inp9_pf4>;
    pinctrl-names = "default";

    #address-cells = <1>;
    #size-cells = <0>;

    // laser power meter (via photodiode)
    photodiode: channel@9 {
        reg = <9>;
        zephyr,gain = "ADC_GAIN_1";
        zephyr,reference = "ADC_REF_INTERNAL";
        zephyr,acquisition-time = <ADC_ACQ_TIME(ADC_ACQ_TIME_TICKS, 65)>;
        zephyr,resolution = <12>;
        zephyr,oversampling = <4>;
    };
};

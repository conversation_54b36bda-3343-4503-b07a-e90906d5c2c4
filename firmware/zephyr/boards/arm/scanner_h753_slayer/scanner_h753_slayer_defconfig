CONFIG_SOC_SERIES_STM32H7X=y
CONFIG_SOC_STM32H753XX=y
CONFIG_BOARD_SCANNER_H753_SLAYER=y

CONFIG_ARM_MPU=y
CONFIG_HW_STACK_PROTECTION=y

CONFIG_CORTEX_M_SYSTICK=y

# RTT block stored in DTCM
CONFIG_SEGGER_RTT_SECTION_DTCM=y

# built-in hardware peripherals
CONFIG_CLOCK_CONTROL=y
CONFIG_DMA=y
CONFIG_REGULATOR=y

# don't use SPI
CONFIG_SPI=n
CONFIG_SPI_STM32=n
CONFIG_CAN_MCP2515=n

# ethernet configuration
CONFIG_ETH_STM32_HAL=y
CONFIG_PTP_CLOCK_STM32_HAL=y

# ethernet PHY config: MDIO addr 0, RMII interface
CONFIG_ETH_STM32_HAL_MII=n
CONFIG_ETH_STM32_HAL_PHY_ADDRESS=0
CONFIG_ETH_STM32_RESET_ON_INIT=y

# zlcb interrupts
CONFIG_ZLCB_GD32=n
CONFIG_ZLCB_STM32=y
CONFIG_QDEC64K_STM32=y

# required for bootloader
CONFIG_GPIO=y
CONFIG_PINCTRL=y

# liquid lens driver
CONFIG_LIQUID_LENS_MAX14515=n
CONFIG_LIQUID_LENS_MAX14574=y

CONFIG_LOG_BACKEND_NET_AUTOSTART=n

# helpers for common little things on newly designed boards
CONFIG_HW_REV_STRAPS=y

# EEPROM emulation
#
# The initialization priority is set to the lowest possible (e.g. highest numerical value) such that
# the flash controller the EEPROM emulation depends on will already have been initialized.
# Otherwise we are beholden to the behavior of the linker on how it will order the initialization
# functions for the EEPROM emulation and internal flash which may result in the flash controller not
# being initialized when the EEPROM emulation is.
CONFIG_EEPROM=y
CONFIG_EEPROM_EMULATOR=y
CONFIG_EEPROM_INIT_PRIORITY=99

# no need for I2C EEPROM
CONFIG_EEPROM_AT24=n

# support PWM LED (for status indicator)
CONFIG_LED_PWM=y

# hardware info driver (reading out unique id)
CONFIG_HWINFO=y

# support LED subsystem (for status indicators)
CONFIG_LED=y
CONFIG_LED_GPIO=y
CONFIG_LIB_STATUS_LED=y
CONFIG_LIB_STATUS_LED_COLOR_CONVERSION=y

CONFIG_STM32_TEMP=y

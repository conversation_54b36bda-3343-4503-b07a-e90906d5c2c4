description: |
  STM32 Combined PWM.

  Note: In STM32, combined PWM period is global to combined PWM device.
  Use cases that use different periods must use different combined PWM devices.

compatible: "st,stm32-combo-pwm"

include: [pwm-controller.yaml, base.yaml, pinctrl-device.yaml]

properties:
    label:
      required: true

    pinctrl-0:
      required: false

    pinctrl-names:
      required: false

    sync:
      type: string
      required: true
      enum:
        - "MASTER"
        - "SLAVE_ITR0"
        - "SLAVE_ITR1"
        - "SLAVE_ITR2"
        - "SLAVE_ITR3"
      description: |
        Defines synchronization of this combined PWM timer with other timers.

        In order to enable synchronization, set one of the timers to MASTER, and other
        timers that need to synchronize to this timers to SLAVE_ITRx. See MCU datasheet
        for routing spec between master and slave timers which drives right ITRx
        selection.

        Note that slaves also act as trigger retransmitters, so transitive ITRx setup
        is possible.

    "#pwm-cells":
      const: 2

pwm-cells:
  - channel
  - flags

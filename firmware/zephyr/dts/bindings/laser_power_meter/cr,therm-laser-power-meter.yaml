description: |
  Laser power meter using thermistors


compatible: "cr,therm-laser-power-meter"

include: [base.yaml]
properties:
    label:
      required: false

    io-channels:
      required: true
      type: phandle-array
      description: ADC IO channel used to sample current, thermistors and photodiode
    io-channel-names:
      required: true
      type: string-array
      description: "ADC IO names: supported are `therm1`, `therm2`, `photodiode`, `current`"
    status-in-gpios:
      required: false
      type: phandle-array
      description: The gpio to use for lpsu status

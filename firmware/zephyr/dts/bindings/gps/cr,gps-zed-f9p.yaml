description: GPS ZED-F9P device

compatible: "cr,gps-zed-f9p"

include: spi-device.yaml

properties:
  pps-gpios:
    type: phandle-array
    required: false
    description: GPIO line connected to GPS PPS output
  reset-gpios:
    type: phandle-array
    required: false
    description: GPIO line connected to module reset, driven during init
  interrupt-gpios:
    type: phandle-array
    required: false
    description: GPIO line connected to module interrupt output

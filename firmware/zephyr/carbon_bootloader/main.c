/*
 * Copyright (c) 2012-2014 Wind River Systems, Inc.
 * Copyright (c) 2020 Arm Limited
 * Copyright (c) 2021 Nordic Semiconductor ASA
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <assert.h>
#include <zephyr.h>
#include <drivers/gpio.h>
#include <sys/__assert.h>
#include <drivers/flash.h>
#include <drivers/hwinfo.h>
#include <drivers/timer/system_timer.h>
#include <net/ethernet_mgmt.h>
#include <usb/usb_device.h>
#include <soc.h>
#include <linker/linker-defs.h>

#if IS_ENABLED(CONFIG_NET_DHCPV4)
#include <zephyr/net/net_if.h>
#include <zephyr/net/net_core.h>
#endif

#include "target.h"

#include "bootutil/bootutil_log.h"
#include "bootutil/image.h"
#include "bootutil/bootutil.h"
#include "bootutil/fault_injection_hardening.h"
#include "flash_map_backend/flash_map_backend.h"

#if IS_ENABLED(CONFIG_LIB_GPIO_IP)
#include "lib/gpio_ip/gpio_ip.h" // Carbon Robotics
#endif

#include "lib/smp/smp.h"         // Carbon Robotics

#if IS_ENABLED(CONFIG_LIB_STATUS_LED)
#include <lib/status_led/status_led.h>
#endif

#include <drivers/hw_rev_straps.h>


#ifdef CONFIG_MCUBOOT_SERIAL
#include "boot_serial/boot_serial.h"
#include "serial_adapter/serial_adapter.h"

const struct boot_uart_funcs boot_funcs = {
    .read = console_read,
    .write = console_write
};
#endif

#if defined(CONFIG_BOOT_USB_DFU_WAIT) || defined(CONFIG_BOOT_USB_DFU_GPIO)
#include <usb/class/usb_dfu.h>
#endif

#if CONFIG_MCUBOOT_CLEANUP_ARM_CORE
#include <arm_cleanup.h>
#endif

/* CONFIG_LOG_MINIMAL is the legacy Kconfig property,
 * replaced by CONFIG_LOG_MODE_MINIMAL.
 */
#if (defined(CONFIG_LOG_MODE_MINIMAL) || defined(CONFIG_LOG_MINIMAL))
#define ZEPHYR_LOG_MODE_MINIMAL 1
#endif

#if defined(CONFIG_LOG) && !defined(CONFIG_LOG_IMMEDIATE) && \
    !defined(ZEPHYR_LOG_MODE_MINIMAL)
#ifdef CONFIG_LOG_PROCESS_THREAD
#warning "The log internal thread for log processing can't transfer the log"\
         "well for MCUBoot."
#else
#include <logging/log_ctrl.h>

#define BOOT_LOG_PROCESSING_INTERVAL K_MSEC(30) /* [ms] */

/* log are processing in custom routine */
K_THREAD_STACK_DEFINE(boot_log_stack, CONFIG_MCUBOOT_LOG_THREAD_STACK_SIZE);
struct k_thread boot_log_thread;
volatile bool boot_log_stop = false;
K_SEM_DEFINE(boot_log_sem, 1, 1);

/* log processing need to be initalized by the application */
#define ZEPHYR_BOOT_LOG_START() zephyr_boot_log_start()
#define ZEPHYR_BOOT_LOG_STOP() zephyr_boot_log_stop()
#endif /* CONFIG_LOG_PROCESS_THREAD */
#else
/* synchronous log mode doesn't need to be initalized by the application */
#define ZEPHYR_BOOT_LOG_START() do { } while (false)
#define ZEPHYR_BOOT_LOG_STOP() do { } while (false)
#endif /* defined(CONFIG_LOG) && !defined(CONFIG_LOG_IMMEDIATE) */

#ifdef CONFIG_SOC_FAMILY_NRF
#include <hal/nrf_power.h>

static inline bool boot_skip_serial_recovery()
{
#if NRF_POWER_HAS_RESETREAS
    uint32_t rr = nrf_power_resetreas_get(NRF_POWER);

    return !(rr == 0 || (rr & NRF_POWER_RESETREAS_RESETPIN_MASK));
#else
    return false;
#endif
}
#else
static inline bool boot_skip_serial_recovery()
{
    return false;
}
#endif

MCUBOOT_LOG_MODULE_REGISTER(mcuboot);

#ifdef CONFIG_MCUBOOT_INDICATION_LED
/*
 * Devicetree helper macro which gets the 'flags' cell from a 'gpios'
 * property, or returns 0 if the property has no 'flags' cell.
 */
#define FLAGS_OR_ZERO(node)                        \
  COND_CODE_1(DT_PHA_HAS_CELL(node, gpios, flags), \
              (DT_GPIO_FLAGS(node, gpios)),        \
              (0))

/*
 * The led0 devicetree alias is optional. If present, we'll use it
 * to turn on the LED whenever the button is pressed.
 */

#define LED0_NODE DT_ALIAS(bootloader_led0)

#if DT_NODE_HAS_STATUS(LED0_NODE, okay) && DT_NODE_HAS_PROP(LED0_NODE, gpios)
#define LED0_GPIO_LABEL DT_GPIO_LABEL(LED0_NODE, gpios)
#define LED0_GPIO_PIN DT_GPIO_PIN(LED0_NODE, gpios)
#define LED0_GPIO_FLAGS (GPIO_OUTPUT | FLAGS_OR_ZERO(LED0_NODE))
#else
/* A build error here means your board isn't set up to drive an LED. */
#error "Unsupported board: led0 devicetree alias is not defined"
#endif

const static struct device *led;

void led_init(void)
{

  led = device_get_binding(LED0_GPIO_LABEL);
  if (led == NULL) {
    BOOT_LOG_ERR("Didn't find LED device %s\n", LED0_GPIO_LABEL);
    return;
  }

  gpio_pin_configure(led, LED0_GPIO_PIN, LED0_GPIO_FLAGS);
  gpio_pin_set(led, LED0_GPIO_PIN, 0);

}
#endif

void os_heap_init(void);

// initialize MAC only if we don't use GPIO-IP library too
#define GPIO_IP_SETS_MAC (IS_ENABLED(CONFIG_LIB_GPIO_IP))
#define USE_FIXED_MACADDR (IS_ENABLED(CONFIG_SOC_SERIES_STM32H7X) && IS_ENABLED(CONFIG_HWINFO) && !GPIO_IP_SETS_MAC)

#if USE_FIXED_MACADDR
/**
 * @brief Calculate a non-cryptographic hash over the given data (for MAC address)
 */
static inline uint32_t macaddr_hash(const uint8_t *in, const size_t length) {
    uint32_t hash = 5381;

    for(size_t i = 0; i < length; ++in, ++i) {
        hash = ((hash << 5) + hash) + (*in);
    }

    return hash;
}

/**
 * @brief Apply fixed MAC address
 */
static void macaddr_set() {
    int err;
    struct ethernet_req_params params;
    char uniqueId[16], macAddr[6];
    uint32_t hwRev = 0;

#if IS_ENABLED(CONFIG_HW_REV_STRAPS)
    static const struct device *gHwRevs = DEVICE_DT_GET_OR_NULL(DT_PATH(hw_rev));
    if(gHwRevs) {
        err = hw_rev_straps_get(gHwRevs, &hwRev);
        if(err) {
            LOG_WRN("Failed to get %s: %d", "hw rev", err);
        }
    }
#endif

    int uniqueIdLen = hwinfo_get_device_id(uniqueId, sizeof(uniqueId));
    if(uniqueIdLen < 0) {
        LOG_WRN("Failed to get %s: %d", "chip id", uniqueIdLen);
        return;
    }

    const uint32_t low = macaddr_hash(uniqueId, uniqueIdLen);

    memset(macAddr, 0, sizeof(macAddr));
    macAddr[0] = 0b01110010; // unicast, locally administered
    macAddr[1] = hwRev;
    memcpy(macAddr+2, &low, 4);

    LOG_INF("MAC address: %02x:%02x:%02x:%02x:%02x:%02x", macAddr[0], macAddr[1], macAddr[2],
            macAddr[3], macAddr[4], macAddr[5]);

    memset(&params, 0, sizeof(params));
    memcpy(params.mac_address.addr, macAddr, sizeof(macAddr));

    err = net_mgmt(NET_REQUEST_ETHERNET_SET_MAC_ADDRESS, net_if_get_default(),
                             &params, sizeof(params));
    if(err) {
        LOG_WRN("Failed to set MAC address: %d", err);
        return;
    }
}
#endif

/**
 * @brief Set up networking for bootloader
 *
 * If the device has a unique ID (silicon ID on STM32s) use it to derive a static MAC address for
 * Ethernet; start DHCP if enabled.
 */
static void bootloader_net_init() {
#if USE_FIXED_MACADDR
    macaddr_set();
#endif

    // start the DHCP client
#if IS_ENABLED(CONFIG_NET_DHCPV4)
    LOG_INF("Starting DHCP client");
    net_dhcpv4_start(net_if_get_default());
#endif
}

#if defined(CONFIG_ARM)

#ifdef CONFIG_SW_VECTOR_RELAY
extern void *_vector_table_pointer;
#endif

struct arm_vector_table {
    uint32_t msp;
    uint32_t reset;
};

extern void sys_clock_disable(void);

static void do_boot(struct boot_rsp *rsp)
{
    struct arm_vector_table *vt;
    uintptr_t flash_base;
    int rc;

    /* The beginning of the image is the ARM vector table, containing
     * the initial stack pointer address and the reset vector
     * consecutively. Manually set the stack pointer and jump into the
     * reset vector
     */
    rc = flash_device_base(rsp->br_flash_dev_id, &flash_base);
    assert(rc == 0);

    vt = (struct arm_vector_table *)(flash_base +
                                     rsp->br_image_off +
                                     rsp->br_hdr->ih_hdr_size);

    irq_lock();
#ifdef CONFIG_SYS_CLOCK_EXISTS
    sys_clock_disable();
#endif
#ifdef CONFIG_USB
    /* Disable the USB to prevent it from firing interrupts */
    usb_disable();
#endif
#if CONFIG_MCUBOOT_CLEANUP_ARM_CORE
    cleanup_arm_nvic(); /* cleanup NVIC registers */

#ifdef CONFIG_CPU_CORTEX_M7
    /* Disable instruction cache and data cache before chain-load the application */
    SCB_DisableDCache();
    SCB_DisableICache();
#endif

#if CONFIG_CPU_HAS_ARM_MPU || CONFIG_CPU_HAS_NXP_MPU
    z_arm_clear_arm_mpu_config();
#endif

#if defined(CONFIG_BUILTIN_STACK_GUARD) && \
    defined(CONFIG_CPU_CORTEX_M_HAS_SPLIM)
    /* Reset limit registers to avoid inflicting stack overflow on image
     * being booted.
     */
    __set_PSPLIM(0);
    __set_MSPLIM(0);
#endif

#endif /* CONFIG_MCUBOOT_CLEANUP_ARM_CORE */

#ifdef CONFIG_BOOT_INTR_VEC_RELOC
#if defined(CONFIG_SW_VECTOR_RELAY)
    _vector_table_pointer = vt;
#ifdef CONFIG_CPU_CORTEX_M_HAS_VTOR
    SCB->VTOR = (uint32_t)__vector_relay_table;
#endif
#elif defined(CONFIG_CPU_CORTEX_M_HAS_VTOR)
    SCB->VTOR = (uint32_t)vt;
#endif /* CONFIG_SW_VECTOR_RELAY */
#else /* CONFIG_BOOT_INTR_VEC_RELOC */
#if defined(CONFIG_CPU_CORTEX_M_HAS_VTOR) && defined(CONFIG_SW_VECTOR_RELAY)
    _vector_table_pointer = _vector_start;
    SCB->VTOR = (uint32_t)__vector_relay_table;
#endif
#endif /* CONFIG_BOOT_INTR_VEC_RELOC */

    __set_MSP(vt->msp);
#if CONFIG_MCUBOOT_CLEANUP_ARM_CORE
    __set_CONTROL(0x00); /* application will configures core on its own */
    __ISB();
#endif
    ((void (*)(void))vt->reset)();
}

#elif defined(CONFIG_BOARD_SCANNER_GD)
#define SRAM_BASE_ADDRESS	0x20030000

static void copy_img_to_SRAM(int slot, unsigned int hdr_offset)
{
    const struct flash_area *fap;
    int area_id;
    int rc;
    unsigned char *dst = (unsigned char *)(SRAM_BASE_ADDRESS + hdr_offset);

    BOOT_LOG_INF("Copying image to SRAM");

    area_id = flash_area_id_from_image_slot(slot);
    rc = flash_area_open(area_id, &fap);
    if (rc != 0) {
        BOOT_LOG_ERR("flash_area_open failed with %d\n", rc);
        goto done;
    }

    rc = flash_area_read(fap, hdr_offset, dst, fap->fa_size - hdr_offset);
    if (rc != 0) {
        BOOT_LOG_ERR("flash_area_read failed with %d\n", rc);
        goto done;
    }

done:
    flash_area_close(fap);
}

/* Entry point (.ResetVector) is at the very beginning of the image.
 * Simply copy the image to a suitable location and jump there.
 */
static void do_boot(struct boot_rsp *rsp)
{
    void *start;

    BOOT_LOG_INF("br_image_off = 0x%x\n", rsp->br_image_off);
    BOOT_LOG_INF("ih_hdr_size = 0x%x\n", rsp->br_hdr->ih_hdr_size);

    /* Copy from the flash to HP SRAM */
    copy_img_to_SRAM(0, rsp->br_hdr->ih_hdr_size);

    /* Jump to entry point */
    start = (void *)(SRAM_BASE_ADDRESS + rsp->br_hdr->ih_hdr_size);
    ((void (*)(void))start)();
}

#else
/* Default: Assume entry point is at the very beginning of the image. Simply
 * lock interrupts and jump there. This is the right thing to do for X86 and
 * possibly other platforms.
 */
static void do_boot(struct boot_rsp *rsp)
{
    uintptr_t flash_base;
    void *start;
    int rc;

    rc = flash_device_base(rsp->br_flash_dev_id, &flash_base);
    assert(rc == 0);

    start = (void *)(flash_base + rsp->br_image_off +
                     rsp->br_hdr->ih_hdr_size);

    /* Lock interrupts and dive into the entry point */
    irq_lock();
    ((void (*)(void))start)();
}
#endif

#if defined(CONFIG_LOG) && !defined(CONFIG_LOG_IMMEDIATE) &&\
    !defined(CONFIG_LOG_PROCESS_THREAD) && !defined(ZEPHYR_LOG_MODE_MINIMAL)
/* The log internal thread for log processing can't transfer log well as has too
 * low priority.
 * Dedicated thread for log processing below uses highest application
 * priority. This allows to transmit all logs without adding k_sleep/k_yield
 * anywhere else int the code.
 */

/* most simple log processing theread */
void boot_log_thread_func(void *dummy1, void *dummy2, void *dummy3)
{
    (void)dummy1;
    (void)dummy2;
    (void)dummy3;

     log_init();

     while (1) {
             if (log_process() == false) {
                    if (boot_log_stop) {
                        break;
                    }
                    k_sleep(BOOT_LOG_PROCESSING_INTERVAL);
             }
     }

     k_sem_give(&boot_log_sem);
}

void zephyr_boot_log_start(void)
{
        /* start logging thread */
        k_thread_create(&boot_log_thread, boot_log_stack,
                K_THREAD_STACK_SIZEOF(boot_log_stack),
                boot_log_thread_func, NULL, NULL, NULL,
                K_HIGHEST_APPLICATION_THREAD_PRIO, 0,
                BOOT_LOG_PROCESSING_INTERVAL);

        k_thread_name_set(&boot_log_thread, "logging");
}

void zephyr_boot_log_stop(void)
{
    boot_log_stop = true;

    /* wait until log procesing thread expired
     * This can be reworked using a thread_join() API once a such will be
     * available in zephyr.
     * see https://github.com/zephyrproject-rtos/zephyr/issues/21500
     */
    (void)k_sem_take(&boot_log_sem, K_FOREVER);

    /* extra wait for shell logging */
    k_sleep(K_MSEC(100));
}
#endif/* defined(CONFIG_LOG) && !defined(CONFIG_LOG_IMMEDIATE) &&\
        !defined(CONFIG_LOG_PROCESS_THREAD) */

#if defined(CONFIG_MCUBOOT_SERIAL) || defined(CONFIG_BOOT_USB_DFU_GPIO)
static bool detect_pin(const char* port, int pin, uint32_t expected, int delay)
{
    int rc;
    int detect_value;
    struct device const *detect_port;

    detect_port = device_get_binding(port);
    __ASSERT(detect_port, "Error: Bad port for boot detection.\n");

    /* The default presence value is 0 which would normally be
     * active-low, but historically the raw value was checked so we'll
     * use the raw interface.
     */
    rc = gpio_pin_configure(detect_port, pin,
                            GPIO_INPUT | GPIO_PULL_UP);
    __ASSERT(rc == 0, "Failed to initialize boot detect pin.\n");

    rc = gpio_pin_get_raw(detect_port, pin);
    detect_value = rc;

    __ASSERT(rc >= 0, "Failed to read boot detect pin.\n");

    if (detect_value == expected) {
        if (delay > 0) {
#ifdef CONFIG_MULTITHREADING
            k_sleep(K_MSEC(50));
#else
            k_busy_wait(50000);
#endif

            /* Get the uptime for debounce purposes. */
            int64_t timestamp = k_uptime_get();

            for(;;) {
                rc = gpio_pin_get_raw(detect_port, pin);
                detect_value = rc;
                __ASSERT(rc >= 0, "Failed to read boot detect pin.\n");

                /* Get delta from when this started */
                uint32_t delta = k_uptime_get() -  timestamp;

                /* If not pressed OR if pressed > debounce period, stop. */
                if (delta >= delay || detect_value != expected) {
                    break;
                }

                /* Delay 1 ms */
#ifdef CONFIG_MULTITHREADING
                k_sleep(K_MSEC(1));
#else
                k_busy_wait(1000);
#endif
            }
        }
    }

    return detect_value == expected;
}
#endif

void main(void)
{
    struct boot_rsp rsp;
    int rc;
    fih_int fih_rc = FIH_FAILURE;

    MCUBOOT_WATCHDOG_FEED();

#if !defined(MCUBOOT_DIRECT_XIP)
    BOOT_LOG_INF("Starting bootloader");
#else
    BOOT_LOG_INF("Starting Direct-XIP bootloader");
#endif

#ifdef CONFIG_MCUBOOT_INDICATION_LED
    /* LED init */
    led_init();
#endif

#if IS_ENABLED(CONFIG_LIB_STATUS_LED)
  leds_init();
  leds_set_status(LED_COLOR_OFF);
#endif

    os_heap_init();

    ZEPHYR_BOOT_LOG_START();

    bootloader_net_init();

// Carbon Robotics
#if CONFIG_LIB_GPIO_IP
    // reaper has two fixed addresses, selected between by a single address pin
#if CONFIG_BOARD_SCANNER_H753_REAPER
    const gpio_ip_data gpio_cfg[] = {
        {.label = "GPIOD", .pin = 0},
    };
    const char *addresses[] = {
        "*********",
        "*********"
    };

    if(!gpio_ip_set_ip_list(addresses, gpio_cfg, 1, NULL)) {
        BOOT_LOG_ERR("Failed to set IP address using GPIO");
    }
#else
    // non-reaper sets address directly from binary value
#if CONFIG_BOARD_SCANNER_H753_SLAYER
#define GPIO_IP_LABEL "GPIOD"
  gpio_ip_data gpio_cfg[] = {{.label = GPIO_IP_LABEL, .pin = 0},
                             {.label = GPIO_IP_LABEL, .pin = 1},
                             {.label = GPIO_IP_LABEL, .pin = 2},
                             {.label = GPIO_IP_LABEL, .pin = 3}};
#elif CONFIG_BOARD_SCANNER_GD
#define GPIO_IP_LABEL "GPIOG"
  gpio_ip_data gpio_cfg[] = {{.label = GPIO_IP_LABEL, .pin = 4},
                             {.label = GPIO_IP_LABEL, .pin = 5},
                             {.label = GPIO_IP_LABEL, .pin = 6},
                             {.label = GPIO_IP_LABEL, .pin = 7}};
#else
#define GPIO_IP_LABEL "GPIOE"
  gpio_ip_data gpio_cfg[] = {{.label = GPIO_IP_LABEL, .pin = 0},
                             {.label = GPIO_IP_LABEL, .pin = 1},
                             {.label = GPIO_IP_LABEL, .pin = 2},
                             {.label = GPIO_IP_LABEL, .pin = 3}};
#endif
  if(!gpio_ip_set_ip(CONFIG_NET_CONFIG_MY_IPV4_ADDR, CONFIG_NET_CONFIG_MY_IPV4_ADDR, gpio_cfg,
                                      sizeof(gpio_cfg) / sizeof(gpio_cfg[0]))) {
    BOOT_LOG_ERR("Failed to set IP address using GPIO");
    }
#endif
#endif

    // set up SMP interface: must go AFTER network init
    start_smp_lib();     // Carbon Robotics
    wait_for_smp_idle(); // Carbon Robotics

    (void)rc;

#if (!defined(CONFIG_XTENSA) && DT_HAS_CHOSEN(zephyr_flash_controller))
    if (!flash_device_get_binding(DT_LABEL(DT_CHOSEN(zephyr_flash_controller)))) {
        BOOT_LOG_ERR("Flash device %s not found",
             DT_LABEL(DT_CHOSEN(zephyr_flash_controller)));
        while (1)
            ;
    }
#elif (defined(CONFIG_XTENSA) && defined(JEDEC_SPI_NOR_0_LABEL))
    if (!flash_device_get_binding(JEDEC_SPI_NOR_0_LABEL)) {
        BOOT_LOG_ERR("Flash device %s not found", JEDEC_SPI_NOR_0_LABEL);
        while (1)
            ;
    }
#endif

#ifdef CONFIG_MCUBOOT_SERIAL
    if (detect_pin(CONFIG_BOOT_SERIAL_DETECT_PORT,
                   CONFIG_BOOT_SERIAL_DETECT_PIN,
                   CONFIG_BOOT_SERIAL_DETECT_PIN_VAL,
                   CONFIG_BOOT_SERIAL_DETECT_DELAY) &&
            !boot_skip_serial_recovery()) {
#ifdef CONFIG_MCUBOOT_INDICATION_LED
        gpio_pin_set(led, LED0_GPIO_PIN, 1);
#endif

        BOOT_LOG_INF("Enter the serial recovery mode");
        rc = boot_console_init();
        __ASSERT(rc == 0, "Error initializing boot console.\n");
        boot_serial_start(&boot_funcs);
        __ASSERT(0, "Bootloader serial process was terminated unexpectedly.\n");
    }
#endif

#if defined(CONFIG_BOOT_USB_DFU_GPIO)
    if (detect_pin(CONFIG_BOOT_USB_DFU_DETECT_PORT,
                   CONFIG_BOOT_USB_DFU_DETECT_PIN,
                   CONFIG_BOOT_USB_DFU_DETECT_PIN_VAL,
                   CONFIG_BOOT_USB_DFU_DETECT_DELAY)) {
#ifdef CONFIG_MCUBOOT_INDICATION_LED
        gpio_pin_set(led, LED0_GPIO_PIN, 1);
#endif
        rc = usb_enable(NULL);
        if (rc) {
            BOOT_LOG_ERR("Cannot enable USB");
        } else {
            BOOT_LOG_INF("Waiting for USB DFU");
            wait_for_usb_dfu(K_FOREVER);
            BOOT_LOG_INF("USB DFU wait time elapsed");
        }
    }
#elif defined(CONFIG_BOOT_USB_DFU_WAIT)
    rc = usb_enable(NULL);
    if (rc) {
        BOOT_LOG_ERR("Cannot enable USB");
    } else {
        BOOT_LOG_INF("Waiting for USB DFU");
        wait_for_usb_dfu(K_MSEC(CONFIG_BOOT_USB_DFU_WAIT_DELAY_MS));
        BOOT_LOG_INF("USB DFU wait time elapsed");
    }
#endif

    FIH_CALL(boot_go, fih_rc, &rsp);
    if (fih_not_eq(fih_rc, FIH_SUCCESS)) {
        BOOT_LOG_ERR("Unable to find bootable image");
#if IS_ENABLED(CONFIG_LIB_STATUS_LED)
        // flash red LED if no bootable image
        size_t counter = 0;
        while (1) {
          counter++;
          leds_set_status((counter & 1) ? LED_COLOR_RED : LED_COLOR_OFF);
          k_sleep(K_SECONDS(1));
        } // Carbon Robotics, SMP Server Running
#else
        while (1) {
            k_sleep(K_SECONDS(1));
        } // Carbon Robotics, SMP Server Running
#endif
    }

    BOOT_LOG_INF("Bootloader chainload address offset: 0x%x",
                 rsp.br_image_off);

    // white status LED when jumping to app
#if IS_ENABLED(CONFIG_LIB_STATUS_LED)
    leds_set_status(LED_COLOR_WHITE);
#endif

#if defined(MCUBOOT_DIRECT_XIP)
    BOOT_LOG_INF("Jumping to the image slot");
#else
    BOOT_LOG_INF("Jumping to the first image slot");
#endif
    ZEPHYR_BOOT_LOG_STOP();
    do_boot(&rsp);

    BOOT_LOG_ERR("Never should get here");
    while (1)
        ;
}

CONFIG_LIB_GPIO_IP=y
CONFIG_NET_CONFIG_MY_IPV4_ADDR="*********"
CONFIG_NET_CONFIG_PEER_IPV4_ADDR="*********"

# required to set the MAC address programmatically
CONFIG_NET_MGMT=y
CONFIG_NET_MGMT_EVENT=y
CONFIG_NET_L2_ETHERNET_MGMT=y

# no PTP stuff
CONFIG_PTP_CLOCK_STM32_HAL=n

# no need for ADC in bootloader
CONFIG_ADC=n

# do not pull in math routines for status LED library
CONFIG_LIB_STATUS_LED_COLOR_CONVERSION=n

CONFIG_PWM=n
CONFIG_LED_PWM=n

# no UARTs
CONFIG_SERIAL=n
CONFIG_UART_ASYNC_API=n
CONFIG_UART_STM32=n

# no QDEC/timer stuff
CONFIG_QDEC64K_STM32=n
CONFIG_ZLCB_STM32=n

# no liquid lens driver
CONFIG_LIQUID_LENS_MAX14515=n
CONFIG_LIQUID_LENS_MAX14574=n

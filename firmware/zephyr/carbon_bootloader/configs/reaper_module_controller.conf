# need to use DHCP to acquire board address in bootloader, as we do not have access to the
# module configuration in EEPROM used to assign a static IP
CONFIG_NET_DHCPV4=y
CONFIG_NET_ICMPV4_ACCEPT_BROADCAST=y

CONFIG_NET_L2_ETHERNET_MGMT=y

# enable hwinfo driver (get silicon id for mac)
CONFIG_HWINFO=y

# readout of hardware revision
CONFIG_HW_REV_STRAPS=y

# no ADC or sensor stuff
CONFIG_ADC=n
CONFIG_ADC_STM32_SHARED_IRQS=n
CONFIG_SENSOR=n
CONFIG_BME680=n

# no UARTs
CONFIG_SERIAL=n
CONFIG_UART_STM32=n

# reset PHY on boot
CONFIG_ETH_STM32_RESET_ON_INIT=y

# remove PTP support
CONFIG_PTP_CLOCK_STM32_HAL=n

# logging via RTT (for debugging)
CONFIG_USE_SEGGER_RTT=y
CONFIG_SEGGER_RTT_SECTION_DTCM=y

# use the status LED library
CONFIG_LED=y
CONFIG_LED_GPIO=y
CONFIG_LIB_STATUS_LED=y
CONFIG_LIB_STATUS_LED_COLOR_CONVERSION=n

# remove some unused stuff
CONFIG_PWM=n

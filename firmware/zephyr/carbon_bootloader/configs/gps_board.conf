CONFIG_NET_CONFIG_MY_IPV4_ADDR="*********"

# Enabled to fix compiler error "undefined reference to `net_mgmt_NET_REQUEST_ETHERNET_SET_MAC_ADDRESS'". 
#  (Function to set the MAC addr is not compiled in.)
CONFIG_NET_L2_ETHERNET_MGMT=y

# Setting mcumgr log level to Dbg does almost nothing additional vs inf during normal operation.
CONFIG_MCUMGR_SMP_LOG_LEVEL_DBG=y

# Do not know that either of these options are required. 
# Believe that CONFIG_MCUMGR_SMP_UDP_STACK_SIZE fixes a stack overflow issue, but don't remember 100%.
# Because the RAM available on STM32H7 is large, doesn't seem to hurt to increase these sizes to avoid potential mcumgr stack overflows.
CONFIG_MCUMGR_SMP_UDP_STACK_SIZE=4096
CONFIG_MAIN_STACK_SIZE=4096


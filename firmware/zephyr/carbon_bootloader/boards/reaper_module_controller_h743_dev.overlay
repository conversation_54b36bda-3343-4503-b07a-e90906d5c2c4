// get rid of ADCs (not needed in bootloader)
/delete-node/ &adc2;

// also don't need emu eeprom
/delete-node/ &emu_eeprom;

// force using software reset for ethernet PHY
// this is a (possibly hacky) workaround for the hardware reset generator asserting a ~2 second
// long reset pulse, but our code starts executing before that. since the bootloader doesn't
// have many peripherals to initialize, the PHY is still physically held in reset when we try to
// configure the fixed link. the added delay for the software reset ensures we don't have this
// issue but this might be variable to temperature variations on the reset generator
&mac {
    /delete-property/ reset-gpios;
};

// no support for PWM LEDs in the bootloader, drive as GPIO LEDs
/ {
    leds {
        compatible = "gpio-leds";

        // Status LED, red
        status_r {
            gpios = <&gpioe 11 GPIO_ACTIVE_HIGH>;
        };
        // Status LED, green
        status_g {
            gpios = <&gpioe 14 GPIO_ACTIVE_HIGH>;
        };
        // Status LED, blue
        status_b {
            gpios = <&gpioe 13 GPIO_ACTIVE_HIGH>;
        };
    };
};

// get rid of ADCs (and anything that depends thereon)
/delete-node/ &lpm;
/delete-node/ &adc1;
/delete-node/ &adc2;
/delete-node/ &adc3;

// no laser control
/delete-node/ &{/laser};
/ {
    aliases {
        /delete-property/ laser;
    };
};

// no need for EEPROM
/delete-node/ &eeprom0;

// use software PHY reset on boot
&mac {
    /delete-property/ reset-gpios;
};

// drive RGB LED with bare GPIOs rather than PWM
/ {
    status_led_gpio: bootloader_rgb_status {
        compatible = "gpio-leds";

        // Status LED, red
        status_r {
            gpios = <&gpioe 11 GPIO_ACTIVE_HIGH>;
        };
        // Status LED, green
        status_g {
            gpios = <&gpioe 14 GPIO_ACTIVE_HIGH>;
        };
        // Status LED, blue
        status_b {
            gpios = <&gpioe 13 GPIO_ACTIVE_HIGH>;
        };
    };

    aliases {
        led-status = &status_led_gpio;
    };
};

/delete-node/ &status_led;

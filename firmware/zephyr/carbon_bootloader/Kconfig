# Copyright (c) 2017-2020 Linaro Limited
# Copyright (c) 2020 Arm Limited
#
# SPDX-License-Identifier: Apache-2.0
#

mainmenu "MCUboot configuration"

comment "MCUboot-specific configuration options"

# Hidden option to mark a project as MCUboot
config MCUBOOT
	default y
	bool
	select MPU_ALLOW_FLASH_WRITE if ARM_MPU
	select USE_DT_CODE_PARTITION if HAS_FLASH_LOAD_OFFSET
	select MCUBOOT_BOOTUTIL_LIB

config BOOT_USE_MBEDTLS
	bool
	# Hidden option
	default n
	help
	  Use mbedTLS for crypto primitives.

config BOOT_USE_TINYCRYPT
	bool
	# Hidden option
	default n
	# When building for ECDSA, we use our own copy of mbedTLS, so the
	# Zephyr one must not be enabled or the MBEDTLS_CONFIG_FILE macros
	# will collide.
	depends on ! MBEDTLS
	help
	  Use TinyCrypt for crypto primitives.

config BOOT_USE_CC310
	bool
	# Hidden option
	default n
	# When building for ECDSA, we use our own copy of mbedTLS, so the
	# Zephyr one must not be enabled or the MBEDTLS_CONFIG_FILE macros
	# will collide.
	depends on ! MBEDTLS
	help
	  Use cc310 for crypto primitives.

config BOOT_USE_NRF_CC310_BL
	bool
	default n

config NRFXLIB_CRYPTO
	bool
	default n

config NRF_CC310_BL
	bool
	default n

menu "MCUBoot settings"

config SINGLE_APPLICATION_SLOT
	bool "Single slot bootloader"
	default n
	help
	  Single image area is used for application which means that
	  uploading a new application overwrites the one that previously
	  occupied the area.

choice
	prompt "Signature type"
	default BOOT_SIGNATURE_TYPE_RSA

config BOOT_SIGNATURE_TYPE_NONE
	bool "No signature; use only hash check"
	select BOOT_USE_TINYCRYPT

config BOOT_SIGNATURE_TYPE_RSA
	bool "RSA signatures"
	select BOOT_USE_MBEDTLS
	select MBEDTLS

if BOOT_SIGNATURE_TYPE_RSA
config BOOT_SIGNATURE_TYPE_RSA_LEN
	int "RSA signature length"
	range 2048 3072
	default 2048
endif

config BOOT_SIGNATURE_TYPE_ECDSA_P256
	bool "Elliptic curve digital signatures with curve P-256"

if BOOT_SIGNATURE_TYPE_ECDSA_P256
choice
	prompt "Ecdsa implementation"
	default BOOT_ECDSA_TINYCRYPT

config BOOT_ECDSA_TINYCRYPT
	bool "Use tinycrypt"
	select BOOT_USE_TINYCRYPT

config BOOT_ECDSA_CC310
	bool "Use CC310"
	depends on HAS_HW_NRF_CC310
	select BOOT_USE_NRF_CC310_BL
	select NRF_CC310_BL
	select NRFXLIB_CRYPTO
	select BOOT_USE_CC310
endchoice # Ecdsa implementation
endif

config BOOT_SIGNATURE_TYPE_ED25519
	bool "Edwards curve digital signatures using ed25519"

if BOOT_SIGNATURE_TYPE_ED25519
choice
	prompt "Ecdsa implementation"
	default BOOT_ED25519_TINYCRYPT
config BOOT_ED25519_TINYCRYPT
	bool "Use tinycrypt"
	select BOOT_USE_TINYCRYPT
config BOOT_ED25519_MBEDTLS
	bool "Use mbedTLS"
	select BOOT_USE_MBEDTLS
	select MBEDTLS
endchoice
endif

endchoice

config BOOT_SIGNATURE_KEY_FILE
	string "PEM key file"
	default "root-ec-p256.pem" if BOOT_SIGNATURE_TYPE_ECDSA_P256
	default "root-ed25519.pem" if BOOT_SIGNATURE_TYPE_ED25519
	default "root-rsa-3072.pem" if BOOT_SIGNATURE_TYPE_RSA && BOOT_SIGNATURE_TYPE_RSA_LEN=3072
	default "root-rsa-2048.pem" if BOOT_SIGNATURE_TYPE_RSA && BOOT_SIGNATURE_TYPE_RSA_LEN=2048
	default ""
	help
	  You can use either absolute or relative path.
	  In case relative path is used, the build system assumes that it starts
	  from the directory where the MCUBoot KConfig configuration file is
	  located. If the key file is not there, the build system uses relative
	  path that starts from the MCUBoot repository root directory.
	  The key file will be parsed by imgtool's getpub command and a .c source
	  with the public key information will be written in a format expected by
	  MCUboot.

config MCUBOOT_CLEANUP_ARM_CORE
	bool "Perform core cleanup before chain-load the application"
	depends on CPU_CORTEX_M
	default y if !ARCH_SUPPORTS_ARCH_HW_INIT
	help
	  This option instructs MCUboot to perform a clean-up of a set of
	  architecture core HW registers before junping to the application
	  firmware. The clean-up sets these registers to their warm-reset
	  values as specified by the architecture.

	  By default, this option is enabled only if the architecture does
	  not have the functionality to perform such a register clean-up
	  during application firmware boot.

	  Zephyr applications on Cortex-M will perform this register clean-up
	  by default, if they are chain-loadable by MCUboot, so MCUboot does
	  not need to perform such a cleanup itself.

config MBEDTLS_CFG_FILE
	default "mcuboot-mbedtls-cfg.h"

config BOOT_HW_KEY
	bool "Use HW key for image verification"
	default n
	help
	  Use HW key for image verification, otherwise the public key is embedded
	  in MCUBoot. If enabled the public key is appended to the signed image
	  and requires the hash of the public key to be provisioned to the device
	  beforehand.

config BOOT_VALIDATE_SLOT0
	bool "Validate image in the primary slot on every boot"
	default y
	help
	  If y, the bootloader attempts to validate the signature of the
	  primary slot every boot. This adds the signature check time to
	  every boot, but can mitigate against some changes that are
	  able to modify the flash image itself.

if !SINGLE_APPLICATION_SLOT
choice
	prompt "Image upgrade modes"
	default BOOT_SWAP_USING_MOVE if SOC_FAMILY_NRF
	default BOOT_SWAP_USING_SCRATCH

config BOOT_SWAP_USING_SCRATCH
	bool "Swap mode that run with the scratch partition"
	help
	  This is the most conservative swap mode but it can work even on
	  devices with heterogeneous flash page layout.

config BOOT_UPGRADE_ONLY
	bool "Overwrite image updates instead of swapping"
	help
	  If y, overwrite the primary slot with the upgrade image instead
	  of swapping them. This prevents the fallback recovery, but
	  uses a much simpler code path.

config BOOT_SWAP_USING_MOVE
	bool "Swap mode that can run without a scratch partition"
	help
	  If y, the swap upgrade is done in two steps, where first every
	  sector of the primary slot is moved up one sector, then for
	  each sector X in the secondary slot, it is moved to index X in
	  the primary slot, then the sector at X+1 in the primary is
	  moved to index X in the secondary.
	  This allows a swap upgrade without using a scratch partition,
	  but is currently limited to all sectors in both slots being of
	  the same size.

config BOOT_DIRECT_XIP
	bool "Run the latest image directly from its slot"
	help
	  If y, mcuboot selects the newest valid image based on the image version
	  numbers, thereafter the selected image can run directly from its slot
	  without having to move/copy it into the primary slot. For this reason the
	  images must be linked to be executed from the given image slot. Using this
	  mode results in a simpler code path and smaller code size.

endchoice

config BOOT_DIRECT_XIP_REVERT
	bool "Enable the revert mechanism in direct-xip mode"
	depends on BOOT_DIRECT_XIP
	default n
	help
	  If y, enables the revert mechanism in direct-xip similar to the one in
	  swap mode. It requires the trailer magic to be added to the signed image.
	  When a reboot happens without the image being confirmed at runtime, the
	  bootloader considers the image faulty and erases it. After this it will
	  attempt to boot the previous image. The images can also be made permanent
	  (marked as confirmed in advance) just like in swap mode.

config BOOT_BOOTSTRAP
	bool "Bootstrap erased the primary slot from the secondary slot"
	default n
	help
	  If y, enables bootstraping support. Bootstrapping allows an erased
	  primary slot to be initialized from a valid image in the secondary slot.
	  If unsure, leave at the default value.

config BOOT_SWAP_SAVE_ENCTLV
	bool "Save encrypted key TLVs instead of plaintext keys in swap metadata"
	default n
	help
	  If y, instead of saving the encrypted image keys in plaintext in the
	  swap resume metadata, save the encrypted image TLVs. This should be used
	  when there is no security mechanism protecting the data in the primary
	  slot from being dumped. If n is selected (default), the keys are written
	  after being decrypted from the image TLVs and could be read by an
	  attacker who has access to the flash contents of the primary slot (eg
	  JTAG/SWD or primary slot in external flash).
	  If unsure, leave at the default value.

config BOOT_ENCRYPT_RSA
	bool "Support for encrypted upgrade images using RSA"
	default n
	help
	  If y, images in the secondary slot can be encrypted and are decrypted
	  on the fly when upgrading to the primary slot, as well as encrypted
	  back when swapping from the primary slot to the secondary slot. The
	  encryption mechanism used in this case is RSA-OAEP (2048 bits).

config BOOT_ENCRYPT_EC256
	bool "Support for encrypted upgrade images using ECIES-P256"
	default n
	help
	  If y, images in the secondary slot can be encrypted and are decrypted
	  on the fly when upgrading to the primary slot, as well as encrypted
	  back when swapping from the primary slot to the secondary slot. The
	  encryption mechanism used in this case is ECIES using primitives
	  described under "ECIES-P256 encryption" in docs/encrypted_images.md.

config BOOT_ENCRYPT_X25519
	bool "Support for encrypted upgrade images using ECIES-X25519"
	default n
	help
	  If y, images in the secondary slot can be encrypted and are decrypted
	  on the fly when upgrading to the primary slot, as well as encrypted
	  back when swapping from the primary slot to the secondary slot. The
	  encryption mechanism used in this case is ECIES using primitives
	  described under "ECIES-X25519 encryption" in docs/encrypted_images.md.
endif # !SINGLE_APPLICATION_SLOT

config BOOT_MAX_IMG_SECTORS
	int "Maximum number of sectors per image slot"
	default 128
	help
	  This option controls the maximum number of sectors that each of
	  the two image areas can contain. Smaller values reduce MCUboot's
	  memory usage; larger values allow it to support larger images.
	  If unsure, leave at the default value.

config BOOT_ERASE_PROGRESSIVELY
	bool "Erase flash progressively when receiving new firmware"
	default y if SOC_FAMILY_NRF
	help
	 If enabled, flash is erased as necessary when receiving new firmware,
	 instead of erasing the whole image slot at once. This is necessary
	 on some hardware that has long erase times, to prevent long wait
	 times at the beginning of the DFU process.

config MEASURED_BOOT
	bool "Store the boot state/measurements in shared memory"
	default n
	help
	  If enabled, the bootloader will store certain boot measurements such as
	  the hash of the firmware image in a shared memory area. This data can
	  be used later by runtime services (e.g. by a device attestation service).

config BOOT_SHARE_DATA
	bool "Save application specific data in shared memory area"
	default n

choice
	prompt "Fault injection hardening profile"
	default BOOT_FIH_PROFILE_OFF

config BOOT_FIH_PROFILE_OFF
	bool "No hardening against hardware level fault injection"
	help
	  No hardening in SW against hardware level fault injection: power or
	  clock glitching, etc.

config BOOT_FIH_PROFILE_LOW
	bool "Moderate level hardening against hardware level fault injection"
	help
	  Moderate level hardening: Long global fail loop to avoid break out,
	  control flow integrity check to discover discrepancy in expected code
	  flow.

config BOOT_FIH_PROFILE_MEDIUM
	bool "Medium level hardening against hardware level fault injection"
	help
	  Medium level hardening: Long global fail loop to avoid break out,
	  control flow integrity check to discover discrepancy in expected code
	  flow, double variables to discover register or memory corruption.

config BOOT_FIH_PROFILE_HIGH
	bool "Maximum level hardening against hardware level fault injection"
	select MBEDTLS
	help
	  Maximum level hardening: Long global fail loop to avoid break out,
	  control flow integrity check to discover discrepancy in expected code
	  flow, double variables to discover register or memory corruption, random
	  delays to make code execution less predictable. Random delays requires an
	  entropy source.

endchoice

choice BOOT_USB_DFU
	prompt "USB DFU"
	default BOOT_USB_DFU_NO

config BOOT_USB_DFU_NO
	prompt "Disabled"

config BOOT_USB_DFU_WAIT
	bool "Wait for a prescribed duration to see if USB DFU is invoked"
	select USB
	select USB_DFU_CLASS
	select IMG_MANAGER
	help
	  If y, MCUboot waits for a prescribed duration of time to allow
	  for USB DFU to be invoked. Please note DFU always updates the
	  slot1 image.

config BOOT_USB_DFU_GPIO
	bool "Use GPIO to detect whether to trigger DFU mode"
	select USB
	select USB_DFU_CLASS
	select IMG_MANAGER
	help
	  If y, MCUboot uses GPIO to detect whether to invoke USB DFU.

endchoice

config BOOT_USB_DFU_WAIT_DELAY_MS
	int "USB DFU wait duration"
	depends on BOOT_USB_DFU_WAIT
	default 12000
	help
	  Milliseconds to wait for USB DFU to be invoked.

if BOOT_USB_DFU_GPIO

config BOOT_USB_DFU_DETECT_PORT
	string "GPIO device to trigger USB DFU mode"
	default GPIO_0 if SOC_FAMILY_NRF
	help
	  Zephyr GPIO device that contains the pin used to trigger
	  USB DFU.

config BOOT_USB_DFU_DETECT_PIN
	int "Pin to trigger USB DFU mode"
	default 6 if BOARD_NRF9160DK_NRF9160
	default 11 if BOARD_NRF52840DK_NRF52840
	default 13 if BOARD_NRF52DK_NRF52832
	default 23 if BOARD_NRF5340_DK_NRF5340_CPUAPP || BOARD_NRF5340_DK_NRF5340_CPUAPPNS
	default 43 if BOARD_BL5340_DVK_CPUAPP || BOARD_BL5340_DVK_CPUAPPNS
	help
	  Pin on the DFU detect port that triggers DFU mode.

config BOOT_USB_DFU_DETECT_PIN_VAL
	int "USB DFU detect pin trigger value"
	default 0
	range 0 1
	help
	  Logic value of the detect pin that triggers USB DFU mode.

config BOOT_USB_DFU_DETECT_DELAY
	int "Serial detect pin detection delay time [ms]"
	default 0
	help
	  Used to prevent the bootloader from loading on button press.
	  Useful for powering on when using the same button as
	  the one used to place the device in bootloader mode.

endif # BOOT_USB_DFU_GPIO

config ZEPHYR_TRY_MASS_ERASE
	bool "Try to mass erase flash when flashing MCUboot image"
	default y
	help
	  If y, attempt to configure the Zephyr build system's "flash"
	  target to mass-erase the flash device before flashing the
	  MCUboot image. This ensures the scratch and other partitions
	  are in a consistent state.

	  This is not available for all targets.

config BOOT_USE_BENCH
        bool "Enable benchmark code"
        default n
        help
          If y, adds support for simple benchmarking that can record
          time intervals between two calls.  The time printed depends
          on the particular Zephyr target, and is generally ticks of a
          specific board-specific timer.

module = MCUBOOT
module-str = MCUBoot bootloader
source "subsys/logging/Kconfig.template.log_config"

config MCUBOOT_LOG_THREAD_STACK_SIZE
	int "Stack size for the MCUBoot log processing thread"
	depends on LOG && !LOG_IMMEDIATE
	default 2048 if COVERAGE_GCOV
	default 1024 if NO_OPTIMIZATIONS
	default 1024 if XTENSA
	default 4096 if (X86 && X86_64)
	default 4096 if ARM64
	default 768
	help
	  Set the internal stack size for MCUBoot log processing thread.

menuconfig MCUBOOT_SERIAL
	bool "MCUboot serial recovery"
	default n
	select REBOOT
	select GPIO
	select SERIAL
	select UART_INTERRUPT_DRIVEN
	select BASE64
	select TINYCBOR
	help
	  If y, enables a serial-port based update mode. This allows
	  MCUboot itself to load update images into flash over a UART.
	  If unsure, leave at the default value.

if MCUBOOT_SERIAL

choice
	prompt "Serial device"
	default BOOT_SERIAL_UART if !BOARD_NRF52840DONGLE_NRF52840
	default BOOT_SERIAL_CDC_ACM if BOARD_NRF52840DONGLE_NRF52840

config BOOT_SERIAL_UART
	bool "UART"
	# SERIAL and UART_INTERRUPT_DRIVEN already selected

config BOOT_SERIAL_CDC_ACM
	bool "CDC ACM"
	select USB
	select USB_DEVICE_STACK
	select USB_CDC_ACM

endchoice

config MCUBOOT_INDICATION_LED
	bool "Turns on LED indication when device is in DFU"
	default n
	help
	  Device device activates the LED while in bootloader mode.
	  bootloader-led0 alias must be set in the device's .dts
	  definitions for this to work.

config BOOT_MAX_LINE_INPUT_LEN
	int "Maximum command line length"
	default 512
	help
	  Maximum length of commands transported over the serial port.

config BOOT_SERIAL_DETECT_PORT
	string "GPIO device to trigger serial recovery mode"
	default GPIO_0 if SOC_FAMILY_NRF
	help
	  Zephyr GPIO device that contains the pin used to trigger
	  serial recovery mode.

config BOOT_SERIAL_DETECT_PIN
	int "Pin to trigger serial recovery mode"
	default 6 if BOARD_NRF9160DK_NRF9160
	default 11 if BOARD_NRF52840DK_NRF52840
	default 13 if BOARD_NRF52DK_NRF52832
	default 23 if BOARD_NRF5340PDK_NRF5340_CPUAPP || BOARD_NRF5340PDK_NRF5340_CPUAPPNS || \
		BOARD_NRF5340DK_NRF5340_CPUAPP || BOARD_NRF5340DK_NRF5340_CPUAPPNS
	help
	  Pin on the serial detect port that triggers serial recovery mode.

config BOOT_SERIAL_DETECT_PIN_VAL
	int "Serial detect pin trigger value"
	default 0
	range 0 1
	help
	  Logic value of the detect pin that triggers serial recovery
	  mode.

config BOOT_SERIAL_DETECT_DELAY
	int "Serial detect pin detection delay time [ms]"
	default 0
	help
	  Used to prevent the bootloader from loading on button press.
	  Useful for powering on when using the same button as
	  the one used to place the device in bootloader mode.

# Workaround for not being able to have commas in macro arguments
DT_CHOSEN_Z_CONSOLE := zephyr,console

config RECOVERY_UART_DEV_NAME
	string "UART Device Name for Recovery UART"
	default "$(dt_chosen_label,$(DT_CHOSEN_Z_CONSOLE))" if HAS_DTS
	default "UART_0"
	depends on BOOT_SERIAL_UART
	help
	  This option specifies the name of UART device to be used for
	  serial recovery.

endif # MCUBOOT_SERIAL

config BOOT_INTR_VEC_RELOC
	bool "Relocate the interrupt vector to the application"
	default n
	depends on SW_VECTOR_RELAY || CPU_CORTEX_M_HAS_VTOR
	help
	  Relocate the interrupt vector to the application before it is started.
	  Select this option if application requires vector relocation,
	  but it doesn't relocate vector in its reset handler.

config UPDATEABLE_IMAGE_NUMBER
	int "Number of updateable images"
	default 1
	range 1 1 if SINGLE_APPLICATION_SLOT
	help
	  Enables support of multi image update.

choice
	prompt "Downgrade prevention"
	optional

config MCUBOOT_DOWNGRADE_PREVENTION
	bool "SW based downgrade prevention"
	depends on BOOT_UPGRADE_ONLY
	help
	  Prevent downgrades by enforcing incrementing version numbers.
	  When this option is set, any upgrade must have greater major version
	  or greater minor version with equal major version. This mechanism
	  only protects against some attacks against version downgrades (for
	  example, a JTAG could be used to write an older version).

config MCUBOOT_HW_DOWNGRADE_PREVENTION
	bool "HW based downgrade prevention"
	help
	  Prevent undesirable/malicious software downgrades. When this option is
	  set, any upgrade must have greater or equal security counter value.
	  Because of the acceptance of equal values it allows for software
	  downgrade to some extent.

endchoice

config BOOT_WATCHDOG_FEED
	bool "Feed the watchdog while doing swap"
	default y if SOC_FAMILY_NRF
	imply NRFX_WDT
	imply NRFX_WDT0
	imply NRFX_WDT1
	help
	  Enables implementation of MCUBOOT_WATCHDOG_FEED() macro which is
	  used to feed watchdog while doing time consuming operations.

endmenu

config MCUBOOT_DEVICE_SETTINGS
	# Hidden selector for device-specific settings
	bool
	default y
        # CPU options
	select MCUBOOT_DEVICE_CPU_CORTEX_M0 if CPU_CORTEX_M0
        # Enable flash page layout if available
	select FLASH_PAGE_LAYOUT if FLASH_HAS_PAGE_LAYOUT
	# Enable flash_map module as flash I/O back-end
	select FLASH_MAP

config MCUBOOT_DEVICE_CPU_CORTEX_M0
	# Hidden selector for Cortex-M0 settings
	bool
	default n
	select SW_VECTOR_RELAY if !CPU_CORTEX_M0_HAS_VECTOR_TABLE_REMAP

comment "Zephyr configuration options"

# Disabling MULTITHREADING provides a code size advantage, but
# it requires peripheral drivers (particularly a flash driver)
# that works properly with the option enabled.
#
# If you know for sure that your hardware will work, you can default
# it to n here. Otherwise, having it on by default makes the most
# hardware work.
config MULTITHREADING
	default y if BOOT_SERIAL_CDC_ACM #usb driver requires MULTITHREADING
	default y if BOOT_USB_DFU_GPIO || BOOT_USB_DFU_WAIT
	default n if SOC_FAMILY_NRF
	default y

config LOG_PROCESS_THREAD
	default n # mcuboot has its own log processing thread

# override USB device name
config USB_DEVICE_PRODUCT
	default "MCUBOOT"

# use MCUboot's own log configuration
config MCUBOOT_BOOTUTIL_LIB_OWN_LOG
	bool
	default n

source "Kconfig.zephyr"

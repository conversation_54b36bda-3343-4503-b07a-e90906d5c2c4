CONFIG_CONSOLE_HANDLER=n # Carbon Robotics
CONFIG_DEBUG=n # Carbon Robotics
CONFIG_PM=n

CONFIG_MAIN_STACK_SIZE=10240
CONFIG_MBEDTLS_CFG_FILE="mcuboot-mbedtls-cfg.h"

CONFIG_BOOT_SWAP_SAVE_ENCTLV=n
CONFIG_BOOT_ENCRYPT_RSA=n
CONFIG_BOOT_ENCRYPT_EC256=n
CONFIG_BOOT_ENCRYPT_X25519=n

CONFIG_BOOT_UPGRADE_ONLY=n
CONFIG_BOOT_BOOTSTRAP=n

### mbedTLS has its own heap
# CONFIG_HEAP_MEM_POOL_SIZE is not set

### We never want <PERSON><PERSON><PERSON><PERSON>'s copy of tinycrypt.  If tinycrypt is needed,
### MCUboot has its own copy in tree.
# CONFIG_TINYCRYPT is not set
# CONFIG_TINYCRYPT_ECC_DSA is not set
# CONFIG_TINYCRYPT_SHA256 is not set

CONFIG_FLASH=y

### Various Zephyr boards enable features that we don't want.
# CONFIG_BT is not set
# CONFIG_BT_CTLR is not set
# CONFIG_I2C is not set

CONFIG_LOG=y
### Ensure Zephyr logging changes don't use more resources
CONFIG_LOG_DEFAULT_LEVEL=0
### Decrease footprint by ~4 KB in comparison to CBPRINTF_COMPLETE=y
CONFIG_CBPRINTF_NANO=y

### Below Is Carbon Robotics Config

CONFIG_MCUBOOT_BOOT_MAX_ALIGN=32
CONFIG_BOOT_SIGNATURE_KEY_FILE="../.crypto/carbon_mcuboot_rsa_2048_key.pem"

# Enable the UDP mcumgr transports.
# Enable mcumgr.
CONFIG_MCUMGR=y
CONFIG_MCUMGR_SMP_NOTIFY_CALLBACK=y

# Some command handlers require a large stack.
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=2048

# Enable most core commands.
CONFIG_MCUMGR_CMD_IMG_MGMT=y
CONFIG_MCUMGR_CMD_OS_MGMT=y
CONFIG_MCUMGR_CMD_STAT_MGMT=n
CONFIG_MCUMGR_SMP_UDP=y
CONFIG_MCUMGR_SMP_UDP_IPV4=y

# Network settings
CONFIG_NETWORKING=y
CONFIG_NET_UDP=y
CONFIG_NET_TCP=n
CONFIG_NET_IPV4=y
CONFIG_NET_IPV6=n
CONFIG_NET_SOCKETS=y
CONFIG_NET_SOCKETS_POSIX_NAMES=y
CONFIG_NET_CONFIG_INIT_TIMEOUT=-1
CONFIG_NET_CONNECTION_MANAGER=y
CONFIG_NET_BUF_USER_DATA_SIZE=64
CONFIG_NET_CONFIG_SETTINGS=y
CONFIG_NET_CONFIG_NEED_IPV4=y
CONFIG_NET_CONFIG_PEER_IPV4_ADDR="*********"
CONFIG_NET_CONFIG_MY_IPV4_NETMASK="*************"
CONFIG_NET_PKT_RX_COUNT=64
CONFIG_NET_BUF_RX_COUNT=256

# Syslog
CONFIG_LOG=y
CONFIG_LOG_BACKEND_NET=y
CONFIG_LOG_BACKEND_NET_SERVER="*********:2442"

# log to RTT
CONFIG_USE_SEGGER_RTT=y

CONFIG_LIB_SMP=y
CONFIG_MCUMGR_SMP_TIMEOUT_MS=5000
CONFIG_MCUMGR_SMP_UDP_IPV6=n

# Combined PWMs
CONFIG_COMBO_PWM=y
CONFIG_COMBO_PWM_STM32=y

# Enable networking
CONFIG_NETWORKING=y
CONFIG_NET_IPV4=y
CONFIG_NET_IPV6=n
CONFIG_NET_TCP=n
CONFIG_NET_UDP=y
CONFIG_NET_SOCKETS=y
CONFIG_NET_SOCKETS_POSIX_NAMES=y
CONFIG_NET_LOG=y
CONFIG_NET_PKT_RX_COUNT=64
CONFIG_NET_BUF_RX_COUNT=256

# Network settings
CONFIG_NET_CONFIG_SETTINGS=y
CONFIG_NET_CONFIG_NEED_IPV4=y
CONFIG_NET_CONFIG_MY_IPV4_ADDR="*********"
CONFIG_NET_CONFIG_PEER_IPV4_ADDR="*********"
CONFIG_NET_CONFIG_MY_IPV4_NETMASK="*************"
# Don't wait for link up to start running
CONFIG_NET_CONFIG_INIT_TIMEOUT=-1
CONFIG_NET_CONNECTION_MANAGER=y
CONFIG_NET_BUF_USER_DATA_SIZE=64
CONFIG_MCUMGR_BUF_COUNT=4

# Port
CONFIG_APP_UDP_PORT=4243

# Syslog
CONFIG_LOG=y
# Disabled as there is a bug in network logging which can
# cause networking to become unresponsive if no syslog server
# is running and there is a large quantity of network traffic to/from the board
CONFIG_LOG_BACKEND_NET=y
CONFIG_LOG_BACKEND_NET_SERVER="*********:2442"

# Nanopb
CONFIG_NANOPB=y

#udp
CONFIG_LIB_UDP=y
CONFIG_PB_REQUESTS_BUFFER_SIZE=5

# Support reboot
CONFIG_REBOOT=y

# Watchdog
CONFIG_LIB_WATCHDOG=y
CONFIG_WATCHDOG=y
CONFIG_WDT_DISABLE_AT_BOOT=n

# EEPROM
CONFIG_EEPROM=y
CONFIG_EEPROM_AT24=y
CONFIG_I2C=y

# MCUBoot
CONFIG_BOOTLOADER_MCUBOOT=y

# Enable mcumgr.
CONFIG_MCUMGR=y
CONFIG_MCUMGR_SMP_NOTIFY_CALLBACK=y
CONFIG_LIB_SMP=y
CONFIG_FLASH=y
CONFIG_MCUBOOT_BOOT_MAX_ALIGN=32

# Some command handlers require a large stack.
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=2048

# Enable statistics and statistic names.
CONFIG_STATS=y
CONFIG_STATS_NAMES=y

# Enable most core commands.
CONFIG_MCUMGR_CMD_IMG_MGMT=y
CONFIG_MCUMGR_CMD_OS_MGMT=y
CONFIG_MCUMGR_CMD_STAT_MGMT=y
CONFIG_MCUMGR_SMP_UDP=y
CONFIG_MCUMGR_SMP_UDP_IPV4=y

# Required by the `taskstat` command.
CONFIG_THREAD_MONITOR=y

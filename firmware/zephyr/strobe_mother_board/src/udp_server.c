#include <logging/log.h>
LOG_MODULE_REGISTER(udp_server, CONFIG_APP_LOG_LEVEL);

#include <errno.h>
#include <lib/udp/udp.h>
#include <pb_decode.h>
#include <pb_encode.h>
#include <sys/reboot.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

#include "eeprom.h"
#include "generated/lib/drivers/nanopb/proto/strobe_control_board.pb.h"
#include "pwm_ctl.h"
#include "udp_server.h"

#if IS_ENABLED(CONFIG_NET_TC_THREAD_COOPERATIVE)
#define THREAD_PRIORITY K_PRIO_COOP(CONFIG_NUM_COOP_PRIORITIES - 1)
#else
#define THREAD_PRIORITY K_PRIO_PREEMPT(8)
#endif

#define RECV_BUFFER_SIZE 1280

static void process_udp();

K_THREAD_DEFINE(udp_thread_id, RECV_BUFFER_SIZE + 1024, process_udp, NULL, NULL, NULL, THREAD_PRIORITY, 0,
                K_TICKS_FOREVER);
UDP_SERVER_DEFINE(npb_udp_server, CONFIG_PB_REQUESTS_BUFFER_SIZE, strobe_control_board_Request_size * 2,
                  THREAD_PRIORITY);

static void handle_ping(diagnostic_Ping *req, diagnostic_Pong *resp) { resp->x = req->x; }

static void handle_version(version_Version_Reply *resp) {
  resp->major = 0;
  resp->minor = 0;
}

void handle_strobe_control(strobe_control_Request *req, strobe_control_Reply *resp) {
  int ret = apply_strobe_settings(req->period_us, req->exposure_us, req->targets_per_predict_ratio);
  if (ret >= 0) {
    HANDLE_CRITICAL(eeprom_save_strobe_settings(req->period_us, req->exposure_us, req->targets_per_predict_ratio));
  } else {
    LOG_INF("Restoring settings from EEPROM");
    uint32_t period_us, exposure_us, targets_per_predict_ratio;
    HANDLE_CRITICAL(eeprom_load_strobe_settings(&period_us, &exposure_us, &targets_per_predict_ratio));
    HANDLE_CRITICAL(apply_strobe_settings(period_us, exposure_us, targets_per_predict_ratio));
  }
  resp->ok = ret >= 0;
}

void handle_camera_power_control(camera_power_control_Request *req, camera_power_control_Reply *resp) {
  LOG_WRN("Unsupported camera power control requested");
  resp->ok = false;
}

static void serve_request(uint8_t *data, uint16_t received, udp_msg_metadata *metadata) {
  strobe_control_board_Request req = strobe_control_board_Request_init_zero;
  pb_istream_t istream = pb_istream_from_buffer(data, received);
  if (!pb_decode(&istream, strobe_control_board_Request_fields, &req)) {
    LOG_WRN("Failed to decode UDP packet with size: %d", received);
    return;
  }

  strobe_control_board_Reply resp = strobe_control_board_Reply_init_zero;
  resp.has_header = true;
  resp.header.requestId = req.header.requestId;

  switch (req.which_request) {
  case strobe_control_board_Request_ping_tag:
    resp.which_reply = strobe_control_board_Reply_pong_tag;
    handle_ping(&req.request.ping, &resp.reply.pong);
    break;
  case strobe_control_board_Request_strobe_control_tag:
    resp.which_reply = strobe_control_board_Reply_strobe_control_tag;
    handle_strobe_control(&req.request.strobe_control, &resp.reply.strobe_control);
    break;
  case strobe_control_board_Request_camera_power_control_tag:
    resp.which_reply = strobe_control_board_Reply_camera_power_control_tag;
    handle_camera_power_control(&req.request.camera_power_control, &resp.reply.camera_power_control);
    break;
  case strobe_control_board_Request_version_tag:
    resp.which_reply = strobe_control_board_Reply_version_tag;
    handle_version(&resp.reply.version);
    break;
  case strobe_control_board_Request_reset_tag:
    sys_reboot(SYS_REBOOT_COLD);
    break;
  default:
    LOG_WRN("Received unknown request with tag: %d", req.which_request);
    return;
  }

  pb_ostream_t ostream = pb_ostream_from_buffer(data, udp_buffer_size(&npb_udp_server));
  if (!pb_encode(&ostream, strobe_control_board_Reply_fields, &resp)) {
    LOG_WRN("Failed to encode nanoPB response");
    return;
  }
  udp_tx(&npb_udp_server, data, ostream.bytes_written, metadata);
}

static void process_udp() {
  for (;;) {
    uint8_t *data;
    udp_msg_metadata metadata;
    uint16_t received = udp_get_data_no_copy(&npb_udp_server, &data, &metadata, 0);
    serve_request(data, received, &metadata);
    udp_get_data_release(&npb_udp_server, data);
  }
}

void start_udp_server() {
  UDP_SERVER_START(npb_udp_server, CONFIG_APP_UDP_PORT);
  k_thread_name_set(udp_thread_id, "udp");
  k_thread_start(udp_thread_id);
}

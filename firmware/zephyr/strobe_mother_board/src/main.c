#include <logging/log.h>
LOG_MODULE_REGISTER(main, CONFIG_APP_LOG_LEVEL);

#include <drivers/gpio.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

#include "eeprom.h"
#include "lib/smp/smp.h"
#include "pwm_ctl.h"
#include "udp_server.h"
#include <lib/watchdog/watchdog.h>

static const struct gpio_dt_spec pps_in = GPIO_DT_SPEC_GET(DT_PATH(gpios, pps_in), gpios);
static struct gpio_callback pps_in_cb_data;

void pps_in_cb(const struct device *dev, struct gpio_callback *cb, uint32_t pins) { LOG_DBG("PPS signalled"); }

static int boot() {
  LOG_INF("Strobe mother board booting");

  /* configure PPS_IN pin */
  HANDLE_UNLIKELY_BOOL(device_is_ready(pps_in.port), ENODEV);
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&pps_in, GPIO_INPUT));
  HANDLE_UNLIKELY(gpio_pin_interrupt_configure_dt(&pps_in, GPIO_INT_EDGE_TO_ACTIVE));
  gpio_init_callback(&pps_in_cb_data, pps_in_cb, BIT(pps_in.pin));
  gpio_add_callback(pps_in.port, &pps_in_cb_data);

  /* apply previous strobe settings */
  uint32_t period_us, exposure_us, targets_per_predict_ratio;
  HANDLE_UNLIKELY(eeprom_load_strobe_settings(&period_us, &exposure_us, &targets_per_predict_ratio));
  HANDLE_UNLIKELY(apply_strobe_settings(period_us, exposure_us, targets_per_predict_ratio));

  return 0;
}

void main() {
  start_watchdog();
  start_smp_lib();
  HANDLE_CRITICAL(boot());
  start_udp_server();
}

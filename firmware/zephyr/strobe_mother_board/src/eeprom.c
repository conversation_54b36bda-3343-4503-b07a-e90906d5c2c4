#include <logging/log.h>
LOG_MODULE_REGISTER(eeprom, CONFIG_APP_LOG_LEVEL);

#include <drivers/eeprom.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

#include "eeprom.h"

#define EEPROM_STROBE_SETTINGS_OFFSET 0x0
#define EEPROM_MAGIC 0xdeadbeefu
#define DEFAULT_PERIOD_US 12500u
#define DEFAULT_EXPOSURE_US 800u
#define DEFAULT_TARGETS_PER_PREDICT 2u

struct strobe_settings {
  uint32_t magic;
  uint32_t period_us;
  uint32_t exposure_us;
  uint32_t targets_per_predict;
};

#define EEPROM_NODE DT_ALIAS(eeprom0)

int eeprom_save_strobe_settings(uint32_t period_us, uint32_t exposure_us, uint32_t targets_per_predict) {
  const struct device *dev = DEVICE_DT_GET(EEPROM_NODE);
  HANDLE_UNLIKELY_BOOL(device_is_ready(dev), ENODEV);

  struct strobe_settings s;
  s.magic = EEPROM_MAGIC;
  s.period_us = period_us;
  s.exposure_us = exposure_us;
  s.targets_per_predict = targets_per_predict;

  HANDLE_UNLIKELY(eeprom_write(dev, EEPROM_STROBE_SETTINGS_OFFSET, &s, sizeof(s)));

  return 0;
}

int eeprom_load_strobe_settings(uint32_t *period_us, uint32_t *exposure_us, uint32_t *targets_per_predict) {
  const struct device *dev = DEVICE_DT_GET(EEPROM_NODE);
  HANDLE_UNLIKELY_BOOL(device_is_ready(dev), ENODEV);

  struct strobe_settings s;
  HANDLE_UNLIKELY(eeprom_read(dev, EEPROM_STROBE_SETTINGS_OFFSET, &s, sizeof(s)));

  if (s.magic != EEPROM_MAGIC) {
    /* first time magic number will not match */
    LOG_INF("Invalid magic number (%d), flashing default settings to EEPROM", s.magic);
    HANDLE_UNLIKELY(eeprom_save_strobe_settings(DEFAULT_PERIOD_US, DEFAULT_EXPOSURE_US, DEFAULT_TARGETS_PER_PREDICT));
    HANDLE_UNLIKELY(eeprom_read(dev, EEPROM_STROBE_SETTINGS_OFFSET, &s, sizeof(s)));
    HANDLE_UNLIKELY_BOOL(s.magic == EEPROM_MAGIC, EPROTO);
  }

  *period_us = s.period_us;
  *exposure_us = s.exposure_us;
  *targets_per_predict = s.targets_per_predict;

  return 0;
}

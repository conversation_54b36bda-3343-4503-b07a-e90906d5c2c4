#include <logging/log.h>
LOG_MODULE_REGISTER(pwm_ctl, CONFIG_APP_LOG_LEVEL);

#include <drivers/combo_pwm.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

#include "pwm_ctl.h"

#define TIMER_MAX_VALUE 65536u
#define CAMERA_TRIGGER_US 50u

static int verify_all_combo_pwm_prescalers(uint64_t cycles_per_second) {
  uint64_t verify_cycles_per_second;

  HANDLE_UNLIKELY(DT_COMBO_PWM_GET_CYCLES_PER_SEC(DT_PATH(combo_pwms, row1_strobe_trigger), &verify_cycles_per_second));
  HANDLE_UNLIKELY_BOOL(verify_cycles_per_second == cycles_per_second, EINVAL);
  HANDLE_UNLIKELY(
      DT_COMBO_PWM_GET_CYCLES_PER_SEC(DT_PATH(combo_pwms, row1_predicts_trigger), &verify_cycles_per_second));
  HANDLE_UNLIKELY_BOOL(verify_cycles_per_second == cycles_per_second, EINVAL);
  HANDLE_UNLIKELY(
      DT_COMBO_PWM_GET_CYCLES_PER_SEC(DT_PATH(combo_pwms, row1_targets_trigger), &verify_cycles_per_second));
  HANDLE_UNLIKELY_BOOL(verify_cycles_per_second == cycles_per_second, EINVAL);

  HANDLE_UNLIKELY(DT_COMBO_PWM_GET_CYCLES_PER_SEC(DT_PATH(combo_pwms, row2_strobe_trigger), &verify_cycles_per_second));
  HANDLE_UNLIKELY_BOOL(verify_cycles_per_second == cycles_per_second, EINVAL);
  HANDLE_UNLIKELY(
      DT_COMBO_PWM_GET_CYCLES_PER_SEC(DT_PATH(combo_pwms, row2_predicts_trigger), &verify_cycles_per_second));
  HANDLE_UNLIKELY_BOOL(verify_cycles_per_second == cycles_per_second, EINVAL);
  HANDLE_UNLIKELY(
      DT_COMBO_PWM_GET_CYCLES_PER_SEC(DT_PATH(combo_pwms, row2_targets_trigger), &verify_cycles_per_second));
  HANDLE_UNLIKELY_BOOL(verify_cycles_per_second == cycles_per_second, EINVAL);

  HANDLE_UNLIKELY(DT_COMBO_PWM_GET_CYCLES_PER_SEC(DT_PATH(combo_pwms, row3_strobe_trigger), &verify_cycles_per_second));
  HANDLE_UNLIKELY_BOOL(verify_cycles_per_second == cycles_per_second, EINVAL);
  HANDLE_UNLIKELY(
      DT_COMBO_PWM_GET_CYCLES_PER_SEC(DT_PATH(combo_pwms, row3_predicts_trigger), &verify_cycles_per_second));
  HANDLE_UNLIKELY_BOOL(verify_cycles_per_second == cycles_per_second, EINVAL);
  HANDLE_UNLIKELY(
      DT_COMBO_PWM_GET_CYCLES_PER_SEC(DT_PATH(combo_pwms, row3_targets_trigger), &verify_cycles_per_second));
  HANDLE_UNLIKELY_BOOL(verify_cycles_per_second == cycles_per_second, EINVAL);

  return 0;
}

int apply_strobe_settings(uint32_t period_us, uint32_t exposure_us, uint32_t targets_per_predict) {
  LOG_INF("Applying strobe settings. Period: %d us, exposure: %d us, targets_per_predict: %d", period_us, exposure_us,
          targets_per_predict);

  if (period_us < 10000) {
    LOG_ERR("Failed to apply strobe settings, period (%d us) is too small", period_us);
    return -EINVAL;
  }

  if (exposure_us < 25) {
    LOG_ERR("Failed to apply strobe settings, exposure (%d us) is too small", exposure_us);
    return -EINVAL;
  }

  if (exposure_us > 1000) {
    LOG_ERR("Failed to apply strobe settings, exposure (%d us) is too large", exposure_us);
    return -EINVAL;
  }

  uint64_t cycles_per_second;
  HANDLE_UNLIKELY(DT_COMBO_PWM_GET_CYCLES_PER_SEC(DT_PATH(combo_pwms, row1_strobe_trigger), &cycles_per_second));
  HANDLE_UNLIKELY(verify_all_combo_pwm_prescalers(cycles_per_second));

  uint32_t period_cycles = period_us * cycles_per_second / USEC_PER_SEC;
  if (period_cycles * targets_per_predict >= TIMER_MAX_VALUE) {
    LOG_ERR("Failed to apply strobe settings, period (%d us) is too large: %d >= %d", period_us, period_cycles,
            TIMER_MAX_VALUE);
    return -EINVAL;
  }

  /* exposure cycle count is rounded up */
  uint32_t exposure_cycles = MAX(1, (exposure_us * cycles_per_second + USEC_PER_SEC - 1) / USEC_PER_SEC);

  /* camera trigger cycle count is rounded up */
  uint32_t camera_trigger_cycles = MAX(1, (CAMERA_TRIGGER_US * cycles_per_second + USEC_PER_SEC - 1) / USEC_PER_SEC);

  /* expose three rows with even offset from each other */
  uint32_t row_offset = period_cycles / 3;

  HANDLE_UNLIKELY(
      DT_COMBO_PWM_PIN_SET_CYCLES(DT_PATH(combo_pwms, row1_strobe_trigger), period_cycles, 0, exposure_cycles));
  HANDLE_UNLIKELY(DT_COMBO_PWM_PIN_SET_CYCLES(DT_PATH(combo_pwms, row1_predicts_trigger),
                                              period_cycles * targets_per_predict, 0, camera_trigger_cycles));
  HANDLE_UNLIKELY(
      DT_COMBO_PWM_PIN_SET_CYCLES(DT_PATH(combo_pwms, row1_targets_trigger), period_cycles, 0, camera_trigger_cycles));

  HANDLE_UNLIKELY(DT_COMBO_PWM_PIN_SET_CYCLES(DT_PATH(combo_pwms, row2_strobe_trigger), period_cycles, row_offset,
                                              exposure_cycles + row_offset));
  HANDLE_UNLIKELY(DT_COMBO_PWM_PIN_SET_CYCLES(DT_PATH(combo_pwms, row2_predicts_trigger),
                                              period_cycles * targets_per_predict, row_offset,
                                              camera_trigger_cycles + row_offset));
  HANDLE_UNLIKELY(DT_COMBO_PWM_PIN_SET_CYCLES(DT_PATH(combo_pwms, row2_targets_trigger), period_cycles, row_offset,
                                              camera_trigger_cycles + row_offset));

  HANDLE_UNLIKELY(DT_COMBO_PWM_PIN_SET_CYCLES(DT_PATH(combo_pwms, row3_strobe_trigger), period_cycles, 2u * row_offset,
                                              exposure_cycles + 2u * row_offset));
  HANDLE_UNLIKELY(DT_COMBO_PWM_PIN_SET_CYCLES(DT_PATH(combo_pwms, row3_predicts_trigger),
                                              period_cycles * targets_per_predict, 2u * row_offset,
                                              camera_trigger_cycles + 2u * row_offset));
  HANDLE_UNLIKELY(DT_COMBO_PWM_PIN_SET_CYCLES(DT_PATH(combo_pwms, row3_targets_trigger), period_cycles, 2u * row_offset,
                                              camera_trigger_cycles + 2u * row_offset));

  return 0;
}

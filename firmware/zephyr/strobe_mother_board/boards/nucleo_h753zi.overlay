/ {

    chosen {
        zephyr,code-partition = &slot0_partition;
    };
    
    aliases {
        watchdog0 = &iwdg1;
        eeprom0 = &eeprom0;
    };

    gpios {
        compatible = "gpio-keys";
        pps_in {
            gpios = <&gpioa 4 GPIO_ACTIVE_HIGH>;
            label = "PPS_IN";
        };
    };

    combo_pwms {
        compatible = "combo-pwms";

        /**
         * Note: In STM32, combined PWM period is global to combined PWM device.
         * Use cases that use different periods must use different combined PWM devices.
         *
         * We share combined PWM devices among rows 1 and 3 as we suspect more asymmetry
         * between middle and side rows.
         */

        row1_strobe_trigger {
            pwms = <&combo_pwm1 1 0x0>;
            label = "ROW1_STROBE_TRIGGER";
        };

        row1_predicts_trigger {
            pwms = <&combo_pwm2 1 0x0>;
            label = "ROW1_PREDICTS_TRIGGER";
        };

        row1_targets_trigger {
            pwms = <&combo_pwm3 1 0x0>;
            label = "ROW1_TARGETS_TRIGGER";
        };

        row2_strobe_trigger {
            pwms = <&combo_pwm4 1 0x0>;
            label = "ROW2_STROBE_TRIGGER";
        };

        row2_predicts_trigger {
            pwms = <&combo_pwm5 1 0x0>;
            label = "ROW2_PREDICTS_TRIGGER";
        };

        row2_targets_trigger {
            pwms = <&combo_pwm15 1 0x0>;
            label = "ROW2_TARGETS_TRIGGER";
        };

        row3_strobe_trigger {
            pwms = <&combo_pwm1 3 0x0>;
            label = "ROW3_STROBE_TRIGGER";
        };

        row3_predicts_trigger {
            pwms = <&combo_pwm2 3 0x0>;
            label = "ROW3_PREDICTS_TRIGGER";
        };

        row3_targets_trigger {
            pwms = <&combo_pwm3 3 0x0>;
            label = "ROW3_TARGETS_TRIGGER";
        };
    };
};

&timers1 {
    status = "okay";
    st,prescaler = <1199>;
    combo_pwm1: combo_pwm {
        compatible = "st,stm32-combo-pwm";
        status = "okay";
        label = "COMBO_PWM_1";
        pinctrl-0 = <&tim1_ch1_pe9 &tim1_ch3_pe13>;
        pinctrl-names = "default";
        sync = "MASTER";
        #pwm-cells = <2>;
    };
};

&timers2 {
    status = "okay";
    st,prescaler = <1199>;
    combo_pwm2: combo_pwm {
        compatible = "st,stm32-combo-pwm";
        status = "okay";
        label = "COMBO_PWM_2";
        pinctrl-0 = <&tim2_ch1_pa5 &tim2_ch3_pb10>;
        pinctrl-names = "default";
        sync = "SLAVE_ITR0";
        #pwm-cells = <2>;
    };
};

&timers3 {
    status = "okay";
    st,prescaler = <1199>;
    combo_pwm3: combo_pwm {
        compatible = "st,stm32-combo-pwm";
        status = "okay";
        label = "COMBO_PWM_3";
        pinctrl-0 = <&tim3_ch1_pa6 &tim3_ch3_pc8>;
        pinctrl-names = "default";
        sync = "SLAVE_ITR0";
        #pwm-cells = <2>;
    };
};

&timers4 {
    status = "okay";
    st,prescaler = <1199>;
    combo_pwm4: combo_pwm {
        compatible = "st,stm32-combo-pwm";
        status = "okay";
        label = "COMBO_PWM_4";
        pinctrl-0 = <&tim4_ch1_pb6>;
        pinctrl-names = "default";
        sync = "SLAVE_ITR0";
        #pwm-cells = <2>;
    };
};

&timers5 {
    status = "okay";
    st,prescaler = <1199>;
    combo_pwm5: combo_pwm {
        compatible = "st,stm32-combo-pwm";
        status = "okay";
        label = "COMBO_PWM_5";
        pinctrl-0 = <&tim5_ch1_pa0>;
        pinctrl-names = "default";
        sync = "SLAVE_ITR0";
        #pwm-cells = <2>;
    };
};

&timers15 {
    status = "okay";
    st,prescaler = <1199>;
    combo_pwm15: combo_pwm {
        compatible = "st,stm32-combo-pwm";
        status = "okay";
        label = "COMBO_PWM_15";
        pinctrl-0 = <&tim15_ch1_pe5>;
        pinctrl-names = "default";
        sync = "SLAVE_ITR0";
        #pwm-cells = <2>;
    };
};

&iwdg1 {
    status = "okay";
};

&i2c1 {
    /* ATMEL AT24C04D */
    eeprom0: atmel_at24@a0 {
        compatible = "atmel,at24";
        reg = <0x50>;
        /**
         * Whole EEPROM is 512 bytes, but address 0x50 will only access half of it.
         * Use address 0x51 to access the other half.
         */
        size = <256>;
        pagesize = <16>;
        address-width = <8>;
        timeout = <5>;
        label = "ATMEL_AT24";
    };
};
&clk_hse {
    /delete-property/ hse-bypass;
};

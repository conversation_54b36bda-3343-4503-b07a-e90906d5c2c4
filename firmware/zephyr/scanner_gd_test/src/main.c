#include <logging/log.h>
LOG_MODULE_REGISTER(main, 3);
#include <devicetree.h>
#include <drivers/can.h>
#include <drivers/eeprom.h>
#include <drivers/liquid_lens.h>
#include <drivers/pwm.h>
#include <drivers/qdec64k.h>
#include <drivers/zlcb.h>
#include <string.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

#define TEST_LOOP_RATE_MS 1

/** SECTION: EEPROM testing */

const struct device *eeprom_dev;

void test_eeprom_init() {
  LOG_INF("Setting up eeprom driver...");
  eeprom_dev = DEVICE_DT_GET(DT_ALIAS(eeprom0));
  HANDLE_UNLIKELY_BOOL(device_is_ready(eeprom_dev), ENODEV);
}

void test_eeprom() {
  char c;
  HANDLE_UNLIKELY(eeprom_read(eeprom_dev, 0, &c, 1));
}

/** SECTION: Zero-latency Callback testing */

// Test ZLCB by setting a flag in callback and checking it in cyclic test loop

const struct device *zlcb_dev;
uint32_t zlcb_flag = 0;

void zlcb_callback(void *arg) {
  // Set flag
  zlcb_flag += 1;
}

void test_zlcb_init() {
  LOG_INF("Setting up Zero-Latency Callback driver...");
  zlcb_dev = DEVICE_DT_GET(DT_ALIAS(zlcb0));
  HANDLE_UNLIKELY_BOOL(device_is_ready(zlcb_dev), ENODEV);
  HANDLE_UNLIKELY(zlcb_set_callback_usec(zlcb_dev, 100, (zlcb_callback_t)zlcb_callback, NULL));
}

void test_zlcb() {
  // Check that flag has been set
  if (zlcb_flag == 0) {
    LOG_ERR("ZLCB failed");
  }
  // Reset the flag
  zlcb_flag = 0;
}

/** SECTION: Liquid Lens testing */

const struct device *ll_dev;

void test_ll_init() {
  LOG_INF("Setting up Liquid Lens device...");

  ll_dev = DEVICE_DT_GET(DT_NODELABEL(ll_max14574));
  HANDLE_UNLIKELY_BOOL(device_is_ready(ll_dev), ENODEV);
}

void test_ll() {
  uint16_t buf;
  int set_ret = liquid_lens_set_value(ll_dev, 1, 100);
  int get_ret = liquid_lens_get_value(ll_dev, 1, &buf);
  LOG_INF("Set returned: %d , Get returned: %d, Value: %d", set_ret, get_ret, buf);
}

/** SECTION: CAN testing */

const struct device *can_dev;

void test_can_init() {
  LOG_INF("Setting up CAN device...");

  can_dev = DEVICE_DT_GET(DT_CHOSEN(zephyr_can_primary));
  HANDLE_UNLIKELY_BOOL(device_is_ready(can_dev), ENODEV);
}

void test_can() {
  struct zcan_frame frame = {.id_type = CAN_STANDARD_IDENTIFIER, .rtr = 0, .id = 0xFF, .fd = 0, .brs = 0, .dlc = 0};
  can_send(can_dev, &frame, K_MSEC(1), NULL, NULL);
}

/** SECTION: Quadrature encoder testing  */

const struct device *tilt_qdec64k_dev;
const struct device *pan_qdec64k_dev;
struct sensor_value tilt_qdec_value;
struct sensor_value pan_qdec_value;

void test_qdec_init() {

  LOG_INF("Setting up quadrature decoder...");

  // Get device
  tilt_qdec64k_dev = DEVICE_DT_GET(DT_NODELABEL(qdec64k_tilt));
  pan_qdec64k_dev = DEVICE_DT_GET(DT_NODELABEL(qdec64k_pan));
  HANDLE_UNLIKELY_BOOL(device_is_ready(tilt_qdec64k_dev), ENODEV);
  HANDLE_UNLIKELY_BOOL(device_is_ready(pan_qdec64k_dev), ENODEV);
}

void test_qdec() {
  // Just make sure we are reading ticks
  HANDLE_CRITICAL(sensor_sample_fetch(pan_qdec64k_dev));
  HANDLE_CRITICAL(sensor_sample_fetch(tilt_qdec64k_dev));
  HANDLE_CRITICAL(sensor_channel_get(pan_qdec64k_dev, SENSOR_CHAN_QDEC64K_TICKS, &pan_qdec_value));
  HANDLE_CRITICAL(sensor_channel_get(tilt_qdec64k_dev, SENSOR_CHAN_QDEC64K_TICKS, &tilt_qdec_value));
}

/** SECTION: PWM testing */

#define PWM_PERIOD_US (10000)
#define PWM_HIGH_TIME_US (5000)

const struct device *pwm_dev;

void test_pwm_init() {

  LOG_INF("Setting up PWM...");

  pwm_dev = DEVICE_DT_GET(DT_NODELABEL(pwm_laser));

  HANDLE_UNLIKELY_BOOL(device_is_ready(pwm_dev), ENODEV);
}

/*
 * Intensity is number between 0 and 1000 giving 1 decimal precision
 */
void test_pwm(int32_t intensity) {
  intensity = 1000 - intensity; // Invert pwm as it gets re-inverted by LPSU board.
  const static uint32_t period_20khz_ns = 50000;
  if (pwm_set(pwm_dev, 0, period_20khz_ns, intensity * period_20khz_ns / 1000, 0) != 0) {
    LOG_ERR("Failed to set PWM pin");
  }
}

void main() {

  test_eeprom_init();
  test_zlcb_init();
  test_ll_init();
  test_pwm_init();
  test_qdec_init();
  test_can_init();

  LOG_INF("Starting test loop");
  for (;;) {
    k_msleep(TEST_LOOP_RATE_MS);
    test_eeprom();
    test_zlcb();
    test_ll();
    test_can();
    test_pwm(500);
    test_qdec();
  }
}
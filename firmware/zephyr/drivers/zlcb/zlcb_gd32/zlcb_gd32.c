/*
 * Copyright (c) 2016 Linaro Limited.
 * Copyright (c) 2020 Teslabs Engineering S.L.
 * Copyright (c) 2021 Carbon Robotics.
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#define DT_DRV_COMPAT gd_gd32_zlcb

#include <errno.h>

#include <device.h>
#include <drivers/zlcb.h>
#include <gd32f4xx_rcu.h>
#include <gd32f4xx_timer.h>
#include <init.h>
#include <kernel.h>
#include <soc.h>
#include <zephyr/sys/util_macro.h>

#include <logging/log.h>
LOG_MODULE_REGISTER(zlcb_gd32, CONFIG_ZLCB_LOG_LEVEL);

/* L0 series MCUs only have 16-bit timers and don't have below macro defined */
#ifndef IS_TIM_32B_COUNTER_INSTANCE
#define IS_TIM_32B_COUNTER_INSTANCE(INSTANCE) (0)
#endif

#define RCU_CLOCK_OFFSET(rcu_clock) ((rcu_clock) >> 6U)

/** Zero latency callback data. */
struct zlcb_gd32_data {
  /** Timer clock (Hz). */
  uint32_t tim_clk;
  /** Callback to call. */
  zlcb_callback_t callback;
  /** Callback argument. */
  void *arg;
};

/** Zero latency callback configuration. */
struct zlcb_gd32_config {
  /** Timer instance. */
  uint32_t timer;
  /** Timer Capture/Compare IRQ. */
  unsigned int irq;
  /** Interrupt priority to use. */
  unsigned int interrupt_priority;
  /** Prescaler. */
  uint32_t prescaler;
  /** Clock configuration. */
  uint32_t rcu_periph_clock;
  uint32_t rcu_periph_reset;
};

static int get_tim_clk(const struct device *dev) {
  const struct zlcb_gd32_config *config = dev->config;
  uint32_t apb_psc, apb_clk;

  /* obtain APB prescaler value */
  if (RCU_CLOCK_OFFSET(config->rcu_periph_clock) == APB1EN_REG_OFFSET) {
    apb_psc = RCU_CFG0 & RCU_CFG0_APB1PSC;
  } else {
    apb_psc = RCU_CFG0 & RCU_CFG0_APB2PSC;
  }

  switch (apb_psc) {
  case RCU_APB1_CKAHB_DIV2:
    apb_psc = 2U;
    break;
  case RCU_APB1_CKAHB_DIV4:
    apb_psc = 4U;
    break;
  case RCU_APB1_CKAHB_DIV8:
    apb_psc = 8U;
    break;
  case RCU_APB1_CKAHB_DIV16:
    apb_psc = 16U;
    break;
  default:
    apb_psc = 1U;
    break;
  }

  apb_clk = CONFIG_SYS_CLOCK_HW_CYCLES_PER_SEC / apb_psc;

#ifdef RCU_CFG1_TIMERSEL
  /*
   * The TIMERSEL bit in RCU_CFG1 controls the clock frequency of all the
   * timers connected to the APB1 and APB2 domains.
   *
   * Up to a certain threshold value of APB{1,2} prescaler, timer clock
   * equals to CK_AHB. This threshold value depends on TIMERSEL setting
   * (2 if TIMERSEL=0, 4 if TIMERSEL=1). Above threshold, timer clock is
   * set to a multiple of the APB domain clock CK_APB{1,2} (2 if
   * TIMERSEL=0, 4 if TIMERSEL=1).
   */

  /* TIMERSEL = 0 */
  if ((RCU_CFG1 & RCU_CFG1_TIMERSEL) == 0U) {
    if (apb_psc <= 2U) {
      return CONFIG_SYS_CLOCK_HW_CYCLES_PER_SEC;
    }

    return apb_clk * 2U;
  }

  /* TIMERSEL = 1 */
  if (apb_psc <= 4U) {
    return CONFIG_SYS_CLOCK_HW_CYCLES_PER_SEC;
  }

  return apb_clk * 4U;
#else
  /*
   * If the APB prescaler equals 1, the timer clock frequencies are set to
   * the same frequency as that of the APB domain. Otherwise, they are set
   * to twice the frequency of the APB domain.
   */
  if (apb_psc == 1U) {
    return apb_clk;
  }

  return apb_clk * 2U;
#endif /* RCU_CFG1_TIMERSEL */
  return 0;
}

static void zlcb_gd32_callback(const struct device *dev) {
  struct zlcb_gd32_data *data = dev->data;
  const struct zlcb_gd32_config *cfg = dev->config;

  timer_flag_clear(cfg->timer, TIMER_INT_FLAG_CH1);
  data->callback(data->arg);
}

static int zlcb_gd32_set_callback(const struct device *dev, uint32_t period_cycles, zlcb_callback_t callback,
                                  void *arg) {
  struct zlcb_gd32_data *data = dev->data;
  const struct zlcb_gd32_config *cfg = dev->config;

  /*
   * Non 32-bit timers count from 0 up to the value in the ARR register
   * (16-bit). Thus period_cycles cannot be greater than UINT16_MAX + 1.
   */
  if (!IS_TIM_32B_COUNTER_INSTANCE(cfg->timer) && (period_cycles > UINT16_MAX + 1)) {
    return -ENOTSUP;
  }

  /* period with one cycle is not supported */
  if (period_cycles == 1) {
    LOG_ERR("Period with a single cycle is not supported, decrease prescaler");
    return -ENOTSUP;
  }

  /* disable interrupt before making changes */
  irq_disable(cfg->irq);

  if (period_cycles == 0 || callback == NULL) {
    timer_disable(cfg->timer);
    data->callback = NULL;
    data->arg = NULL;
    return 0;
  }

  timer_oc_parameter_struct oc_init;
  timer_channel_output_struct_para_init(&oc_init);

  oc_init.outputstate = TIMER_CCX_ENABLE;

  timer_channel_output_config(cfg->timer, TIMER_CH_1, &oc_init);

  timer_enable(cfg->timer);
  timer_autoreload_value_config(cfg->timer, period_cycles - 1u);

  /* update callback & argument */
  data->callback = callback;
  data->arg = arg;

  /* reenable interrupt */
  irq_enable(cfg->irq);

  return 0;
}

static int zlcb_gd32_get_cycles_per_sec(const struct device *dev, uint64_t *cycles) {
  struct zlcb_gd32_data *data = dev->data;
  const struct zlcb_gd32_config *cfg = dev->config;

  *cycles = (uint64_t)(data->tim_clk / (cfg->prescaler + 1));

  return 0;
}

static const struct zlcb_driver_api zlcb_gd32_driver_api = {
    .set_callback = zlcb_gd32_set_callback,
    .get_cycles_per_sec = zlcb_gd32_get_cycles_per_sec,
};

static int zlcb_gd32_init(const struct device *dev) {
  struct zlcb_gd32_data *data = dev->data;
  const struct zlcb_gd32_config *cfg = dev->config;

  int r;
  const struct device *clk;

  rcu_periph_clock_enable(cfg->rcu_periph_clock);

  r = get_tim_clk(dev);
  if (r <= 0) {
    LOG_ERR("Could not obtain timer clock (%d)", r);
    return r;
  }
  data->tim_clk = r;

  /* initialize timer */
  timer_parameter_struct init;
  timer_struct_para_init(&init);

  init.prescaler = cfg->prescaler;
  init.counterdirection = TIMER_COUNTER_UP;
  init.clockdivision = TIMER_CKDIV_DIV1;

  gd32_timer_init(cfg->timer, &init);

  /* prepare interrupt */
  irq_connect_dynamic(cfg->irq, cfg->interrupt_priority, (zlcb_callback_t)zlcb_gd32_callback, dev, IRQ_ZERO_LATENCY);
  timer_interrupt_enable(cfg->timer, TIMER_INT_CH1);

  timer_enable(cfg->timer);

  return 0;
}

#define ZLCB_DEVICE_INIT(index)                                                                                        \
  static struct zlcb_gd32_data zlcb_gd32_data_##index;                                                                 \
                                                                                                                       \
  static const struct zlcb_gd32_config zlcb_gd32_config_##index = {                                                    \
      .timer = (uint32_t)DT_REG_ADDR(DT_PARENT(DT_DRV_INST(index))),                                                   \
      .rcu_periph_clock = DT_PROP(DT_INST_PARENT(index), rcu_periph_clock),                                            \
      .rcu_periph_reset = DT_PROP(DT_INST_PARENT(index), rcu_periph_reset),                                            \
      .irq = DT_IRQ_BY_NAME(DT_PARENT(DT_DRV_INST(index)), cc, irq),                                                   \
      .prescaler = DT_PROP(DT_INST_PARENT(index), prescaler)};                                                         \
                                                                                                                       \
  DEVICE_DT_INST_DEFINE(index, &zlcb_gd32_init, NULL, &zlcb_gd32_data_##index, &zlcb_gd32_config_##index, POST_KERNEL, \
                        CONFIG_KERNEL_INIT_PRIORITY_DEVICE, &zlcb_gd32_driver_api);

DT_INST_FOREACH_STATUS_OKAY(ZLCB_DEVICE_INIT)

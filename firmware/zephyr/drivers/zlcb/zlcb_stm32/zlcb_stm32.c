/*
 * Copyright (c) 2016 Linaro Limited.
 * Copyright (c) 2020 Teslabs Engineering S.L.
 * Copyright (c) 2021 Carbon Robotics.
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#define DT_DRV_COMPAT st_stm32_zlcb

#include <errno.h>

#include <device.h>
#include <drivers/zlcb.h>
#include <init.h>
#include <kernel.h>
#include <soc.h>
#include <stm32_ll_rcc.h>
#include <stm32_ll_tim.h>

#include <drivers/clock_control/stm32_clock_control.h>

#include <logging/log.h>
LOG_MODULE_REGISTER(zlcb_stm32, CONFIG_ZLCB_LOG_LEVEL);

/* L0 series MCUs only have 16-bit timers and don't have below macro defined */
#ifndef IS_TIM_32B_COUNTER_INSTANCE
#define IS_TIM_32B_COUNTER_INSTANCE(INSTANCE) (0)
#endif

/** Zero latency callback data. */
struct zlcb_stm32_data {
  /** Timer clock (Hz). */
  uint32_t tim_clk;
  /** Callback to call. */
  zlcb_callback_t callback;
  /** Callback argument. */
  void *arg;
};

/** Zero latency callback configuration. */
struct zlcb_stm32_config {
  /** Timer instance. */
  TIM_TypeDef *timer;
  /** Timer Capture/Compare IRQ. */
  unsigned int irq;
  /** Interrupt priority to use. */
  unsigned int interrupt_priority;
  /** Prescaler. */
  uint32_t prescaler;
  /** Clock configuration. */
  struct stm32_pclken pclken;
};

/**
 * Obtain timer clock speed.
 *
 * @param pclken  Timer clock control subsystem.
 * @param tim_clk Where computed timer clock will be stored.
 *
 * @return 0 on success, error code otherwise.
 */
static int get_tim_clk(const struct stm32_pclken *pclken, uint32_t *tim_clk) {
  int r;
  const struct device *clk;
  uint32_t bus_clk, apb_psc;

  clk = DEVICE_DT_GET(STM32_CLOCK_CONTROL_NODE);

  r = clock_control_get_rate(clk, (clock_control_subsys_t *)pclken, &bus_clk);
  if (r < 0) {
    return r;
  }

#if defined(CONFIG_SOC_SERIES_STM32H7X)
  if (pclken->bus == STM32_CLOCK_BUS_APB1) {
    apb_psc = STM32_D2PPRE1;
  } else {
    apb_psc = STM32_D2PPRE2;
  }
#else
  if (pclken->bus == STM32_CLOCK_BUS_APB1) {
    apb_psc = STM32_APB1_PRESCALER;
  }
#if !defined(CONFIG_SOC_SERIES_STM32F0X) && !defined(CONFIG_SOC_SERIES_STM32G0X)
  else {
    apb_psc = STM32_APB2_PRESCALER;
  }
#endif
#endif

#if defined(RCC_DCKCFGR_TIMPRE) || defined(RCC_DCKCFGR1_TIMPRE) || defined(RCC_CFGR_TIMPRE)
  /*
   * There are certain series (some F4, F7 and H7) that have the TIMPRE
   * bit to control the clock frequency of all the timers connected to
   * APB1 and APB2 domains.
   *
   * Up to a certain threshold value of APB{1,2} prescaler, timer clock
   * equals to HCLK. This threshold value depends on TIMPRE setting
   * (2 if TIMPRE=0, 4 if TIMPRE=1). Above threshold, timer clock is set
   * to a multiple of the APB domain clock PCLK{1,2} (2 if TIMPRE=0, 4 if
   * TIMPRE=1).
   */

  if (LL_RCC_GetTIMPrescaler() == LL_RCC_TIM_PRESCALER_TWICE) {
    /* TIMPRE = 0 */
    if (apb_psc <= 2u) {
      LL_RCC_ClocksTypeDef clocks;

      LL_RCC_GetSystemClocksFreq(&clocks);
      *tim_clk = clocks.HCLK_Frequency;
    } else {
      *tim_clk = bus_clk * 2u;
    }
  } else {
    /* TIMPRE = 1 */
    if (apb_psc <= 4u) {
      LL_RCC_ClocksTypeDef clocks;

      LL_RCC_GetSystemClocksFreq(&clocks);
      *tim_clk = clocks.HCLK_Frequency;
    } else {
      *tim_clk = bus_clk * 4u;
    }
  }
#else
  /*
   * If the APB prescaler equals 1, the timer clock frequencies
   * are set to the same frequency as that of the APB domain.
   * Otherwise, they are set to twice (×2) the frequency of the
   * APB domain.
   */
  if (apb_psc == 1u) {
    *tim_clk = bus_clk;
  } else {
    *tim_clk = bus_clk * 2u;
  }
#endif

  return 0;
}

static void zlcb_stm32_callback(const struct device *dev) {
  struct zlcb_stm32_data *data = dev->data;
  const struct zlcb_stm32_config *cfg = dev->config;

  LL_TIM_ClearFlag_CC1(cfg->timer);
  data->callback(data->arg);
}

static int zlcb_stm32_set_callback(const struct device *dev, uint32_t period_cycles, zlcb_callback_t callback,
                                   void *arg) {
  struct zlcb_stm32_data *data = dev->data;
  const struct zlcb_stm32_config *cfg = dev->config;

  /*
   * Non 32-bit timers count from 0 up to the value in the ARR register
   * (16-bit). Thus period_cycles cannot be greater than UINT16_MAX + 1.
   */
  if (!IS_TIM_32B_COUNTER_INSTANCE(cfg->timer) && (period_cycles > UINT16_MAX + 1)) {
    return -ENOTSUP;
  }

  /* period with one cycle is not supported */
  if (period_cycles == 1) {
    LOG_ERR("Period with a single cycle is not supported, decrease prescaler");
    return -ENOTSUP;
  }

  /* disable interrupt before making changes */
  irq_disable(cfg->irq);

  if (period_cycles == 0 || callback == NULL) {
    LL_TIM_CC_DisableChannel(cfg->timer, LL_TIM_CHANNEL_CH1);
    data->callback = NULL;
    data->arg = NULL;
    return 0;
  }

  LL_TIM_EnableARRPreload(cfg->timer);
  if (!LL_TIM_CC_IsEnabledChannel(cfg->timer, LL_TIM_CHANNEL_CH1)) {
    LL_TIM_OC_InitTypeDef oc_init;
    LL_TIM_OC_StructInit(&oc_init);

    oc_init.OCMode = LL_TIM_OCMODE_ACTIVE;
    oc_init.OCState = LL_TIM_OCSTATE_ENABLE;

    if (LL_TIM_OC_Init(cfg->timer, LL_TIM_CHANNEL_CH1, &oc_init) != SUCCESS) {
      LOG_ERR("Could not initialize timer channel output");
      return -EIO;
    }

    LL_TIM_OC_EnablePreload(cfg->timer, LL_TIM_CHANNEL_CH1);
  }
  LL_TIM_SetAutoReload(cfg->timer, period_cycles - 1u);
  LL_TIM_GenerateEvent_UPDATE(cfg->timer);

  /* update callback & argument */
  data->callback = callback;
  data->arg = arg;

  /* reenable interrupt */
  irq_enable(cfg->irq);

  return 0;
}

static int zlcb_stm32_get_cycles_per_sec(const struct device *dev, uint64_t *cycles) {
  struct zlcb_stm32_data *data = dev->data;
  const struct zlcb_stm32_config *cfg = dev->config;

  *cycles = (uint64_t)(data->tim_clk / (cfg->prescaler + 1));

  return 0;
}

static const struct zlcb_driver_api zlcb_stm32_driver_api = {
    .set_callback = zlcb_stm32_set_callback,
    .get_cycles_per_sec = zlcb_stm32_get_cycles_per_sec,
};

static int zlcb_stm32_init(const struct device *dev) {
  struct zlcb_stm32_data *data = dev->data;
  const struct zlcb_stm32_config *cfg = dev->config;

  int r;
  const struct device *clk;
  LL_TIM_InitTypeDef init;

  /* enable clock and store its speed */
  clk = DEVICE_DT_GET(STM32_CLOCK_CONTROL_NODE);

  r = clock_control_on(clk, (clock_control_subsys_t *)&cfg->pclken);
  if (r < 0) {
    LOG_ERR("Could not initialize clock (%d)", r);
    return r;
  }

  r = get_tim_clk(&cfg->pclken, &data->tim_clk);
  if (r < 0) {
    LOG_ERR("Could not obtain timer clock (%d)", r);
    return r;
  }

  /* initialize timer */
  LL_TIM_StructInit(&init);

  init.Prescaler = cfg->prescaler;
  init.CounterMode = LL_TIM_COUNTERMODE_UP;
  init.Autoreload = 0u;
  init.ClockDivision = LL_TIM_CLOCKDIVISION_DIV1;

  if (LL_TIM_Init(cfg->timer, &init) != SUCCESS) {
    LOG_ERR("Could not initialize timer");
    return -EIO;
  }

  /* prepare interrupt */
  irq_connect_dynamic(cfg->irq, cfg->interrupt_priority, (zlcb_callback_t)zlcb_stm32_callback, dev, IRQ_ZERO_LATENCY);
  LL_TIM_EnableIT_CC1(cfg->timer);

#if !defined(CONFIG_SOC_SERIES_STM32L0X) && !defined(CONFIG_SOC_SERIES_STM32L1X)
  /* enable outputs and counter */
  if (IS_TIM_BREAK_INSTANCE(cfg->timer)) {
    LL_TIM_EnableAllOutputs(cfg->timer);
  }
#endif

  LL_TIM_EnableCounter(cfg->timer);

  return 0;
}

#define DT_INST_CLK(index, inst)                                                                                       \
  {                                                                                                                    \
    .bus = DT_CLOCKS_CELL(DT_PARENT(DT_DRV_INST(index)), bus),                                                         \
    .enr = DT_CLOCKS_CELL(DT_PARENT(DT_DRV_INST(index)), bits)                                                         \
  }

#define ZLCB_DEVICE_INIT(index)                                                                                        \
  static struct zlcb_stm32_data zlcb_stm32_data_##index;                                                               \
                                                                                                                       \
  static const struct zlcb_stm32_config zlcb_stm32_config_##index = {                                                  \
      .timer = (TIM_TypeDef *)DT_REG_ADDR(DT_PARENT(DT_DRV_INST(index))),                                              \
      .irq = DT_IRQ_BY_NAME(DT_PARENT(DT_DRV_INST(index)), cc, irq),                                                   \
      .interrupt_priority = DT_IRQ_BY_NAME(DT_PARENT(DT_DRV_INST(index)), cc, priority),                               \
      .prescaler = DT_PROP(DT_INST_PARENT(index), st_prescaler),                                                       \
      .pclken = DT_INST_CLK(index, timer)};                                                                            \
                                                                                                                       \
  DEVICE_DT_INST_DEFINE(index, &zlcb_stm32_init, NULL, &zlcb_stm32_data_##index, &zlcb_stm32_config_##index,           \
                        POST_KERNEL, CONFIG_KERNEL_INIT_PRIORITY_DEVICE, &zlcb_stm32_driver_api);

DT_INST_FOREACH_STATUS_OKAY(ZLCB_DEVICE_INIT)

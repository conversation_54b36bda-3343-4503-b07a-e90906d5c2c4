#define DT_DRV_COMPAT cr_gps_zed_f9p

#include <stdbool.h>

#include <zephyr/drivers/gpio.h>
#include <zephyr/drivers/spi.h>
#include <zephyr/irq.h>
#include <zephyr/sys/byteorder.h>
#include <zephyr/sys/timeutil.h>
#include <zephyr/zephyr.h>

#include <drivers/gps.h>
#include <utils/handle_errors.h>

#include <logging/log.h>
LOG_MODULE_REGISTER(gps_zed_f9p, CONFIG_GPS_LOG_LEVEL);

#include "ubx_proto.h"

#define PACKET_BUFFER_SIZE 256

#define UBX_TIMEOUT_MS 3000

#define U2(x) (*(uint16_t *)(&x))
#define I4(x) (*(int32_t *)(&x))
#define U4(x) (*(uint32_t *)(&x))

struct gps_zed_f9p_data {
  struct k_mutex mutex;

  // PPS interrupt handler
  struct gpio_callback pps_gpio_cb;

  // user specified PPS callback
  void (*user_pps_callback)(void *ctx);
  // context ptr argument for PPS callback
  void *user_pps_callback_ctx;

  // packet buffer
  uint8_t buffer[PACKET_BUFFER_SIZE];
};

struct gps_zed_f9p_config {
  const struct device *spi;
  const struct spi_config spi_cfg;

  // PPS input (from module)
  const struct gpio_dt_spec gpio_pps;
  // Active low reset (towards module)
  const struct gpio_dt_spec gpio_reset;
  // Interrupt request (from module)
  const struct gpio_dt_spec gpio_irq;
};

static int spi_write_string(struct gps_zed_f9p_config *cfg, const unsigned char *chr, const size_t len);
static int spi_read_string(struct gps_zed_f9p_config *cfg, unsigned char *chr, const size_t len);

/**
 * @brief GPIO interrupt callback for PPS
 */
static void pps_in_cb(const struct device *, struct gpio_callback *cb, uint32_t) {
  // get the GPS device instance
  struct gps_zed_f9p_data *data = CONTAINER_OF(cb, struct gps_zed_f9p_data, pps_gpio_cb);

  // invoke the callback, if installed
  // does not use locks (not allowed in ISR) since writes are done with IRQs masked
  if (data->user_pps_callback) {
    data->user_pps_callback(data->user_pps_callback_ctx);
  }
}

/**
 * @brief Send an UBX frame
 *
 * Append the checksum to the specified UBX frame and transmit via SPI.
 *
 * @remark The length field of the frame must have been filled in.
 */
static int ubx_send(const struct device *dev, ubx_hdr_t *packet) {
  struct gps_zed_f9p_config *cfg = (struct gps_zed_f9p_config *)dev->config;

  // calculate the checksum over header and payload
  const size_t bytesWritten = sys_le16_to_cpu(packet->payload_length) + sizeof(*packet);
  ubx_append_checksum(packet);

  // then send the entire frame
  const size_t txLength = bytesWritten + UBX_FTR_LEN;
  LOG_HEXDUMP_DBG(packet, txLength, "UBX TX");
  return spi_write_string(cfg, (unsigned char *)packet, txLength);
}

/**
 * @brief Receive an UBX frame of the given length
 *
 * Read UBX data into the device's message buffer. Once the full length of the message has been
 * read out, validate the header (e.g. the magic bytes) and then validate the checksum.
 */
static int ubx_receive(const struct device *dev, const size_t payloadLength, ubx_hdr_t **outResponse) {
  struct gps_zed_f9p_data *data = (struct gps_zed_f9p_data *)dev->data;
  struct gps_zed_f9p_config *cfg = (struct gps_zed_f9p_config *)dev->config;

  if (!outResponse) {
    return -EFAULT;
  }

  const size_t rxBytes = UBX_HDR_LEN + payloadLength + UBX_FTR_LEN;
  if (rxBytes > PACKET_BUFFER_SIZE) {
    return -EMSGSIZE;
  }

  // receive the desired packet
  HANDLE_UNLIKELY(spi_read_string(cfg, data->buffer, rxBytes));
  LOG_HEXDUMP_DBG(data->buffer, rxBytes, "UBX RX");

  // validate the packet
  ubx_hdr_t *packet = (ubx_hdr_t *)data->buffer;
  HANDLE_UNLIKELY(ubx_validate_packet(packet, rxBytes));

  // packet is good!
  *outResponse = packet;
  return 0;
}

/**
 * @brief Wait for an acknowledgement of a previous message
 *
 * Read from the module  a packet, which we expect to be UBX-ACK-ACK or UBX-ACK-NACK type.
 */
static int ubx_await_ack(const struct device *dev, bool *outIsAck) {
  ubx_hdr_t *replyPacket = NULL;

  HANDLE_UNLIKELY(ubx_receive(dev, 2, &replyPacket));

  // is it ack/nack (determined by message id)
  *outIsAck = (replyPacket->msg_id == 0x01);

  return 0;
}

static int spi_write_string(struct gps_zed_f9p_config *cfg, const unsigned char *chr, const size_t len) {
  const struct spi_buf buf = {.buf = (unsigned char *)chr, .len = len};
  const struct spi_buf_set bufset = {.buffers = &buf, .count = 1};
  return spi_write(cfg->spi, &cfg->spi_cfg, &bufset);
}

static int spi_read_string(struct gps_zed_f9p_config *cfg, unsigned char *chr, const size_t len) {
  int64_t start_time_ms = k_uptime_get();

  /* read leading 0xffs */
  const struct spi_buf buf_first = {.buf = chr, .len = 1};
  const struct spi_buf_set bufset_first = {.buffers = &buf_first, .count = 1};
  do {
    k_msleep(1);
    if (k_uptime_get() - start_time_ms > UBX_TIMEOUT_MS) {
      return -EIO;
    }
    if (spi_read(cfg->spi, &cfg->spi_cfg, &bufset_first) != 0) {
      return -EIO;
    }
  } while (*chr == 0xff);

  /* read the rest */
  const struct spi_buf buf_rest = {.buf = chr + 1, .len = len - 1};
  const struct spi_buf_set bufset_rest = {.buffers = &buf_rest, .count = 1};
  if (spi_read(cfg->spi, &cfg->spi_cfg, &bufset_rest) != 0) {
    return -EIO;
  }

  return 0;
}

/**
 * @brief Request current location from module
 *
 * Poll the `UBX-NAV-PVT` message to retrieve a position/velocity fix
 */
static int gps_zed_f9p_query_pvt(const struct device *dev, struct gps_query_result *r) {
  static const uint8_t kMessageId = 0x07;
  int err = 0;
  struct gps_zed_f9p_data *data = (struct gps_zed_f9p_data *)dev->data;

  ubx_hdr_t *replyPacket = NULL;

  k_mutex_lock(&data->mutex, K_FOREVER);
  {
    // send query
    ubx_hdr_t *poll = ubx_init_header(data->buffer, kUbxClassNav, kMessageId, 0);

    err = ubx_send(dev, poll);
    if (err) {
      LOG_WRN("%s: ubx_send failed (%d)", __FUNCTION__, err);
      goto beach;
    }

    // receive response
    err = ubx_receive(dev, 92, &replyPacket);
    if (err) {
      LOG_WRN("%s: ubx_receive failed (%d)", __FUNCTION__, err);
      goto beach;
    }

    if (replyPacket->msg_class != kUbxClassNav || replyPacket->msg_id != kMessageId) {
      LOG_WRN("%s: invalid message (%02x, %02x)", __FUNCTION__, replyPacket->msg_class, replyPacket->msg_id);
      err = -EINVAL;
      goto beach;
    }
  }
beach:;
  k_mutex_unlock(&data->mutex);
  if (err) {
    return err;
  }

  // decode the response
  uint8_t *resp = replyPacket->payload;

  const struct tm tm = {
      .tm_year = U2(resp[4]) - 1900,
      .tm_mon = resp[6] - 1,
      .tm_mday = resp[7],
      .tm_hour = resp[8],
      .tm_min = resp[9],
      .tm_sec = resp[10],
  };
  int32_t tm_ns = I4(resp[16]);
  r->timestamp_ms = timeutil_timegm64(&tm) * 1000 + (tm_ns / 1000000);
  /* validDate, validTime, fullyResolved */
  r->time_valid = (resp[11] & 0b111) == 0b111;

  r->fix_type = resp[20];
  r->fix_flags = resp[21];
  bool gnss_fix_ok = r->fix_flags & 0b1;
  bool rtcm_applied = r->fix_flags & 0b10;
  bool fixed_fix = r->fix_flags & 0b10000000;
  r->have_fix = gnss_fix_ok && rtcm_applied && fixed_fix;
  r->have_approx_fix = gnss_fix_ok;

  r->latitude = (double)I4(resp[28]) * 1e-7;
  r->longitude = (double)I4(resp[24]) * 1e-7;
  r->height_mm = I4(resp[32]);

  r->num_sats = resp[23];
  r->hdop = (double)U2(resp[76]) * 0.01;

  return 0;
}

static int gps_zed_f9p_spartn(const struct device *dev, const uint8_t *spartn_data, const size_t size) {
  struct gps_zed_f9p_data *data = (struct gps_zed_f9p_data *)dev->data;
  struct gps_zed_f9p_config *cfg = (struct gps_zed_f9p_config *)dev->config;
  k_mutex_lock(&data->mutex, K_FOREVER);
  HANDLE_UNLIKELY(spi_write_string(cfg, spartn_data, size));
  k_mutex_unlock(&data->mutex);
  return 0;
}

/**
 * @brief Write RTCM message to module
 *
 * These are sent directly, no framing is applied.
 */
static int gps_zed_f9p_rtcm(const struct device *dev, const uint8_t *rtcm_data, const size_t size) {
  struct gps_zed_f9p_data *data = (struct gps_zed_f9p_data *)dev->data;
  struct gps_zed_f9p_config *cfg = (struct gps_zed_f9p_config *)dev->config;

  k_mutex_lock(&data->mutex, K_FOREVER);
  { HANDLE_UNLIKELY(spi_write_string(cfg, rtcm_data, size)); }
  k_mutex_unlock(&data->mutex);
  return 0;
}

/**
 * @brief Set up a callback invoked when PPS line is asserted
 *
 * Internally this wraps a GPIO interrupt handler; the provided function pointer is invoked via
 * that.
 *
 * @param callback Function to invoke on PPS, or NULL to remove handler
 */
static int gps_zed_f9p_set_pps_callback(const struct device *dev, gps_pps_callback_t callback, void *callbackCtx) {
  int err = 0;
  struct gps_zed_f9p_data *data = (struct gps_zed_f9p_data *)dev->data;

  k_mutex_lock(&data->mutex, K_FOREVER);
  {
    unsigned int key = irq_lock();

    data->user_pps_callback = callback;
    data->user_pps_callback_ctx = callbackCtx;

    irq_unlock(key);
  }
  k_mutex_unlock(&data->mutex);

  return err;
}

/**
 * @brief Write configuration to module
 *
 * Generates an UBX-CFG-VALSET message with the specified parameters being written. Config is
 * applied to RAM only.
 */
static int gps_zed_f9p_set_config(const struct device *dev, const gps_config_item_t *items, const size_t numItems) {
  static const uint8_t kMessageId = 0x8A;
  int err = 0;
  struct gps_zed_f9p_data *data = (struct gps_zed_f9p_data *)dev->data;

  // validate input
  if (!items) {
    return -EFAULT;
  } else if (!numItems || numItems > 64) {
    return -EINVAL;
  }

  // figure out payload length
  size_t payloadLength = 4;

  for (size_t i = 0; i < numItems; i++) {
    // always use 4 bytes for the key
    payloadLength += 4;

    // and the length used for data
    switch (items[i].data_length) {
    case kGpsConfigValueLength1:
      payloadLength += 1;
      break;
    case kGpsConfigValueLength2:
      payloadLength += 2;
      break;
    case kGpsConfigValueLength4:
      payloadLength += 4;
      break;
    case kGpsConfigValueLength8:
      payloadLength += 8;
      break;
    }
  }

  if (payloadLength > (PACKET_BUFFER_SIZE - UBX_HDR_LEN - UBX_FTR_LEN)) {
    return -ENOMEM;
  }

  k_mutex_lock(&data->mutex, K_FOREVER);
  {
    // assemble the command
    ubx_hdr_t *hdr = ubx_init_header(data->buffer, kUbxClassCfg, kMessageId, payloadLength);
    uint8_t *writePtr = hdr->payload;

    // VALSET header
    *writePtr++ = 0x00;  // version
    *writePtr++ = 0b001; // active layers: RAM only
    *writePtr++ = 0;     // reserved 1/2
    *writePtr++ = 0;     // reserved 2/2

    for (size_t i = 0; i < numItems; i++) {
      // element ID, in big endian order
      sys_put_le32(items[i].key, writePtr);
      writePtr += 4;

      // write data (which may need to be byteswapped)
      switch (items[i].data_length) {
      case kGpsConfigValueLength1:
        *writePtr++ = items[i].data.byte;
        break;

      case kGpsConfigValueLength2:
        sys_put_le16(items[i].data.word, writePtr);
        writePtr += 2;
        break;
      case kGpsConfigValueLength4:
        sys_put_le32(items[i].data.dword, writePtr);
        writePtr += 4;
        break;
      case kGpsConfigValueLength8:
        sys_put_le64(items[i].data.qword, writePtr);
        writePtr += 8;
        break;
      }
    }

    // send the packet
    err = ubx_send(dev, hdr);
    if (err) {
      LOG_WRN("%s: ubx_send failed (%d)", __FUNCTION__, err);
      goto beach;
    }

    // await response: should be UBX-ACK-ACK or UBX-ACK-NACK
    bool isAck = false;
    err = ubx_await_ack(dev, &isAck);
    if (err) {
      LOG_WRN("%s: ubx_await_ack failed (%d)", __FUNCTION__, err);
      goto beach;
    }

    err = isAck ? 0 : -ENXIO;
  }
beach:;
  k_mutex_unlock(&data->mutex);

  return err;
}

/**
 * @brief Get relative position/heading information
 *
 * Send an `UBX-NAV-RELPOSNED` poll frame and decode the response.
 */
static int gps_zed_f9p_query_relpos(const struct device *dev, gps_relposned_result_t *outResult) {
  static const uint8_t kMessageId = 0x3C;
  int err = 0;
  ubx_hdr_t *replyPacket = NULL;
  struct gps_zed_f9p_data *data = (struct gps_zed_f9p_data *)dev->data;

  if (!outResult) {
    return -EFAULT;
  }

  k_mutex_lock(&data->mutex, K_FOREVER);
  {
    // send query
    ubx_hdr_t *poll = ubx_init_header(data->buffer, kUbxClassNav, kMessageId, 0);

    err = ubx_send(dev, poll);
    if (err) {
      LOG_WRN("%s: ubx_send failed (%d)", __FUNCTION__, err);
      goto beach;
    }

    // receive response
    err = ubx_receive(dev, 64, &replyPacket);
    if (err) {
      LOG_WRN("%s: ubx_receive failed (%d)", __FUNCTION__, err);
      goto beach;
    }

    if (replyPacket->msg_class != kUbxClassNav || replyPacket->msg_id != kMessageId) {
      LOG_WRN("%s: invalid message (%02x, %02x)", __FUNCTION__, replyPacket->msg_class, replyPacket->msg_id);
      err = -EINVAL;
      goto beach;
    }
  }
beach:;
  k_mutex_unlock(&data->mutex);

  // decode the message
  outResult->ref_station_id = sys_get_le16(replyPacket->payload + 2);
  outResult->gps_itow = sys_get_le32(replyPacket->payload + 4);

  outResult->rel_pos_north = (((double)((int32_t)sys_get_le32(replyPacket->payload + 8))) * 10) +
                             (((double)((int8_t)replyPacket->payload[32])) * 0.1);
  outResult->rel_pos_north_accuracy = (((double)((int32_t)sys_get_le32(replyPacket->payload + 36))) * 0.1);

  outResult->rel_pos_east = (((double)((int32_t)sys_get_le32(replyPacket->payload + 12))) * 10) +
                            (((double)((int8_t)replyPacket->payload[33])) * 0.1);
  outResult->rel_pos_east_accuracy = (((double)((int32_t)sys_get_le32(replyPacket->payload + 40))) * 0.1);

  outResult->rel_pos_down = (((double)((int32_t)sys_get_le32(replyPacket->payload + 16))) * 10) +
                            (((double)((int8_t)replyPacket->payload[34])) * 0.1);
  outResult->rel_pos_down_accuracy = (((double)((int32_t)sys_get_le32(replyPacket->payload + 44))) * 0.1);

  outResult->rel_pos_length = (((double)((int32_t)sys_get_le32(replyPacket->payload + 20))) * 10) +
                              (((double)((int8_t)replyPacket->payload[35])) * 0.1);
  outResult->rel_pos_length_accuracy = (((double)((int32_t)sys_get_le32(replyPacket->payload + 48))) * 0.1);

  outResult->rel_pos_heading = (((double)((int32_t)sys_get_le32(replyPacket->payload + 24))) * 1e-5);
  outResult->rel_pos_heading_accuracy = (((double)((int32_t)sys_get_le32(replyPacket->payload + 52))) * 1e-5);

  const uint32_t flags = sys_get_le32(replyPacket->payload + 60);
  outResult->gnss_valid = !!(flags & (1 << 0));
  outResult->diff_corrections_applied = !!(flags & (1 << 1));
  outResult->rel_pos_valid = !!(flags & (1 << 2));
  outResult->rel_pos_heading_valid = !!(flags & (1 << 8));
  outResult->is_moving_base = !!(flags & (1 << 5));
  outResult->has_carrier_phase_soln = !!((flags & 0b11000) >> 3);
  outResult->carrier_phase_soln_fixed = ((flags & 0b11000) >> 3) == 2;

  return err;
}

/**
 * @brief Poll the NMEA GGA message
 *
 * This is the "Global Positioning System Fix Data" message.
 */
static int gps_zed_f9p_query_nmea_gga(const struct device *dev, char *outBuffer, const size_t outBufferSize) {
  static const char *kGgaQuery = "$EIGNQ,GGA*39\r\n";

  struct gps_zed_f9p_config *config = (struct gps_zed_f9p_config *)dev->config;
  struct gps_zed_f9p_data *data = (struct gps_zed_f9p_data *)dev->data;
  int err;

  if (!outBuffer) {
    return -EFAULT;
  } else if (outBufferSize <= 1) {
    return -EINVAL;
  }

  if (outBufferSize < 82) {
    LOG_WRN("NMEA sentence length is up to 82 bytes (buffer is %u), response may be truncated!", outBufferSize);
  }

  k_mutex_lock(&data->mutex, K_FOREVER);
  {
    // send query
    err = spi_write_string(config, kGgaQuery, strlen(kGgaQuery));
    if (err) {
      LOG_WRN("%s: spi_write_string failed (%d)", __FUNCTION__, err);
      goto beach;
    }

    // receive response
    err = spi_read_string(config, outBuffer, outBufferSize - 1);
    if (err) {
      LOG_WRN("%s: ubx_receive failed (%d)", __FUNCTION__, err);
      goto beach;
    }

    LOG_HEXDUMP_DBG(outBuffer, outBufferSize, "NMEA rx");

    // null terminate the packet (by finding the trailing `\r\n`)
    size_t len = 0;

    for (size_t i = (outBufferSize - 2); i != 0; i--) {
      if (outBuffer[i] == '\r' && outBuffer[i + 1] == '\n') {
        len = i + 2;
        break;
      }
    }

    LOG_DBG("NMEA sentence length = %u", len);
    if (len && len < outBufferSize) {
      outBuffer[len] = '\0';
    }

    // TODO: packet validation (NMEA checksum)
  }
beach:;
  k_mutex_unlock(&data->mutex);

  return err;
}

/**
 * @brief Initialize the F9P driver
 *
 * Set up all the GPIOs associated with the module and any needed internal data structures.
 */
static int gps_zed_f9p_init(const struct device *dev) {
  struct gps_zed_f9p_config *config = (struct gps_zed_f9p_config *)dev->config;
  struct gps_zed_f9p_data *data = (struct gps_zed_f9p_data *)dev->data;

  HANDLE_UNLIKELY(k_mutex_init(&data->mutex));

  // set up GPIOs
  HANDLE_UNLIKELY_BOOL(device_is_ready(config->gpio_reset.port), ENODEV);
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&config->gpio_reset, GPIO_OUTPUT | GPIO_OUTPUT_INACTIVE));

  HANDLE_UNLIKELY_BOOL(device_is_ready(config->gpio_irq.port), ENODEV);
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&config->gpio_irq, GPIO_OUTPUT | GPIO_OUTPUT_INACTIVE));

  HANDLE_UNLIKELY_BOOL(device_is_ready(config->gpio_pps.port), ENODEV);
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&config->gpio_pps, GPIO_INPUT));

  // assert reset for the module
  if (config->gpio_reset.port) {
    HANDLE_UNLIKELY(gpio_pin_set_dt(&config->gpio_reset, 1));
    k_busy_wait(5000);
    HANDLE_UNLIKELY(gpio_pin_set_dt(&config->gpio_reset, 0));

    // required to wait for firmware on module to boot back up
    // this is not specified by u-blox; tested ~2 sec
    k_sleep(K_SECONDS(2));
  }

  // irq for PPS
  HANDLE_UNLIKELY(gpio_pin_interrupt_configure_dt(&config->gpio_pps, GPIO_INT_EDGE_TO_ACTIVE));
  gpio_init_callback(&data->pps_gpio_cb, pps_in_cb, BIT(config->gpio_pps.pin));
  gpio_add_callback(config->gpio_pps.port, &data->pps_gpio_cb);

  return 0;
}

static const struct gps_driver_api gps_zed_f9p_api = {
    .query_pvt = gps_zed_f9p_query_pvt,
    .spartn = gps_zed_f9p_spartn,
    .rtcm = gps_zed_f9p_rtcm,
    .set_pps_callback = gps_zed_f9p_set_pps_callback,
    .set_config = gps_zed_f9p_set_config,
    .query_relpos = gps_zed_f9p_query_relpos,
    .query_nmea_gga = gps_zed_f9p_query_nmea_gga,
};

#define ZED_F9P_SPI_OP_MODE (SPI_OP_MODE_MASTER | SPI_TRANSFER_MSB | SPI_WORD_SET(8) | SPI_LINES_SINGLE)

#define ZED_F9P_DEFINE(inst)                                                                                           \
  static struct gps_zed_f9p_data gps_zed_f9p_data_##inst;                                                              \
  static const struct gps_zed_f9p_config gps_zed_f9p_config_##inst = {                                                 \
      .spi = DEVICE_DT_GET(DT_INST_BUS(                                                                                \
          inst)), /* 1µS delay around toggling the /CS GPIO because it's in a different clock domain than SPI */      \
      .spi_cfg = SPI_CONFIG_DT_INST(inst, ZED_F9P_SPI_OP_MODE, 1),                                                     \
      .gpio_pps = GPIO_DT_SPEC_INST_GET_OR(inst, pps_gpios, {NULL}),                                                   \
      .gpio_reset = GPIO_DT_SPEC_INST_GET(inst, reset_gpios),                                                          \
      .gpio_irq = GPIO_DT_SPEC_INST_GET(inst, interrupt_gpios),                                                        \
  };                                                                                                                   \
  DEVICE_DT_INST_DEFINE(inst, gps_zed_f9p_init, NULL, &gps_zed_f9p_data_##inst, &gps_zed_f9p_config_##inst,            \
                        POST_KERNEL, CONFIG_KERNEL_INIT_PRIORITY_DEVICE, &gps_zed_f9p_api);

DT_INST_FOREACH_STATUS_OKAY(ZED_F9P_DEFINE);

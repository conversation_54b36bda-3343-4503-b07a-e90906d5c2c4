/**
 * @file
 * @brief Type definitions and helpers for UBX protocol frames
 *
 * @remark All multi-byte values are sent in little endian order.
 */
#pragma once

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>
#include <string.h>

#include <zephyr/sys/byteorder.h>

#define UBX_HDR_LEN 6
#define UBX_FTR_LEN 2

#define UBX_MAGIC_0 0xB5
#define UBX_MAGIC_1 0x62

/**
 * @brief UBX message class/endpoint values
 */
typedef enum ubx_msg_class {
  kUbxClassNav = 0x01,
  kUbxClassAck = 0x05,
  kUbxClassCfg = 0x06,
} ubx_msg_class_t;

/**
 * @brief UBX packet header
 */
typedef struct ubx_hdr {
  // UBX magic values, must be 0xB5, 0x62
  uint8_t magic[2];
  // message class (e.g. endpoint)
  uint8_t msg_class;
  // message identifier (e.g. command on the endpoint)
  uint8_t msg_id;
  // number of bytes of payload data
  uint16_t payload_length;

  char payload[];
} __attribute__((packed)) ubx_hdr_t;

/**
 * @brief Set up UBX packet header
 */
static inline ubx_hdr_t *ubx_init_header(void *start, const ubx_msg_class_t class, const uint8_t msgid,
                                         const uint16_t length) {
  ubx_hdr_t *header = (ubx_hdr_t *)start;
  memset(header, 0, sizeof(*header));

  header->magic[0] = UBX_MAGIC_0;
  header->magic[1] = UBX_MAGIC_1;

  header->msg_class = class;
  header->msg_id = msgid;

  header->payload_length = sys_cpu_to_le16(length);

  return header;
}

/**
 * @brief Calculate UBX packet checksum
 *
 * The checksum is computed over the header and the payload bytes, the length of which is
 * specified by the header.
 *
 * @param data First byte of packet (0xB5 header)
 */
static inline void ubx_calc_checksum(const ubx_hdr_t *packet, uint8_t *outChecksum) {
  uint8_t CK_A = 0, CK_B = 0;

  uint8_t *data = (uint8_t *)packet;
  const size_t numBytes = sys_le16_to_cpu(packet->payload_length) + UBX_HDR_LEN;

  // checksum is calculated starting at class byte
  for (size_t i = offsetof(ubx_hdr_t, msg_class); i < numBytes; i++) {
    CK_A = CK_A + data[i];
    CK_B = CK_B + CK_A;
  }

  outChecksum[0] = CK_A;
  outChecksum[1] = CK_B;
}

/**
 * @brief Calculate and store UBX packet checksum
 *
 * Calculate the checksum over all bytes of the packet specified, then write the two checksum bytes
 * immediately after the end.
 *
 * @param data First byte of packet (0xB5 header)
 */
static inline void ubx_append_checksum(ubx_hdr_t *packet) {
  uint8_t *checksumOffset = packet->payload + sys_le16_to_cpu(packet->payload_length);
  ubx_calc_checksum(packet, checksumOffset);
}

/**
 * @brief Validate an UBX frame
 *
 * Check for the magic, sanity check the length and then verify the checksum.
 */
static inline int ubx_validate_packet(const ubx_hdr_t *packet, const size_t totalLength) {
  uint8_t expectedChecksum[2];

  // check magic
  if (packet->magic[0] != UBX_MAGIC_0 || packet->magic[1] != UBX_MAGIC_1) {
    return -EINVAL;
  }

  // sanity check length
  const size_t payloadLen = sys_le16_to_cpu(packet->payload_length);
  if (payloadLen > (totalLength - UBX_HDR_LEN - UBX_FTR_LEN)) {
    return -EINVAL;
  }

  // verify checksum
  ubx_calc_checksum(packet, expectedChecksum);

  const uint8_t *actualChecksum = packet->payload + sys_le16_to_cpu(packet->payload_length);
  if (expectedChecksum[0] != actualChecksum[0] || expectedChecksum[1] != actualChecksum[1]) {
    LOG_WRN("packet checksum mismatch: expected (%02x, %02x) got (%02x, %02x)", expectedChecksum[0],
            expectedChecksum[1], actualChecksum[0], actualChecksum[1]);
    return -EINVAL;
  }

  return 0;
}

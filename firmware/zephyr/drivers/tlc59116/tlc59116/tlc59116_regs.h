/**
 * @brief Register definitions
 */
#pragma once

#include <stdint.h>

#include <zephyr/drivers/i2c.h>

/* TLC59116 select registers determine the source that drives LED outputs (via LEDOUT reg) */
#define TLC59116_LED_OFF 0x0     /* LED driver off */
#define TLC59116_LED_ON 0x1      /* LED driver on */
#define TLC59116_LED_PWM 0x2     /* Controlled through PWM */
#define TLC59116_LED_GRP_PWM 0x3 /* Controlled through PWM/GRPPWM */

/* TLC59116 control register */
#define TLC59116_MODE1 0x00
#define TLC59116_MODE2 0x01
#define TLC59116_PWMx_BASE 0x02
#define TLC59116_GRPPWM 0x12
#define TLC59116_GRPFREQ 0x13
#define TLC59116_LEDOUT_BASE 0x14

/* TLC59116 mode register 1 */
#define TLC59116_MODE1_OSC (1U << 4)

/* TLC59116 mode register 2 */
#define TLC59116_MODE2_DMBLNK (1U << 5) /* Enable blinking */

#define TLC59116_MASK 0x03

// number of output LED channels
#define TLC59116_NUM_LEDS 16

/**
 * @brief Update the LEDOUT register for the specified channel
 */
static inline int tlc59116_set_ledout(const struct i2c_dt_spec *i2c, uint32_t led, uint8_t val) {
  const size_t regOffset = led / 4, ledOffset = regOffset * 4;

  const uint8_t reg = TLC59116_LEDOUT_BASE + regOffset;
  const size_t relativeLed = (led - ledOffset);

  const uint8_t mask = TLC59116_MASK << (relativeLed << 1);
  const uint8_t newValue = val << (relativeLed << 1);

  if (i2c_reg_update_byte_dt(i2c, reg, mask, newValue)) {
    LOG_ERR("LED reg $%x update failed", reg);
    return -EIO;
  }

  return 0;
}

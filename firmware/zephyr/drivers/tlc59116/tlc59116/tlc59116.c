#define DT_DRV_COMPAT ti_tlc59116

#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(led_tlc59116, CONFIG_LED_TLC59116_LOG_LEVEL);

#include <zephyr/device.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/drivers/i2c.h>
#include <zephyr/drivers/led.h>
#include <zephyr/kernel.h>

#include <stdint.h>
#include <string.h>

#include <utils/handle_errors.h>

#include "tlc59116.h"
#include "tlc59116_regs.h"

/**
 * @brief Per instance data
 */
typedef struct led_tlc59116_data {
  // TODO: anything to put here?
} led_tlc59116_data_t;

/**
 * @brief Per instance configuration data
 *
 * This is taken from the device tree.
 */
typedef struct led_tlc59116_config {
  // I2C device handle
  struct i2c_dt_spec i2c;
  // Optional hardware reset line
  const struct gpio_dt_spec reset_gpio;
} led_tlc59116_config_t;

static int tlc59116_write_bulk(const struct device *dev, uint32_t start_channel, uint32_t num_channels,
                               const uint8_t *buf);
static int tlc59116_led_blink(const struct device *dev, uint32_t led, uint32_t delay_on, uint32_t delay_off);
static int tlc59116_led_set_brightness(const struct device *dev, uint32_t led, uint8_t value);
static int tlc59116_led_on(const struct device *dev, uint32_t led);
static int tlc59116_led_off(const struct device *dev, uint32_t led);
static int tlc59116_led_init(const struct device *dev);

/**
 * @brief Update the brightness of multiple channels in one transaction
 *
 * Prepare an I2C bus write (to the PWMx registers) with autoincrement, effectively updating the
 * brightness of multiple channels in one go.
 *
 * @remark All channels must have previously been set to PWM output mode by using the
 *         `set_brightness` API.
 */
static int tlc59116_write_bulk(const struct device *dev, uint32_t start_channel, uint32_t num_channels,
                               const uint8_t *buf) {
  const led_tlc59116_config_t *config = dev->config;

  if (!buf) {
    return -EFAULT;
  } else if (start_channel >= TLC59116_NUM_LEDS || (start_channel + num_channels) > TLC59116_NUM_LEDS) {
    return -EINVAL;
  }

  // convert brightness values
  uint8_t brightnesses[TLC59116_NUM_LEDS];
  memset(brightnesses, 0, sizeof(brightnesses));

  for (size_t i = 0; i < num_channels; i++) {
    const uint8_t value = buf[i];
    if (value < TLC59116_MIN_BRIGHTNESS || value > TLC59116_MAX_BRIGHTNESS) {
      return -EINVAL;
    }

    brightnesses[i] = value;
  }

  // send the transaction
  const uint8_t command = (0b101 << 5) | (TLC59116_PWMx_BASE + start_channel);

  if (i2c_burst_write_dt(&config->i2c, command, brightnesses, num_channels)) {
    LOG_ERR("LED reg $%x update failed", command);
    return -EIO;
  }

  return 0;
}

/**
 * @brief Configure LED blinking frequency
 *
 * The blink frequency/period is shared by all LED outputs on the device.
 */
static int tlc59116_led_blink(const struct device *dev, uint32_t led, uint32_t delay_on, uint32_t delay_off) {
  const led_tlc59116_config_t *config = dev->config;
  uint8_t gdc, gfrq;
  uint32_t period;

  period = delay_on + delay_off;

  if (led >= TLC59116_NUM_LEDS) {
    return -EINVAL;
  }

  if (period < TLC59116_BLINK_MIN_PERIOD || period > TLC59116_BLINK_MAX_PERIOD) {
    return -EINVAL;
  }

  gdc = delay_on * 256U / period;
  if (i2c_reg_write_byte_dt(&config->i2c, TLC59116_GRPPWM, gdc)) {
    LOG_ERR("LED reg $%x write failed", TLC59116_GRPPWM);
    return -EIO;
  }

  gfrq = (period * 24U / 1000) - 1;
  if (i2c_reg_write_byte_dt(&config->i2c, TLC59116_GRPFREQ, gfrq)) {
    LOG_ERR("LED reg $%x write failed", TLC59116_GRPFREQ);
    return -EIO;
  }

  // Enable blinking mode
  if (i2c_reg_update_byte_dt(&config->i2c, TLC59116_MODE2, TLC59116_MODE2_DMBLNK, TLC59116_MODE2_DMBLNK)) {
    LOG_ERR("LED reg $%x update failed", TLC59116_MODE2);
    return -EIO;
  }

  // Select the GRPPWM source to drive the LED output
  return tlc59116_set_ledout(&config->i2c, led, TLC59116_LED_GRP_PWM);
}

/**
 * @brief Set the brightness of a LED channel
 *
 * Brightness should be in the range of 0-100.
 */
static int tlc59116_led_set_brightness(const struct device *dev, uint32_t led, uint8_t value) {
  const led_tlc59116_config_t *config = dev->config;

  if (led >= TLC59116_NUM_LEDS) {
    return -EINVAL;
  }

  if (value < TLC59116_MIN_BRIGHTNESS || value > TLC59116_MAX_BRIGHTNESS) {
    return -EINVAL;
  }

  // write brightness
  if (i2c_reg_write_byte_dt(&config->i2c, TLC59116_PWMx_BASE + led, value)) {
    LOG_ERR("LED $%x reg write failed", TLC59116_PWMx_BASE + led);
    return -EIO;
  }

  // led controlled by PWMx registers
  return tlc59116_set_ledout(&config->i2c, led, TLC59116_LED_PWM);
}

/**
 * @brief Turn on a LED output
 *
 * The LED is full on, though blinking may be applied.
 */
static int tlc59116_led_on(const struct device *dev, uint32_t led) {
  const led_tlc59116_config_t *config = dev->config;

  if (led >= TLC59116_NUM_LEDS) {
    return -EINVAL;
  }

  return tlc59116_set_ledout(&config->i2c, led, TLC59116_LED_ON);
}

/**
 * @brief Turn off a LED output channel
 */
static int tlc59116_led_off(const struct device *dev, uint32_t led) {
  const led_tlc59116_config_t *config = dev->config;

  if (led >= TLC59116_NUM_LEDS) {
    return -EINVAL;
  }

  return tlc59116_set_ledout(&config->i2c, led, TLC59116_LED_OFF);
}

/**
 * @brief Initialize TLC59116 driver
 */
static int tlc59116_led_init(const struct device *dev) {
  const led_tlc59116_config_t *config = dev->config;

  if (!device_is_ready(config->i2c.bus)) {
    LOG_ERR("I2C bus device %s is not ready", config->i2c.bus->name);
    return -ENODEV;
  }

  // assert reset line if specified
  if (config->reset_gpio.port) {
    HANDLE_UNLIKELY_BOOL(device_is_ready(config->reset_gpio.port), ENODEV);
    HANDLE_UNLIKELY(gpio_pin_configure_dt(&config->reset_gpio, GPIO_OUTPUT | GPIO_OUTPUT_INACTIVE));

    HANDLE_UNLIKELY(gpio_pin_set_dt(&config->reset_gpio, 1));
    k_busy_wait(500);
    HANDLE_UNLIKELY(gpio_pin_set_dt(&config->reset_gpio, 0));
    k_busy_wait(5000);
  }

  // wake up from sleep mode
  if (i2c_reg_update_byte_dt(&config->i2c, TLC59116_MODE1, TLC59116_MODE1_OSC, 0)) {
    LOG_ERR("LED reg $%x update failed", TLC59116_MODE1);
    return -EIO;
  }

  return 0;
}

/// API routines exported by this driver
static const struct led_driver_api gApiFuncs = {
    .blink = tlc59116_led_blink,
    .set_brightness = tlc59116_led_set_brightness,
    .on = tlc59116_led_on,
    .off = tlc59116_led_off,
    .write_channels = tlc59116_write_bulk,
};

// create a device instance
#define TLC59116_DEFINE(inst)                                                                                          \
  __dtcm_bss_section static led_tlc59116_data_t led_tlc59116_data_##inst = {};                                         \
  static const led_tlc59116_config_t led_tlc59116_config_##inst = {                                                    \
      .i2c = I2C_DT_SPEC_INST_GET(inst),                                                                               \
      .reset_gpio = GPIO_DT_SPEC_INST_GET_OR(inst, reset_gpios, {NULL}),                                               \
  };                                                                                                                   \
  DEVICE_DT_INST_DEFINE(inst, tlc59116_led_init, NULL, &led_tlc59116_data_##inst, &led_tlc59116_config_##inst,         \
                        POST_KERNEL, CONFIG_LED_INIT_PRIORITY, &gApiFuncs);

DT_INST_FOREACH_STATUS_OKAY(TLC59116_DEFINE)

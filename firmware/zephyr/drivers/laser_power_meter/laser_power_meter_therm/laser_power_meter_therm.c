#define DT_DRV_COMPAT cr_therm_laser_power_meter

#include <drivers/laser_power_meter.h>
#include <utils/adc.h>
#include <utils/handle_errors.h>
#include <zephyr/device.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/adc.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/sys/util_macro.h>

#include <logging/log.h>
LOG_MODULE_REGISTER(laser_power_meter_therm, CONFIG_LASER_POWER_METER_LOG_LEVEL);

#include <stdint.h>

// number of thermistor channels
#define NUM_CHANNELS 2
// number of times software oversampling is done for LPSU current feedback
#define ADC_CURRENT_OVERSAMPLE 16

struct lpm_data {
  // TODO: any data we need to store here?
};

struct lpm_config {
  // Photodiode current sense input
  const struct adc_dt_spec photodiode;
  // Thermistor current inputs
  const struct adc_dt_spec therm[NUM_CHANNELS];
  // LPSU current feedback input
  const struct adc_dt_spec current;
  // LPSU status feedback line
  const struct gpio_dt_spec status_in;
};

/**
 * @brief Read an ADC channel (using device tree settings) as millivolts
 */
static int do_read_adc(const struct adc_dt_spec *spec, int32_t *out) {
  uint16_t raw = 0;
  int32_t mv = 0;

  /*
   * Get the ADC reference voltage
   *
   * For some chips (notable gigadevice) we don't have a reference voltage provided by the
   * driver; in this case, assume it's fixed at 3.3V.
   */
  uint32_t vref = adc_ref_internal(spec->dev);
  if (!vref) {
    vref = CONFIG_LPMT_REF_VOLT_MV;
  }

  // read raw ADC value
  struct adc_sequence seq = {
      .buffer = &raw,
      .buffer_size = sizeof(raw),
      .resolution = spec->resolution,
      .channels = BIT(spec->channel_id),
  };
  HANDLE_UNLIKELY(adc_read(spec->dev, &seq));

  // convert raw ADC code to voltage
  mv = raw;

  HANDLE_UNLIKELY(adc_raw_to_millivolts(vref, spec->channel_cfg.gain, spec->resolution, &mv));

  *out = mv;
  return 0;
}

/**
 * @brief Initialize laser power meter handler
 *
 * Configure all of the analog input channels and GPIO.
 */
static int lpm_init(const struct device *dev) {
  struct lpm_config *cfg = (struct lpm_config *)dev->config;

  // configure the thermistor ADC channels
  for (size_t i = 0; i < NUM_CHANNELS; i++) {
    if (!cfg->therm[i].dev) {
      LOG_WRN("no thermistor for ch %u", i);
      continue;
    }

    HANDLE_UNLIKELY(!device_is_ready(cfg->therm[i].dev));
    HANDLE_UNLIKELY(adc_channel_setup_dt(&cfg->therm[i]));
  }

  // configure current feedback ADC line
  if (cfg->current.dev) {
    HANDLE_UNLIKELY(!device_is_ready(cfg->current.dev));
    HANDLE_UNLIKELY(adc_channel_setup_dt(&cfg->current));
  } else {
    LOG_WRN("no LPSU current feedback channel");
  }

  // configure photodiode ADC line
  if (cfg->photodiode.dev) {
    HANDLE_UNLIKELY(!device_is_ready(cfg->photodiode.dev));
    HANDLE_UNLIKELY(adc_channel_setup_dt(&cfg->photodiode));
  } else {
    LOG_WRN("no photodiode channel");
  }

  // set up status signal GPIO (if specified in config)
  if (cfg->status_in.port) {
    HANDLE_UNLIKELY_BOOL(device_is_ready(cfg->status_in.port), ENODEV);
    HANDLE_UNLIKELY(gpio_pin_configure_dt(&cfg->status_in, GPIO_INPUT));
  } else {
    LOG_WRN("no LPSU status signal");
  }

  return 0;
}

/**
 * @brief Get raw voltage on photodiode input (in mV)
 */
static int lpmt_get_raw_photodiode(const struct device *dev, uint32_t *reading) {
  struct lpm_config *cfg = (struct lpm_config *)dev->config;

  if (!cfg->photodiode.dev) {
    return -ENXIO;
  }

  HANDLE_UNLIKELY(do_read_adc(&cfg->photodiode, reading));

  return 0;
}

/**
 * @brief Get raw thermistor readings (in mV)
 */
static int lpmt_get_raw_readings(const struct device *dev, int32_t *reading1, int32_t *reading2) {
  struct lpm_config *cfg = (struct lpm_config *)dev->config;

  // ensure both channels are configured
  if (!cfg->therm[0].dev || !cfg->therm[1].dev) {
    return -ENXIO;
  }

  HANDLE_UNLIKELY(do_read_adc(&cfg->therm[0], reading1));
  HANDLE_UNLIKELY(do_read_adc(&cfg->therm[1], reading2));

  return 0;
}

/**
 * @brief Get laser power via thermistors
 */
static int lpmt_get_power(const struct device *dev, float *output) {
  int32_t mv_value[NUM_CHANNELS];
  HANDLE_UNLIKELY(lpmt_get_raw_readings(dev, &mv_value[0], &mv_value[1]));

  // TODO get formula to compare the 2 thermistors to calculate power.
  *output = ((float)mv_value[0]) +
            ((float)mv_value[1]) /
                10000.0; // Since max voltage should be 3.3v divide by 100000 should make all of value 2 in decimal
  return 0;
}

/**
 * @brief Read LPSU current
 */
static int lpmt_get_current(const struct device *dev, float *output) {
  struct lpm_config *cfg = (struct lpm_config *)dev->config;

  if (!cfg->current.dev) {
    return -ENXIO;
  }

  int32_t sum_mv_value = 0;

  for (int i = 0; i < ADC_CURRENT_OVERSAMPLE; i++) {
    int32_t mv_value = 0;
    HANDLE_UNLIKELY(do_read_adc(&cfg->current, &mv_value));

    sum_mv_value += mv_value;
  }

  // TODO get formula to convert mv to current
  *output = ((float)sum_mv_value / ADC_CURRENT_OVERSAMPLE) / 1000.0; // For now just reply with volts
  return 0;
}

/**
 * @brief Read the LPSU status GPIO
 */
static int lpmt_get_status(const struct device *dev, bool *output) {
  struct lpm_config *cfg = (struct lpm_config *)dev->config;

  if (!cfg->status_in.port) {
    return -ENXIO;
  }

  *output = gpio_pin_get_dt(&cfg->status_in);
  return 0;
}

static const struct laser_power_meter_api lpm_api_funcs = {
    .get_power = lpmt_get_power,
    .get_raw_readings = lpmt_get_raw_readings,
    .get_current = lpmt_get_current,
    .get_status = lpmt_get_status,
    .get_raw_photodiode = lpmt_get_raw_photodiode,
};

#define LPMT_DEFINE(inst)                                                                                              \
  static struct lpm_data lpm_data_##inst = {};                                                                         \
  static const struct lpm_config lpm_config_##inst = {                                                                 \
      .photodiode = ADC_DT_SPEC_INST_GET_BY_NAME_OR_NULL(inst, photodiode),                                            \
      .therm =                                                                                                         \
          {                                                                                                            \
              ADC_DT_SPEC_INST_GET_BY_NAME_OR_NULL(inst, therm1),                                                      \
              ADC_DT_SPEC_INST_GET_BY_NAME_OR_NULL(inst, therm2),                                                      \
          },                                                                                                           \
      .current = ADC_DT_SPEC_INST_GET_BY_NAME_OR_NULL(inst, current),                                                  \
      .status_in = GPIO_DT_SPEC_INST_GET_OR(inst, status_in_gpios, {NULL}),                                            \
  };                                                                                                                   \
  DEVICE_DT_INST_DEFINE(inst, lpm_init, NULL, &lpm_data_##inst, &lpm_config_##inst, POST_KERNEL,                       \
                        CONFIG_KERNEL_INIT_PRIORITY_DEVICE, &lpm_api_funcs);

/* Create the struct device for every status "okay" node in the devicetree. */
DT_INST_FOREACH_STATUS_OKAY(LPMT_DEFINE)

/**
 * @file
 *
 * @brief BMI270 IMU driver
 *
 * This is a very basic driver for the BMI270 IMU; it supports only the simplest features needed
 * for Reaper board.
 */
#pragma once

#include <stdint.h>

#include <zephyr/device.h>

/**
 * @brief Data provided by IMU
 *
 * Contains the acceleration values (in m/s^s) and gyroscope values (in °/sec) with the
 * associated measurement timestamp.
 */
typedef struct imu_bmi270_data {
  /// Accelerometer data (m/s^2)
  struct {
    /// System timestamp at which this data was collected
    int64_t timestamp;

    union {
      struct {
        float x, y, z;
      };
      float data[3];
    };
  } accel;

  /// Gyroscope data (°/sec)
  struct {
    /// System timestamp at which this data was collected
    int64_t timestamp;

    union {
      struct {
        float x, y, z;
      };
      float data[3];
    };
  } gyro;
} imu_bmi270_data_t;

/**
 * @brief API functions exported by the IMU sensor driver
 */
typedef struct imu_bmi270_api {
  /**
   * @brief Soft reset and configure IMU
   *
   * Perform a soft reset, then upload the mandatory microcode to the device. It is then
   * configured for basic operation.
   */
  int (*boot)(const struct device *imu);

  /**
   * @brief Fetch most recent data from sensor
   *
   * Trigger the conversion of new data and read out the previous data from the device into
   * an internal buffer.
   */
  int (*trigger)(const struct device *imu);

  /**
   * @brief Copy out most recent sensor data
   *
   * Reads out the most recently acquired sensor data, if any.
   */
  int (*getReading)(const struct device *imu, imu_bmi270_data_t *outData);
} imu_bmi270_api_t;

/**
 * @brief Configure IMU for operation
 */
static inline int imu_bmi270_boot(const struct device *dev) {
  const imu_bmi270_api_t *api = (const imu_bmi270_api_t *)dev->api;
  return api->boot(dev);
}

/**
 * @brief Trigger a new reading and fetch it
 */
static inline int imu_bmi270_trigger(const struct device *dev) {
  const imu_bmi270_api_t *api = (const imu_bmi270_api_t *)dev->api;
  return api->trigger(dev);
}

/**
 * @brief Copy out most recent data
 */
static inline int imu_bmi270_get(const struct device *dev, imu_bmi270_data_t *outData) {
  const imu_bmi270_api_t *api = (const imu_bmi270_api_t *)dev->api;
  return api->getReading(dev, outData);
}

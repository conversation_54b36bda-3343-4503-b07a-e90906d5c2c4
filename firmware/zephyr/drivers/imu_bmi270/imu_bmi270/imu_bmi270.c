#define DT_DRV_COMPAT cr_bmi270

#include <stdint.h>
#include <string.h>

#include <zephyr/device.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/adc.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/drivers/i2c.h>
#include <zephyr/kernel.h>
#include <zephyr/logging/log.h>

#include <drivers/imu_bmi270.h>
#include <utils/handle_errors.h>

#include "bmi270.h"

LOG_MODULE_REGISTER(imu_bmi270, CONFIG_IMU_BMI270_LOG_LEVEL);

/**
 * @brief Per instance data
 *
 * Current state of the device
 */
typedef struct bmi270_data {
  // lock for internal state
  struct k_mutex lock;

  // sensor handle
  struct bmi2_dev bmi;
  // Most recently read out data
  imu_bmi270_data_t lastData;
} bmi270_data_t;

/**
 * @brief Per instance configuration data
 *
 * This is taken from the device tree.
 */
typedef struct bmi270_config {
  // How to access the device
  struct i2c_dt_spec i2c;
} bmi270_config_t;

static BMI2_INTF_RETURN_TYPE bmi_i2c_read(uint8_t reg, uint8_t *rxBuffer, uint32_t rxLen, void *ctx);
static BMI2_INTF_RETURN_TYPE bmi_i2c_write(uint8_t reg, const uint8_t *txBuffer, uint32_t txLen, void *ctx);
static void bmi_delay_us(uint32_t period, void *ctx);
static float bmi_lsb_to_dps(int16_t raw, float fullScale, uint8_t resolution);
static float bmi_lsb_to_ms2(int16_t raw, float range, uint8_t resolution);

/**
 * @brief Device driver init routine
 *
 * Verify the chip ID of the device is sane. The actual initialization (uploading microcode) takes
 * place on request from the user.
 */
static int imu_init(const struct device *dev) {
  struct bmi270_config *cfg = (struct bmi270_config *)dev->config;
  struct bmi270_data *data = (struct bmi270_data *)dev->data;

  // initialize internal state
  memset(data, 0, sizeof(*data));

  k_mutex_init(&data->lock);

  // set up the I2C bus
  HANDLE_UNLIKELY_BOOL(device_is_ready(cfg->i2c.bus), ENODEV);

  data->bmi.intf = BMI2_I2C_INTF;
  data->bmi.intf_ptr = cfg;
  data->bmi.read = bmi_i2c_read;
  data->bmi.write = bmi_i2c_write;
  data->bmi.delay_us = bmi_delay_us;
  // TODO: figure out the proper max IO length
  data->bmi.read_write_len = 128;

  return 0;
}

/**
 * @brief Start the IMU
 *
 * Perform soft reset and then load microcode to the device. The IMU sensors are configured and
 * enabled after this call.
 */
static int imu_start(const struct device *dev) {
  struct bmi2_sens_config imuConf[2];

  struct bmi270_data *data = (struct bmi270_data *)dev->data;

  // initialize sensor (upload microcode) and then get its current config
  HANDLE_UNLIKELY_BOOL(bmi270_init(&data->bmi) == BMI2_OK, EIO);

  imuConf[0].type = BMI2_ACCEL;
  imuConf[1].type = BMI2_GYRO;

  HANDLE_UNLIKELY_BOOL(bmi2_get_sensor_config(imuConf, 2, &data->bmi) == BMI2_OK, EIO);

  // apply new configuration
  imuConf[0].cfg.acc.odr = BMI2_ACC_ODR_25HZ;
  imuConf[0].cfg.acc.range = BMI2_ACC_RANGE_2G;
  imuConf[0].cfg.acc.bwp = BMI2_ACC_NORMAL_AVG4;
  imuConf[0].cfg.acc.filter_perf = BMI2_PERF_OPT_MODE;

  imuConf[1].cfg.gyr.odr = BMI2_GYR_ODR_25HZ;
  imuConf[1].cfg.gyr.range = BMI2_GYR_RANGE_250;
  imuConf[1].cfg.gyr.bwp = BMI2_GYR_NORMAL_MODE;
  imuConf[1].cfg.gyr.noise_perf = BMI2_POWER_OPT_MODE;
  imuConf[1].cfg.gyr.filter_perf = BMI2_PERF_OPT_MODE;

  HANDLE_UNLIKELY_BOOL(bmi2_set_sensor_config(imuConf, 2, &data->bmi) == BMI2_OK, EIO);

  // enable all sensors
  uint8_t sensorList[2] = {BMI2_ACCEL, BMI2_GYRO};
  HANDLE_UNLIKELY_BOOL(
      bmi2_sensor_enable(sensorList, sizeof(sensorList) / sizeof(sensorList[0]), &data->bmi) == BMI2_OK, EIO);

  return 0;
}

/**
 * @brief Trigger an IMU conversion
 *
 * Data is transferred from the device into our internal data buffer.
 */
static int imu_trigger(const struct device *dev) {
  struct bmi270_data *data = (struct bmi270_data *)dev->data;

  // read out the raw samples
  struct bmi2_sens_data rawData;
  HANDLE_UNLIKELY_BOOL(bmi2_get_sensor_data(&rawData, &data->bmi) == BMI2_OK, EIO);

  // convert to absolute units and store
  imu_bmi270_data_t converted;
  memset(&converted, 0, sizeof(converted));

  // TODO: use sensortime
  const int64_t now = k_uptime_get();

  if (rawData.status & BMI2_DRDY_ACC) {
    converted.accel.x = bmi_lsb_to_ms2(rawData.acc.x, 2, data->bmi.resolution);
    converted.accel.y = bmi_lsb_to_ms2(rawData.acc.y, 2, data->bmi.resolution);
    converted.accel.z = bmi_lsb_to_ms2(rawData.acc.z, 2, data->bmi.resolution);

    converted.accel.timestamp = now;
  }
  if (rawData.status & BMI2_DRDY_GYR) {
    converted.gyro.x = bmi_lsb_to_dps(rawData.gyr.x, 250, data->bmi.resolution);
    converted.gyro.y = bmi_lsb_to_dps(rawData.gyr.y, 250, data->bmi.resolution);
    converted.gyro.z = bmi_lsb_to_dps(rawData.gyr.z, 250, data->bmi.resolution);

    converted.gyro.timestamp = now;
  }

  // store the converted data (under lock)
  HANDLE_UNLIKELY(k_mutex_lock(&data->lock, K_FOREVER));
  { data->lastData = converted; }
  k_mutex_unlock(&data->lock);

  return 0;
}

/**
 * @brief Copy out the last reading
 */
static int imu_get_reading(const struct device *dev, imu_bmi270_data_t *outData) {
  struct bmi270_data *data = (struct bmi270_data *)dev->data;

  HANDLE_UNLIKELY(k_mutex_lock(&data->lock, K_FOREVER));
  { memcpy(outData, &data->lastData, sizeof(*outData)); }
  k_mutex_unlock(&data->lock);

  return 0;
}

/**
 * @brief Read register(s) from attached device
 *
 * Issue an I2C read transaction, starting with the specified register.
 *
 * @param reg Register number to begin reading at
 * @param rxBuffer Memory to receive read data
 * @param rxLen Number of bytes to read from device (excluding register number)
 * @param ctx Pointer to the device config (`bmi270_config_t`) structure
 */
static BMI2_INTF_RETURN_TYPE bmi_i2c_read(uint8_t reg, uint8_t *rxBuffer, uint32_t rxLen, void *ctx) {
  struct bmi270_config *cfg = (struct bmi270_config *)ctx;
  int err;

  err = i2c_burst_read_dt(&cfg->i2c, reg, rxBuffer, rxLen);

  return (err == 0) ? BMI2_INTF_RET_SUCCESS : BMI2_E_COM_FAIL;
}

/**
 * @brief Write register(s) on attached device
 *
 * Issue an I2C transaction to write data to the device, starting with the specified register
 * number.
 *
 * @param reg Register number to begin writing at
 * @param txBuffer Data to be written to device
 * @param txLen Number of bytes to write to device (excluding register number)
 * @param ctx Pointer to the device config (`bmi270_config_t`) structure
 */
static BMI2_INTF_RETURN_TYPE bmi_i2c_write(uint8_t reg, const uint8_t *txBuffer, uint32_t txLen, void *ctx) {
  struct bmi270_config *cfg = (struct bmi270_config *)ctx;
  int err;

  err = i2c_burst_write_dt(&cfg->i2c, reg, txBuffer, txLen);

  return (err == 0) ? BMI2_INTF_RET_SUCCESS : BMI2_E_COM_FAIL;
}

/**
 * @brief Driver callback for delay
 *
 * Implement a blocking delay (of at least `period` µsec) for the driver.
 *
 * @param ctx Pointer to the device config (`bmi270_config_t`) structure
 */
static void bmi_delay_us(uint32_t period, void *ctx) { k_usleep(period); }

/**
 * @brief Convert raw accelerometer value to m/s^2
 */
static float bmi_lsb_to_ms2(int16_t raw, float range, uint8_t resolution) {
  static const float g = 9.80665f;
  float half_scale = ((float)(1 << resolution) / 2.0f);
  return (g * raw * range) / half_scale;
}

/**
 * @brief Convert raw gyroscope value to °/sec
 */
static float bmi_lsb_to_dps(int16_t raw, float fullScale, uint8_t resolution) {
  float half_scale = ((float)(1 << resolution) / 2.0f);
  return (fullScale / half_scale) * ((float)raw);
}

/// API routines exported by this driver
static const imu_bmi270_api_t gApiFuncs = {
    .boot = imu_start,
    .trigger = imu_trigger,
    .getReading = imu_get_reading,
};

// create a device instance
#define BMI270_DEFINE(inst)                                                                                            \
  __dtcm_bss_section static bmi270_data_t bmi270_data_##inst = {};                                                     \
  static const bmi270_config_t bmi270_config_##inst = {.i2c = I2C_DT_SPEC_INST_GET(inst)};                             \
  DEVICE_DT_INST_DEFINE(inst, imu_init, NULL, &bmi270_data_##inst, &bmi270_config_##inst, POST_KERNEL,                 \
                        CONFIG_KERNEL_INIT_PRIORITY_DEVICE, &gApiFuncs);

DT_INST_FOREACH_STATUS_OKAY(BMI270_DEFINE)

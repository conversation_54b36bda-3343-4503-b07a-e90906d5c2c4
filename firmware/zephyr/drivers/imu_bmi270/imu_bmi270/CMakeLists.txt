# get the official <PERSON><PERSON> driver for this sensor
include(<PERSON><PERSON><PERSON>ontent)

FetchContent_Declare(
    bmi270_api
    GIT_REPOSITORY  https://github.com/boschsensortec/BMI270_SensorAPI.git
    # v2.86.1
    GIT_TAG         d270cde
)
FetchContent_MakeAvailable(bmi270_api)

# then build our wrapper around it
zephyr_library()
zephyr_library_sources(imu_bmi270.c)

# include the BMI270 library as part of it
zephyr_library_sources(
    ${bmi270_api_SOURCE_DIR}/bmi2.c
    ${bmi270_api_SOURCE_DIR}/bmi270.c
)
zephyr_library_include_directories(${bmi270_api_SOURCE_DIR})

/*
 * Copyright (c) 2021 Carbon Robotics.
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#define DT_DRV_COMPAT eo_liquid_lens_max14515

#include <logging/log.h>
LOG_MODULE_REGISTER(liquid_lens_max14515, CONFIG_LIQUID_LENS_LOG_LEVEL);

#include <device.h>
#include <devicetree.h>
#include <drivers/i2c.h>
#include <drivers/liquid_lens.h>
#include <errno.h>
#include <init.h>

#define ENABLE_ADDR 0x00
#define VOLTAGE_ADDR 0x01

struct liquid_lens_data {};
struct liquid_lens_config {
  const struct device *bus;
  uint16_t i2c_addr;
};

#define RETURN_ON_ERR(x)                                                                                               \
  do {                                                                                                                 \
    int ret = x;                                                                                                       \
    if (ret != 0) {                                                                                                    \
      return ret;                                                                                                      \
    }                                                                                                                  \
  } while (false);
static int liquid_lens_set_val(const struct device *dev, uint8_t channel, uint16_t input) {
  struct liquid_lens_config *cfg = (struct liquid_lens_config *)dev->config;
  uint8_t buf = 255;
  if (input > 255) {
    LOG_ERR("Invalid input setting to max value");
  } else {
    buf = input;
  }
  return i2c_reg_write_byte(cfg->bus, cfg->i2c_addr, VOLTAGE_ADDR, buf);
}
static int liquid_lens_get_val(const struct device *dev, uint8_t channel, uint16_t *output) {
  struct liquid_lens_config *cfg = (struct liquid_lens_config *)dev->config;
  uint8_t buf;
  RETURN_ON_ERR(i2c_reg_read_byte(cfg->bus, cfg->i2c_addr, VOLTAGE_ADDR, &buf));
  *output = buf;
  return 0;
}

static const struct liquid_lens_api liquid_lens_api_funcs = {
    .set = liquid_lens_set_val,
    .get = liquid_lens_get_val,
};

static int liquid_lens_init(const struct device *dev) {
  struct liquid_lens_config *cfg = (struct liquid_lens_config *)dev->config;
  if (!device_is_ready(cfg->bus)) {
    return ENODEV;
  }
  RETURN_ON_ERR(i2c_reg_write_byte(cfg->bus, cfg->i2c_addr, ENABLE_ADDR, 0b00000001));
  return 0;
}

#define LIQUID_LENS_DEFINE(inst)                                                                                       \
  static struct liquid_lens_data liquid_lens_data_##inst;                                                              \
  static const struct liquid_lens_config liquid_lens_config_##inst = {.bus = DEVICE_DT_GET(DT_INST_BUS(inst)),         \
                                                                      .i2c_addr = DT_INST_REG_ADDR(inst)};             \
  DEVICE_DT_INST_DEFINE(inst, &liquid_lens_init, NULL, &liquid_lens_data_##inst, &liquid_lens_config_##inst,           \
                        POST_KERNEL, CONFIG_KERNEL_INIT_PRIORITY_DEVICE, &liquid_lens_api_funcs);

/* Create the struct device for every status "okay" node in the devicetree. */
DT_INST_FOREACH_STATUS_OKAY(LIQUID_LENS_DEFINE)
/*
 * Copyright (c) 2021 Carbon Robotics.
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#define DT_DRV_COMPAT eo_liquid_lens_max14574

#include <logging/log.h>
LOG_MODULE_REGISTER(liquid_lens_max14574, CONFIG_LIQUID_LENS_LOG_LEVEL);

#include <device.h>
#include <devicetree.h>
#include <drivers/i2c.h>
#include <drivers/liquid_lens.h>
#include <errno.h>
#include <init.h>

#include <utils/handle_errors.h>

#define STATUS_ADDR 0x00
#define FAIL_ADDR 0x01
#define ENABLE_ADDR 0x03
#define VOLTAGE_ADDR 0x04
#define CMD_ADDR 0x09
#define DRIVER_CONF_ADDR 0x0A

#define MAX_8BIT 255
#define MAX_10BIT 1023

struct liquid_lens_data {
  uint16_t remainder;
};
struct liquid_lens_config {
  const struct device *bus;
  uint16_t i2c_addr;
};

#define CHECK_CHANNEL(channel)                                                                                         \
  if (channel == 0 || channel > 4) {                                                                                   \
    return -1;                                                                                                         \
  }

static int check_status(const struct device *dev) {
  struct liquid_lens_config *cfg = (struct liquid_lens_config *)dev->config;
  uint8_t buf;
  HANDLE_UNLIKELY(i2c_reg_read_byte(cfg->bus, cfg->i2c_addr, STATUS_ADDR, &buf));
  LOG_DBG("status = %u", buf);
  if ((buf & 0b100) != 0) {
    LOG_ERR("Liquid lens reported BST_FAIL");
    return -1;
  }
  HANDLE_UNLIKELY(i2c_reg_read_byte(cfg->bus, cfg->i2c_addr, FAIL_ADDR, &buf));
  LOG_DBG("fail = %u", buf);
  return 0;
}
static uint16_t scale_8bit_to_10bit(uint32_t input) {
  input = (input * MAX_10BIT) / MAX_8BIT;
  return (uint16_t)input;
}
static uint16_t scale_10bit_to_8bit(uint32_t input) {
  input = (input * MAX_8BIT) / MAX_10BIT;
  return (uint16_t)input;
}

static int liquid_lens_set_val(const struct device *dev, uint8_t channel, uint16_t input) {
  check_status(dev);
  CHECK_CHANNEL(channel);
  struct liquid_lens_config *cfg = (struct liquid_lens_config *)dev->config;
  struct liquid_lens_data *data = (struct liquid_lens_data *)dev->data;
  uint16_t tmp = scale_8bit_to_10bit(input);
  uint16_t calculated = scale_10bit_to_8bit(tmp);
  data->remainder = input - calculated; // Since using integer math calculated will always be less than or equal to
                                        // input so remainder can never be negative.
  input = tmp;
  uint8_t buf;
  uint8_t mask = ~(0b11 << ((channel - 1) * 2)); // Keep all but the 2 bits we need to set
  i2c_reg_read_byte(cfg->bus, cfg->i2c_addr, VOLTAGE_ADDR, &buf);
  buf = (buf & mask) | ((input & 0b11) << ((channel - 1) * 2));
  HANDLE_UNLIKELY(i2c_reg_write_byte(cfg->bus, cfg->i2c_addr, VOLTAGE_ADDR, buf));
  buf = (input >> 2) & 0xFF;
  HANDLE_UNLIKELY(i2c_reg_write_byte(cfg->bus, cfg->i2c_addr, VOLTAGE_ADDR + channel, buf));
  HANDLE_UNLIKELY(i2c_reg_write_byte(cfg->bus, cfg->i2c_addr, CMD_ADDR, 0b10));
  check_status(dev);
  return 0;
}
static int liquid_lens_get_val(const struct device *dev, uint8_t channel, uint16_t *output) {
  check_status(dev);
  CHECK_CHANNEL(channel);
  uint32_t tmp = 0;
  struct liquid_lens_config *cfg = (struct liquid_lens_config *)dev->config;
  struct liquid_lens_data *data = (struct liquid_lens_data *)dev->data;
  uint8_t buf;
  HANDLE_UNLIKELY(i2c_reg_read_byte(cfg->bus, cfg->i2c_addr, VOLTAGE_ADDR, &buf));
  tmp = (buf >> ((channel - 1) * 2)) & 0b11;
  HANDLE_UNLIKELY(i2c_reg_read_byte(cfg->bus, cfg->i2c_addr, VOLTAGE_ADDR + channel, &buf));
  tmp |= buf << 2;
  check_status(dev);
  *output = scale_10bit_to_8bit(tmp) + data->remainder;
  return 0;
}

static const struct liquid_lens_api liquid_lens_api_funcs = {
    .set = liquid_lens_set_val,
    .get = liquid_lens_get_val,
};

static int liquid_lens_init(const struct device *dev) {
  struct liquid_lens_config *cfg = (struct liquid_lens_config *)dev->config;
  if (!device_is_ready(cfg->bus)) {
    return ENODEV;
  }
  HANDLE_UNLIKELY(i2c_reg_write_byte(cfg->bus, cfg->i2c_addr, ENABLE_ADDR, 0b00000000));
  HANDLE_UNLIKELY(i2c_reg_write_byte(cfg->bus, cfg->i2c_addr, DRIVER_CONF_ADDR, 0b10000011));
  HANDLE_UNLIKELY(i2c_reg_write_byte(cfg->bus, cfg->i2c_addr, ENABLE_ADDR, 0b00000011));
  return 0;
}

#define LIQUID_LENS_DEFINE(inst)                                                                                       \
  static struct liquid_lens_data liquid_lens_data_##inst = {.remainder = 0};                                           \
  static const struct liquid_lens_config liquid_lens_config_##inst = {.bus = DEVICE_DT_GET(DT_INST_BUS(inst)),         \
                                                                      .i2c_addr = DT_INST_REG_ADDR(inst)};             \
  DEVICE_DT_INST_DEFINE(inst, &liquid_lens_init, NULL, &liquid_lens_data_##inst, &liquid_lens_config_##inst,           \
                        POST_KERNEL, CONFIG_KERNEL_INIT_PRIORITY_DEVICE, &liquid_lens_api_funcs);

/* Create the struct device for every status "okay" node in the devicetree. */
DT_INST_FOREACH_STATUS_OKAY(LIQUID_LENS_DEFINE)
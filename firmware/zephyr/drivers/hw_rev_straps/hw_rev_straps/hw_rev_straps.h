/**
 * @file
 * @brief Hardware revision GPIO strap driver
 *
 * Reads a hardware revision (usually indicated by a set of resistors connected to GPIO pins) and
 * makes it available as an integer value. This is read during initialization and then is not
 * changed.
 */
#pragma once

#include <stdint.h>
#include <zephyr/device.h>

/**
 * @brief API functions exported by the driver
 */
typedef struct hw_rev_straps_api {
  int (*getRev)(const struct device *dev, uint32_t *outRev);
} hw_rev_straps_api_t;

/**
 * @brief Retrieve hardware version
 */
static inline int hw_rev_straps_get(const struct device *dev, uint32_t *outRev) {
  const hw_rev_straps_api_t *api = (const hw_rev_straps_api_t *)dev->api;
  return api->getRev(dev, outRev);
}

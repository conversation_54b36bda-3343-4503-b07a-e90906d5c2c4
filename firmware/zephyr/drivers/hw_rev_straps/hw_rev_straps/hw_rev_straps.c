#define DT_DRV_COMPAT cr_hw_rev_straps

#include <device.h>
#include <devicetree.h>
#include <drivers/gpio.h>
#include <kernel.h>
#include <string.h>

#include <utils/handle_errors.h>

#include <logging/log.h>
LOG_MODULE_REGISTER(hw_rev_straps, CONFIG_HW_REV_STRAPS_LOG_LEVEL);

#include "hw_rev_straps.h"

/**
 * @brief Per instance data
 */
typedef struct rev_straps_data {
  // revision read from the strapping pins (during init)
  uint32_t rev;
} rev_straps_data_t;

/**
 * @brief Per instance configuration data
 *
 * This is taken from the device tree.
 */
typedef struct rev_straps_config {
  // number of GPIO pins to be read
  size_t numPins;
  // GPIO pins to be accessed
  const struct gpio_dt_spec gpios[];
} rev_straps_config_t;

static int rev_straps_init(const struct device *dev);
static int rev_straps_get(const struct device *dev, uint32_t *outRev);
static int rev_straps_read(const struct device *dev);

/**
 * @brief Initialize GPIO straps handler
 *
 * Set up the GPIOs and read out their value.
 */
static int rev_straps_init(const struct device *dev) {
  int err;

  const rev_straps_config_t *conf = (rev_straps_config_t *)dev->config;

  // set up IOs
  for (size_t i = 0; i < conf->numPins; i++) {
    HANDLE_UNLIKELY(!device_is_ready(conf->gpios[i].port));
    HANDLE_UNLIKELY(gpio_pin_configure_dt(&conf->gpios[i], GPIO_INPUT));
  }

  // then read the straps
  err = rev_straps_read(dev);
  if (err) {
    LOG_WRN("failed to read rev straps: %d", err);
  }

  return err;
}

/**
 * @brief Copy out previously retrieved hw revision
 */
static int rev_straps_get(const struct device *dev, uint32_t *outRev) {
  if (!outRev) {
    return -EFAULT;
  }

  rev_straps_data_t *data = (rev_straps_data_t *)dev->data;
  *outRev = data->rev;

  return 0;
}

/*
 * @brief Read GPIO strap pins
 */
static int rev_straps_read(const struct device *dev) {
  int err;
  uint8_t rev = 0;

  const rev_straps_config_t *conf = (rev_straps_config_t *)dev->config;
  rev_straps_data_t *data = (rev_straps_data_t *)dev->data;

  for (size_t i = 0; i < conf->numPins; i++) {
    err = gpio_pin_get_dt(&conf->gpios[i]);
    if (err < 0) {
      return err;
    }

    rev |= ((!!err ? 1U : 0U) << i);
  }

  data->rev = rev;
  return 0;
}

/// API routines exported by this driver
static const hw_rev_straps_api_t gApiFuncs = {
    .getRev = rev_straps_get,
};

// create a device instance
#define STRAPS_DEFINE(inst)                                                                                            \
  __dtcm_bss_section static rev_straps_data_t rev_straps_data_##inst = {};                                             \
  static const rev_straps_config_t rev_straps_config_##inst = {                                                        \
      .numPins = DT_INST_PROP_LEN(inst, gpios),                                                                        \
      .gpios =                                                                                                         \
          {                                                                                                            \
              GPIO_DT_SPEC_INST_GET_BY_IDX(inst, gpios, 0),                                                            \
              GPIO_DT_SPEC_INST_GET_BY_IDX(inst, gpios, 1),                                                            \
              GPIO_DT_SPEC_INST_GET_BY_IDX(inst, gpios, 2),                                                            \
              GPIO_DT_SPEC_INST_GET_BY_IDX(inst, gpios, 3),                                                            \
          },                                                                                                           \
  };                                                                                                                   \
  DEVICE_DT_INST_DEFINE(inst, rev_straps_init, NULL, &rev_straps_data_##inst, &rev_straps_config_##inst, POST_KERNEL,  \
                        CONFIG_KERNEL_INIT_PRIORITY_DEVICE, &gApiFuncs);

DT_INST_FOREACH_STATUS_OKAY(STRAPS_DEFINE)

#define DT_DRV_COMPAT gd_gd32_qdec64k

#include <errno.h>

#include <device.h>
#include <drivers/pinctrl.h>
#include <drivers/qdec64k.h>
#include <gd32f4xx_timer.h>
#include <init.h>
#include <kernel.h>
#include <soc.h>

#include <logging/log.h>
LOG_MODULE_REGISTER(qdec64k_gd32, CONFIG_QDEC64K_LOG_LEVEL);

/**
 * XXX: this is kind of a hack to work around incompatible pointer typess being passed to the
 * `timer_input_capture_config` function; this stuff seems to work so I'm not going to touch it
 * further for now. This just allows building with gcc14
 */
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wincompatible-pointer-types"

/** Quadrature decoder data. */
struct qdec64k_gd32_data {
  uint16_t ticks;
};

/** Quadrature decoder configuration. */
struct qdec64k_gd32_config {
  /** Timer instance. */
  uint32_t timer;

  /** Clock configuration */
  uint32_t rcu_periph_clock;
  uint32_t rcu_periph_reset;

  /** pinctrl configurations. */
  const struct pinctrl_dev_config *pcfg;
};

static int qdec64k_gd32_sample_fetch(const struct device *dev, enum sensor_channel chan) {
  struct qdec64k_gd32_data *data = dev->data;
  const struct qdec64k_gd32_config *cfg = dev->config;

  if ((int16_t)chan != SENSOR_CHAN_QDEC64K_TICKS && chan != SENSOR_CHAN_ALL) {
    return -ENOTSUP;
  }

  data->ticks = timer_counter_read(cfg->timer);

  return 0;
}

static int qdec64k_gd32_channel_get(const struct device *dev, enum sensor_channel chan, struct sensor_value *val) {
  struct qdec64k_gd32_data *data = dev->data;

  if ((int16_t)chan != SENSOR_CHAN_QDEC64K_TICKS) {
    return -ENOTSUP;
  }

  val->val1 = data->ticks;

  return 0;
}

static const struct sensor_driver_api qdec64k_gd32_driver_api = {
    .sample_fetch = qdec64k_gd32_sample_fetch,
    .channel_get = qdec64k_gd32_channel_get,
};

static int qdec64k_gd32_init(const struct device *dev) {
  int ret;
  struct qdec64k_gd32_data *data = dev->data;
  const struct qdec64k_gd32_config *cfg = dev->config;

  /* initialize data */
  data->ticks = 0u;

  rcu_periph_clock_enable(cfg->rcu_periph_clock);

  /* configure pinmux */
  ret = pinctrl_apply_state(cfg->pcfg, PINCTRL_STATE_DEFAULT);
  if (ret < 0) {
    LOG_ERR("Quadrature decoder pinctrl setup failed (%d)", ret);
    return ret;
  }

  /* initialize timer */
  timer_parameter_struct init;
  timer_struct_para_init(&init);
  init.period = 65535u;

  init.alignedmode = TIMER_COUNTER_EDGE; // Configure the edge to its mode
  init.clockdivision =
      TIMER_CKDIV_DIV1; // Configure dead time and sampling clock （DTS） Frequency division coefficient between
  init.counterdirection = TIMER_COUNTER_UP; // Configure the counting direction to count up
  init.prescaler = 0;                       // prescale
  init.repetitioncounter = 0;

  gd32_timer_init(cfg->timer, &init);

  timer_ic_parameter_struct input_init;
  timer_channel_input_struct_para_init(&input_init);

  input_init.icfilter = 0x5; // Configure filter
  input_init.icpolarity = TIMER_IC_POLARITY_RISING;
  input_init.icselection = TIMER_IC_SELECTION_DIRECTTI;
  input_init.icprescaler = TIMER_IC_PSC_DIV1;

  timer_input_capture_config(cfg->timer, TIMER_CH_0, &init);
  timer_input_capture_config(cfg->timer, TIMER_CH_1, &init);

  /* configure timer as encoder */
  timer_quadrature_decoder_mode_config(cfg->timer,
                                       TIMER_ENCODER_MODE2, // count on every A & B edge
                                       TIMER_IC_POLARITY_RISING, TIMER_IC_POLARITY_RISING);

  timer_enable(cfg->timer);

  return 0;
}

#define DT_INST_CLK(index, inst)                                                                                       \
  {                                                                                                                    \
    .bus = DT_CLOCKS_CELL(DT_PARENT(DT_DRV_INST(index)), bus),                                                         \
    .enr = DT_CLOCKS_CELL(DT_PARENT(DT_DRV_INST(index)), bits)                                                         \
  }

#define QDEC64K_DEVICE_INIT(index)                                                                                     \
  static struct qdec64k_gd32_data qdec64k_gd32_data_##index;                                                           \
                                                                                                                       \
  PINCTRL_DT_INST_DEFINE(index);                                                                                       \
                                                                                                                       \
  static const struct qdec64k_gd32_config qdec64k_gd32_config_##index = {                                              \
      .timer = (uint32_t)DT_REG_ADDR(DT_PARENT(DT_DRV_INST(index))),                                                   \
      .rcu_periph_clock = DT_PROP(DT_INST_PARENT(index), rcu_periph_clock),                                            \
      .rcu_periph_reset = DT_PROP(DT_INST_PARENT(index), rcu_periph_reset),                                            \
      .pcfg = PINCTRL_DT_INST_DEV_CONFIG_GET(index)};                                                                  \
                                                                                                                       \
  DEVICE_DT_INST_DEFINE(index, &qdec64k_gd32_init, NULL, &qdec64k_gd32_data_##index, &qdec64k_gd32_config_##index,     \
                        POST_KERNEL, CONFIG_KERNEL_INIT_PRIORITY_DEVICE, &qdec64k_gd32_driver_api);

DT_INST_FOREACH_STATUS_OKAY(QDEC64K_DEVICE_INIT)

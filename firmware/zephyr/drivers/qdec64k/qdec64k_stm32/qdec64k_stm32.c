#define DT_DRV_COMPAT st_stm32_qdec64k

#include <errno.h>

#include <device.h>
#include <drivers/clock_control/stm32_clock_control.h>
#include <drivers/pinctrl.h>
#include <drivers/qdec64k.h>
#include <init.h>
#include <kernel.h>
#include <soc.h>
#include <stm32_ll_tim.h>

#include <logging/log.h>
LOG_MODULE_REGISTER(qdec64k_stm32, CONFIG_QDEC64K_LOG_LEVEL);

/** Quadrature decoder data. */
struct qdec64k_stm32_data {
  uint16_t ticks;
};

/** Quadrature decoder configuration. */
struct qdec64k_stm32_config {
  /** Timer instance. */
  TIM_TypeDef *timer;
  /** Clock configuration. */
  struct stm32_pclken pclken;
  /** pinctrl configurations. */
  const struct pinctrl_dev_config *pcfg;
};

static int qdec64k_stm32_sample_fetch(const struct device *dev, enum sensor_channel chan) {
  struct qdec64k_stm32_data *data = dev->data;
  const struct qdec64k_stm32_config *cfg = dev->config;

  if ((int16_t)chan != SENSOR_CHAN_QDEC64K_TICKS && chan != SENSOR_CHAN_ALL) {
    return -ENOTSUP;
  }

  data->ticks = LL_TIM_GetCounter(cfg->timer);

  return 0;
}

static int qdec64k_stm32_channel_get(const struct device *dev, enum sensor_channel chan, struct sensor_value *val) {
  struct qdec64k_stm32_data *data = dev->data;

  if ((int16_t)chan != SENSOR_CHAN_QDEC64K_TICKS) {
    return -ENOTSUP;
  }

  val->val1 = data->ticks;

  return 0;
}

static const struct sensor_driver_api qdec64k_stm32_driver_api = {
    .sample_fetch = qdec64k_stm32_sample_fetch,
    .channel_get = qdec64k_stm32_channel_get,
};

static int qdec64k_stm32_init(const struct device *dev) {
  int ret;
  struct qdec64k_stm32_data *data = dev->data;
  const struct qdec64k_stm32_config *cfg = dev->config;

  /* initialize data */
  data->ticks = 0u;

  /* enable clock */
  const struct device *clk = DEVICE_DT_GET(STM32_CLOCK_CONTROL_NODE);

  ret = clock_control_on(clk, (clock_control_subsys_t *)&cfg->pclken);
  if (ret < 0) {
    LOG_ERR("Could not initialize clock (%d)", ret);
    return ret;
  }

  /* configure pinmux */
  ret = pinctrl_apply_state(cfg->pcfg, PINCTRL_STATE_DEFAULT);
  if (ret < 0) {
    LOG_ERR("Quadrature decoder pinctrl setup failed (%d)", ret);
    return ret;
  }

  /* initialize timer */
  LL_TIM_InitTypeDef init;

  LL_TIM_StructInit(&init);
  init.Autoreload = 65535u;

  if (LL_TIM_Init(cfg->timer, &init) != SUCCESS) {
    LOG_ERR("Could not initialize timer");
    return -EIO;
  }

  /* configure timer as encoder */
  LL_TIM_ENCODER_InitTypeDef enc_init;

  LL_TIM_ENCODER_StructInit(&enc_init);
  enc_init.EncoderMode = LL_TIM_ENCODERMODE_X4_TI12;

  if (LL_TIM_ENCODER_Init(cfg->timer, &enc_init) != SUCCESS) {
    LOG_ERR("Could not configure timer as encoder");
    return -EIO;
  }

  LL_TIM_EnableCounter(cfg->timer);

  return 0;
}

#define DT_INST_CLK(index, inst)                                                                                       \
  {                                                                                                                    \
    .bus = DT_CLOCKS_CELL(DT_PARENT(DT_DRV_INST(index)), bus),                                                         \
    .enr = DT_CLOCKS_CELL(DT_PARENT(DT_DRV_INST(index)), bits)                                                         \
  }

#define QDEC64K_DEVICE_INIT(index)                                                                                     \
  static struct qdec64k_stm32_data qdec64k_stm32_data_##index;                                                         \
                                                                                                                       \
  PINCTRL_DT_INST_DEFINE(index);                                                                                       \
                                                                                                                       \
  static const struct qdec64k_stm32_config qdec64k_stm32_config_##index = {                                            \
      .timer = (TIM_TypeDef *)DT_REG_ADDR(DT_PARENT(DT_DRV_INST(index))),                                              \
      .pclken = DT_INST_CLK(index, timer),                                                                             \
      .pcfg = PINCTRL_DT_INST_DEV_CONFIG_GET(index)};                                                                  \
                                                                                                                       \
  DEVICE_DT_INST_DEFINE(index, &qdec64k_stm32_init, NULL, &qdec64k_stm32_data_##index, &qdec64k_stm32_config_##index,  \
                        POST_KERNEL, CONFIG_KERNEL_INIT_PRIORITY_DEVICE, &qdec64k_stm32_driver_api);

DT_INST_FOREACH_STATUS_OKAY(QDEC64K_DEVICE_INIT)

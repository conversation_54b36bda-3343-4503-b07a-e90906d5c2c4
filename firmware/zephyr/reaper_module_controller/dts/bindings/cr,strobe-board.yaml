description: Strobe board interface

compatible: "cr,strobe-board"

properties:
  ready-gpios:
    type: phandle-array
    required: true
    description: Input signal used to indicate strobe board is ready to fire
  io-channels:
    required: true
    type: phandle-array
    description: ADC input channels used for voltage and current feedback
  io-channel-names:
    required: false
    type: string-array
    description: Short names for each ADC channel

menuconfig APP_UDP_PORT
    int "UDP server port"
    range 1 65535

menuconfig APP_PCSERIAL_BAUD
    int "PC serial port baud rate"
    range 300 115200
menuconfig APP_PCSERIAL_MAX_MSG
    int "PC serial maximum message size (bytes)"
    range 64 512

menuconfig APP_SENSOR_DUMP_READINGS
    bool "Write raw sensor readings to console"

# general app logs
module = APP
module-str = APP
source "subsys/logging/Kconfig.template.log_config"

# network subsystem has its own log level
module = APP_NET
module-str = APP_NET
source "subsys/logging/Kconfig.template.log_config"

source "Kconfig.zephyr"

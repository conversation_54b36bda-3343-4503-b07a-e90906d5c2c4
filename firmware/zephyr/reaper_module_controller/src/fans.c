#include <logging/log.h>
LOG_MODULE_REGISTER(fans, CONFIG_APP_LOG_LEVEL);

#include <device.h>
#include <devicetree.h>
#include <drivers/gpio.h>

#include <utils/handle_errors.h>

#include "fans.h"
#include "sensors/sensors.h"

// Number of fans in the system
#define NUM_FANS (2)
// Time interval at which the thermostat is processed
#define THERMO_TICK_INTERVAL K_MSEC(2609)

typedef struct fan_state {
  struct k_mutex lock;

  struct {
    // whether thermostat control is enabled
    bool enabled;
    // thermostat config
    fan_thermo_config_t config;

    // most recently captured temperature reading
    float currentTemp;
  } thermo;
} fan_state_t;

static int set_fan(const fan_t fan, const bool newState);
static void do_thermo_work(struct k_work *);
static void do_thermo_tick(struct k_timer *);

// GPIO lines for each of the fans
static const struct gpio_dt_spec gFanGpios[NUM_FANS] = {
    GPIO_DT_SPEC_GET(DT_PATH(fans, fan1), gpios),
    GPIO_DT_SPEC_GET(DT_PATH(fans, fan2), gpios),
};

// Global fan controller state
__dtcm_data_section static fan_state_t gState;

// Work item to sample thermostat sensor and update fan state
__dtcm_data_section static K_WORK_DEFINE(gFanThermoWork, do_thermo_work);
// Timer to periodically trigger the thermostat stuff
K_TIMER_DEFINE(gFanThermoTimer, do_thermo_tick, NULL);

/**
 * @brief Initialize fan controller
 *
 * Set up the GPIOs for the fans and ensure they're off.
 */
int fans_init() {
  k_mutex_init(&gState.lock);

  for (size_t fan = 0; fan < NUM_FANS; fan++) {
    HANDLE_UNLIKELY_BOOL(device_is_ready(gFanGpios[fan].port), ENXIO);
    HANDLE_UNLIKELY(gpio_pin_configure_dt(&gFanGpios[fan], GPIO_OUTPUT_INACTIVE));
  }

  return 0;
}

/**
 * @brief Force the state of a fan
 */
int fans_set_manual(const fan_t fan, const bool state) {
  int err;

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    if (gState.thermo.enabled) {
      err = -ENOTSUP;
    } else {
      err = set_fan(fan, state);
    }
  }
  k_mutex_unlock(&gState.lock);

  return err;
}

/**
 * @brief Get the current (actual) state of a fan
 */
int fans_get_state(const fan_t fan, bool *outState) {
  int err = gpio_pin_get_dt(&gFanGpios[fan]);
  if (err < 0) {
    return err;
  }

  *outState = !!err;
  return 0;
}

/**
 * @brief Retrieve the current thermostat state
 *
 * Query whether the thermostat is enabled, its setpoint and the most recently acquired
 * temperature.
 */
int fans_get_thermo_state(bool *outIsEnabled, fan_thermo_config_t *outCurrentConfig, float *outCurrentTemp) {
  if (!outIsEnabled && !outCurrentConfig && !outCurrentConfig) {
    return -EFAULT;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    if (outIsEnabled) {
      *outIsEnabled = gState.thermo.enabled;
    }
    if (outCurrentConfig) {
      *outCurrentConfig = gState.thermo.config;
    }
    if (outCurrentTemp) {
      *outCurrentTemp = gState.thermo.currentTemp;
    }
  }
  k_mutex_unlock(&gState.lock);

  return 0;
}

/**
 * @brief Configure automatic thermostatic fan control
 */
int fans_set_thermo_enabled(const bool isEnabled) {
  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    gState.thermo.enabled = isEnabled;
    LOG_DBG("thermostat enable = %u", isEnabled);

    if (isEnabled) {
      k_timer_start(&gFanThermoTimer, THERMO_TICK_INTERVAL, THERMO_TICK_INTERVAL);
    } else {
      k_timer_stop(&gFanThermoTimer);
      gState.thermo.currentTemp = 0.f;
    }
  }
  k_mutex_unlock(&gState.lock);

  return 0;
}

/**
 * @brief Update thermostat configuration
 */
int fans_set_thermo_config(const fan_thermo_config_t *newConfig) {
  if (!newConfig) {
    return -EFAULT;
  } else if (isnan(newConfig->hysteresis) || newConfig->setpoint < 0.f || newConfig->setpoint > 75.f) {
    return -ERANGE;
  } else if (isnan(newConfig->hysteresis) || newConfig->hysteresis < 0.f) {
    return -EDOM;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    gState.thermo.config = *newConfig;
    LOG_DBG("thermostat config: set=%d, hysteresis=%d, source=%u", (int)newConfig->setpoint, (int)newConfig->hysteresis,
            newConfig->tempSource);

    // if thermostat is enabled, trigger re-evaluation
    if (gState.thermo.enabled) {
      k_work_submit(&gFanThermoWork);
    }
  }
  k_mutex_unlock(&gState.lock);

  return 0;
}

/**
 * @brief Update physical fan state
 *
 * Set the underlying GPIO pin controlling the fan; no checks are done to see if the automatic
 * thermostat is enabled.
 */
static int set_fan(const fan_t fan, const bool newState) { return gpio_pin_set_dt(&gFanGpios[fan], newState ? 1 : 0); }

/**
 * @brief Tick callback for fan thermostat handler
 *
 * Enqueue the thermostat work item to the system work queue; this avoids overflowing the stack of
 * the timer task.
 */
static void do_thermo_tick(struct k_timer *) { k_work_submit(&gFanThermoWork); }

/**
 * @brief Process thermostat control
 *
 * Implements a simple bang-bang control loop (with configurable hysteresis) to control the fans on
 * the system.
 *
 * Currently both fans are controlled together as one.
 */
static void do_thermo_work(struct k_work *) {
  int err;
  float currentTemp;

  err = k_mutex_lock(&gState.lock, K_FOREVER);
  if (err) {
    LOG_WRN("%s failed: %d", "k_mutex_lock", err);
    return;
  } else {
    // bail if thermostat isn't enabled; then acquire temperature reading
    if (!gState.thermo.enabled) {
      goto beach;
    }

    switch (gState.thermo.config.tempSource) {
    case kThermoSourceEnviroInternal:
      err = sensors_enviro_get(kEnviroInternal, NULL, &currentTemp, NULL, NULL);
      break;
    case kThermoSourceEnviroExternal:
      err = sensors_enviro_get(kEnviroExternalPc, NULL, &currentTemp, NULL, NULL);
      break;

    default:
      LOG_WRN("unknown thermostat source (%u)", gState.thermo.config.tempSource);
      goto beach;
    }

    if (err) {
      LOG_WRN("failed to get current temp: %d", err);
      goto beach;
    }

    // implement control loop
    bool currentState;
    const float onThreshold = gState.thermo.config.setpoint + gState.thermo.config.hysteresis,
                offThreshold = gState.thermo.config.setpoint - gState.thermo.config.hysteresis;

    err = fans_get_state(kFan1, &currentState);
    if (err) {
      LOG_WRN("%s failed: %d", "fan_get_state", err);
      goto beach;
    }

    if (!currentState && (currentTemp >= onThreshold)) {
      LOG_DBG("turning %s fans (set %d, actual %d)", "on", (int)gState.thermo.config.setpoint, (int)currentTemp);

      set_fan(kFan1, true);
      set_fan(kFan2, true);
    } else if (currentState && (currentTemp <= offThreshold)) {
      LOG_DBG("turning %s fans (set %d, actual %d)", "off", (int)gState.thermo.config.setpoint, (int)currentTemp);

      set_fan(kFan1, false);
      set_fan(kFan2, false);
    }

    // update state
    gState.thermo.currentTemp = currentTemp;
  }
beach:;
  k_mutex_unlock(&gState.lock);
}

#include <logging/log.h>
LOG_MODULE_REGISTER(scanners, CONFIG_APP_LOG_LEVEL);

#include <device.h>
#include <devicetree.h>
#include <drivers/gpio.h>
#include <lib/ptp/ptp.h>

#include <utils/handle_errors.h>

#include "module_id.h"
#include "scanners.h"
#include "sensors/isense.h"

/**
 * @brief Fuse detection debounce interval
 *
 * This is the delay between an edge on the fuse detect lines for a scanner, and us polling the
 * state of the pins. Effectively this does some debouncing by not sampling it immediately while
 * the polyfuse finishes tripping.
 */
#define FUSE_GPIO_WORK_DELAY K_MSEC(15)

typedef struct scanner_manager_state {
  // protect against concurrent access for state
  struct k_mutex lock;

  // per scanner state
  struct {
    // is the external power switch on?
    bool enabled;

    // is the fuse/overcurrent protection tripped? (user visible flag)
    bool ocp;
    // most recently sampled OCP line state; if different than physical state, handle it
    bool ocpShadow;
    // timestamp of last OCP status change
    struct net_ptp_time ocpTimestamp;
  } state[NUM_SCANNERS];

  // fuse blown GPIO callbacks
  struct gpio_callback ocpGpioCallbacks[NUM_SCANNERS];

  // Whether we're on a HW revision that needs A/B channels flipped
  bool reverseChannels;
  // Whether hardware "fuse detect" signals are present and valid
  bool hasFuseDetect;
} scanner_manager_state_t;

static void apply_power_state();
static void fuse_gpio_callback(const struct device *, struct gpio_callback *, gpio_port_pins_t);
static void fuse_work(struct k_work *);

/// Internal state of scanner manager
__dtcm_data_section static scanner_manager_state_t gState;
/// Work item to process fuse state changes
K_WORK_DELAYABLE_DEFINE(gFuseGpioWork, fuse_work);

// IO lines for scanner power control
static const struct gpio_dt_spec gPowerGpios[NUM_SCANNERS] = {
    GPIO_DT_SPEC_GET_OR(DT_PATH(scanners, a), enable_gpios, {NULL}),
    GPIO_DT_SPEC_GET_OR(DT_PATH(scanners, b), enable_gpios, {NULL}),
};
// Input lines for fuse blown detection: high if fuse blew
static const struct gpio_dt_spec gFuseDetectGpios[NUM_SCANNERS] = {
    GPIO_DT_SPEC_GET_OR(DT_PATH(scanners, a), fuse_gpios, {NULL}),
    GPIO_DT_SPEC_GET_OR(DT_PATH(scanners, b), fuse_gpios, {NULL}),
};

/**
 * @brief Initialize scanner hardware
 *
 * Set up IOs for controlling power and fuse supervision.
 */
int scanners_init() {
  // set up kernel objects and default state
  k_mutex_init(&gState.lock);

  // figure out hw revision (in case IOs need swapping)
  uint32_t hwRev = 0;
  HANDLE_UNLIKELY(module_hw_rev_get(&hwRev));

  gState.reverseChannels = (hwRev == 1);
  gState.hasFuseDetect = (hwRev >= 2);
  LOG_DBG("scanner power is%s reversed (HW rev %u)", gState.reverseChannels ? "" : " not", hwRev);

#if HAS_SCANNER_CONTROL
  // set up GPIOs
  for (size_t i = 0; i < NUM_SCANNERS; i++) {
    // power enable
    HANDLE_UNLIKELY_BOOL(device_is_ready(gPowerGpios[i].port), ENXIO);
    HANDLE_UNLIKELY(gpio_pin_configure_dt(&gPowerGpios[i], GPIO_OUTPUT_INACTIVE));

    // fuse blow detect
    if (gState.hasFuseDetect && gFuseDetectGpios[i].port) {
      HANDLE_UNLIKELY_BOOL(device_is_ready(gFuseDetectGpios[i].port), ENXIO);
      HANDLE_UNLIKELY(gpio_pin_configure_dt(&gFuseDetectGpios[i], GPIO_INPUT));

      HANDLE_UNLIKELY(gpio_pin_interrupt_configure_dt(&gFuseDetectGpios[i], GPIO_INT_EDGE_BOTH));
      gpio_init_callback(&gState.ocpGpioCallbacks[i], fuse_gpio_callback, BIT(gFuseDetectGpios[i].pin));
      gpio_add_callback(gFuseDetectGpios[i].port, &gState.ocpGpioCallbacks[i]);
    } else {
      LOG_WRN("no fuse blown detection for scanner %c", ('A' + i));
    }
  }

  // read out the initial state of the "fuse blown" lines (should be deasserted)
  if (gState.hasFuseDetect) {
    k_work_schedule(&gFuseGpioWork, K_NO_WAIT);
  }
#endif

  return 0;
}

/**
 * @brief Enable and/or disable scanners
 *
 * @param enable Scanner(s) to enable power to
 * @param disable Scanner(s) to disable power to
 *
 * @remark It is illegal to specify the same scanner in both parameters.
 */
int scanners_set_power(const scanner_t enable, const scanner_t disable) {
  if (enable & disable) {
    return -EINVAL;
  }

#if !HAS_SCANNER_CONTROL
  return -ENOTSUP;
#endif

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    for (size_t i = 0; i < NUM_SCANNERS; i++) {
      const scanner_t scanner = scanners_index_to_enum(i);

      if (enable & scanner) {
        gState.state[i].enabled = true;
      } else if (disable & scanner) {
        gState.state[i].enabled = false;
      }
    }

    apply_power_state();
  }
  k_mutex_unlock(&gState.lock);

  return 0;
}

/**
 * @brief Get the current power state of all scanners
 *
 * This only indicates whether the power switch is enabled. If the scanner tripped overcurrent
 * protection this will still read as `true`
 *
 * @param outEnabled Bitfield set to the logical OR of all powered on scanners
 */
int scanners_get_power(scanner_t *outEnabled) {
  if (!outEnabled) {
    return -EFAULT;
  }

  scanner_t out = 0;

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    for (size_t i = 0; i < NUM_SCANNERS; i++) {
      if (gState.state[i].enabled) {
        out |= scanners_index_to_enum(i);
      }
    }
  }
  k_mutex_unlock(&gState.lock);

  *outEnabled = out;
  return 0;
}

/**
 * @brief Retrieve detailed status information for a single scanner
 *
 * Get the power switch state, as well as the current consumption and overcurrent protection state.
 *
 * @param which Scanner to get info for; cannot be a logical OR
 */
int scanners_get_state(const scanner_t which, scanner_state_t *outState) {
  int err;

  if (!which || __builtin_popcount(which) > 1) {
    return -EINVAL;
  } else if (!outState) {
    return -EFAULT;
  }

  const size_t idx = __builtin_ctz(which);

  scanner_state_t state;
  memset(&state, 0, sizeof(state));

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    state.power = gState.state[idx].enabled;

    state.ocp.triggered = gState.state[idx].ocp;
    state.ocp.timestamp = ptp_ts_net_to_ms(&gState.state[idx].ocpTimestamp);
  }
  k_mutex_unlock(&gState.lock);

  // query isense handler for scanner current consumption
  static const isense_channel_t channel[] = {kIsenseScannerA, kIsenseScannerB};

  err = isense_get(channel[idx], &state.isense.timestamp, &state.isense.current, NULL);
  if (err) {
    LOG_WRN("failed to get ISense for scanner %c: %d", ('A' + idx), err);

    state.isense.timestamp = 0;
    state.isense.current = NAN;
  }

  *outState = state;

  return 0;
}

/**
 * @brief Reset the overcurrent flag for a given scanner
 *
 * When the physical polyfuse triggers and enters a high impedance state (due to overcurrent) the
 * fuse blown line is asserted. While the fuse itself recovers automatically and the flow of
 * current will resume automatically, the overcurrent detect flag is latching and must be
 * reset via this call.
 *
 * This way client code will not miss an overcurrent event if it takes place between polling
 * intervals.
 *
 * @param which Scanner to reset OCP flag for; cannot be a logical OR
 */
int scanners_reset_ocp(const scanner_t which) {
  if (!which || __builtin_popcount(which) > 1) {
    return -EINVAL;
  }

#if !HAS_SCANNER_CONTROL
  return -ENOTSUP;
#endif

  const size_t idx = __builtin_ctz(which);

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    gState.state[idx].ocp = false;
    HANDLE_UNLIKELY(ptp_slave_clk_get(&gState.state[idx].ocpTimestamp));
  }
  k_mutex_unlock(&gState.lock);

  return 0;
}

/**
 * @brief Fill nanopb structure for scanner status
 */
int scanners_nanopb_fill_status(reaper_module_controller_ScannerStatus *data, const size_t numEntries) {
  if (!data) {
    return -EFAULT;
  } else if (!numEntries) {
    return -EINVAL;
  }

  for (size_t i = 0; i < MIN(NUM_SCANNERS, numEntries); i++) {
    scanner_state_t scannerState;
    const scanner_t which = scanners_index_to_enum(i);
    HANDLE_UNLIKELY(scanners_get_state(which, &scannerState));

    reaper_module_controller_ScannerStatus *status = &data[i];
    status->powerEnabled = scannerState.power;

    status->fuseBlown = scannerState.ocp.triggered;
    status->fuseTimestamp = scannerState.ocp.timestamp;

    status->current = scannerState.isense.current;
    status->currentTimestamp = scannerState.isense.timestamp;
  }

  return 0;
}

/**
 * @brief Apply physical scanner power state
 *
 * Update the state of the scanner power switches to match the logical (software) state.
 *
 * @remark Caller must hold the state lock
 */
static void apply_power_state() {
#if HAS_SCANNER_CONTROL
  for (size_t i = 0; i < NUM_SCANNERS; i++) {
    // flip A/B on rev1 board
    const struct gpio_dt_spec *gpio;

    if (gState.reverseChannels) {
      gpio = &gPowerGpios[(NUM_SCANNERS - 1) - i];
    } else {
      gpio = &gPowerGpios[i];
    }

    // set the GPIO
    LOG_DBG("set pwr[%c] = %s", ('A' + i), gState.state[i].enabled ? "on" : "off");
    HANDLE_CRITICAL(gpio_pin_set_dt(gpio, gState.state[i].enabled ? 1 : 0));
  }
#endif
}

/**
 * @brief Fuse blown detection GPIO callback
 *
 * Called on either edge of the fuse detect pin changing; triggers the work item to actually read
 * out and handle the state change. We cannot take the state lock from an ISR.
 */
static void fuse_gpio_callback(const struct device *port, struct gpio_callback *, gpio_port_pins_t pins) {
  k_work_schedule(&gFuseGpioWork, FUSE_GPIO_WORK_DELAY);
}

/**
 * @brief Update the state of the overcurrent detect lines
 *
 * Sample the state of each of the scanners' fuse blown lines: if either of them has changed state
 * from previous, record the state change.
 *
 * The overcurrent flag is sticky, e.g. the flag visible externally is never cleared even if the
 * fuse restores itself. However, the internal shadow flag used to track changes will be updated
 * in this case and log messages are emitted regardless.
 */
static void fuse_work(struct k_work *) {
  struct net_ptp_time now;
  int state;

  // acquire current timestamp
  ptp_slave_clk_get(&now);

  for (size_t i = 0; i < NUM_SCANNERS; i++) {
    if (!gFuseDetectGpios[i].port) {
      continue;
    }

    // read out the current pin state
    state = gpio_pin_get_dt(&gFuseDetectGpios[i]);
    if (state < 0) {
      LOG_WRN("%s failed: %d", "gpio_pin_set_dt", state);
      continue;
    }

    // store it if changed
    const bool oldState = gState.state[i].ocpShadow;

    if (oldState != state) {
      gState.state[i].ocpShadow = state;

      if (state) {
        LOG_ERR("scanner %c fuse tripped", ('A' + i));
        gState.state[i].ocp = true;
        gState.state[i].ocpTimestamp = now;
      } else {
        LOG_WRN("scanner %c fuse reset", ('A' + i));
      }
    }
  }
}

#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(watchdog, CONFIG_APP_LOG_LEVEL);

#include <stdbool.h>
#include <stdio.h>
#include <string.h>

#include <zephyr/device.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/drivers/hwinfo.h>
#include <zephyr/drivers/watchdog.h>
#include <zephyr/sys/atomic.h>

#include <img_mgmt/img_mgmt.h>
#include <lib/status_led/status_led.h>
#include <utils/handle_errors.h>

#include "watchdog.h"

// Enable internal watchdog
#define USE_INTERNAL_WDG (1)
// Software watchdog time interval (msec)
#define INT_WDG_TIMEOUT_MSEC (20000)

/**
 * @brief Force using external watchdog, even with debugger attached
 *
 * Use this to test the external watchdog while debugging on the bench; note that this will cause
 * board reset if you break into the debugger!
 */
#define FORCE_EXT_WDG_WHEN_DEBUGGING (0)

/**
 * @brief How often watchdog supervisory loop runs
 *
 * This MUST be lower than both the internal (INT_WDG_TIMEOUT_MSEC) and external watchdog
 * timeout (~1 sec)
 */
#define WDG_LOOP_TIMEOUT_MSEC (500)

/**
 * @brief How often to pet external watchdog, if in network stack
 */
#define WDG_NETWORK_RX_PET_INTERVAL_MSEC (WDG_LOOP_TIMEOUT_MSEC / 2)

#if USE_INTERNAL_WDG
static int int_wdg_init();
static void int_wdg_pet();
#endif
static int ext_wdg_init();
static void ext_wdg_set_enabled(const bool isEnabled);
static void ext_wdg_pet();

static void watchdog_worker();

static void mcumgr_dfu_start();
static void mcumgr_dfu_stopped();
static void mcumgr_dfu_pending();
static void inhibit_watchdog();
static void restore_watchdog(const char *cause);

static void pet_from_eth_rx_workaround(const bool l2);

// mcumgr image management callbacks
// TODO: ensure nothing else uses these?
static const struct img_mgmt_dfu_callbacks_t gImgCallbacks = {
    mcumgr_dfu_start,
    mcumgr_dfu_stopped,
    mcumgr_dfu_pending,
    NULL,
};

#if USE_INTERNAL_WDG
// Internal watchdog channel
__dtcm_bss_section static int gIntWdtChannel;
// Internal hardware watchdog device node
static const struct device *gIntWatchdog = DEVICE_DT_GET(DT_ALIAS(watchdog0));
#endif

// bitmap set by each watchdog worker task as it starts
__dtcm_data_section static atomic_t gStartFlag = ATOMIC_INIT(0);

// Timestamp (in system uptime counter) of last time the external watchdog was petted
__dtcm_bss_section static int64_t gExtWdgLastPet = 0;
// Number of times the external watchdog has been petted from the workaround
__dtcm_bss_section static atomic_t gExtWdgWorkaroundPets = ATOMIC_INIT(0);

// Set when the external watchdog is started
__dtcm_bss_section static bool gExtWdgActive = false;
// IO line for enabling the watchdog
static const struct gpio_dt_spec gWatchdogEnableGpio = GPIO_DT_SPEC_GET(DT_PATH(ext_watchdog), enable_gpios);
// IO line for servicing the watchdog
static const struct gpio_dt_spec gWatchdogPetGpio = GPIO_DT_SPEC_GET(DT_PATH(ext_watchdog), pet_gpios);

static K_THREAD_DEFINE(watchdog_thread_id, 1024, watchdog_worker, NULL, NULL, NULL, K_LOWEST_APPLICATION_THREAD_PRIO, 0,
                       K_TICKS_FOREVER);

/**
 * @brief Initialize the watchdog manager
 *
 * Set up GPIOs to interface with the external watchdog and also set up the internal watchdog to
 * periodically service it.
 */
int watchdog_init() {
  // register mcumgr callbacks - this allows us to inhibit external wdg during updates
  img_mgmt_register_callbacks(&gImgCallbacks);

  // start servicing task
  k_thread_name_set(watchdog_thread_id, "watchdawg");
  k_thread_start(watchdog_thread_id);

#if USE_INTERNAL_WDG
  // set up the internal watchdog: this will reset us if boot fails
  HANDLE_UNLIKELY(int_wdg_init());
#endif

  // set up the external watchdog but have it disabled until boot completes
  HANDLE_UNLIKELY(ext_wdg_init());
  ext_wdg_set_enabled(false);

  // service both watchdogs
#if USE_INTERNAL_WDG
  int_wdg_pet();
#endif
  ext_wdg_pet();

  return 0;
}

/**
 * @brief Arm the watchdog servicer
 *
 * This enables the external hardware watchdog with its 1 second timeout. The servicing task
 * must be running at this point and no more software checkin functions can be registered. Call
 * whenever boot completes.
 *
 * @remark If a debugger is attached, external watchdog is not armed
 */
int watchdog_arm() {
  uint32_t resetCause = 0;

  LOG_INF("arming external wdg");

  // get reset cause
  HANDLE_UNLIKELY(hwinfo_get_reset_cause(&resetCause));
  HANDLE_UNLIKELY(hwinfo_clear_reset_cause());

  char causeStr[128];
  {
    struct entry {
      // AND with reset cause, if nonzero, add to list
      uint32_t mask;
      // display name for this reset cause
      const char *name;
    };
    static const struct entry kCauseNames[] = {
        {RESET_SOFTWARE, "Software reset"},
        {RESET_PIN, "HW reset (NRST; likely external wdg)"},
        {RESET_WATCHDOG, "Watchdog (internal)"},
        {RESET_SECURITY, "Security fault"},
        {RESET_BROWNOUT, "Brownout"},
        {RESET_POR, "Power on reset"},
        {RESET_LOW_POWER_WAKE, "Wake from low power"},
    };

    int len;
    // reserve one byte for terminator
    size_t writeLen = sizeof(causeStr) - 1;

    memset(causeStr, 0, sizeof(causeStr));
    char *writePtr = causeStr;

    for (size_t i = 0; i < ARRAY_SIZE(kCauseNames); i++) {
      const struct entry *ent = &kCauseNames[i];

      if (ent->mask & resetCause) {
        len = snprintf(writePtr, writeLen, "%s, ", ent->name);
        writePtr += MIN(len, writeLen);
        writeLen -= len;
      }
    }

    // remove ", " from last element
    if (writePtr != causeStr) {
      writePtr[-2] = '\0';
    }
  }

  LOG_WRN("Reset reason: %08x (%s)", resetCause, causeStr);

  // ensure servicing task started
  while (!atomic_test_bit(&gStartFlag, 0)) {
    // TODO: add timeout or something
    k_sleep(K_MSEC(5));
  }

  // do not arm external wdg if debugger attached
  ext_wdg_pet();

  if (CoreDebug->DHCSR & CoreDebug_DHCSR_C_DEBUGEN_Msk && !FORCE_EXT_WDG_WHEN_DEBUGGING) {
    LOG_WRN("not arming external wdg: debugger attached");
  } else {
    ext_wdg_set_enabled(true);
    gExtWdgActive = true;

    LOG_INF("external wdg armed");
  }

  return 0;
}

#if USE_INTERNAL_WDG
/**
 * @brief Set up the internal watchdog
 */
static int int_wdg_init() {
  HANDLE_UNLIKELY_BOOL(device_is_ready(gIntWatchdog), ENODEV);

  // configure timeout
  struct wdt_timeout_cfg config = {
      // reset on timeout
      .flags = WDT_FLAG_RESET_SOC,

      // expire after timeout (in msec)
      .window.min = 0U,
      .window.max = INT_WDG_TIMEOUT_MSEC,
  };

  gIntWdtChannel = wdt_install_timeout(gIntWatchdog, &config);
  HANDLE_UNLIKELY(gIntWdtChannel);

  // pause during debugging
  HANDLE_UNLIKELY(wdt_setup(gIntWatchdog, WDT_OPT_PAUSE_HALTED_BY_DBG));

  return 0;
}

/**
 * @brief Service internal watchdog
 */
static void int_wdg_pet() { wdt_feed(gIntWatchdog, gIntWdtChannel); }
#endif

/**
 * @brief Set up the external watchdog
 *
 * This sets up the GPIOs and pulses the servicing line to reset its timeout; it will also be
 * disabled.
 */
static int ext_wdg_init() {
  HANDLE_UNLIKELY(!device_is_ready(gWatchdogEnableGpio.port));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&gWatchdogEnableGpio, GPIO_OUTPUT_INACTIVE));

  HANDLE_UNLIKELY(!device_is_ready(gWatchdogPetGpio.port));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&gWatchdogPetGpio, GPIO_OUTPUT_INACTIVE));

  ext_wdg_pet();

  return 0;
}

/**
 * @brief Enable or disable the hardware watchdog
 */
static void ext_wdg_set_enabled(const bool isEnabled) { gpio_pin_set_dt(&gWatchdogEnableGpio, isEnabled ? 1 : 0); }

/**
 * @brief Pet the external watchdog
 *
 * Pulses the watchdog line for 2µS; datasheet requires 1µS minimum.
 */
static void ext_wdg_pet() {
  unsigned int irqKey = irq_lock();
  {
    gpio_pin_set_dt(&gWatchdogPetGpio, 1);
    k_busy_wait(2);
    gpio_pin_set_dt(&gWatchdogPetGpio, 0);

    gExtWdgLastPet = k_uptime_get();
  }
  irq_unlock(irqKey);
}

/**
 * @brief Software watchdog worker
 *
 * This dude checks that all software tasks are alive at double the rate of the internal watchdog,
 * petting every time if things are ok.
 */
static void watchdog_worker() {
  atomic_set_bit(&gStartFlag, 0);

  size_t led = 0;
  do {
    // TODO: check in that things are ok

    /*
     * Check how much the network stack has starved us of processing time; one iteration is ok
     * (as the network stack servicing interval is half of what we use here; so that accounts for
     * situation where a network packet is received when it's been at 250 ≤ x < 500 msec since the
     * last petting) but any more indicate a problem.
     */
    const atomic_t rxStackPets = atomic_set(&gExtWdgWorkaroundPets, 0);
    if (rxStackPets >= 2) {
      LOG_WRN("Stuck in network stack for %lu pets!", rxStackPets);
    }

    // service watchdog(s)
    // TODO: external watchdog should be serviced by another task
#if USE_INTERNAL_WDG
    int_wdg_pet();
#endif
    ext_wdg_pet();

    // toggle the debug LED (every 4 rounds through here) and await next time
    leds_set_debug(!(led++ & 0b100));

    k_sleep(K_MSEC(WDG_LOOP_TIMEOUT_MSEC / 2));
  } while (true);
}

/**
 * @brief mcumgr firmware upload start callback
 *
 * Disable the external watchdog during the firmware update. This is because writing to the
 * flash may cause the application to "freeze" for some time.
 */
static void mcumgr_dfu_start() {
  LOG_INF("Starting DFU, disabling wdg!");

  inhibit_watchdog();
}

/**
 * @brief mcumgr firmware upload complete callback (failure)
 *
 * Re-enable the watchdogs.
 */
static void mcumgr_dfu_stopped() {
  LOG_WRN("firmware upload failed or aborted!");

  restore_watchdog("DFU complete");
}

/**
 * @brief mcumgr firmware upload complete callback (success)
 *
 * Re-enable the watchdogs.
 */
static void mcumgr_dfu_pending() {
  LOG_INF("firmware upload success (pending = %u)", img_mgmt_state_any_pending());

  restore_watchdog("DFU complete");
}

/**
 * @brief Inhibit the external watchdog
 *
 * Disable the external watchdog.
 */
static void inhibit_watchdog() {
#if USE_INTERNAL_WDG
  int_wdg_pet();
#endif
  ext_wdg_pet();

  ext_wdg_set_enabled(false);

  // TODO: start a software timer (for some amount of time) after which it's forcibly re-enabled
}

/**
 * @brief restore watchdog state post update
 */
static void restore_watchdog(const char *reason) {
  LOG_INF("%s; restoring wdg state = %u", reason, gExtWdgActive);

  if (gExtWdgActive) {
    ext_wdg_pet();
    ext_wdg_set_enabled(true);
  }
}

/**
 * @brief EEPROM write callback
 *
 * Invoked immediately before an EEPROM write operation. Since the EEPROM is emulated through
 * flash, writes to it could cause system stalls. Therefore we need to pet the watchdog right
 * before doing the write.
 *
 * @param writeEnable Whether writing is enabled or not; a call with true should be followed with
 *                    a call with false
 */
void watchdog_eeprom_write_callback(const bool writeEnable) {
  // about to write to flash
  if (writeEnable) {
    LOG_INF("start EEPROM write; inhibiting wdg!");
    inhibit_watchdog();
  }
  // done writing to flash
  else {
    restore_watchdog("EEPROM write complete");
  }
}

/**
 * @brief Invoked periodically from the coredump backend
 *
 * The system is in a mostly panic-ed state when this is called; tasks (such as the watchdog
 * servicer) are not running anymore. This thing will service the watchdogs manually.
 *
 * Also handles blinking all status LEDs like crazy (debug LED fast blink, RGB status blink
 * between orange and white)
 */
void watchdog_coredump_callback() {
  int_wdg_pet();

  if (gExtWdgActive) {
    ext_wdg_pet();
  }

  // LED blinking to identify this state visually
  __dtcm_bss_section static size_t gLedState = 0;
  leds_set_debug(!(gLedState & 0b1));
  leds_set_status(!(gLedState & 0b1) ? 0xFF4000 : 0x000000);

  gLedState++;
}

/**
 * @brief Callback for watchdog petting from STM32 receive thread
 */
void __carbon_eth_rx_callback_l2() { pet_from_eth_rx_workaround(true); }

/**
 * @brief Callback for watchdog petting from Zephyr IP receive thread
 */
void __carbon_eth_rx_callback_l3() { pet_from_eth_rx_workaround(false); }

/**
 * @brief Watchdog petting helper from receive thread
 *
 * Possibly pet the external watchdog if it's been too long. This guards against the case where we
 * are being bombarded with packets and the watchdog task is starved.
 *
 * This deliberately does not pet the internal watchdog; since it has a significantly longer
 * timeout than the external watchdog, but is still serviced at the same rate as the external
 * watchdog we can rely on always having all but ~1 sec of the timeout still available.
 */
static void pet_from_eth_rx_workaround(const bool l2) {
  if (!gExtWdgActive) {
    return;
  }

  const int64_t now = k_uptime_get();
  const int64_t delta = now - gExtWdgLastPet;

  if (delta >= WDG_NETWORK_RX_PET_INTERVAL_MSEC) {
    ext_wdg_pet();
    atomic_inc(&gExtWdgWorkaroundPets);
  }
}

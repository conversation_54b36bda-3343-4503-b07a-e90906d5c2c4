/**
 * @file
 *
 * @brief EEPROM emulation
 *
 * Provides a high level interface to store module information in a virtual EEPROM. This is
 * emulated through a page in the internal flash, and a block based interface (where blocks are
 * looked up by a logical type, translating to a physical address) that allows callers to
 * arbitrarily access those blocks.
 */
#pragma once

#include <stddef.h>
#include <stdint.h>

/**
 * @brief Block identifiers/types
 *
 * These serve as a sort of filename for the underlying block data.
 */
typedef enum eeprom_block_type {
  // available to be written/empty: any block without data/invalid header is this type
  kEepromBlockTypeEmpty = 0x00,
  // 0x01 was network configuration, now reserved
  // Module identity information
  kEepromBlockTypeModuleId = 0x02,
  // Board identity information
  kEepromBlockTypeBoardId = 0x03,
  // Strobe configuration
  kEepromBlockTypeStrobeConfig = 0x04,
} eeprom_block_type_t;

int eeprom_init();
int eeprom_read_block(eeprom_block_type_t type, void *outBuffer, size_t *outBufferSize);
int eeprom_write_block(eeprom_block_type_t type, const void *payload, const size_t payloadSize);
int eeprom_erase_block(eeprom_block_type_t type);
int eeprom_erase_all();

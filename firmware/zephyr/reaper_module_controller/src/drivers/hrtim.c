#define DT_DRV_COMPAT cr_hrtim

#include <logging/log.h>
LOG_MODULE_REGISTER(hrtim, LOG_LEVEL_INF);

#include <device.h>
#include <devicetree.h>
#include <drivers/clock_control.h>
#include <drivers/clock_control/stm32_clock_control.h>
#include <drivers/pinctrl.h>
#include <soc.h>

#include <utils/handle_errors.h>

#include "hrtim.h"

// Set to nonzero to use CPU system clock; otherwise use APB2 timer clocks (e.g. APB2 x 2)
#define HRTIM_CLOCK_USE_CPU (0)

// Number of timer output channels (dedicated: this is A..E)
#define NUM_CHANNELS (5)
// Number of outputs per channel
#define NUM_OUTPUTS_PER_CHANNEL (2)
// Maximum value for timer channel period count
#define MAX_PERIOD (0xFFFC)

// device config (from device tree)
typedef struct hrtim_config {
  // pinmux settings (to be applied during init)
  const struct pinctrl_dev_config *pcfg;
} hrtim_config_t;

// Runtime data for HRTIM
typedef struct hrtim_data {
  // TODO: put stuff here
  int dummy;
} hrtim_data_t;

// Mapping of 0 based index to register offset
static const uint32_t kRegOffset[NUM_CHANNELS] = {
    HRTIM_TIMERINDEX_TIMER_A, HRTIM_TIMERINDEX_TIMER_B, HRTIM_TIMERINDEX_TIMER_C,
    HRTIM_TIMERINDEX_TIMER_D, HRTIM_TIMERINDEX_TIMER_E,
};

static bool channel_is_valid(const hrtim_channel_t);
static size_t channel_to_index(const hrtim_channel_t);
static uint32_t get_timebase_freq(const struct device *dev, const hrtim_channel_t channel,
                                  const hrtim_prescaler_t prescaler);
static int calc_count(const struct device *hrtim, const hrtim_channel_t channel, const uint32_t nanos,
                      uint32_t *outPeriod);

static int hrtim_init(const struct device *dev);
static int set_timebase_enable(const struct device *, const hrtim_channel_t channels, const bool started);
static int set_timebase_mode(const struct device *, const hrtim_channel_t channel, const hrtim_counter_mode_t newMode);
static int reset_counter(const struct device *, const hrtim_channel_t channels);
static int update_counter(const struct device *, const hrtim_channel_t channels);
static int set_reset_sources(const struct device *, const hrtim_channel_t channel,
                             const hrtim_reset_sources_t *sources);
static int set_output_enable(const struct device *, const hrtim_channel_t channel, const size_t output,
                             const bool enabled);
static int set_output_polarity(const struct device *, const hrtim_channel_t channel, const size_t output,
                               const bool inverted);
static int set_output_idle_state(const struct device *, const hrtim_channel_t channel, const size_t output,
                                 const bool isActive);
static int set_period(const struct device *, const hrtim_channel_t channel, const uint32_t nanos);
static int get_period(const struct device *, const hrtim_channel_t channel, uint32_t *outNanos, float *outFreq);
static int set_waveform(const struct device *hrtim, const hrtim_channel_t channel, const hrtim_waveform_point_t *points,
                        const size_t numPoints);

static void set_waveform_flags(const struct device *hrtim, const hrtim_channel_t channel,
                               const hrtim_waveform_point_t *points, const size_t bitIndex);
static int get_prescaler(const struct device *, const hrtim_channel_t channel, hrtim_prescaler_t *outPrescaler);
static int set_prescaler(const struct device *, const hrtim_channel_t channel, const hrtim_prescaler_t newPrescaler);
static bool is_timebase_stopped(const struct device *, const hrtim_channel_t channel);

/**
 * @brief Start the high resolution timer instance
 *
 * Set up the GPIOs (defined in the `pinctrl` node) for the timer as well as any required clocks
 * and other resources.
 */
static int hrtim_init(const struct device *dev) {
  int err;

  struct hrtim_config *cfg = (struct hrtim_config *)dev->config;
  struct hrtim_data *data = (struct hrtim_data *)dev->data;

  // initialize internal state
  memset(data, 0, sizeof(*data));

  // enable clocks to HRTIM, then reset its internal registers
#if HRTIM_CLOCK_USE_CPU
  __HAL_RCC_HRTIM1_CONFIG(RCC_HRTIM1CLK_CPUCLK);
#else
  __HAL_RCC_HRTIM1_CONFIG(RCC_HRTIM1CLK_TIMCLK);
#endif
  __HRTIM1_CLK_ENABLE();

  __HAL_RCC_HRTIM1_FORCE_RESET();
  __DSB();
  __HAL_RCC_HRTIM1_RELEASE_RESET();
  __DSB();

  // set up pinmux
  err = pinctrl_apply_state(cfg->pcfg, PINCTRL_STATE_DEFAULT);
  if (err < 0) {
    LOG_ERR("%s failed: %d", "pinctrl_apply_state", err);
    return err;
  }

  return 0;
}

/**
 * @brief Start or stop channel timebase
 *
 * This controls the actual counter driving the channel's output. When disabled, the counter will
 * stay constant and the output will remain in whatever the most recent state was.
 *
 * @remark There are several cycles delay due to hardware synchronization when changing timebase
 */
static int set_timebase_enable(const struct device *dev, const hrtim_channel_t channel, const bool started) {
  if (!dev) {
    return -EFAULT;
  } else if (!channel) {
    return 0;
  }

  uint32_t bits = 0;

  if (channel & kHrtimChannelA) {
    bits |= HRTIM_MCR_TACEN;
  }
  if (channel & kHrtimChannelB) {
    bits |= HRTIM_MCR_TBCEN;
  }
  if (channel & kHrtimChannelC) {
    bits |= HRTIM_MCR_TCCEN;
  }
  if (channel & kHrtimChannelD) {
    bits |= HRTIM_MCR_TDCEN;
  }
  if (channel & kHrtimChannelE) {
    bits |= HRTIM_MCR_TECEN;
  }
  if (channel & kHrtimMaster) {
    bits |= HRTIM_MCR_MCEN;
  }

  if (started) {
    HRTIM1->sMasterRegs.MCR |= bits;
  } else {
    HRTIM1->sMasterRegs.MCR &= ~bits;
  }

  return 0;
}

/**
 * @brief Set the counter mode for a given channel
 */
static int set_timebase_mode(const struct device *hrtim, const hrtim_channel_t channel,
                             const hrtim_counter_mode_t newMode) {
  if (!hrtim) {
    return -EFAULT;
  } else if (!channel_is_valid(channel)) {
    return -EINVAL;
  } else if (!is_timebase_stopped(hrtim, channel)) {
    return -EBUSY;
  }

  size_t regOffset;
  uint32_t cr;

  if (channel == kHrtimMaster) {
    cr = HRTIM1->sMasterRegs.MCR;
  } else {
    regOffset = kRegOffset[channel_to_index(channel)];
    cr = HRTIM1->sTimerxRegs[regOffset].TIMxCR;
  }

  switch (newMode) {
  case kHrtimCounterModeContinuous:
    cr |= HRTIM_TIMCR_CONT;
    cr &= ~HRTIM_TIMCR_RETRIG;
    break;
  case kHrtimCounterModeSingleShot:
    cr &= ~(HRTIM_TIMCR_CONT | HRTIM_TIMCR_RETRIG);
    break;
  case kHrtimCounterModeSingleShotResettable:
    cr &= ~HRTIM_TIMCR_CONT;
    cr |= HRTIM_TIMCR_RETRIG;
    break;

  default:
    return -EINVAL;
  }

  if (channel == kHrtimMaster) {
    HRTIM1->sMasterRegs.MCR = cr;
  } else {
    HRTIM1->sTimerxRegs[regOffset].TIMxCR = cr;
  }

  return 0;
}

/**
 * @brief Software reset the specified counter
 */
static int reset_counter(const struct device *hrtim, const hrtim_channel_t channels) {
  if (!hrtim) {
    return -EFAULT;
  } else if (!channels) {
    return -EINVAL;
  }

  uint32_t bits = 0;

  if (channels & kHrtimChannelA) {
    bits |= HRTIM_CR2_TARST;
  }
  if (channels & kHrtimChannelB) {
    bits |= HRTIM_CR2_TBRST;
  }
  if (channels & kHrtimChannelC) {
    bits |= HRTIM_CR2_TCRST;
  }
  if (channels & kHrtimChannelD) {
    bits |= HRTIM_CR2_TDRST;
  }
  if (channels & kHrtimChannelE) {
    bits |= HRTIM_CR2_TERST;
  }
  if (channels & kHrtimMaster) {
    bits |= HRTIM_CR2_MRST;
  }

  HRTIM1->sCommonRegs.CR2 |= bits;

  return 0;
}

/**
 * @brief Force shadow register update on the specified counter
 */
static int update_counter(const struct device *hrtim, const hrtim_channel_t channels) {
  if (!hrtim) {
    return -EFAULT;
  } else if (!channels) {
    return -EINVAL;
  }

  uint32_t bits = 0;

  if (channels & kHrtimChannelA) {
    bits |= HRTIM_CR2_TASWU;
  }
  if (channels & kHrtimChannelB) {
    bits |= HRTIM_CR2_TBSWU;
  }
  if (channels & kHrtimChannelC) {
    bits |= HRTIM_CR2_TCSWU;
  }
  if (channels & kHrtimChannelD) {
    bits |= HRTIM_CR2_TDSWU;
  }
  if (channels & kHrtimChannelE) {
    bits |= HRTIM_CR2_TESWU;
  }
  if (channels & kHrtimMaster) {
    bits |= HRTIM_CR2_MSWU;
  }

  HRTIM1->sCommonRegs.CR2 |= bits;

  return 0;
}

/**
 * @brief Configure the timer's reset sources
 *
 * Reset sources are what trigger the counter.
 */
static int set_reset_sources(const struct device *hrtim, const hrtim_channel_t channel,
                             const hrtim_reset_sources_t *sources) {
  if (!hrtim || !sources) {
    return -EFAULT;
  } else if (!channel_is_valid(channel) || channel == kHrtimMaster) {
    // TODO: we could probably support master timer
    return -EINVAL;
  }

  uint32_t resetSources = 0;

  if (sources->masterPeriod) {
    resetSources |= HRTIM_RSTR_MSTPER;
  }
  if (sources->masterComp1) {
    resetSources |= HRTIM_RSTR_MSTCMP1;
  }
  if (sources->masterComp2) {
    resetSources |= HRTIM_RSTR_MSTCMP2;
  }
  if (sources->masterComp3) {
    resetSources |= HRTIM_RSTR_MSTCMP3;
  }
  if (sources->masterComp4) {
    resetSources |= HRTIM_RSTR_MSTCMP4;
  }

  // TODO: support other sources also

  // write it to the register
  const size_t regOffset = kRegOffset[channel_to_index(channel)];

  HRTIM1->sTimerxRegs[regOffset].RSTxR = resetSources;

  return 0;
}

/**
 * @brief Change enable status of an output channel
 *
 * This controls whether the timer's output is gated or not. The GPIOs should already be
 * configured.
 */
static int set_output_enable(const struct device *dev, const hrtim_channel_t channel, const size_t output,
                             const bool enabled) {
  // mapping of channel enable bits: first index is channel, second is output number
  static const uint32_t kEnableBits[NUM_CHANNELS][NUM_OUTPUTS_PER_CHANNEL] =
      {
          {HRTIM_OENR_TA1OEN, HRTIM_OENR_TA2OEN}, {HRTIM_OENR_TB1OEN, HRTIM_OENR_TB2OEN},
          {HRTIM_OENR_TC1OEN, HRTIM_OENR_TC2OEN}, {HRTIM_OENR_TD1OEN, HRTIM_OENR_TD2OEN},
          {HRTIM_OENR_TE1OEN, HRTIM_OENR_TE2OEN},
      },
                        kDisableBits[NUM_CHANNELS][NUM_OUTPUTS_PER_CHANNEL] = {
                            {HRTIM_ODISR_TA1ODIS, HRTIM_ODISR_TA2ODIS}, {HRTIM_ODISR_TB1ODIS, HRTIM_ODISR_TB2ODIS},
                            {HRTIM_ODISR_TC1ODIS, HRTIM_ODISR_TC2ODIS}, {HRTIM_ODISR_TD1ODIS, HRTIM_ODISR_TD2ODIS},
                            {HRTIM_ODISR_TE1ODIS, HRTIM_ODISR_TE2ODIS},
                        };

  if (!dev) {
    return -EFAULT;
  } else if (!channel_is_valid(channel) || channel == kHrtimMaster || output >= NUM_OUTPUTS_PER_CHANNEL) {
    return -EINVAL;
  }

  if (enabled) {
    HRTIM1->sCommonRegs.OENR |= kEnableBits[channel_to_index(channel)][output];
  } else {
    HRTIM1->sCommonRegs.ODISR |= kDisableBits[channel_to_index(channel)][output];
  }

  return 0;
}

/**
 * @brief Change output pin polarity
 */
static int set_output_polarity(const struct device *hrtim, const hrtim_channel_t channel, const size_t output,
                               const bool inverted) {
  if (!hrtim) {
    return -EFAULT;
  } else if (!channel_is_valid(channel) || channel == kHrtimMaster || output >= NUM_OUTPUTS_PER_CHANNEL) {
    return -EINVAL;
  }

  const size_t regOffset = kRegOffset[channel_to_index(channel)];
  uint32_t bit;

  if (output == 0) {
    bit = HRTIM_OUTR_POL1;
  } else {
    bit = HRTIM_OUTR_POL2;
  }

  if (inverted) {
    HRTIM1->sTimerxRegs[regOffset].OUTxR |= bit;
  } else {
    HRTIM1->sTimerxRegs[regOffset].OUTxR &= ~bit;
  }

  return 0;
}

/**
 * @brief Change output active level when idle
 */
static int set_output_idle_state(const struct device *hrtim, const hrtim_channel_t channel, const size_t output,
                                 const bool isActive) {
  if (!hrtim) {
    return -EFAULT;
  } else if (!channel_is_valid(channel) || channel == kHrtimMaster || output >= NUM_OUTPUTS_PER_CHANNEL) {
    return -EINVAL;
  }

  const size_t regOffset = kRegOffset[channel_to_index(channel)];
  uint32_t bit;

  if (output == 0) {
    bit = HRTIM_OUTR_IDLES1;
  } else {
    bit = HRTIM_OUTR_IDLES2;
  }

  if (isActive) {
    HRTIM1->sTimerxRegs[regOffset].OUTxR |= bit;
  } else {
    HRTIM1->sTimerxRegs[regOffset].OUTxR &= ~bit;
  }

  return 0;
}

/**
 * @brief Set channel timebase period
 *
 * The period is the maximum number of ticks that a timer channel will count up to.
 */
static int set_period(const struct device *hrtim, const hrtim_channel_t channel, const uint32_t nanos) {
  if (!hrtim) {
    return -EFAULT;
  } else if (!channel_is_valid(channel) | !nanos) {
    return -EINVAL;
  } else if (!is_timebase_stopped(hrtim, channel)) {
    return -EBUSY;
  }

  // calculate the requested period
  size_t timebase;
  float timebasePeriod;
  uint32_t timebaseTicks;
  hrtim_prescaler_t timebasePsc = kHrtimPrescaler1;

  do {
    timebase = get_timebase_freq(hrtim, channel, timebasePsc);
    timebasePeriod = ((float)NSEC_PER_SEC) / ((float)timebase);

    timebaseTicks = ((float)nanos) / timebasePeriod;

    LOG_DBG("requested %u ns: timebase = %u Hz/%u ns (psc %u) -> %u ticks", nanos, timebase,
            (unsigned int)timebasePeriod, (1 << timebasePsc), timebaseTicks);

    // try to increase prescaler if this period is too large to fit
    if (timebaseTicks > MAX_PERIOD) {
      // already at the highest prescaler, so the period cannot be supported
      if (timebasePsc == kHrtimPrescaler4) {
        LOG_WRN("period %u ns out of range (max %u)", nanos, (unsigned int)(timebasePeriod * MAX_PERIOD));
        return -ERANGE;
      }
      // already at the highest prescaler, so the period cannot be supported
      else {
        timebasePsc++;
      }
    }
    // otherwise, program the prescaler and tick count
    else {
      LOG_DBG("timebase = %u Hz (%u ns), requested period = %u ns, ticks = %u, psc = %u", timebase, (int)timebasePeriod,
              nanos, timebaseTicks, (1 << timebasePsc));
      break;
    }
  } while (true);

  HANDLE_UNLIKELY(set_prescaler(hrtim, channel, timebasePsc));

  if (channel == kHrtimMaster) {
    HRTIM1->sMasterRegs.MPER = timebaseTicks & UINT16_MAX;
  } else {
    const size_t regOffset = kRegOffset[channel_to_index(channel)];
    HRTIM1->sTimerxRegs[regOffset].PERxR = timebaseTicks & UINT16_MAX;
  }

  return 0;
}

/**
 * @brief Retrieve programmed timer period
 *
 * Calculate the timer's period (in nanoseconds) and frequency (in Hz) that it's currently
 * programmed for. This takes into account also the channel's prescaler.
 */
static int get_period(const struct device *hrtim, const hrtim_channel_t channel, uint32_t *outNanos, float *outFreq) {
  if (!hrtim) {
    return -EFAULT;
  } else if (!channel_is_valid(channel) || (!outNanos && !outFreq)) {
    return -EINVAL;
  }

  // get timebase frequency
  hrtim_prescaler_t timebasePsc;

  HANDLE_UNLIKELY(get_prescaler(hrtim, channel, &timebasePsc));
  const size_t timebase = get_timebase_freq(hrtim, channel, timebasePsc);
  const float timebasePeriod = ((float)NSEC_PER_SEC) / ((float)timebase);

  // convert period to timebase
  uint32_t rawPeriod;

  if (channel == kHrtimMaster) {
    rawPeriod = HRTIM1->sMasterRegs.MPER & UINT16_MAX;
  } else {
    const size_t regOffset = kRegOffset[channel_to_index(channel)];
    rawPeriod = HRTIM1->sTimerxRegs[regOffset].PERxR & UINT16_MAX;
  }

  if (outNanos) {
    *outNanos = timebasePeriod * ((float)rawPeriod);
  }
  if (outFreq) {
    *outFreq = ((float)timebase) / ((float)rawPeriod);
  }

  return 0;
}

/**
 * @brief Configure timer channel period/count registers
 *
 * This supports a maximum of four arbitrary time points, as well as an additional point at
 * the period overflow.
 */
static int set_waveform(const struct device *hrtim, const hrtim_channel_t channel, const hrtim_waveform_point_t *points,
                        const size_t numPoints) {
  if (!hrtim || !points) {
    return -EFAULT;
  } else if (!channel_is_valid(channel) || channel == kHrtimMaster) {
    // TODO: this could support the master timer, but it's just used as a dumb timebase now
    return -EINVAL;
  } else if (numPoints > 5) {
    // TODO: better sanity check, this must be 4 points + 1 end-of-period max
    return -ERANGE;
  } else if (!is_timebase_stopped(hrtim, channel)) {
    return -EBUSY;
  }

  const size_t regOffset = kRegOffset[channel_to_index(channel)];

  // clear both channel's set/reset registers
  const uint32_t kClearMask =
      (HRTIM_SET1R_PER | HRTIM_SET1R_CMP1 | HRTIM_SET1R_CMP2 | HRTIM_SET1R_CMP3 | HRTIM_SET1R_CMP4);

  MODIFY_REG(HRTIM1->sTimerxRegs[regOffset].SETx1R, kClearMask, 0);
  MODIFY_REG(HRTIM1->sTimerxRegs[regOffset].RSTx1R, kClearMask, 0);
  MODIFY_REG(HRTIM1->sTimerxRegs[regOffset].SETx2R, kClearMask, 0);
  MODIFY_REG(HRTIM1->sTimerxRegs[regOffset].RSTx2R, kClearMask, 0);

  /*
   * Process each of the waveform points provided.
   *
   * Each point is sequentially assigned to one of the four counter slots; any unused slots are
   * gated off. The end-of-period point can occur anywhere in this list of points.
   */
  size_t timerSlot = 0;

  for (size_t i = 0; i < numPoints; i++) {
    const hrtim_waveform_point_t *point = &points[i];

    // end of period event
    if (point->time == UINT32_MAX) {
      // update the event flags
      set_waveform_flags(hrtim, channel, point, HRTIM_SET1R_PER_Pos);
    }
    // assign to the next counter slot
    else {
      uint32_t period;

      // update period
      HANDLE_UNLIKELY(calc_count(hrtim, channel, point->time, &period));

      LOG_DBG("ch %u: pt[%u] = {slot = %u, time = %u ns (%04x/%04x)}", channel_to_index(channel), i, timerSlot,
              point->time, period, HRTIM1->sTimerxRegs[regOffset].PERxR);

      switch (timerSlot) {
      case 0:
        HRTIM1->sTimerxRegs[regOffset].CMP1xR = period & UINT16_MAX;
        break;
      case 1:
        HRTIM1->sTimerxRegs[regOffset].CMP2xR = period & UINT16_MAX;
        break;
      case 2:
        HRTIM1->sTimerxRegs[regOffset].CMP3xR = period & UINT16_MAX;
        break;
      case 3:
        HRTIM1->sTimerxRegs[regOffset].CMP4xR = period & UINT16_MAX;
        break;
      }

      // and the event flags
      const size_t bitIndex = (HRTIM_SET1R_CMP1_Pos + timerSlot);
      set_waveform_flags(hrtim, channel, point, bitIndex);

      timerSlot++;
    }
  }

  return 0;
}

/**
 * @brief Apply the channel set/reset config
 *
 * This will only set output bits: it's assumed that they're all cleared previously.
 */
static void set_waveform_flags(const struct device *hrtim, const hrtim_channel_t channel,
                               const hrtim_waveform_point_t *point, const size_t bitIndex) {
  const size_t regOffset = kRegOffset[channel_to_index(channel)];
  const uint32_t bit = (1U << bitIndex);

  if (point->ch1Active) {
    HRTIM1->sTimerxRegs[regOffset].SETx1R |= bit;
  }
  if (point->ch1Reset) {
    HRTIM1->sTimerxRegs[regOffset].RSTx1R |= bit;
  }

  if (point->ch2Active) {
    HRTIM1->sTimerxRegs[regOffset].SETx2R |= bit;
  }
  if (point->ch2Reset) {
    HRTIM1->sTimerxRegs[regOffset].RSTx2R |= bit;
  }
}

/**
 * @brief Calculate the base timebase frequency for a given channel
 *
 * HRTIM is configured to use the same clock as the CPU, so this returns the current CPU clock.
 */
static uint32_t get_timebase_freq(const struct device *dev, const hrtim_channel_t channel,
                                  const hrtim_prescaler_t prescaler) {
#if HRTIM_CLOCK_USE_CPU
  uint32_t clk = HAL_RCC_GetSysClockFreq();
#else
  uint32_t clk = HAL_RCC_GetPCLK2Freq() * 2;
#endif

  clk /= (1U << prescaler);

  return clk;
}

/**
 * @brief Calculate a period/count value for a channel
 *
 * This takes into account the prescaler currently configured in the channel.
 */
static int calc_count(const struct device *hrtim, const hrtim_channel_t channel, const uint32_t nanos,
                      uint32_t *outPeriod) {
  hrtim_prescaler_t timebasePsc;
  HANDLE_UNLIKELY(get_prescaler(hrtim, channel, &timebasePsc));

  const size_t timebase = get_timebase_freq(hrtim, channel, timebasePsc);
  const float timebasePeriod = ((float)NSEC_PER_SEC) / ((float)timebase);

  const uint32_t timebaseTicks = ((float)nanos) / timebasePeriod;
  if (timebaseTicks > MAX_PERIOD) {
    return -ERANGE;
  }

  *outPeriod = timebaseTicks;

  return 0;
}

/**
 * @brief Get the current prescaler configured for a channel
 */
static int get_prescaler(const struct device *hrtim, const hrtim_channel_t channel, hrtim_prescaler_t *outPrescaler) {
  if (!hrtim || !outPrescaler) {
    return -EFAULT;
  } else if (!channel_is_valid(channel)) {
    return -EINVAL;
  }

  uint32_t bits = 0;

  if (channel == kHrtimMaster) {
    bits = READ_BIT(HRTIM1->sMasterRegs.MCR, HRTIM_MCR_CK_PSC);
  } else {
    const size_t regOffset = kRegOffset[channel_to_index(channel)];
    bits = READ_BIT(HRTIM1->sTimerxRegs[regOffset].TIMxCR, HRTIM_MCR_CK_PSC);
  }

  switch (bits) {
  case HRTIM_PRESCALERRATIO_DIV1:
    *outPrescaler = kHrtimPrescaler1;
    break;
  case HRTIM_PRESCALERRATIO_DIV2:
    *outPrescaler = kHrtimPrescaler2;
    break;
  case HRTIM_PRESCALERRATIO_DIV4:
    *outPrescaler = kHrtimPrescaler4;
    break;

  default:
    return -EINVAL;
  }

  return 0;
}

/**
 * @brief Set channel timebase prescaler
 *
 * This allows the frequency of the timebase to be divided from the timer input frequency, which is
 * typically matching the CPU speed.
 */
static int set_prescaler(const struct device *hrtim, const hrtim_channel_t channel,
                         const hrtim_prescaler_t newPrescaler) {
  if (!hrtim) {
    return -EFAULT;
  } else if (!channel_is_valid(channel)) {
    return -EINVAL;
  } else if (!is_timebase_stopped(hrtim, channel)) {
    return -EBUSY;
  }

  uint32_t bits = 0;

  switch (newPrescaler) {
  case kHrtimPrescaler1:
    bits = HRTIM_PRESCALERRATIO_DIV1;
    break;
  case kHrtimPrescaler2:
    bits = HRTIM_PRESCALERRATIO_DIV2;
    break;
  case kHrtimPrescaler4:
    bits = HRTIM_PRESCALERRATIO_DIV4;
    break;

  default:
    return -EINVAL;
  }

  if (channel == kHrtimMaster) {
    MODIFY_REG(HRTIM1->sMasterRegs.MCR, HRTIM_MCR_CK_PSC, bits);
  } else {
    const size_t regOffset = kRegOffset[channel_to_index(channel)];
    MODIFY_REG(HRTIM1->sTimerxRegs[regOffset].TIMxCR, HRTIM_MCR_CK_PSC, bits);
  }

  return 0;
}

/**
 * @brief Determine if the channel's timebase stopped
 */
static bool is_timebase_stopped(const struct device *, const hrtim_channel_t channel) {
  const uint32_t mcr = HRTIM1->sMasterRegs.MCR;

  switch (channel) {
  case kHrtimChannelA:
    return !(mcr & HRTIM_MCR_TACEN);
  case kHrtimChannelB:
    return !(mcr & HRTIM_MCR_TBCEN);
  case kHrtimChannelC:
    return !(mcr & HRTIM_MCR_TCCEN);
  case kHrtimChannelD:
    return !(mcr & HRTIM_MCR_TDCEN);
  case kHrtimChannelE:
    return !(mcr & HRTIM_MCR_TECEN);
  case kHrtimMaster:
    return !(mcr & HRTIM_MCR_MCEN);

  // we really ought to never get here
  default:
    __builtin_unreachable();
  }
}

/**
 * @brief Is the provided channel index valid?
 *
 * It is expected the input defines precisely a single channel: no more, no less.
 */
static bool channel_is_valid(const hrtim_channel_t in) {
  switch (in) {
  case kHrtimChannelA:
  case kHrtimChannelB:
  case kHrtimChannelC:
  case kHrtimChannelD:
  case kHrtimChannelE:
  case kHrtimMaster:
    return true;

  default:
    return false;
  }
}

/**
 * @brief Get zero-based index for the given channel
 *
 * Channel A is index 0, B is index 1, and so forth.
 */
static size_t channel_to_index(const hrtim_channel_t in) {
  switch (in) {
  case kHrtimChannelA:
    return 0;
  case kHrtimChannelB:
    return 1;
  case kHrtimChannelC:
    return 2;
  case kHrtimChannelD:
    return 3;
  case kHrtimChannelE:
    return 4;

  default:
    LOG_ERR("invalid input channel %u", in);
    return 0;
  }
}

// Our exported public APIs
static const hrtim_api_t gApiFuncs = {
    .startStopChannel = set_timebase_enable,
    .setCounterMode = set_timebase_mode,
    .reset = reset_counter,
    .update = update_counter,
    .setResetSources = set_reset_sources,
    .setOutputEnabled = set_output_enable,
    .setOutputPolarity = set_output_polarity,
    .setOutputIdle = set_output_idle_state,
    .setPeriod = set_period,
    .getPeriod = get_period,
    .setWaveform = set_waveform,
};

// create a device instance
#define HRTIM_DEFINE(inst)                                                                                             \
  PINCTRL_DT_INST_DEFINE(inst);                                                                                        \
  __dtcm_bss_section static hrtim_data_t hrtim_data_##inst = {};                                                       \
  static const hrtim_config_t hrtim_config_##inst = {                                                                  \
      .pcfg = PINCTRL_DT_INST_DEV_CONFIG_GET(inst),                                                                    \
  };                                                                                                                   \
  DEVICE_DT_INST_DEFINE(inst, hrtim_init, NULL, &hrtim_data_##inst, &hrtim_config_##inst, POST_KERNEL,                 \
                        CONFIG_KERNEL_INIT_PRIORITY_DEVICE, &gApiFuncs);

DT_INST_FOREACH_STATUS_OKAY(HRTIM_DEFINE)

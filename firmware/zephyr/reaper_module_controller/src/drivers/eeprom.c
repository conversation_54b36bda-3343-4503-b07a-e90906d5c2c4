#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(eeprom, CONFIG_APP_LOG_LEVEL);

#include <string.h>

#include <zephyr/device.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/eeprom.h>
#include <zephyr/sys/byteorder.h>
#include <zephyr/sys/crc.h>

#include <utils/carbon_response_codes.h>
#include <utils/handle_errors.h>

#include "eeprom.h"
#include "watchdog.h"

// Total size of a block (actual storage is minus header size)
#define EEPROM_BLOCK_SIZE (256)
// Maximum payload size for an EEPROM block
#define EEPROM_BLOCK_PAYLOAD_SIZE (EEPROM_BLOCK_SIZE - sizeof(eeprom_block_header_t))
// Current EEPROM block header version
#define EEPROM_BLOCK_VERSION (1)

// Total number of blocks to reserve storage space for
#define EEPROM_MAX_NUM_BLOCKS (32)

/**
 * @brief Block header
 *
 * Contains the block type, various metadata, and a checksum over the header and contents of the
 * payload area including the actual used byte size.
 *
 * All checksums/CRCs use CRC-16 with the CCITT polynomial (x^16 + x^12 + x^5 + 1)
 *
 * @remark All multibyte values are stored in big endian (network) byte order
 */
typedef struct eeprom_block_header {
  // Header version flag
  uint8_t version;
  // Block data type (see `eeprom_block_type_t`)
  uint8_t type;

  // CRC over all written payload bytes (starting from payload, extending payloadLen bytes)
  uint16_t payloadCrc;
  // Actual data length (bytes)
  uint16_t payloadLen;

  // Pad header to 16 bytes
  uint8_t padding[8];

  // CRC over header (all bytes up to this point)
  uint16_t headerCrc;

  // Payload data follows this header
  char payload[];
} __attribute__((packed)) eeprom_block_header_t;
_Static_assert(sizeof(eeprom_block_header_t) == 16, "EEPROM block header size invalid");

/**
 * @brief EEPROM emulation status
 *
 * This stores a map of each of the data blocks in the emulated EEPROM and what type of data is
 * contained within.
 */
typedef struct eeprom_state {
  // lock to protect concurrent access to internal state and underlying storage
  struct k_mutex lock;

  // shadow map of all blocks in EEPROM
  struct {
    // Is there a valid block at this address?
    uint8_t valid : 1;
    uint8_t reserved : 7;

    // Block type
    uint8_t type;
  } blocks[EEPROM_MAX_NUM_BLOCKS];
} eeprom_state_t;

/// Device handle for emulated EEPROM
static const struct device *const gEeprom = DEVICE_DT_GET(DT_NODELABEL(emu_eeprom));
/// High level state for EEPROM emulation (e.g. logical blocks, etc.)
__dtcm_bss_section static eeprom_state_t gState;

static int scan_blocks();
__attribute__((warn_unused_result)) static bool validate_header(const eeprom_block_header_t *header);
static bool is_empty_header(const eeprom_block_header_t *header);
static void finalize_header(eeprom_block_header_t *header, const void *data, const size_t dataLen);
__attribute__((warn_unused_result)) static bool find_write_block(const eeprom_block_type_t type, size_t *outIndex);

/**
 * @brief Initialize EEPROM emulation
 *
 * Scan the emulated EEPROM for all logical blocks stored within.
 */
int eeprom_init() {
  // initialize EEPROM and internal state
  HANDLE_UNLIKELY_BOOL(device_is_ready(gEeprom), EIO);

  k_mutex_init(&gState.lock);

  // scan the contents of EEPROM and do consistency check
  HANDLE_UNLIKELY(scan_blocks());
  // TODO: consistency check here

  return 0;
}

/**
 * @brief Locate EEPROM block of the given type and attempt to read it out
 *
 * Find the first block of the given type (there should only be a single one!) and reads out its
 * payload data. The provided buffer must be of sufficient size to fit the entire payload:
 * truncated reads are not supported as this does not allow verifying the data CRC.
 *
 * @param type Block type to read from EEPROM
 * @param outBuffer Memory area to receive the payload of the block
 * @param outBufferSize Pointer to size variable; on input, it holds the size of the buffer, and
 *                      on return it has the actual payload size.
 */
int eeprom_read_block(eeprom_block_type_t type, void *outBuffer, size_t *outBufferSize) {
  if (!outBuffer | !outBufferSize || !*outBufferSize) {
    return -EINVAL;
  }

  int ret = -ENOENT;

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    const size_t numBlocks = eeprom_get_size(gEeprom) / EEPROM_BLOCK_SIZE;

    for (size_t block = 0; block < numBlocks; block++) {
      // skip block if type doesn't match
      if (!gState.blocks[block].valid || gState.blocks[block].type != type) {
        continue;
      }

      LOG_DBG("found type %02x at %2u", type, block);

      // read header (and validate) to determine data length
      eeprom_block_header_t header;
      const size_t blockAddr = (block * EEPROM_BLOCK_SIZE);

      HANDLE_UNLIKELY(eeprom_read(gEeprom, blockAddr, &header, sizeof(header)));

      if (!validate_header(&header)) {
        ret = -EBADF;
        goto beach;
      } else if (header.payloadLen < *outBufferSize) {
        ret = -EOVERFLOW;
        goto beach;
      }

      // read out the data
      const size_t toRead = MIN(*outBufferSize, header.payloadLen);

      ret = eeprom_read(gEeprom, (blockAddr + offsetof(eeprom_block_header_t, payload)), outBuffer, toRead);
      if (ret) {
        goto beach;
      }

      // validate data checksum
      const uint16_t hdrChecksum = sys_be16_to_cpu(header.payloadCrc);
      const uint16_t actualChecksum = crc16_ccitt(0, outBuffer, header.payloadLen);

      if (hdrChecksum != actualChecksum) {
        ret = -EBADF;
        goto beach;
      }

      *outBufferSize = header.payloadLen;

      ret = 0;
    }
  }
beach:;
  k_mutex_unlock(&gState.lock);
  return ret;
}

/**
 * @brief Write an EEPROM data block
 *
 * Find a place for the provided data in the EEPROM, either by allocating a new block or
 * overwriting an existing one.
 *
 * @param type Block type to be written
 * @param payload Data to store in the payload area of the block
 * @param payloadSize Total length of the payload, in bytes
 */
int eeprom_write_block(eeprom_block_type_t type, const void *payload, const size_t payloadSize) {
  if (!payload || !payloadSize || payloadSize > EEPROM_BLOCK_PAYLOAD_SIZE) {
    return -EINVAL;
  }

  int ret = -ENOENT;

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    // find a suitable block slot
    size_t block = 0;
    if (!find_write_block(type, &block)) {
      ret = -ENOSPC;
      goto beach;
    }

    LOG_DBG("writing type %02x to block %2u (%u payload bytes)", type, block, payloadSize);

    // build up the header
    eeprom_block_header_t header;
    memset(&header, 0, sizeof(header));

    header.type = type;
    finalize_header(&header, payload, payloadSize);

    // write data first, then the header
    const size_t blockAddr = (block * EEPROM_BLOCK_SIZE);

    watchdog_eeprom_write_callback(true);
    ret = eeprom_write(gEeprom, blockAddr + offsetof(eeprom_block_header_t, payload), payload, payloadSize);
    if (ret != 0) {
      goto beach;
    }

    ret = eeprom_write(gEeprom, blockAddr, &header, sizeof(header));
    if (ret != 0) {
      goto beach;
    }

    // if write was successful, update bookkeeping
    gState.blocks[block].valid = true;
    gState.blocks[block].type = header.type;
  }
beach:;
  k_mutex_unlock(&gState.lock);
  watchdog_eeprom_write_callback(false);
  return ret;
}

/**
 * @brief Erase EEPROM data block of the given type
 *
 * All blocks (should only be one, but this will get them all) will be erased.
 *
 * @remark The data of the block is NOT erased; only the header is zeroed.
 */
int eeprom_erase_block(eeprom_block_type_t type) {
  if (type == kEepromBlockTypeEmpty) {
    return -EINVAL;
  }

  const size_t numBlocks = eeprom_get_size(gEeprom) / EEPROM_BLOCK_SIZE;
  int ret = -ENOENT;

  eeprom_block_header_t header;
  memset(&header, 0xFF, sizeof(header));

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    for (size_t block = 0; block < numBlocks; block++) {
      // skip block if wrong type
      if (!gState.blocks[block].valid || gState.blocks[block].type != type) {
        continue;
      }

      LOG_DBG("erasing type %02x at block %2u", type, block);

      // erase the block header
      const size_t blockAddr = (block * EEPROM_BLOCK_SIZE);

      watchdog_eeprom_write_callback(true);
      ret = eeprom_write(gEeprom, blockAddr, &header, sizeof(header));
      if (ret != 0) {
        goto beach;
      }

      // if that was successful, update bookkeeping
      gState.blocks[block].valid = false;
      gState.blocks[block].type = kEepromBlockTypeEmpty;
    }
  }

beach:;
  k_mutex_unlock(&gState.lock);
  watchdog_eeprom_write_callback(false);
  return ret;
}

/**
 * @brief Erase the entire EEPROM
 *
 * All blocks and their data are erased.
 */
int eeprom_erase_all() {
  const size_t numBlocks = eeprom_get_size(gEeprom) / EEPROM_BLOCK_SIZE;
  int ret = -ENOENT;

  uint8_t zeroed[EEPROM_BLOCK_PAYLOAD_SIZE];
  memset(zeroed, 0, sizeof(zeroed));

  eeprom_block_header_t header;
  memset(&header, 0, sizeof(header));

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    for (size_t block = 0; block < numBlocks; block++) {
      // erase the block header AND its data
      const size_t blockAddr = (block * EEPROM_BLOCK_SIZE);

      watchdog_eeprom_write_callback(true);
      ret = eeprom_write(gEeprom, blockAddr, &header, sizeof(header));
      if (ret != 0) {
        goto beach;
      }

      ret = eeprom_write(gEeprom, blockAddr + offsetof(eeprom_block_header_t, payload), zeroed, sizeof(zeroed));
      if (ret != 0) {
        goto beach;
      }

      // if that was successful, update bookkeeping
      gState.blocks[block].valid = false;
      gState.blocks[block].type = kEepromBlockTypeEmpty;
    }
  }

beach:;
  k_mutex_unlock(&gState.lock);
  watchdog_eeprom_write_callback(false);
  return ret;
}

/**
 * @brief Read out all blocks on the EEPROM and determine their types
 *
 * @remark Caller must hold the internal state lock
 */
static int scan_blocks() {
  const size_t numBlocks = eeprom_get_size(gEeprom) / EEPROM_BLOCK_SIZE;

  memset(gState.blocks, 0, sizeof(gState.blocks));

  for (size_t block = 0; block < numBlocks; block++) {
    // read the block header and validate the header
    eeprom_block_header_t header;
    const size_t blockAddr = (block * EEPROM_BLOCK_SIZE);

    HANDLE_UNLIKELY(eeprom_read(gEeprom, blockAddr, &header, sizeof(header)));

    if (!validate_header(&header)) {
      // print error message only if header isn't unprogrammed/empty
      if (!is_empty_header(&header)) {
        LOG_WRN("block %2u: invalid header", block);
        LOG_HEXDUMP_WRN(&header, sizeof(header), "invalid block header");
      }

      continue;
    }

    // if it's valid, store block number
    gState.blocks[block].valid = true;
    gState.blocks[block].type = header.type;

    LOG_DBG("block %2u: type=%02x, payload=%u bytes", block, header.type, header.payloadLen);
  }

  return 0;
}

/**
 * @brief Validate a block header
 *
 * Check that the header version matches what's expected, then compute the checksum over the header
 * data and ensure it's valid.
 */
static bool validate_header(const eeprom_block_header_t *header) {
  // check version
  if (header->version != EEPROM_BLOCK_VERSION) {
    return false;
  }

  // verify checksum
  const uint16_t hdrChecksum = sys_be16_to_cpu(header->headerCrc);
  const uint16_t actualChecksum = crc16_ccitt(0, (const uint8_t *)header, offsetof(eeprom_block_header_t, headerCrc));

  if (hdrChecksum != actualChecksum) {
    return false;
  }

  // header is valid
  return true;
}

/**
 * @brief Check whether block header is unprogrammed
 *
 * EEPROMs will be all one's (0xFF byte) for unprogrammed/erased byte positions. This checks
 * whether all bytes are unprogrammed, so that an "invalid" header can be skipped.
 */
static bool is_empty_header(const eeprom_block_header_t *header) {
  const uint8_t *bytes = (const uint8_t *)header;
  for (size_t i = 0; i < sizeof(*header); i++) {
    if (bytes[i] != 0xff) {
      return false;
    }
  }

  return true;
}

/**
 * @brief Prepare block header for writing to EEPROM
 *
 * Calculate the checksum over the block payload, update the header with this and then recalculate
 * the block header checksum.
 *
 * @note The caller should have filled out all relevant header fields (such as type) before
 *       invoking this routine.
 *
 * @param header Block header to update (it's updated in place)
 * @param data Pointer to payload data (not copied; used only for CRC)
 * @param dataLen Length of payload, in bytes
 */
static void finalize_header(eeprom_block_header_t *header, const void *data, const size_t dataLen) {
  // fixed fields
  header->version = EEPROM_BLOCK_VERSION;
  header->payloadLen = dataLen;

  // payload checksum
  const uint16_t payloadCrc = crc16_ccitt(0, data, dataLen);
  header->payloadCrc = sys_cpu_to_be16(payloadCrc);

  // header checksum
  const uint16_t hdrCrc = crc16_ccitt(0, (const uint8_t *)header, offsetof(eeprom_block_header_t, headerCrc));
  header->headerCrc = sys_cpu_to_be16(hdrCrc);
}

/**
 * @brief Locate block index to write to
 *
 * Find a suitable location that a block of the given type may be written to. This roughly falls
 * into one of two cases:
 *
 * 1. A block with this type already exists; it will be overwritten.
 * 2. No such block exists; pick the first empty block.
 *
 * This routine does not update any internal bookkeeping, nor does it perform any writes to the
 * EEPROM.
 *
 * @param type Block type to be written
 * @param outIndex Variable to receive the logical block index to be written to
 *
 * @return Whether a suitable block location was found
 *
 * @remark Caller must hold the internal state lock
 */
static bool find_write_block(const eeprom_block_type_t type, size_t *outIndex) {
  const size_t numBlocks = eeprom_get_size(gEeprom) / EEPROM_BLOCK_SIZE;

  // check for an existing block
  for (size_t block = 0; block < numBlocks; block++) {
    if (!gState.blocks[block].valid || gState.blocks[block].type != type) {
      continue;
    }

    *outIndex = block;
    return true;
  }

  // find the first available (e.g. invalid) block
  for (size_t block = 0; block < numBlocks; block++) {
    if (gState.blocks[block].valid && gState.blocks[block].type != kEepromBlockTypeEmpty) {
      continue;
    }

    *outIndex = block;
    return true;
  }

  // if we get here, the EEPROM is full and no blocks remain
  return false;
}

/**
 * @file
 * @brief High resolution timer (HRTIM) peripheral driver
 *
 * This implements a basic driver for the HRTIM peripheral; it implements only the pieces needed for
 * use in the strobe/camera triggering application.
 */
#pragma once

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

#include <device.h>

// Physical timer channels
typedef enum hrtim_channel {
  kHrtimChannelA = (1 << 0),
  kHrtimChannelB = (1 << 1),
  kHrtimChannelC = (1 << 2),
  kHrtimChannelD = (1 << 3),
  kHrtimChannelE = (1 << 4),
  kHrtimMaster = (1 << 5),
} hrtim_channel_t;

// Timebase divider settings
typedef enum hrtim_prescaler {
  // fHRTIM
  kHrtimPrescaler1 = 0,
  // fHRTIM / 2
  kHrtimPrescaler2 = 1,
  // fHRTIM / 4
  kHrtimPrescaler4 = 2,
} hrtim_prescaler_t;

// Timer channel operation mode
typedef enum hrtim_counter_mode {
  // Automatically resets to zero when reaching period (auto reset)
  kHrtimCounterModeContinuous,
  // Stops after reaching period; can be reset while counting
  kHrtimCounterModeSingleShotResettable,
  // Stops after reaching period; ignores reset requests while counting
  kHrtimCounterModeSingleShot,
} hrtim_counter_mode_t;

/**
 * @brief Counter reset source bitmask
 *
 * Each bit corresponds to a source for an event that can be enabled to cause the counter to be
 * reset.
 */
typedef struct hrtim_reset_sources {
  uint32_t masterPeriod : 1;
  uint32_t masterComp1 : 1;
  uint32_t masterComp2 : 1;
  uint32_t masterComp3 : 1;
  uint32_t masterComp4 : 1;

  uint32_t reserved : 27;
} hrtim_reset_sources_t;

/**
 * @brief Definition of the waveform at a given point in the period
 *
 * This structure is used to represent an arbitrary point on the waveform (by period) and the
 * action to perform at that point.
 *
 * @remark Specifying a point with a `time` of UINT32_MAX corresponds to the end of the period.
 */
typedef struct hrtim_waveform_point {
  // Time point (in nanoseconds) relative to start of period
  uint32_t time;

  // Assert channel 1 output
  uint32_t ch1Active : 1;
  // Assert channel 2 output
  uint32_t ch2Active : 1;
  // Clear channel 1 output
  uint32_t ch1Reset : 1;
  // Clear channel 2 output
  uint32_t ch2Reset : 1;

  uint32_t reserved : 28;
} hrtim_waveform_point_t;

/**
 * @brief API methods exported by the HRTIM peripheral
 */
typedef struct hrtim_api {
  int (*startStopChannel)(const struct device *hrtim, const hrtim_channel_t channels, const bool state);
  int (*setCounterMode)(const struct device *hrtim, const hrtim_channel_t channel, const hrtim_counter_mode_t mode);
  int (*reset)(const struct device *hrtim, const hrtim_channel_t channels);
  int (*update)(const struct device *hrtim, const hrtim_channel_t channels);

  int (*setResetSources)(const struct device *hrtim, const hrtim_channel_t channel,
                         const hrtim_reset_sources_t *newSources);

  int (*setOutputEnabled)(const struct device *hrtim, const hrtim_channel_t channel, const size_t output,
                          const bool enabled);
  int (*setOutputPolarity)(const struct device *hrtim, const hrtim_channel_t channel, const size_t output,
                           const bool isInverted);
  int (*setOutputIdle)(const struct device *hrtim, const hrtim_channel_t channel, const size_t output,
                       const bool isActive);

  int (*setPeriod)(const struct device *hrtim, const hrtim_channel_t channel, const uint32_t nanos);
  int (*getPeriod)(const struct device *hrtim, const hrtim_channel_t channel, uint32_t *outNanos, float *outFreq);

  int (*setWaveform)(const struct device *hrtim, const hrtim_channel_t channel, const hrtim_waveform_point_t *points,
                     const size_t numPoints);
} hrtim_api_t;

/**
 * @brief Start or stop a timer's counter
 *
 * @param channel Logical OR of one or more timer channels
 * @param state Whether the timer's counter should be started (`true`) or stopped (`false`)
 */
static inline int hrtim_set_timebase_enabled(const struct device *hrtim, const hrtim_channel_t channels,
                                             const bool start) {
  const hrtim_api_t *api = (const hrtim_api_t *)hrtim->api;
  return api->startStopChannel(hrtim, channels, start);
}

/**
 * @brief Update timebase counting mode
 *
 * The counting mode defines the behavior of the counter once it reaches its maximum value.
 *
 * @param channel Channel to configure; must select precisely one channel
 */
static inline int hrtim_set_counter_mode(const struct device *hrtim, const hrtim_channel_t channels,
                                         const hrtim_counter_mode_t newMode) {
  const hrtim_api_t *api = (const hrtim_api_t *)hrtim->api;
  return api->setCounterMode(hrtim, channels, newMode);
}

/**
 * @brief Enable or disable a channel's output
 *
 * @param channel Channel to enable or disable; must select precisely one channel
 * @param output Which of the channel's two output lines to configure
 */
static inline int hrtim_set_output_enabled(const struct device *hrtim, const hrtim_channel_t channel,
                                           const size_t output, const bool enabled) {
  const hrtim_api_t *api = (const hrtim_api_t *)hrtim->api;
  return api->setOutputEnabled(hrtim, channel, output, enabled);
}

/**
 * @brief Reset channel counter
 *
 * @param channel Logical OR of one or more timer channels
 */
static inline int hrtim_reset_channel(const struct device *hrtim, const hrtim_channel_t channels) {
  const hrtim_api_t *api = (const hrtim_api_t *)hrtim->api;
  return api->reset(hrtim, channels);
}

/**
 * @brief Update channel shadow registers
 *
 * @param channel Logical OR of one or more timer channels
 */
static inline int hrtim_update_channel(const struct device *hrtim, const hrtim_channel_t channels) {
  const hrtim_api_t *api = (const hrtim_api_t *)hrtim->api;
  return api->update(hrtim, channels);
}

/**
 * @brief Set the channel reset sources
 *
 * Various external events (e.g. external to the configured channel) can be used to reset the
 * counter. This can be used to implement a divided timebase, for example.
 */
static inline int hrtim_set_reset_sources(const struct device *hrtim, const hrtim_channel_t channel,
                                          const hrtim_reset_sources_t *newSources) {
  const hrtim_api_t *api = (const hrtim_api_t *)hrtim->api;
  return api->setResetSources(hrtim, channel, newSources);
}

/**
 * @brief Configure pin output polarity
 *
 * When inverted, activating the output corresponds to a deasserted physical level.
 *
 * @param channel Channel to configure; must select precisely one channel
 * @param output Which of the channel's two output lines to configure
 * @param isInverted Whether the output channel shall be inverted
 */
static inline int hrtim_set_output_inverted(const struct device *hrtim, const hrtim_channel_t channel,
                                            const size_t output, const bool isInverted) {
  const hrtim_api_t *api = (const hrtim_api_t *)hrtim->api;
  return api->setOutputPolarity(hrtim, channel, output, isInverted);
}

/**
 * @brief Configure pin idle polarity
 *
 * @param channel Channel to configure; must select precisely one channel
 * @param output Which of the channel's two output lines to configure
 * @param isActive Whether the pin outputs an active level when idle
 */
static inline int hrtim_set_output_idle(const struct device *hrtim, const hrtim_channel_t channel, const size_t output,
                                        const bool isActive) {
  const hrtim_api_t *api = (const hrtim_api_t *)hrtim->api;
  return api->setOutputIdle(hrtim, channel, output, isActive);
}

/**
 * @brief Set channel period
 *
 * Configure the overall period of the channel's PWM.
 */
static inline int hrtim_set_period(const struct device *hrtim, const hrtim_channel_t channel, const uint32_t nanos) {
  const hrtim_api_t *api = (const hrtim_api_t *)hrtim->api;
  return api->setPeriod(hrtim, channel, nanos);
}

/**
 * @brief Update channel waveform
 *
 * Set up the counter registers and channel output change triggers.
 */
static inline int hrtim_set_waveform(const struct device *hrtim, const hrtim_channel_t channel,
                                     const hrtim_waveform_point_t *points, const size_t numPoints) {
  const hrtim_api_t *api = (const hrtim_api_t *)hrtim->api;
  return api->setWaveform(hrtim, channel, points, numPoints);
}

/**
 * @brief Configure channel with basic PWM
 *
 * Set up the selected channel to generate a single pulse with configurable duty cycle.
 *
 * @param dutyCycle PWM duty cycle (in the range [0, 1])
 * @param out1 Set to enable PWM output on channel output 1
 * @param out2 Set to enable PWM output on channel output 2
 */
static inline int hrtim_pwm_set_duty(const struct device *hrtim, const hrtim_channel_t channel, const float dutyCycle,
                                     const bool out1, const bool out2) {
  int err;
  const hrtim_api_t *api = (const hrtim_api_t *)hrtim->api;

  if (dutyCycle < 0.f || dutyCycle > 1.f) {
    return -ERANGE;
  }

  // get current period (so we can calculate the time point for duty cycle)
  uint32_t period;

  err = api->getPeriod(hrtim, channel, &period, NULL);
  if (err) {
    return err;
  }

  const float dutyPeriod = ((float)period) * dutyCycle;

  // configure the points
  hrtim_waveform_point_t points[2] = {
      // assert line at overflow
      {
          .time = UINT32_MAX,
          .ch1Active = out1,
          .ch2Active = out2,
          .ch1Reset = false,
          .ch2Reset = false,
      },
      // deassert at the duty cycle point
      {
          .time = ((uint32_t)dutyPeriod),
          .ch1Active = false,
          .ch2Active = false,
          .ch1Reset = out1,
          .ch2Reset = out2,
      },
  };

  return hrtim_set_waveform(hrtim, channel, points, 2);
}

/**
 * @brief Configure channel with basic PWM
 *
 * Set up the selected channel to generate a single pulse with configurable active period length.
 *
 * @param onPeriod How long the PWM signal is asserted (active) in the period (nsec)
 * @param out1 Set to enable PWM output on channel output 1
 * @param out2 Set to enable PWM output on channel output 2
 */
static inline int hrtim_pwm_set_duty_period(const struct device *hrtim, const hrtim_channel_t channel,
                                            const uint32_t onPeriod, const bool out1, const bool out2) {
  hrtim_waveform_point_t points[2] = {
      // assert line at overflow
      {
          .time = UINT32_MAX,
          .ch1Active = out1,
          .ch2Active = out2,
          .ch1Reset = false,
          .ch2Reset = false,
      },
      // deassert at the duty cycle point
      {
          .time = onPeriod,
          .ch1Active = false,
          .ch2Active = false,
          .ch1Reset = out1,
          .ch2Reset = out2,
      },
  };

  return hrtim_set_waveform(hrtim, channel, points, 2);
}

#include <logging/log.h>
LOG_MODULE_REGISTER(main, CONFIG_APP_LOG_LEVEL);

#include <devicetree.h>
#include <zephyr.h>
#include <zephyr/logging/log_backend.h>
#include <zephyr/logging/log_ctrl.h>
#include <zephyr/random/rand32.h>

#include <lib/ptp/ptp.h>
#include <lib/smp/smp.h>
#include <lib/status_led/status_led.h>

#include <utils/handle_errors.h>

#include "drivers/coredump.h"
#include "drivers/eeprom.h"
#include "fans.h"
#include "module_id.h"
#include "network/init.h"
#include "network/utils.h"
#include "power/power.h"
#include "power/relays.h"
#include "rpc/pc_serial.h"
#include "rpc/udp_server.h"
#include "scanners.h"
#include "sensors/isense.h"
#include "sensors/sensors.h"
#include "strobe/strobe.h"
#include "watchdog.h"

#if IS_ENABLED(CONFIG_LOG_BACKEND_NET)
extern const struct log_backend *log_backend_net_get(void);
#endif

static int init_net();
static int init();
static void handle_blinkenlights();

/**
 * @brief Initialize network
 *
 * Set up the network interface and external switch
 */
static int init_net() {
  LOG_INF("start network setup");

  // randomize startup delay to avoid all MCBs querying simultaneously
  k_sleep(K_MSEC(sys_rand32_get() % 1500));

  // configure the physical interface and addresses
  HANDLE_UNLIKELY(network_init());

  // start remote management (SMP for MCUMGR commands) and network logging
  start_smp_lib();

  if (!IS_ENABLED(CONFIG_LOG_BACKEND_NET_AUTOSTART)) {
    const struct log_backend *backend = log_backend_net_get();

    if (!log_backend_is_active(backend)) {
      if (backend->api->init != NULL) {
        backend->api->init(backend);
      }

      log_backend_activate(backend, NULL);
    }
  }

  // start PTP client
  struct net_if *iface = net_if_get_default();
  struct net_if_config *iface_cfg = net_if_get_config(net_if_get_default());
  uint8_t last_ip_digit = iface_cfg->ip.ipv4->unicast[0].address.in_addr.s4_addr[3];

  HANDLE_CRITICAL(ptp_slave_init(iface, last_ip_digit));
  HANDLE_CRITICAL(ptp_slave_start());

  log_set_timestamp_func(log_get_ptp_timestamp, 1000000U);

  return 0;
}

/**
 * @brief Initialize all components of the firmware
 */
static int init() {
  int err;

  HANDLE_UNLIKELY(leds_init());
  leds_set_status(LED_COLOR_BLUE);

  HANDLE_UNLIKELY(watchdog_init());

  // set up control for external power
  HANDLE_UNLIKELY(power_init());
  HANDLE_UNLIKELY(relays_init());

  // initialize the EEPROM emulation, then read out module ID
  HANDLE_UNLIKELY(eeprom_init());
  HANDLE_UNLIKELY(module_id_init());

  // set up network and network services
  HANDLE_UNLIKELY(init_net());

  // set up control for external power, scanners
  HANDLE_UNLIKELY(isense_init());
  HANDLE_UNLIKELY(scanners_init());

  // set up sensors and other local IO
  err = sensors_init();
  if (err) {
    LOG_ERR("sensor init failed: %d", err);
  }

  HANDLE_UNLIKELY(fans_init());
  HANDLE_UNLIKELY(strobe_init());

  // host interfaces (via Ethernet and local RS232)
  LOG_INF("starting host interfaces");

  HANDLE_UNLIKELY(pc_serial_init());
  HANDLE_UNLIKELY(udp_server_init());

  HANDLE_UNLIKELY(coredump_init());

  return 0;
}

/**
 * @brief Main thread entry point (automagically called after Zephyr init)
 */
void main() {
  LOG_INF("%s booting", "reaper module controller");

  HANDLE_CRITICAL(init());

  LOG_INF("%s ready", "reaper module controller");

  /*
   * Set up the default state of the system:
   * - 240VAC power: PC on, BTL on, LDD on
   * - 24VDC power: predict cam on
   *  - Strobe/relay board is enabled by the appropriate initializers
   * - Scanners: A/B on
   * - Strobing: Always enabled; 40Hz, 2% default otherwise use last applied settings
   * - Fan control: Thermostatic mode, 30°C threshold, use MCB temp sensor
   *
   * A random delay (up to 5 seconds) is inserted before powering on the PC to limit the
   * chance that all modules power up simultaneously and create large inrush current
   * demands on the electrical distribution system.
   */
  HANDLE_CRITICAL(power_enable(kPowerPredictCam));

#if HAS_SCANNER_CONTROL
  HANDLE_CRITICAL(scanners_set_power((kScannerA | kScannerB), 0));
#endif

  // enable strobes: uses either the default (40Hz/2%) config or what's stored in EEPROM
  HANDLE_CRITICAL(strobe_set_enabled(true));

  // random power-on delay for switched 240V stuff
  const size_t powerDelay = sys_rand32_get() % 5000, delay2 = sys_rand32_get() % 150, delay3 = sys_rand32_get() % 150;
  LOG_INF("Relay power on delay: %u ms", powerDelay);
  k_sleep(K_MSEC(powerDelay));

  HANDLE_CRITICAL(relays_set_single(kRelayBtl, true));
  k_sleep(K_MSEC(250 + delay2));
  HANDLE_CRITICAL(relays_set_single(kRelayPc, true));
  k_sleep(K_MSEC(100 + delay3));
  HANDLE_CRITICAL(relays_set_single(kRelayLdd, true));

  // enable fan control
  static const fan_thermo_config_t kThermostatConfig = {30, 2, kThermoSourceEnviroInternal};
  HANDLE_CRITICAL(fans_set_thermo_config(&kThermostatConfig));
  HANDLE_CRITICAL(fans_set_thermo_enabled(true));

  // firmware init complete; arm watchdog and handle blinky lights
  HANDLE_CRITICAL(watchdog_arm());

  k_thread_priority_set(k_current_get(), (K_LOWEST_APPLICATION_THREAD_PRIO - 1));

  handle_blinkenlights();
}

typedef enum blinky_state {
  // link down
  kBlinkyStateLinkDown,
  // link up and received frame recently
  kBlinkyStateLinkUp,
  // link up and no frame recently received
  kBlinkyStateLinkUpInactive,
} blinky_state_t;

/**
 * @brief Process the RGB status LED
 *
 * This is used to indicate the overall state of the system:
 *
 * - Solid red: Firmware not running
 * - Solid blue: Initializing/system booting
 * - Blinking green: Link up, no packet to nanopb interface in last 5 seconds
 * - Color cycling: Link up, received nanopb packet in the last 5 seconds
 * - Solid yellow: Link down
 */
static void handle_blinkenlights() {
  int err;
  blinky_state_t state = kBlinkyStateLinkDown, nextState = state;
  size_t iterationsInState = 0;

  // Time duration of a single blinky tick, in msec
  const size_t kTickInterval = 20;
  // Time interval between packets until link is considered "alive, but inactive" (in msec)
  const size_t kPacketTimeout = 10000;

  // blink interval (ticks)
  const size_t kBlinkInterval = 50;
  // duration of the "connected" hue cycle, in msec
  const size_t kConnectedCycleLength = 15 * MSEC_PER_SEC;

  do {
    switch (state) {
    /*
     * Link up, active: solid green LED
     *
     * We've received a packet in the last five seconds. This periodically checks this, and
     * if no packet is received go to the inactive state.
     */
    case kBlinkyStateLinkUp: {
      int64_t timestamp;
      struct net_ptp_time ptpTime;
      if (ptp_slave_clk_get(&ptpTime)) {
        break;
      }

      uint64_t msec = ptp_ts_net_to_ms(&ptpTime) % kConnectedCycleLength;
      double hue = (((double)msec) / (double)kConnectedCycleLength) * 360.0;

      leds_set_status(leds_hsi_to_rgb(hue, 1, 1));

      err = udp_get_last_request_time(&timestamp);
      if (err) {
        LOG_WRN("%s failed: %d", "udp_get_last_request_time", err);
      }

      // timed out waiting for a packet?
      if (err || k_uptime_delta(&timestamp) > kPacketTimeout) {
        nextState = kBlinkyStateLinkUpInactive;
      }
      // link went down?
      else if (!network_is_up()) {
        nextState = kBlinkyStateLinkDown;
      }
      break;
    }

    /*
     * Link up, inactive: blinking green LED
     *
     * Indicates that the Ethernet link is up but no nanopb packets received
     */
    case kBlinkyStateLinkUpInactive:
      int64_t timestamp;

      if ((iterationsInState / kBlinkInterval) & 1) {
        leds_set_status(LED_COLOR_OFF);
      } else {
        leds_set_status(LED_COLOR_GREEN);
      }

      err = udp_get_last_request_time(&timestamp);
      if (err) {
        LOG_WRN("%s failed: %d", "udp_get_last_request_time", err);
      }

      // received packet recently?
      if (!err && timestamp && k_uptime_delta(&timestamp) <= kPacketTimeout) {
        nextState = kBlinkyStateLinkUp;
      }
      // link went down?
      else if (!network_is_up()) {
        nextState = kBlinkyStateLinkDown;
      }
      break;

    /*
     * Link down: solid yellow LED
     */
    case kBlinkyStateLinkDown:
      leds_set_status(LED_COLOR_YELLOW);

      // link went up?
      if (network_is_up()) {
        nextState = kBlinkyStateLinkUpInactive;
      }
      break;
    }

    // transition to next state
    if (state != nextState) {
      LOG_DBG("%u -> %u", state, nextState);

      iterationsInState = 0;
      state = nextState;
    }
    // increment ticks counter
    else {
      iterationsInState++;
    }

    k_sleep(K_MSEC(kTickInterval));
  } while (true);
}

#include <zephyr/logging/log.h>
LOG_MODULE_DECLARE(network, CONFIG_APP_NET_LOG_LEVEL);

#include <zephyr/net/net_if.h>
#include <zephyr/net/net_ip.h>
#include <zephyr/random/rand32.h>

#include <utils/handle_errors.h>

#include "network/utils.h"
#include "power/power.h"

K_MUTEX_DEFINE(gNetworkPowerCycleMutex);

/**
 * @brief Power cycle the Ethernet switch
 *
 * This results in all links being renegotiated. Concurrent calls to this method will be
 * serialized.
 */
int network_do_power_cycle() {
  int err;

  HANDLE_UNLIKELY(k_mutex_lock(&gNetworkPowerCycleMutex, K_FOREVER));
  {
    err = power_disable(kPowerEthSwitch);
    if (err) {
      LOG_WRN("%s failed: %d", "power_disable", err);
      goto beach;
    }

    k_sleep(K_SECONDS(2));

    err = power_enable(kPowerEthSwitch);
    if (err) {
      LOG_WRN("%s failed: %d", "power_enable", err);
      goto beach;
    }

    // this sleep is to wait for the switch to initialize. links may not yet be up
    k_sleep(K_SECONDS(2));

    LOG_INF("Network power cycle complete");
  }
beach:;
  k_mutex_unlock(&gNetworkPowerCycleMutex);

  return err;
}

/**
 * @brief Is the network interface up?
 *
 * Check if the physical link is up; static addressing is always used so no IP needs to be checked.
 */
bool network_is_up() {
  struct net_if *intf = net_if_get_default();

  return net_if_is_up(intf);
}

/**
 * @brief Add a link-local (autoconf) address to interface
 *
 * This is used for DHCP if there's no other addresses on the interface; the Zephyr IP stack
 * outright refuses to send packets if there's no address configured, and rejects the unspecified
 * address (0.0.0.0) as network address.
 */
int network_add_link_local(struct net_if *intf) {
  struct in_addr addr;
  struct net_if_addr *addrPtr;

  addr.s4_addr[0] = 169;
  addr.s4_addr[1] = 254;
  addr.s4_addr[2] = sys_rand32_get() % 254;
  addr.s4_addr[3] = sys_rand32_get() % 254;

  addrPtr = net_if_ipv4_addr_add(intf, &addr, NET_ADDR_AUTOCONF, 0);
  if (!addrPtr) {
    LOG_WRN("%s failed", "net_if_ipv4_addr_add");
    return -1;
  }

  char addrStr[INET_ADDRSTRLEN];
  memset(addrStr, 0, sizeof(addrStr));

  net_addr_ntop(AF_INET, &addrPtr->address.in_addr, addrStr, sizeof(addrStr));

  LOG_DBG("Added temporary link-local address: %s", addrStr);

  return 0;
}

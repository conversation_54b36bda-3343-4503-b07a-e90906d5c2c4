#include <zephyr/logging/log.h>
LOG_MODULE_DECLARE(network, CONFIG_APP_NET_LOG_LEVEL);

#include <zephyr/net/net_core.h>
#include <zephyr/net/net_if.h>
#include <zephyr/net/net_stats.h>

#include "network/init.h"
#include "network/rx_lockup_watcher.h"
#include "network/utils.h"

typedef enum {
  // collect initial perf counter values
  kWatcherStateInitial,
  // accumulate deltas
  kWatcherStateAccumulating,
} watcher_worker_state_t;

typedef struct {
  watcher_worker_state_t state;

  // last network perf counters state
  struct net_stats last;
  // number of iterations without RX packets
  size_t rxFailed;

  // Number of times the recovery procedure has been activated (this time around)
  size_t numRecoveries;
  // Total times we've tried recovery
  size_t totalRecoveries;

  // Whether we're in the fault state
  bool faulted;
  // Flag to enable/disable the recovery aspect
  bool recoveryEnabled;
} watcher_state_t;

// How often the lockup watcher thread wakes up to check network interface
#define WATCHER_INTERVAL K_MSEC(7459 * 2)
// Number of intervals without any RX packets before resetting network
#define RX_FAIL_THRESHOLD (10)
// Stack size, in bytes
#define THREAD_STACK_SIZE (1024)
// Priority of the watcher thread
#define THREAD_PRIORITY K_PRIO_PREEMPT(14)
// Maximum number of times we'll try to recover the network before giving up
#define MAX_RECOVERY_ATTEMPTS (2)

static void rx_lockup_watcher_main();
static void reset_state_machine();

// state for the watcher
__dtcm_bss_section static watcher_state_t gState;
// worker thread for rx watcher
K_THREAD_DEFINE(gRxLockupWatcherThread, THREAD_STACK_SIZE, rx_lockup_watcher_main, NULL, NULL, NULL, THREAD_PRIORITY, 0,
                K_TICKS_FOREVER);

/**
 * @brief Start the Ethernet receive lockup watcher thread
 */
int network_rx_watcher_start() {
  // recovery is disabled for now; expect to do it via serial/OOB
  gState.recoveryEnabled = false;

  // start the worker thread
  k_thread_name_set(gRxLockupWatcherThread, "rx lockup watcher");
  k_thread_start(gRxLockupWatcherThread);

  return 0;
}

/**
 * @brief Has an RX fault been detected?
 */
bool network_rx_watcher_is_faulted() { return gState.faulted; }

/**
 * @brief Get the number of times the recovery procedure has been activated
 */
size_t network_rx_watcher_get_num_recoveries() { return gState.totalRecoveries; }

/**
 * @brief Configure whether automatic network recovery is enabled
 *
 * This entails the automatic restart of the network switch and network stack if we fail to
 * receive packets.
 */
int network_rx_watcher_set_recovery_enabled(const bool isEnabled) {
  gState.recoveryEnabled = isEnabled;

  return 0;
}

/**
 * @brief Main loop of the lockup detector task
 *
 * This periodically queries the performance counters for the network interface, and looks at the
 * delta in received packets (ignoring overflow) here. If for more than a certain interval that
 * delta remains at zero, the network interface is presumed to have hung and will be reset.
 */
static void rx_lockup_watcher_main() {
  struct net_stats data;
  int err;

  // initialize state machine
  gState.state = kWatcherStateInitial;
  reset_state_machine();

  do {
    net_mgmt(NET_REQUEST_STATS_GET_ALL, NULL, &data, sizeof(data));

    // calculate deltas, then copy the stats over
    if (gState.state == kWatcherStateAccumulating) {
      uint64_t delta = data.ipv4.recv - gState.last.ipv4.recv;
      LOG_DBG("rx packets: %llu", delta);

      if (delta) {
        gState.rxFailed = 0;
        gState.faulted = false;
        gState.numRecoveries = 0;
      } else {
        gState.rxFailed++;
        LOG_WRN("%u cycles without RX packets (recv = %u)", gState.rxFailed, data.ipv4.recv);

        // handle fault state
        if (gState.rxFailed > RX_FAIL_THRESHOLD) {
          gState.faulted = true;

          // power cycle network if recovery is enabled
          if (gState.recoveryEnabled) {
            if (gState.numRecoveries > MAX_RECOVERY_ATTEMPTS) {
              LOG_ERR("exceeded maximum network recovery attempts, doing nothing!");
              goto beach;
            }

            LOG_WRN("resetting network (recovery attempt %u, %u)", (gState.numRecoveries + 1),
                    (gState.totalRecoveries + 1));
            k_sleep(K_SECONDS(1));

            err = network_do_power_cycle();
            if (err) {
              LOG_ERR("%s failed: %d", "network_do_power_cycle", err);
            }

          beach:;
            gState.numRecoveries++;
            gState.totalRecoveries++;
            gState.state = kWatcherStateInitial;
            continue;
          }
        }
      }
    }

    k_sleep(WATCHER_INTERVAL);

    // state transitions
    if (gState.state == kWatcherStateInitial) {
      reset_state_machine();
      gState.state = kWatcherStateAccumulating;
    }

    gState.last = data;
  } while (true);
}

/**
 * @brief Clear the state of the state machine
 */
static void reset_state_machine() {
  gState.rxFailed = 0;
  memset(&gState.last, 0, sizeof(gState.last));
}

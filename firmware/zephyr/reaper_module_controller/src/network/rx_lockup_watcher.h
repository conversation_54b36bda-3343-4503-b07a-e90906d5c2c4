/**
 * @file
 *
 * @brief Network RX lockup watcher
 *
 * This is a thread that periodically checks that we've received RX packets. Sometimes the network
 * stack gets into a weird state where it doesn't pass RX packets anymore; in that case we power
 * cycle the network switch to force a link cycle, which resolves it.
 *
 * Added note: Turns out this is true only in some cases. There are two situations where the
 * network stack gets into this state with the same symptom, one of which can be cleared by this
 * link cycling, another requires a SoC reset.
 */
#pragma once

int network_rx_watcher_start();

bool network_rx_watcher_is_faulted();
size_t network_rx_watcher_get_num_recoveries();
int network_rx_watcher_set_recovery_enabled(const bool isEnabled);

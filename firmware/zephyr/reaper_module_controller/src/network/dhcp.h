/**
 * @file
 * @brief DHCPv4 client
 *
 * This is a small custom implementation of a DHCPv4 client.
 *
 * We cannot use the Zephyr built-in one since it does not play nice with having multiple IP
 * addresses on the same interface, and likewise doesn't support the MAC changing we do. This
 * is probably not exactly RFC compliant but it works enough[citation needed]
 *
 * Specifically, the following behaviors diverge from the RFC:
 *
 * - Only a single server is supported; the client will immediately send a DHCPREQUEST to the
 *   first server from which it receives an offer after sending a DHCPDISCOVER.
 * - No support for DHCPRELEASE
 * - No support for option overloading (e.g. overflow options into sname/file)
 * - No support for options > 255 bytes (e.g. repeated options)
 * - Only supports a single upstream gateway/router (e.g. DHCP option 3)
 */
#pragma once

#include <zephyr/net/net_if.h>

int network_dhcp_start(struct net_if *);
int network_dhcp_request();

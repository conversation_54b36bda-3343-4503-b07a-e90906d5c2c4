#include <zephyr/logging/log.h>
LOG_MODULE_DECLARE(network, CONFIG_APP_NET_LOG_LEVEL);

#include <string.h>
#include <zephyr/net/hostname.h>
#include <zephyr/net/net_if.h>
#include <zephyr/net/net_ip.h>
#include <zephyr/net/socket.h>
#include <zephyr/random/rand32.h>
#include <zephyr/sys/byteorder.h>

#include <utils/handle_errors.h>

#include "network/dhcp.h"
#include "network/dhcp_types.h"
#include "network/init.h"
#include "network/utils.h"

/// Priority of the DHCP client thread
#define THREAD_PRIORITY K_PRIO_PREEMPT(12)

/// Flag to output all DHCP RX/TX packets to log (debug level)
#define LOG_DHCP_PACKETS (0)
/// Destination port for outgoing packets
#define UDP_SEND_PORT (67)
/// Client listening port
#define UDP_LISTEN_PORT (68)

// Maximum DHCP packet size
#define MAX_PACKET_SIZE (1000)
// Maximum length of DHCP options for tx packet
#define MAX_TX_OPTIONS_SIZE (MAX_PACKET_SIZE - offsetof(dhcp_msg_t, options))
// Number of RX buffers to allocate
#define NUM_RX_BUFFERS (3)
// Maximum packet size (receive)
#define MAX_RX_PACKET_SIZE (MAX_PACKET_SIZE)

// How often DHCPDISCOVER packets are broadcast
#define DHCP_DISCOVER_INTERVAL (K_SECONDS(5))
// Timeout waiting for reply to DHCPREQUEST
#define DHCP_REQUEST_TIMEOUT (K_SECONDS(5))
// How often we attempt to renew DHCP lease
#define DHCP_RENEW_INTERVAL (K_SECONDS(60))
// How long after link goes up to send the initial DHCPDISCOVER
#define DHCP_LINK_UP_DELAY (K_SECONDS(2))

// Mask of events to register for notifications for on the interface
#define IFACE_MGMT_EVENT_MASK (NET_EVENT_IF_DOWN | NET_EVENT_IF_UP)

typedef enum dhcp_stage {
  kDhcpStageIdle,
  /**
   * @brief Identify DHCP servers
   *
   * Repeatedly send a DHCPDISCOVER message to find DHCP servers on the network. When a
   * server has been located (via DHCPOFFER), process the received message.
   */
  kDhcpStageDiscover,
  /**
   * @brief Handle IP address offer
   *
   * Based on the previously received offer, send a DHCPREQUEST until the server that issued the
   * address responds, either with a DHCPACK or DHCPNAK.
   */
  kDhcpStageOffer,
  /**
   * @brief DHCP configuration complete
   *
   * We've acquired all IP parameters required, and the address has been bound.
   *
   * Set up a timer that will handle renewing the address as needed.
   */
  kDhcpStageBound,

  /**
   * @brief Renew bound address
   *
   * Send a DHCPREQUEST to renew the current lease. We should receive a DHCPACK in response
   * here. This process will happen repeatedly until the lease is either renewed or has
   * expired, in which case we'll go back to the "discover" stage.
   *
   * This stage is entered automagically via timer that's triggered after half of the lease
   * duration.
   */
  kDhcpStageRenew,
} dhcp_stage_t;

// Result values for linkStateChange event
typedef enum {
  kDhcpLinkUpNotification,
  kDhcpLinkDownNotification,
} dhcp_link_notification_t;

typedef struct dhcp_state {
  struct {
    // DHCP internal timer expired
    struct k_poll_signal timerExpired;
    // Interface state changed
    struct k_poll_signal linkStateChange;
  } events;

  // socket and friends
  struct {
    // Network interface to be used
    struct net_if *interface;
    // Callback for interface link state changes
    struct net_mgmt_event_callback linkCallback;

    // DHCP listening socket
    int listenSock;

    // Kernel slab allocator used for rx buffers
    struct k_mem_slab rxAllocator;
    // Transmit buffer
    uint8_t txBuffer[MAX_PACKET_SIZE];
  } net;

  // state machine
  struct {
    // Random transaction id (generated at boot)
    uint32_t xid;
    // current state of the state machine
    dhcp_stage_t stage;
  } state;

  // Information about the current lease
  struct {
    // Server from which we received the lease
    struct in_addr server;

    // Address that was assigned to us
    struct in_addr address;
    // Subnet mask provided by the server
    struct in_addr subnet;
    // Upstream gateway/router for server
    struct in_addr router;

    // Time point at which the lease was acquired (expiration is relative to this)
    int64_t acquiredAt;
    // Lease duration, in seconds
    uint32_t duration;
  } lease;
} dhcp_state_t;

typedef struct rx_packet {
  // Start of packet buffer (pass to release_rx_buffer when done)
  void *buffer;
  // Number of received bytes
  size_t length;

  // Address of the sender (for replies)
  struct sockaddr_in source;
} rx_packet_t;

typedef struct tx_packet {
  // Start of the message buffer
  dhcp_msg_t *msg;
  // Number of bytes of options used
  size_t optionBytes;
} tx_packet_t;

static void dhcp_worker();
static void dhcp_socket();
static int init_kernel_objects();
static void update_xid();
static void dhcp_timer_tick(struct k_timer *);

static void *get_rx_buffer();
static void release_rx_buffer(void *);

static int handle_rx_packet(const rx_packet_t *packet);
static int handle_rx_packet_offer(const rx_packet_t *packet);
static int handle_rx_packet_ack(const rx_packet_t *packet);
static int validate_rx_packet(const rx_packet_t *packet, bool *outIgnored);
static int get_packet_option(const rx_packet_t *packet, const dhcp_option_t option, void *dataBuf,
                             const size_t dataBufLength);

static int handle_timer();
static int handle_lease_expiration();
static int remove_dhcp_addr();

static int dhcp_start(k_timeout_t delay);
static int handle_link_change(const bool isUp);

static int get_tx_packet(tx_packet_t *outPacket, const dhcp_msg_type_t type);
static int fill_packet_header(tx_packet_t *msg, const dhcp_msg_type_t type);
static int add_packet_option(tx_packet_t *msg, const dhcp_option_t option, const void *optionData,
                             const size_t optionLength);
static int finalize_packet(tx_packet_t *msg);
static int send_packet(tx_packet_t *msg, const struct in_addr *unicastAddr);

static int send_discover();
static int send_request(const rx_packet_t *offer, const bool isRenewal);

static int remove_interface_addrs(const bool autoip, const bool dhcp);

static void dhcp_link_mgmt_callback(struct net_mgmt_event_callback *cb, uint32_t mgmt_event, struct net_if *iface);

// State for the DHCP client
__dtcm_bss_section static dhcp_state_t gState;
// Socket thread
K_THREAD_DEFINE(gNetDhcpSocket, 1536, dhcp_socket, NULL, NULL, NULL, THREAD_PRIORITY, 0, K_TICKS_FOREVER);
// Worker thread
K_THREAD_DEFINE(gNetDhcpWorker, 2048, dhcp_worker, NULL, NULL, NULL, THREAD_PRIORITY, 0, K_TICKS_FOREVER);
// timer (for renewal/discover)
K_TIMER_DEFINE(gDhcpTimer, dhcp_timer_tick, NULL);

// Message queue of received packets
K_MSGQ_DEFINE(gRxQueue, sizeof(rx_packet_t), NUM_RX_BUFFERS, 2);

/**
 * @brief Start the DHCP client
 *
 * This will launch the worker task for the DHCP client, which will start working on the specified
 * interface. We'll also set up the listening socket (broadcast mode) also.
 *
 * @remark After the DHCP worker starts, you should call network_dhcp_request to request an address.
 */
int network_dhcp_start(struct net_if *interface) {
  int err;
  struct sockaddr_in bindAddr;

  // set up kernel objects + some state
  // TODO: do this only once
  HANDLE_UNLIKELY(init_kernel_objects());
  update_xid();

  // allocate and bind listening socket
  gState.net.interface = interface;

  err = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
  if (err == -1) {
    LOG_WRN("%s failed: %d", "socket", errno);
    return -errno;
  }
  gState.net.listenSock = err;

  memset(&bindAddr, 0, sizeof(bindAddr));
  bindAddr.sin_addr.s_addr = INADDR_ANY;
  bindAddr.sin_family = AF_INET;
  bindAddr.sin_port = htons(UDP_LISTEN_PORT);

  err = bind(gState.net.listenSock, (struct sockaddr *)&bindAddr, sizeof(bindAddr));
  if (err == -1) {
    LOG_WRN("%s failed: %d", "bind socket", errno);
    return -errno;
  }

  // start worker task
  k_thread_start(gNetDhcpWorker);
  k_thread_start(gNetDhcpSocket);

  return 0;
}

/**
 * @brief Place DHCP client state machine into "request" state
 *
 * This will begin address acquisition anew. Once in this state, the DHCP client will keep trying
 * to acquire an address indefinitely.
 */
int network_dhcp_request() { return dhcp_start(K_MSEC(500)); }

/**
 * @brief Actually start DHCP discover state
 *
 * @param delay Time delay before sending the first DHCPDISCOVER
 */
static int dhcp_start(k_timeout_t delay) {
  // reset DHCP state machine
  gState.state.stage = kDhcpStageDiscover;
  update_xid();

  // start the timer
  k_timer_start(&gDhcpTimer, delay, DHCP_DISCOVER_INTERVAL);

  return 0;
}

/**
 * @brief Set up all of the kernel objects for the DHCP client
 *
 * This includes poll signal, timers and message queues and the worker thread.
 */
static int init_kernel_objects() {
  k_thread_name_set(gNetDhcpWorker, "DHCP client");
  k_thread_name_set(gNetDhcpSocket, "DHCP socket worker");

  // poll signals
  k_poll_signal_init(&gState.events.timerExpired);
  k_poll_signal_init(&gState.events.linkStateChange);

  // memory pools
  static uint8_t __aligned(4) gRxSlabBuffer[NUM_RX_BUFFERS * MAX_RX_PACKET_SIZE];
  k_mem_slab_init(&gState.net.rxAllocator, gRxSlabBuffer, MAX_RX_PACKET_SIZE, NUM_RX_BUFFERS);

  return 0;
}

/**
 * @brief Update the DHCP transaction ID
 *
 * A random nonzero integer value is generated.
 */
static void update_xid() {
  do {
    gState.state.xid = sys_rand32_get();
  } while (!gState.state.xid);
  LOG_DBG("DHCP XID = %08x", gState.state.xid);
}

/**
 * @brief Timer callback
 *
 * This signals the "timer expired" event that the DHCP worker task polls on.
 */
static void dhcp_timer_tick(struct k_timer *) { k_poll_signal_raise(&gState.events.timerExpired, 1); }

/**
 * @brief DHCP client worker loop
 *
 * At this point the listening socket should already have been set up. We will set up to poll for
 * incoming packets as well as our work queue.
 */
static void dhcp_worker() {
  enum event_idx {
    kEventTimer = 0,
    kEventListenSocket = 1,
    kEventLinkStateChange = 2,
    kMaxEvent,
  };

  int err;
  struct k_poll_event events[kMaxEvent] = {
      K_POLL_EVENT_INITIALIZER(K_POLL_TYPE_SIGNAL, K_POLL_MODE_NOTIFY_ONLY, &gState.events.timerExpired),
      K_POLL_EVENT_INITIALIZER(K_POLL_TYPE_MSGQ_DATA_AVAILABLE, K_POLL_MODE_NOTIFY_ONLY, &gRxQueue),
      K_POLL_EVENT_INITIALIZER(K_POLL_TYPE_SIGNAL, K_POLL_MODE_NOTIFY_ONLY, &gState.events.linkStateChange),
  };

  // install handler for network link state change
  net_mgmt_init_event_callback(&gState.net.linkCallback, dhcp_link_mgmt_callback, IFACE_MGMT_EVENT_MASK);
  net_mgmt_add_event_callback(&gState.net.linkCallback);

  // sit forever, to process
  LOG_INF("DHCP client started");

  do {
    err = k_poll(events, ARRAY_SIZE(events), K_FOREVER);
    if (err) {
      LOG_WRN("%s failed: %d", "k_poll", err);
      continue;
    }

    // received a packet on the socket?
    if (events[kEventListenSocket].state == K_POLL_STATE_MSGQ_DATA_AVAILABLE) {
      int err;
      rx_packet_t packet;

      do {
        err = k_msgq_get(&gRxQueue, &packet, K_NO_WAIT);
        if (err) {
          if (err != -ENOMSG) {
            LOG_WRN("failed to get rx packet: %d", err);
          }
          break;
        }

#if LOG_DHCP_PACKETS
        LOG_HEXDUMP_DBG(packet.buffer, packet.length, "DHCP RX");
#endif

        err = handle_rx_packet(&packet);
        if (err) {
          LOG_WRN("%s failed: %d", "handle_rx_packet", err);
        }

        // release packet
        release_rx_buffer(packet.buffer);
      } while (!err);

      // reset event
      events[kEventListenSocket].state = K_POLL_STATE_NOT_READY;
    }

    // handle timer expiration
    if (events[kEventTimer].signal->signaled) {
      err = handle_timer();
      if (err) {
        LOG_WRN("%s failed: %d", "handle_timer", err);
      }

      k_poll_signal_reset(events[kEventTimer].signal);
      events[kEventTimer].state = K_POLL_STATE_NOT_READY;
    }

    // link state change
    if (events[kEventLinkStateChange].signal->signaled) {
      const bool up = events[kEventLinkStateChange].signal->result == kDhcpLinkUpNotification;

      err = handle_link_change(up);
      if (err) {
        LOG_WRN("%s failed: %d", "handle_link_change", err);
      }

      k_poll_signal_reset(events[kEventLinkStateChange].signal);
      events[kEventLinkStateChange].state = K_POLL_STATE_NOT_READY;
    }
  } while (true);
}

/**
 * @brief DHCP socket worker
 *
 * Continuously handles receiving packets from the socket, and posts them on the receive queue to
 * the worker thread. This is required as we cannot use sockets in a k_poll call.
 */
static void dhcp_socket() {
  int err;
  ssize_t size;
  struct sockaddr_in srcAddr;
  size_t srcAddrLen;

  LOG_DBG("DHCP socket listener started");

  do {
    // get receive buffer
    void *buf = get_rx_buffer();
    if (!buf) {
      LOG_WRN("failed to acquire rx buffer!");
      // we'll probably, hopefully have processed one packet by then?
      k_sleep(K_MSEC(250));
      continue;
    }

    // get packet into it
    srcAddrLen = sizeof(srcAddr);
    size = recvfrom(gState.net.listenSock, buf, MAX_RX_PACKET_SIZE, 0, (struct sockaddr *)&srcAddr, &srcAddrLen);
    if (size == -1) {
      LOG_WRN("%s failed: %d", "recvfrom", errno);
      release_rx_buffer(buf);
      continue;
    }

    // send to worker task
    rx_packet_t packet;

    packet.buffer = buf;
    packet.length = size;
    memcpy(&packet.source, &srcAddr, sizeof(srcAddr));

    err = k_msgq_put(&gRxQueue, &packet, K_NO_WAIT);
    if (err) {
      LOG_WRN("failed to submit rx packet: %d", err);
      release_rx_buffer(buf);
    }
  } while (true);
}

/**
 * @brief Get a receive buffer for the next packet
 */
static void *get_rx_buffer() {
  int err;
  void *ptr = NULL;

  err = k_mem_slab_alloc(&gState.net.rxAllocator, &ptr, K_NO_WAIT);
  if (!err) {
    return ptr;
  } else {
    LOG_WRN("failed to allocate DHCP RX buffer: %d", err);
    return NULL;
  }
}

/**
 * @brief Mark the receive buffer as available for reuse
 */
static void release_rx_buffer(void *bufPtr) { k_mem_slab_free(&gState.net.rxAllocator, &bufPtr); }

/**
 * @brief Handle a received packet
 */
static int handle_rx_packet(const rx_packet_t *packet) {
  int err;
  bool ignored = true;
  uint8_t msgType;

  // validate packet and pull out type
  HANDLE_UNLIKELY(validate_rx_packet(packet, &ignored));
  if (ignored) {
    LOG_HEXDUMP_DBG(packet->buffer, packet->length, "ignoring valid DHCP packet");
    return 0;
  }

  err = get_packet_option(packet, kDhcpOptionMessageType, &msgType, sizeof(msgType));
  if (err < 0) {
    LOG_WRN("get_packet_option(%s) failed: %d", "kDhcpOptionMessageType", err);
    return err;
  }

  switch (gState.state.stage) {
  /*
   * Awaiting server response to DHCPDISCOVER
   *
   * Ignore any received messages of other types; the first packet of the correct type is
   * inspected to gather the information for the next stage. We'll use that to send a
   * DHCPREQUEST after this.
   */
  case kDhcpStageDiscover:
    if (msgType != kDhcpMsgOffer) {
      LOG_WRN("invalid DHCP message type: %u, expected %u", msgType, kDhcpMsgDiscover);
      return -EINVAL;
    }

    return handle_rx_packet_offer(packet);

  /*
   * Sent DHCPREQUEST, awaiting DHCPACK/DHCPNACK
   *
   * Wait for the server whose offer we accepted to respond: we should receive either a
   * DHCPACK or DHCPNAK. In the former case, the configuration is applied and we
   * transition into the "bound" state. Otherwise, after timeout period, we'll go back
   * to the discover stage.
   *
   * This is the same codepath used by the "renew lease" stage, except on timeout we will
   * just retry renewing the lease.
   */
  case kDhcpStageRenew:
  case kDhcpStageOffer:
    // stop timer (in case we were timing out waiting for ACK/NAK)
    k_timer_stop(&gDhcpTimer);

    // handle NAK
    if (msgType == kDhcpMsgNak) {
      // TODO: better handling here?
      LOG_WRN("received DHCPNAK, restarting!");
      return network_dhcp_request();
    }
    // handle ACK
    else if (msgType == kDhcpMsgAck) {
      return handle_rx_packet_ack(packet);
    } else {
      LOG_WRN("invalid DHCP message type: %u, expected %u", msgType, kDhcpMsgAck);
      return -EINVAL;
    }
    break;

  /*
   * When in the bound state, ignore received packets. There are no packets that we are
   * interested in until it's renewal time.
   */
  case kDhcpStageBound:
    break;

  default:
    LOG_HEXDUMP_WRN(packet->buffer, packet->length, "unhandled DHCP packet");
    break;
  }
  return 0;
}

/**
 * @brief Process DHCPOFFER packet
 *
 * We've already validated the packet is the appropriate type at this point; pull out the server and
 * IP configuration, store it, and generate a DHCPREQUEST.
 */
static int handle_rx_packet_offer(const rx_packet_t *packet) {
  int err;
  const dhcp_msg_t *msg = (const dhcp_msg_t *)packet->buffer;

  // IP address of DHCP server
  struct in_addr server;
  // offered IP address configuration
  struct in_addr offerAddr, subnet, router;
  // lease duration, in seconds
  uint32_t leaseDuration = 0;

  memset(&server, 0, sizeof(server));
  memset(&offerAddr, 0, sizeof(offerAddr));
  memset(&subnet, 0, sizeof(subnet));
  memset(&router, 0, sizeof(router));

  // retrieve information
  offerAddr.s_addr = msg->yiaddr;

  // lease duration and server identifier are mandatory in DHCPOFFER
  err = get_packet_option(packet, kDhcpOptionLeaseTime, &leaseDuration, sizeof(leaseDuration));
  if (err < 0) {
    LOG_WRN("get_packet_option(%s) failed: %d", "kDhcpOptionLeaseTime", err);
    return err;
  }
  leaseDuration = sys_be32_to_cpu(leaseDuration);

  err = get_packet_option(packet, kDhcpOptionServerId, &server.s_addr, sizeof(server.s_addr));
  if (err < 0) {
    LOG_WRN("get_packet_option(%s) failed: %d", "kDhcpOptionServerId", err);
    return err;
  }

  // subnet mask and upstream gateway may be absent; we also only handle a single router
  err = get_packet_option(packet, kDhcpOptionSubnetMask, &subnet.s_addr, sizeof(subnet.s_addr));
  if (err < 0) {
    LOG_WRN("get_packet_option(%s) failed: %d", "kDhcpOptionSubnetMask", err);
  }

  err = get_packet_option(packet, kDhcpOptionRouters, &router.s_addr, sizeof(router.s_addr));
  if (err < 0) {
    LOG_WRN("get_packet_option(%s) failed: %d", "kDhcpOptionRouters", err);
  }

  /*
   * If the packet had all of the afforementioned data, select this server's offer. We'll respond
   * with a DHCPREQUEST after the IP address has been applied.
   */
  // store information
  gState.lease.server = server;
  gState.lease.address = offerAddr;
  gState.lease.subnet = subnet;
  gState.lease.router = router;

  gState.lease.acquiredAt = k_uptime_get();
  gState.lease.duration = leaseDuration;

  // send a DHCPREQUEST and prepare timeout
  HANDLE_UNLIKELY(send_request(packet, false));
  k_timer_start(&gDhcpTimer, DHCP_REQUEST_TIMEOUT, K_NO_WAIT);

  // then transition to the next state
  gState.state.stage = kDhcpStageOffer;

  // print the info
  {
    char serverStr[INET_ADDRSTRLEN], offerStr[INET_ADDRSTRLEN], subnetStr[INET_ADDRSTRLEN], routerStr[INET_ADDRSTRLEN];

    net_addr_ntop(AF_INET, &server, serverStr, sizeof(serverStr));
    net_addr_ntop(AF_INET, &offerAddr, offerStr, sizeof(offerStr));
    net_addr_ntop(AF_INET, &subnet, subnetStr, sizeof(subnetStr));
    net_addr_ntop(AF_INET, &router, routerStr, sizeof(routerStr));

    LOG_INF("Received offer from %s: %s/%s, router %s, expires in %u secs", serverStr, offerStr, subnetStr, routerStr,
            leaseDuration);
  }

  return 0;
}

/**
 * @brief Process DHCPACK packet
 */
static int handle_rx_packet_ack(const rx_packet_t *packet) {
  const dhcp_msg_t *msg = (const dhcp_msg_t *)packet->buffer;
  struct in_addr address;
  struct net_if_addr *addrPtr;
  bool isRenewal = true;

  // get the address the server offered us
  address.s_addr = msg->yiaddr;

  char offerStr[INET_ADDRSTRLEN];
  net_addr_ntop(AF_INET, &address, offerStr, sizeof(offerStr));

  /*
   * Apply the IP address confguration.
   *
   * This is skipped if this was a renewal of the existing address, but only if the server
   * returned the same address as before.
   */
  if (gState.state.stage == kDhcpStageOffer || address.s_addr != gState.lease.address.s_addr) {
    char oldAddrStr[INET_ADDRSTRLEN];
    net_addr_ntop(AF_INET, &gState.lease.address, oldAddrStr, sizeof(oldAddrStr));

    LOG_DBG("DHCP address change: %s -> %s", oldAddrStr, offerStr);

    // remove old DHCP/autoconf address
    HANDLE_UNLIKELY(remove_interface_addrs(true, true));

    // add new address
    addrPtr = net_if_ipv4_addr_add(gState.net.interface, &address, NET_ADDR_DHCP, 0);
    if (!addrPtr) {
      LOG_WRN("%s failed", "net_if_ipv4_addr_add");
      return -EINVAL;
    }

    // TODO: do stuff with router, netmask data?

    gState.lease.address = address;
    isRenewal = false;
  }

  /*
   * Set up a timer to renew the lease. We start renewing after half the lease duration has
   * expired.
   *
   * Then transition to the bound state.
   */
  const size_t renewInSecs = gState.lease.duration / 2;
  k_timer_start(&gDhcpTimer, K_SECONDS(renewInSecs), DHCP_RENEW_INTERVAL);

  gState.state.stage = kDhcpStageBound;
  LOG_INF("DHCP %s to address %s (renewing in %u sec)", (isRenewal ? "rebound" : "bound"), offerStr, renewInSecs);

  // invoke network stack callback
  return network_dhcp_callback();
}

/**
 * @brief Validate and inspect received DHCP packet
 *
 * This ensures that the packet is validly formed; then we'll check to ensure that it's actually
 * directed towards us (e.g. not a broadcast for another node)
 *
 * @param outIgnored Bool flag set if the packet is valid, but should be ignored
 */
static int validate_rx_packet(const rx_packet_t *packet, bool *outIgnored) {
  const dhcp_msg_t *msg = (const dhcp_msg_t *)packet->buffer;

  /*
   * Validate that the DHCP packet is valid:
   *
   * - Large enough to fit a complete DHCP frame
   * - BOOTP opcode must be BOOTREQUEST or BOOTREPLY
   * - Ensure magic cookie is valid
   */
  if (packet->length < sizeof(dhcp_msg_t)) {
    LOG_WRN("packet too short (%u bytes)", packet->length);
    return -EMSGSIZE;
  } else if (msg->opcode != BOOTP_OPCODE_BOOTREQUEST && msg->opcode != BOOTP_OPCODE_BOOTREPLY) {
    LOG_WRN("invalid BOOTP opcode (%02x)", msg->opcode);
    return -EINVAL;
  } else if (msg->cookie != DHCP_MAGIC_COOKIE) {
    LOG_WRN("invalid magic cookie (%08x)", msg->cookie);
    return -EINVAL;
  }

  /*
   * Once we've established that the received packet is valid, check the following to ensure it
   * is actually destined for us:
   *
   * - Compare client hardware address; must ALWAYS match.
   * - Transaction id (xid) must match
   */
  const struct net_linkaddr *mac = net_if_get_link_addr(gState.net.interface);
  if (!mac) {
    return -ENODEV;
  }

  if (msg->hwaddrType != BOOTP_HWTYPE_ETHERNET || msg->hwaddrLen != mac->len ||
      memcmp(msg->chaddr, mac->addr, mac->len)) {
    LOG_DBG("hwaddr mismatch (type=%u, len=%u)", msg->hwaddrType, msg->hwaddrLen);

    *outIgnored = true;
    return 0;
  }

  if (sys_be32_to_cpu(msg->xid) != gState.state.xid) {
    LOG_DBG("xid mismatch (expected %08x, got %08x)", gState.state.xid, sys_be32_to_cpu(msg->xid));

    *outIgnored = true;
    return 0;
  }

  // otherwise, the packet is valid _and_ for us
  *outIgnored = false;
  return 0;
}

/**
 * @brief Search for an option and copy its value
 *
 * Iterate over the options section of the DHCP packet to locate the specified option; then copy
 * out its value if found.
 *
 * @param option Option to look for
 * @param dataBuf Buffer to receive the option payload data
 * @param dataBufLength Length of the output buffer, in bytes
 *
 * @return Number of bytes copied out, or a negative error code (such as -ENOENT if no such option)
 */
static int get_packet_option(const rx_packet_t *packet, const dhcp_option_t option, void *dataBuf,
                             const size_t dataBufLength) {
  // validate inputs
  if (!packet || !dataBuf) {
    return -EFAULT;
  } else if (option == kDhcpOptionEnd || option == kDhcpOptionPad) {
    return -EINVAL;
  } else if (!dataBufLength) {
    return -EINVAL;
  }

  // then begin to iterate over all options
  const dhcp_msg_t *msg = (const dhcp_msg_t *)packet->buffer;
  const dhcp_option_header_t *opt = (const dhcp_option_header_t *)msg->options;
  while (((uintptr_t)opt) < (((uintptr_t)packet->buffer) + packet->length - sizeof(*opt))) {
    LOG_DBG("opt(%p) = %3u,%u bytes", opt, opt->option, opt->length);

    // found correct option; copy out data
    if (opt->option == option) {
      // TODO: also verify that we wouldn't read past the bounds of the packet
      if (opt->length > dataBufLength) {
        LOG_WRN("need %u bytes for option %02x, but buffer is only %u", opt->length, option, dataBufLength);
        return -ENOMEM;
      }

      memcpy(dataBuf, opt->payload, opt->length);
      return opt->length;
    }

    // special case single byte options
    if (opt->option == kDhcpOptionEnd || opt->option == kDhcpOptionPad) {
      opt = (const dhcp_option_header_t *)(((uintptr_t)opt) + 1);
    }
    // otherwise, skip hdr + payload
    else {
      opt = (const dhcp_option_header_t *)(((uintptr_t)opt) + sizeof(*opt) + opt->length);
    }
  }

  // if we get here, option not found
  return -ENOENT;
}

/**
 * @brief Process DHCP timer expiration
 *
 * This timer is used for retransmission of packets and to drive the renewal process.
 */
static int handle_timer() {
  switch (gState.state.stage) {
  /*
   * Send a DHCPDISCOVER packet
   *
   * This is called repeatedly (frequency defined by `DHCP_DISCOVER_INTERVAL`) from a timer
   * until we get a response, at which time the timer is stopped.
   */
  case kDhcpStageDiscover:
    LOG_DBG("sending DHCPDISCOVER");
    return send_discover();

  /*
   * Timeout expired waiting for DHCPACK/DHCPNAK
   *
   * A timer is started when we send the DHCPREQUEST (using `DHCP_ACK_TIMEOUT`) and if no
   * response is received in that time, we restart DHCP process.
   */
  case kDhcpStageOffer:
    LOG_WRN("Timed out waiting for DHCPACK, restarting!");
    return network_dhcp_request();

  /*
   * Reached half the lease expiration
   *
   * After the initial DHCPACK from the server, a timer is set for half the lease expiration
   * to star the renewal process. This will repeatedly retry renewing the DHCP lease until
   * it expires.
   */
  case kDhcpStageBound:
    char serverStr[INET_ADDRSTRLEN];
    net_addr_ntop(AF_INET, &gState.lease.server, serverStr, sizeof(serverStr));
    LOG_INF("Reached T1, renewing lease (from %s)", serverStr);

    k_timer_start(&gDhcpTimer, DHCP_RENEW_INTERVAL, DHCP_RENEW_INTERVAL);
    gState.state.stage = kDhcpStageRenew;

    // NOTE: fallthrough to "renew" stage to send packet

  /*
   * Renewal timer expiration
   *
   * Send a DHCPREQUEST to renew our address. This will continue perpetually until we either
   * accomplish renewing the lease or the lease expires, at which point we'll go back to the
   * discovery stage.
   */
  case kDhcpStageRenew:
    // check for lease expiration
    int64_t reftime = gState.lease.acquiredAt;
    const int64_t elapsed = k_uptime_delta(&reftime);

    if ((elapsed / MSEC_PER_SEC) >= gState.lease.duration) {
      LOG_DBG("DHCP lease expired (elapsed %llu msec, lease duration %u sec)", elapsed, gState.lease.duration);

      HANDLE_UNLIKELY(handle_lease_expiration());
      return 0;
    }

    // send an unicast DHCPREQUEST
    return send_request(NULL, true);

  default:
    LOG_WRN("spurious DHCP timer expiration (stage=%u)", gState.state.stage);
    break;
  }

  return 0;
}

/**
 * @brief DHCP lease expired
 *
 * This will remove the DHCP address from the interface, and add back an AutoIP address (if no
 * other addresses remain) and restart the DHCP process.
 */
static int handle_lease_expiration() {
  // remove the DHCP address from the interface
  HANDLE_UNLIKELY(remove_dhcp_addr());

  // call into remaining network stack
  HANDLE_UNLIKELY(network_dhcp_callback());

  // then attempt to acquire a new lease
  HANDLE_UNLIKELY(network_dhcp_request());

  return 0;
}

/**
 * @brief Remove DHCP address
 *
 * Remove the DHCP interface address. Add the AutoIP back if this would leave the interface without
 * any addresses.
 */
static int remove_dhcp_addr() {
  // remove old DHCP address
  HANDLE_UNLIKELY(remove_interface_addrs(false, true));

  // add the autoip address if needed (e.g. no other addresses)
  size_t numUsableAddrs = 0;

  struct net_if_ipv4 *oldConfig;
  HANDLE_UNLIKELY(net_if_config_ipv4_get(gState.net.interface, &oldConfig));

  for (size_t i = 0; i < ARRAY_SIZE(oldConfig->unicast); i++) {
    struct net_if_addr *addr = &oldConfig->unicast[i];

    if (addr->addr_type == NET_ADDR_MANUAL || addr->addr_type == NET_ADDR_AUTOCONF) {
      if (addr->address.in_addr.s_addr) {
        numUsableAddrs++;
      }
    }
  }

  if (!numUsableAddrs) {
    LOG_INF("Re-adding link local address (for DHCP)");
    HANDLE_UNLIKELY(network_add_link_local(gState.net.interface));
  }

  return 0;
}

/**
 * @brief Get a packet for transmission
 *
 * We can only have one packet being assembled at a time, so this will zero out the packet,
 * initialize the packet structure and fill in common fields.
 */
static int get_tx_packet(tx_packet_t *outPacket, const dhcp_msg_type_t type) {
  if (!outPacket) {
    return -EFAULT;
  }

  tx_packet_t packet;
  memset(&packet, 0, sizeof(packet));

  // prepare the tx packet buffer
  dhcp_msg_t *msg = (dhcp_msg_t *)gState.net.txBuffer;
  memset(msg, 0, sizeof(*msg));

  packet.msg = msg;
  packet.optionBytes = 0;

  fill_packet_header(&packet, type);

  // then store it in the output
  *outPacket = packet;

  return 0;
}

/**
 * @brief Prepare client->server message
 *
 * @param msg Packet to initialize; assumes the header
 */
static int fill_packet_header(tx_packet_t *msg, const dhcp_msg_type_t type) {
  // general message type
  msg->msg->opcode = BOOTP_OPCODE_BOOTREQUEST;
  msg->msg->numHops = 0;
  msg->msg->xid = gState.state.xid;

  // MAC address
  const struct net_linkaddr *mac = net_if_get_link_addr(gState.net.interface);
  if (mac) {
    msg->msg->hwaddrType = BOOTP_HWTYPE_ETHERNET;
    msg->msg->hwaddrLen = mac->len;

    memcpy(msg->msg->chaddr, mac->addr, MIN(mac->len, sizeof(msg->msg->chaddr)));
  } else {
    LOG_WRN("failed to get MAC address :(");
  }

  /*
   * If we are in the bound or renew state, we have a DHCP IP that  can be used for unicast
   * messages, and should likewise be included in the packet.
   */
  if (gState.state.stage == kDhcpStageBound || gState.state.stage == kDhcpStageRenew) {
    msg->msg->ciaddr = gState.lease.address.s_addr;
  } else {
    msg->msg->flags |= kDhcpFlagBroadcast;
  }

  // DHCP magic cookie and message type
  msg->msg->cookie = DHCP_MAGIC_COOKIE;

  uint8_t typeData = (uint8_t)type;
  HANDLE_UNLIKELY(add_packet_option(msg, kDhcpOptionMessageType, &typeData, sizeof(typeData)));

  // add our hostname
#if IS_ENABLED(CONFIG_NET_HOSTNAME_ENABLE)
  const char *hostname = net_hostname_get();
  if (hostname) {
    const size_t hostnameLen = strlen(hostname);

    if (hostnameLen && hostnameLen < 255) {
      HANDLE_UNLIKELY(add_packet_option(msg, kDhcpOptionHostname, hostname, hostnameLen));
    }
  }
#endif

  return 0;
}

/**
 * @brief Append a DHCP option to the packet
 *
 * Add the specified option (e.g. tag + length + value) to the specified DHCP packet.
 */
static int add_packet_option(tx_packet_t *msg, const dhcp_option_t option, const void *optionData,
                             const size_t optionLength) {
  // you can only write options w/ length here
  if (option == kDhcpOptionPad || option == kDhcpOptionEnd) {
    LOG_WRN("unsupported DHCP option %02x", option);
    return -EINVAL;
  }
  // don't support options > 0xff length
  else if (optionLength > 0xff) {
    return -EINVAL;
  }

  // see if we've got space
  const size_t bytesNeeded = optionLength + sizeof(dhcp_option_header_t);
  if (bytesNeeded + msg->optionBytes >= MAX_TX_OPTIONS_SIZE) {
    LOG_WRN("cannot add DHCP option %02x: need %u bytes, %u available", option, bytesNeeded,
            (MAX_TX_OPTIONS_SIZE - msg->optionBytes));
    return -ENOMEM;
  }

  LOG_DBG("add option(%02x): %u bytes @ %p, need %u total", option, optionLength, optionData, bytesNeeded);

  // write it out
  dhcp_option_header_t *ohdr = (dhcp_option_header_t *)(msg->msg->options + msg->optionBytes);

  ohdr->option = option;
  ohdr->length = optionLength;
  memcpy(ohdr->payload, optionData, optionLength);

  msg->optionBytes += bytesNeeded;

  return 0;
}

/**
 * @brief Prepare a packet for transmission
 *
 * Terminate the DHCP options list and byteswap any header fields that need it
 */
static int finalize_packet(tx_packet_t *packet) {
  if (!packet) {
    return -EFAULT;
  }

  // terminate options list
  if (packet->optionBytes == MAX_TX_OPTIONS_SIZE) {
    return -ENOMEM;
  }

  packet->msg->options[packet->optionBytes++] = kDhcpOptionEnd;

  // byteswap stuff in the header
  packet->msg->xid = sys_cpu_to_be32(packet->msg->xid);
  packet->msg->secs = sys_cpu_to_be16(packet->msg->secs);
  packet->msg->flags = sys_cpu_to_be16(packet->msg->flags);

  return 0;
}

/**
 * @brief Transmit the specified packet
 *
 * This requires that the packet has been previously finalized in order to set up all of the
 * fields inside the packet. The destination address (broadcast vs. unicast to the server) is
 * determined automagically from the contents of the packet.
 *
 * @param unicastAddr If non-NULL, an unicast IP address to direct the packet to
 */
static int send_packet(tx_packet_t *msg, const struct in_addr *unicastAddr) {
  int err;
  struct sockaddr_in dest;

  // determine destination address
  memset(&dest, 0, sizeof(dest));

  if (unicastAddr) {
    dest.sin_addr = *unicastAddr;
  } else {
    // global broadcast address
    dest.sin_addr.s_addr = ~0;
  }

  dest.sin_family = AF_INET;
  dest.sin_port = htons(UDP_SEND_PORT);

  // send it
  size_t msgLen = sizeof(dhcp_msg_t) + msg->optionBytes;

#if LOG_DHCP_PACKETS
  LOG_HEXDUMP_DBG(msg->msg, msgLen, "DHCP TX");
#endif

  err = sendto(gState.net.listenSock, msg->msg, msgLen, 0, (struct sockaddr *)&dest, sizeof(dest));
  if (err == -1) {
    LOG_WRN("failed to send DHCP frame: %d", errno);
    return -errno;
  } else if (err != msgLen) {
    // TODO: probably can handle this case
    LOG_WRN("incomplete write: %u/%u", err, msgLen);
    return -EINVAL;
  }

  return 0;
}

/**
 * @brief Transmit a DHCPDISCOVER
 *
 * Broadcast packet to find all of the DHCP servers on the network.
 */
static int send_discover() {
  tx_packet_t packet;

  // acquire and prepare packet
  HANDLE_UNLIKELY(get_tx_packet(&packet, kDhcpMsgDiscover));

  // add desired options
  static const uint8_t kDesiredOptions[] = {kDhcpOptionSubnetMask, kDhcpOptionRouters, kDhcpOptionSyslog,
                                            kDhcpOptionLeaseTime};
  HANDLE_UNLIKELY(add_packet_option(&packet, kDhcpOptionRequestList, &kDesiredOptions, sizeof(kDesiredOptions)));

  // TODO: need we fill in other fields?

  // send packet
  HANDLE_UNLIKELY(finalize_packet(&packet));
  HANDLE_UNLIKELY(send_packet(&packet, NULL));

  return 0;
}

/**
 * @brief Transmit a DHCPREQUEST
 *
 * Send a broadcast packet to acknowledge a previously received DHCP offer.
 *
 * @remark The internal lease state (e.g. server address, lease address and duration, etc.) must
 *         already be updated prior to this call.
 */
static int send_request(const rx_packet_t *request, const bool isRenewal) {
  tx_packet_t packet;

  // acquire and prepare packet
  HANDLE_UNLIKELY(get_tx_packet(&packet, kDhcpMsgRequest));

  /*
   * When initially acquiring an address, the packet will be broadcast so we need to include
   * server identifier, and put the IP offered to us in the "requested address" field.
   */
  if (!isRenewal) {
    HANDLE_UNLIKELY(add_packet_option(&packet, kDhcpOptionServerId, &gState.lease.server.s_addr,
                                      sizeof(gState.lease.server.s_addr)));

    HANDLE_UNLIKELY(add_packet_option(&packet, kDhcpOptionRequestedAddr, &gState.lease.address.s_addr,
                                      sizeof(gState.lease.address.s_addr)));
    packet.msg->ciaddr = 0;
  }
  /*
   * If in the renewal state, the current address is placed in the "ciaddr" field and the server
   * identifier is omitted. The packet will then be sent unicast to the server.
   */
  else {
    packet.msg->ciaddr = gState.lease.address.s_addr;
  }

  // send packet
  HANDLE_UNLIKELY(finalize_packet(&packet));
  HANDLE_UNLIKELY(send_packet(&packet, isRenewal ? &gState.lease.server : NULL));

  return 0;
}

/**
 * @brief Remove previous (stale) addresses
 *
 * @param autoip Remove AutoIP/autoconf addresses
 * @param dhcp Remove DHCP addresses
 */
static int remove_interface_addrs(const bool autoip, const bool dhcp) {
  struct net_if_ipv4 *oldConfig;
  HANDLE_UNLIKELY(net_if_config_ipv4_get(gState.net.interface, &oldConfig));

  for (size_t i = 0; i < ARRAY_SIZE(oldConfig->unicast); i++) {
    struct net_if_addr *addr = &oldConfig->unicast[i];

    // ignore unused/empty addresses
    if (addr->address.in_addr.s_addr == INADDR_ANY || !addr->is_used) {
      continue;
    }

    // skip based on type (AutoIP or DHCP)
    if (!(addr->addr_type == NET_ADDR_AUTOCONF && autoip) && !(addr->addr_type == NET_ADDR_DHCP && dhcp)) {
      continue;
    }

    char addrStr[INET_ADDRSTRLEN];
    net_addr_ntop(AF_INET, &addr->address.in_addr, addrStr, sizeof(addrStr));
    LOG_DBG("removing address %s", addrStr);

    if (!net_if_ipv4_addr_rm(gState.net.interface, &addr->address.in_addr)) {
      LOG_ERR("failed to remove address %s (type %u)", addrStr, addr->addr_type);
    }
  }

  return 0;
}

/**
 * @brief Link management state callback
 *
 * This is invoked by the OS whenever the link state of the interface the DHCP client is running
 * on changes.
 */
static void dhcp_link_mgmt_callback(struct net_mgmt_event_callback *cb, uint32_t mgmt_event, struct net_if *iface) {
  k_poll_signal_raise(&gState.events.linkStateChange,
                      (mgmt_event == NET_EVENT_IF_UP) ? kDhcpLinkUpNotification : kDhcpLinkDownNotification);
}

/**
 * @brief DHCP worker handler for link state change
 *
 * Respond to the new link state. When the link goes down, DHCP timers are aborted and the client
 * returns to the UNBOUND state. Then when it goes up again, we'll re-enter the DISCOVER state.
 */
static int handle_link_change(const bool isUp) {
  LOG_DBG("link state: %u", isUp);

  // link went down
  if (!isUp) {
    LOG_INF("Link down, resetting DHCP client + removing address");

    // quiesce the client and reset its state
    k_timer_stop(&gDhcpTimer);

    gState.state.stage = kDhcpStageDiscover;

    // then we must remove the DHCP address
    HANDLE_UNLIKELY(remove_dhcp_addr());
  }
  // link went up
  else {
    LOG_INF("Link up, restarting DISCOVER");

    HANDLE_UNLIKELY(dhcp_start(DHCP_LINK_UP_DELAY));
  }

  return 0;
}

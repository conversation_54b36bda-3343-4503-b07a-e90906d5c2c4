#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(network, CONFIG_APP_NET_LOG_LEVEL);

#include <stdio.h>
#include <string.h>

#include <zephyr/drivers/gpio.h>
#include <zephyr/drivers/hwinfo.h>
#include <zephyr/drivers/regulator.h>
#include <zephyr/net/ethernet_mgmt.h>
#include <zephyr/net/hostname.h>
#include <zephyr/net/net_if.h>
#include <zephyr/net/net_ip.h>
#include <zephyr/random/rand32.h>
#include <zephyr/sys/byteorder.h>
#include <zephyr/sys/math_extras.h>

#include "drivers/eeprom.h"
#include "module_id.h"
#include "network/dhcp.h"
#include "network/init.h"
#include "network/rx_lockup_watcher.h"
#include "network/utils.h"
#include "power/power.h"

#include <utils/carbon_response_codes.h>
#include <utils/handle_errors.h>

// set this flag to enable acquiring an IP via DHCP
#define USE_DHCP (1)

static int init_hostname();
static int init_macaddr(struct net_if *intf);
static int init_ipaddr(struct net_if *intf, const bool addLinkLocal);
static int apply_config(struct net_if *intf, const module_id_t *config);
static void log_config(struct net_if *intf);

static uint32_t hash(const uint8_t *in, const size_t length);

/**
 * @brief Initialize the network stack
 *
 * Set the MAC address of the network interface and then apply the IP address configuration: if we
 * have a module ID, we'll assign a static IP, but either way we will also start a DHCP client.
 *
 * Randomize all of the delays slightly to prevent a "thundering herd" problem on startup where
 * all the MCBs on the machine start their network stacks simultaneously, and DHCP broadcasts get
 * sent out.
 */
int network_init() {
  const size_t delay1 = sys_rand32_get() % 1500, delay2 = sys_rand32_get() % 500;
  struct net_if *intf = net_if_get_default();

  HANDLE_UNLIKELY(net_if_down(intf));
  HANDLE_UNLIKELY(init_macaddr(intf));
  HANDLE_UNLIKELY(init_ipaddr(intf, true));
  HANDLE_UNLIKELY(init_hostname());
  HANDLE_UNLIKELY(net_if_up(intf));

  // after applying all the configuration, power up the Ethernet switch to link up upstream, then
  // enable RX lockup watchdog
  k_sleep(K_MSEC(delay1));
  HANDLE_UNLIKELY(power_enable(kPowerEthSwitch));
  k_sleep(K_SECONDS(5));

  HANDLE_UNLIKELY(network_rx_watcher_start());

#if USE_DHCP
  // start the DHCP client once interface is back up, and request an address
  HANDLE_UNLIKELY(network_dhcp_start(intf));

  k_sleep(K_MSEC(500 + delay2));
  HANDLE_UNLIKELY(network_dhcp_request());
#endif

  log_config(intf);

  return 0;
}

/**
 * @brief Update network configuration
 *
 * Update the assignment of the static IP on the network interface; this should be invoked when the
 * module ID changes.
 */
int network_update() {
  int err = 0;
  char addrStr[INET_ADDRSTRLEN];
  struct net_if *intf = net_if_get_default();

  // take network link down
  LOG_INF("Removing old static IP addresses");
  HANDLE_UNLIKELY(net_if_down(intf));

  // remove old static addresses
  const struct net_if_config *ifconfig = net_if_get_config(intf);
  if (!ifconfig) {
    LOG_WRN("%s failed", "net_if_get_config");
    return -ENODEV;
  }

  if (ifconfig->ip.ipv4) {
    for (size_t i = 0; i < NET_IF_MAX_IPV4_ADDR; i++) {
      // skip unset/invalid addresses
      const struct net_if_addr *unicast = &(ifconfig->ip.ipv4->unicast[i]);
      if (unicast->address.in_addr.s_addr == INADDR_ANY || !unicast->is_used) {
        continue;
      }

      // remove manual addresses (for module ID assignment)
      if (unicast->addr_type == NET_ADDR_MANUAL) {
        net_addr_ntop(AF_INET, &unicast->address.in_addr, addrStr, sizeof(addrStr));
        LOG_DBG("Removing address: %s (type %u, state %u, used %u)", addrStr, unicast->addr_type, unicast->addr_state,
                unicast->is_used);

        HANDLE_UNLIKELY_BOOL(net_if_ipv4_addr_rm(intf, &unicast->address.in_addr), -EIO);
      }
    }
  }

  // then re-add static addresses
  HANDLE_UNLIKELY(init_ipaddr(intf, false));

  // also update hostname
  HANDLE_UNLIKELY(init_hostname());

  // bring network interface back up
  HANDLE_UNLIKELY(net_if_up(intf));
  log_config(intf);

  return err;
}

/**
 * @brief DHCP address chagne callback
 *
 * Invoked by the DHCP client when it alters the addresses assigned to the interface.
 */
int network_dhcp_callback() {
  // just log network config
  struct net_if *intf = net_if_get_default();
  log_config(intf);

  return 0;
}

/**
 * @brief Configure the system's hostname
 *
 * By default, the hostname of the system is "reaper-mcb-(MAC address)"; if we have a module id
 * it is changed to "reaper-mcb-modN" where N is the module id.
 */
static int init_hostname() {
  int err, hostnameLen;
  module_id_t mid;
  char hostname[32];

  // try to get module id; ignore "not found" (use MAC address instead)
  memset(&mid, 0, sizeof(mid));
  err = module_id_get(&mid);

  if (err && err != -ENOENT) {
    LOG_WRN("%s failed: %d", "module_id_get", err);
    return err;
  }

  // format the string and update hostname
  memset(hostname, 0, sizeof(hostname));

  if (mid.module) {
    hostnameLen = snprintf(hostname, sizeof(hostname), "reaper-mcb-mod%u", mid.module);
  } else {
    const struct net_linkaddr *addr = net_if_get_link_addr(net_if_get_default());
    const uint8_t *macAddr = addr->addr;

    hostnameLen = snprintf(hostname, sizeof(hostname), "reaper-mcb-%02x%02x%02x%02x%02x%02x", macAddr[0], macAddr[1],
                           macAddr[2], macAddr[3], macAddr[4], macAddr[5]);
  }

  LOG_INF("Update hostname: `%.*s`", hostnameLen, hostname);
  HANDLE_UNLIKELY(net_hostname_set(hostname));

  return 0;
}

/**
 * @brief Calculate stable MAC address and assign
 *
 * This hashes the unique id of the chip and generates a locally administered MAC address. The
 * generated MAC address will have the top two octets of 0x0200, with the lower four being a
 * hash of the chip unique id.
 */
static int init_macaddr(struct net_if *intf) {
  struct ethernet_req_params params;
  char uniqueId[16], macAddr[6];
  uint32_t hwRev = 0;

  HANDLE_UNLIKELY(module_hw_rev_get(&hwRev));

  int uniqueIdLen = hwinfo_get_device_id(uniqueId, sizeof(uniqueId));
  if (uniqueIdLen < 0) {
    return -EINVAL;
  }

  const uint32_t low = hash(uniqueId, uniqueIdLen);

  memset(macAddr, 0, sizeof(macAddr));
  macAddr[0] = 0b01110010; // unicast, locally administered
  macAddr[1] = hwRev;
  memcpy(macAddr + 2, &low, 4);

  LOG_INF("MAC address: %02x:%02x:%02x:%02x:%02x:%02x", macAddr[0], macAddr[1], macAddr[2], macAddr[3], macAddr[4],
          macAddr[5]);

  // now apply to the interface
  memset(&params, 0, sizeof(params));
  memcpy(params.mac_address.addr, macAddr, sizeof(macAddr));

  HANDLE_UNLIKELY(net_mgmt(NET_REQUEST_ETHERNET_SET_MAC_ADDRESS, intf, &params, sizeof(params)));
  return 0;
}

/**
 * @brief Configure IP addresses
 *
 * Set the IP address, subnet mask and gateway from the configuration stored in the nonvolatile
 * memory; if invalid or not present, apply the default configuration.
 */
static int init_ipaddr(struct net_if *intf, const bool addLinkLocal) {
  int err;
  struct in_addr addr;
  module_id_t mid;
  memset(&mid, 0, sizeof(mid));

  /*
   * Set the fixed network information:
   *
   * - Subnet: ***********
   * - Gateway: *********
   */
  addr.s_addr = htonl(0xffff0000);
  net_if_ipv4_set_netmask(intf, &addr);

  addr.s_addr = htonl(0x0a0a0301);
  net_if_ipv4_set_gw(intf, &addr);

  // try to read module ID from NVRAM and apply static IP based on this
  err = module_id_get(&mid);

#if !USE_DHCP
  if (err) {
    mid.row = 0;
    mid.module = 1;
    err = 0;

    LOG_WRN("no module ID, using fake values: %u/%u", mid.row, mid.module);
  }
#endif

  // treat zero module ID as unset
  if (err || !mid.module) {
    if (err) {
      LOG_WRN("failed to read module id (%d); skipping static IP", err);
    }

    /*
     * For DHCP to work, we need to add a link-local address to the interface.
     *
     * When we acquire a DHCP address, we'll get a proper address that replaces this one.
     */
    if (addLinkLocal) {
      HANDLE_UNLIKELY(network_add_link_local(intf));
    }
  } else {
    HANDLE_UNLIKELY(apply_config(intf, &mid));
  }

  return 0;
}

/**
 * @brief Apply nonvolatile network config based on module ID
 *
 * This will assign the module a static IP address of the form 10.10.32.x, where x is its
 * module id.
 */
static int apply_config(struct net_if *intf, const module_id_t *config) {
  struct in_addr addr;
  struct net_if_addr *addrPtr;
  char addrStr[INET_ADDRSTRLEN];

  // module ID 0 = not assigned, do not set static
  if (!config->module) {
    LOG_WRN("module id is 0; not applying static IP");
    return 0;
  }

  // set the primary unicast address
  addr.s4_addr[0] = 10;
  addr.s4_addr[1] = 10;
  addr.s4_addr[2] = 22;
  addr.s4_addr[3] = config->module;

  addrPtr = net_if_ipv4_addr_add(intf, &addr, NET_ADDR_MANUAL, 0);
  if (!addrPtr) {
    LOG_WRN("%s failed", "net_if_ipv4_addr_add");
    return CARBON_ERROR_UNKNOWN;
  }

  net_addr_ntop(AF_INET, &addr, addrStr, sizeof(addrStr));
  LOG_INF("Setting static from module ID (%u): %s", config->module, addrStr);
  return CARBON_RESPONSE_OK;
}

/**
 * @brief Print to system log the network config
 */
static void log_config(struct net_if *intf) {
  char addrBuf[INET_ADDRSTRLEN * NET_IF_MAX_IPV4_ADDR], maskStr[INET_ADDRSTRLEN], gwStr[INET_ADDRSTRLEN];
  size_t addrBufOffset = 0;

  const struct net_if_config *ifconfig = net_if_get_config(intf);
  if (!ifconfig) {
    LOG_WRN("%s failed", "net_if_get_config");
    return;
  }

  memset(addrBuf, 0, sizeof(addrBuf));
  net_addr_ntop(AF_INET, &ifconfig->ip.ipv4->netmask, maskStr, sizeof(maskStr));
  net_addr_ntop(AF_INET, &ifconfig->ip.ipv4->gw, gwStr, sizeof(gwStr));

  if (ifconfig->ip.ipv4) {
    for (size_t i = 0; i < NET_IF_MAX_IPV4_ADDR; i++) {
      char addrStr[INET_ADDRSTRLEN];

      // skip unset/invalid addresses
      const struct net_if_addr *unicast = &(ifconfig->ip.ipv4->unicast[i]);
      if (unicast->address.in_addr.s_addr == INADDR_ANY || !unicast->is_used) {
        continue;
      }

      net_addr_ntop(AF_INET, &unicast->address.in_addr, addrStr, sizeof(addrStr));
      const size_t len = strlen(addrStr);
      memcpy(addrBuf + addrBufOffset, addrStr, len);
      addrBufOffset += len;
      addrBuf[addrBufOffset++] = ',';
    }
  }

  // this removes the comma from the last element
  addrBuf[addrBufOffset - 1] = '\0';
  LOG_INF("Assigned IPv4 addresses: [%s] netmask %s gw %s", addrBuf, maskStr, gwStr);
}

/**
 * @brief Calculate a non-cryptographic hash over the given data
 */
static uint32_t hash(const uint8_t *in, const size_t length) {
  uint32_t hash = 5381;

  for (size_t i = 0; i < length; ++in, ++i) {
    hash = ((hash << 5) + hash) + (*in);
  }

  return hash;
}

/**
 * @file
 * @brief Type definitions for DHCP client
 *
 * All multibyte values are in network byte order.
 */
#pragma once

#include <stdint.h>

#define BOOTP_OPCODE_BOOTREQUEST (1)
#define BOOTP_OPCODE_BOOTREPLY (2)

// Hardware address type value: Ethernet
#define BOOTP_HWTYPE_ETHERNET (1)

// DHCP magic cookie value
#define DHCP_MAGIC_COOKIE (0x63538263)

/**
 * @brief DHCP flags
 *
 * This enum defines the possible bit positions in the "flags" field
 */
typedef enum dhcp_flags {
  // Client cannot receive unicast packets until address assigned
  kDhcpFlagBroadcast = (1 << 15),
} dhcp_flags_t;

/**
 * @brief A DHCP packet
 */
typedef struct dhcp_msg {
  // BOOTP message type
  uint8_t opcode;
  // Hardware address type (1 = Ethernet)
  uint8_t hwaddrType;
  // Length of hardware addresses (bytes)
  uint8_t hwaddrLen;
  // Number of relays through which the DHCP packet was forwarded
  uint8_t numHops;
  // Transaction ID: chosen randomly by client
  uint32_t xid;
  // Seconds since client began address acquisition process
  uint16_t secs;
  // DHCP message flags (see dhcp_flags_t)
  uint16_t flags;

  // Current client IP address, set only iff renewing
  uint32_t ciaddr;
  // Your (client) IP address
  uint32_t yiaddr;
  // IP address of next server to use for configuration
  uint32_t siaddr;
  // Address for relay agent
  uint32_t giaddr;
  // Client hardware (MAC) address
  uint8_t chaddr[16];

  // Server host name (optional, NULL terminated)
  char sname[64];
  // Boot file name (optional, NULL terminated)
  char file[128];

  // DHCP magic cookie
  uint32_t cookie;
  // actual options
  uint8_t options[];
} __attribute__((packed)) dhcp_msg_t;

/**
 * @brief DHCP option value
 *
 * This enum defines a subset of the DHCP options (e.g. tag values) specified in RFC 1533.
 */
typedef enum dhcp_option {
  // Padding for options section
  kDhcpOptionPad = 0,
  // Subnet mask
  kDhcpOptionSubnetMask = 1,
  // Upstream router addresses
  kDhcpOptionRouters = 3,
  // DNS server addresses
  kDhcpOptionDnsServers = 6,
  // Remote logging (syslog) server address
  kDhcpOptionSyslog = 7,
  // Client hostname
  kDhcpOptionHostname = 12,
  // Client requested IP address
  kDhcpOptionRequestedAddr = 50,
  // Length of time this lease is valid for
  kDhcpOptionLeaseTime = 51,
  // Message type (see dhcp_msg_type_t)
  kDhcpOptionMessageType = 53,
  // Server identifier
  kDhcpOptionServerId = 54,
  // Desired options
  kDhcpOptionRequestList = 55,
  // Maximum supported DHCP message size
  kDhcpOptionMaxSize = 57,
  // Client identifier (type + min 2 bytes)
  kDhcpOptionClientId = 61,
  // End of DHCP options section
  kDhcpOptionEnd = 255,
} dhcp_option_t;

/**
 * @brief DHCP option header
 *
 * This is the header for a DHCP option. It assumes there is a length byte; some options (e.g. 0
 * and 255 ONLY) are fixed length, where we ignore the length byte.
 */
typedef struct dhcp_option_header {
  // Option type (from dhcp_option_t)
  uint8_t option;
  // Number of payload bytes following
  uint8_t length;

  char payload[];
} __attribute__((packed)) dhcp_option_header_t;

/**
 * @brief Values for the "DHCP Message Type" option
 *
 * This option should be the first specified in a packet, and is used to determine the type of
 * message for DHCP. If absent, the packet is a BOOTP frame.
 */
typedef enum dhcp_msg_type {
  kDhcpMsgDiscover = 1,
  kDhcpMsgOffer = 2,
  kDhcpMsgRequest = 3,
  kDhcpMsgDecline = 4,
  kDhcpMsgAck = 5,
  kDhcpMsgNak = 6,
  kDhcpMsgRelease = 7,
} dhcp_msg_type_t;

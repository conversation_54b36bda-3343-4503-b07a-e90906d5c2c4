/**
 * @file
 * @brief RPC implementations shared between UDP and RS232 interface
 *
 * This is roughly grouped by endpoint, e.g. the top level oneof in the request message. Each
 * routine will fill out the reply structure if successful (e.g. returns 0) otherwise the
 * caller should generate a protocol specific error message. Likewise, the caller will likely
 * need to prepare the outer message.
 */
#pragma once

#include "generated/lib/drivers/nanopb/proto/hwinfo.pb.h"
#include "generated/lib/drivers/nanopb/proto/reaper_module_controller.pb.h"
#include "generated/lib/drivers/nanopb/proto/version.pb.h"

typedef enum {
  /// Request came in via the network
  kRpcFlagNetwork = (1 << 0),
  /// Request was received via serial interface
  kRpcFlagSerial = (1 << 1),
} rpc_flags_t;

int rpc_handle_reboot(const version_Reset_Request *request, const rpc_flags_t flags);

int rpc_handle_version(const version_Version_Request *request, version_Version_Reply *reply, bool *sendGenericReply,
                       const rpc_flags_t flags);
int rpc_handle_config(const reaper_module_controller_ConfigRequest *request,
                      reaper_module_controller_ConfigReply *reply, bool *sendGenericReply, const rpc_flags_t flags);
int rpc_handle_power(const reaper_module_controller_PowerRequest *request, reaper_module_controller_PowerReply *reply,
                     bool *sendGenericReply, const rpc_flags_t flags);
int rpc_handle_relay(const reaper_module_controller_RelayRequest *request, reaper_module_controller_RelayReply *reply,
                     bool *sendGenericReply, const rpc_flags_t flags);
int rpc_handle_network(const reaper_module_controller_NetworkRequest *request,
                       reaper_module_controller_NetworkReply *reply, bool *sendGenericReply, const rpc_flags_t flags);
int rpc_handle_hwinfo(const hwinfo_Request *request, hwinfo_Reply *reply, bool *sendGenericReply,
                      const rpc_flags_t flags);
int rpc_handle_status(const reaper_module_controller_StatusRequest *request,
                      reaper_module_controller_StatusReply *reply, bool *sendGenericReply, const rpc_flags_t flags);

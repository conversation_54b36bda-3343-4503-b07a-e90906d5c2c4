#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(udp_server, CONFIG_APP_LOG_LEVEL);

#include <stdio.h>
#include <string.h>

#include <pb_decode.h>
#include <pb_encode.h>
#include <zephyr/net/dns_sd.h>
#include <zephyr/net/hostname.h>
#include <zephyr/sys/byteorder.h>

#include <lib/ptp/ptp.h>
#include <lib/udp/udp.h>
#include <utils/carbon_response_codes.h>
#include <utils/handle_errors.h>
#include <utils/mcuboot_helpers.h>

#include "generated/lib/drivers/nanopb/proto/reaper_module_controller.pb.h"
#include "generated/lib/drivers/nanopb/proto/time.pb.h"

#include "fans.h"
#include "module_id.h"
#include "rpc/common.h"
#include "rpc/udp_server.h"
#include "scanners.h"
#include "sensors/nanopb.h"
#include "strobe/nanopb.h"
#include "strobe/strobe.h"

// Priority for the UDP server worker task
#define THREAD_PRIORITY K_PRIO_PREEMPT(8)
// UDP request backlog (maximum number of packets queued for processing)
#define UDP_NUM_PENDING (1)
// Size of UDP receive buffer (bytes)
#define UDP_RX_BUF_SIZE (sizeof(reaper_module_controller_UdpRequest) * 2)
// Size of UDP send buffer (bytes)
#define UDP_TX_BUF_SIZE (sizeof(reaper_module_controller_UdpReply) * 2)

/**
 * @brief Auxiliary information for a single request
 *
 * Groups together various information about the request (the identity of the UDP client, as well as
 * the decoded request) as well as data needed to generate a response.
 */
typedef struct request_context {
  // Underlying UDP connection info
  udp_msg_metadata udpClient;
  // Decoded protobuf request
  reaper_module_controller_UdpRequest request;

  // Response payload (should be filled in by request handlers)
  reaper_module_controller_UdpReply reply;
} request_context_t;

// Buffer into which a response packet is encoded
__dtcm_bss_section static uint8_t gTxBuffer[UDP_TX_BUF_SIZE];
// Array of request/response handle structures
__dtcm_bss_section static request_context_t gRequestStateBufs[UDP_NUM_PENDING];
// Timestamp of last inbound request
// TODO: use atomics/lock to access?
__dtcm_bss_section static int64_t gLastRequestTime;

static void udp_worker_main();
static void handle_request(request_context_t *ctx);
static int handle_sensor_request(request_context_t *ctx);
static int handle_fan_request(request_context_t *ctx);
static int handle_scanner_request(request_context_t *ctx);
static int handle_strobe_request(request_context_t *ctx);
static int handle_time_request(request_context_t *ctx);
static void fill_generic_reply(request_context_t *ctx, int err);

#ifdef CONFIG_DNS_SD
static int mdns_advertise();
#endif

K_THREAD_DEFINE(gUdpServerThreadId, 2048, udp_worker_main, NULL, NULL, NULL, THREAD_PRIORITY, 0, K_TICKS_FOREVER);
UDP_SERVER_DEFINE(gUdpServer, UDP_NUM_PENDING, UDP_RX_BUF_SIZE, THREAD_PRIORITY);

/**
 * @brief Start the UDP server
 */
int udp_server_init() {
  gLastRequestTime = 0;

  // begin accepting UDP connections
  UDP_SERVER_START(gUdpServer, CONFIG_APP_UDP_PORT);

  // start processing task
  k_thread_name_set(gUdpServerThreadId, "udp_server");
  k_thread_start(gUdpServerThreadId);

  return 0;
}

/**
 * @brief Retrieve timestamp of last inbound request
 *
 * Any incoming packet (as long as it could be decoded as a protobuf, even if the request it
 * spawned fails) will update this timestamp.
 */
int udp_get_last_request_time(int64_t *outTimestamp) {
  if (!outTimestamp) {
    return -EFAULT;
  }

  *outTimestamp = gLastRequestTime;

  return 0;
}

/**
 * @brief UDP server work loop
 *
 * Receive UDP packets, and attempt to decode them and process the request contained within.
 */
static void udp_worker_main() {
  uint8_t *udpDataPtr = NULL;
  uint16_t udpDataLen;
  int err;

  // advertise via mDNS
#ifdef CONFIG_DNS_SD
  HANDLE_CRITICAL(mdns_advertise());
#endif

  // serve requests
  while (true) {
    // prepare request context buffer
    request_context_t *ctx = gRequestStateBufs;
    memset(ctx, 0, sizeof(*ctx));

    // get the raw packet data payload and decode protobuf
    udpDataLen = udp_get_data_no_copy(&gUdpServer, &udpDataPtr, &ctx->udpClient, 0);

    pb_istream_t istream = pb_istream_from_buffer(udpDataPtr, udpDataLen);
    const bool decodeOk = pb_decode(&istream, reaper_module_controller_UdpRequest_fields, &ctx->request);

    udp_get_data_release(&gUdpServer, udpDataPtr);

    if (!decodeOk) {
      LOG_WRN("pb_decode failed: %s", PB_GET_ERROR(&istream));
      LOG_HEXDUMP_WRN(udpDataPtr, udpDataLen, "message decode failed (UDP)");
      continue;
    }

    // once decoded, invoke handler (which fills out the response)
    gLastRequestTime = k_uptime_get();

    handle_request(ctx);

    // then encode the response and send back via UDP
    pb_ostream_t ostream = pb_ostream_from_buffer(gTxBuffer, sizeof(gTxBuffer));
    const bool encodeOk = pb_encode(&ostream, reaper_module_controller_UdpReply_fields, &ctx->reply);
    if (!encodeOk) {
      LOG_WRN("pb_encode failed: %s", PB_GET_ERROR(&ostream));
      continue;
    }

    err = udp_tx(&gUdpServer, gTxBuffer, ostream.bytes_written, &ctx->udpClient);
    if (err < 0) {
      LOG_WRN("failed to send reply: %d", err);
      continue;
    }
  }
}

#ifdef CONFIG_DNS_SD
/**
 * @brief Append a TXT record to the buffer specified
 *
 * @return true if successful (buffer had enough space)
 */
static bool add_txt_record(char *buffer, size_t *writePtr, const size_t maxLen, const char *label, const char *value) {
  if (!writePtr || !label || !value) {
    return false;
  }

  // sum up required length
  const size_t labelLen = strlen(label);
  const size_t valueLen = strlen(value);

  const size_t total = 1 + labelLen + 1 + valueLen;
  if (total > 255) {
    return false;
  } else if (*writePtr + total >= maxLen) {
    return false;
  }

  // write out the label
  char *lenPtr = buffer + *writePtr;
  *writePtr += 1;
  char *txtPtr = buffer + *writePtr;

  memcpy(txtPtr, label, labelLen);
  txtPtr += labelLen;
  *writePtr += labelLen;

  // then the separator
  *txtPtr++ = '=';
  *writePtr += 1;

  // then copy the value
  memcpy(txtPtr, value, valueLen);
  txtPtr += valueLen;
  *writePtr += valueLen;

  // and write the length ptr
  *lenPtr = labelLen + 1 + valueLen;

  return true;
}

/**
 * @brief Advertise the NanoPB server via mDNS
 *
 * Register the service under the "_nanopb" service address.
 *
 * Also sets up TXT records under the service:
 *
 * - v: to indicate protocol version (currently, this is 1)
 * - hw: Hardware revision
 * - fw: Current firmware version (from mcuboot header)
 * - sn: Serial number of device (from OTP)
 * - mac: Link layer address
 */
static int mdns_advertise() {
  int err;

  char strBuf[32];

  size_t txtPtr = 0;
  __dtcm_bss_section static char txt[96];
  __dtcm_bss_section static char hostname[32];

  // build txt record: fixed fields
  HANDLE_UNLIKELY_BOOL(add_txt_record(txt, &txtPtr, sizeof(txt), "v", "1"), ENOMEM);

  // hw revision
  {
    uint32_t hwRev;

    HANDLE_UNLIKELY(module_hw_rev_get(&hwRev));
    snprintf(strBuf, sizeof(strBuf), "%d", hwRev);
    HANDLE_UNLIKELY_BOOL(add_txt_record(txt, &txtPtr, sizeof(txt), "hw", strBuf), ENOMEM);
  }

  // firmware version
  {
    unsigned int maj, min, rev, build;

    err = mcuboot_get_running_version(&maj, &min, &rev, &build);
    if (err) {
      LOG_WRN("%s failed: %d", "mcuboot_get_running_version", err);
    } else {
      snprintf(strBuf, sizeof(strBuf), "%d.%d.%d", maj, min, rev);
      HANDLE_UNLIKELY_BOOL(add_txt_record(txt, &txtPtr, sizeof(txt), "fw", strBuf), ENOMEM);
    }
  }

  // device S/N
  // TODO: read actual data from OTP
  {
    snprintf(strBuf, sizeof(strBuf), "%s", "TODO: replace");
    HANDLE_UNLIKELY_BOOL(add_txt_record(txt, &txtPtr, sizeof(txt), "sn", strBuf), ENOMEM);
  }

  // MAC address
  struct net_if *intf = net_if_get_default();
  const uint8_t *macAddr = intf->if_dev->link_addr.addr;

  snprintf(strBuf, sizeof(strBuf), "%02x:%02x:%02x:%02x:%02x:%02x", macAddr[0], macAddr[1], macAddr[2], macAddr[3],
           macAddr[4], macAddr[5]);
  HANDLE_UNLIKELY_BOOL(add_txt_record(txt, &txtPtr, sizeof(txt), "mac", strBuf), ENOMEM);

  // module ID
  {
    module_id_t id;
    err = module_id_get(&id);
    if (err) {
      LOG_WRN("%s failed: %d", "module_id_get", err);
    } else {
      snprintf(strBuf, sizeof(strBuf), "%u", id.module);
      HANDLE_UNLIKELY_BOOL(add_txt_record(txt, &txtPtr, sizeof(txt), "id", strBuf), ENOMEM);
    }
  }

  // fill up the remainder of the TXT record (as the dns_sd struct is in ROM, length is fixed)
  const size_t remainder = sizeof(txt) - txtPtr - 1;
  if (remainder) {
    txt[txtPtr++] = (remainder - 1);
    memset(txt + txtPtr, 'A', remainder - 1);
  }

  // get hostname
  memset(hostname, 0, sizeof(hostname));
  strncpy(hostname, net_hostname_get(), sizeof(hostname));

  // finally, advertise the service
  LOG_HEXDUMP_DBG(txt, sizeof(txt), "txt records");

  DNS_SD_REGISTER_UDP_SERVICE(gMdnsService, hostname, "_nanopb", "local", txt, CONFIG_APP_UDP_PORT);

  // gMdnsService.text_size = txtPtr;

  return 0;
}
#endif

/**
 * @brief Process an UDP request
 *
 * Invoke the appropriate handler (based on the request type) and fill in the reply structure in
 * the request context.
 */
static void handle_request(request_context_t *ctx) {
  int err = 0;
  bool needsGenericReply = true;
  rpc_flags_t flags = kRpcFlagNetwork;

  // set up the reply header
  ctx->reply.has_header = true;
  ctx->reply.header = ctx->request.header;

  // dispatch the handler method
  switch (ctx->request.which_request) {
  // respond to host ping
  case reaper_module_controller_UdpRequest_ping_tag:
    ctx->reply.which_reply = reaper_module_controller_UdpReply_pong_tag;
    ctx->reply.reply.pong.x = ctx->request.request.ping.x;
    return;
  // get firmware version
  case reaper_module_controller_UdpRequest_version_tag:
    ctx->reply.which_reply = reaper_module_controller_UdpReply_version_tag;

    err = rpc_handle_version(&ctx->request.request.version, &ctx->reply.reply.version, &needsGenericReply, flags);
    break;
  // reboot the firmware
  case reaper_module_controller_UdpRequest_reset_tag:
    err = rpc_handle_reboot(&ctx->request.request.reset, flags);
    if (err == CARBON_RESPONSE_OK) {
      return;
    }
    break;

  // handle sensor requests
  case reaper_module_controller_UdpRequest_sensor_tag:
    err = handle_sensor_request(ctx);
    if (err == CARBON_RESPONSE_OK) {
      return;
    }
    break;

  // fan controls
  case reaper_module_controller_UdpRequest_fan_tag:
    err = handle_fan_request(ctx);
    if (err == CARBON_RESPONSE_OK) {
      return;
    }
    break;

  // scanner management
  case reaper_module_controller_UdpRequest_scanner_tag:
    err = handle_scanner_request(ctx);
    if (err == CARBON_RESPONSE_OK) {
      return;
    }
    break;

  // strobe control
  case reaper_module_controller_UdpRequest_strobe_tag:
    err = handle_strobe_request(ctx);
    if (err == CARBON_RESPONSE_OK) {
      return;
    }
    break;

  // time control
  case reaper_module_controller_UdpRequest_time_tag:
    err = handle_time_request(ctx);
    if (err == CARBON_RESPONSE_OK) {
      return;
    }
    break;

  // config endpoint
  case reaper_module_controller_UdpRequest_config_tag:
    ctx->reply.which_reply = reaper_module_controller_UdpReply_config_tag;
    err = rpc_handle_config(&ctx->request.request.config, &ctx->reply.reply.config, &needsGenericReply, flags);
    break;

  // power control endpoints
  case reaper_module_controller_UdpRequest_power_tag:
    ctx->reply.which_reply = reaper_module_controller_UdpReply_power_tag;
    err = rpc_handle_power(&ctx->request.request.power, &ctx->reply.reply.power, &needsGenericReply, flags);
    break;
  case reaper_module_controller_UdpRequest_relay_tag:
    ctx->reply.which_reply = reaper_module_controller_UdpReply_relay_tag;
    err = rpc_handle_relay(&ctx->request.request.relay, &ctx->reply.reply.relay, &needsGenericReply, flags);
    break;

  case reaper_module_controller_UdpRequest_network_tag:
    ctx->reply.which_reply = reaper_module_controller_UdpReply_network_tag;
    err = rpc_handle_network(&ctx->request.request.network, &ctx->reply.reply.network, &needsGenericReply, flags);
    break;

  case reaper_module_controller_UdpRequest_hwinfo_tag:
    ctx->reply.which_reply = reaper_module_controller_UdpReply_hwinfo_tag;
    err = rpc_handle_hwinfo(&ctx->request.request.hwinfo, &ctx->reply.reply.hwinfo, &needsGenericReply, flags);
    break;

  case reaper_module_controller_UdpRequest_status_tag:
    ctx->reply.which_reply = reaper_module_controller_UdpReply_status_tag;
    err = rpc_handle_status(&ctx->request.request.status, &ctx->reply.reply.status, &needsGenericReply, flags);
    break;

  default:
    LOG_WRN("unhandled UDP request type (%u)", ctx->request.which_request);
    err = CARBON_ERROR_UNKNOWN;
    break;
  }

  // if we get here, the command doesn't have its own reply type (or failed) so send ack/error
  if (needsGenericReply) {
    fill_generic_reply(ctx, err);
  }
}

/**
 * @brief Handle requests for sensor data
 *
 * These messages are used to read out the current sensor state.
 */
static int handle_sensor_request(request_context_t *ctx) {
  ctx->reply.which_reply = reaper_module_controller_UdpReply_sensor_tag;
  reaper_module_controller_SensorReply *sensorReply = &ctx->reply.reply.sensor;

  HANDLE_UNLIKELY(sensors_nanopb_fill(sensorReply));

  return CARBON_RESPONSE_OK;
}

/**
 * @brief Process fan control message
 */
static int handle_fan_request(request_context_t *ctx) {
  const reaper_module_controller_FanRequest *req = &ctx->request.request.fan;

  ctx->reply.which_reply = reaper_module_controller_UdpReply_fan_tag;
  reaper_module_controller_FanReply *reply = &ctx->reply.reply.fan;

  switch (req->which_request) {
  case reaper_module_controller_FanRequest_set_tag:

    // handle setting new state
    if (req->request.set.which__fan1) {
      HANDLE_UNLIKELY(fans_set_manual(kFan1, req->request.set._fan1.fan1));
    }
    if (req->request.set.which__fan2) {
      HANDLE_UNLIKELY(fans_set_manual(kFan2, req->request.set._fan2.fan2));
    }

    // respond with current state afterwards
    break;

  case reaper_module_controller_FanRequest_thermoConfig_tag:
    const bool hasEnable = (req->request.thermoConfig.which__enabled),
               enableFlag = hasEnable & (req->request.thermoConfig._enabled.enabled),
               hasConfig = (req->request.thermoConfig.which__config);

    // if disabling, do that first
    if (hasEnable && !enableFlag) {
      HANDLE_UNLIKELY(fans_set_thermo_enabled(enableFlag));
    }

    // then update configuration
    if (hasConfig) {
      fan_thermo_config_t conf;
      memset(&conf, 0, sizeof(conf));

      conf.setpoint = req->request.thermoConfig._config.config.setpoint;
      conf.hysteresis = req->request.thermoConfig._config.config.hysteresis;
      switch (req->request.thermoConfig._config.config.source) {
      case reaper_module_controller_ThermostatSource_ENVIRO_INTERNAL:
        conf.tempSource = kThermoSourceEnviroInternal;
        break;
      case reaper_module_controller_ThermostatSource_ENVIRO_EXTERNAL:
        conf.tempSource = kThermoSourceEnviroExternal;
        break;

      default:
        conf.tempSource = kThermoSourceEnviroInternal;
        break;
      }

      HANDLE_UNLIKELY(fans_set_thermo_config(&conf));
    }

    // lastly, handle enabling (ensure correct config is applied)
    if (hasEnable && enableFlag) {
      HANDLE_UNLIKELY(fans_set_thermo_enabled(enableFlag));
    }

    // then respond with the current thermostat state
    break;

  default:
    LOG_WRN("%s: unknown tag %u", __FUNCTION__, req->which_request);
    return CARBON_ERROR_UNKNOWN;
  }

  // unless there was some sort of error, respond always with the current fan state
  bool enabled = false;
  fan_thermo_config_t config;
  float temp = 0.f;

  HANDLE_UNLIKELY(fans_get_state(kFan1, &reply->fan1));
  HANDLE_UNLIKELY(fans_get_state(kFan2, &reply->fan2));

  HANDLE_UNLIKELY(fans_get_thermo_state(&enabled, &config, &temp));

  reply->thermostatEnabled = enabled;

  if (enabled) {
    reply->which__thermostatActual = reaper_module_controller_FanReply_thermostatActual_tag;
    reply->_thermostatActual.thermostatActual = temp;

    reply->which__thermostatConfig = reaper_module_controller_FanReply_thermostatConfig_tag;
    reply->_thermostatConfig.thermostatConfig.setpoint = config.setpoint;
    reply->_thermostatConfig.thermostatConfig.hysteresis = config.hysteresis;

    switch (config.tempSource) {
    case kThermoSourceEnviroInternal:
      reply->_thermostatConfig.thermostatConfig.source = reaper_module_controller_ThermostatSource_ENVIRO_INTERNAL;
      break;

    case kThermoSourceEnviroExternal:
      reply->_thermostatConfig.thermostatConfig.source = reaper_module_controller_ThermostatSource_ENVIRO_EXTERNAL;
      break;

    default:
      reply->_thermostatConfig.thermostatConfig.source = reaper_module_controller_ThermostatSource_DEFAULT;
      break;
    }
  }

  return CARBON_RESPONSE_OK;
}

/**
 * @brief Handle scanner endpoint messages
 *
 * The scanner endpoint allows controlling the scanner's switchable power, as well as reading out
 * the power consumption/fuse state of each.
 */
static int handle_scanner_request(request_context_t *ctx) {
  const reaper_module_controller_ScannerRequest *req = &ctx->request.request.scanner;
  reaper_module_controller_ScannerReply *reply = &ctx->reply.reply.scanner;

  switch (req->which_request) {
  // get status of all scanners
  case reaper_module_controller_ScannerRequest_status_tag:
    ctx->reply.which_reply = reaper_module_controller_UdpReply_scanner_tag;
    reply->which_reply = reaper_module_controller_ScannerReply_status_tag;

    HANDLE_UNLIKELY(scanners_nanopb_fill_status(reply->reply.status.data, ARRAY_SIZE(reply->reply.status.data)));

    reply->reply.status.data_count = NUM_SCANNERS;

    return CARBON_RESPONSE_OK;

  // toggle power to scanners
  case reaper_module_controller_ScannerRequest_power_tag:
    scanner_t toEnable = 0, toDisable = 0;

    if (req->request.power.which__scannerA == reaper_module_controller_ScannerSetPowerRequest_scannerA_tag) {
      if (req->request.power._scannerA.scannerA) {
        toEnable |= kScannerA;
      } else {
        toDisable |= kScannerA;
      }
    }

    if (req->request.power.which__scannerB == reaper_module_controller_ScannerSetPowerRequest_scannerB_tag) {
      if (req->request.power._scannerB.scannerB) {
        toEnable |= kScannerB;
      } else {
        toDisable |= kScannerB;
      }
    }

    // apply power state; respond with an ack on success
    if (toEnable || toDisable) {
      HANDLE_UNLIKELY(scanners_set_power(toEnable, toDisable));
    }

    fill_generic_reply(ctx, CARBON_RESPONSE_OK);
    return CARBON_RESPONSE_OK;

  // reset overcurrent flag
  case reaper_module_controller_ScannerRequest_ocp_tag:
    if (req->request.ocp.which__scannerA == reaper_module_controller_ScannerResetOcpRequest_scannerA_tag &&
        req->request.ocp._scannerA.scannerA) {
      HANDLE_UNLIKELY(scanners_reset_ocp(kScannerA));
    }

    if (req->request.ocp.which__scannerB == reaper_module_controller_ScannerResetOcpRequest_scannerB_tag &&
        req->request.ocp._scannerB.scannerB) {
      HANDLE_UNLIKELY(scanners_reset_ocp(kScannerB));
    }

    fill_generic_reply(ctx, CARBON_RESPONSE_OK);
    return CARBON_RESPONSE_OK;

  default:
    LOG_WRN("%s: unknown tag %u", __FUNCTION__, req->which_request);
    return CARBON_ERROR_UNKNOWN;
  }
}

/**
 * @brief Handle strobe manager messages
 *
 * This allows configuration of the strobe frequency/periods, offsets, as well as querying the
 * current status of the strobe board.
 */
static int handle_strobe_request(request_context_t *ctx) {
  int err;
  const reaper_module_controller_StrobeRequest *req = &ctx->request.request.strobe;

  ctx->reply.which_reply = reaper_module_controller_UdpReply_strobe_tag;
  reaper_module_controller_StrobeReply *reply = &ctx->reply.reply.strobe;

  switch (req->which_request) {
  // update the strobe configuration
  case reaper_module_controller_StrobeRequest_setWaveform_tag: {
    strobe_config_t config;
    memset(&config, 0, sizeof(config));

    config.exposure = req->request.setWaveform.exposure_us;
    config.period = req->request.setWaveform.period_us;
    config.target_exposure_ratio = req->request.setWaveform.targets_per_predict_ratio;

    err = strobe_set_config(&config);
    if (err) {
      LOG_WRN("%s failed: %d", "strobe_set_config", err);
      return err;
    }

    break;
  }

  // query the current strobe board status (sensor readings)
  case reaper_module_controller_StrobeRequest_getStatus_tag: {
    reply->which_reply = reaper_module_controller_StrobeReply_status_tag;
    HANDLE_UNLIKELY(strobe_nanopb_fill_status(&reply->reply.status));

    return CARBON_RESPONSE_OK;
  }

  // enable or disable strobing
  case reaper_module_controller_StrobeRequest_setState_tag: {
    const bool enableFiring = req->request.setState.enabled;

    err = strobe_set_enabled(enableFiring);
    if (err) {
      LOG_WRN("%s failed: %d", "strobe_set_enabled", err);
      return err;
    }

    break;
  }

  // timed disable (for module identification)
  case reaper_module_controller_StrobeRequest_timedDisable_tag: {
    LOG_INF("strobe inhibit for %u msec", req->request.timedDisable.durationMsec);

    err = strobe_timed_disable(K_MSEC(req->request.timedDisable.durationMsec));
    if (err) {
      LOG_WRN("%s failed: %d", "strobe_timed_disable", err);
      return err;
    }

    break;
  };

  default:
    LOG_WRN("%s: unknown tag %u", __FUNCTION__, req->which_request);
    return CARBON_ERROR_UNKNOWN;
  }

  // if we get here, the request succeeded
  fill_generic_reply(ctx, CARBON_RESPONSE_OK);

  return CARBON_RESPONSE_OK;
}

/**
 * @brief Process a time endpoint request
 *
 * Used to retrieve PTP time sync info and timestamp data.
 */
static int handle_time_request(request_context_t *ctx) {
  struct net_ptp_time ts;
  const time_Request *req = &ctx->request.request.time;

  ctx->reply.which_reply = reaper_module_controller_UdpReply_time_tag;
  time_Reply *reply = &ctx->reply.reply.time;

  HANDLE_UNLIKELY(ptp_slave_clk_get(&ts));

  switch (req->which_request) {
  // get current timestamp
  case time_Request_get_tag:
    reply->which_reply = time_Reply_timestamp_tag;

    reply->reply.timestamp.seconds = ts.second;
    reply->reply.timestamp.micros = ts.nanosecond / NSEC_PER_USEC;
    break;

  // get timestamp debug info
  case time_Request_debug_tag:
    reply->which_reply = time_Reply_debug_tag;

    reply->reply.timestamp.seconds = ts.second;
    reply->reply.timestamp.micros = ts.nanosecond / NSEC_PER_USEC;
    break;

  // setting timestamp is not implemented
  case time_Request_set_tag:
    return -ENOTSUP;
  }

  return CARBON_RESPONSE_OK;
}

/**
 * @brief Fill out request reply structure (generic)
 *
 * Fill out the response object to be encoded as either an ack (success) or the provided error
 * code.
 */
static void fill_generic_reply(request_context_t *ctx, int err) {
  if (err) {
    ctx->reply.which_reply = reaper_module_controller_UdpReply_error_tag;
    ctx->reply.reply.error.code = err;
  } else {
    ctx->reply.which_reply = reaper_module_controller_UdpReply_ack_tag;
  }
}

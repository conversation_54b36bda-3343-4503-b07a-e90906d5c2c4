#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(pc_serial, CONFIG_APP_LOG_LEVEL);

#include <pb_decode.h>
#include <pb_encode.h>
#include <stdint.h>
#include <string.h>
#include <zephyr/device.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/uart.h>
#include <zephyr/kernel.h>

#include <lib/cobs/cobs.h>
#include <utils/carbon_response_codes.h>
#include <utils/handle_errors.h>

#include "generated/lib/drivers/nanopb/proto/reaper_module_controller.pb.h"
#include "rpc/common.h"
#include "rpc/pc_serial.h"

// Use DMA driven transmission (rather than polling)
#define UART_TX_DMA (1)

// Baud rate to use for transmission of core dumps
#define CORE_DUMP_BAUD (115200)

// Worker thread priority for the task that handles all serial interface host IO
#define THREAD_PRIORITY K_PRIO_PREEMPT(8)

/// Message terminator byte (fixed at 0 for COBS)
#define MSG_TERMINATOR (0x00)
/// Size of the UART receive buffer; this is half the size of the maximum message size
#define UART_RX_DMA_BUF_LEN (CONFIG_APP_PCSERIAL_MAX_MSG / 2)
/// How many bytes to receive at start of receive buffer for in-place COBS decoding
#define UART_RX_BUF_DECODE_OFFSET (8)
/// Receive buffer size (including decode slack space)
#define UART_RX_BUF_LEN (UART_RX_DMA_BUF_LEN + UART_RX_BUF_DECODE_OFFSET)
/// Receive timeout (e.g. maximum time between characters) in msec
#define UART_RX_TIMEOUT (10)
/// Maximum number of UART events that may be pending at once
#define WORKQUEUE_SIZE (5)
/// Size (in bytes) of the message encode buffer
#define MSG_ENCODE_BUF_LEN (CONFIG_APP_PCSERIAL_MAX_MSG)
/// Length of the DMA transmit buffer (with overhead for COBS encoding)
#define UART_TX_DMA_BUF_LEN (MSG_ENCODE_BUF_LEN + 8)

typedef struct pc_serial_state {
  /// Work queue (send events from UART callback to worker thread)
  struct k_msgq workqueue;
  /// Signalled whenever tx is complete; block before mucking with the tx buffer
  struct k_sem txCompleteSignal;

  /// Which RX buffer should be provided when the driver requests it?
  size_t nextRxBuffer;

  /**
   * Receive message queue: UART receive data is buffered in here til a full message is found
   *
   * Note that this buffer is 8 bytes larger than the largest receive message: this is to allow
   * in place COBS decoding.
   */
  uint8_t rxBuf[UART_RX_BUF_LEN];
  /// Current write offset into the RX buffer
  size_t rxBufWritePtr;

  /// Buffer to encode the protobuf message into
  uint8_t encodeBuf[MSG_ENCODE_BUF_LEN];

  /// Flag set when in coredump mode; inhibits generating responses
  bool inCoredump;
  /// Set once the core dump baud rate has been applied
  bool coredumpBaudRate;
} pc_serial_state_t;

typedef enum pc_serial_workitem_type {
  /// Received a full message from the PC
  kWorkMessageRx,
} pc_serial_workitem_type_t;

typedef struct pc_serial_workitem {
  pc_serial_workitem_type_t type;

  // message payload: keyed on the type enum above
  union {
    // Receive message handle
    struct {
      /// Pointer to received message data (in RX DMA buffer)
      void *data;
      /// Total number of received bytes
      size_t length;
    } messageRx;
  };
} pc_serial_workitem_t;

static int pc_serial_worker();
static void handle_uart_event(const struct device *dev, struct uart_event *evt, void *);
static int send_message(const void *data, const size_t dataLen);
static int process_message(const void *rxData, const size_t rxDataLen);
static void reset_rx_buf();
static int configure_coredump_rate();

/// Internal state for the PC serial interface
__dtcm_bss_section static pc_serial_state_t gState;
/// Storage for the work queue items
__dtcm_bss_section static pc_serial_workitem_t gWorkqueueBuffer[WORKQUEUE_SIZE];

/// Message receive buffer (musn't be in CCM such that DMA can access it)
__nocache static uint8_t gRxBuffer[2][UART_RX_DMA_BUF_LEN];
/// DMA transmit buffer (musn't be in CCM such that DMA can access it)
__nocache static uint8_t gTxBuffer[UART_TX_DMA_BUF_LEN];

/// Configuration for host interface UART: 9600,8N1
static const struct uart_config gUartConfig = {
    .baudrate = CONFIG_APP_PCSERIAL_BAUD,
    .parity = UART_CFG_PARITY_NONE,
    .stop_bits = UART_CFG_STOP_BITS_1,
    .data_bits = UART_CFG_DATA_BITS_8,
    .flow_ctrl = UART_CFG_FLOW_CTRL_NONE,
};
/// Driver instance for the host UART
static const struct device *const gDevUart = DEVICE_DT_GET(DT_ALIAS(pc_serial));

// Worker thread
K_THREAD_DEFINE(pc_serial_thread_id, 2048, pc_serial_worker, NULL, NULL, NULL, THREAD_PRIORITY, 0, K_TICKS_FOREVER);

/**
 * @brief Initialize the host PC serial interface
 *
 * Set up the serial interface and the task that processes messages received on the interface.
 */
int pc_serial_init() {
  // set up the state and kernel objects
  k_msgq_init(&gState.workqueue, (uint8_t *)gWorkqueueBuffer, sizeof(pc_serial_workitem_t), WORKQUEUE_SIZE);
  k_sem_init(&gState.txCompleteSignal, 1, 1);

  k_thread_name_set(pc_serial_thread_id, "pc_serial");

  reset_rx_buf();

  gState.inCoredump = false;

  // configure serial port and enable reception
  HANDLE_UNLIKELY_BOOL(device_is_ready(gDevUart), ENXIO);
  HANDLE_UNLIKELY(uart_configure(gDevUart, &gUartConfig));

  HANDLE_UNLIKELY(uart_callback_set(gDevUart, handle_uart_event, NULL));

  HANDLE_UNLIKELY(uart_rx_enable(gDevUart, gRxBuffer[0], UART_RX_DMA_BUF_LEN, (UART_RX_TIMEOUT * 1000)));

  // initialize work queue and start worker thread
  k_thread_start(pc_serial_thread_id);

  return 0;
}

/**
 * @brief Prepare serial interface for coredump output
 *
 * Any ongoing transmissions are cancelled and receiving is disabled.
 */
int pc_serial_start_coredump() {
  int err;

  // should not already be in coredump mode
  if (gState.inCoredump) {
    k_oops();
  }
  gState.inCoredump = true;

  // cancel TX DMA if in progress?
  err = uart_tx_abort(gDevUart);
  if (err && err != -EFAULT) {
    LOG_WRN("%s failed: %d", "uart_tx_abort", err);
  }

  // disable receiving
  err = uart_rx_disable(gDevUart);
  if (err) {
    LOG_WRN("%s failed: %d", "uart_rx_disable", err);
  }

  // TODO: send break or NULL bytes to cancel reception at the host

  return 0;
}

/**
 * @brief Perform switch to core dump baud rate
 *
 * May be called more than once, all subsequent invocations are a no op.
 */
int pc_serial_switch_rate() {
  if (!gState.coredumpBaudRate) {
    HANDLE_UNLIKELY(configure_coredump_rate());
    gState.coredumpBaudRate = true;
  }

  return 0;
}

/**
 * @brief Send coredump data
 *
 * Use polling mode to write out data via the serial port.
 */
int pc_serial_send_coredump(const void *data, const size_t dataLen) {
  if (!data) {
    return -EFAULT;
  } else if (!gState.inCoredump) {
    return -EINVAL;
  }

  return send_message(data, dataLen);
}

/**
 * @brief PC serial interface handler
 */
static int pc_serial_worker() {
  int err;

  while (true) {
    pc_serial_workitem_t werk;
    k_msgq_get(&gState.workqueue, &werk, K_FOREVER);

    switch (werk.type) {
    /*
     * Received some data on UART
     *
     * This may be a full message or part thereof; it's shoved into the message receive
     * buffer until a terminator byte (NULL) is encountered. Then the enire contents of
     * the buffer are decoded as a message and parsed; and then the buffer and its state
     * are cleared.
     *
     * Should there still be bytes unused (e.g. the offset of the NULL byte is not the
     * last byte of the buffer) this repeats with the first non-NULL byte until the
     * buffer is fully used.
     */
    case kWorkMessageRx: {
      size_t readOffset = 0;

      do {
        // insert into the message buffer
        const uint8_t byte = ((uint8_t *)werk.messageRx.data)[readOffset];

        const size_t writeOffset = gState.rxBufWritePtr++;
        gState.rxBuf[MIN(writeOffset, (UART_RX_BUF_LEN - 1))] = byte;

        // if this was a terminator byte, COBS decode and proces message within
        if (byte == MSG_TERMINATOR) {
          // decode in place
          size_t dataLen = 0;
          const size_t rawDataLen = gState.rxBufWritePtr - UART_RX_BUF_DECODE_OFFSET;

          if (!cobs_decode(gState.rxBuf, UART_RX_BUF_LEN, (gState.rxBuf + UART_RX_BUF_DECODE_OFFSET), (rawDataLen - 1),
                           &dataLen)) {
            LOG_HEXDUMP_WRN((gState.rxBuf + UART_RX_BUF_DECODE_OFFSET), rawDataLen, "COBS decode failed");
            goto beach;
          }

          // then process message (protobuf decode)
          if (!gState.inCoredump) {
            if ((err = process_message(gState.rxBuf, dataLen))) {
              LOG_WRN("failed to decode message (%d)", err);
            }
          } else {
            LOG_WRN("Discarding RX data: %u bytes@ (in coredump mode!)", dataLen);
          }

        beach:;
          // …and then reset the buffer to prepare for next byte
          reset_rx_buf();
        }
        // otherwise, check for overflow
        else {
          if (gState.rxBufWritePtr >= UART_RX_BUF_LEN) {
            LOG_HEXDUMP_WRN(gState.rxBuf, UART_RX_BUF_LEN, "rx buffer overflow");
            reset_rx_buf();
          }
        }
      } while (++readOffset < werk.messageRx.length);

      break;
    }
    }
  }

  return 0;
}

/**
 * @brief Process UART driver event
 *
 * UART driver asynchronously calls this (which may happen from other contexts/threads) with
 * various events, such as receive complete.
 */
static void handle_uart_event(const struct device *dev, struct uart_event *evt, void *) {
  pc_serial_workitem_t werk;
  memset(&werk, 0, sizeof(werk));

  switch (evt->type) {
  // provide the next RX buffer
  case UART_RX_BUF_REQUEST:
    uart_rx_buf_rsp(gDevUart, gRxBuffer[++gState.nextRxBuffer % 2], UART_RX_DMA_BUF_LEN);
    break;
  // buffer no longer in use; don't do anything here
  case UART_RX_BUF_RELEASED:
    break;

  // Receive complete (e.g. timeout, buffer full, receiver disabled/stopped)
  case UART_RX_RDY:
    werk.type = kWorkMessageRx;
    werk.messageRx.data = evt->data.rx.buf + evt->data.rx.offset;
    werk.messageRx.length = evt->data.rx.len;

    HANDLE_CRITICAL(k_msgq_put(&gState.workqueue, &werk, K_FOREVER));
    break;

  // Likely due to overrun or other framing error
  case UART_RX_STOPPED:
    // TODO: handle this (reset receive state machine)
    break;

  // finished transmitting most recent buffer
  case UART_TX_DONE:
    k_sem_give(&gState.txCompleteSignal);
    break;
  // transmission failed/aborted, reset state
  case UART_TX_ABORTED:
    // TODO: need we do anything here?
    break;

  // ignore all other events types
  default:
    break;
  }
}

/**
 * @brief Transmit a message to the PC
 *
 * The specified payload data is framed and then sent over the serial port. Assumes that the
 * payload is already a correctly encoded protobuf; only COBS framing is applied.
 */
static int send_message(const void *data, const size_t dataLen) {
#if UART_TX_DMA
  // attempt to acquire access to TX buffer, but only if not in coredump mode
  if (!gState.inCoredump) {
    HANDLE_UNLIKELY(k_sem_take(&gState.txCompleteSignal, K_FOREVER));
  }
#endif

  // apply framing (incl. terminating character)
  size_t encodedLen = 0;
  if (!cobs_encode(gTxBuffer, (UART_TX_DMA_BUF_LEN - 1), data, dataLen, &encodedLen)) {
    return -1;
  }
  gTxBuffer[encodedLen++] = '\0';

  // transmit using DMA if enabled; in coredump mode must use polling
  if (gState.inCoredump) {
    for (size_t i = 0; i < encodedLen; i++) {
      uart_poll_out(gDevUart, gTxBuffer[i]);
    }
  } else {
#if UART_TX_DMA
    return uart_tx(gDevUart, gTxBuffer, encodedLen, SYS_FOREVER_US);
#else
    for (size_t i = 0; i < encodedLen; i++) {
      uart_poll_out(gDevUart, gTxBuffer[i]);
    }
#endif
  }

  return 0;
}

/**
 * @brief Process a received message
 *
 * Decode the outer message (it's a protobuf) and dispatch to the appropriate handler, generating
 * a response as necessary.
 */
static int process_message(const void *rxData, const size_t rxDataLen) {
  __dtcm_bss_section static reaper_module_controller_OobRequest req;
  __dtcm_bss_section static reaper_module_controller_OobReply reply;
  int err = 0;
  bool generateResponse = true;
  rpc_flags_t flags = kRpcFlagSerial;

  // prepare reply and decode message
  memset(&reply, 0, sizeof(reply));

  pb_istream_t istream = pb_istream_from_buffer(rxData, rxDataLen);
  if (!pb_decode(&istream, reaper_module_controller_OobRequest_fields, &req)) {
    LOG_WRN("pb_decode failed: %s", PB_GET_ERROR(&istream));
    LOG_HEXDUMP_WRN(rxData, rxDataLen, "message decode failed (serial)");
    return CARBON_ERROR_UNKNOWN;
  }

  // invoke correct handler (based on request type)
  switch (req.which_request) {
  case reaper_module_controller_OobRequest_ping_tag:
    reply.which_reply = reaper_module_controller_OobReply_pong_tag;
    reply.reply.pong.x = req.request.ping.x;
    generateResponse = false;
    break;

  case reaper_module_controller_OobRequest_version_tag:
    reply.which_reply = reaper_module_controller_OobReply_version_tag;

    err = rpc_handle_version(&req.request.version, &reply.reply.version, &generateResponse, flags);
    break;

  // reboot the firmware
  case reaper_module_controller_OobRequest_reset_tag:
    err = rpc_handle_reboot(&req.request.reset, flags);
    break;

  case reaper_module_controller_OobRequest_config_tag:
    reply.which_reply = reaper_module_controller_OobReply_config_tag;
    err = rpc_handle_config(&req.request.config, &reply.reply.config, &generateResponse, flags);
    break;

  case reaper_module_controller_OobRequest_network_tag:
    reply.which_reply = reaper_module_controller_OobReply_network_tag;
    err = rpc_handle_network(&req.request.network, &reply.reply.network, &generateResponse, flags);
    break;

  case reaper_module_controller_OobRequest_hwinfo_tag:
    reply.which_reply = reaper_module_controller_OobReply_hwinfo_tag;
    err = rpc_handle_hwinfo(&req.request.hwinfo, &reply.reply.hwinfo, &generateResponse, flags);
    break;

  // TODO: strobe endpoints
  // these need to be refactored out of the udp_server and into rpc common

  // handling power control
  case reaper_module_controller_OobRequest_power_tag:
    reply.which_reply = reaper_module_controller_OobReply_power_tag;
    err = rpc_handle_power(&req.request.power, &reply.reply.power, &generateResponse, flags);
    break;
  case reaper_module_controller_OobRequest_relay_tag:
    reply.which_reply = reaper_module_controller_OobReply_relay_tag;
    err = rpc_handle_relay(&req.request.relay, &reply.reply.relay, &generateResponse, flags);
    break;

  // unknown message type (host using a newer proto definition than us)
  default:
    LOG_WRN("unknown request type (%zu)", req.which_request);

    err = CARBON_ERROR_UNKNOWN;
    break;
  }

  /*
   * Fill out the response (ack/error, depending on current state; or do nothing if the
   * handler has filled out a different response message) and prepare the header to match the
   * incoming message. Then, apply COBS framing and send over the wire.
   */
  reply.has_header = true;
  reply.header.requestId = req.header.requestId;

  if (generateResponse) {
    // success: generate an ack response
    if (err == 0) {
      reply.which_reply = reaper_module_controller_OobReply_ack_tag;
    }
    // otherwise, return the error code
    else {
      reply.which_reply = reaper_module_controller_OobReply_error_tag;
      reply.reply.error.code = err;
    }
  }

  // encode the protobuf and send to host
  pb_ostream_t ostream = pb_ostream_from_buffer(gState.encodeBuf, sizeof(gState.encodeBuf));
  if (!pb_encode(&ostream, reaper_module_controller_OobReply_fields, &reply)) {
    LOG_WRN("pb_encode failed: %s", PB_GET_ERROR(&ostream));
    return CARBON_ERROR_UNKNOWN;
  }

  return send_message(gState.encodeBuf, ostream.bytes_written);
}

/**
 * @brief Reset the message receive buffer
 *
 * The write pointer is set to the start of the buffer (offset sufficiently to allow in place
 * decoding) and all previously used buffer space is cleared.
 */
static void reset_rx_buf() {
  const size_t toClear = MIN(UART_RX_BUF_LEN, gState.rxBufWritePtr);
  memset(gState.rxBuf, 0, toClear);

  gState.rxBufWritePtr = UART_RX_BUF_DECODE_OFFSET;
}

/**
 * @brief Reconfigure the UART for core dump
 *
 * The baud rate is juiced up to the core dump setting so it's not as hellaciously slow as the
 * regular rate.
 */
static int configure_coredump_rate() {
  struct uart_config config;
  memset(&config, 0, sizeof(config));

  HANDLE_UNLIKELY(uart_config_get(gDevUart, &config));
  LOG_INF("PC UART: %u -> %u baud", config.baudrate, CORE_DUMP_BAUD);

  config.baudrate = CORE_DUMP_BAUD;

  HANDLE_UNLIKELY(uart_configure(gDevUart, &config));

  // need some time to make UART chill out; busy wait due to called from coredump context
  k_busy_wait(USEC_PER_MSEC * 5);

  return 0;
}

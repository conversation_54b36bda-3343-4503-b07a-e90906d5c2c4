/**
 * @file
 * @brief PC serial interface handler
 *
 * Implements the serial command protocol used by the PC to communicate out of band with the
 * Reaper module; this is primarily used to identify the module addresses.
 *
 * The protocol used to exchange messages uses binary messages (protobufs) which are framed using
 * [COBS](https://en.wikipedia.org/wiki/Consistent_Overhead_Byte_Stuffing) to allow a NULL byte to
 * serve as a message separator. To this end, several intermediary buffers are a few bytes larger
 * than the configured message size: this is the fixed worst case overhead for COBS encoding,
 * required to support in-place decoding, and the overhead of encoding.
 */
#pragma once

#include <stddef.h>
#include <stdint.h>

int pc_serial_init();

int pc_serial_start_coredump();
int pc_serial_switch_rate();
int pc_serial_send_coredump(const void *data, const size_t dataLen);

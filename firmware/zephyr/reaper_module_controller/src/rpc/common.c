// for strnlen
#define _POSIX_C_SOURCE 200809L

#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(rpc_common, CONFIG_APP_LOG_LEVEL);

#include <zephyr/net/net_if.h>
#include <zephyr/net/net_ip.h>
#include <zephyr/sys/atomic.h>
#include <zephyr/sys/byteorder.h>
#include <zephyr/sys/reboot.h>

#include <utils/carbon_response_codes.h>
#include <utils/handle_errors.h>
#include <utils/mcuboot_helpers.h>

#include <string.h>

#include "generated/lib/drivers/nanopb/proto/reaper_module_controller.pb.h"
#include "generated/lib/drivers/nanopb/proto/version.pb.h"

#include "drivers/eeprom.h"
#include "module_id.h"
#include "network/utils.h"
#include "power/nanopb.h"
#include "power/power.h"
#include "power/relays.h"
#include "rpc/common.h"
#include "scanners.h"
#include "sensors/nanopb.h"
#include "strobe/nanopb.h"

// Unlock key for writing OTP (board id)
#define LOCK_KEY_BOARD_ID (0x59454554)
// Lock flag index for writing OTP (board id)
#define LOCK_FLAG_INDEX_BOARD_ID (0)
// Timeout for the board ID writing, after which the lock is released
#define BOARD_ID_UNLOCK_TIMEOUT (K_SECONDS(1))

static void unlock_bit(const uintptr_t bits, const k_timeout_t timeout);
static void lock_all();
static void handle_lock_timeout(struct k_timer *);

// Flags used to lock potentially destructive actions behind some sort of timeout/gate
__dtcm_data_section static atomic_t gLockFlags = ATOMIC_INIT(0);
// Timer used to reset the lock flags after timeout
K_TIMER_DEFINE(gLockTimeout, handle_lock_timeout, NULL);

/**
 * @brief Unlock access to a particular function
 *
 * This will set the appropriate lock flag, which allows access to the relevant feature. Then a
 * timer is set to clear the flags again after the specified timeout.
 */
static void unlock_bit(const uintptr_t bit, const k_timeout_t timeout) {
  atomic_set_bit(&gLockFlags, bit);
  k_timer_start(&gLockTimeout, timeout, K_NO_WAIT);
}

/**
 * @brief Re-lock access
 *
 * All previously enabled functions are disabled and the timer stopped.
 */
static void lock_all() {
  atomic_clear(&gLockFlags);
  k_timer_stop(&gLockTimeout);
}

/**
 * @brief Timeout handler for the lock timer
 *
 * Clear all lock bits so all scary functions are inhibited again.
 */
static void handle_lock_timeout(struct k_timer *) {
  LOG_INF("relocking OTP due to timeout");

  lock_all();
}

/**
 * @brief Handle request to reboot board
 */
int rpc_handle_reboot(const version_Reset_Request *request, const rpc_flags_t flags) {
  LOG_WRN("rebooting due to reset request (via %s)", (flags & kRpcFlagNetwork) ? "UDP" : "serial");

  k_sleep(K_SECONDS(1));
  sys_reboot(SYS_REBOOT_COLD);

  return 0;
}

/**
 * @brief Process a version request
 *
 * Get the current firmware version from the mcuboot header.
 */
int rpc_handle_version(const version_Version_Request *request, version_Version_Reply *reply, bool *sendGenericReply,
                       const rpc_flags_t flags) {
  int err;
  unsigned int maj, min;

  err = mcuboot_get_running_version(&maj, &min, NULL, NULL);
  if (err) {
    // IO error likely means we're on dev firmware flashed via jflasher
    if (err != -EIO) {
      LOG_WRN("%s failed: %d", "mcuboot_get_running_version", err);
    }
    return err;
  }

  *sendGenericReply = false;
  reply->major = maj;
  reply->minor = min;

  return CARBON_RESPONSE_OK;
}

/**
 * @brief Respond to a configuration request
 *
 * This endpoint is used to update nonvolatile configuration (network address, module location,
 * etc.) as well as query this same information and some other data written to OTP during
 * provisioning.
 */
int rpc_handle_config(const reaper_module_controller_ConfigRequest *request,
                      reaper_module_controller_ConfigReply *reply, bool *sendGenericReply, const rpc_flags_t flags) {
  int err = 0;
  memset(reply, 0, sizeof(*reply));

  switch (request->which_request) {
  // handle locking/unlocking OTP access
  case reaper_module_controller_ConfigRequest_otpLock_tag: {
    const reaper_module_controller_SetOtpLockRequest *args = &request->request.otpLock;
    if (args->lock) {
      lock_all();
    } else {
      if (args->key != LOCK_KEY_BOARD_ID) {
        LOG_WRN("invalid OTP unlock key %08x (expected %08x)", args->key, LOCK_KEY_BOARD_ID);
        err = -EPERM;
      } else {
        unlock_bit(LOCK_FLAG_INDEX_BOARD_ID, BOARD_ID_UNLOCK_TIMEOUT);
      }
    }
    break;
  }

  // write module identity (row/module number)
  case reaper_module_controller_ConfigRequest_setIdentity_tag: {
    const reaper_module_controller_ModuleIdentity *args = &request->request.setIdentity;
    module_id_t id;
    memset(&id, 0, sizeof(id));

    id.module = args->number;

    err = module_id_set(&id);
    if (err) {
      LOG_WRN("failed to %s %s: %d", "write", "module id", err);
    }
    break;
  }
  // get the current module id
  case reaper_module_controller_ConfigRequest_getIdentity_tag: {
    module_id_t id;
    reaper_module_controller_ModuleIdentity *idReply = &reply->reply.identity;

    err = module_id_get(&id);
    if (err) {
      // module id not being set is not worthy of an error log
      if (err != -ENOENT) {
        LOG_WRN("failed to %s %s: %d", "read", "module id", err);
      }
      break;
    }

    // prepare the response
    reply->which_reply = reaper_module_controller_ConfigReply_identity_tag;
    idReply->number = id.module;

    *sendGenericReply = false;
    return 0;
  }

  // write CBSN, module assembly S/N
  case reaper_module_controller_ConfigRequest_setBoardIdentity_tag: {
    const reaper_module_controller_SetBoardIdRequest *args = &request->request.setBoardIdentity;

    // ensure that OTP is unlocked for writing
    if (!atomic_test_bit(&gLockFlags, LOCK_FLAG_INDEX_BOARD_ID)) {
      err = -EPERM;
      break;
    }

    // set CBSN
    if (args->which__cbsn == reaper_module_controller_SetBoardIdRequest_cbsn_tag) {
      const size_t len = strnlen(args->_cbsn.cbsn, sizeof(args->_cbsn.cbsn));
      err = module_pcb_sn_set(args->_cbsn.cbsn, len);
      if (err) {
        LOG_WRN("failed to %s %s: %d", "write", "CBSN", err);
        goto beach;
      }

      LOG_INF("Wrote %s: `%.*s`", "CBSN", len, args->_cbsn.cbsn);
    }

    // set assembly SN
    if (args->which__assySn == reaper_module_controller_SetBoardIdRequest_assySn_tag) {
      const size_t len = strnlen(args->_assySn.assySn, sizeof(args->_assySn.assySn));
      err = module_assy_sn_set(args->_assySn.assySn, len);
      if (err) {
        LOG_WRN("failed to %s %s: %d", "write", "module S/N", err);
        goto beach;
      }

      LOG_INF("Wrote %s: `%.*s`", "module S/N", len, args->_assySn.assySn);
    }

  beach:;
    // drop the write unlock flag (even on failure)
    lock_all();
    break;
  }

  default:
    LOG_WRN("%s: unknown request type %d", __FUNCTION__, request->which_request);
    err = -EINVAL;
    break;
  }

  // send ack/error reply
  *sendGenericReply = true;
  return err;
}

/**
 * @brief Handle power endpoint request
 *
 * Provides control over all the switchable +24V rails.
 */
int rpc_handle_power(const reaper_module_controller_PowerRequest *request, reaper_module_controller_PowerReply *reply,
                     bool *sendGenericReply, const rpc_flags_t flags) {
  int err;
  memset(reply, 0, sizeof(*reply));
  power_channel_t enable = 0, disable = 0;

  // decode the requested input changes
  if (request->which__ethSwitch == reaper_module_controller_PowerRequest_ethSwitch_tag) {
    if (flags & kRpcFlagNetwork) {
      LOG_WRN("Turning off Ethrenet switch via network is preposterous, so I will ignore this");
    } else {
      if (request->_ethSwitch.ethSwitch) {
        enable |= kPowerEthSwitch;
      } else {
        disable |= kPowerEthSwitch;
      }
    }
  }
  if (request->which__relayBoard == reaper_module_controller_PowerRequest_relayBoard_tag) {
    if (request->_relayBoard.relayBoard) {
      enable |= kPowerRelayBoard;
    } else {
      disable |= kPowerRelayBoard;
    }
  }
  if (request->which__strobeBoard == reaper_module_controller_PowerRequest_strobeBoard_tag) {
    if (request->_strobeBoard.strobeBoard) {
      enable |= kPowerStrobeBoard;
    } else {
      disable |= kPowerStrobeBoard;
    }
  }
  if (request->which__predictCam == reaper_module_controller_PowerRequest_predictCam_tag) {
    if (request->_predictCam.predictCam) {
      enable |= kPowerPredictCam;
    } else {
      disable |= kPowerPredictCam;
    }
  }

  // set state if any of them changed
  if (enable || disable) {
    err = power_update(enable, disable);
    if (err) {
      LOG_WRN("%s failed: %d", "power_update", err);
      goto error;
    }
  }

  // respond with current state
  err = power_nanopb_fill(reply);
  if (err) {
    LOG_WRN("%s failed: %d", "power_nanopb_fill", err);
    goto error;
  }

  *sendGenericReply = false;
  return 0;

error:;
  // send ack/error reply
  *sendGenericReply = true;
  return err;
}

/**
 * @brief Process a relay endpoint request
 *
 * One or more relays may be updated in a single request depending on whether the proto field is
 * present in the message or not
 */
int rpc_handle_relay(const reaper_module_controller_RelayRequest *request, reaper_module_controller_RelayReply *reply,
                     bool *sendGenericReply, const rpc_flags_t flags) {
  int err;

  // handle setting state
  if (request->which__btl == reaper_module_controller_RelayRequest_btl_tag) {
    err = relays_set_single(kRelayBtl, request->_btl.btl);
    if (err) {
      LOG_WRN("%s(%s) failed: %d", "relays_set_single", "BTL", err);
      goto error;
    }
  }
  if (request->which__laser == reaper_module_controller_RelayRequest_laser_tag) {
    err = relays_set_single(kRelayLdd, request->_laser.laser);
    if (err) {
      LOG_WRN("%s(%s) failed: %d", "relays_set_single", "Lasers", err);
      goto error;
    }
  }
  if (request->which__pc == reaper_module_controller_RelayRequest_pc_tag) {
    err = relays_set_single(kRelayPc, request->_pc.pc);
    if (err) {
      LOG_WRN("%s(%s) failed: %d", "relays_set_single", "PC", err);
      goto error;
    }
  }

  // respond with the current state
  HANDLE_UNLIKELY(relays_nanopb_fill(reply));

  *sendGenericReply = false;
  return 0;

error:;
  // send ack/error reply
  *sendGenericReply = true;
  return err;
}

/**
 * @brief Handle request to network endpoint
 *
 * This is used to retrieve the addresses on the network interface.
 */
int rpc_handle_network(const reaper_module_controller_NetworkRequest *request,
                       reaper_module_controller_NetworkReply *reply, bool *sendGenericReply, const rpc_flags_t flags) {
  int err;
  memset(reply, 0, sizeof(*reply));

  switch (request->which_request) {
  case reaper_module_controller_NetworkRequest_config_tag: {
    reply->which_reply = reaper_module_controller_NetworkReply_config_tag;
    reaper_module_controller_NetworkConfigReply *config = &reply->reply.config;

    struct net_if *intf = net_if_get_default();
    if (!intf) {
      err = -ENXIO;
      goto beach;
    }

    // link state and MAC address
    config->linkUp = network_is_up();

    const struct net_linkaddr *macaddr = net_if_get_link_addr(intf);
    memcpy(config->mac, macaddr->addr, MIN(sizeof(config->mac), macaddr->len));

    // each of the interface's addresses
    const struct net_if_config *ifconfig = net_if_get_config(intf);
    if (!ifconfig) {
      LOG_WRN("%s failed", "net_if_get_config");

      err = -ENODEV;
      goto beach;
    }

    if (ifconfig->ip.ipv4) {
      for (size_t i = 0; i < NET_IF_MAX_IPV4_ADDR; i++) {
        const struct net_if_addr *unicast = &(ifconfig->ip.ipv4->unicast[i]);
        if (unicast->address.in_addr.s_addr == INADDR_ANY || !unicast->is_used) {
          continue;
        }

        // addresses are stored big endian; so we may copy directly
        reaper_module_controller_NetworkAddress *outConfig = &(config->addresses[config->addresses_count++]);

        memcpy(outConfig->unicast, unicast->address.in_addr.s4_addr, 4);
        memcpy(outConfig->subnet, ifconfig->ip.ipv4->netmask.s4_addr, 4);

        if (ifconfig->ip.ipv4->gw.s_addr) {
          outConfig->which__gateway = reaper_module_controller_NetworkAddress_gateway_tag;
          memcpy(outConfig->_gateway.gateway, ifconfig->ip.ipv4->gw.s4_addr, 4);
        }

        switch (unicast->addr_type) {
        case NET_ADDR_DHCP:
          outConfig->source = reaper_module_controller_NetworkAddressSource_DHCP;
          break;
        case NET_ADDR_MANUAL:
        case NET_ADDR_AUTOCONF:
          outConfig->source = reaper_module_controller_NetworkAddressSource_STATIC;
          break;

        default:
          outConfig->source = reaper_module_controller_NetworkAddressSource_MANUAL;
          LOG_WRN("unknown address type (addr %08x, type %u)", unicast->address.in_addr.s_addr, unicast->addr_type);
          break;
        }
      }
    } else {
      LOG_WRN("no IPv4 addresses (wtf) for interface %p", intf);
    }

    *sendGenericReply = false;
    return 0;
  }
  case reaper_module_controller_NetworkRequest_powerCycle_tag:
    LOG_WRN("Power cycling network (request from %s)", (flags & kRpcFlagNetwork) ? "UDP" : "serial");
    k_sleep(K_SECONDS(1));

    err = network_do_power_cycle();

    // add additional sleep if called via UDP to give the response a better chance of making it
    if (!err && (flags & kRpcFlagNetwork)) {
      k_sleep(K_SECONDS(5));
    }
    goto beach;

  default:
    LOG_WRN("%s: unknown request type %d", __FUNCTION__, request->which_request);
    err = -EINVAL;
    break;
  }

  // send ack/error reply
beach:;
  *sendGenericReply = true;
  return err;
}

/**
 * @brief Handle request to the hardware info endpoint
 *
 * Used to retrieve information such as module board revision and serial numbers.
 */
int rpc_handle_hwinfo(const hwinfo_Request *request, hwinfo_Reply *reply, bool *sendGenericReply,
                      const rpc_flags_t flags) {
  int err;
  memset(reply, 0, sizeof(*reply));

  switch (request->which_request) {
  /*
   * Retrieve PCB version information
   *
   * This includes the hardware revision (from the GPIO strap pins) as well as the board
   * configuration, taken from the dts.
   */
  case hwinfo_Request_version_tag: {
    reply->which_reply = hwinfo_Reply_version_tag;
    hwinfo_BoardVersionReply *msg = &reply->reply.version;

    const char *compatible = DT_PROP(DT_ROOT, compatible);
    if (compatible) {
      strncpy(msg->model, compatible, sizeof(msg->model));
    }

    HANDLE_UNLIKELY(module_hw_rev_get(&msg->rev))

    *sendGenericReply = false;
    return 0;
  }

  /*
   * Retrieve hardware serial number (identity)
   *
   * CBSN is programmed into the board during manufacturing/bringup. The module serial
   * number is set during validation. Both are stored in nonvolatile memory.
   */
  case hwinfo_Request_identity_tag:
    reply->which_reply = hwinfo_Reply_identity_tag;
    hwinfo_BoardIdentityReply *msg = &reply->reply.identity;

    err = module_pcb_sn_get(msg->_cbsn.cbsn, sizeof(msg->_cbsn.cbsn));
    if (err < 0) {
      LOG_WRN("failed to %s %s: %d", "read", "CBSN", err);
    } else {
      msg->which__cbsn = hwinfo_BoardIdentityReply_cbsn_tag;
    }

    err = module_assy_sn_get(msg->_assySn.assySn, sizeof(msg->_assySn.assySn));
    if (err < 0) {
      LOG_WRN("failed to %s %s: %d", "read", "module SN", err);
    } else {
      msg->which__assySn = hwinfo_BoardIdentityReply_assySn_tag;
    }

    *sendGenericReply = false;
    return 0;

  default:
    LOG_WRN("%s: unknown request type %d", __FUNCTION__, request->which_request);
    err = -EINVAL;
    break;
  }

  // send ack/error reply
  *sendGenericReply = true;
  return err;
}

/**
 * @brief Fill in a board status request
 *
 * This is used by hardware_manager to poll the status of the MCB.
 */
int rpc_handle_status(const reaper_module_controller_StatusRequest *request,
                      reaper_module_controller_StatusReply *reply, bool *sendGenericReply, const rpc_flags_t flags) {
  int err = 0;
  *sendGenericReply = false;

  // get sensor data
  err = sensors_nanopb_fill(&reply->sensors);
  if (err) {
    LOG_WRN("%s failed: %d", "sensors_nanopb_fill", err);
  } else {
    reply->has_sensors = true;
  }

  // retrieve relay and power status
  err = relays_nanopb_fill(&reply->relays);
  if (err) {
    LOG_WRN("%s failed: %d", "relays_nanopb_fill", err);
  } else {
    reply->has_relays = true;
  }

  err = power_nanopb_fill(&reply->power);
  if (err) {
    LOG_WRN("%s failed: %d", "power_nanopb_fill", err);
  } else {
    reply->has_power = true;
  }

  // get strobe status
  err = strobe_nanopb_fill_status(&reply->strobe);
  if (err) {
    LOG_WRN("%s failed: %d", "strobe_nanopb_fill_status", err);
  } else {
    reply->has_strobe = true;
  }

  // get the scanner's status
  err = scanners_nanopb_fill_status(reply->scanners, ARRAY_SIZE(reply->scanners));

  if (err) {
    LOG_WRN("%s failed: %d", "scanners_nanopb_fill_status", err);
  } else {
    reply->scanners_count = NUM_SCANNERS;
  }

  return err;
}

/**
 * @file
 * @brief Module identity manager
 */
#pragma once

#include <stddef.h>
#include <stdint.h>

typedef struct module_id {
  // module index on the machine
  uint32_t module;
} module_id_t;

typedef struct board_id {
  // PCB serial number
  char cbsn[16];
  // Module serial number
  char moduleSn[32];
} board_id_t;

int module_id_init();
int module_id_get(module_id_t *outId);
int module_id_set(const module_id_t *newId);

int module_hw_rev_get(uint32_t *outRev);

int module_pcb_sn_get(char *outBuf, const size_t outBufLen);
int module_pcb_sn_set(const char *sn, const size_t snLen);

int module_assy_sn_get(char *outBuf, const size_t outBufLen);
int module_assy_sn_set(const char *sn, const size_t snLen);

bool module_is_validation();

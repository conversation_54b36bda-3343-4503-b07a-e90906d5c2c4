#include <logging/log.h>
LOG_MODULE_REGISTER(module_id, CONFIG_APP_LOG_LEVEL);

#include <device.h>
#include <devicetree.h>
#include <drivers/gpio.h>
#include <stdbool.h>
#include <stdint.h>
#include <sys/byteorder.h>
#include <sys/crc.h>

#include <drivers/hw_rev_straps.h>
#include <utils/carbon_response_codes.h>
#include <utils/handle_errors.h>

#include "drivers/eeprom.h"
#include "module_id.h"
#include "network/init.h"
#include "strobe/strobe.h"

// Highest possible module ID
#define MAX_MODULE_ID (100)

/**
 * @brief Module ID (as stored in NVRAM)
 *
 * All multibyte values are stored in big endian byte order.
 */
typedef struct eeprom_module_id {
  // total length of this struct (bytes)
  uint32_t size;
  // actual module id data
  module_id_t data;
} eeprom_module_id_t;

/**
 * @brief Board ID
 *
 * This is stored in the EEPROM and holds the CBSN (e.g. PCB serial number) as well as the assembly
 * (weeding module) serial number.
 */
typedef struct eeprom_board_id {
  // Length of this struct, in bytes
  uint32_t size;
  // Board and assembly serial numbers
  board_id_t data;
} __attribute__((packed)) eeprom_board_id_t;

typedef struct module_id_state {
  struct k_mutex lock;

  // is the stored module ID valid?
  bool moduleIdValid;
  // is the stored board id valid?
  bool boardIdValid;

  // module ID (as read from NVRAM)
  module_id_t moduleId;
  // board ID (as read from NVRAM)
  board_id_t boardId;
} module_id_state_t;

// hw rev device
static const struct device *gHwRevs = DEVICE_DT_GET_OR_NULL(DT_PATH(hw_rev));

// Module ID state (initialized during startup/whenever requests come in externally)
__dtcm_bss_section static module_id_state_t gState;

static int write_module_id(const module_id_t *newId);
static int try_read_module_id();
static bool validate_module_id(const eeprom_module_id_t *block);

static int write_board_id(const board_id_t *newId);
static int try_read_board_id();
static bool validate_board_id(const eeprom_board_id_t *block);

/**
 * @brief Initialize the module ID system
 *
 * Read module ID from NVRAM, if it's been programmed in.
 */
int module_id_init() {
  uint32_t hwRev;
  int err;

  memset(&gState, 0, sizeof(gState));
  HANDLE_UNLIKELY(k_mutex_init(&gState.lock));

  // get HW rev: should already have been acquired
  HANDLE_UNLIKELY_BOOL(device_is_ready(gHwRevs), ENODEV);
  HANDLE_UNLIKELY(hw_rev_straps_get(gHwRevs, &hwRev));

  LOG_INF("hw rev %u", hwRev);

  // try to read module id from NVRAM
  err = try_read_module_id();
  if (err) {
    LOG_WRN("failed to read %s id from NVRAM: %d", "module", err);
  }

  // try to read board id from NVRAM
  err = try_read_board_id();
  if (err) {
    LOG_WRN("failed to read %s id from NVRAM: %d", "board", err);
  }

  return 0;
}

/**
 * @brief Retrieve module ID
 *
 * Get the current module ID: this was either read from NVRAM during boot or updated by a previous
 * request to write the module id.
 */
int module_id_get(module_id_t *outId) {
  if (!outId) {
    return -EFAULT;
  }

  int err;

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    if (gState.moduleIdValid) {
      *outId = gState.moduleId;
      err = 0;
    } else {
      err = -ENOENT;
    }
  }
  k_mutex_unlock(&gState.lock);

  return err;
}

/**
 * @brief Update the module ID
 *
 * Set the new module ID and write it to NVRAM.
 */
int module_id_set(const module_id_t *newId) {
  if (!newId) {
    return -EFAULT;
  }

  bool changed = false;
  int err;

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    // bail if module id is the same as what's already written
    if (gState.moduleIdValid && gState.moduleId.module == newId->module) {
      err = 0;
      goto beach;
    }

    changed = true;

    // write to NVRAM
    err = write_module_id(newId);
    if (err) {
      goto beach;
    }

    // then update internally
    gState.moduleId = *newId;
    gState.moduleIdValid = true;
  }
beach:;
  k_mutex_unlock(&gState.lock);

  if (gState.moduleIdValid) {
    if (changed) {
      HANDLE_UNLIKELY(network_update());
    }

    // turn on strobes if we set an id
    HANDLE_UNLIKELY(strobe_set_enabled(true));
  }

  return err;
}

/**
 * @brief Retrieve hardware revision
 */
int module_hw_rev_get(uint32_t *outRev) {
  if (!outRev) {
    return -EFAULT;
  }

  HANDLE_UNLIKELY(hw_rev_straps_get(gHwRevs, outRev));

  return 0;
}

/**
 * @brief Retrieve PCB serial number
 *
 * This matches the CBSN sticker placed on the board.
 *
 * @return Negative error code or length of the CBSN
 */
int module_pcb_sn_get(char *outBuf, const size_t outBufLen) {
  if (!outBuf) {
    return -EFAULT;
  }

  int err;

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    if (gState.boardIdValid) {
      strncpy(outBuf, gState.boardId.cbsn, MIN(outBufLen, sizeof(gState.boardId.cbsn)));
      err = strlen(gState.boardId.cbsn);
    } else {
      err = -ENOENT;
    }
  }
  k_mutex_unlock(&gState.lock);

  return err;
}

/**
 * @brief Update the CBSN
 *
 * Set the new CBSN in the board ID struct and write it to NVRAM.
 */
int module_pcb_sn_set(const char *sn, const size_t snLen) {
  if (!sn || snLen > sizeof(gState.boardId.cbsn)) {
    return -EFAULT;
  }

  int err;

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    // assemble a struct here pls
    board_id_t block;
    memset(&block, 0, sizeof(block));

    if (gState.boardIdValid) {
      block = gState.boardId;
    }

    memset(block.cbsn, 0, sizeof(block.cbsn));
    strncpy(block.cbsn, sn, MIN(snLen, sizeof(block.cbsn)));

    // write to NVRAM
    err = write_board_id(&block);
    if (err) {
      goto beach;
    }

    // then update internally
    gState.boardId = block;
    gState.boardIdValid = true;
  }
beach:;
  k_mutex_unlock(&gState.lock);

  return err;
}

/**
 * @brief Retrieve assembly serial number
 *
 * Each module has an overall assembly serial number, which is stored as the "assembly S/N."
 *
 * @return Negative error code or length of the assembly S/N
 */
int module_assy_sn_get(char *outBuf, const size_t outBufLen) {
  if (!outBuf) {
    return -EFAULT;
  }

  int err;

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    if (gState.boardIdValid) {
      strncpy(outBuf, gState.boardId.moduleSn, MIN(outBufLen, sizeof(gState.boardId.moduleSn)));
      err = strlen(gState.boardId.moduleSn);
    } else {
      err = -ENOENT;
    }
  }
  k_mutex_unlock(&gState.lock);

  return err;
}

/**
 * @brief Update the module serial
 *
 * Set the new module sn in the board ID struct and write it to NVRAM.
 */
int module_assy_sn_set(const char *sn, const size_t snLen) {
  if (!sn || snLen > sizeof(gState.boardId.moduleSn)) {
    return -EFAULT;
  }

  int err;

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    // assemble a struct here pls
    board_id_t block;
    memset(&block, 0, sizeof(block));

    if (gState.boardIdValid) {
      block = gState.boardId;
    }

    memset(block.moduleSn, 0, sizeof(block.moduleSn));
    strncpy(block.moduleSn, sn, MIN(snLen, sizeof(block.moduleSn)));

    // write to NVRAM
    err = write_board_id(&block);
    if (err) {
      goto beach;
    }

    // then update internally
    gState.boardId = block;
    gState.boardIdValid = true;
  }
beach:;
  k_mutex_unlock(&gState.lock);

  return err;
}

/**
 * @brief Determine if the module is in validation
 *
 * At the validation process, the module will not have a module ID. Additionally, the validation
 * station shall include a special DHCP option to communicate that it's running in validation.
 */
bool module_is_validation() {
  bool inValidation = true;

  HANDLE_UNLIKELY(k_mutex_lock(&gState.lock, K_FOREVER));
  {
    // first, check for no module id
    if (gState.moduleIdValid && gState.moduleId.module) {
      inValidation = false;
      goto beach;
    }

    // TODO: check for the DHCP option
  }
beach:;
  k_mutex_unlock(&gState.lock);

  return inValidation;
}

/**
 * @brief Read module ID from NVRAM
 */
static int try_read_module_id() {
  int err;

  // read into temporary buffer
  eeprom_module_id_t buf;
  size_t bufLen = sizeof(buf);

  err = eeprom_read_block(kEepromBlockTypeModuleId, &buf, &bufLen);
  if (err) {
    return err;
  }

  // validate the block and if valid store in internal state for retrieval
  if (!validate_module_id(&buf)) {
    LOG_HEXDUMP_WRN(&buf, sizeof(buf), "module id block invalid");
    return -EINVAL;
  }

  module_id_t newId;
  memset(&newId, 0, sizeof(newId));

  newId.module = sys_be32_to_cpu(buf.data.module);

  gState.moduleId = newId;
  gState.moduleIdValid = true;

  return 0;
}

/**
 * @brief Write module ID to NVRAM
 */
static int write_module_id(const module_id_t *newId) {
  int err;
  eeprom_module_id_t buf;

  if (!newId) {
    return -EFAULT;
  }

  // setting ID = 0 erases the block
  if (newId->module == 0) {
    LOG_WRN("Deleting module ID!");
    err = eeprom_erase_block(kEepromBlockTypeModuleId);

    // not an error if module id cannot be deleted (e.g. it was never set)
    if (err != -ENOENT) {
      return err;
    } else {
      return 0;
    }
  }

  // set up byteswapped nvram structure
  memset(&buf, 0, sizeof(buf));
  buf.size = sys_cpu_to_be32(sizeof(buf));

  buf.data.module = sys_cpu_to_be32(newId->module);

  if (!validate_module_id(&buf)) {
    LOG_HEXDUMP_WRN(&buf, sizeof(buf), "module id block invalid");
    return -EINVAL;
  }

  // then write it out to EEPROM
  err = eeprom_write_block(kEepromBlockTypeModuleId, &buf, sizeof(buf));
  if (err) {
    LOG_WRN("%s failed: %d", "eeprom_write_block", err);
    return err;
  }

  return 0;
}

/**
 * @brief Validate a module id structure (for NVRAM)
 *
 * Ensure that the size is sane and that the values within the payload are also sane.
 */
static bool validate_module_id(const eeprom_module_id_t *block) {
  // check size
  const size_t swappedSize = sys_be32_to_cpu(block->size);
  if (swappedSize < sizeof(*block)) {
    LOG_WRN("%s: invalid size (got %u, expected at least %u)", __FUNCTION__, swappedSize, sizeof(*block));
    return false;
  }

  // module ID should be somewhat sane
  const uint32_t swappedId = sys_be32_to_cpu(block->data.module);
  if (swappedId > MAX_MODULE_ID) {
    return false;
  }

  return true;
}

/**
 * @brief Read board ID from NVRAM
 */
static int try_read_board_id() {
  int err;

  // read into temporary buffer
  eeprom_board_id_t buf;
  size_t bufLen = sizeof(buf);

  err = eeprom_read_block(kEepromBlockTypeBoardId, &buf, &bufLen);
  if (err) {
    return err;
  }

  // validate the block and if valid store in internal state for retrieval
  if (!validate_board_id(&buf)) {
    LOG_HEXDUMP_WRN(&buf, sizeof(buf), "board id block invalid");
    return -EINVAL;
  }

  board_id_t newId;
  memset(&newId, 0, sizeof(newId));

  newId = buf.data;

  gState.boardId = newId;
  gState.boardIdValid = true;

  return 0;
}

/**
 * @brief Write board ID to NVRAM
 */
static int write_board_id(const board_id_t *newId) {
  int err;
  eeprom_board_id_t buf;

  if (!newId) {
    return -EFAULT;
  }

  // set up byteswapped nvram structure
  memset(&buf, 0, sizeof(buf));
  buf.size = sys_cpu_to_be32(sizeof(buf));
  buf.data = *newId;

  if (!validate_board_id(&buf)) {
    LOG_HEXDUMP_WRN(&buf, sizeof(buf), "board id block invalid");
    return -EINVAL;
  }

  // then write it out to EEPROM
  err = eeprom_write_block(kEepromBlockTypeBoardId, &buf, sizeof(buf));
  if (err) {
    LOG_WRN("%s failed: %d", "eeprom_write_block", err);
    return err;
  }

  return 0;
}

/**
 * @brief Validate a board id structure (for NVRAM)
 */
static bool validate_board_id(const eeprom_board_id_t *block) {
  // check size
  const size_t swappedSize = sys_be32_to_cpu(block->size);
  if (swappedSize < sizeof(*block)) {
    LOG_WRN("%s: invalid size (got %u, expected at least %u)", __FUNCTION__, swappedSize, sizeof(*block));
    return false;
  }

  return true;
}

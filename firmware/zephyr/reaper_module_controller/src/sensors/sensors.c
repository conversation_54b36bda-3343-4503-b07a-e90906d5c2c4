#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(sensors, CONFIG_APP_LOG_LEVEL);

#include <math.h>
#include <string.h>

#include <zephyr/device.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/adc.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/drivers/sensor.h>
#include <zephyr/irq.h>
#include <zephyr/kernel.h>
#include <zephyr/net/ptp_time.h>
#include <zephyr/sys/atomic.h>

#include <drivers/imu_bmi270.h>
#include <lib/ptp/ptp.h>
#include <utils/adc.h>
#include <utils/carbon_response_codes.h>
#include <utils/handle_errors.h>

#include "sensors.h"

#define NUM_ENVIRO_SENSORS (2)
#define NUM_EXT_THERMISTORS (2)
#define NUM_PRESS_SENSORS (2)
#define NUM_LEAK_SENSORS (2)
// Total number of thermistors from all sources
#define NUM_THERMISTORS (NUM_EXT_THERMISTORS + NUM_PRESS_SENSORS)

// Worker thread priority for reading the sensors
#define THREAD_PRIORITY K_PRIO_PREEMPT(9)

// Time interval for sampling sensors; each sensor can be sampled at some integer multiple of this
#define SENSOR_INTERVAL K_MSEC(100)
// Thermistor reading interval (multiple of SENSOR_INTERVAL)
#define THERMISTOR_INTERVAL (10)
// Environmental sensor reading interval (multiple of SENSOR_INTERVAL)
#define ENVIRO_INTERVAL (5)
// IMU sensor reading interval (multiple of SENSOR_INTERVAL)
#define IMU_INTERVAL (1)
// Pressure sensor reading interval (multiple of SENSOR_INTERVAL)
#define PRESS_INTERVAL (5)
// Leak sensor reading interval
#define LEAK_INTERVAL (1)

// Offset in the thermistor device tree array to the pressure transducer thermistors
#define THERM_OFFSET_PRESSURE (2)

/**
 * @brief Definition of constants to convert thermistor voltage to temperature
 */
typedef struct thermistor_spec {
  // Upper resistor (R1) in thermistor readout divider (for voltage -> resistance conversion)
  float r1;

  // Temperature for nominal resistance (and β)
  float refTemp;
  // Resistance (in Ω) of thermistor at reference temperature
  float refResistance;
  // β constant for Steinhart-Hart interpolation
  float beta;
} thermistor_spec_t;

typedef struct sensor_state {
  // Mutex used to protect access to the data within
  struct k_mutex dataLock;

  // environmental sensors
  struct {
    struct net_ptp_time timestamp;

    /// Absolute temperature (°C)
    float temp;
    /// Relative humidity (RH%, [0, 1])
    float humidity;
    /// Air pressure (Pa)
    float pressure;
  } environment[NUM_ENVIRO_SENSORS];
  // Interval counter for environmental sensors
  size_t enviroSampleCounter;
  // flag indicating the second environmental sensor exists
  bool hasExternalEnviro;

  // readings from thermistors
  struct {
    struct net_ptp_time timestamp;
    float temps[NUM_EXT_THERMISTORS];
  } therm;
  // Interval counter for thermistor data
  size_t thermSampleCounter;

  // IMU (combo of accelerometer and gryoscope)
  struct {
    imu_bmi270_data_t raw;
    // Board inclination angles: (x, y, z) in radians
    double inclination[3];
  } imu;
  // Interval counter for IMU data
  size_t imuSampleCounter;
  // whether IMU initialization was successfu
  bool imuOk;

  // External pressure transducers, which include a thermistor
  struct {
    struct net_ptp_time timestamp;
    float pressure;
    float temperature;
  } pressure[NUM_PRESS_SENSORS];
  // Interval counter for sampling pressure transducers
  size_t pressSampleCounter;

  // Leak sensor, including last change timestamp
  struct {
    struct net_ptp_time timestamp;
    // Whether a leak was detected
    bool state;
  } leak[NUM_LEAK_SENSORS];
  // Interval counter for sampling of leak sensors
  size_t leakSampleCounter;
} sensor_state_t;

static void sensor_worker();
static int imu_update();
static int enviro_init();
static int enviro_update(const struct device *dev, const size_t dataIndex);
static int thermistor_init();
static int thermistor_update();
static int thermistor_read(const struct adc_dt_spec *data, const thermistor_spec_t *convspec, float *outTemp);
static int pressure_init();
static int pressure_update();
static int pressure_read(const struct adc_dt_spec *data, float *outPressure);
static int leak_init();
static int leak_update();

/// Convert a sensor value to floating point
static inline float sensor_value_to_float(const struct sensor_value *val) {
  return ((float)val->val1) + (((float)val->val2) / 1000000.f);
}

/// State for all sensor readings
__dtcm_bss_section static sensor_state_t gState;

/// Device object for IMU
static const struct device *const gDevImu = DEVICE_DT_GET(DT_NODELABEL(imu0));
/// Device object for internal environmental sensor
static const struct device *const gDevEnviroInternal = DEVICE_DT_GET(DT_NODELABEL(enviro0));
/// External environmental sensor (optional)
static const struct device *const gDevEnviroExternal = DEVICE_DT_GET(DT_NODELABEL(enviro1));
/// Thermistor ADC channels
static const struct adc_dt_spec gThermistor[NUM_THERMISTORS] = {
    ADC_DT_SPEC_GET_BY_NAME(DT_PATH(sensors, thermistors), therm1),
    ADC_DT_SPEC_GET_BY_NAME(DT_PATH(sensors, thermistors), therm2),
    // Pressure transducer 1, bonus thermistor
    ADC_DT_SPEC_GET_BY_NAME_OR_NULL(DT_PATH(sensors, thermistors), press1),
    // Pressure transducer 2, bonus thermistor
    ADC_DT_SPEC_GET_BY_NAME_OR_NULL(DT_PATH(sensors, thermistors), press2),
};
/// Leak sensor GPIO channels
static const struct gpio_dt_spec gLeak[NUM_LEAK_SENSORS] = {
    GPIO_DT_SPEC_GET_BY_IDX(DT_PATH(sensors, leak), gpios, 0),
    GPIO_DT_SPEC_GET_BY_IDX(DT_PATH(sensors, leak), gpios, 1),
};
/// Pressure transducer ADC channels
static const struct adc_dt_spec gPressure[NUM_PRESS_SENSORS] = {
#if DT_NODE_EXISTS(DT_PATH(sensors, pressure))
    ADC_DT_SPEC_GET_BY_NAME(DT_PATH(sensors, pressure), press1),
    ADC_DT_SPEC_GET_BY_NAME(DT_PATH(sensors, pressure), press2),
#else
    {NULL},
    {NULL},
#endif
};

K_THREAD_DEFINE(sensors_thread_id, 1536, sensor_worker, NULL, NULL, NULL, THREAD_PRIORITY, 0, K_TICKS_FOREVER);

/**
 * @brief Initialize the sensor manager
 *
 * Set up the various sensors (some are on I2C, others are connected directly to GPIOs or the ADC)
 * and associated peripherals.
 */
int sensors_init() {
  int err;

  memset(&gState, 0, sizeof(gState));
  HANDLE_UNLIKELY(k_mutex_init(&gState.dataLock));

  gState.enviroSampleCounter = ENVIRO_INTERVAL;
  gState.thermSampleCounter = THERMISTOR_INTERVAL;
  gState.imuSampleCounter = IMU_INTERVAL;
  gState.pressSampleCounter = PRESS_INTERVAL;
  gState.leakSampleCounter = LEAK_INTERVAL;

  // required sensors
  HANDLE_UNLIKELY(enviro_init());
  HANDLE_UNLIKELY(thermistor_init());
  HANDLE_UNLIKELY(leak_init());
  HANDLE_UNLIKELY(pressure_init());

  // these can fail to initialize
  err = imu_bmi270_boot(gDevImu);
  gState.imuOk = (err == 0);
  if (err) {
    LOG_ERR("%s failed: %d; %s", "imu_bmi270_boot", err, "IMU readings will be unavailable!");
  }

  // perform initial reading of sensors updated via interrupts
  HANDLE_UNLIKELY(leak_update());

  // start the worker task
  k_thread_name_set(sensors_thread_id, "sensor_io");
  k_thread_start(sensors_thread_id);

  return 0;
}

/**
 * @brief Get most recent environmental sensor data
 *
 * Any of the output pointers may be null.
 *
 * @param which Environmental sensor to read data out from
 * @param outTimestamp Optional variable to receive timestamp this reading was done
 */
int sensors_enviro_get(const enviro_sensor_t which, int64_t *outTimestamp, float *outTemp, float *outHumidity,
                       float *outPressure) {
  if (!outTimestamp && !outTemp && !outHumidity && !outPressure) {
    return -EINVAL;
  } else if (which >= NUM_ENVIRO_SENSORS) {
    return -EINVAL;
  } else if (which == kEnviroExternalPc && !gState.hasExternalEnviro) {
    return -EINVAL;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.dataLock, K_FOREVER));
  {
    if (outTimestamp) {
      *outTimestamp = ptp_ts_net_to_ms(&gState.environment[which].timestamp);
    }
    if (outTemp) {
      *outTemp = gState.environment[which].temp;
    }
    if (outHumidity) {
      *outHumidity = gState.environment[which].humidity;
    }
    if (outPressure) {
      *outPressure = gState.environment[which].pressure;
    }
  }
  HANDLE_UNLIKELY(k_mutex_unlock(&gState.dataLock));

  return CARBON_RESPONSE_OK;
}

/**
 * @brief Test whether an environmental sensor is present
 */
int sensors_enviro_is_present(const enviro_sensor_t which, bool *outIsPresent) {
  if (!outIsPresent) {
    return -EFAULT;
  } else if (which >= NUM_ENVIRO_SENSORS) {
    return -EINVAL;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.dataLock, K_FOREVER));
  {
    if (which == kEnviroInternal) {
      *outIsPresent = true;
    } else if (which == kEnviroExternalPc) {
      *outIsPresent = gState.hasExternalEnviro;
    }
  }
  HANDLE_UNLIKELY(k_mutex_unlock(&gState.dataLock));

  return CARBON_RESPONSE_OK;
}

/**
 * @brief Get the most recent thermistor reading(s)
 *
 * @param which Thermistor index to be read out
 * @param outTimestamp Optional variable to receive timestamp this reading was done
 * @param outTemps Buffer to receive thermistor reading
 */
int sensors_thermistor_get(const size_t which, int64_t *outTimestamp, float *outTemp) {
  if (which >= NUM_EXT_THERMISTORS || (!outTimestamp && !outTemp)) {
    return -EINVAL;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.dataLock, K_FOREVER));
  {
    if (outTimestamp) {
      *outTimestamp = ptp_ts_net_to_ms(&gState.therm.timestamp);
    }

    *outTemp = gState.therm.temps[which];
  }
  HANDLE_UNLIKELY(k_mutex_unlock(&gState.dataLock));

  return CARBON_RESPONSE_OK;
}

/**
 * @brief Get the raw IMU readings
 *
 * @param outTimestamp Optional variable to receive timestamp this reading was done
 * @param outAccel Buffer to receive acceleration data (in g) for all axes
 * @param outAccelSize Number of axes to read data for
 * @param outGyro Buffer to receive gyroscope rates (in °/sec) for all axes
 * @param outGyroSize Number of axes to read data for
 */
int sensors_imu_get_raw(int64_t *outTimestamp, float *outAccel, const size_t outAccelSize, float *outGyro,
                        const size_t outGyroSize) {
  if (!outTimestamp && (!outAccel || !outAccelSize) && (!outGyro || !outGyroSize)) {
    return -EINVAL;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.dataLock, K_FOREVER));
  {
    // TODO: separate timestamps
    if (outTimestamp) {
      *outTimestamp = gState.imu.raw.accel.timestamp;
    }

    if (outAccel) {
      const size_t toCopy = MIN(outAccelSize, ARRAY_SIZE(gState.imu.raw.accel.data));
      memcpy(outAccel, gState.imu.raw.accel.data, toCopy * sizeof(*outAccel));
    }

    if (outGyro) {
      const size_t toCopy = MIN(outGyroSize, ARRAY_SIZE(gState.imu.raw.gyro.data));
      memcpy(outGyro, gState.imu.raw.gyro.data, toCopy * sizeof(*outGyro));
    }
  }
  HANDLE_UNLIKELY(k_mutex_unlock(&gState.dataLock));

  return CARBON_RESPONSE_OK;
}

/**
 * @brief Get board inclination
 *
 * This is derived from the IMU accelerometer data.
 *
 * On production boards, the positive X axis faces the side panel, the positive Y axis towards
 * the rear panel, and the positive Z axis up towards the sky.
 */
int sensors_imu_get_inclination(int64_t *outTimestamp, double *outAngles, const size_t numOutAngles) {
  if (!outTimestamp && !outAngles) {
    return -EFAULT;
  } else if (!numOutAngles) {
    return -EINVAL;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.dataLock, K_FOREVER));
  {
    const size_t toCopy = MIN(numOutAngles, ARRAY_SIZE(gState.imu.inclination));
    memcpy(outAngles, gState.imu.inclination, sizeof(*outAngles) * toCopy);
  }
  HANDLE_UNLIKELY(k_mutex_unlock(&gState.dataLock));

  return 0;
}

/**
 * @brief Get the leak sensor state
 *
 * Read out the last time the given leak sensor changed state, as well as its new state.
 */
int sensors_leak_get(const size_t which, int64_t *outTimestamp, bool *outState) {
  if (which >= NUM_LEAK_SENSORS || (!outTimestamp && !outState)) {
    return -EINVAL;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.dataLock, K_FOREVER));
  {
    if (outTimestamp) {
      *outTimestamp = ptp_ts_net_to_ms(&gState.leak[which].timestamp);
    }
    if (outState) {
      *outState = gState.leak[which].state;
    }
  }
  HANDLE_UNLIKELY(k_mutex_unlock(&gState.dataLock));

  return CARBON_RESPONSE_OK;
}

/**
 * @brief Get reading for pressure transducer
 */
int sensors_pressure_get(const size_t which, int64_t *outTimestamp, float *outPressure, float *outTemp) {
  if (which >= NUM_PRESS_SENSORS || (!outTimestamp && !outPressure && !outTemp)) {
    return -EINVAL;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gState.dataLock, K_FOREVER));
  {
    if (outTimestamp) {
      *outTimestamp = ptp_ts_net_to_ms(&gState.pressure[which].timestamp);
    }
    if (outPressure) {
      *outPressure = gState.pressure[which].pressure;
    }
    if (outTemp) {
      *outTemp = gState.pressure[which].temperature;
    }
  }
  HANDLE_UNLIKELY(k_mutex_unlock(&gState.dataLock));

  return CARBON_RESPONSE_OK;
}

/**
 * @brief Sensor work task main loop
 *
 * This is responsible for reading out all of the sensors on the system.
 */
static void sensor_worker() {
  int err;
  LOG_DBG("sensor worker started");

  while (true) {
    if (gState.imuOk && !--gState.imuSampleCounter) {
      err = imu_update();
      if (err) {
        LOG_WRN("%s failed: %d", "imu_update", err);
      }

      gState.imuSampleCounter = IMU_INTERVAL;
    }

    if (!--gState.enviroSampleCounter) {
      err = enviro_update(gDevEnviroInternal, 0);
      if (err) {
        LOG_WRN("%s failed: %d", "enviro_update(internal)", err);
      }

      if (gState.hasExternalEnviro) {
        err = enviro_update(gDevEnviroExternal, 1);
        if (err) {
          LOG_WRN("%s failed: %d", "enviro_update(external)", err);
        }
      }

      gState.enviroSampleCounter = ENVIRO_INTERVAL;
    }

    if (!--gState.thermSampleCounter) {
      err = thermistor_update();
      if (err) {
        LOG_WRN("%s failed: %d", "thermistor_update", err);
      }

      gState.thermSampleCounter = THERMISTOR_INTERVAL;
    }

    if (!--gState.pressSampleCounter) {
      err = pressure_update();
      if (err) {
        LOG_WRN("%s failed: %d", "pressure_update", err);
      }

      gState.pressSampleCounter = PRESS_INTERVAL;
    }

    if (!--gState.leakSampleCounter) {
      err = leak_update();
      if (err) {
        LOG_WRN("%s failed: %d", "leak_update", err);
      }

      gState.leakSampleCounter = LEAK_INTERVAL;
    }

    // delay until next reading time
    k_sleep(SENSOR_INTERVAL);
  }
}

/**
 * @brief Collect an IMU reading
 *
 * Reads the latest data from the IMU and stores it for later use.
 */
static int imu_update() {
  imu_bmi270_data_t data;

  // trigger reading and read it out
  HANDLE_UNLIKELY(imu_bmi270_trigger(gDevImu));
  imu_bmi270_get(gDevImu, &data);

  // calculate the inclination angles
  double theta = atan(data.accel.x / sqrt(pow(data.accel.y, 2) + pow(data.accel.z, 2)));
  double psi = atan(data.accel.y / sqrt(pow(data.accel.x, 2) + pow(data.accel.z, 2)));
  double phi = atan(sqrt(pow(data.accel.x, 2) + pow(data.accel.y, 2)) / data.accel.z);

  // store the angles
  HANDLE_UNLIKELY(k_mutex_lock(&gState.dataLock, K_FOREVER));
  {
    gState.imu.raw = data;
    gState.imu.inclination[0] = theta;
    gState.imu.inclination[1] = psi;
    gState.imu.inclination[2] = phi;
  }
  HANDLE_UNLIKELY(k_mutex_unlock(&gState.dataLock));

#if CONFIG_APP_SENSOR_DUMP_READINGS
  LOG_INF("%s: accel=[%d.%u, %d.%u, %d.%u] g", __FUNCTION__, (int)data.accel.x, (int)(fabs(data.accel.x) * 1000) % 1000,
          (int)data.accel.y, (int)(fabs(data.accel.y) * 1000) % 1000, (int)data.accel.z,
          (int)(fabs(data.accel.z) * 1000) % 1000);
  LOG_INF("%s: gyro=[%d, %d, %d] deg/s", __FUNCTION__, (int)data.gyro.x, (int)data.gyro.y, (int)data.gyro.z);

  // print inclination angles as degrees
  const double deg_theta = (theta) * (180.f / 3.1415926535);
  const double deg_psi = (psi) * (180.f / 3.1415926535);
  const double deg_phi = (phi) * (180.f / 3.1415926535);

  LOG_INF("%s: Angles = %3d.%03u, %3d.%03u, %3d.%03u", __FUNCTION__, (int)deg_theta,
          ((int)(fabs(deg_theta) * 1000)) % 1000, (int)deg_psi, ((int)(fabs(deg_psi) * 1000)) % 1000, (int)deg_phi,
          ((int)(fabs(deg_phi) * 1000)) % 1000);
#endif

  return 0;
}

/**
 * @brief Set up environmental sensors
 *
 * Ensure the sensors have been initialized. The internal sensor always exists; any failures in
 * setting up the external sensor are ignored and we'll just assume that sensor isn't connected
 * to the board.
 */
static int enviro_init() {
  // initialize internal sensor (must exist)
  HANDLE_UNLIKELY(!device_is_ready(gDevEnviroInternal));

  // handle external sensor
  gState.hasExternalEnviro = device_is_ready(gDevEnviroExternal);

  LOG_INF("external environmental sensor %s", gState.hasExternalEnviro ? "present" : "not present");
  return 0;
}

/**
 * @brief Read data from an environmental sensor
 *
 * Data is stored into the specified index in the data array, in order to support multiple sensors.
 *
 * At this time, only temperature, humidity and air pressure are read out. The gas sensor is not
 * exposed or stored.
 */
static int enviro_update(const struct device *dev, const size_t dataIndex) {
  struct net_ptp_time now;
  struct sensor_value temp, humidity, pressure, gas;

  HANDLE_UNLIKELY(ptp_slave_clk_get(&now));
  HANDLE_UNLIKELY(sensor_sample_fetch(dev));

  HANDLE_UNLIKELY(sensor_channel_get(dev, SENSOR_CHAN_AMBIENT_TEMP, &temp));
  HANDLE_UNLIKELY(sensor_channel_get(dev, SENSOR_CHAN_HUMIDITY, &humidity));
  HANDLE_UNLIKELY(sensor_channel_get(dev, SENSOR_CHAN_PRESS, &pressure));
  HANDLE_UNLIKELY(sensor_channel_get(dev, SENSOR_CHAN_GAS_RES, &gas));

  HANDLE_UNLIKELY(k_mutex_lock(&gState.dataLock, K_FOREVER));
  {
    gState.environment[dataIndex].timestamp = now;
    gState.environment[dataIndex].temp = sensor_value_to_float(&temp);
    gState.environment[dataIndex].humidity = sensor_value_to_float(&humidity);
    gState.environment[dataIndex].pressure = sensor_value_to_float(&pressure);

    // TODO: do something with the gas sensor resistance
  }
  HANDLE_UNLIKELY(k_mutex_unlock(&gState.dataLock));

#if CONFIG_APP_SENSOR_DUMP_READINGS
  LOG_INF("%s[%u]: temp=%d.%u C, humid=%d.%u %%RH, pressure = %d.%u Pa", __FUNCTION__, dataIndex, temp.val1,
          temp.val2 / 1000000, humidity.val1, humidity.val2 / 1000000, pressure.val1, pressure.val2 / 1000000);
#endif

  return 0;
}

/**
 * @brief Set up thermistor sensing
 *
 * Configure all ADC thermistor channels. These are spread between the external thermistor
 * connections as well as the thermistors for each pressure transducer.
 */
static int thermistor_init() {
  for (size_t i = 0; i < NUM_THERMISTORS; i++) {
    if (!gThermistor[i].dev) {
      continue;
    }

    HANDLE_UNLIKELY(!device_is_ready(gThermistor[i].dev));
    HANDLE_UNLIKELY(adc_channel_setup_dt(&gThermistor[i]));
  }

  return 0;
}

/**
 * @brief Read the thermistor values
 *
 * Read both external thermistor channels simultaneously.
 */
static int thermistor_update() {
  HANDLE_UNLIKELY(ptp_slave_clk_get(&gState.therm.timestamp));

  // TODO: proper values for this when thermistor choice is finalized
  static const thermistor_spec_t kExtThermistorSpec = {10000, 25, 10000, 4200};

  for (size_t i = 0; i < NUM_EXT_THERMISTORS; i++) {
    float temp;
    if (!gThermistor[i].dev) {
      continue;
    }

    HANDLE_UNLIKELY(thermistor_read(&gThermistor[i], &kExtThermistorSpec, &temp));

    HANDLE_UNLIKELY(k_mutex_lock(&gState.dataLock, K_FOREVER));
    { gState.therm.temps[i] = temp; }
    HANDLE_UNLIKELY(k_mutex_unlock(&gState.dataLock));
  }

#if CONFIG_APP_SENSOR_DUMP_READINGS
  LOG_INF("%s: therm=[%u, %u]", __FUNCTION__, (int)gState.therm.temps[0], (int)gState.therm.temps[1]);
#endif

  return 0;
}

/**
 * @brief Sample a single thermistor channel
 *
 * @param convspec Parameters defining how thermistor voltage shall be converted to temperature
 * @param outTemps Variable to receive resulting temperature, in °C
 */
static int thermistor_read(const struct adc_dt_spec *channel, const thermistor_spec_t *convspec, float *outTemp) {
  if (!channel || !convspec || !outTemp) {
    return -EFAULT;
  }

  uint16_t rawValue;
  struct adc_sequence seq = {
      .buffer = &rawValue,
      .buffer_size = sizeof(rawValue),
      .resolution = channel->resolution,
      .channels = BIT(channel->channel_id),
  };

  HANDLE_UNLIKELY(adc_read(channel->dev, &seq));

  // convert to temperature
  float mv = adc_raw_to_millivolts_dt_float(channel, rawValue);
  const double ohms = get_divider_ohms(convspec->r1, mv, 3300);
  *outTemp = get_thermistor_temp(convspec->refTemp, convspec->refResistance, convspec->beta, ohms);

#if CONFIG_APP_SENSOR_DUMP_READINGS
  LOG_DBG("mv=%u, ohms=%d, temp=%d ref = %u", (int)mv, (int)ohms, (int)*outTemp, adc_ref_internal(channel->dev));
#endif

  return 0;
}

/**
 * @brief Initialize leak sensor IOs
 *
 * The pins are configured as pull up; they're shorted to ground if a leak is detected.
 *
 * @remark Due to IO conflicts these are read via polling rather than by interrupts as EXTI is
 *         used for scanner fuse detection
 */
static int leak_init() {
  for (size_t i = 0; i < NUM_LEAK_SENSORS; i++) {
    // configure IOs
    HANDLE_UNLIKELY(!device_is_ready(gLeak[i].port));
    HANDLE_UNLIKELY(gpio_pin_configure_dt(&gLeak[i], GPIO_INPUT));
  }

  return 0;
}

/**
 * @brief Read out the current leak sensor state
 *
 * Read the GPIO pins for the leak sensors and record their state.
 */
static int leak_update() {
  struct net_ptp_time now;
  int state;

  HANDLE_UNLIKELY(ptp_slave_clk_get(&now));

  HANDLE_UNLIKELY(k_mutex_lock(&gState.dataLock, K_FOREVER));
  {
    for (size_t i = 0; i < NUM_LEAK_SENSORS; i++) {
      if ((state = gpio_pin_get_dt(&gLeak[i])) >= 0) {
        if (!gState.leak[i].timestamp.second || gState.leak[i].state != !!state) {
          gState.leak[i].timestamp = now;
          gState.leak[i].state = !!state;

#if CONFIG_APP_SENSOR_DUMP_READINGS
          LOG_INF("%s: leak %u = %d", __FUNCTION__, i, gState.leak[i].state);
#endif
        }
      }
    }
  }
  HANDLE_UNLIKELY(k_mutex_unlock(&gState.dataLock));

  return 0;
}

/**
 * @brief Initialize pressure transducers
 *
 * Set up the ADC channels the pressure transducers are connected to.
 */
static int pressure_init() {
  for (size_t i = 0; i < NUM_PRESS_SENSORS; i++) {
    if (!gPressure[i].dev) {
      continue;
    }

    HANDLE_UNLIKELY(!device_is_ready(gPressure[i].dev));
    HANDLE_UNLIKELY(adc_channel_setup_dt(&gPressure[i]));
  }

  return 0;
}

/**
 * @brief Read pressure transducers
 *
 * Sample the pressure and temperature channel of each of the coolant pressure transducers. Both
 * readings are converted from ADC voltage to the appropriate unit.
 */
static int pressure_update() {
  /*
   * β value derived by curve fitting to table in datsheet: specifically with the 10°C, 25°C and
   * 40°C points to maximize accuracy in the range of temperatures expected for chiller fluid
   * lines.
   *
   * This gives roughly ±0.2°C across the entire range of 0°C through 50°C compared to the table
   * in the datasheet which should be sufficient.
   */
  static const thermistor_spec_t kPressThermistorSpec = {2400, 25, 3000, 3872.88};

  for (size_t i = 0; i < NUM_PRESS_SENSORS; i++) {
    float press = 0, temp = NAN;
    struct net_ptp_time now;

    if (!gPressure[i].dev) {
      continue;
    }

    HANDLE_UNLIKELY(ptp_slave_clk_get(&now));
    HANDLE_UNLIKELY(pressure_read(&gPressure[i], &press));

    if (gThermistor[THERM_OFFSET_PRESSURE + i].dev) {
      HANDLE_UNLIKELY(thermistor_read(&gThermistor[THERM_OFFSET_PRESSURE + i], &kPressThermistorSpec, &temp));
    }
#if CONFIG_APP_SENSOR_DUMP_READINGS
    LOG_INF("%s: pressure %u = temp=%d, pressure=%d", __FUNCTION__, i, (int)temp, (int)press);
#endif

    HANDLE_UNLIKELY(k_mutex_lock(&gState.dataLock, K_FOREVER));
    {
      gState.pressure[i].timestamp = now;
      gState.pressure[i].pressure = press;
      gState.pressure[i].temperature = temp;
    }
    HANDLE_UNLIKELY(k_mutex_unlock(&gState.dataLock));
  }

  return 0;
}

/**
 * @brief Sample and convert a single pressure transducer channel
 *
 * The conversion is based on the assumption of using Amphenol GE-2096 sensors with the following
 * characteristics:
 *
 * - Analog output, 0-5V
 * - Transfer function: Vo = ((P * 0.00168) - 0.00040) x Vs
 *
 * This assumes the GE-2096 sensor and its associated pressure transfer curve is connected. We also
 * assume the 10k/22k resistor divider is connected prior to the ADC input to scale the 0-5V analog
 * voltage. The sensor also has clipping limits (from 0-0.08Vs and 0.92-1.0Vs) outside of which the
 * conversion is clipped, and diagnostic limits outside of which it will never output a value.
 *
 * Resulting pressure is in kPa.
 */
static int pressure_read(const struct adc_dt_spec *channel, float *outPressure) {
  // Supply/reference voltage for pressure transducers, in volts
  static const float kPressureVref = 5.f;

  uint16_t rawValue;
  struct adc_sequence seq = {
      .buffer = &rawValue,
      .buffer_size = sizeof(rawValue),
      .resolution = channel->resolution,
      .channels = BIT(channel->channel_id),
  };

  HANDLE_UNLIKELY(adc_read(channel->dev, &seq));

  // convert to an actual pressure
  const float mv = adc_raw_to_millivolts_dt_float(channel, rawValue);
  const float inVolts = get_divider_voltage(10000, 22000, mv);

  // ensure reading is inside the diagnostic bounds
  if (inVolts >= (kPressureVref * 0.05) && inVolts <= (kPressureVref * 0.95)) {
    *outPressure = ((inVolts / kPressureVref) + 0.0004) / 0.00168f;
  } else {
    *outPressure = NAN;
  }

  return 0;
}

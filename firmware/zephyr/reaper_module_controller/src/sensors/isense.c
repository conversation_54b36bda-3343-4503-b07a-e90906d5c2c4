#include <logging/log.h>
LOG_MODULE_REGISTER(isense, CONFIG_APP_LOG_LEVEL);

#include <device.h>
#include <devicetree.h>
#include <drivers/adc.h>
#include <lib/ptp/ptp.h>
#include <net/ptp_time.h>
#include <string.h>

#include <arm_math.h>
#include <arm_math_f16.h>
#include <utils/adc.h>
#include <utils/handle_errors.h>

#include "isense.h"

// for resistor divider
#define RDIV_FACTOR(r1, r2) (((r1 + r2) / r2) / 1000.)

// Number of channels to continuously sample
// TODO: requires zephyr ADC fixes
#define ADC_NUM_CONT_CHANNELS (1)

// Number of ADC channels (for current) to reserve space for
#define ADC_NUM_CURRENT_CHANNELS (3)
// Number of ADC samples to average for current readings
#define ADC_NUM_CURRENT_SAMPLES (2048)
// Total size (in samples) of conversion buffer
#define ADC_CURRENT_BUF_LEN (ADC_NUM_CURRENT_CHANNELS * ADC_NUM_CURRENT_SAMPLES)

// Enable ADC sample rate estimator
#define ADC_SAMPLE_RATE_ESTIMATOR (0)
// Enable buffer of ADC samples (saves ~2 sec of data)
#define ADC_TEST_BUF (0)

typedef struct isense_channel_info {
  struct adc_dt_spec current;
  // Scale factor from ADC mV -> sensed current (A)
  double currentScale;

  // Voltage sense channel (if any)
  struct adc_dt_spec voltage;
  // Scale factor from ADC mV -> input volts
  double voltageScale;
} isense_channel_info_t;

typedef struct isense_state {
  // raw samples from ADC
  struct {
    // signalled when ADC conversions have completed
    struct k_poll_signal convComplete;

    // Raw ADC buffer
    // TODO: use double buffering
    union {
      uint16_t buf[2][ADC_CURRENT_BUF_LEN];
      float16_t floatBuf[2][ADC_CURRENT_BUF_LEN];
    };
    // Active buffer (for writing)
    size_t activeBuf;

    // Timestamp at which we started sampling
    struct net_ptp_time start;
    // Timestamp at which sampling ended and we received the signal event
    struct net_ptp_time end;

#if ADC_SAMPLE_RATE_ESTIMATOR
    struct {
      struct net_ptp_time start;
      struct net_ptp_time end;
      size_t samples;
    } estimator;
#endif
  } adc;

  // TODO: scratch buffers (for filtering, etc.)

  // converted data
  struct {
    struct k_mutex lock;

    float amps[ADC_NUM_CURRENT_CHANNELS];
    float volts[ADC_NUM_CURRENT_CHANNELS];
  } data;
} isense_state_t;

static void isense_worker();

static int do_sample_volts();
static int trigger_sample_amps();
static int trigger_sample_amps_discontinuous();
static int convert_isense_adc();

#if ADC_TEST_BUF
// how many samples to store in the buffer
#define TEST_BUF_LEN 98304

// ADC samples (in mV) are stored in here
uint16_t gBuffer[TEST_BUF_LEN];
// current write offset
size_t gBufferOffset = 0;

#warning ADC debug buffer enabled - this will waste tons of RAM!
#endif

// Configured ADC channels
static const isense_channel_info_t gInputChannels[ADC_NUM_CURRENT_CHANNELS] = {
    // Strobe board
    {
        ADC_DT_SPEC_GET_BY_NAME(DT_PATH(strobe_board), amps),
        RDIV_FACTOR(10000, 3320),
        ADC_DT_SPEC_GET_BY_NAME(DT_PATH(strobe_board), volts),
        RDIV_FACTOR(100000, 3400),
    },
    // Scanner A current feedback
    {
        ADC_DT_SPEC_GET_BY_NAME_OR_NULL(DT_PATH(scanners, a), amps),
        // V = 200 * I * 0.005
        0.001,
        // no voltage sense
        {NULL},
    },
    // Scanner B current feedback
    {
        ADC_DT_SPEC_GET_BY_NAME_OR_NULL(DT_PATH(scanners, b), amps),
        // V = 200 * I * 0.005
        0.001,
        // no voltage sense
        {NULL},
    },
};

// Current sense handler's global state
__dtcm_bss_section static isense_state_t gState;

// worker task (handles conversions, filtering, etc.)
K_THREAD_DEFINE(gIsenseWorker, 1536, isense_worker, NULL, NULL, NULL, K_PRIO_PREEMPT(10), 0, K_TICKS_FOREVER);

/**
 * @brief Initialize current sense handler
 */
int isense_init() {
  k_mutex_init(&gState.data.lock);

  // set up ADCs
  for (size_t i = 0; i < ADC_NUM_CURRENT_CHANNELS; i++) {
    const isense_channel_info_t *ch = &gInputChannels[i];

    if (ch->current.dev) {
      HANDLE_UNLIKELY_BOOL(device_is_ready(ch->current.dev), ENXIO);
      HANDLE_UNLIKELY(adc_channel_setup_dt(&ch->current));
    } else {
      LOG_WRN("no ISense ADC configured for ch %u", i);
    }

    if (ch->voltage.dev) {
      HANDLE_UNLIKELY_BOOL(device_is_ready(ch->voltage.dev), ENXIO);
      HANDLE_UNLIKELY(adc_channel_setup_dt(&ch->voltage));
    } else {
      LOG_DBG("no VSense ADC configured for ch %u", i);

      gState.data.volts[i] = NAN;
    }

    // TODO: ensure all current channels use same ADC
  }

  // TODO: do more setup for filters

  // set up resources for worker task and start it
  k_poll_signal_init(&gState.adc.convComplete);

  k_thread_name_set(gIsenseWorker, "ISense");
  k_thread_start(gIsenseWorker);

  return 0;
}

/**
 * @brief Main loop for the current sense handler
 *
 * This services the ADC complete event, in response to which the ADC readings are processed into
 * current data.
 */
static void isense_worker() {
  enum event_idx {
    kEventAdcConversionDone = 0,
  };

  int err;
  struct k_poll_event events[1] = {
      K_POLL_EVENT_INITIALIZER(K_POLL_TYPE_SIGNAL, K_POLL_MODE_NOTIFY_ONLY, &gState.adc.convComplete),
  };

  // trigger initial conversions
  HANDLE_CRITICAL(do_sample_volts());
  HANDLE_CRITICAL(trigger_sample_amps());

  // perpetually handle events
  do {
    err = k_poll(events, (sizeof(events) / sizeof(struct k_poll_event)), K_FOREVER);
    if (err) {
      LOG_WRN("%s failed: %d", "k_poll", err);
      continue;
    }

    // ADC batch readings complete
    if (events[kEventAdcConversionDone].signal->signaled) {
      // capture voltage and any discontinuous current conversions
      err = do_sample_volts();
      if (err) {
        LOG_WRN("%s failed: %d", "do_sample_volts", err);
        break;
      }

#if ADC_NUM_CONT_CHANNELS != ADC_NUM_CURRENT_CHANNELS
      err = trigger_sample_amps_discontinuous();
      if (err) {
        LOG_WRN("%s failed: %d", "trigger_sample_amps_discontinuous", err);
        break;
      }
#endif

      // trigger new batch of current conversions (continuous)
      err = trigger_sample_amps();
      if (err) {
        LOG_WRN("%s failed: %d", "trigger_sample_amps", err);
        break;
      }

      // estimate ADC sample rate, if desired
#if ADC_SAMPLE_RATE_ESTIMATOR
      gState.adc.estimator.samples += ADC_NUM_CURRENT_SAMPLES;

      struct net_ptp_time now;
      ptp_slave_clk_get(&now);

      const uint32_t nsec = ptp_ts_net_to_ns(&now) - ptp_ts_net_to_ns(&gState.adc.estimator.start);
      if (nsec >= NSEC_PER_SEC) {
        // usec per sample
        const double perSample = ((double)NSEC_PER_SEC) / ((double)gState.adc.estimator.samples);
        // samples per second
        const double rate = ((double)NSEC_PER_SEC) / perSample;

        gState.adc.estimator.samples = 0;
        LOG_DBG("sample rate = %u Hz", (int)rate);
      }
#endif

      // convert existing data (from "old" buffer)
      err = convert_isense_adc();
      if (err) {
        LOG_WRN("%s failed: %d", "convert_isense_adc", err);
      }

      k_poll_signal_reset(events[kEventAdcConversionDone].signal);
      events[kEventAdcConversionDone].state = K_POLL_STATE_NOT_READY;
    }
  } while (true);
}

/**
 * @brief Take a voltage reading
 *
 * Attempt to acquire a voltage reading from each channel with a VSense line.
 */
static int do_sample_volts() {
  for (size_t i = 0; i < ADC_NUM_CURRENT_CHANNELS; i++) {
    const isense_channel_info_t *ch = &gInputChannels[i];
    float volts = NAN;

    // some channels may not have VSense
    if (ch->voltage.dev) {
      uint16_t rawValue;

      struct adc_sequence seq = {
          .buffer = &rawValue,
          .buffer_size = sizeof(rawValue),
          .resolution = ch->voltage.resolution,
          .channels = BIT(ch->voltage.channel_id),
      };

      HANDLE_UNLIKELY(adc_read(ch->voltage.dev, &seq));

      float mv = adc_raw_to_millivolts_dt_float(&ch->voltage, rawValue);
      volts = mv * ch->voltageScale;

      HANDLE_UNLIKELY(k_mutex_lock(&gState.data.lock, K_FOREVER));
      { gState.data.volts[i] = volts; }
      k_mutex_unlock(&gState.data.lock);
    }
  }

  return 0;
}

/**
 * @brief Trigger current sense (discontinuous channels)
 *
 * Perform a one shot current reading for all non-continuous channels.
 */
static int trigger_sample_amps_discontinuous() {
  for (size_t i = ADC_NUM_CONT_CHANNELS; i < ADC_NUM_CURRENT_CHANNELS; i++) {
    const isense_channel_info_t *ch = &gInputChannels[i];
    float amps = NAN;

    if (ch->current.dev) {
      uint16_t rawValue;

      struct adc_sequence seq = {
          .buffer = &rawValue,
          .buffer_size = sizeof(rawValue),
          .resolution = ch->current.resolution,
          .channels = BIT(ch->current.channel_id),
      };

      HANDLE_UNLIKELY(adc_read(ch->current.dev, &seq));

      float mv = adc_raw_to_millivolts_dt_float(&ch->current, rawValue);
      amps = mv * ch->currentScale;

      HANDLE_UNLIKELY(k_mutex_lock(&gState.data.lock, K_FOREVER));
      { gState.data.amps[i] = amps; }
      k_mutex_unlock(&gState.data.lock);
    }
  }

  return 0;
}

/**
 * @brief Trigger current sense
 *
 * Start a current sense reading for all configured channels.
 */
static int trigger_sample_amps() {
  static const struct adc_sequence_options gSeqOptions = {
      .extra_samplings = (ADC_NUM_CURRENT_SAMPLES - 1),
  };

  struct adc_sequence seq = {
      .options = &gSeqOptions,
      .buffer = gState.adc.buf[gState.adc.activeBuf],
      .buffer_size = sizeof(gState.adc.buf[0]),
      .resolution = gInputChannels[0].current.resolution,
      .channels = BIT(gInputChannels[0].current.channel_id),
  };

#if ADC_NUM_CONT_CHANNELS > 1
  for (size_t i = 0; i < ADC_NUM_CURRENT_CHANNELS; i++) {
    const isense_channel_info_t *ch = &gInputChannels[i];

    if (ch->current.dev) {
      seq.channels |= BIT(ch->current.channel_id);
    }
  }
#endif

  HANDLE_UNLIKELY(ptp_slave_clk_get(&gState.adc.start));
  HANDLE_UNLIKELY(adc_read_async(gInputChannels[0].current.dev, &seq, &gState.adc.convComplete));

#if ADC_SAMPLE_RATE_ESTIMATOR
  if (!gState.adc.estimator.samples) {
    HANDLE_UNLIKELY(ptp_slave_clk_get(&gState.adc.estimator.start));
  }
#endif

  // flip buffers
  gState.adc.activeBuf = (gState.adc.activeBuf + 1) % 2;

  return 0;
}

/**
 * @brief Filter sampled current data and convert
 */
static int convert_isense_adc() {
  const size_t bufIdx = (gState.adc.activeBuf == 1) ? 0 : 1;

  const uint16_t *rawBuf = gState.adc.buf[bufIdx];
  float16_t *floatBuf = gState.adc.floatBuf[bufIdx];

  // capture the end-of-conversion timestamp
  HANDLE_UNLIKELY(ptp_slave_clk_get(&gState.adc.end));

  // convert the ADC readings -> floating point voltage values in place
  for (size_t i = 0; i < (ADC_NUM_CONT_CHANNELS * ADC_NUM_CURRENT_SAMPLES); i++) {
#if ADC_NUM_CONT_CHANNELS > 1
    const isense_channel_info_t *ch = &gInputChannels[i % ADC_NUM_CONT_CHANNELS];
    // const isense_channel_info_t *ch = &gInputChannels[i % ADC_NUM_CURRENT_CHANNELS];
#else
    const isense_channel_info_t *ch = &gInputChannels[0];
#endif

    const uint16_t rawValue = rawBuf[i];
    float mv = adc_raw_to_millivolts_dt_float(&ch->current, rawValue);
    floatBuf[i] = mv;

#if ADC_TEST_BUF
    gBuffer[gBufferOffset++] = mv;
    if (gBufferOffset == TEST_BUF_LEN) {
      gBufferOffset = 0;
      LOG_DBG("ADC test buffer filled");
    }
#endif
  }

  // TODO: update the below for handling multiple channels

  // TODO: do more fun and exciting filtering

  // get min, max, mean over the previously captured data
  double sum = 0;
  float16_t min = F16_MAX, max = 0, mean = 0;
  uint32_t minIdx, maxIdx;

  for (size_t i = 0; i < ADC_NUM_CURRENT_SAMPLES; i++) {
    sum += floatBuf[i];
  }
  mean = sum / ((double)ADC_NUM_CURRENT_SAMPLES);

  arm_min_f16(floatBuf, ADC_NUM_CURRENT_SAMPLES, &min, &minIdx);
  arm_max_f16(floatBuf, ADC_NUM_CURRENT_SAMPLES, &max, &maxIdx);

  // calculate current
  const double Imin = get_divider_voltage(10000, 3320, min), Imax = get_divider_voltage(10000, 3320, max),
               Imean = get_divider_voltage(10000, 3320, mean);

  HANDLE_CRITICAL(k_mutex_lock(&gState.data.lock, K_FOREVER));
  { gState.data.amps[0] = Imean; }
  k_mutex_unlock(&gState.data.lock);

#if 0
    LOG_INF("Min = %u.%04u, max = %u.%04u, avg = %u.%04u",
            (int) Imin, ((int) (Imin * 10000.f)) % 10000,
            (int) Imax, ((int) (Imax * 10000.f)) % 10000,
            (int) Imean, ((int) (Imean * 10000.f)) % 10000
            );
#endif

  return 0;
}

/**
 * @brief Get converted voltage and current
 */
int isense_get(const isense_channel_t ch, int64_t *outTimestamp, float *outCurrent, float *outVoltage) {
  if (ch >= ADC_NUM_CURRENT_CHANNELS) {
    return -EINVAL;
  } else if (!outTimestamp && !outCurrent && !outVoltage) {
    return -EINVAL;
  }

  HANDLE_CRITICAL(k_mutex_lock(&gState.data.lock, K_FOREVER));
  {
    // TODO: fetch timestamp also

    if (outCurrent) {
      *outCurrent = gState.data.amps[ch];
    }
    if (outVoltage) {
      *outVoltage = gState.data.volts[ch];
    }
  }
  k_mutex_unlock(&gState.data.lock);

  return 0;
}

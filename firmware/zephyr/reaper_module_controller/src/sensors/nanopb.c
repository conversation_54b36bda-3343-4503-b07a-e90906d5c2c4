#include <zephyr/logging/log.h>
LOG_MODULE_DECLARE(sensors);

#include <utils/handle_errors.h>

#include "sensors/nanopb.h"
#include "sensors/sensors.h"

/**
 * @brief Fill the current sensor values into nanopb message
 */
int sensors_nanopb_fill(reaper_module_controller_SensorReply *reply) {
  int err;

  // get environmental sensor data: at least the first should have data
  reply->env_count = 0;

  for (size_t i = 0; i < 2; i++) {
    err = sensors_enviro_get(i, &reply->env[i].timestamp, &reply->env[i].temp, &reply->env[i].humidity,
                             &reply->env[i].pressure);

    if (!i && err) {
      LOG_WRN("%s(%u) failed: %d", "sensors_enviro_get", i, err);
      return err;
    } else if (!err) {
      reply->env_count++;
    }
  }

  // next, retrieve the IMU data
  err = sensors_imu_get_raw(&reply->imu.timestamp, reply->imu.accel, 3, reply->imu.gyro, 3);
  if (err) {
    LOG_WRN("%s failed: %d", "sensors_imu_get_raw", err);
    return err;
  }

  reply->has_imu = true;
  reply->imu.accel_count = reply->imu.gyro_count = 3;

  // thermistor readings
  reply->therm_count = 0;

  for (size_t i = 0; i < 2; i++) {
    err = sensors_thermistor_get(i, &reply->therm[i].timestamp, &reply->therm[i].temp);
    if (err) {
      LOG_WRN("%s(%u) failed: %d", "sensors_thermistor_get", i, err);
      return err;
    }

    reply->therm_count++;
  }

  // leak sensors
  reply->leak_count = 0;

  for (size_t i = 0; i < 2; i++) {
    err = sensors_leak_get(i, &reply->leak[i].timestamp, &reply->leak[i].active);
    if (err) {
      LOG_WRN("%s(%u) failed: %d", "sensors_leak_get", i, err);
      return err;
    }

    reply->leak_count++;
  }

  // pressure transducers
  reply->press_count = 0;

  for (size_t i = 0; i < 2; i++) {
    err = sensors_pressure_get(i, &reply->press[i].timestamp, &reply->press[i].pressure, &reply->press[i].temperature);
    if (err) {
      LOG_WRN("%s(%u) failed: %d", "sensors_pressure_get", i, err);
      return err;
    }

    reply->press_count++;
  }

  // whether external environmental sensor was detected at boot
  err = sensors_enviro_is_present(kEnviroExternalPc, &reply->hasExternalEnv);
  if (err) {
    LOG_WRN("%s failed: %d", "sensors_enviro_is_present", err);
    return err;
  }

  return 0;
}

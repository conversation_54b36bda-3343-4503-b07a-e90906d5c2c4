/**
 * @file
 * @brief Current/power measurement helper
 *
 * <PERSON>les measuring current through the strobes as well as for the scanners. The underlying ADC
 * is used in continuous sampling mode, and filtering is applied to the resultant data.
 */
#pragma once

#include <stdint.h>

typedef enum isense_channel {
  kIsenseStrobes = 0,
  kIsenseScannerA = 1,
  kIsenseScannerB = 2,
} isense_channel_t;

int isense_init();

int isense_get(const isense_channel_t ch, int64_t *outTimestamp, float *outCurrent, float *outVoltage);

/**
 * @file
 *
 * @brief Sensor manager
 *
 * This guy is responsible for all of the sensors – humidity/temperature, on-board IMU, external
 * pressure and thermistors, as well as the leak sensors – and will periodically read them out and
 * store the results for later retrieval.
 *
 * All timestamps are output as milliseconds, in terms of the PTP clock epoch.
 */
#pragma once

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

typedef enum enviro_sensor {
  // On-board sensor
  kEnviroInternal = 0,
  // Remote sensor in PC enclosure
  kEnviroExternalPc = 1,
} enviro_sensor_t;

int sensors_init();

int sensors_enviro_get(const enviro_sensor_t which, int64_t *outTimestamp, float *outTemp, float *outHumidity,
                       float *outPressure);
int sensors_enviro_is_present(const enviro_sensor_t which, bool *outIsPresent);
int sensors_thermistor_get(const size_t which, int64_t *outTimestamp, float *outTemp);
int sensors_imu_get_raw(int64_t *outTimestamp, float *outAccel, const size_t outAccelSize, float *outGyro,
                        const size_t outGyroSize);
int sensors_imu_get_inclination(int64_t *outTimestamp, double *outAngles, const size_t numOutAngles);
int sensors_leak_get(const size_t which, int64_t *outTimestamp, bool *outState);
int sensors_pressure_get(const size_t which, int64_t *outTimestamp, float *outPressure, float *outTemp);

/**
 * @file
 * @brief Fan controller
 *
 * Manual and automatic (thermostatic) control over 24V circulating fans.
 */
#pragma once

#include <stdbool.h>

typedef enum fan {
  kFan1 = 0,
  kFan2 = 1,
} fan_t;

/// Possible sensor sources for fan thermostat control
typedef enum fan_thermo_source {
  kThermoSourceEnviroInternal = 0,
  kThermoSourceEnviroExternal = 1,
} fan_thermo_source_t;

/// Configuration options for temperature controlled fans
typedef struct fan_thermo_config {
  /// Set point temperature
  float setpoint;
  /// Amount of hysteresis around this setpoint (setpoint±hysteresis = when state changes)
  float hysteresis;

  /// Which sensor should be used for the control?
  fan_thermo_source_t tempSource;
} fan_thermo_config_t;

int fans_init();
int fans_set_manual(const fan_t fan, const bool state);
int fans_get_state(const fan_t fan, bool *outState);

int fans_get_thermo_state(bool *outIsEnabled, fan_thermo_config_t *outCurrentConfig, float *outCurrentTemp);
int fans_set_thermo_enabled(const bool isEnabled);
int fans_set_thermo_config(const fan_thermo_config_t *newConfig);

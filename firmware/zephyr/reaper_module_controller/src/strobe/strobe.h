/**
 * @file
 * @brief Strobe board handler
 *
 * The strobe manager handles generating triggering waveforms (for the BTLs as well as all cameras
 * in the system) as well as supervision of the feedback signals (current/voltage/temp) from the
 * strobe board.
 *
 * \section{Analog Sense Handling}
 *
 * Strobe board reports several analog values back to the MCB – namely capacitor bank voltage,
 * current through the capacitors, and an optional thermistor to provide the temperature of the
 * board.
 *
 * The thermistor (if present) is always read at a fixed periodic rate, regardless of the state
 * of the strobe outputs.
 *
 * Current is always read continuously, then filtered and averaged to produce a single current
 * reading. The capacitor voltage is likewise sampled and filtered.
 */
#pragma once

#include <stdbool.h>
#include <stdint.h>

#include <zephyr/kernel.h>

/**
 * @brief Bitset of output channels from strobe manager
 *
 * When generating strobe pulses, a mask is applied that can gate the pulses for any combination
 * of the predict and target cams as well as the BTLs.
 */
typedef enum strobe_channel_mask {
  kStrobeMaskPredict = (1 << 0),
  kStrobeMaskTarget = (1 << 1),
  kStrobeMaskBtl = (1 << 2),

  kStrobeMaskAll = (kStrobeMaskPredict | kStrobeMaskTarget | kStrobeMaskBtl),
} strobe_channel_mask_t;

/**
 * @brief Strobe output configuration
 *
 * Holds all parameters that define how the strobes are triggered.
 */
typedef struct strobe_config {
  // Exposure duration (µS)
  uint32_t exposure;
  // Total strobe period (µS)
  uint32_t period;

  // Ratio of target exposures to predict exposures
  uint32_t target_exposure_ratio;
} strobe_config_t;

/**
 * @brief Current strobe status
 */
typedef struct strobe_status {
  struct {
    // Capacitor bank voltage
    struct {
      int64_t timestamp;
      float value;
    } voltage;
    // Capacitor current, amps
    struct {
      int64_t timestamp;
      float value;
    } current;
    // Board temperature (via thermistor)
    struct {
      int64_t timestamp;
      float value;
    } temperature;
  } sense;

  // Is strobe board ready to fire?
  bool ready;
  // Is firing enabled and commanded?
  bool enabled;
  // Whether we're actually firing strobes/triggering cameras
  bool firing;

  // Current strobe config (if configured)
  strobe_config_t currentConfig;
} strobe_status_t;

int strobe_init();

int strobe_set_enabled(const bool isEnabled);
int strobe_get_enabled(bool *outIsEnabled);
int strobe_set_config(const strobe_config_t *newConfig);
int strobe_get_config(strobe_config_t *outConfig);
bool strobe_has_config();
int strobe_get_status(strobe_status_t *outStatus);
int strobe_set_mask(const strobe_channel_mask_t newMask);

int strobe_timed_disable(k_timeout_t duration);

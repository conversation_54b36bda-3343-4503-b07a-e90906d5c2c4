#include <zephyr/logging/log.h>
LOG_MODULE_DECLARE(strobe, CONFIG_APP_LOG_LEVEL);

#include <zephyr/device.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/adc.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/sys/byteorder.h>

#include <lib/ptp/ptp.h>
#include <utils/adc.h>
#include <utils/handle_errors.h>

#include "drivers/eeprom.h"
#include "strobe/internal.h"
#include "strobe/strobe.h"

// Time period to sample the discrete analog signals (thermistor)
#define ADC_SENSE_INTERVAL K_MSEC(666)

// Time period to sample trigger utilization
#define TRIGGER_SAMPLE_INTERVAL K_MSEC(1)
// Time period to report trigger utilization
#define TRIGGER_REPORT_INTERVAL K_SECONDS(15)

/**
 * @brief Time interval for debouncing ready signal
 */
#define READY_DEBOUNCE_INTERVAL K_MSEC(5)

/**
 * @brief Ready rising edge delay
 *
 * Time interval after the rising edge of the strobe ready signal is asserted before strobing will
 * actually commence
 */
#define READY_RISING_EDGE_DELAY K_SECONDS(5)

/**
 * @brief Cooldown for excess trigger utilization
 *
 * If the trigger pin gets stuck high, force BTLs off for this amount of time.
 */
#define EXCESS_TRIGGER_COOLDOWN K_SECONDS(20)

/**
 * @brief Strobe config save delay
 *
 * Time interval to wait after the strobe config is changed before writing to EEPROM.
 */
#define CONFIG_WRITE_DELAY K_SECONDS(30)

/**
 * @brief Default strobe configuration
 *
 * This is applied at startup if there is no previous strobe config in EEPROM.
 *
 * This sets up for a ~2% duty cycle with a 60Hz frequency.
 */
static const strobe_config_t kDefaultStrobeConfig = {
    .exposure = 400,
    .period = (USEC_PER_SEC / 60),
    .target_exposure_ratio = 6,
};

static void strobe_worker();

static void ready_state_changed();
static void ready_rising_edge_debounce_timer_expired(struct k_timer *);
static void handle_ready_rising_edge_debounce();
static void ready_debounce_work(struct k_work *);
static void ready_gpio_callback(const struct device *, struct gpio_callback *, gpio_port_pins_t);

static void sense_tick(struct k_timer *);
static int do_sample_thermistor();

static void disable_tick(struct k_timer *);

static void trigger_sample_tick(struct k_timer *);
static void trigger_report_tick(struct k_timer *);
static void reset_trigger_utilization();
static void handle_trigger_report();

static int load_strobe_config();
static void config_write_tick(struct k_timer *);
static int read_eeprom_config(strobe_config_t *outConfig);
static int write_eeprom_config();
static bool validate_eeprom_block(const eeprom_strobe_config_t *);

// worker thread that handles strobe-related tasks
K_THREAD_DEFINE(gStrobeWorkerThread, 1536, strobe_worker, NULL, NULL, NULL, K_PRIO_PREEMPT(9), 0, K_TICKS_FOREVER);
// Timer that is triggered to periodically sample strobe board readings
static K_TIMER_DEFINE(gSenseTimer, sense_tick, NULL);
// Timer used for the expiration of the "timed disable" duration
static K_TIMER_DEFINE(gDisableTimer, disable_tick, NULL);
// Handles timeout for writing config to EEPROM
static K_TIMER_DEFINE(gConfigWriteTimer, config_write_tick, NULL);
// Debouncing of rising edge of strobe ready signal
static K_TIMER_DEFINE(gStrobeReadyEnableTimer, ready_rising_edge_debounce_timer_expired, NULL);
// Timer for sampling trigger utilization
static K_TIMER_DEFINE(gTriggerSampleTimer, trigger_sample_tick, NULL);
// Timer for reporting trigger utilization
static K_TIMER_DEFINE(gTriggerReportTimer, trigger_report_tick, NULL);

// GPIO change callback for ready pin
__dtcm_bss_section static struct gpio_callback gReadyGpioCallback;
// Delayable work item to process the "ready" line change
K_WORK_DELAYABLE_DEFINE(gReadyDebounceWork, ready_debounce_work);

// ADC channel for thermistor on strobe board (temperature feedback)
static const struct adc_dt_spec gAdcThermistor =
    ADC_DT_SPEC_GET_BY_NAME_OR_NULL(DT_PATH(strobe_board, thermistor), temp);
// Strobe ready to trigger input (indicates caps fully charged)
static const struct gpio_dt_spec gReadyGpio = GPIO_DT_SPEC_GET(DT_PATH(strobe_board), ready_gpios);

// Trigger utilization tracking
static struct {
  uint32_t active_samples; // Number of samples where trigger was active
  uint32_t total_samples;  // Total number of samples taken
} gTriggerUtil = {0};

/**
 * @brief Initialize the strobe worker task
 */
int strobe_worker_init() {
  k_sem_init(&gStrobeState.initSem, 0, 1);

  // set up GPIO + irq for ready flag
  HANDLE_UNLIKELY_BOOL(device_is_ready(gReadyGpio.port), ENXIO);
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&gReadyGpio, GPIO_INPUT));

  HANDLE_UNLIKELY(gpio_pin_interrupt_configure_dt(&gReadyGpio, GPIO_INT_EDGE_BOTH));
  gpio_init_callback(&gReadyGpioCallback, ready_gpio_callback, BIT(gReadyGpio.pin));
  gpio_add_callback(gReadyGpio.port, &gReadyGpioCallback);

  // configure ADCs for temperature feedback
  if (gAdcThermistor.dev) {
    HANDLE_UNLIKELY_BOOL(device_is_ready(gAdcThermistor.dev), ENXIO);
    HANDLE_UNLIKELY(adc_channel_setup_dt(&gAdcThermistor));
  }

  // set up the poll signals and thread name
  k_poll_signal_init(&gStrobeState.events.readyChanged);
  k_poll_signal_init(&gStrobeState.events.periodicTimer);
  k_poll_signal_init(&gStrobeState.events.timedDisable);
  k_poll_signal_init(&gStrobeState.events.configChanged);
  k_poll_signal_init(&gStrobeState.events.readyEnableDebounceExpired);
  k_poll_signal_init(&gStrobeState.events.checkOutputUtilization);

  k_thread_name_set(gStrobeWorkerThread, "Strobe Worker");

  // Start trigger utilization monitoring timers
  k_timer_start(&gTriggerSampleTimer, TRIGGER_SAMPLE_INTERVAL, TRIGGER_SAMPLE_INTERVAL);
  k_timer_start(&gTriggerReportTimer, TRIGGER_REPORT_INTERVAL, TRIGGER_REPORT_INTERVAL);

  return 0;
}

/**
 * @brief Start the strobe worker task
 */
int strobe_worker_start() {
  k_thread_start(gStrobeWorkerThread);

  // wait for worker init done
  HANDLE_UNLIKELY(k_sem_take(&gStrobeState.initSem, K_FOREVER));

  return 0;
}

/**
 * @brief Handle strobe config change
 */
int strobe_worker_config_changed(const strobe_config_t *oldConfig, const strobe_config_t *newConfig) {
  // bail if configs are equal
  if (strobe_config_equal(oldConfig, newConfig)) {
    LOG_DBG("not writing strobe config (new config is same as old)");
    return 0;
  }

  // config changed, so trigger the timer (which triggers writing from worker task)
  k_timer_start(&gConfigWriteTimer, CONFIG_WRITE_DELAY, K_FOREVER);

  // also need to clear the duty cycle metrics
  int key = irq_lock();
  { reset_trigger_utilization(); }
  irq_unlock(key);

  return 0;
}

/**
 * @brief Strobe supervisor task entry point
 *
 * This thread handles all strobe-related events (e.g. ready state changes, processing current ADC
 * data, etc.) here. Events are signalled through various polling events.
 */
static void strobe_worker() {
  enum event_idx {
    kEventReady = 0,
    kEventPeriodicSense = 1,
    kEventTimedDisable = 2,
    kEventConfigChanged = 3,
    kEventReadyEnableDebounceExpired = 4,
    kEventOutputUtilization = 5,
  };

  int err;
  struct k_poll_event events[] = {
      K_POLL_EVENT_INITIALIZER(K_POLL_TYPE_SIGNAL, K_POLL_MODE_NOTIFY_ONLY, &gStrobeState.events.readyChanged),
      K_POLL_EVENT_INITIALIZER(K_POLL_TYPE_SIGNAL, K_POLL_MODE_NOTIFY_ONLY, &gStrobeState.events.periodicTimer),
      K_POLL_EVENT_INITIALIZER(K_POLL_TYPE_SIGNAL, K_POLL_MODE_NOTIFY_ONLY, &gStrobeState.events.timedDisable),
      K_POLL_EVENT_INITIALIZER(K_POLL_TYPE_SIGNAL, K_POLL_MODE_NOTIFY_ONLY, &gStrobeState.events.configChanged),
      K_POLL_EVENT_INITIALIZER(K_POLL_TYPE_SIGNAL, K_POLL_MODE_NOTIFY_ONLY,
                               &gStrobeState.events.readyEnableDebounceExpired),
      K_POLL_EVENT_INITIALIZER(K_POLL_TYPE_SIGNAL, K_POLL_MODE_NOTIFY_ONLY,
                               &gStrobeState.events.checkOutputUtilization),
  };

  // kick off the initial data collection
  k_timer_start(&gSenseTimer, ADC_SENSE_INTERVAL, ADC_SENSE_INTERVAL);

  k_poll_signal_raise(&gStrobeState.events.readyChanged, 0);
  k_poll_signal_raise(&gStrobeState.events.periodicTimer, 0);

  // load strobe config from EEPROM; apply defaults otherwise
  HANDLE_CRITICAL(load_strobe_config());

  // strobes ready to go
  k_sem_give(&gStrobeState.initSem);

  // loop forever to handle events
  do {
    err = k_poll(events, ARRAY_SIZE(events), K_FOREVER);
    if (err) {
      LOG_WRN("%s failed: %d", "k_poll", err);
      continue;
    }

    // ready signal line changed?
    if (events[kEventReady].signal->signaled) {
      ready_state_changed();

      k_poll_signal_reset(events[kEventReady].signal);
      events[kEventReady].state = K_POLL_STATE_NOT_READY;
    }
    // ready debounce timer expired
    if (events[kEventReadyEnableDebounceExpired].signal->signaled) {
      handle_ready_rising_edge_debounce();

      k_poll_signal_reset(events[kEventReadyEnableDebounceExpired].signal);
      events[kEventReadyEnableDebounceExpired].state = K_POLL_STATE_NOT_READY;
    }

    // convert periodic sensors (thermistor)
    if (events[kEventPeriodicSense].signal->signaled) {
      HANDLE_CRITICAL(k_mutex_lock(&gStrobeState.lock, K_FOREVER));
      {
        err = do_sample_thermistor();
        if (err) {
          LOG_WRN("%s failed: %d", "do_sample_thermistor", err);
        }
      }
      k_mutex_unlock(&gStrobeState.lock);

      k_poll_signal_reset(events[kEventPeriodicSense].signal);
      events[kEventPeriodicSense].state = K_POLL_STATE_NOT_READY;
    }

    // timed disable expired
    if (events[kEventTimedDisable].signal->signaled) {
      HANDLE_CRITICAL(k_mutex_lock(&gStrobeState.lock, K_FOREVER));
      {
        LOG_INF("timed disable expired, restoring state (enable=%u)", gStrobeState.timedDisable.oldState);

        gStrobeState.timedDisable.active = false;
        gStrobeState.enableFiring = gStrobeState.timedDisable.oldState;

        strobe_update_trigger_enable();
      }
      k_mutex_unlock(&gStrobeState.lock);

      k_poll_signal_reset(events[kEventTimedDisable].signal);
      events[kEventTimedDisable].state = K_POLL_STATE_NOT_READY;
    }

    // timer to check trigger line utilization expired
    if (events[kEventOutputUtilization].signal->signaled) {
      HANDLE_CRITICAL(k_mutex_lock(&gStrobeState.lock, K_FOREVER));
      { handle_trigger_report(); }
      k_mutex_unlock(&gStrobeState.lock);

      k_poll_signal_reset(events[kEventOutputUtilization].signal);
      events[kEventOutputUtilization].state = K_POLL_STATE_NOT_READY;
    }

    // strobe config to be written to EEPROM
    if (events[kEventConfigChanged].signal->signaled) {
      HANDLE_CRITICAL(k_mutex_lock(&gStrobeState.lock, K_FOREVER));
      {
        LOG_INF("Committing strobe config to EEPROM: period=%u, exposure=%u, ratio=%u", gStrobeState.config.period,
                gStrobeState.config.exposure, gStrobeState.config.target_exposure_ratio);

        err = write_eeprom_config();
        if (err) {
          // TODO: can we meaningfully recover from this? retry write again later?
          LOG_WRN("%s failed: %d", "write_eeprom_config", err);
        }
      }
      k_mutex_unlock(&gStrobeState.lock);

      k_poll_signal_reset(events[kEventConfigChanged].signal);
      events[kEventConfigChanged].state = K_POLL_STATE_NOT_READY;
    }
  } while (true);
}

/**
 * @brief Respond to ready line state change
 *
 * This signal indicates whether the capacitor banks are sufficiently charged to support triggering
 * the BTLs. If the signal is not asserted, firing should be inhibited until sufficient charge has
 * accumulated.
 */
static void ready_state_changed() {
  const bool isReady = (gpio_pin_get_dt(&gReadyGpio) == 1);

  HANDLE_CRITICAL(k_mutex_lock(&gStrobeState.lock, K_FOREVER));
  {
    const bool oldReady = gStrobeState.ready;
    gStrobeState.ready = isReady;

    // if ready state changed, we'll need to either enable or disable the strobes
    if (oldReady != isReady) {
      // rising READY edge: delay
      if (isReady) {
        k_timer_start(&gStrobeReadyEnableTimer, READY_RISING_EDGE_DELAY, K_NO_WAIT);
      }
      // falling edge takes effect immediately (and needs to abort timer)
      else {
        k_timer_stop(&gStrobeReadyEnableTimer);

        gStrobeState.ready = isReady;
        strobe_update_trigger_enable();
      }

      LOG_INF("strobe ready = %s", isReady ? "yes" : "no");
    }
  }
  k_mutex_unlock(&gStrobeState.lock);
}

/**
 * @brief Handle the rising edge on the ready signal
 */
static void handle_ready_rising_edge_debounce() {
  // ensure ready signal still asserted
  const bool isReady = (gpio_pin_get_dt(&gReadyGpio) == 1);
  if (!isReady) {
    LOG_WRN("rising edge timer expired but READY no longer asserted!");
    return;
  }

  // if so, do the needful
  LOG_INF("READY assert delay expired, enabling strobing");

  HANDLE_CRITICAL(k_mutex_lock(&gStrobeState.lock, K_FOREVER));
  {
    gStrobeState.ready = true;
    strobe_update_trigger_enable();
  }
  k_mutex_unlock(&gStrobeState.lock);
}

/**
 * @brief Debounce timer for ready rising edge expired
 */
static void ready_rising_edge_debounce_timer_expired(struct k_timer *) {
  k_poll_signal_raise(&gStrobeState.events.readyEnableDebounceExpired, 0);
}

/**
 * @brief Ready state change debounce work
 *
 * When the delayable work item expires we'll poll the ready line.
 */
static void ready_debounce_work(struct k_work *) { k_poll_signal_raise(&gStrobeState.events.readyChanged, 0); }

/**
 * @brief Strobe board ready state change interrupt handler
 *
 * This is called whenever a rising or falling edge is detected on the "strobe ready" input
 * signal.
 */
static void ready_gpio_callback(const struct device *, struct gpio_callback *, gpio_port_pins_t) {
  // TODO: should we use reschedule instead, w/ bounded max delay since first edge?
  k_work_schedule(&gReadyDebounceWork, READY_DEBOUNCE_INTERVAL);
}

/**
 * @brief Timer callback for ADC sense timer
 *
 * Kick off work item to perform ADC sampling on the system work queue.
 */
static void sense_tick(struct k_timer *) { k_poll_signal_raise(&gStrobeState.events.periodicTimer, 0); }

/**
 * @brief Capture thermistor value
 *
 * Perform a single conversion of the strobe board thermistor.
 *
 * @remark Caller must hold the internal state lock
 */
static int do_sample_thermistor() {
  uint16_t rawValue;

  // read thermistor
  struct adc_sequence seq = {
      .buffer = &rawValue,
      .buffer_size = sizeof(rawValue),
      .resolution = gAdcThermistor.resolution,
      .channels = BIT(gAdcThermistor.channel_id),
  };

  HANDLE_UNLIKELY(adc_read(gAdcThermistor.dev, &seq));
  HANDLE_UNLIKELY(ptp_slave_clk_get(&gStrobeState.temperature.timestamp));

  float mv = adc_raw_to_millivolts_dt_float(&gAdcThermistor, rawValue);

  /*
   * This conversion assumes the ERT-J1VR103J SMD 10k thermistor that's currently on rev 1 of
   * 1001148 STROBE BOARD PCBA with the following characteristics:
   *
   * - Nominal resistance (at 25°C): 10k, ±5%
   * - B at 25/50 = 4250
   */
  const double ohms = get_divider_ohms(10000, mv, 3300.0);
  const double temp = get_thermistor_temp(25, 10000, 4250, ohms);

  gStrobeState.temperature.value = temp;

  return 0;
}

/**
 * @brief Activate the timed disable feature
 *
 * When enabled, strobing (of BTLs) is inhibited for the given timeout. At the conclusion of the
 * timeout, the previous strobe enable state is restored.
 *
 * Any calls to enable/disable strobing while this is active do not have an immediate effect;
 * however, these affect the shadow enable state which is restored at the conclusion of the
 * timeout period.
 */
int strobe_timed_disable(k_timeout_t duration) {
  int err = 0;

  HANDLE_CRITICAL(k_mutex_lock(&gStrobeState.lock, K_FOREVER));
  {
    // bail if active already
    if (gStrobeState.timedDisable.active) {
      err = -EALREADY;
      goto beach;
    }

    // set state, disable firing, and start timer
    gStrobeState.timedDisable.oldState = gStrobeState.enableFiring;
    gStrobeState.timedDisable.active = true;

    gStrobeState.enableFiring = false;
    strobe_update_trigger_enable();

    k_timer_start(&gDisableTimer, duration, K_NO_WAIT);
  }
beach:;
  k_mutex_unlock(&gStrobeState.lock);

  return err;
}

/**
 * @brief Timed disable duration expiration
 */
static void disable_tick(struct k_timer *) { k_poll_signal_raise(&gStrobeState.events.timedDisable, 0); }

/**
 * @brief Set up strobe configuration
 *
 * Try to load the config from EEPROM, otherwise use defaults. Then apply that config to the
 * timer peripherals.
 */
static int load_strobe_config() {
  strobe_config_t config;
  int err;

  err = read_eeprom_config(&config);
  if (err) {
    LOG_WRN("%s failed: %d", "read_eeprom_config", err);

    config = kDefaultStrobeConfig;
  } else {
    LOG_INF("Restoring strobe config: period=%u, exposure=%u, ratio=%u", config.period, config.exposure,
            config.target_exposure_ratio);
  }

  HANDLE_UNLIKELY(strobe_set_config_internal(&config, false));

  return 0;
}

/**
 * @brief Delay after changing strobe config elapsed to commit to EEPROM
 */
static void config_write_tick(struct k_timer *) { k_poll_signal_raise(&gStrobeState.events.configChanged, 0); }

/**
 * @brief Load strobe config from EEPROM
 *
 * Try to load the strobe config from the EEPROM by reading the appropriate block. It will ensure the
 * config is valid.
 *
 * @return 0 if config was successfully restored, negative error code otherwise
 */
static int read_eeprom_config(strobe_config_t *outConfig) {
  int err;

  if (!outConfig) {
    return -EFAULT;
  }

  // read block from EEPROM
  eeprom_strobe_config_t read;
  size_t readSize = sizeof(read);

  memset(&read, 0, sizeof(read));
  err = eeprom_read_block(kEepromBlockTypeStrobeConfig, &read, &readSize);
  if (err) {
    // don't write error log if EEPROM block not found; this is a valid case
    if (err != -ENOENT) {
      LOG_WRN("%s failed: %d", "eeprom_read_block", err);
    }
    return err;
  }

  // validate the EEPROM read size, then validate the message
  if (readSize < sizeof(read)) {
    LOG_WRN("%s: got %u bytes, expected at least %u", "Short EEPROM read", readSize, sizeof(read));
    return -EMSGSIZE;
  }

  if (!validate_eeprom_block(&read)) {
    LOG_HEXDUMP_WRN(&read, readSize, "failed to validate block");
  }

  // it's valid, so copy out the config
  *outConfig = read.config;

  return 0;
}

/**
 * @brief Write current strobe config to EEPROM
 *
 * @remark Caller must hold the strobe state lock
 */
static int write_eeprom_config() {
  eeprom_strobe_config_t block;

  // create the EEPROM block
  memset(&block, 0, sizeof(block));

  block.size = sys_cpu_to_be32(sizeof(block));
  block.config = gStrobeState.config;

  // validate, then write it
  if (!validate_eeprom_block(&block)) {
    LOG_HEXDUMP_ERR(&block, sizeof(block), "failled to validate block pre-write (wtf)");
    return -EINVAL;
  }

  HANDLE_UNLIKELY(eeprom_write_block(kEepromBlockTypeStrobeConfig, &block, sizeof(block)));
  return 0;
}

/**
 * @brief Ensure the EEPROM strobe config block is valid
 *
 * This really just checks the size specified as well as the sanity of the values within.
 */
static bool validate_eeprom_block(const eeprom_strobe_config_t *block) {
  // validate size of the buffer
  const uint32_t bufSize = sys_be32_to_cpu(block->size);

  if (bufSize < sizeof(eeprom_strobe_config_t)) {
    LOG_WRN("%s: got %u bytes, expected at least %u", "Invalid size in block header", bufSize, sizeof(*block));
    return false;
  }

  // pro gamer move: copy the struct as it may not be aligned properly within the packed EEPROM buf
  strobe_config_t config;
  memcpy(&config, &block->config, sizeof(config));

  // TODO: byteswapping of contents in the struct?

  if (!strobe_check_config(&config)) {
    LOG_HEXDUMP_WRN(&config, sizeof(config), "Invalid strobe config");
    return false;
  }

  return true;
}

/**
 * @brief Timer callback for trigger sampling
 */
static void trigger_sample_tick(struct k_timer *) {
  uint32_t strobe_trigger = GPIOC->IDR & GPIO_PIN_7;

  // need to modify with irq disabled to avoid issues, these operations aren't atomic
  int key = irq_lock();
  {
    if (strobe_trigger > 0) {
      gTriggerUtil.active_samples++;
    }
    gTriggerUtil.total_samples++;
  }
  irq_unlock(key);
}

/**
 * @brief Timer callback for trigger utilization reporting
 */
static void trigger_report_tick(struct k_timer *) {
  k_poll_signal_raise(&gStrobeState.events.checkOutputUtilization, 1);
}

/**
 * @brief Clear state used by the trigger utilization metrics
 */
static void reset_trigger_utilization() {
  gTriggerUtil.active_samples = 0;
  gTriggerUtil.total_samples = 0;
}

/**
 * @brief Process strobe trigger utilization report
 *
 * Calculate the most recent average and then use that to perform fault detection if it's above a
 * what is acceptable for the current duty cycle.
 */
static void handle_trigger_report() {
  float utilization = 0.f;
  const float strobeDuty = ((float)gStrobeState.config.exposure) / ((float)gStrobeState.config.period);

  int key = irq_lock();
  {
    if (gTriggerUtil.total_samples > 0) {
      utilization = ((float)gTriggerUtil.active_samples) / ((float)gTriggerUtil.total_samples);
      reset_trigger_utilization();
    }
  }
  irq_unlock(key);

  if (!utilization) {
    return;
  }
  LOG_INF("Strobe trigger utilization: %.5g%%, duty cycle %.3g%%", utilization * 100.f, strobeDuty * 100.f);

  // check if too high for current duty cycle
  if (utilization > (strobeDuty * 2.f)) {
    LOG_ERR("Trigger utilization too high (%.5g%%, duty %.3g%%) - triggering cool-off", utilization * 100.f,
            strobeDuty * 100.f);

    strobe_timed_disable(EXCESS_TRIGGER_COOLDOWN);
  }
}

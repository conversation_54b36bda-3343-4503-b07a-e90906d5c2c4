#include <logging/log.h>
LOG_MODULE_REGISTER(strobe, CONFIG_APP_LOG_LEVEL);

#include <stm32h7xx_ll_gpio.h>

#include <device.h>
#include <devicetree.h>
#include <drivers/adc.h>
#include <drivers/zlcb.h>
#include <net/ptp_time.h>
#include <string.h>
#include <sys/atomic.h>

#include <arm_math.h>
#include <arm_math_f16.h>
#include <lib/ptp/ptp.h>
#include <utils/handle_errors.h>

#include "drivers/hrtim.h"
#include "power/power.h"
#include "sensors/isense.h"
#include "strobe/internal.h"
#include "strobe/strobe.h"

/*
 * HRTIM channel assignment (prod intent/rev 1 board)
 *
 * -  Strobe trigger: CHA.2
 * -  Target trigger: CHB.1
 * - Predict trigger: CHD.2
 */
#define HRTIM_CHANNEL_STROBE (kHrtimChannelA)
#define HRTIM_OUTPUT_STROBE (1)

#define HRTIM_CHANNEL_TARGET (kHrtimChannelB)
#define HRTIM_OUTPUT_TARGET (0)

#define HRTIM_CHANNEL_PREDICT (kHrtimChannelD)
#define HRTIM_OUTPUT_PREDICT (1)

// Logical OR of all HRTIM channels being used
#define HRTIM_CHANNEL_ALL (HRTIM_CHANNEL_STROBE | HRTIM_CHANNEL_TARGET | HRTIM_CHANNEL_PREDICT)

// Length of a camera trigger pulse (µS)
#define CAM_TRIGGER_PULSE_DURATION_US (50)

// Bit index (in strobe flag) indicating strobing/triggering is enabled
#define STROBE_FLAG_ENABLE_BIT (0)

static void update_output_enable(const bool);
static void set_trigger_timer_gpio_enable(const bool);

static void zlcb_trigger_callback(const void *);
static int apply_config(const struct strobe_config *);
static int apply_config_ch(const hrtim_channel_t channel, const size_t output, const uint32_t onPeriod,
                           const size_t offset);
static bool check_config_valid();

// Handle to the HRTIM instance used for trigger signals
static const struct device *const gDevHrtim = DEVICE_DT_GET(DT_PATH(strobe_board, hrtim));
// For triggering the overall waveform stuff
static const struct device *gZlcb = DEVICE_DT_GET(DT_ALIAS(strobe_zlcb));

// Strobe board handler's state
__dtcm_bss_section strobe_state_t gStrobeState;

// This flag is set when the software timer should trigger strobing (see `strobe_update_trigger_enable`)
__dtcm_bss_section static atomic_t gStrobeFlag = ATOMIC_INIT(0);

/**
 * @brief Initialize strobe board handler
 */
int strobe_init() {
  // initialize state
  memset(&gStrobeState, 0, sizeof(gStrobeState));
  k_mutex_init(&gStrobeState.lock);

  gStrobeState.mask = kStrobeMaskAll;
  gStrobeState.readyMask = kStrobeMaskTarget | kStrobeMaskPredict;

  HANDLE_UNLIKELY(strobe_worker_init());

  atomic_set(&gStrobeFlag, 0);

  // timer irq for triggering HRTIM waveforms
  HANDLE_UNLIKELY_BOOL(device_is_ready(gZlcb), ENXIO);

  // lastly, set up the HRTIM peripheral (for triggering strobes/cameras)
  HANDLE_UNLIKELY_BOOL(device_is_ready(gDevHrtim), ENXIO);

  HANDLE_UNLIKELY(hrtim_set_counter_mode(gDevHrtim, HRTIM_CHANNEL_STROBE, kHrtimCounterModeSingleShotResettable));
  HANDLE_UNLIKELY(hrtim_set_output_idle(gDevHrtim, HRTIM_CHANNEL_STROBE, HRTIM_OUTPUT_STROBE, false));

  HANDLE_UNLIKELY(hrtim_set_counter_mode(gDevHrtim, HRTIM_CHANNEL_TARGET, kHrtimCounterModeSingleShotResettable));
  HANDLE_UNLIKELY(hrtim_set_counter_mode(gDevHrtim, HRTIM_CHANNEL_PREDICT, kHrtimCounterModeSingleShotResettable));

  // ensure trigger outputs are disabled (such that BTLs do not strobe) then power up board
  // with a small delay to ensure voltage stabilizes (avoids spurious ready trigger)
  strobe_update_trigger_enable();

  HANDLE_UNLIKELY(power_enable(kPowerStrobeBoard));
  k_sleep(K_MSEC(15));

  // start worker pls
  HANDLE_UNLIKELY(strobe_worker_start());

  return 0;
}

/**
 * @brief Set whether strobe firing is enabled
 *
 * This also requires that there is nothing inhibiting firing and that the strobe board indicates
 * capacitors are sufficiently charged for strobing.
 */
int strobe_set_enabled(const bool isEnabled) {
  int err = 0;

  HANDLE_UNLIKELY(k_mutex_lock(&gStrobeState.lock, K_FOREVER));
  {
    // configuration must have been set & applied
    if (!check_config_valid()) {
      LOG_WRN("attempt to enable strobes without valid config!");

      err = -EBUSY;
      goto beach;
    }

    // then enable strobing
    if (gStrobeState.timedDisable.active) {
      LOG_INF("Strobe timed disable active, forcing new state: %u", isEnabled);
      gStrobeState.timedDisable.oldState = isEnabled;
    }

    gStrobeState.enableFiring = isEnabled;
    strobe_update_trigger_enable();
  }
beach:;
  k_mutex_unlock(&gStrobeState.lock);

  return err;
}

/**
 * @brief Get whether strobing is enabled
 *
 * If the timed disable feature is active, this returns the state that will be restored at the
 * conclusion of the timeout.
 */
int strobe_get_enabled(bool *outIsEnabled) {
  if (!outIsEnabled) {
    return -EFAULT;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gStrobeState.lock, K_FOREVER));
  {
    if (gStrobeState.timedDisable.active) {
      *outIsEnabled = gStrobeState.timedDisable.oldState;
    } else {
      *outIsEnabled = gStrobeState.enableFiring;
    }
  }
  k_mutex_unlock(&gStrobeState.lock);

  return 0;
}

/**
 * @brief Update the strobe configuration
 *
 * The config is validated, and if valid, is applied to the timers driving the strobes.
 */
int strobe_set_config(const strobe_config_t *newConfig) {
  int err = 0;

  // validate the provided config
  if (!newConfig) {
    return -EFAULT;
  } else if (!strobe_check_config(newConfig)) {
    return -EINVAL;
  }

  LOG_DBG("strobe cfg: period=%u, exposure=%u, ratio=%u", newConfig->period, newConfig->exposure,
          newConfig->target_exposure_ratio);

  // apply to the timer hardware
  HANDLE_UNLIKELY(k_mutex_lock(&gStrobeState.lock, K_FOREVER));
  { err = strobe_set_config_internal(newConfig, true); }
  k_mutex_unlock(&gStrobeState.lock);

  return err;
}

/**
 * @brief Test whether the provided strobe config is valid
 */
bool strobe_check_config(const strobe_config_t *config) {
  if (!config->target_exposure_ratio) {
    LOG_WRN("invalid %s: %d", "target:predict ratio", config->target_exposure_ratio);
    return false;
  }
  if (config->exposure < EXPOSURE_MIN_USEC || config->exposure > EXPOSURE_MAX_USEC) {
    LOG_WRN("invalid %s: %d", "exposure", config->exposure);
    return false;
  }
  if (config->period < (USEC_PER_SEC / STROBE_MAX_FREQ_HZ) || config->period > (USEC_PER_SEC / STROBE_MIN_FREQ_HZ)) {
    LOG_WRN("invalid %s: %d", "period", config->period);
    return -EINVAL;
  }

  const float duty = ((float)config->exposure) / ((float)config->period), percent = duty * 100.f;
  if (duty > STROBE_MAX_DUTY) {
    LOG_WRN("invalid duty cycle: %g%% (period=%u, exposure=%u)", percent, config->period, config->exposure);
    return -EINVAL;
  }

  return true;
}

/**
 * @brief Actually apply the given strobe config
 *
 * The strobe settings are configured into the timer peripheral used to generate the waveforms; all
 * internal state is updated as well.
 *
 * @param newConfig Strobe configuration to apply
 * @param shouldWrite Whether this config change is eligible to be written to EEPROM
 *
 * @remark Caller must hold strobe state lock
 *
 * @remark This does NOT validate that the provided strobe config is sane, it's expected the caller
 *         has already done this (see `strobe_check_config`)
 */
int strobe_set_config_internal(const strobe_config_t *newConfig, const bool shouldWrite) {
  int err;

  // inhibit firing
  gStrobeState.inhibit++;
  strobe_update_trigger_enable();

  // update HRTIM
  err = apply_config(newConfig);
  if (err) {
    // disable strobe firing in this case
    gStrobeState.enableFiring = false;
    LOG_WRN("%s failed: %d", "apply_config", err);
  }
  // configuring HRTIM was successful, update internal state
  else {
    strobe_config_t old;
    memcpy(&old, &gStrobeState.config, sizeof(old));

    // store new config, also write to EEPROM if requested
    gStrobeState.config = *newConfig;

    if (shouldWrite) {
      err = strobe_worker_config_changed(&old, newConfig);
      if (err) {
        LOG_WRN("%s failed: %d", "strobe_worker_config_changed", err);
      }
    }
  }

  // re-enable firing
  gStrobeState.inhibit--;
  strobe_update_trigger_enable();

  return err;
}

/**
 * @brief Get the strobe configuration
 */
int strobe_get_config(strobe_config_t *outConfig) {
  if (!outConfig) {
    return -EFAULT;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gStrobeState.lock, K_FOREVER));
  { memcpy(outConfig, &gStrobeState.config, sizeof(*outConfig)); }
  k_mutex_unlock(&gStrobeState.lock);

  return 0;
}

/**
 * @brief Do we have a valid strobe config?
 */
bool strobe_has_config() {
  bool valid = false;

  HANDLE_CRITICAL(k_mutex_lock(&gStrobeState.lock, K_FOREVER));
  { valid = check_config_valid(); }
  k_mutex_unlock(&gStrobeState.lock);

  return valid;
}

/**
 * @brief Helper to determine if current config is valid
 *
 * @remark Caller must hold the global state lock
 */
static bool check_config_valid() {
  return !!gStrobeState.config.period && !!gStrobeState.config.exposure && !!gStrobeState.config.target_exposure_ratio;
}

/**
 * @brief Retrieve current status of the strobe board
 */
int strobe_get_status(strobe_status_t *outStatus) {
  float volts, amps;
  int64_t isenseTimestamp = 0;

  if (!outStatus) {
    return -EFAULT;
  }

  HANDLE_UNLIKELY(isense_get(kIsenseStrobes, &isenseTimestamp, &amps, &volts));

  HANDLE_UNLIKELY(k_mutex_lock(&gStrobeState.lock, K_FOREVER));
  {
    strobe_status_t status;
    memset(&status, 0, sizeof(status));

    status.sense.voltage.timestamp = isenseTimestamp;
    status.sense.voltage.value = volts;
    status.sense.current.timestamp = isenseTimestamp;
    status.sense.current.value = amps;

    status.sense.temperature.timestamp = ptp_ts_net_to_ms(&gStrobeState.temperature.timestamp);
    status.sense.temperature.value = gStrobeState.temperature.value;

    status.ready = gStrobeState.ready;
    if (gStrobeState.timedDisable.active) {
      status.enabled = gStrobeState.timedDisable.oldState;
    } else {
      status.enabled = gStrobeState.enableFiring;
    }
    status.firing = atomic_test_bit(&gStrobeFlag, STROBE_FLAG_ENABLE_BIT) && gStrobeState.ready;

    status.currentConfig = gStrobeState.config;

    memcpy(outStatus, &status, sizeof(*outStatus));
  }
  k_mutex_unlock(&gStrobeState.lock);

  return 0;
}

/**
 * @brief Update strobe output state
 *
 * This will either force off or enable the regular timer output. All of the following conditions
 * must be met to enable strobing:
 *
 * - Strobing enabled in config
 * - Strobe board reports ready
 * - Inhibit counter is zero
 * - Safety cutout not engaged
 *
 * When the strobing functionality transitions from disabled to enabled, its internal timebase is
 * reset such that the next pulse train is complete, rather than being truncated at whatever point
 * the timebase was previously disabled.
 *
 * By default when the strobe board no longer reports ready we'll still trigger the cameras but
 * only stop the triggering of the BTLs; this is via the `readyMask`.
 *
 * @remark Caller must hold the state mutex
 */
void strobe_update_trigger_enable() {
  int irqKey = irq_lock();
  {
    const bool oldFlag = atomic_test_bit(&gStrobeFlag, STROBE_FLAG_ENABLE_BIT);
    bool enableStrobing = true;

    enableStrobing &= gStrobeState.enableFiring;
    enableStrobing &= !(gStrobeState.inhibit);

    if (gStrobeState.ready) {
      gStrobeState.readyMask |= kStrobeMaskBtl;
    } else {
      gStrobeState.readyMask &= ~kStrobeMaskBtl;
    }

    LOG_DBG("strobe state change: %u -> %u (ready=%u, enable=%u, inhibit=%u, masks=%08x/%08x)", oldFlag, enableStrobing,
            gStrobeState.ready, gStrobeState.enableFiring, gStrobeState.inhibit, gStrobeState.mask,
            gStrobeState.readyMask);

    // transition from inactive -> active: enable outputs & timebase
    if (!oldFlag && enableStrobing) {
      gStrobeState.strobeCount = 0;

      update_output_enable(true);

      // then enable flag (so ZLCB callback triggers the waveforms)
      atomic_set_bit(&gStrobeFlag, STROBE_FLAG_ENABLE_BIT);
    }
    // transition from active -> inactive: disable outputs & timebase
    else if (oldFlag && !enableStrobing) {
      atomic_clear_bit(&gStrobeFlag, STROBE_FLAG_ENABLE_BIT);

      // force outputs inactive and stop timebase
      update_output_enable(false);
    }

    // we only ever DISABLE the GPIO here
    if (!enableStrobing) {
      set_trigger_timer_gpio_enable(enableStrobing);
    }
  }
  irq_unlock(irqKey);
}

/**
 * @brief Configure timer output line enables
 *
 * This sets up whether the outputs are enabled and connected to the timer's output generation
 * circuitry. When disabled, the outputs are forced to their corresponding idle level.
 */
static void update_output_enable(const bool enable) {
  const strobe_channel_mask_t mask = gStrobeState.mask, readyMask = gStrobeState.readyMask;
  LOG_DBG("Strobe mask = %08x/%08x", mask, readyMask);

  // only the actual BTL trigger is configurable
  hrtim_set_output_enabled(gDevHrtim, HRTIM_CHANNEL_STROBE, HRTIM_OUTPUT_STROBE, enable);

  // predict/target triggers are basically always enabled
  hrtim_set_output_enabled(gDevHrtim, HRTIM_CHANNEL_TARGET, HRTIM_OUTPUT_TARGET, true);
  hrtim_set_output_enabled(gDevHrtim, HRTIM_CHANNEL_PREDICT, HRTIM_OUTPUT_PREDICT, true);
}

/**
 * @brief Begin strobing sequence
 *
 * This is invoked (from an ISR context, so no kernel functions can be used!) at a fixed time
 * interval corresponding to the period specified in the strobe config. It then starts a single
 * waveform cycle in HRTIM for the relevant output channels.
 *
 * We need this as HRTIM itself cannot handle particularly long periods since its internal
 * counters are 16 bit only. Since all channels are triggered simultaneously, the rising edges of
 * signals will be aligned. Each channel will therefore have the same amount of jitter/drift (if
 * any) in the output waveforms.
 */
static void zlcb_trigger_callback(const void *) {
  const strobe_channel_mask_t readyMask = gStrobeState.readyMask;

  // strobe enable flag must be set
  if (!atomic_test_bit(&gStrobeFlag, STROBE_FLAG_ENABLE_BIT)) {
    return;
  }

  // figure out what channels to trigger (handle predict:target ratio)
  hrtim_channel_t channel = HRTIM_CHANNEL_ALL;

  if (++gStrobeState.strobeCount == gStrobeState.config.target_exposure_ratio) {
    gStrobeState.strobeCount = 0;
  } else {
    channel &= ~HRTIM_CHANNEL_PREDICT;
  }

  // inhibit strobes if not ready
  if (!(readyMask & kStrobeMaskBtl)) {
    channel &= ~HRTIM_CHANNEL_STROBE;
  } else {
    set_trigger_timer_gpio_enable(true);
  }

  hrtim_reset_channel(gDevHrtim, channel);
}

/**
 * @brief Update strobe timer configuration
 *
 * Set the timebase and also reconfigure the zlcb callback that serves to trigger the waveform
 * generation.
 *
 * The period programmed into the HRTIM is really just a placeholder; it needs to only be long
 * enough to account for the longest pulse (plus a bit of fudge factor) generated as the ZLCB
 * serves as the actual low frequency timebase to trigger pulsing.
 *
 * @remark HRTIM timebase should be stopped when reconfiguring; otherwise, the outputs may show
 *         various glitches.
 */
static int apply_config(const strobe_config_t *config) {
  const uint32_t exposureNs = config->exposure * NSEC_PER_USEC,
                 // periodNs = config->period * NSEC_PER_USEC,
      periodNs = MIN(MAX(config->period - 5, 1000), 5000) * NSEC_PER_USEC,
                 triggerNs = (CAM_TRIGGER_PULSE_DURATION_US * NSEC_PER_USEC), startOffsetNs = 500;

  // disable timebase before changing anything
  hrtim_set_timebase_enabled(gDevHrtim, HRTIM_CHANNEL_ALL, false);

  // program period into all timers: this shall be long enough for each pulse
  HANDLE_UNLIKELY(hrtim_set_period(gDevHrtim, HRTIM_CHANNEL_STROBE, periodNs));
  HANDLE_UNLIKELY(hrtim_set_period(gDevHrtim, HRTIM_CHANNEL_PREDICT, periodNs));
  HANDLE_UNLIKELY(hrtim_set_period(gDevHrtim, HRTIM_CHANNEL_TARGET, periodNs));

  LOG_DBG("HRTIM period=%u ns, exposure=%u, trigger=%u, off=%u", periodNs, exposureNs, triggerNs, startOffsetNs);

  // program the pulse durations for each channel
  HANDLE_UNLIKELY(apply_config_ch(HRTIM_CHANNEL_STROBE, HRTIM_OUTPUT_STROBE, exposureNs, 0));

  // predict trigger
  HANDLE_UNLIKELY(apply_config_ch(HRTIM_CHANNEL_PREDICT, HRTIM_OUTPUT_PREDICT, triggerNs, 0));

  // target trigger
  HANDLE_UNLIKELY(apply_config_ch(HRTIM_CHANNEL_TARGET, HRTIM_OUTPUT_TARGET, triggerNs, 0));

  // reconfigure ZLCB (to actually generate the triggers)
  HANDLE_UNLIKELY(zlcb_set_callback_usec(gZlcb, config->period, zlcb_trigger_callback, NULL));

  // reset the timebase before re-enabling
  hrtim_reset_channel(gDevHrtim, HRTIM_CHANNEL_ALL);
  hrtim_set_timebase_enabled(gDevHrtim, HRTIM_CHANNEL_ALL, true);

  return 0;
}

/**
 * @brief Set whether the timer GPIO is enabled
 *
 * If disabled, the output is forced low by using general purpose output mode; otherwise it is in
 * alternate function mode.
 *
 * @param enabled Whether the GPIO is in timer mode (set)
 */
static void set_trigger_timer_gpio_enable(const bool enabled) {
  int lock = irq_lock();
  {
    // timer mode
    if (enabled) {
      LL_GPIO_SetPinMode(GPIOC, LL_GPIO_PIN_7, LL_GPIO_MODE_ALTERNATE);
    }
    // force off
    else {
      LL_GPIO_ResetOutputPin(GPIOC, LL_GPIO_PIN_7);
      LL_GPIO_SetPinMode(GPIOC, LL_GPIO_PIN_7, LL_GPIO_MODE_OUTPUT);
    }
  }
  irq_unlock(lock);
}

/**
 * @brief Apply per-channel config
 *
 * Set a particular channel to generate a pulse of the specified width.
 *
 * @param onPeriod On duration of the pulse (ns)
 * @param offset Row offset (index)
 */
static int apply_config_ch(const hrtim_channel_t channel, const size_t output, const uint32_t onPeriod,
                           const size_t offset) {
  const uint32_t startOffsetNs = ((offset + 1) * 500);

  hrtim_waveform_point_t points[2] = {
      // assert after a small delay
      {
          .time = startOffsetNs,
          .ch1Active = (output == 0),
          .ch2Active = (output == 1),
          .ch1Reset = false,
          .ch2Reset = false,
      },
      // deassert after the fixed duration for camera trigger signal
      {
          .time = (startOffsetNs + onPeriod),
          .ch1Active = false,
          .ch2Active = false,
          .ch1Reset = (output == 0),
          .ch2Reset = (output == 1),
      },
  };

  HANDLE_UNLIKELY(hrtim_set_waveform(gDevHrtim, channel, points, 2));

  return 0;
}

/**
 * @brief Update output channel enable mask
 *
 * This allows for gating of any of the three output channels from the strobe handler.
 */
int strobe_set_mask(const strobe_channel_mask_t newMask) {
  HANDLE_CRITICAL(k_mutex_lock(&gStrobeState.lock, K_FOREVER));
  {
    gStrobeState.mask = newMask;

    if (atomic_test_bit(&gStrobeFlag, STROBE_FLAG_ENABLE_BIT)) {
      update_output_enable(true);
    }
  }
  k_mutex_unlock(&gStrobeState.lock);

  return 0;
}

/**
 * @file
 * @brief Data types and internal functions for strobe handler
 */
#pragma once

#include <stddef.h>
#include <stdint.h>

#include <zephyr/net/ptp_time.h>
#include <zephyr/zephyr.h>

#include "strobe/strobe.h"

// Minimum exposure duration, µS
#define EXPOSURE_MIN_USEC (25)
// Maximum exposure duration, µS
#define EXPOSURE_MAX_USEC (1250)
// Minimum strobe frequency, Hz
#define STROBE_MIN_FREQ_HZ (30)
// Maximum strobe frequency, Hz
#define STROBE_MAX_FREQ_HZ (125)
// Maximum strobe duty cycle, percent ([0, 1])
#define STROBE_MAX_DUTY (0.08f)

/**
 * @brief Compare two strobe config instances to see if they're equal
 *
 * This does an element-wise comparison rather than memcmp.
 */
static inline bool strobe_config_equal(const strobe_config_t *lhs, const strobe_config_t *rhs) {
  return (lhs->exposure == rhs->exposure) && (lhs->period == rhs->period) &&
         (lhs->target_exposure_ratio == rhs->target_exposure_ratio);
}

// ADC value + timestamp
typedef struct strobe_analog_sample {
  struct net_ptp_time timestamp;
  float value;
} strobe_analog_sample_t;

typedef struct strobe_state {
  // Lock to protect all shared state
  struct k_mutex lock;
  // Signalled when worker thread is up and running
  struct k_sem initSem;

  // Board temperature (from thermistor)
  strobe_analog_sample_t temperature;

  // inhibit ref count; only when zero can we fire. used by `update_trigger_enable`
  uint8_t inhibit;
  // is strobe board ready to fire? (e.g. caps charged fully)
  bool ready;
  // whether strobe firing is enabled
  bool enableFiring;

  // Timed disable feature allows inhibiting strobing for a fixed time period (for module id)
  struct {
    // Set when the timer is running and strobing is inhibited
    bool active;
    // Old state of `enableFiring` (e.g. what's restored at conclusion)
    bool oldState;
  } timedDisable;

  // current output configuration
  strobe_config_t config;

  // strobe count (used for target:predict trigger ratio)
  size_t strobeCount;

  // mask of which output channels are enabled
  strobe_channel_mask_t mask;
  // strobe mask to disable channels when not ready (used to suppress trigger)
  strobe_channel_mask_t readyMask;

  // polling events used to communicate events to worker task
  struct {
    // polling event indicating ready state changed
    struct k_poll_signal readyChanged;
    // timer for periodic sampling has expired
    struct k_poll_signal periodicTimer;
    // timed disable has expired, restore previous state
    struct k_poll_signal timedDisable;
    // Strobe configuration changed (and post-change timer expired)
    struct k_poll_signal configChanged;
    // Signalled when the READY signal debounce timer expired
    struct k_poll_signal readyEnableDebounceExpired;
    // Timer signal to process the periodic output activity sampling
    struct k_poll_signal checkOutputUtilization;
  } events;
} strobe_state_t;

/**
 * @brief Nonvolatile strobe configuration
 *
 * Stored in EEPROM, this dude holds the most recently applied strobe configuration; this is used
 * so on startup the most recent config can be restored before hardware_manager is started.
 */
typedef struct eeprom_strobe_config {
  // Length of this struct, in bytes
  uint32_t size;
  // Actual strobe config data
  strobe_config_t config;
} __attribute__((packed)) eeprom_strobe_config_t;

extern strobe_state_t gStrobeState;

bool strobe_check_config(const strobe_config_t *config);
int strobe_set_config_internal(const strobe_config_t *newConfig, const bool shouldWrite);
void strobe_update_trigger_enable();

int strobe_worker_init();
int strobe_worker_start();

int strobe_worker_config_changed(const strobe_config_t *oldConfig, const strobe_config_t *newConfig);

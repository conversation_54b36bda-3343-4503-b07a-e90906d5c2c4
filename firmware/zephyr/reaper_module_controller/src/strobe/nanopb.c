#include <zephyr/logging/log.h>
LOG_MODULE_DECLARE(strobe);

#include <utils/handle_errors.h>

#include "strobe/nanopb.h"
#include "strobe/strobe.h"

int strobe_nanopb_fill_status(reaper_module_controller_StrobeStatusReply *reply) {
  strobe_status_t status;
  memset(&status, 0, sizeof(status));

  HANDLE_UNLIKELY(strobe_get_status(&status));

  reply->voltageTimestamp = status.sense.voltage.timestamp;
  reply->voltage = status.sense.voltage.value;
  reply->currentTimestamp = status.sense.current.timestamp;
  reply->current = status.sense.current.value;
  reply->temperatureTimestamp = status.sense.temperature.timestamp;
  reply->temperature = status.sense.temperature.value;
  reply->ready = status.ready;
  reply->enabled = status.enabled;
  reply->firing = status.firing;

  if (status.currentConfig.exposure) {
    reply->which__exposureUs = reaper_module_controller_StrobeStatusReply_exposureUs_tag;
    reply->_exposureUs.exposureUs = status.currentConfig.exposure;
  }
  if (status.currentConfig.period) {
    reply->which__periodUs = reaper_module_controller_StrobeStatusReply_periodUs_tag;
    reply->_periodUs.periodUs = status.currentConfig.period;
  }
  if (status.currentConfig.target_exposure_ratio) {
    reply->which__targetsPerPredict = reaper_module_controller_StrobeStatusReply_targetsPerPredict_tag;
    reply->_targetsPerPredict.targetsPerPredict = status.currentConfig.target_exposure_ratio;
  }

  return 0;
}

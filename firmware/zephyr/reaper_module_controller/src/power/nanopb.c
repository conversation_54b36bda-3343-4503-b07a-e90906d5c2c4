#include <zephyr/logging/log.h>
LOG_MODULE_DECLARE(power);

#include <utils/handle_errors.h>

#include "power/nanopb.h"
#include "power/power.h"
#include "power/relays.h"

/**
 * @brief Fill in message with the current power state
 */
int power_nanopb_fill(reaper_module_controller_PowerReply *reply) {
  power_channel_t current = 0;
  HANDLE_UNLIKELY(power_get_state(&current));

  reply->ethSwitch = (current & kPowerEthSwitch);
  reply->relayBoard = (current & kPowerRelayBoard);
  reply->strobeBoard = (current & kPowerStrobeBoard);
  reply->predictCam = (current & kPowerPredictCam);

  return 0;
}

/**
 * @brief Fill in message with the current relay state
 */
int relays_nanopb_fill(reaper_module_controller_RelayReply *reply) {
  relay_channel_t relayState = 0;

  HANDLE_UNLIKELY(relays_get_state(&relayState));

  reply->btl = !!(relayState & kRelayBtl);
  reply->laser = !!(relayState & kRelayLdd);
  reply->pc = !!(relayState & kRelayPc);

  return 0;
}

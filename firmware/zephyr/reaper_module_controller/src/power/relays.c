#include <zephyr/logging/log.h>
LOG_MODULE_DECLARE(power);

#include <stdint.h>
#include <zephyr/device.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/irq.h>
#include <zephyr/sys/atomic.h>

#include <utils/carbon_response_codes.h>
#include <utils/handle_errors.h>

#include "power/power.h"
#include "power/relays.h"

static int update_relays();

/// Current (actually written to GPIOs) relay output state
__dtcm_data_section static atomic_t gActualState = ATOMIC_INIT(0);
/// Desired relay output state
__dtcm_data_section static atomic_t gDesiredState = ATOMIC_INIT(0);

static const struct gpio_dt_spec gRelayLdd = GPIO_DT_SPEC_GET_BY_IDX(DT_PATH(relay_board), gpios, 0);
static const struct gpio_dt_spec gRelayBtl = GPIO_DT_SPEC_GET_BY_IDX(DT_PATH(relay_board), gpios, 1);
static const struct gpio_dt_spec gRelayPc = GPIO_DT_SPEC_GET_BY_IDX(DT_PATH(relay_board), gpios, 2);

/**
 * @brief Initialize relay outputs
 */
int relays_init() {
  atomic_clear(&gActualState);
  atomic_clear(&gDesiredState);

  // ensure the GPIO ports are initialized
  HANDLE_UNLIKELY(!device_is_ready(gRelayLdd.port));
  HANDLE_UNLIKELY(!device_is_ready(gRelayBtl.port));
  HANDLE_UNLIKELY(!device_is_ready(gRelayPc.port));

  // then set up the actual output ports
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&gRelayLdd, GPIO_OUTPUT_INACTIVE));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&gRelayBtl, GPIO_OUTPUT_INACTIVE));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&gRelayPc, GPIO_OUTPUT_INACTIVE));

  // enable power to the relay board
  HANDLE_UNLIKELY(power_enable(kPowerRelayBoard));

  return 0;
}

/**
 * @brief Get the current output state of all relays
 *
 * If the corresponding bit is set in the output bitfield, the relay is powered.
 */
int relays_get_state(relay_channel_t *outState) {
  if (!outState) {
    return CARBON_ERROR_UNKNOWN;
  }

  *outState = atomic_get(&gDesiredState);

  return CARBON_RESPONSE_OK;
}

/**
 * @brief Set the state of all relays
 */
int relays_set_state(const relay_channel_t newState) {
  atomic_set(&gDesiredState, newState);

  HANDLE_UNLIKELY(update_relays());
  return CARBON_RESPONSE_OK;
}

/**
 * @brief Set the state of a single relay
 *
 * No other relays will be changed.
 */
int relays_set_single(const relay_channel_t relay, const bool isOn) {
  if (!relay) {
    return CARBON_ERROR_UNKNOWN;
  }

  if (isOn) {
    atomic_or(&gDesiredState, relay);
  } else {
    atomic_and(&gDesiredState, ~relay);
  }

  HANDLE_UNLIKELY(update_relays());
  return CARBON_RESPONSE_OK;
}

/**
 * @brief Update relay output state
 *
 * Set the state of relay GPIOs to match internal state. This will update only the pins that differ
 * from what the actual output state is: some pins are managed via the regulator subsystem which is
 * ref counted.
 */
static int update_relays() {
  const relay_channel_t state = atomic_get(&gDesiredState), actualState = atomic_get(&gActualState),
                        changed = state ^ actualState;

  uintptr_t irqFlag = irq_lock();
  {
    if (changed & kRelayBtl) {
      HANDLE_UNLIKELY(gpio_pin_set_dt(&gRelayBtl, (state & kRelayBtl) ? 1 : 0));
    }
    if (changed & kRelayLdd) {
      HANDLE_UNLIKELY(gpio_pin_set_dt(&gRelayLdd, (state & kRelayLdd) ? 1 : 0));
    }
    if (changed & kRelayPc) {
      HANDLE_UNLIKELY(gpio_pin_set_dt(&gRelayPc, (state & kRelayPc) ? 1 : 0));
    }

    atomic_set(&gActualState, state);
  }
  irq_unlock(irqFlag);

  return 0;
}

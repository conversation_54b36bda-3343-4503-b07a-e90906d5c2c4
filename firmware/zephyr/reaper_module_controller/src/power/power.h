/**
 * @file
 * @brief Power control
 *
 * Manages switched 24V DC power provided to various external subsystems.
 */
#pragma once

#include <stdbool.h>

/**
 * @brief Switchable power control endpoints
 *
 * Each of the enum values can be ORed together.
 */
typedef enum power_channel {
  kPowerEthSwitch = (1 << 0),
  kPowerRelayBoard = (1 << 1),
  kPowerStrobeBoard = (1 << 2),
  kPowerPredictCam = (1 << 3),
} power_channel_t;

int power_init();

int power_get_state(power_channel_t *outState);
int power_update(const power_channel_t enable, const power_channel_t disable);

inline static int power_enable(const power_channel_t channel) { return power_update(channel, 0); }

inline static int power_disable(const power_channel_t channel) { return power_update(0, channel); }

/**
 * @file
 *
 * @brief Power control relay IO
 */
#pragma once

#include <stdbool.h>

/**
 * @brief Available relay outputs
 *
 * Many functions (specifically those to get/set more than one relay state at once) can accept a
 * combination of these entries by logical OR.
 */
typedef enum relay_channel {
  kRelayBtl = (1 << 0),
  kRelayLdd = (1 << 1),
  kRelayPc = (1 << 2),
} relay_channel_t;

int relays_init();

int relays_get_state(relay_channel_t *outState);
int relays_set_state(const relay_channel_t newState);
int relays_set_single(const relay_channel_t relay, const bool isOn);

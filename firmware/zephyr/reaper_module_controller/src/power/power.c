#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(power, CONFIG_APP_LOG_LEVEL);

#include <stdint.h>
#include <zephyr/device.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/regulator.h>
#include <zephyr/sys/atomic.h>

#include <utils/handle_errors.h>

#include "power/power.h"

static int update_state();

/// Current (actually written to GPIOs) power output state
__dtcm_data_section static atomic_t gActualState = ATOMIC_INIT(0);
/// Desired power output state
__dtcm_data_section static atomic_t gDesiredState = ATOMIC_INIT(0);

// +24V for Ethernet switch is controlled via load switch
static const struct device *gEthSwitchPower = DEVICE_DT_GET(DT_PATH(eth_switch_power));

// Relay board +24V power
#if DT_NODE_EXISTS(DT_PATH(relay_board, power))
static const struct device *gRelayBoard = DEVICE_DT_GET(DT_PATH(relay_board, power));
#else
static const struct device *gRelayBoard = NULL;
#endif

// strobe board +24V power
#if DT_NODE_EXISTS(DT_PATH(strobe_board, power))
static const struct device *gStrobeBoard = DEVICE_DT_GET(DT_PATH(strobe_board, power));
#else
static const struct device *gStrobeBoard = NULL;
#endif

// Predict camera power
#if DT_NODE_EXISTS(DT_PATH(predict_cam, power))
static const struct device *gPredictCam = DEVICE_DT_GET(DT_PATH(predict_cam, power));
#else
static const struct device *gPredictCam = NULL;
#endif

/**
 * @brief Initialize the power output control
 *
 * This sets up all the regulators specified in device tree. They will all be off by default.
 */
int power_init() {
  atomic_clear(&gActualState);
  atomic_clear(&gDesiredState);

  if (gEthSwitchPower) {
    HANDLE_UNLIKELY(!device_is_ready(gEthSwitchPower));
  }
  if (gRelayBoard) {
    HANDLE_UNLIKELY(!device_is_ready(gRelayBoard));
  }
  if (gStrobeBoard) {
    HANDLE_UNLIKELY(!device_is_ready(gStrobeBoard));
  }
  if (gPredictCam) {
    HANDLE_UNLIKELY(!device_is_ready(gPredictCam));
  }

  return 0;
}

/**
 * @brief Get the actual state of switched power rails
 */
int power_get_state(power_channel_t *outState) {
  if (!outState) {
    return -EFAULT;
  }

  *outState = atomic_get(&gActualState);

  return 0;
}

/**
 * @brief Enable and/or disable power channels
 */
int power_update(const power_channel_t enable, const power_channel_t disable) {
  atomic_or(&gDesiredState, enable);
  atomic_and(&gDesiredState, ~disable);

  return update_state();
}

/**
 * @brief Power manager callback when rail is enabled
 *
 * This doesn't do anything
 */
static void regulator_callback(struct onoff_manager *, struct onoff_client *, uint32_t, int) {}

/**
 * @brief Update the physical state of output lines
 *
 * Determine which IOs changed and then update them synchronously
 */
static int update_state() {
  const power_channel_t state = atomic_get(&gDesiredState), actualState = atomic_get(&gActualState),
                        changed = state ^ actualState;

  LOG_DBG("power update: %04x (%04x -> %04x)", changed, actualState, state);

  uintptr_t irqFlag = irq_lock();
  {
    if (gEthSwitchPower && (changed & kPowerEthSwitch)) {
      __dtcm_bss_section static struct onoff_client cb;
      memset(&cb, 0, sizeof(cb));

      sys_notify_init_callback(&cb.notify, regulator_callback);

      if (state & kPowerEthSwitch) {
        HANDLE_UNLIKELY(regulator_enable(gEthSwitchPower, &cb));
      } else {
        HANDLE_UNLIKELY(regulator_disable(gEthSwitchPower));
      }
    }

    if (gRelayBoard && (changed & kPowerRelayBoard)) {
      __dtcm_bss_section static struct onoff_client cb;
      memset(&cb, 0, sizeof(cb));

      sys_notify_init_callback(&cb.notify, regulator_callback);

      if (state & kPowerRelayBoard) {
        HANDLE_UNLIKELY(regulator_enable(gRelayBoard, &cb));
      } else {
        HANDLE_UNLIKELY(regulator_disable(gRelayBoard));
      }
    }

    if (gStrobeBoard && (changed & kPowerStrobeBoard)) {
      __dtcm_bss_section static struct onoff_client cb;
      memset(&cb, 0, sizeof(cb));

      sys_notify_init_callback(&cb.notify, regulator_callback);

      if (state & kPowerStrobeBoard) {
        HANDLE_UNLIKELY(regulator_enable(gStrobeBoard, &cb));
      } else {
        HANDLE_UNLIKELY(regulator_disable(gStrobeBoard));
      }
    }

    if (gPredictCam && (changed & kPowerPredictCam)) {
      __dtcm_bss_section static struct onoff_client cb;
      memset(&cb, 0, sizeof(cb));

      sys_notify_init_callback(&cb.notify, regulator_callback);

      if (state & kPowerPredictCam) {
        HANDLE_UNLIKELY(regulator_enable(gPredictCam, &cb));
      } else {
        HANDLE_UNLIKELY(regulator_disable(gPredictCam));
      }
    }

    atomic_set(&gActualState, state);
  }
  irq_unlock(irqFlag);

  return 0;
}

/**
 * @file
 * @brief Scanner connection supervision
 *
 * Controls power to the scanners, and supervises their current draw as well as the fuse blown
 * detection.
 */
#pragma once

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

#include "generated/lib/drivers/nanopb/proto/reaper_module_controller.pb.h"

// Whether control over scanner power is available (not on dev board)
#define HAS_SCANNER_CONTROL DT_NODE_EXISTS(DT_PATH(scanners))
// Total number of scanners in the module, managed by the MCB
#define NUM_SCANNERS (2)

typedef enum scanner {
  kScannerA = (1 << 0),
  kScannerB = (1 << 1),
} scanner_t;

typedef struct scanner_state {
  // Whether switchable +24V power to the scanner is enabled
  bool power;

  struct {
    // Whether scanner's fuse has blown since last reset
    bool triggered;
    // Timestamp at which the fuse blew
    int64_t timestamp;
  } ocp;

  struct {
    // Instantaneous current consumption of scanner, amps
    float current;
    // Timestamp at which this current reading was taken
    int64_t timestamp;
  } isense;
} scanner_state_t;

int scanners_init();

int scanners_set_power(const scanner_t enable, const scanner_t disable);
int scanners_get_power(scanner_t *outEnabled);

int scanners_get_state(const scanner_t which, scanner_state_t *outState);
int scanners_reset_ocp(const scanner_t which);

int scanners_nanopb_fill_status(reaper_module_controller_ScannerStatus *, const size_t numEntries);

/**
 * @brief Convert an index to a scanner enum value
 */
static inline scanner_t scanners_index_to_enum(const size_t index) {
  if (index == 0) {
    return kScannerA;
  } else if (index == 1) {
    return kScannerB;
  }

  __builtin_unreachable();
}

cmake_minimum_required(VERSION 3.13.1)

set_property(GLOBAL PROPERTY CSTD c11)

find_package(Zephyr REQUIRED HINTS $ENV{ZEPHYR_BASE})
project(reaper_module_controller)

# build protobuf defs separately
set(ROOT_PATH ../../..)
set(GENERATED_PROTO_PATH ${ROOT_PATH}/generated/lib/drivers/nanopb/proto)
file(GLOB PROTO_SOURCES CONFIGURE_DEPENDS ${GENERATED_PROTO_PATH}/*.c)
file(GLOB PROTO_HEADERS CONFIGURE_DEPENDS ${GENERATED_PROTO_PATH}/*.h)

zephyr_library_named(protobufs)
target_sources(protobufs PRIVATE
    ${PROTO_SOURCES} ${PROTO_HEADERS}
)
target_include_directories(protobufs PUBLIC ${ROOT_PATH})

# create the app target
target_sources(app PRIVATE
    src/main.c
    src/module_id.c
    src/module_id.h
    # drivers
    src/drivers/hrtim.c
    src/drivers/hrtim.h
    src/drivers/eeprom.c
    src/drivers/eeprom.h
    src/drivers/coredump.c
    src/drivers/coredump.h
    # networking (address assignment/configuration)
    src/network/init.c
    src/network/init.h
    src/network/dhcp.c
    src/network/dhcp.h
    src/network/utils.c
    src/network/utils.h
    src/network/rx_lockup_watcher.c
    src/network/rx_lockup_watcher.h
    # sensors
    src/sensors/sensors.c
    src/sensors/sensors.h
    src/sensors/isense.c
    src/sensors/isense.h
    src/sensors/nanopb.c
    src/sensors/nanopb.h

    # external RPC interface (via UDP and serial)
    src/rpc/common.c
    src/rpc/common.h
    src/rpc/udp_server.c
    src/rpc/udp_server.h
    src/rpc/pc_serial.c
    src/rpc/pc_serial.h
    # strobe handling
    src/strobe/strobe.c
    src/strobe/strobe.h
    src/strobe/worker.c
    src/strobe/nanopb.c
    src/strobe/nanopb.h

    # power control
    src/power/power.c
    src/power/power.h
    src/power/relays.c
    src/power/relays.h
    src/power/nanopb.c
    src/power/nanopb.h

    # supervisory/management stuff
    src/watchdog.c
    src/watchdog.h
    src/fans.c
    src/fans.h
    src/scanners.c
    src/scanners.h
)

target_include_directories(app PRIVATE src)
target_link_libraries(app PRIVATE protobufs)

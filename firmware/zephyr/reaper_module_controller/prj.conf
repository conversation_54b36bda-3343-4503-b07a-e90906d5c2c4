CONFIG_THREAD_NAME=y
CONFIG_THREAD_MONITOR=y

# PC serial config
CONFIG_APP_PCSERIAL_BAUD=9600
CONFIG_APP_PCSERIAL_MAX_MSG=384

# senor settings
CONFIG_APP_SENSOR_DUMP_READINGS=n

# application ports
CONFIG_APP_UDP_PORT=4200

# network configuration
CONFIG_LOG_BACKEND_NET_SERVER="*********:2442"

CONFIG_LOG_PROCESS_THREAD_SLEEP_MS=500
CONFIG_LOG_BUFFER_SIZE=4096

# embiggen the buffer used for RTT logs to host
# larger buffer helps collect more logs for coredumps, too
CONFIG_USE_SEGGER_RTT=y
CONFIG_SEGGER_RTT_SECTION_DTCM=y
CONFIG_RTT_CONSOLE=n
CONFIG_SEGGER_RTT_BUFFER_SIZE_UP=8192
CONFIG_SEGGER_RTT_MAX_NUM_UP_BUFFERS=1
CONFIG_SEGGER_RTT_BUFFER_SIZE_DOWN=16
CONFIG_SEGGER_RTT_MAX_NUM_DOWN_BUFFERS=1

# libraries
CONFIG_LIB_COBS=y
CONFIG_LIB_UDP=y
CONFIG_LIB_SMP=y
CONFIG_LIB_PTP=y
CONFIG_CARBON_PTP_USE_INTERCEPT=n
CONFIG_LIB_STATUS_LED=y
CONFIG_LIB_STATUS_LED_COLOR_CONVERSION=y
CONFIG_NANOPB=y
CONFIG_HWINFO=y

CONFIG_CMSIS_DSP=y
CONFIG_CMSIS_DSP_FILTERING=y
CONFIG_CMSIS_DSP_STATISTICS=y

CONFIG_NEWLIB_LIBC=y
CONFIG_NEWLIB_LIBC_NANO=y

# enable printing floating point numbers
CONFIG_CBPRINTF_FP_SUPPORT=y

# enable zero latency callbacks (direct timer irq)
CONFIG_ZLCB=y
CONFIG_ZLCB_STM32=y

# increase temperature and humidity resolution
CONFIG_BME680_TEMP_OVER_8X=y
CONFIG_BME680_HUMIDITY_OVER_8X=y
CONFIG_BME680_FILTER_16=y

################################################################################
# Everything below here probably doesn't need changing

# Some command handlers require a large stack
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=2048

# erase flash block by block rather than all at once (reduce mcumgr image upload latency)
CONFIG_IMG_ERASE_PROGRESSIVELY=y

# enable certain APIs
CONFIG_UART_ASYNC_API=y
CONFIG_ADC_ASYNC=y

# networking support
CONFIG_NETWORKING=y
CONFIG_NET_L2_ETHERNET=y
CONFIG_NET_IPV4=y
CONFIG_NET_IPV6=n
CONFIG_NET_TCP=n
CONFIG_NET_UDP=y
CONFIG_NET_ARP=y
CONFIG_NET_SOCKETS=y
CONFIG_NET_SOCKETS_POSIX_NAMES=y
CONFIG_NET_LOG=y
CONFIG_NET_PKT_RX_COUNT=512
CONFIG_NET_BUF_RX_COUNT=512
CONFIG_NET_BUF_DATA_SIZE=256
CONFIG_NET_BUF_USER_DATA_SIZE=64

# embiggen the ARP table
CONFIG_NET_ARP_TABLE_SIZE=64

# networking statistics
CONFIG_NET_STATISTICS=y
CONFIG_NET_STATISTICS_USER_API=y
CONFIG_NET_STATISTICS_PER_INTERFACE=y
CONFIG_NET_STATISTICS_ETHERNET=y

# dynamic hostname
CONFIG_NET_HOSTNAME_ENABLE=y
CONFIG_NET_HOSTNAME="reaper-mcb-"
CONFIG_NET_HOSTNAME_UNIQUE=y
CONFIG_NET_HOSTNAME_UNIQUE_UPDATE=y

# PTP support
CONFIG_PTP_CLOCK=y
CONFIG_NET_L2_PTP=y

# runtime management of MAC and IP addresses
CONFIG_NET_MGMT=y
CONFIG_NET_MGMT_EVENT=y
CONFIG_NET_L2_ETHERNET_MGMT=y

CONFIG_NET_CONNECTION_MANAGER=y

# enable multiple IPs per interface (for DHCP + static)
CONFIG_NET_IF_UNICAST_IPV4_ADDR_COUNT=3

# respond to broadcast ping
CONFIG_NET_ICMPV4_ACCEPT_BROADCAST=y

# remote logging via syslog
CONFIG_LOG=y
CONFIG_LOG_BACKEND_NET=y
CONFIG_LOG_TIMESTAMP_64BIT=y

# Support rebooting under software control (for updates)
CONFIG_REBOOT=y

# MCUBoot
CONFIG_BOOTLOADER_MCUBOOT=y
CONFIG_MCUBOOT_BOOT_MAX_ALIGN=32

# mcumgr (updates)
CONFIG_MCUMGR=y
CONFIG_MCUMGR_SMP_NOTIFY_CALLBACK=y
CONFIG_MCUMGR_BUF_COUNT=4
CONFIG_MCUMGR_SMP_UDP=y
CONFIG_MCUMGR_SMP_UDP_IPV4=y
CONFIG_MCUMGR_CMD_IMG_MGMT=y
CONFIG_MCUMGR_CMD_OS_MGMT=y

# driver components
CONFIG_IMU_BMI270=y

# EEPROM emulation
#
# The initialization priority is set to the lowest possible (e.g. highest numerical value) such that
# the flash controller the EEPROM emulation depends on will already have been initialized.
# Otherwise we are beholden to the behavior of the linker on how it will order the initialization
# functions for the EEPROM emulation and internal flash which may result in the flash controller not
# being initialized when the EEPROM emulation is.
CONFIG_EEPROM=y
CONFIG_EEPROM_EMULATOR=y
# CONFIG_EEPROM_EMULATOR_INIT_PRIORITY=99
CONFIG_EEPROM_INIT_PRIORITY=99

# memory allocation policies
CONFIG_NOCACHE_MEMORY=y
CONFIG_DCACHE=y

# no UART console/shell stuff
CONFIG_ADC_SHELL=n
CONFIG_FLASH_SHELL=n

# LED subsystem used for status indicators (to support dimming/PWM)
CONFIG_LED=y
CONFIG_LED_GPIO=y
CONFIG_LED_PWM=y

# random number generation
CONFIG_XOSHIRO_RANDOM_GENERATOR=y

# GPIO pin straps for hardware rev
CONFIG_HW_REV_STRAPS=y

# Include more information in crash dumps
CONFIG_EXTRA_EXCEPTION_INFO=y

# Coredumps on crash, via custom serial log interface
CONFIG_DEBUG_COREDUMP=y
CONFIG_DEBUG_COREDUMP_BACKEND_OTHER=y
CONFIG_COREDUMP_DEVICE=y

# XXX: this breaks mcumgr... do not enable until fixed
# zeroize stack memory
# CONFIG_INIT_STACKS=y

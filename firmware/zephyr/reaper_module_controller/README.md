# Reaper Module Controller (MCB)

This directory contains the firmware for the Reaper Module Control Board (MCB)
which is responsible for controlling power, communications, and reading in data
from a variety of sensors inside each Weeding Module.

## Board Versions

|                                     | **MCU**     | **DTB Variant** | **Rev ID** |
|-------------------------------------|-------------|-----------------|------------|
| **Reaper STM Dev Board (JLCPCB)**   | STM32H743ZI | h743_dev    | 0 |
| **Reaper STM Dev Board (OOTB)**     | STM32H753ZI | h743_dev    | 0 |
| **Reaper MCB Prod Intent (JLCPCB)** | STM32H743ZI | prod        | 0 |
| **Reaper MCB Prod Intent (OOTB)**   | STM32H753ZI | prod        | 0 |
| **Reaper MCB Prod Intent Rev A** | STM32H753ZI | prod          |  0 |
| **Reaper MCB Prod Intent Rev B** | STM32H753ZI | prod          |  1 |

"DTB Variant" refers to the board revision that's passed to `carbon_zephyr_cli`
as the `-r` argument. "Rev ID" is the physical board revision indicated using the
revision GPIO straps.

The only difference between the STM32H743 and '753 is that the latter features
hardware crypto/security features which aren't used. Therefore, the same
firmware runs on both boards.

CONFIG_GPIO=y

# Enable networking
CONFIG_NETWORKING=y
CONFIG_NET_IPV4=y
CONFIG_NET_IPV6=n
CONFIG_NET_TCP=n
CONFIG_NET_UDP=y
CONFIG_NET_SOCKETS=y
CONFIG_NET_SOCKETS_POSIX_NAMES=y
CONFIG_NET_LOG=y
CONFIG_NET_PKT_RX_COUNT=64
CONFIG_NET_BUF_RX_COUNT=256

# Network settings
CONFIG_NET_CONFIG_SETTINGS=y
CONFIG_NET_CONFIG_NEED_IPV4=y
CONFIG_NET_CONFIG_MY_IPV4_ADDR="**********"
CONFIG_NET_CONFIG_PEER_IPV4_ADDR="*********"
CONFIG_NET_CONFIG_MY_IPV4_NETMASK="*************"
# Don't wait for link up to start running
CONFIG_NET_CONFIG_INIT_TIMEOUT=-1
CONFIG_NET_CONNECTION_MANAGER=y
CONFIG_NET_BUF_USER_DATA_SIZE=64

# Port
CONFIG_APP_UDP_PORT=4243

# Syslog
CONFIG_LOG=y
CONFIG_LOG_BACKEND_NET=y
CONFIG_LOG_BACKEND_NET_SERVER="*********:2442"

#CONFIG_SYS_CLOCK_HW_CYCLES_PER_SEC=100000000

# Support reboot
CONFIG_REBOOT=y

#UDP
CONFIG_LIB_UDP=y
CONFIG_PB_REQUESTS_BUFFER_SIZE=10
# Nanopb
CONFIG_NANOPB=y
# PTP
CONFIG_LIB_PTP=y

# MCUBoot
CONFIG_BOOTLOADER_MCUBOOT=y

# Enable mcumgr.
CONFIG_MCUMGR=y
CONFIG_MCUMGR_SMP_NOTIFY_CALLBACK=y
CONFIG_LIB_SMP=y
CONFIG_FLASH=y
CONFIG_MCUBOOT_BOOT_MAX_ALIGN=32
CONFIG_MCUMGR_BUF_COUNT=4

#CAN
CONFIG_CAN=y
CONFIG_CAN_MCP2515=y
# Initialize CAN after SPI
CONFIG_CAN_INIT_PRIORITY=80
#SPI
CONFIG_SPI=y

# Some command handlers require a large stack.
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=2048

# Enable statistics and statistic names.
CONFIG_STATS=y
CONFIG_STATS_NAMES=y

# Enable most core commands.
CONFIG_MCUMGR_CMD_IMG_MGMT=y
CONFIG_MCUMGR_CMD_OS_MGMT=y
CONFIG_MCUMGR_CMD_STAT_MGMT=y
CONFIG_MCUMGR_SMP_UDP=y
CONFIG_MCUMGR_SMP_UDP_IPV4=y

# Required by the `taskstat` command.
CONFIG_THREAD_MONITOR=y
CONFIG_NEWLIB_LIBC=n

CONFIG_LOG_TIMESTAMP_64BIT=y

# Watchdog
CONFIG_LIB_WATCHDOG=y
CONFIG_WATCHDOG=y
CONFIG_WDT_DISABLE_AT_BOOT=n

CONFIG_LOG_BACKEND_UART=n
CONFIG_LOG_BACKEND_UART_ASYNC=n
CONFIG_UART_CONSOLE=n

# SEGGER RTT
CONFIG_SEGGER_RTT_SECTION_DTCM=y
CONFIG_USE_SEGGER_RTT=y

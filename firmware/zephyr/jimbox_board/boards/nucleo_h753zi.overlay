/ {
    chosen {
        zephyr,code-partition = &slot0_partition;
        zephyr,can-primary = &can3;
    };

    aliases {
        watchdog0 = &iwdg1;
        zlcb0 = &zlcb0;
    };

    gpios {
        compatible = "gpio-keys";
        // Speed control
        wheel_a_in {
            gpios = < &gpioe 2 (GPIO_PULL_DOWN | GPIO_ACTIVE_HIGH) >;
            label = "WHEEL_A_IN";
        };
        wheel_b_in {
            gpios = < &gpioe 3 (GPIO_PULL_DOWN | GPIO_ACTIVE_HIGH) >;
            label = "WHEEL_B_IN";
        };
        wheel_a_out {
            gpios = < &gpioe 6 (GPIO_PULL_DOWN | GPIO_ACTIVE_HIGH) >;
            label = "WHEEL_A_OUT";
        };
        wheel_b_out {
            gpios = < &gpioe 7 (GPIO_PULL_DOWN | GPIO_ACTIVE_HIGH) >;
            label = "WHEEL_B_OUT";
        };
        wheel_a_ctrl {
            gpios = < &gpioe 8 (GPIO_PULL_DOWN | GPIO_ACTIVE_HIGH) >;
            label = "ENCB_OUT2";
        };
        wheel_b_ctrl {
            gpios = < &gpioe 9 (GPIO_PULL_DOWN | GPIO_ACTIVE_HIGH) >;
            label = "ENC_RELAYS";
        };
        spi_reset {
            gpios = <&gpiob 5 (GPIO_PULL_UP | GPIO_ACTIVE_LOW)>;
            label = "CAN_RESET";
        };
    };
};

&spi1 {
    status = "disabled";
};

&spi2 {
    pinctrl-0 = <&spi2_sck_pb10 &spi2_miso_pb14 &spi2_mosi_pb15 &spi2_nss_pb9>;
    pinctrl-names = "default";
    //cs-gpios = <&gpiob 9 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;
    status = "okay";
    can3: mcp2515@0 {
        compatible = "microchip,mcp2515";
        spi-max-frequency = < 4000000 >;
        int-gpios = <&gpiob 6 GPIO_ACTIVE_LOW>;
        status = "okay";
        label = "CAN_3";
        reg = < 0x0 >;
        osc-freq = <16000000>;
        bus-speed = < 500000 >;
        sjw = < 0x1 >;
        prop-seg = < 1 >;
        phase-seg1 = < 4 >;
        phase-seg2 = < 2 >;
        #address-cells = < 0x1 >;
        #size-cells = < 0x0 >;
    };
};

&can1 {
    status = "disabled";
};


&iwdg1 {
    status = "okay";
};

&timers8 {
    status = "okay";
    st,prescaler = <119>;
    zlcb0: zlcb {
        compatible = "st,stm32-zlcb";
        status = "okay";
        label = "ZLCB0";
    };
};

&usart3 {
    status = "disabled";
};

&clk_hse {
    /delete-property/ hse-bypass;
};
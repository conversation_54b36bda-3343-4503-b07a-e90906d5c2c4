#pragma once

#define DEBUG_CAN 1

typedef enum {
  TRACTOR_PACKET_TYPE_NULL = 0,
  TRACTOR_PACKET_TYPE_SPEED_WHEEL = 1,
  TRACTOR_PACKET_TYPE_SPEED_LEVER_POSITION = 2,
  TRACTOR_PACKET_TYPE_S1 = 3,
  TRACTOR_PACKET_TYPE_S2 = 4,
} Tractor_Packet_Type_t;

typedef struct Tractor_Packet_t {
  uint32_t id;
  Tractor_Packet_Type_t type;
  uint8_t *data;
  uint8_t dlc;
} Tractor_Packet_t;

extern uint32_t speed_wheel_pos;
extern uint32_t speed_lever_pos;
extern uint32_t speed_lever_s1;
extern uint32_t speed_lever_s2;

void tractor_can_init(void);
void send_dummy_can_frame();
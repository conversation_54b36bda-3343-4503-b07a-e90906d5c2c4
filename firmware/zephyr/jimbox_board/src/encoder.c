#include <logging/log.h>
LOG_MODULE_REGISTER(speed_wheel, CONFIG_APP_LOG_LEVEL);
#include "encoder.h"
#include <drivers/gpio.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

static const struct gpio_dt_spec enca = GPIO_DT_SPEC_GET(DT_PATH(gpios, wheel_a_in), gpios);
static const struct gpio_dt_spec encb = GPIO_DT_SPEC_GET(DT_PATH(gpios, wheel_b_in), gpios);
static const struct gpio_dt_spec enca_out = GPIO_DT_SPEC_GET(DT_PATH(gpios, wheel_a_out), gpios);
static const struct gpio_dt_spec encb_out = GPIO_DT_SPEC_GET(DT_PATH(gpios, wheel_b_out), gpios);
static const struct gpio_dt_spec encb_out2 = GPIO_DT_SPEC_GET(DT_PATH(gpios, wheel_b_out), gpios);
static const struct gpio_dt_spec enc_relay_a = GPIO_DT_SPEC_GET(DT_PATH(gpios, wheel_a_ctrl), gpios);
static const struct gpio_dt_spec enc_relay_b = GPIO_DT_SPEC_GET(DT_PATH(gpios, wheel_b_ctrl), gpios);

static struct gpio_callback enca_cb_data;
static struct gpio_callback encb_cb_data;

int tick_period_ms = 50;

#define ENC_A_MASK(val) ((val & 0b10) >> 1)
#define ENC_B_MASK(val) (val & 0b01)
uint8_t enc_value = 0b00;
bool enc_enabled = false;
uint8_t joystick_value = 0;

enum enc_values { ONE = 0b00, TWO = 0b10, THREE = 0b11, FOUR = 0b01 };

void set_enc_relays(uint8_t relays_on) {
  gpio_pin_set(enc_relay_a.port, enc_relay_a.pin, relays_on);
  gpio_pin_set(enc_relay_b.port, enc_relay_b.pin, relays_on);
}

void set_enc_value() {
  uint8_t enc_a_value = ENC_A_MASK(enc_value);
  uint8_t enc_b_value = ENC_B_MASK(enc_value);
  if (enc_a_value) {
    if (enc_b_value) {
      LOG_INF("THREE");
    } else {
      LOG_INF("TWO");
    }
  } else {
    if (enc_b_value) {
      LOG_INF("FOUR");
    } else {
      LOG_INF("ONE");
    }
  }
  gpio_pin_set(enca_out.port, enca_out.pin, enc_a_value);
  gpio_pin_set(encb_out.port, encb_out.pin, enc_b_value);
  gpio_pin_set(encb_out2.port, encb_out2.pin, enc_b_value);
}

uint8_t get_joystick_value() {
  uint8_t joystick_a = gpio_pin_get(enca.port, enca.pin);
  uint8_t joystick_b = gpio_pin_get(encb.port, encb.pin);
  return (joystick_a << 1) | (joystick_b);
}

void set_encoder_to_joystick() {
  enc_enabled = false;
  joystick_value = get_joystick_value();
  enc_value = joystick_value;
  set_enc_value();
}

uint8_t joystick_did_increment(uint8_t old_val, uint8_t new_val) {
  if (old_val == ONE && new_val == TWO) {
    return 1;
  }
  if (old_val == TWO && new_val == THREE) {
    return 1;
  }
  if (old_val == THREE && new_val == FOUR) {
    return 1;
  }
  if (old_val == FOUR && new_val == ONE) {
    return 1;
  }
  return 0;
}

uint8_t joystick_did_decrement(uint8_t old_val, uint8_t new_val) {
  if (old_val == ONE && new_val == FOUR) {
    return 1;
  }
  if (old_val == FOUR && new_val == THREE) {
    return 1;
  }
  if (old_val == THREE && new_val == TWO) {
    return 1;
  }
  if (old_val == TWO && new_val == ONE) {
    return 1;
  }
  return 0;
}

void check_joystick_change() {
  uint8_t new_val = get_joystick_value();
  if (joystick_did_increment(joystick_value, new_val)) {
    LOG_INF("Operator incremented joystick");
    increment_encoder();
  }
  if (joystick_did_decrement(joystick_value, new_val)) {
    LOG_INF("Operator decremented joystick");
    decrement_encoder();
  }
  joystick_value = new_val;
}

void enca_isr(const struct device *dev, struct gpio_callback *cb, uint32_t pins) {
  enc_enabled = false;
  LOG_INF("Encoder A tick");
  check_joystick_change();
}
void encb_isr(const struct device *dev, struct gpio_callback *cb, uint32_t pins) {
  enc_enabled = false;
  LOG_INF("Encoder B tick");
  check_joystick_change();
}

void encoder_init() {
  int ret;

  if (!device_is_ready(enca.port)) {
    LOG_ERR("Error: encoder device %s is not ready \n", enca.port->name);
    return;
  }
  if (!device_is_ready(encb.port)) {
    LOG_ERR("Error: encoder device %s is not ready \n", encb.port->name);
    return;
  }
  if (!device_is_ready(enca_out.port)) {
    LOG_ERR("Error: encoder device %s is not ready \n", encb.port->name);
    return;
  }
  if (!device_is_ready(encb_out.port)) {
    LOG_ERR("Error: encoder device %s is not ready \n", encb.port->name);
    return;
  }

  ret = gpio_pin_configure_dt(&enca_out, GPIO_OUTPUT);
  ret = gpio_pin_configure_dt(&encb_out, GPIO_OUTPUT);
  ret = gpio_pin_configure_dt(&encb_out2, GPIO_OUTPUT);
  ret = gpio_pin_configure_dt(&enc_relay_a, GPIO_OUTPUT);
  ret = gpio_pin_configure_dt(&enc_relay_b, GPIO_OUTPUT);

  ret = gpio_pin_configure_dt(&enca, GPIO_INPUT);
  if (ret != 0) {
    LOG_ERR("Error %d: failed to configure %s pin %d\n", ret, enca.port->name, enca.pin);
    return;
  }
  ret = gpio_pin_configure_dt(&encb, GPIO_INPUT);
  if (ret != 0) {
    LOG_ERR("Error %d: failed to configure %s pin %d\n", ret, encb.port->name, encb.pin);
    return;
  }

  ret = gpio_pin_interrupt_configure_dt(&enca, GPIO_INT_EDGE_BOTH);
  if (ret != 0) {
    LOG_ERR("Error %d: failed to configure interrupt on %s pin %d\n", ret, enca.port->name, enca.pin);
    return;
  }
  ret = gpio_pin_interrupt_configure_dt(&encb, GPIO_INT_EDGE_BOTH);
  if (ret != 0) {
    printk("Error %d: failed to configure interrupt on %s pin %d\n", ret, encb.port->name, encb.pin);
    return;
  }

  gpio_init_callback(&enca_cb_data, enca_isr, BIT(enca.pin));
  gpio_add_callback(enca.port, &enca_cb_data);
  LOG_INF("Set up ENC_A at %s pin %d\n", enca.port->name, enca.pin);
  gpio_init_callback(&encb_cb_data, encb_isr, BIT(encb.pin));
  gpio_add_callback(encb.port, &encb_cb_data);
  LOG_INF("Set up ENC_B at %s pin %d\n", encb.port->name, encb.pin);

  set_encoder_to_joystick();
  k_sleep(K_MSEC(20));
  set_enc_relays(1);
}

void increment_encoder() {
  switch (enc_value) {
  case ONE:
    enc_value = TWO;
    break;
  case TWO:
    enc_value = THREE;
    break;
  case THREE:
    enc_value = FOUR;
    break;
  case FOUR:
    enc_value = ONE;
    break;
  default:
    enc_value = ONE;
    break;
  }
  set_enc_value();
}

void decrement_encoder() {
  switch (enc_value) {
  case ONE:
    enc_value = FOUR;
    break;
  case TWO:
    enc_value = ONE;
    break;
  case THREE:
    enc_value = TWO;
    break;
  case FOUR:
    enc_value = THREE;
    break;
  default:
    enc_value = ONE;
    break;
  }
  set_enc_value();
}

void encoder_send_ticks(int ticks) {
  if (!enc_enabled) {
    return;
  }

  if (ticks < 0) {
    for (int i = 0; i < -1 * ticks; i++) {
      decrement_encoder();
      k_sleep(K_MSEC(tick_period_ms));
    }

  } else {
    for (int i = 0; i < ticks; i++) {
      increment_encoder();
      k_sleep(K_MSEC(tick_period_ms));
    }
  }
}

void encoder_set_enabled(bool enabled) {
  enc_enabled = enabled;
  if (enc_enabled) {
    // Relays turn on first time encoder is enabled
    set_enc_relays(1);
  }
}

bool encoder_get_enabled() { return enc_enabled; }
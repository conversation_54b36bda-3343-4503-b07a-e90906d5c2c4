#include <logging/log.h>
LOG_MODULE_REGISTER(main, CONFIG_APP_LOG_LEVEL);

#include "encoder.h"
#include "lib/smp/smp.h"
#include "tractor_can.h"
#include "udp_server.h"
#include <devicetree.h>
#include <lib/watchdog/watchdog.h>
#include <string.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

static int boot() {
  LOG_INF("Jimbox booting");

  encoder_init();
  tractor_can_init();

  return 0;
}

void main() {
  start_watchdog();
  start_smp_lib();
  HANDLE_CRITICAL(boot());
  start_udp_server();

  LOG_INF("Jimbox finished booting");
  for (;;) {
    k_msleep(100);
    //#ifdef DEBUG_CAN
    // send_dummy_can_frame();
    //#endif
  }
}
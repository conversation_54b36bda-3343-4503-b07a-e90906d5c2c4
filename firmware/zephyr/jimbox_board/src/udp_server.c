#include <logging/log.h>
LOG_MODULE_REGISTER(udp_server, CONFIG_APP_LOG_LEVEL);

#include <errno.h>
#include <lib/udp/udp.h>
#include <pb_decode.h>
#include <pb_encode.h>
#include <sys/reboot.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

#include "encoder.h"
#include "generated/lib/drivers/nanopb/proto/cruise.pb.h"
#include "generated/lib/drivers/nanopb/proto/jimbox_board.pb.h"
#include "tractor_can.h"
#include "udp_server.h"

#if IS_ENABLED(CONFIG_NET_TC_THREAD_COOPERATIVE)
#define THREAD_PRIORITY K_PRIO_COOP(CONFIG_NUM_COOP_PRIORITIES - 1)
#else
#define THREAD_PRIORITY K_PRIO_PREEMPT(8)
#endif

#define RECV_BUFFER_SIZE 1280

static void process_udp();

K_THREAD_DEFINE(udp_thread_id, RECV_BUFFER_SIZE + 1024, process_udp, NULL, NULL, NULL, THREAD_PRIORITY, 0,
                K_TICKS_FOREVER);
UDP_SERVER_DEFINE(npb_udp_server, CONFIG_PB_REQUESTS_BUFFER_SIZE, 1024, THREAD_PRIORITY);

static void handle_ping(diagnostic_Ping *req, diagnostic_Pong *resp) { resp->x = req->x; }

void handle_throttle(cruise_Throttle_Request *req, cruise_Throttle_Reply *resp) {
  encoder_send_ticks(req->change);
  resp->success = true;
}

void handle_enable(cruise_Enable_Request *req, cruise_Enable_Reply *resp) {
  encoder_set_enabled(req->enabled);
  resp->enabled = true;
}

void handle_status(cruise_Status_Request *req, cruise_Status_Reply *resp) {
  resp->enabled = encoder_get_enabled();
  resp->speed_ticks = speed_wheel_pos;
  resp->speed_lever_position = speed_lever_pos;
  resp->speed_lever_s1 = speed_lever_s1;
  resp->speed_lever_s2 = speed_lever_s2;
}

void handle_cruise(cruise_Request *req, cruise_Reply *resp) {
  switch (req->which_request) {
  case cruise_Request_throttle_tag:
    resp->which_reply = cruise_Reply_throttle_tag;
    handle_throttle(&req->request.throttle, &resp->reply.throttle);
    break;
  case cruise_Request_enable_tag:
    resp->which_reply = cruise_Reply_enable_tag;
    handle_enable(&req->request.enable, &resp->reply.enable);
    break;
  case cruise_Request_status_tag:
    resp->which_reply = cruise_Reply_status_tag;
    handle_status(&req->request.status, &resp->reply.status);
    break;
  }
}

static void serve_request(uint8_t *data, uint16_t received, udp_msg_metadata *metadata) {
  jimbox_board_Request req = jimbox_board_Request_init_zero;
  pb_istream_t istream = pb_istream_from_buffer(data, received);
  if (!pb_decode(&istream, jimbox_board_Request_fields, &req)) {
    LOG_WRN("Failed to decode UDP packet with size %d", received);
    return;
  }

  jimbox_board_Reply resp = jimbox_board_Reply_init_zero;
  resp.has_header = true;
  resp.header.requestId = req.header.requestId;

  switch (req.which_request) {
  case jimbox_board_Request_ping_tag:
    resp.which_reply = jimbox_board_Reply_pong_tag;
    handle_ping(&req.request.ping, &resp.reply.pong);
    break;
  case jimbox_board_Request_reset_tag:
    sys_reboot(SYS_REBOOT_COLD);
    break;
  case jimbox_board_Request_cruise_tag:
    resp.which_reply = jimbox_board_Reply_cruise_tag;
    handle_cruise(&req.request.cruise, &resp.reply.cruise);
    break;
  }

  pb_ostream_t ostream = pb_ostream_from_buffer(data, udp_buffer_size(&npb_udp_server));
  if (!pb_encode(&ostream, jimbox_board_Reply_fields, &resp)) {
    LOG_WRN("Failed to encode nanoPB response");
    return;
  }
  udp_tx(&npb_udp_server, data, ostream.bytes_written, metadata);
}

static void process_udp() {
  for (;;) {
    uint8_t *data;
    udp_msg_metadata metadata;
    uint16_t received = udp_get_data_no_copy(&npb_udp_server, &data, &metadata, 0);
    serve_request(data, received, &metadata);
    udp_get_data_release(&npb_udp_server, data);
  }
}

void start_udp_server() {
  UDP_SERVER_START(npb_udp_server, CONFIG_APP_UDP_PORT);
  k_thread_name_set(udp_thread_id, "udp");
  k_thread_start(udp_thread_id);
}
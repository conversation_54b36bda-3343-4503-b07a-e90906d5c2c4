#include <logging/log.h>
LOG_MODULE_REGISTER(tractor_can, CONFIG_APP_LOG_LEVEL);

#include "drivers/gpio.h"
#include "encoder.h"
#include "tractor_can.h"
#include <device.h>
#include <devicetree.h>
#include <drivers/can.h>
#include <string.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

static const struct gpio_dt_spec can_reset = GPIO_DT_SPEC_GET(DT_PATH(gpios, spi_reset), gpios);

#define MAILBOX_SIZE (10)

//#define SPEED_WHEEL_ID (0x8CFFFE8C)
#define SPEED_WHEEL_ID (0x18FFFA05)
#define SPEED_LEVER_ID (0x0CFFFE8C)
#define GEAR_ID (0xFFFFFFFF)
#define SPEED_WHEEL_FIRST_BYTE (0x8C)
#define SPEED_LEVER_POSITION_ID (0x00)
#define SPEED_LEVER_POSITION_FIRST_BYTE (0x8C)

typedef struct {
  // char __aligned(4) buffer[MAILBOX_SIZE * sizeof(J1939_Pgn_Packet_t)];
  Tractor_Packet_t buffer[MAILBOX_SIZE];
  struct k_msgq msgq;
} Tractor_Mailbox_t;

typedef struct Tractor_t {
  const struct device *can;
  Tractor_Mailbox_t mailbox;
  uint32_t address;
} Tractor;

Tractor tractor;

uint32_t speed_wheel_pos;
uint32_t speed_lever_pos;
uint32_t speed_lever_s1;
uint32_t speed_lever_s2;

void speed_wheel_callback(const struct device *dev, struct zcan_frame *msg, void *arg) {
  if (msg->data[1] != 75) {
    return;
  }
  // Received frame on can0 - ID: 8CFFFE8C, DLC: 8, Data: 8C FA 9F 1F 37 FF FF FF
  /**
  if (msg->data[1] == 75) {
      LOG_INF("RX %d", msg->data[3] / 7);
      //uint16_t val = msg->data[2] << 8 | msg->data[3];
      uint32_t raw = msg->data[2] << 16 | msg->data[3] << 8 | msg->data[4];
      LOG_INF("Raw %x", raw);
      uint16_t val = ((msg->data[4] << 8 | msg->data[3]) / 7) - msg->data[4];
      LOG_INF("Test %d", val);
  }
  */
  ARG_UNUSED(dev);

  uint32_t speed_wheel_value = ((msg->data[4] << 8 | msg->data[3]) / 7) - msg->data[4];

  if (speed_wheel_value == 0) {
    encoder_set_enabled(false);
  }

  if (speed_wheel_value != speed_wheel_pos) {
    LOG_INF("Speed changed to %d", speed_wheel_value);
  }

  speed_wheel_pos = speed_wheel_value;
}

void gear_callback(const struct device *dev, struct zcan_frame *msg, void *arg) {
  ARG_UNUSED(dev);

  return;
}

void speed_lever_callback(const struct device *dev, struct zcan_frame *msg, void *arg) {
  ARG_UNUSED(dev);

  Tractor_Packet_t tractor_msg;
  uint8_t data[8];
  memcpy(&data[0], &(msg->data[0]), 8);

  // Parse message as Tractor Msg
  tractor_msg.dlc = msg->dlc;
  tractor_msg.data = msg->data;
  tractor_msg.id = msg->id;

  // Check if message is a speed lever message
  if (msg->id == SPEED_LEVER_POSITION_ID || msg->data[0] == SPEED_LEVER_POSITION_FIRST_BYTE) {
    tractor_msg.type = TRACTOR_PACKET_TYPE_SPEED_LEVER_POSITION;
    speed_lever_pos = msg->data[1];
    uint32_t speed_lever_f1_f2_value = msg->data[2];
    if (speed_lever_f1_f2_value == 0x9F) {
      speed_lever_s1 = 1;
    } else {
      speed_lever_s1 = 0;
    }
    if (speed_lever_s1 != 1) {
      encoder_set_enabled(false);
    }
    if (speed_lever_pos <= 125) {
      encoder_set_enabled(false);
    }
  }
}

void send_can_frame(struct zcan_frame *frame) {
  // LOG_INF("send_can_frame");
  int ret = 0;
  for (int i = 0; i < 5; i++) {
    ret = can_send(tractor.can, frame, K_MSEC(100), NULL, NULL);
    if (ret == 0) {
      // LOG_INF("Sent dummy msg");
      return 0;
    }
  }
  LOG_ERR("Sending msg id: [%u] failed, err %d", frame->id, ret);
}

void send_dummy_can_frame() {
  struct zcan_frame frame = {
      .id_type = CAN_STANDARD_IDENTIFIER,
      .rtr = CAN_DATAFRAME,
      .id = 0x8CFFFE8C,
      .dlc = 8,
      .data = {0x8C, 0xFA, 0x9F, 0x1F, 0x37, 0xFF, 0xFF, 0xFF},
  };
  // LOG_INF("send_dummy_can_frame");
  send_can_frame(&frame);
}

void dummy_rx_callback(const struct device *dev, struct zcan_frame *msg, void *arg) {
  LOG_INF("RX dummy");
  return;
}

void tractor_can_init() {
  int ret;
  if (!device_is_ready(can_reset.port)) {
    LOG_ERR("Error: CAN reset device %s is not ready \n", can_reset.port->name);
    return;
  }
  ret = gpio_pin_configure_dt(&can_reset, GPIO_OUTPUT);
  if (ret != 0) {
    LOG_ERR("Error %d: failed to configure %s pin %d\n", ret, can_reset.port->name, can_reset.pin);
    return;
  }
  gpio_pin_set(can_reset.port, can_reset.pin, 0);

  tractor.can = DEVICE_DT_GET(DT_CHOSEN(zephyr_can_primary));
  HANDLE_UNLIKELY_BOOL(device_is_ready(tractor.can), ENODEV);
  k_msgq_init(&tractor.mailbox.msgq, (char *)tractor.mailbox.buffer, sizeof(tractor.mailbox.buffer), MAILBOX_SIZE);

  const struct zcan_filter speed_wheel_filter = {
      .id_type = CAN_EXTENDED_IDENTIFIER,
      .rtr = CAN_DATAFRAME,
      .id = SPEED_WHEEL_ID,
      .rtr_mask = 0,
      .id_mask = 0x7FF,
  };

  int filter_id;
  filter_id = can_add_rx_filter(tractor.can, speed_wheel_callback, NULL, &speed_wheel_filter);
  if (filter_id < 0) {
    LOG_ERR("Could not attach CAN ISR");
  }

  const struct zcan_filter speed_lever_filter = {
      .id_type = CAN_EXTENDED_IDENTIFIER,
      .rtr = CAN_DATAFRAME,
      .id = SPEED_LEVER_ID,
      .rtr_mask = 0,
      .id_mask = 0x7FF,
  };

  filter_id = can_add_rx_filter(tractor.can, speed_lever_callback, NULL, &speed_lever_filter);
  if (filter_id < 0) {
    LOG_ERR("Could not attach CAN ISR");
  }

  // DEBUG{
  //#ifdef DEBUG_CAN
  /**
  const struct zcan_filter everything_filter = {
      .id_type = CAN_EXTENDED_IDENTIFIER, .rtr = CAN_DATAFRAME, .id = 0x0, .rtr_mask = 0, .id_mask = 0
  };
  filter_id = can_add_rx_filter(tractor.can, dummy_rx_callback, NULL, &everything_filter);
  if (filter_id < 0) {
      LOG_ERR("Could not attach CAN ISR");
  }
  */
  //#endif
  // }DEBUG

  can_set_mode(tractor.can, CAN_MODE_NORMAL);
  return 0;
}
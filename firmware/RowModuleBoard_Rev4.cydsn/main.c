/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */
#include "FreeRTOS.h"
#include "freertos_setup.h"
#include "generated/lib/drivers/nanopb/proto/row_module_board.pb.h"
#include "m_row_module_request.h"
#include "nano_dawg.h"
#include "nano_row_module.h"
#include "nano_scanner.h"
#include "project.h"
#include "queue.h"
#include "stdbool.h"
#include "stdlib.h"
#include "task.h"

void Handle_Ping(uint16_t request_id, diagnostic_Ping *request) {
  row_module_board_Reply reply = row_module_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = request_id;
  reply.which_reply = row_module_board_Reply_pong_tag;
  reply.reply.pong.x = request->x;
  Nano_Send_row_module_board_Reply(&reply);
}

void Row_Module_Reset() { CySoftwareReset(); }

void Row_Module_Clear_Config(M_Row_Module_Clear_Config_Request_t *request) {
  EPOS_MCAN_Clear_Node_Configuration();
  row_module_board_Reply reply = row_module_board_Reply_init_zero;
  reply.has_header = true;
  reply.header.requestId = request->request_id;
  reply.which_reply = row_module_board_Reply_row_module_tag;
  reply.reply.row_module.which_reply = row_module_Reply_ack_tag;
  Nano_Send_row_module_board_Reply(&reply);
}

void Row_Module_Handle_Scanner_Request(M_Row_Module_Scanner_Request_t *request) {
  switch (request->scanner_id) {
  case 1:
    Scanner_1_Send_Request(&request->scanner);
    break;
  case 2:
    Scanner_2_Send_Request(&request->scanner);
    break;
  case 3:
    Scanner_3_Send_Request(&request->scanner);
    break;
  case 4:
    Scanner_4_Send_Request(&request->scanner);
    break;
  default:
    break;
  }
}

void Row_Module_Handle_Request(M_Row_Module_Request_t *request) {
  switch (request->request_type) {
  case ROW_MODULE_REQUEST_TYPE_RESET:
    Row_Module_Reset();
    break;
  case ROW_MODULE_REQUEST_TYPE_CLEAR_CONFIG:
    Row_Module_Clear_Config(&request->request.clear);
    break;
  case ROW_MODULE_REQUEST_TYPE_DAWG:
    Dawg_Send_Request(&request->request.dawg);
    break;
  case ROW_MODULE_REQUEST_TYPE_SCANNER:
    Row_Module_Handle_Scanner_Request(&request->request.scanner);
    break;
  default:
    break;
  }
}

void Nano_Dispatch(row_module_board_Request *request) {
  M_Row_Module_Request_t row_module_request;
  Request_Metadata_t metadata;
  metadata.request_id = request->header.requestId;
  switch (request->which_request) {
  case row_module_board_Request_ping_tag:
    Handle_Ping(request->header.requestId, &request->request.ping);
    break;
  case row_module_board_Request_row_module_tag:
    Nano_Convert_Row_Module_Request(&request->request.row_module, &row_module_request, &metadata);
    Row_Module_Handle_Request(&row_module_request);
    break;
  default:
    break;
  }
}

void Send_Scanner_Reply(M_Scanner_Reply_t *reply) {
  row_module_board_Reply nano_reply = row_module_board_Reply_init_zero;
  nano_reply.has_header = true;
  nano_reply.header.requestId = reply->request_id;
  nano_reply.which_reply = row_module_board_Reply_row_module_tag;
  nano_reply.reply.row_module.which_reply = row_module_Reply_scanner_tag;
  Nano_Convert_Scanner_Reply(reply, &nano_reply.reply.row_module.reply.scanner);
  Nano_Send_row_module_board_Reply(&nano_reply);
}

M_Error_Code_t Send_Dawg_Reply(M_Dawg_Reply_t *reply) {
  if (reply->metadata.request_type != REQUEST_METADATA_TYPE_NONE) {
    return M_ERROR_CODE_INVALID_METADATA;
  }

  row_module_board_Reply nano_reply = row_module_board_Reply_init_zero;
  nano_reply.has_header = true;
  nano_reply.header.requestId = reply->metadata.request_id;
  nano_reply.which_reply = row_module_board_Reply_row_module_tag;
  nano_reply.reply.row_module.which_reply = row_module_Reply_dawg_tag;
  Nano_Convert_Dawg_Reply(reply, &nano_reply.reply.row_module.reply.dawg);
  Nano_Send_row_module_board_Reply(&nano_reply);

  return M_ERROR_CODE_OK;
}

void Boot_Task(void *unused) {
  (void)unused;

  // Startup
  Nano_boot();
  EPOS_boot(1);

  EPOS_MCAN_Clear_Node_Configuration();

  Scanner_1_Boot_Component(Send_Scanner_Reply, 1);
  Scanner_2_Boot_Component(Send_Scanner_Reply, 1);
  Scanner_3_Boot_Component(Send_Scanner_Reply, 1);
  Scanner_4_Boot_Component(Send_Scanner_Reply, 1);
  Dawg_Boot_Component(Send_Dawg_Reply, 1);

  // Finish boot sequence.
  vTaskDelete(NULL);
}

int main(void) {
  // Enable global interrupts.
  CyGlobalIntEnable;

  // Install FreeRTOS interrupts.
  setup_FreeRTOS_interrupts();

  // Schedule boot sequence.
  xTaskCreate(Boot_Task, "Boot", configMINIMAL_STACK_SIZE + 32, 0, 1, 0);

  // Start the scheduler.
  vTaskStartScheduler();
}

/* [] END OF FILE */

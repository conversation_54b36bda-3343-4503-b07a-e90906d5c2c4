
// Generated Code. Do not modify.
#include "project.h"
#include "leb128.h"
#include "stop.h"
#ifndef MAKA_row_module_command_H
#define MAKA_row_module_command_H
#pragma pack(1)
    typedef enum {
        ROW_MODULE_COMMAND_AS_SDO_DOWNLOAD_COMMAND = 1,
        ROW_MODULE_COMMAND_AS_SDO_UPLOAD_COMMAND = 2,
        ROW_MODULE_COMMAND_AS_ARM = 3,
        ROW_MODULE_COMMAND_AS_DISARM = 4,
        ROW_MODULE_COMMAND_AS_ON_COMMAND = 5,
        ROW_MODULE_COMMAND_AS_OFF_COMMAND = 6
} _tag_row_module_command;
typedef struct {
        uint8_t node_id;
        uint16_t index;
        uint8_t subindex;
        uint32_t value;
} sdo_download_command;
typedef uint16_t request_id;
typedef struct {
        uint8_t node_id;
        uint16_t index;
        uint8_t subindex;
        request_id request_id;
} sdo_upload_command;
typedef struct {
        uint8_t scanner_no;
} on_command;
typedef struct {
        uint8_t scanner_no;
} off_command;
typedef struct {
        _tag_row_module_command tag;
        union {
                sdo_download_command as_sdo_download_command;
                sdo_upload_command as_sdo_upload_command;
                on_command as_on_command;
                off_command as_off_command;
        } val;
} row_module_command;
void row_module_command_as_sdo_download_command(sdo_download_command* val, void* ctx);
void row_module_command_as_sdo_upload_command(sdo_upload_command* val, void* ctx);
void row_module_command_as_arm(void* ctx);
void row_module_command_as_disarm(void* ctx);
void row_module_command_as_on_command(on_command* val, void* ctx);
void row_module_command_as_off_command(off_command* val, void* ctx);

inline void demux_row_module_command(row_module_command *tu, void* ctx) {
    switch(tu->tag) {

        case ROW_MODULE_COMMAND_AS_ARM:
                row_module_command_as_arm(ctx);
                break;
        case ROW_MODULE_COMMAND_AS_DISARM:
                row_module_command_as_disarm(ctx);
                break;
        case ROW_MODULE_COMMAND_AS_SDO_DOWNLOAD_COMMAND:
                row_module_command_as_sdo_download_command(&(tu->val.as_sdo_download_command), ctx);
                break;
        case ROW_MODULE_COMMAND_AS_SDO_UPLOAD_COMMAND:
                row_module_command_as_sdo_upload_command(&(tu->val.as_sdo_upload_command), ctx);
                break;
        case ROW_MODULE_COMMAND_AS_ON_COMMAND:
                row_module_command_as_on_command(&(tu->val.as_on_command), ctx);
                break;
        case ROW_MODULE_COMMAND_AS_OFF_COMMAND:
                row_module_command_as_off_command(&(tu->val.as_off_command), ctx);
                break;
        default:
            stop("Unknown tag for row_module_command");
    }
}
typedef enum {
        ROW_MODULE_RESPONSE_AS_SDO_UPLOAD_RESPONSE = 1
} _tag_row_module_response;
typedef struct {
        request_id request_id;
        uint32_t value;
} sdo_upload_response;
typedef struct {
        _tag_row_module_response tag;
        union {
                sdo_upload_response as_sdo_upload_response;
        } val;
} row_module_response;
void row_module_response_as_sdo_upload_response(sdo_upload_response* val, void* ctx);

inline void demux_row_module_response(row_module_response *tu, void* ctx) {
    switch(tu->tag) {


        case ROW_MODULE_RESPONSE_AS_SDO_UPLOAD_RESPONSE:
                row_module_response_as_sdo_upload_response(&(tu->val.as_sdo_upload_response), ctx);
                break;
        default:
            stop("Unknown tag for row_module_response");
    }
}

#pragma pack()
#endif // MAKA_row_module_command_H
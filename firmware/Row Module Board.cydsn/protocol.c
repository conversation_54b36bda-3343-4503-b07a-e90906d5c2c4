/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#include "common.h"

void row_module_command_as_sdo_download_command(sdo_download_command* val, void* ctx) {
    UNUSED(ctx);
    M_CANOpen_SetObject_u32(val->node_id, val->index, val->subindex, val->value);
}
void row_module_command_as_sdo_upload_command(sdo_upload_command* val, void* ctx) {
    UNUSED(ctx);
    register_sdo_upload_request(val->request_id, val->node_id, val->index, val->subindex);
    M_CANOpen_GetObject(val->node_id, val->index, val->subindex);
}

void row_module_command_as_arm(void* ctx) {
    UNUSED(ctx);
    Arm_Write(1);
}

void row_module_command_as_disarm(void* ctx) {
    UNUSED(ctx);
    Arm_Write(0);
}

void row_module_command_as_on_command(on_command* val, void* ctx) {
    UNUSED(ctx);
    uint8_t scanner_no = val->scanner_no;
    if (scanner_no >= NUM_SCANNERS) {
        stop("Bad on %d", scanner_no);
    }
    Fire_Write(Fire_Read() | (1 << scanner_no));
}

void row_module_command_as_off_command(off_command* val, void* ctx) {
    UNUSED(ctx);
    uint8_t scanner_no = val->scanner_no;
    if (scanner_no >= NUM_SCANNERS) {
        stop("Bad off %d", scanner_no);
    }
    Fire_Write(Fire_Read() & ~(1 << scanner_no));
}

void M_ProtoUSB_Receive_Callback(uint8_t* pkt, size_t size) {
    UNUSED(size);
    demux_row_module_command((row_module_command*)pkt, NULL);
}

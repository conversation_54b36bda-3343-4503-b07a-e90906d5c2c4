/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#include "project.h"
#include "protocol.h"
#include "common.h"
#include "canopen.h"

void stop (const char* fmt, ...) {
    Fault_LED_Write(1);
    va_list ap;
    va_start(ap, fmt);
    Debug_Print("Stop \r\n");
    Debug_Printv(fmt, ap);
}

int main(void)
{
    CyGlobalIntEnable;
    Debug_Start();
    Debug_Print("Initializing Maka Row Module Board...");
    M_ProtoUSB_Start();
    M_CANOpen_Start();
    
    for(;;)
    {
        Run_LED_Write(!Run_LED_Read());
        M_ProtoUSB_Loop();
        M_CANOpen_eventq_elem eqe = M_CANOpen_NextEvent();
        while (eqe.header.typ != M_CANOpen_INPUT_EVENT_NOTHING) {
            if (eqe.header.typ == M_CANOpen_INPUT_EVENT_SDO_UPLOAD_COMPLETE) {
                M_CANOpen_SDO_upload_complete_event *ev = (M_CANOpen_SDO_upload_complete_event*)&eqe;
                int32_t request_id = register_sdo_upload_response(eqe.header.node_id, eqe.as_M_CANOpen_SDO_upload_complete_event.index, eqe.as_M_CANOpen_SDO_upload_complete_event.subindex);
                if (request_id >= 0) {
                    row_module_response resp;
                    resp.tag = ROW_MODULE_RESPONSE_AS_SDO_UPLOAD_RESPONSE;
                    resp.val.as_sdo_upload_response.request_id = request_id;
                    resp.val.as_sdo_upload_response.value = ev->data.as_u32[0];
                    M_ProtoUSB_Send((uint8_t*)&resp, sizeof(resp));
                }
            }
            eqe = M_CANOpen_NextEvent();
        }
        M_CANOpen_Loop();
    }
}

/* [] END OF FILE */

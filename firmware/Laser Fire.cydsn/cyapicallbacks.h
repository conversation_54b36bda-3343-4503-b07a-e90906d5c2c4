/* ========================================
 *
 * Copyright YOUR COMPANY, THE YEAR
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/
#ifndef CYAPICALLBACKS_H
#define CYAPICALLBACKS_H
#include "stdlib.h"
#include "stdint.h"

    /*Define your macro callbacks here */
    /*For more information, refer to the Writing Code topic in the PSoC Creator Help.*/

#define Protocol_RECEIVE_CALLBACK
void Protocol_Receive_Callback(uint8_t* pkt, size_t size);
#endif /* CYAPICALLBACKS_H */   
/* [] */

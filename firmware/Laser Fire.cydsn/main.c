/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#include "project.h"
#include "laser_fire_board_cmd.h"

void stop(char *fmt, ...) {
    // TODO this
    while(1); // Prepare for a visit from Mr. <PERSON>.
}

void laser_fire_board_cmd_as_disarm(void* ctx) {
    Arm_Write(0);
}

void laser_fire_board_cmd_as_arm(void* ctx) {
    Arm_Write(1);
}

void laser_fire_board_cmd_as_set_intensity(set_intensity* val, void* ctx) {
    switch (val->scanner_no) {
        case 0: 
            M_LaserControl_0_Set(val->intensity);
            break;
        case 1:
            M_LaserControl_1_Set(val->intensity);
            break;
        case 2:
            M_LaserControl_2_Set(val->intensity);
            break;
        case 3:
            M_LaserControl_3_Set(val->intensity);
            break;
        default:
            break;
    }
}

void laser_fire_board_cmd_as_sync_pulse(sync_pulse* val, void* ctx) {
       switch (val->scanner_no) {
        case 0: 
            M_LaserControl_0_SyncPulse(val->intensity, val->duration_ms);
            break;
        case 1:
            M_LaserControl_1_SyncPulse(val->intensity, val->duration_ms);
            break;
        case 2:
            M_LaserControl_2_SyncPulse(val->intensity, val->duration_ms);
            break;
        case 3:
            M_LaserControl_3_SyncPulse(val->intensity, val->duration_ms);
            break;
        default:
            break;
    }
}

void Protocol_Receive_Callback(uint8_t* pkt, size_t size) {
    // len isn't used here cause packet structure determines length
    demux_laser_fire_board_cmd((laser_fire_board_cmd*)pkt, NULL);
}

int main(void)
{
    CyGlobalIntEnable; /* Enable global interrupts. */
    Protocol_Start();
    M_LaserControl_0_Start();
    M_LaserControl_1_Start();
    M_LaserControl_2_Start();
    M_LaserControl_3_Start();
    Arm_Write(1);
    CyDelay(1000);
    Arm_Write(0);
    for(;;)
    {
        Protocol_Loop();
        /* Place your application code here. */
    }
}

/* [] END OF FILE */

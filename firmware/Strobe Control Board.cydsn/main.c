/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#include "project.h"
#include "FreeRTOS.h"
#include "task.h"
#include "stdbool.h"
#include "freertos_setup.h"
#include "pb_encode.h"
#include "pb_decode.h"
#include "ethernet_config.h"

#include "generated/lib/drivers/nanopb/proto/strobe_control.pb.h"
#include "generated/lib/drivers/nanopb/proto/camera_power_control.pb.h"
#include "generated/lib/drivers/nanopb/proto/strobe_control_board.pb.h"

#define STROBE_VERSION_MAJOR 1  // Increase this for major breaking changes
#define STROBE_VERSION_MINOR 0  // Increase this for minor non-breaking changes

#define EEPROM_EXPOSURE_TICKS_OFFSET            0
#define EEPROM_PERIOD_TICKS_OFFSET              2
#define EEPROM_TARGETS_PER_PREDICT_RATIO_OFFSET 4
#define EEPROM_ROW_NUMBER                       0


void Restore_Settings_EEPROM()
{
    volatile void CYXDATA* eeprom_base = (volatile void CYXDATA*)CYDEV_EE_BASE + CYDEV_EEPROM_ROW_SIZE * EEPROM_ROW_NUMBER;
    
    volatile uint16_t *exposure_ticks = (volatile uint16_t CYXDATA*)(eeprom_base + EEPROM_EXPOSURE_TICKS_OFFSET);
    Lights_PWM_WriteCompare(*exposure_ticks);

    volatile uint16_t *period_ticks = (volatile uint16_t CYXDATA*)(eeprom_base + EEPROM_PERIOD_TICKS_OFFSET);
    Lights_PWM_WritePeriod(*period_ticks);

    volatile uint8_t *targets_per_predict_ratio = (volatile uint8_t CYXDATA*)(eeprom_base + EEPROM_TARGETS_PER_PREDICT_RATIO_OFFSET);
    PredictDivider_Write(*targets_per_predict_ratio);
}

void Save_Settings_EEPROM()
{
    uint8_t buffer[16];
    memset(buffer, 0, sizeof(buffer));
    
    uint16_t exposure_ticks = Lights_PWM_ReadCompare();
    memcpy(buffer + EEPROM_EXPOSURE_TICKS_OFFSET, &exposure_ticks, sizeof(exposure_ticks));

    uint16_t period_ticks = Lights_PWM_ReadPeriod();
    memcpy(buffer + EEPROM_PERIOD_TICKS_OFFSET, &period_ticks, sizeof(period_ticks));
    
    uint8_t targets_per_predict_ratio = PredictDivider_Read();
    memcpy(buffer + EEPROM_TARGETS_PER_PREDICT_RATIO_OFFSET, &targets_per_predict_ratio, sizeof(targets_per_predict_ratio));    
    
    EEPROM_UpdateTemperature();
    EEPROM_Write(buffer, EEPROM_ROW_NUMBER);
}

void Handle_Ping(diagnostic_Ping *req, diagnostic_Pong *resp)
{
    resp->x = req->x;
}

void Handle_Version(version_Version_Reply *resp)
{   
    resp->major = STROBE_VERSION_MAJOR;
    resp->minor = STROBE_VERSION_MINOR;
}

void Handle_Strobe_Control(strobe_control_Request *req, strobe_control_Reply *resp)
{
    if (req->exposure_us >= 25 && req->exposure_us < req->period_us && req->period_us < 1000000 && 
        req->targets_per_predict_ratio >= 1 && req->targets_per_predict_ratio < 16)
    {
        // Compare value is 80 ticks per 1ms, which is 12.5us per tick.
        uint16_t exposure_ticks = req->exposure_us * 10 / 125;
        Lights_PWM_WriteCompare(exposure_ticks);
        
        // Period is 40 ticks per 1ms, and requires -1.
        uint16_t period_ticks = req->period_us / 25;
        Lights_PWM_WritePeriod(period_ticks - 1);
        
        // Set predict to target ratio.
        PredictDivider_Write(req->targets_per_predict_ratio);
        
        // Save settings to EEPROM.
        Save_Settings_EEPROM();
        
        // OK
        resp->ok = true;
    }
    else
    {
        // Not OK
        resp->ok = false;
    }
}

void Handle_Camera_Power_Control(camera_power_control_Request *req, camera_power_control_Reply *resp)
{
    if (req->camera_id < 4) {
        switch (req->camera_id) {
        case 0:
            Camera_Reset0_Write(!req->power_on);
            break;
        case 1:
            Camera_Reset1_Write(!req->power_on);
            break;
        case 2:
            Camera_Reset2_Write(!req->power_on);
            break;
        case 3:
            Camera_Reset3_Write(!req->power_on);
            break;
        }
        
        // OK
        resp->ok = true;
    }
    else
    {
        // Not OK
        resp->ok = false;
    }
}

void UDP_Task(void *unused)
{
    (void) unused;

    for (;;)
    {
        strobe_control_board_Request req = strobe_control_board_Request_init_zero;
        M_Ethernet_W5500_msg_t msg = M_Ethernet_W5500_UdpRead();

        // Decode request.
        {
            pb_istream_t stream = pb_istream_from_buffer(msg.data, msg.count);
            bool status = pb_decode(&stream, strobe_control_board_Request_fields, &req);
            M_Ethernet_W5500_Release_Msg_Data(&msg);
            if (!status)
            {
                // Decoding failed.
                continue;
            }
        }
        
        // Process request.
        strobe_control_board_Reply resp = strobe_control_board_Reply_init_zero;
        resp.has_header = true;
        resp.header.requestId = req.header.requestId;
        switch (req.which_request) {
            case strobe_control_board_Request_ping_tag:
                resp.which_reply = strobe_control_board_Reply_pong_tag;
                Handle_Ping(&req.request.ping, &resp.reply.pong);
                break;
            case strobe_control_board_Request_strobe_control_tag:
                resp.which_reply = strobe_control_board_Reply_strobe_control_tag;
                Handle_Strobe_Control(&req.request.strobe_control, &resp.reply.strobe_control);
                break;
            case strobe_control_board_Request_camera_power_control_tag:
                resp.which_reply = strobe_control_board_Reply_camera_power_control_tag;
                Handle_Camera_Power_Control(&req.request.camera_power_control, &resp.reply.camera_power_control);
                break;
            case strobe_control_board_Request_version_tag:
                resp.which_reply = strobe_control_board_Reply_version_tag;
                Handle_Version(&resp.reply.version);
                break;
            case strobe_control_board_Request_reset_tag:
                CySoftwareReset();
                break;
            default:
                // Unknown request.
                continue;
        }
        
        // Encode response.
        uint8_t buffer[32];
        {          
            pb_ostream_t stream = pb_ostream_from_buffer(buffer, sizeof(buffer));
            bool status = pb_encode(&stream, strobe_control_board_Reply_fields, &resp);
            configASSERT(status); // encoding should not fail
            msg.data = buffer;
            msg.count = stream.bytes_written;
        }
        M_Ethernet_W5500_UdpWrite(msg);
    }
}

void Boot_Task(void *unused)
{
    (void) unused;
        
    // Start PWM & counters.
    Lights_PWM_Start();

    // Read EEPROM.
    EEPROM_Start();
    Restore_Settings_EEPROM();
    
    // Configure Ethernet.
    uint8_t mac[] = BOARD_MAC(STROBE_IP_INDEX, STROBE_IP_SUB_INDEX);
    uint8_t ip[] = BOARD_IP(STROBE_IP_INDEX, STROBE_IP_SUB_INDEX);
    uint8_t netmask[] = BOARD_NETMASK;
    uint8_t gateway[] = BOARD_GATEWAY;
    size_t read_queue_len = 10;
    M_Ethernet_W5500_Boot(mac, ip, netmask, gateway, read_queue_len);
    M_Ethernet_W5500_UdpOpen(BASE_PORT);
    
    // Schedule UDP task.
    xTaskCreate(UDP_Task, "UDP", configMINIMAL_STACK_SIZE + 128, 0, 1, 0);
    
    // Finish boot sequence.
    vTaskDelete(NULL);
}

int main(void)
{
    // Enable global interrupts.
    CyGlobalIntEnable;

    // Install FreeRTOS interrupts.
    setup_FreeRTOS_interrupts();
    
    // Schedule boot sequence.
    xTaskCreate(Boot_Task, "Boot", configMINIMAL_STACK_SIZE, 0, 1, 0);

    // Start the scheduler.
    vTaskStartScheduler();
}

/* [] END OF FILE */

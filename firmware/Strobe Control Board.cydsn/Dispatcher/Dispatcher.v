
//`#start header` -- edit after this line, do not edit this line
// ========================================
//
// Copyright YOUR COMPANY, THE YEAR
// All Rights Reserved
// UNPUBLISHED, LICENSED SOFTWARE.
//
// CONFIDENTIAL AND PROPRIETARY INFORMATION
// WHICH IS THE PROPERTY OF your company.
//
// ========================================
`include "cypress.v"
//`#end` -- edit above this line, do not edit this line
// Generated on 10/09/2020 at 23:51
// Component: Dispatcher
module Dispatcher (
	output  left_predict_trigger,
	output  left_strobe,
	output  left_target_trigger,
	output  right_predict_trigger,
	output  right_strobe,
	output  right_target_trigger,
	input   clock,
	input   double_strobe,
	input   [3:0] predict_divider
);

//`#start body` -- edit after this line, do not edit this line

reg side_cnt;
reg [3:0] predict_cnt;
reg [3:0] prev_predict_divider;
reg prev_double_strobe;

always @(posedge clock)
begin
    if (double_strobe && !prev_double_strobe)
    begin
        // !double_strobe => double_strobe
        side_cnt <= !side_cnt;
        if (side_cnt == 1)
        begin
            // only count down predict on every other strobe
            if (predict_cnt != prev_predict_divider - 1)
                predict_cnt <= predict_cnt + 1;
            else
                predict_cnt <= 0;
        end
    end
    prev_double_strobe <= double_strobe;
    // this cached value is used to relax the timing constraints and meet 72mhz
    prev_predict_divider <= predict_divider;
end

assign left_predict_trigger = prev_double_strobe && !predict_cnt && (side_cnt == 0);
assign left_strobe = prev_double_strobe && (side_cnt == 0);
assign left_target_trigger = prev_double_strobe && (side_cnt == 0);
assign right_predict_trigger = prev_double_strobe && !predict_cnt && (side_cnt == 1);
assign right_strobe = prev_double_strobe && (side_cnt == 1);
assign right_target_trigger = prev_double_strobe && (side_cnt == 1);

//`#end` -- edit above this line, do not edit this line
endmodule
//`#start footer` -- edit after this line, do not edit this line
//`#end` -- edit above this line, do not edit this line

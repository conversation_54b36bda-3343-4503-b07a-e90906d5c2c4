#include <project.h>

#define CHAR_ENABLE '1'
#define CHAR_DISABLE '0'

int main() {
  char8 ch; /* Data received from the Serial port */

  CyGlobalIntEnable; /* Enable all interrupts by the processor. */

  UART_Start();

  while (1) {
    /* Check the UART status */
    ch = UART_GetChar();

    /* If byte received */
    if (ch == CHAR_ENABLE) {
      PowerControl_Write(1);
    } else if (ch == CHAR_DISABLE) {
      PowerControl_Write(0);
    }
  }
}

/* [] END OF FILE */

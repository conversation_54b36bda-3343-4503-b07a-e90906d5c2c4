﻿<?xml version="1.0" encoding="utf-8"?>
<DesignWideResources xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://cypress.com/xsd/cydwr">
  <Group key="Clock2">
    <Group key="DesigneWideClks" />
    <Group key="LocalClks">
      <Group key="c7d86769-4e96-4ef1-8422-7d9d4e64afd9">
        <Data key="domain" value="0" />
        <Data key="placement" value="AUTO" />
        <Data key="start_on_reset" value="True" />
        <Data key="user_set_domain" value="False" />
      </Group>
    </Group>
    <Group key="SourceClks" />
    <Group key="SystemClks">
      <Group key="1F7CF08C-1B36-4851-9441-035049A4211B">
        <Data key="check_tolerance" value="True" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="15" />
        <Data key="divider" value="0" />
        <Data key="domain" value="0" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="Digital Signal" />
        <Data key="netlist_name" value="Digital_Signal" />
        <Data key="placement" value="GLOBAL" />
        <Data key="plus_tolerance" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="False" />
        <Data key="sync_with_bus_clk" value="False" />
        <Data key="user_set_domain" value="False" />
      </Group>
      <Group key="39D5E4C2-EBFC-44ab-AE3D-19F9BBFD674D">
        <Data key="check_tolerance" value="True" />
        <Data key="desired_freq" value="24" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="domain" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="PLL_OUT" />
        <Data key="netlist_name" value="PLL_OUT" />
        <Data key="placement" value="GLOBAL" />
        <Data key="plus_tolerance" value="0" />
        <Data key="src_clk_id" value="CEF43CFB-0213-49b9-B980-2FFAB81C5B47" />
        <Data key="src_clk_name" value="IMO" />
        <Data key="start_on_reset" value="True" />
        <Data key="sync_with_bus_clk" value="False" />
        <Data key="user_set_domain" value="False" />
      </Group>
      <Group key="46B167E3-1786-4598-8688-AACCF18668D4">
        <Data key="check_tolerance" value="True" />
        <Data key="desired_freq" value="25" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="domain" value="0" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="XTAL" />
        <Data key="netlist_name" value="XTAL" />
        <Data key="placement" value="GLOBAL" />
        <Data key="plus_tolerance" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="False" />
        <Data key="sync_with_bus_clk" value="False" />
        <Data key="user_set_domain" value="False" />
        <Data key="xtal_automatic_gain_control" value="False" />
        <Data key="xtal_crystal_accuracy_unit" value="Pct" />
        <Data key="xtal_enable_fault_recovery" value="False" />
        <Data key="xtal_feedback_ref_level" value="3" />
        <Data key="xtal_minus_Crystal_acuracy_name" value="0" />
        <Data key="xtal_plus_crystal_acuracy_name" value="0" />
        <Data key="xtal_ref_level_customized" value="False" />
        <Data key="xtal_watchdog_ref_level" value="3" />
      </Group>
      <Group key="75C2148C-3656-4d8a-846D-0CAE99AB6FF7">
        <Data key="check_tolerance" value="True" />
        <Data key="clk_key_bus_usedivider" value="True" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="15" />
        <Data key="divider" value="1" />
        <Data key="domain" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="BUS_CLK" />
        <Data key="netlist_name" value="BUS_CLK" />
        <Data key="placement" value="GLOBAL" />
        <Data key="plus_tolerance" value="0" />
        <Data key="src_clk_id" value="61737EF6-3B74-48f9-8B91-F7473A442AE7" />
        <Data key="src_clk_name" value="MASTER_CLK" />
        <Data key="start_on_reset" value="True" />
        <Data key="sync_with_bus_clk" value="True" />
        <Data key="user_set_domain" value="False" />
      </Group>
      <Group key="61737EF6-3B74-48f9-8B91-F7473A442AE7">
        <Data key="check_tolerance" value="True" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="15" />
        <Data key="divider" value="1" />
        <Data key="domain" value="0" />
        <Data key="enabled" value="True" />
        <Data key="master_divider_used" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="MASTER_CLK" />
        <Data key="netlist_name" value="MASTER_CLK" />
        <Data key="placement" value="GLOBAL" />
        <Data key="plus_tolerance" value="0" />
        <Data key="src_clk_id" value="39D5E4C2-EBFC-44ab-AE3D-19F9BBFD674D" />
        <Data key="src_clk_name" value="PLL_OUT" />
        <Data key="start_on_reset" value="True" />
        <Data key="sync_with_bus_clk" value="True" />
        <Data key="user_set_domain" value="False" />
      </Group>
      <Group key="96816ED8-BCFA-4aad-B6AF-0E41C02E8C31">
        <Data key="check_tolerance" value="True" />
        <Data key="clk_key_usb_imox2" value="True" />
        <Data key="clk_key_usb_usedivider" value="False" />
        <Data key="desired_freq" value="48" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="1" />
        <Data key="domain" value="0" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="USB_CLK" />
        <Data key="netlist_name" value="USB_CLK" />
        <Data key="placement" value="GLOBAL" />
        <Data key="plus_tolerance" value="0" />
        <Data key="src_clk_id" value="CEF43CFB-0213-49b9-B980-2FFAB81C5B47" />
        <Data key="src_clk_name" value="IMO" />
        <Data key="start_on_reset" value="False" />
        <Data key="sync_with_bus_clk" value="False" />
        <Data key="user_set_domain" value="False" />
      </Group>
      <Group key="315365C3-2E3E-4f04-84A2-BB564A173261">
        <Data key="check_tolerance" value="True" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="15" />
        <Data key="divider" value="0" />
        <Data key="domain" value="0" />
        <Data key="enabled" value="True" />
        <Data key="ilo_1k_enabled" value="True" />
        <Data key="ilo_33k_enabled" value="False" />
        <Data key="ilo_100k_enabled" value="False" />
        <Data key="ilo_oscillator" value="ILO_1kHz" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="ILO" />
        <Data key="netlist_name" value="ILO" />
        <Data key="placement" value="GLOBAL" />
        <Data key="plus_tolerance" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="True" />
        <Data key="sync_with_bus_clk" value="False" />
        <Data key="user_set_domain" value="False" />
      </Group>
      <Group key="CEF43CFB-0213-49b9-B980-2FFAB81C5B47">
        <Data key="check_tolerance" value="True" />
        <Data key="desired_freq" value="3" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="domain" value="0" />
        <Data key="enabled" value="True" />
        <Data key="imo_doubler" value="False" />
        <Data key="imo_oscillator" value="IMO_3MHZ" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="IMO" />
        <Data key="netlist_name" value="IMO" />
        <Data key="placement" value="GLOBAL" />
        <Data key="plus_tolerance" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="True" />
        <Data key="sync_with_bus_clk" value="False" />
        <Data key="user_set_domain" value="False" />
      </Group>
      <Group key="EC9D9168-D68F-4573-AC21-F93D3BF005CD">
        <Data key="check_tolerance" value="True" />
        <Data key="desired_freq" value="32.768" />
        <Data key="desired_unit" value="3" />
        <Data key="divider" value="0" />
        <Data key="domain" value="0" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="XTAL 32kHz" />
        <Data key="netlist_name" value="XTAL_32KHZ" />
        <Data key="placement" value="GLOBAL" />
        <Data key="plus_tolerance" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="False" />
        <Data key="sync_with_bus_clk" value="False" />
        <Data key="user_set_domain" value="False" />
        <Data key="xtal_minus_Crystal_acuracy_name" value="0" />
        <Data key="xtal_plus_crystal_acuracy_name" value="0" />
      </Group>
    </Group>
  </Group>
  <Group key="Component">
    <Group key="v1">
      <Data key="cy_boot" value="cy_boot_v5_60" />
      <Data key="Em_EEPROM_Dynamic" value="Em_EEPROM_Dynamic_v2_20" />
      <Data key="LIN_Dynamic" value="LIN_Dynamic_v4_0" />
    </Group>
  </Group>
  <Group key="DWRInstGuidMapping">
    <Group key="Clock">
      <Data key="c7d86769-4e96-4ef1-8422-7d9d4e64afd9" value="Clock_1" />
    </Group>
    <Group key="Pin">
      <Data key="004a9eaa-ba03-4d0a-8bd7-e53162e72a22" value="TX" />
      <Data key="6e95e86f-84e9-4110-8cf2-9c52a4477959" value="LED" />
      <Data key="b06fc995-3035-47bf-85ed-c1bc7b9b7beb" value="RX" />
      <Data key="dcd4b771-335e-4c23-85bd-200df30cbfae" value="NeutralOut" />
      <Data key="e851a3b9-efb8-48be-bbb8-b303b216c393" value="LiveOut" />
      <Data key="fd4a83f8-4e5c-4236-8551-3a8732c9e78c/ed092b9b-d398-4703-be89-cebf998501f6" value="LCD_Char_1_LCDPort" />
    </Group>
  </Group>
  <Group key="M0S8Clock">
    <Group key="LocalClks">
      <Group key="c7d86769-4e96-4ef1-8422-7d9d4e64afd9">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="AUTO" />
        <Data key="desired_freq" value="460.8" />
        <Data key="desired_unit" value="3" />
        <Data key="divider" value="52" />
        <Data key="enabled" value="True" />
        <Data key="fract_divider_denominator" value="0" />
        <Data key="fract_divider_numerator" value="0" />
        <Data key="minus_tolerance" value="10" />
        <Data key="name" value="Clock_1" />
        <Data key="netlist_name" value="Clock_1" />
        <Data key="ph_align_clock_id" value="" />
        <Data key="ph_align_clock_name" value="" />
        <Data key="plus_tolerance" value="10" />
        <Data key="scope" value="LOCAL" />
        <Data key="sourceClk_minus_acuracy_name" value="2" />
        <Data key="sourceClk_plus_acuracy_name" value="2" />
        <Data key="src_clk_id" value="413DE2EF-D9F2-4233-A808-DFAF137FD877" />
        <Data key="src_clk_name" value="HFCLK" />
        <Data key="start_on_reset" value="True" />
        <Data key="uses_fract_divider" value="False" />
      </Group>
    </Group>
    <Group key="SystemClks">
      <Group key="1EB9C57A-B1AC-449a-92B8-1EB82F7A8FE5">
        <Data key="accuracy_display_unit" value="1" />
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="24" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="EXTCLK" />
        <Data key="netlist_name" value="EXTCLK" />
        <Data key="plus_tolerance" value="0" />
        <Data key="port_number" value="5" />
        <Data key="port_offset" value="1" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="False" />
      </Group>
      <Group key="3E3A6273-3DC0-49b9-A765-A81596A23306">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="24" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="Routed1" />
        <Data key="netlist_name" value="Routed1" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="2" />
        <Data key="sourceClk_plus_acuracy_name" value="2" />
        <Data key="src_clk_id" value="13A80296-70D5-4f89-B0D3-510602484A50" />
        <Data key="src_clk_name" value="Direct_Sel" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="5A84F528-323D-4eda-8141-58D504D70A4B">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="1" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="SYSCLK" />
        <Data key="netlist_name" value="SYSCLK" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="2" />
        <Data key="sourceClk_plus_acuracy_name" value="2" />
        <Data key="src_clk_id" value="413DE2EF-D9F2-4233-A808-DFAF137FD877" />
        <Data key="src_clk_name" value="HFCLK" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="9A908CA6-5BB3-4db0-B098-959E5D90882B">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="LFCLK" />
        <Data key="netlist_name" value="LFCLK" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="60" />
        <Data key="sourceClk_plus_acuracy_name" value="60" />
        <Data key="src_clk_id" value="D231CDB6-1E83-48bf-9FBA-0584D07512BE" />
        <Data key="src_clk_name" value="ILO" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="13A80296-70D5-4f89-B0D3-510602484A50">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="24" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="Direct_Sel" />
        <Data key="netlist_name" value="Direct_Sel" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="2" />
        <Data key="sourceClk_plus_acuracy_name" value="2" />
        <Data key="src_clk_id" value="A8C3204D-9BE8-4092-97CC-B0B247063044" />
        <Data key="src_clk_name" value="IMO" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="37D694F5-3CB0-4bfb-AEF0-963FED0CE2EC">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="DigSig4" />
        <Data key="netlist_name" value="DigSig4" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="False" />
      </Group>
      <Group key="223AFA12-A351-4b16-AC47-BAC268E7B826">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="24" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="Routed2" />
        <Data key="netlist_name" value="Routed2" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="2" />
        <Data key="sourceClk_plus_acuracy_name" value="2" />
        <Data key="src_clk_id" value="BA1FF31D-570B-4ee4-8086-5A1087FF2AD2" />
        <Data key="src_clk_name" value="DBL_Sel" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="238B4C77-851C-44d4-8040-C396F09E3440">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="32768" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="WCO" />
        <Data key="netlist_name" value="WCO" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="413DE2EF-D9F2-4233-A808-DFAF137FD877">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="24" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="1" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="HFCLK" />
        <Data key="netlist_name" value="HFCLK" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="2" />
        <Data key="sourceClk_plus_acuracy_name" value="2" />
        <Data key="src_clk_id" value="13A80296-70D5-4f89-B0D3-510602484A50" />
        <Data key="src_clk_name" value="Direct_Sel" />
        <Data key="srss_lite_divider" value="2" />
        <Data key="srss_lite_src_clk_id" value="A8C3204D-9BE8-4092-97CC-B0B247063044" />
        <Data key="srss_lite_src_clk_name" value="IMO" />
        <Data key="srss_src_clk_id" value="13A80296-70D5-4f89-B0D3-510602484A50" />
        <Data key="srss_src_clk_name" value="Direct_Sel" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="0895B506-663A-4890-93A8-F9D21970F5C1">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="DigSig2" />
        <Data key="netlist_name" value="DigSig2" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="False" />
      </Group>
      <Group key="2629E085-8588-4069-AC82-1632B7FD0079">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="DigSig3" />
        <Data key="netlist_name" value="DigSig3" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="False" />
      </Group>
      <Group key="06587E14-7ADD-4811-9EA1-A17991CA4BBA">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="24" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="Routed4" />
        <Data key="netlist_name" value="Routed4" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="2" />
        <Data key="sourceClk_plus_acuracy_name" value="2" />
        <Data key="src_clk_id" value="E08F1AEE-48D4-466e-B1C4-06141E81C5F4" />
        <Data key="src_clk_name" value="DPLL_Sel" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="25052E45-97E1-4618-A621-47712D88CDFB">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="24" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="PLL_Sel" />
        <Data key="netlist_name" value="PLL_Sel" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="2" />
        <Data key="sourceClk_plus_acuracy_name" value="2" />
        <Data key="src_clk_id" value="A8C3204D-9BE8-4092-97CC-B0B247063044" />
        <Data key="src_clk_name" value="IMO" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="A8C3204D-9BE8-4092-97CC-B0B247063044">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="24" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="IMO" />
        <Data key="netlist_name" value="IMO" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="2" />
        <Data key="sourceClk_plus_acuracy_name" value="2" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="AE29FF0C-09F4-4f1f-90F6-BE0C5AFEF171">
        <Data key="accuracy_display_unit" value="1" />
        <Data key="captrim1" value="8200" />
        <Data key="captrim2" value="14300" />
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="24" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="ECO" />
        <Data key="netlist_name" value="ECO" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="BA1FF31D-570B-4ee4-8086-5A1087FF2AD2">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="24" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="DBL_Sel" />
        <Data key="netlist_name" value="DBL_Sel" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="2" />
        <Data key="sourceClk_plus_acuracy_name" value="2" />
        <Data key="src_clk_id" value="A8C3204D-9BE8-4092-97CC-B0B247063044" />
        <Data key="src_clk_name" value="IMO" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="D231CDB6-1E83-48bf-9FBA-0584D07512BE">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="32" />
        <Data key="desired_unit" value="3" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="ILO" />
        <Data key="netlist_name" value="ILO" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="60" />
        <Data key="sourceClk_plus_acuracy_name" value="60" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="E08F1AEE-48D4-466e-B1C4-06141E81C5F4">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="24" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="DPLL_Sel" />
        <Data key="netlist_name" value="DPLL_Sel" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="2" />
        <Data key="sourceClk_plus_acuracy_name" value="2" />
        <Data key="src_clk_id" value="A8C3204D-9BE8-4092-97CC-B0B247063044" />
        <Data key="src_clk_name" value="IMO" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="EF840448-5105-4752-9F6A-7A52DA445067">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="DigSig1" />
        <Data key="netlist_name" value="DigSig1" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="0" />
        <Data key="sourceClk_plus_acuracy_name" value="0" />
        <Data key="src_clk_id" value="" />
        <Data key="src_clk_name" value="" />
        <Data key="start_on_reset" value="False" />
      </Group>
      <Group key="F0E37851-1284-42a2-B1DB-9D4E0792717A">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="0" />
        <Data key="desired_unit" value="0" />
        <Data key="divider" value="1" />
        <Data key="enabled" value="False" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="HALFSYSCLK" />
        <Data key="netlist_name" value="HALFSYSCLK" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="2" />
        <Data key="sourceClk_plus_acuracy_name" value="2" />
        <Data key="src_clk_id" value="5A84F528-323D-4eda-8141-58D504D70A4B" />
        <Data key="src_clk_name" value="SYSCLK" />
        <Data key="start_on_reset" value="True" />
      </Group>
      <Group key="F19A89AE-D2B5-4d11-9670-CE5139C8F272">
        <Data key="check_tolerance" value="False" />
        <Data key="derive_type" value="BUILTIN" />
        <Data key="desired_freq" value="24" />
        <Data key="desired_unit" value="6" />
        <Data key="divider" value="0" />
        <Data key="enabled" value="True" />
        <Data key="minus_tolerance" value="0" />
        <Data key="name" value="Routed3" />
        <Data key="netlist_name" value="Routed3" />
        <Data key="plus_tolerance" value="0" />
        <Data key="scope" value="BUILTIN" />
        <Data key="sourceClk_minus_acuracy_name" value="2" />
        <Data key="sourceClk_plus_acuracy_name" value="2" />
        <Data key="src_clk_id" value="25052E45-97E1-4618-A621-47712D88CDFB" />
        <Data key="src_clk_name" value="PLL_Sel" />
        <Data key="start_on_reset" value="True" />
      </Group>
    </Group>
  </Group>
  <Group key="Pin2">
    <Group key="004a9eaa-ba03-4d0a-8bd7-e53162e72a22">
      <Group key="0">
        <Data key="Port Format" value="12,7" />
      </Group>
    </Group>
    <Group key="6e95e86f-84e9-4110-8cf2-9c52a4477959">
      <Group key="0">
        <Data key="Port Format" value="2,1" />
      </Group>
    </Group>
    <Group key="45e6c623-2c27-44f4-8bbd-c628883cb993">
      <Group key="0">
        <Data key="Port Format" value="0,1" />
      </Group>
    </Group>
    <Group key="b06fc995-3035-47bf-85ed-c1bc7b9b7beb">
      <Group key="0">
        <Data key="Port Format" value="12,6" />
      </Group>
    </Group>
    <Group key="dcd4b771-335e-4c23-85bd-200df30cbfae">
      <Group key="0">
        <Data key="Port Format" value="1,7" />
      </Group>
    </Group>
    <Group key="e851a3b9-efb8-48be-bbb8-b303b216c393">
      <Group key="0">
        <Data key="Port Format" value="1,6" />
      </Group>
    </Group>
    <Group key="fd4a83f8-4e5c-4236-8551-3a8732c9e78c/77253644-2d29-412d-87e5-52485cd7a6d7">
      <Group key="0">
        <Data key="Port Format" value="2,0" />
      </Group>
      <Group key="1">
        <Data key="Port Format" value="2,1" />
      </Group>
      <Group key="2">
        <Data key="Port Format" value="2,2" />
      </Group>
      <Group key="3">
        <Data key="Port Format" value="2,3" />
      </Group>
      <Group key="4">
        <Data key="Port Format" value="2,4" />
      </Group>
      <Group key="5">
        <Data key="Port Format" value="2,5" />
      </Group>
      <Group key="6">
        <Data key="Port Format" value="2,6" />
      </Group>
    </Group>
    <Group key="fd4a83f8-4e5c-4236-8551-3a8732c9e78c/c02e882c-b6dd-46ba-89c8-4554f020b0c8">
      <Group key="0">
        <Data key="Port Format" value="2,0" />
      </Group>
      <Group key="1">
        <Data key="Port Format" value="2,1" />
      </Group>
      <Group key="2">
        <Data key="Port Format" value="2,2" />
      </Group>
      <Group key="3">
        <Data key="Port Format" value="2,3" />
      </Group>
      <Group key="4">
        <Data key="Port Format" value="2,4" />
      </Group>
      <Group key="5">
        <Data key="Port Format" value="2,5" />
      </Group>
      <Group key="6">
        <Data key="Port Format" value="2,6" />
      </Group>
    </Group>
    <Group key="fd4a83f8-4e5c-4236-8551-3a8732c9e78c/ed092b9b-d398-4703-be89-cebf998501f6">
      <Group key="0">
        <Data key="Port Format" value="2,0" />
      </Group>
      <Group key="1">
        <Data key="Port Format" value="2,1" />
      </Group>
      <Group key="2">
        <Data key="Port Format" value="2,2" />
      </Group>
      <Group key="3">
        <Data key="Port Format" value="2,3" />
      </Group>
      <Group key="4">
        <Data key="Port Format" value="2,4" />
      </Group>
      <Group key="5">
        <Data key="Port Format" value="2,5" />
      </Group>
      <Group key="6">
        <Data key="Port Format" value="2,6" />
      </Group>
    </Group>
  </Group>
  <Group key="System3">
    <Data key="CYDEV_BOOTLOADER_IO_COMP" value="" />
    <Data key="CYDEV_BOOTLOADER_WAIT_COMMAND" value="True" />
    <Data key="CYDEV_BOOTLOADER_WAIT_TIME" value="10" />
    <Data key="CYDEV_CONFIG_FASTBOOT_ENABLED" value="True" />
    <Data key="CYDEV_CONFIG_UNUSED_IO" value="AllowButWarn" />
    <Data key="CYDEV_CONFIGURATION_CLEAR_SRAM" value="True" />
    <Data key="CYDEV_CONFIGURATION_ECC" value="False" />
    <Data key="CYDEV_CONFIGURATION_IMOENABLED" value="False" />
    <Data key="CYDEV_CONFIGURATION_MODE" value="COMPRESSED" />
    <Data key="CYDEV_Data_CACHE_ENABLED" value="True" />
    <Data key="CYDEV_DEBUGGING_DPS" value="SWD_SWV" />
    <Data key="CYDEV_DEBUGGING_REQXRES" value="True" />
    <Data key="CYDEV_DEBUGGING_XRES" value="False" />
    <Data key="CYDEV_ECC_ENABLE" value="False" />
    <Data key="CYDEV_HEAP_SIZE" value="0x80" />
    <Data key="CYDEV_INSTRUCT_CACHE_ENABLED" value="True" />
    <Data key="CYDEV_PROTECTION_ENABLE" value="False" />
    <Data key="CYDEV_STACK_SIZE" value="0x0800" />
    <Data key="CYDEV_SWV_ENABLED" value="True" />
    <Data key="CYDEV_TEMPERATURE" value="-40C - 85/125C" />
    <Data key="CYDEV_TRACE_ENABLED" value="False" />
    <Data key="CYDEV_USE_BUNDLED_CMSIS" value="True" />
    <Data key="CYDEV_VARIABLE_VDDA" value="False" />
    <Data key="CYDEV_VDDA" value="5.5" />
    <Data key="CYDEV_VDDD" value="5.5" />
    <Data key="CYDEV_VDDIO0" value="5.5" />
    <Data key="CYDEV_VDDIO1" value="5.5" />
    <Data key="CYDEV_VDDIO2" value="5.5" />
    <Data key="CYDEV_VDDIO3" value="5.5" />
    <Data key="CYDEV_WO_NVL_ENABLED" value="False" />
  </Group>
</DesignWideResources>
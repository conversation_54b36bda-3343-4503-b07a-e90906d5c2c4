/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#ifndef `$INSTANCE_NAME`_H
#define `$INSTANCE_NAME`_H

#include "stdint.h"
#include "canopen.h"
#include "maka_endian.h"
#include "alloca.h"
typedef enum {
    `$INSTANCE_NAME`_INPUT_EVENT_NOTHING = 0, // returned by NextEvent when no new events.
    `$INSTANCE_NAME`_INPUT_EVENT_FAULT, // TODO (landon) implement this
    `$INSTANCE_NAME`_INPUT_EVENT_SDO_UPLOAD_COMPLETE, // some objdict entry we asked for returned
    `$INSTANCE_NAME`_INPUT_EVENT_SDO_ABORT, // either a set or get failed. (also happens after device reset)
    // TODO other events go here. eg target position reached
} `$INSTANCE_NAME`_input_event_type;

typedef struct {
    uint8_t node_id;
    `$INSTANCE_NAME`_input_event_type typ;
} `$INSTANCE_NAME`_input_event_header;

typedef struct {
    `$INSTANCE_NAME`_input_event_header header;
    uint16_t index;
    uint8_t subindex;
    union { // what this is depends on the object
        uint8_t as_u8[4];
        uint16_t as_u16[2];
        uint32_t as_u32[1];
        int8_t as_s8[4];
        int16_t as_s16[2];
        int32_t as_s32[1];
    } data;
} `$INSTANCE_NAME`_SDO_upload_complete_event;

typedef struct {
    `$INSTANCE_NAME`_input_event_header header;
    uint16_t index;
    uint8_t subindex;
    uint32_t abort_code;
} `$INSTANCE_NAME`_SDO_abort_event;
// This is what goes in eventq
typedef union {
    `$INSTANCE_NAME`_input_event_header header;
    `$INSTANCE_NAME`_SDO_upload_complete_event as_`$INSTANCE_NAME`_SDO_upload_complete_event;
} `$INSTANCE_NAME`_eventq_elem;

void `$INSTANCE_NAME`_Start();
void `$INSTANCE_NAME`_Loop();
`$INSTANCE_NAME`_eventq_elem `$INSTANCE_NAME`_NextEvent();
void `$INSTANCE_NAME`_GetObject(uint8_t node_id, uint16_t index, uint8_t subindex);
void `$INSTANCE_NAME`_SetObject_u32(uint8_t node_id, uint16_t index, uint8_t subindex, uint32_t data);
inline void `$INSTANCE_NAME`_SetObject_s32(uint8_t node_id, uint16_t index, uint8_t subindex, int32_t data) {
    `$INSTANCE_NAME`_SetObject_u32(node_id, index, subindex, (uint32_t)data);
}
inline void `$INSTANCE_NAME`_SetObject_u16(uint8_t node_id, uint16_t index, uint8_t subindex, uint16_t data) {
    `$INSTANCE_NAME`_SetObject_u32(node_id, index, subindex, (uint32_t)htobe16(data) << 16);
}
inline void `$INSTANCE_NAME`_SetObject_s16(uint8_t node_id, uint16_t index, uint8_t subindex, int16_t data) {
    `$INSTANCE_NAME`_SetObject_u32(node_id, index, subindex, (uint32_t)htobe16(data) << 16);
}
inline void `$INSTANCE_NAME`_SetObject_u8(uint8_t node_id, uint16_t index, uint8_t subindex, uint8_t data) {
    `$INSTANCE_NAME`_SetObject_u32(node_id, index, subindex, (uint32_t)(data) << 24);
}
inline void `$INSTANCE_NAME`_SetObject_s8(uint8_t node_id, uint16_t index, uint8_t subindex, int8_t data) {
    `$INSTANCE_NAME`_SetObject_u32(node_id, index, subindex, (int32_t)(data) << 24);
}

#endif // `$INSTANCE_NAME`_H

/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#include "maka_endian.h"
#include "ringq.h"
#include "stdint.h"
#include "stdbool.h"
#include "project.h"

#pragma pack(1)

typedef struct { 
    uint8_t cs; // command specifier
    uint16_t index;
    uint8_t subindex;
    uint32_t data;
} pkt_overlay; // THis is what you get _after_ you take the raw packet and endian swap it as two uint32s.
#pragma pack(0)
_Static_assert(sizeof(pkt_overlay) == CANOPEN_DATA_LENGTH, "No");
typedef enum {
    CANOPEN_IDLE,
    CANOPEN_SDO_UPLOAD_REQUESTED,
    CANOPEN_SDO_UPLOAD_RECEIVED,
    CANOPEN_SDO_EXP_DOWNLOAD_SENT,
    CANOPEN_SDO_EXP_DOWNLOAD_ACKNOWLEDGED,
    CANOPEN_SDO_ABORTED,
    // Currently we only support expedited download and upload
    // TODO as we support more protocols and services (as defined in CIA-301 section 7.2)
    // this state machine will get more complex
} canopen_proto_state;

typedef struct {
    canopen_proto_state state;
    ringq sendq;
} canopen_proto;

typedef struct {
    uint8_t node_id;
    uint8_t func;
    pkt_overlay pkt;
} sendq_elem;
 
// node 0 is not valid, but we give it a slot cause +1 here is easier to not mess up than -1 everywhere else,
// which seems easy to forget and annoying to debug.
static volatile canopen_proto canopen_protos[`$MaxNodeID`+1];
static volatile uint8_t sendq_bufs[`$MaxNodeID`+1][`$SendQueueDepth` * sizeof(sendq_elem)];
static volatile ringq eventq;
static volatile uint8_t eventq_buf[`$EventQueueDepth` * sizeof(`$INSTANCE_NAME`_eventq_elem)];
static struct {
    size_t packets_recvd;
    size_t other_sender_packets; // packets send on RX channels by not us. Idealy shouldn't happen.
    size_t packets_sent;
    size_t nmt_packets;
    size_t unknown_packets;
    size_t downloaded_packets; // these count success responses
    size_t uploaded_packets;
    size_t aborted_packets;
    size_t sendq_dropped; // dropped cause per-node queue full
    size_t send_dropped; // dropped by send to wire
    size_t eventq_dropped; // dropped on the way into the input event queue
} stats;

// TODO (landon) Currently only mailbox 0 is configured.
// If we want a bigger input buffer we'll need more logic to figure out which mailbox is ready.
#define MAILBOX 0

// This is the main CANOpen state machine entry point. 
// This function may call itself, but not such that a recursive cycle is created. 
// In general, the interrupt handler will call it when packets are recieved,
// and the loop handler will call it when packets are sent.
// Note that enqueing or receiving a packet SHOULD NOT result in a packet immediately being
//   sent (e.x. `$CANInstanceName`_SendMsg()). That only happens in Loop().
//   This is not a technical limitation AFAIK, it is a convention to keep synchronization simple 
// Thusly, recieving a packet should _not_ result in immediately sending a packet.
// The standard usage pattern is to use `$INSTANCE_NAME`_[Set|Get]Object to either request or set objdict data.
// then every loop, drain the event queue with `$INSTANCE_NAME`_NextEvent().
// There's one input queue and multiple output queues so we can round robin schedule outputs.


static void canopen_proto_set_state(uint8_t node_id, canopen_proto_state state) {
    switch(state) {
        case CANOPEN_SDO_UPLOAD_RECEIVED: ({
            `$INSTANCE_NAME`_SDO_upload_complete_event ev;
            uint32_t* raw_pkt = (uint32_t*)`$CANInstanceName`_RX[MAILBOX].rxdata.byte;
            raw_pkt[0] = be32toh(raw_pkt[0]);
            raw_pkt[1] = be32toh(raw_pkt[1]);
            pkt_overlay *pkt = (pkt_overlay*)raw_pkt;
            ev.header.node_id = node_id;
            ev.header.typ =  `$INSTANCE_NAME`_INPUT_EVENT_SDO_UPLOAD_COMPLETE;
            ev.index = pkt->index;
            ev.subindex = pkt->subindex;
            ev.data.as_u32[0] = pkt->data;
            uint8_t saved_intstatus = CyEnterCriticalSection();
            if (!RINGQ_FULL(eventq)) {
                RINGQ_PUT(eventq, ev);
            } else {
                stats.eventq_dropped++;
            }
            canopen_proto_set_state(node_id, CANOPEN_IDLE);
            CyExitCriticalSection(saved_intstatus);
        }); break;
        case CANOPEN_SDO_EXP_DOWNLOAD_ACKNOWLEDGED: 
            canopen_proto_set_state(node_id, CANOPEN_IDLE);
            break;
        case CANOPEN_SDO_ABORTED:({
            `$INSTANCE_NAME`_SDO_abort_event ev;
            uint32_t* raw_pkt = (uint32_t*)`$CANInstanceName`_RX[MAILBOX].rxdata.byte;
            raw_pkt[0] = be32toh(raw_pkt[0]);
            raw_pkt[1] = be32toh(raw_pkt[1]);
            pkt_overlay *pkt = (pkt_overlay*)raw_pkt;
            ev.header.node_id = node_id;
            ev.header.typ =  `$INSTANCE_NAME`_INPUT_EVENT_SDO_ABORT;
            ev.index = pkt->index;
            ev.subindex = pkt->subindex;
            ev.abort_code = pkt->data;
            uint8_t saved_intstatus = CyEnterCriticalSection();
            if (!RINGQ_FULL(eventq)) {
                RINGQ_PUT(eventq, ev);
            } else {
                stats.eventq_dropped++;
            }
            canopen_proto_set_state(node_id, CANOPEN_IDLE);
            CyExitCriticalSection(saved_intstatus);
        }); break;
        case CANOPEN_SDO_EXP_DOWNLOAD_SENT:
        case CANOPEN_SDO_UPLOAD_REQUESTED:
        case CANOPEN_IDLE: ({
            // This is a single set so we don't need a critical section
            // We also happen to know that these states are set from send_next
            // from a critical section.
            canopen_protos[node_id].state = state;
        });
        break;
        default:
            stop("bad state %d:%d", node_id, state);
            break;
    }
}

// Called by `$INSTANCE_NAME`_Loop() if this node is idle.
// We change states even if the send fails.
// Returns true if there is more stuff to send
static bool canopen_proto_send_next(uint8_t node_id) {
    uint8_t saved_intstatus = CyEnterCriticalSection();
    static `$CANInstanceName`_DATA_BYTES_MSG msgbuf[CANOPEN_DATA_LENGTH];
    bool more = false;
    if (!RINGQ_EMPTY(canopen_protos[node_id].sendq)) {
        sendq_elem sqe = RINGQ_GET(canopen_protos[node_id].sendq, sendq_elem);
        if (CANOPEN_NODE_ID(sqe.node_id) != node_id) {
            stop("bad sqe %d != %d", sqe.node_id, node_id);   
        }
        `$CANInstanceName`_TX_MSG message;
        message.id = CANOPEN_ID_CONSTRUCT(sqe.node_id, sqe.func);
        memset(msgbuf, 0, CANOPEN_DATA_LENGTH);
        message.msg = msgbuf;
        message.rtr = 0;
        message.ide = 0;
        message.dlc = CANOPEN_DATA_LENGTH;
        message.irq = true; // what does this do?
        memcpy(message.msg->byte, (uint32*)&sqe.pkt, sizeof(sqe.pkt));
        if (CANOPEN_FUNC(message.id) == CANOPEN_SDO_RX_FUNC) {
            if (CANOPEN_SDO_SPEC_CS(sqe.pkt.cs) == CANOPEN_SDO_DL_REQ && CANOPEN_SDO_SPEC_XLOAD_E(sqe.pkt.cs)) {
                canopen_proto_set_state(node_id, CANOPEN_SDO_EXP_DOWNLOAD_SENT);
            } else if (CANOPEN_SDO_SPEC_CS(sqe.pkt.cs) == CANOPEN_SDO_UL) {
                canopen_proto_set_state(node_id, CANOPEN_SDO_UPLOAD_REQUESTED);
            } else {
                // Normally we ignore errors, this is a stop cause it means we're _sending_ something we don't understand. That's bad.
                stop("sqr %02d %0x08x", node_id, *((uint32_t*)&sqe.pkt));
            }
        } else {
            // ibid
            stop("sq? %02d %0x08x", node_id, *((uint32_t*)&sqe.pkt));
        }
        if (!(`$CANInstanceName`_SendMsg(&message) == CYRET_SUCCESS)) {
            stats.send_dropped++;
        }
    } else {
        canopen_proto_set_state(node_id, CANOPEN_IDLE);
    }
    more = !RINGQ_EMPTY(canopen_protos[node_id].sendq);
    CyExitCriticalSection(saved_intstatus);
    return more;
}

static void recv_isr() {
    stats.packets_recvd++;
    uint8_t node_id = CANOPEN_NODE_ID(`$CANInstanceName`_GET_RX_ID(MAILBOX));
    switch(CANOPEN_FUNC(`$CANInstanceName`_GET_RX_ID(MAILBOX))) {
        case CANOPEN_SDO_RX_FUNC: // We don't currently do anything about other people trying to talk to the thing we are talking to.
            stats.other_sender_packets++;
            break;
        case CANOPEN_SDO_TX_FUNC: {
            uint8_t spec = (`$CANInstanceName`_RX_DATA_BYTE(MAILBOX, 0));
            uint8_t expedited = CANOPEN_SDO_SPEC_XLOAD_E(spec);
            uint8_t cs = CANOPEN_SDO_SPEC_CS(spec);
            if (cs == CANOPEN_SDO_DL_RESP) {
                // We don't check the multiplexer field here, so if something else is on the bus, we'll
                // generate events for SDOs we didn't ask for (it did) and confuse the state machine.
                canopen_proto_set_state(node_id, CANOPEN_SDO_EXP_DOWNLOAD_ACKNOWLEDGED);
                stats.downloaded_packets++;
            } else if (cs == CANOPEN_SDO_UL && expedited) {
                canopen_proto_set_state(node_id, CANOPEN_SDO_UPLOAD_RECEIVED);
                stats.uploaded_packets++;
            } else if (cs == CANOPEN_SDO_ABORTED) {
                canopen_proto_set_state(node_id, CANOPEN_SDO_ABORT);
                stats.aborted_packets++;
            } else {
                stats.unknown_packets++;
            } // TODO (landon) support for segmented transfers would go here if we need it.
        }; break;
        case CANOPEN_NMT_FUNC: { // We get these when nodes come online (i.e. after reset)
            stats.nmt_packets++;
            if (`$CANInstanceName`_GET_DLC(MAILBOX) == 1 && `$CANInstanceName`_RX_DATA_BYTE(MAILBOX, 0) == 0) {
                canopen_proto_set_state(node_id, CANOPEN_IDLE);  
            }  
        }; break;
        default:
            stats.unknown_packets++;
            break;
    }
    // I _think_ this at the end prevents the ISR from double-firing and the peripheral from overwriting the message data pointer while we're running.
    // It definitely has nothing to do with whether or not we send an ACK bit on the can bus (we do regardless, can specifies this)
    `$CANInstanceName`_RX_ACK_MESSAGE(MAILBOX);
}

static void send_isr() {
    stats.packets_sent++;
}

static CY_ISR(`$INSTANCE_NAME`_ISR) {
    // Multiplex tx/rx isrs and clear the interrupt pending bit
    if (`$CANInstanceName`_INT_SR_REG.byte[1u] & `$CANInstanceName`_TX_MESSAGE_MASK) {
        send_isr();
        `$CANInstanceName`_INT_SR_REG.byte[1u] = `$CANInstanceName`_TX_MESSAGE_MASK;
    } else if (`$CANInstanceName`_INT_SR_REG.byte[1u] & `$CANInstanceName`_RX_MESSAGE_MASK) {
        recv_isr();
        `$CANInstanceName`_INT_SR_REG.byte[1u] = `$CANInstanceName`_RX_MESSAGE_MASK;
    }
}

void `$INSTANCE_NAME`_Start() {
    for (size_t node_id = 1; node_id <= `$MaxNodeID`; node_id++) {
        RINGQ_INIT((canopen_protos[node_id].sendq), sendq_bufs[node_id], `$SendQueueDepth`, sendq_elem);
    }
    RINGQ_INIT(eventq, eventq_buf, `$EventQueueDepth`, `$INSTANCE_NAME`_eventq_elem);
    `$CANInstanceName`_Start();
    CyIntSetVector(`$CANInstanceName`_ISR_NUMBER, `$INSTANCE_NAME`_ISR);
}

void `$INSTANCE_NAME`_Loop() {
    // We send messages out round-robin accoding to node ID. This means actions will be
    // roughly synchronized if the bus gets busy.
    
    bool more = false;
    do {
        more = false;
        for (uint8_t node_id = 1; node_id <= `$MaxNodeID`; node_id++) {
            if (canopen_protos[node_id].state == CANOPEN_IDLE) {
                more = more || canopen_proto_send_next(node_id);
            }
        }
    } while (more);
}
static void put_sqe(uint8_t node_id, uint8_t func, uint8_t cs, uint16_t index, uint8_t subindex, uint32_t data) {
    sendq_elem sqe;
    sqe.node_id = node_id;
    sqe.func = func;
    sqe.pkt.cs = cs;
    sqe.pkt.index = index;
    sqe.pkt.subindex = subindex;
    sqe.pkt.data = data;
    uint8_t saved_intstatus = CyEnterCriticalSection();
    if (!RINGQ_FULL(canopen_protos[node_id].sendq)) {
        RINGQ_PUT(canopen_protos[node_id].sendq, sqe);
    } else {
        stats.sendq_dropped++;
    }
    CyExitCriticalSection(saved_intstatus);
}

void `$INSTANCE_NAME`_SetObject_u32(uint8_t node_id, uint16_t index, uint8_t subindex, uint32_t data) {
    put_sqe(node_id, CANOPEN_SDO_RX_FUNC, CANOPEN_SDO_SPEC_XLOAD_CONSTRUCT(CANOPEN_SDO_DL_REQ, 0, 0, 1, 0), index, subindex, data);
}

void `$INSTANCE_NAME`_GetObject(uint8_t node_id, uint16_t index, uint8_t subindex) {
    put_sqe(node_id, CANOPEN_SDO_RX_FUNC, CANOPEN_SDO_SPEC_XLOAD_CONSTRUCT(CANOPEN_SDO_UL, 0, 0, 0, 0), index, subindex, 0);
}

`$INSTANCE_NAME`_eventq_elem `$INSTANCE_NAME`_NextEvent() {
    `$INSTANCE_NAME`_eventq_elem ev;
    ev.header.typ = `$INSTANCE_NAME`_INPUT_EVENT_NOTHING;
    uint8_t saved_intstatus = CyEnterCriticalSection();
    if (!RINGQ_EMPTY(eventq)) {
        ev = RINGQ_GET(eventq, `$INSTANCE_NAME`_eventq_elem);
    }
    CyExitCriticalSection(saved_intstatus);
    return ev;
}

/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/

#include "project.h"
#include "stdint.h"
#include "stop.h"
#include "maka_endian.h"

#define DAC6573_ADDR 0b1001100
#define NUM_CHANNELS 4
#pragma pack(1)
typedef struct {
    uint8_t control; 
    uint16_t data;
} dac7678_cmd;
#pragma pack(0)

// Converts endianness. (PSoC5 is little, the DAC wants big)
// eaddr is the last 2 bits of the address pins, which can further multiplex.
// We only use eaddr=0 right now, but I like APIs that match datasheets.
static void sync_send(uint8_t channel_no, uint8_t eaddr, uint16_t data) {
    static dac7678_cmd cmd;

    cmd.control = ((eaddr & 0x3) << 6) | ((channel_no & 0x3) << 1) | 0b00010000; // see table 1 in the datasheet
    cmd.data = htobe16(data); // DAC expects big endian, and ignores the lower 6 bits, so no shift necessary
    `$I2cMasterInstanceName`_MasterClearStatus();
    `$I2cMasterInstanceName`_MasterWriteBuf(DAC6573_ADDR, (uint8_t*)&cmd, sizeof(cmd), `$I2cMasterInstanceName`_MODE_COMPLETE_XFER);
    uint8_t mstat;
    do {
        mstat = `$I2cMasterInstanceName`_MasterStatus();
    } while(!(mstat & `$I2cMasterInstanceName`_MSTAT_WR_CMPLT));
    if (mstat & (`$I2cMasterInstanceName`_MSTAT_ERR_XFER)) {
        stop("no");
    }
}

void `$INSTANCE_NAME`_SetChannelValue(uint8_t channel_no, uint16_t value) {
    if (channel_no >= NUM_CHANNELS) {
        stop("bad dac6573 ch: %d", channel_no);
    }
    sync_send(channel_no, 0, value); // Set input and update dac register n. 
}

void `$INSTANCE_NAME`_Start() {
    `$I2cMasterInstanceName`_Start();
    for (int channel_no = 0; channel_no < NUM_CHANNELS; channel_no++) { // each one has 4 channels.
        sync_send(channel_no, 0, 0);
    }
}
/* [] END OF FILE */

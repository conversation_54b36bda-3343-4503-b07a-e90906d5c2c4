/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROP<PERSON>ETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/

#ifndef `$INSTANCE_NAME`_H
#define `$INSTANCE_NAME`_H

void `$INSTANCE_NAME`_Start();

// The DAC is actually 10 bits, we interpolate by shifting the input right.
// TODO (landon) currently this is synchronous on the I2C bus. That's not fantastic.
void `$INSTANCE_NAME`_SetChannelValue(uint8_t channel_no, uint16_t value);

#endif // `$INSTANCE_NAME`_H
/* [] END OF FILE */

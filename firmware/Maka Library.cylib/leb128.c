/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#include "stdint.h"
#include "stdlib.h"
#include "stop.h"

size_t leb128_decode(uint8_t* buf, size_t* out) {
    uint8_t shift = 0;
    *out = 0;
    size_t count = 0;
    while (1) {
        *out |= ((*buf & 0x7F) << shift);
        count += 1;
        if (!(*buf & 0x80)) {
            break;
        }
        shift += 7;
        buf += 1;
        if (count > 4) {
            stop("Bad LEB128 (decode)");
        }
    }
    return count;
}

size_t leb128_encode(uint8_t* buf, size_t val) {
    size_t count = 0;
    while (val > 0) {
        buf[count] = val & 0x7F;
        val = val >> 7;
        if (val > 0) {
            buf[count] |= 0x80;
        }
        count++;
        if (count > 4) {
            stop("Bad LEB128 (encode)");
        }
    }
    return count;
}
/* [] END OF FILE */

/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#pragma once

#include "stdbool.h"
#include "stdint.h"
// Insert a request for node, index, subindex into the RPC table. returns true if successful
bool register_sdo_upload_request(uint16_t request_id, uint8_t node_id, uint16_t index, uint8_t subindex);
// Find the RPC request associated with the node, index, subindex. Returns -1 if none exists.
int32_t register_sdo_upload_response(uint8_t node_id, uint16_t index, uint8_t subindex);
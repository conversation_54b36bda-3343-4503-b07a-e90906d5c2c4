/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#include "sdo_rpc.h"

#define REQUEST_TABLE_SIZE 32
static struct {
    uint8_t node_id; // nodeid 0 isn't valid and means this entry isn't in use
    uint8_t subindex; 
    uint16_t request_id;
    uint16_t index;
} sdo_upload_requests[REQUEST_TABLE_SIZE];


int32_t register_sdo_upload_response(uint8_t node_id, uint16_t index, uint8_t subindex) {
    for (uint8_t id = 0; id < REQUEST_TABLE_SIZE; id++) {
        if (sdo_upload_requests[id].node_id == node_id && 
            sdo_upload_requests[id].index == index && 
            sdo_upload_requests[id].subindex == subindex) {
            sdo_upload_requests[id].node_id = 0;
            return sdo_upload_requests[id].request_id;
        }
    }
    return -1;
}


bool register_sdo_upload_request(uint16_t request_id, uint8_t node_id, uint16_t index, uint8_t subindex) {
    for (uint8_t id = 0; id < REQUEST_TABLE_SIZE; id++) {
    	if (sdo_upload_requests[id].node_id == 0) {
            sdo_upload_requests[id].request_id = request_id;
            sdo_upload_requests[id].node_id = node_id;
            sdo_upload_requests[id].index = index;
            sdo_upload_requests[id].subindex = subindex;
            return true;
    	}
    }
    return false;
}

/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/

// Single header statically allocated ringq for small structs that can be passed on the stack.
// Uses some GCC extensions to avoid typing more.
#ifndef RINGQ_H
#define RINGQ_H

#include "stdint.h"
#include "stdbool.h"
#include "stop.h"

typedef struct {
    uint16_t head; // Next index to be PUT'd (points to am empty slot unless queue empty)
    uint16_t tail; // Next index to be GET'd (points to a full slot unless queue full)
    bool full; // true iff head == tail because the queue is full
    uint16_t elems; // number of elements
    uint8_t esize;
    uint8_t *data;
} ringq;
// Synchronization (i.e. critical sectioning) is the caller's problem.
// A RINGQ_POP from an empty queue or a PUSH to a full one are fatal.
// At some point that may be optimized into UB (by eliding checks)
// so check them in the same critical section you're mutating them in.

// This also passes elements around on the stack, so it's not appropriate for
// large objects. It was implemented with event queues and CAN packets in mind.
#define RINGQ_EMPTY(rqin) ({ \
    volatile ringq* rq = &rqin; \
    (rq->head == rq->tail && !rq->full); \
}) 

#define RINGQ_FULL(rqin) ({ \
    volatile ringq* rq = &(rqin); \
    (rq->full); \
})

#define RINGQ_GET(rqin, typ) ({ \
    volatile ringq* rq = &rqin; \
    typ ret; \
    if (RINGQ_EMPTY(rqin) || rq->esize != sizeof(typ)) { \
        stop("bad ringq get"); \
    } \
    ret = ((typ*)(rq->data))[rq->tail]; \
    rq->tail = (rq->tail + 1) % rq->elems; \
    rq->full = false; \
    ret; \
})

#define RINGQ_PUT(rqin, itm) ({ \
    volatile ringq* rq = &(rqin); \
    if (RINGQ_FULL(rqin) || rq->esize != sizeof(itm)) { \
        stop("bad ringq put"); \
    } \
    ((typeof(itm)*)(rq->data))[rq->head] = itm; \
    rq->head = (rq->head + 1) % rq->elems; \
    rq->full = (rq->head == rq->tail); \
})

// Initialize a rinq rq using backing store data
// rq - a pointer to a ringq struct
// data - an array of the things being rin buffered
// elems - number of 

// typeof(&(datain[0])) thing is to turn buffers into pointers.
// if datain is a uint8[1
#define RINGQ_INIT(rqin, datain, elemsin, typ) ({ \
    volatile ringq *rq = &(rqin); \
    uint16_t elems = (elemsin); \
    typeof(&(datain[0])) data = (datain); \
    memset((uint8_t*)data, 0, sizeof(*data) * elems); \
    rq->elems = elems; \
    rq->esize = sizeof(typ); \
    rq->data = (uint8_t*)data; \
})

#endif // RINGQ_H
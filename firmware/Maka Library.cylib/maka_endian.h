/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/

// endian.h is a GNU/BSD header file that does endianness transformations that the PSoC library doesn't have.
// TODO: This file is incomplete, I only filled out the ones I've needed.
// Add implementations of further functions as needed (and therefore tested)
// Please stick to the function names and prototypes in gnu libc's endian.h (google 'man endian.h').
#ifndef MAKA_ENDIAN_H
#define MAKA_ENDIAN_H
#include "stdint.h"
    
inline uint32_t bswap_32(uint32_t val) {
    val = ((val << 8) & 0xFF00FF00) | ((val >> 8) & 0x00FF00FF); 
    return (val << 16) | (val >> 16);
}

inline uint16_t bswap_16(uint16_t val) {
    return (val << 8) | (val >> 8);
}

// Little Endian
#if(CY_PSOC5LP)
#define htobe16(x) bswap_16(x)
#define htole16(x) (x)
#define be16toh(x) bswap_16(x)
#define le16toh(x) (x)
#define htobe32(x) bswap_32(x)
#define htole32(x) (x)
#define be32toh(x) bswap_32(x)
#define le32toh(x) (x)
#endif

#endif // MAKA_ENDIAN_H
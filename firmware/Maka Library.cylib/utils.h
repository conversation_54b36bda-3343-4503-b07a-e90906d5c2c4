/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
 *
 * This file contains helper utilities and general purpose functions
 * that we should reuse throughout the codebase.
 */

#ifndef UTILS_H
#define UTILS_H

/**
 * Helpful macro for annotating within a function that a parameter is unused.
 */
#define UNUSED(x) (void)(x)
    
#define RETURN_FALSE_IF_NOT(expr) if (!(expr)) {return false; };

#endif

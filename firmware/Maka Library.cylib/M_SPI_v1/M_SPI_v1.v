//`#start header` -- edit after this line, do not edit this line
// ========================================
//
// Copyright Maka ARS, 2020
// All Rights Reserved
// UNPUBLISHED, LICENSED SOFTWARE.
//
// CONFIDENTIAL AND PROPRIETARY INFORMATION
// WHICH IS THE PROPERTY OF Maka ARS.
//
// ========================================
`include "cypress.v"
//`#end` -- edit above this line, do not edit this line
// Generated on 07/28/2020 at 00:18
// Component: M_SPI_v1
module M_SPI_v1 (
	output [7:0] DATA_OUT_0,
	output [7:0] DATA_OUT_1,
	output [7:0] DATA_OUT_2,
	output [7:0] DATA_OUT_3,
	output reg   MOSI,
	output reg   SCLK,
	output reg   XFER_DONE,
	input        CLK,
	input [7:0]  DATA_IN_0,
	input [7:0]  DATA_IN_1,
	input [7:0]  DATA_IN_2,
	input [7:0]  DATA_IN_3,
	input [5:0]  INPUT_RDY,
	input        MISO
);

//`#start body` -- edit after this line, do not edit this line
reg [31:0] data;
reg [5:0] pos;
reg input_rdy_nonzero;

// States:
//   00 - wait for input
//   01 - transmitting: neg SCLK
//   10 - transmitting: pos SCLK
//   11 - waiting for INPUT_RDY = 0
reg [1:0] state;

// This trick improves timing of this circuit.
always @ (posedge CLK)
begin
    if (INPUT_RDY)
        input_rdy_nonzero <= 1'b1;
    else
        input_rdy_nonzero <= 1'b0;
end

always @ (negedge CLK)
begin
    case (state)
        2'b00:
        begin
            if (input_rdy_nonzero)
            begin
                pos <= INPUT_RDY;
                XFER_DONE <= 1'b0;
                MOSI <= DATA_IN_0[7];
                data <= {DATA_IN_0, DATA_IN_1, DATA_IN_2, DATA_IN_3};
                state <= 2'b01;
            end
        end

        2'b01:
        begin
            pos <= pos - 1;
            data <= (data << 1'b1) | MISO;
            SCLK <= ~SCLK;
            state <= 2'b10;
        end
        
        2'b10:
        begin
            if (pos)
            begin
                MOSI <= data[31];
                state <= 2'b01;
            end else begin
                MOSI <= 1'b0;
                XFER_DONE <= 1'b1;
                state <= 2'b11;
            end
            SCLK <= ~SCLK;
        end
        
        2'b11:
        begin
            if (~input_rdy_nonzero)
            begin
                XFER_DONE <= 1'b0;
                state <= 2'b00;
            end
        end
    endcase
end

assign DATA_OUT_0 = data[31:24];
assign DATA_OUT_1 = data[23:16];
assign DATA_OUT_2 = data[15:8];
assign DATA_OUT_3 = data[7:0];

//`#end` -- edit above this line, do not edit this line
endmodule
//`#start footer` -- edit after this line, do not edit this line
//`#end` -- edit above this line, do not edit this line

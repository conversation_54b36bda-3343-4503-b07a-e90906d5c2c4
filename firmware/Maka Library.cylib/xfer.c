/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#include "stdint.h"
#include "string.h"
#include "stdbool.h"
#include "stop.h"
#include "xfer.h"
#include "bitvec.h"

/*
8 bit unit-square transfer function operations and 16 bit interpolations
An xfer is 257 samples, 8 bits each, plus a carry
The size is a compromise between detail and ram usage.
16 bits is because the CPU is 32 bit, and we want extra bits for renormalizing multiplication and carries.
With 256 samples, it's impossible to create an identity transfer that's not distorted*.

Carries show up here cause without them, there's awkward distortion that makes it impossible to create an identity function
that makes sense*.
Given that all the math is 32 bit anyway and ram is not _absurdly_ scarce, we use 9 bits to store the sample value, 
but the 9th can only serve as a carry. i.e. 0x101-0x1FF are not valid sample values, 
if the carry bit is set for a sample, the value is ignored.
In memory, this is a vector of 8 bit samples, and a bitvector of carry bits.

*rationale:
To avoid loss of information due to integer division, we have to only divide and multiply by 2 (i.e. shift)
Each interpolation operation chooses an interpolation bin by its higher order byte, and a position in that bin by its lower bits
Any given input value must have a sample to its left and its right 
    i.e. no input value can land 'off the edge' of the sample table, that would require returning nonsense.
By the pidgeon hole principle, there must be 256 interpolation bins, and thusly 257 samples (samples being fenceposts for bins)

+-+-+...+-+
|*|*|...|*|
+-+-+...+-+
|*|*|...|*|
+-+-+...+-+
.
.
.
+-+-+...+-+
|*|*|...|*|
+-+-+...+-+

* are bins
+ are samples
there are 256 rows and columns

The reasoning for carries in the samples themselves is similar
*/
#define TABLE_BITVEC_SIZE (TABLE_SIZE / 8 + 1)
typedef struct {
    uint8_t samples[TABLE_SIZE];
    uint8_t carries[TABLE_BITVEC_SIZE];
} xfer_table;

static xfer_table tables[NUM_XFER_TABLES];

void set_xfer_table(uint8_t table_no, uint8_t* samples, uint8_t* carries) {
    if (table_no >= NUM_XFER_TABLES) {
        stop("Bad xfer set: %hhd", table_no);
    }
    memcpy(&(tables[table_no].samples), samples, TABLE_SIZE);
    memcpy(&(tables[table_no].carries), carries, TABLE_BITVEC_SIZE);
}

// negative table_no means identity 
int8_t uxfer8(int8_t table_no, uint8_t in) {
    if (table_no >= NUM_XFER_TABLES) {
        stop("bad xfer8 %hhd", table_no);
    } else if (table_no < 0) {
        return in;   
    }  
    return tables[table_no].samples[in];
}

// Interpolate a 16 bit value across an xfer table
// In theory it should be possible to generate pretty quick machine code for this. 
// For now I'm trusting the compiler to do a decent enough job and making an attempt at readable source.
int16_t uxfer16(int8_t table_no, uint16_t in) {
    if (table_no >= NUM_XFER_TABLES) {
        stop("bad xfer16 %hhd", (int8_t)table_no);
    } else if (table_no < 0) {
        return in;   
    }
    // there are 257 array entries, so 256 interpolation bins
    // bin 0 sits between sample 0 and 1, and takes 0x0000 through 0x00FF
    // bin 1 sits between sample 1 and 2, and takes 0x0100 through 0x01FF
    // ...
    // bin 254 sits between sample 254 and 255 and takes 0xFE00 through 0xFEFF
    // bin 255 sits between sample 255 and 256 and takes 0xFF00 through 0xFFFF
    // So, for an int16_t 'in', the bin is bounded by samples in >> 8 and in >> 8 + 1.
    uint16_t left_sample_idx = in >> 8;
    uint16_t right_sample_idx = left_sample_idx + 1;
    uint32_t left_sample;
    if (bitvec_get(tables[table_no].carries, left_sample_idx)) {
        left_sample = 0x10000;
    } else {
        left_sample = tables[table_no].samples[left_sample_idx];
        left_sample <<= 8;
    }
    uint32_t right_sample;
    if (bitvec_get(tables[table_no].carries, right_sample_idx)) {
        right_sample = 0x10000;
    } else {
        right_sample = tables[table_no].samples[right_sample_idx];
        right_sample <<= 8;
    }
    // linear interpolation between the samples
    uint16_t bin_pos = in & 0xFF;
    int32_t sample = (left_sample * (0x100 - bin_pos)) + (right_sample * bin_pos);
    return (int16_t)(sample >> 8); // renormalize
}
/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/

#ifndef MAKA_XFER_H
#define MAKA_XFER_H
#include "stdint.h"
#define NUM_XFER_TABLES ((int8_t)16)

#define TABLE_SIZE 0x101
    
// Each table may only be initialized once
// input is a list of 257 samples as uint8s.
// see xfer.c for more detail
void set_xfer_table(uint8_t table_no, uint8_t* samples, uint8_t *carries);

// negative table_no is magical and means identity 
int8_t uxfer8(int8_t table_no, uint8_t in);
int16_t uxfer16(int8_t table_no, uint16_t in);

#endif // MAKA_XFER_H
/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/

#ifndef MAKA_BITVEC_H
#define MAKA_BITVEC_H
#include "stdint.h"
#include "stdio.h"
#include "stdbool.h"
// return true if the ith bit in bv is set.
inline bool bitvec_get(uint8_t* bv, size_t i) {
    uint8_t idx = i >> 3;
    uint8_t bit = i & 0x07;
    return bv[idx] & (1 << bit);
}

//TODO (landon) bitvec_set. I haven't had a use for it yet.
    
#endif // MAKA_BITVEC_H
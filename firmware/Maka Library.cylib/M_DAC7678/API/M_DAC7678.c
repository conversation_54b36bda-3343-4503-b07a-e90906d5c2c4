/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/

#include "project.h"
#include "stdint.h"
#include "stop.h"
#include "maka_endian.h"
#define NUM_CHANNELS 8
#define DAC7678_ADDR 0b1001000
#pragma pack(1)
typedef struct {
    uint8_t ca; // Command + access, values listed in in table 12 of the datasheet.
    uint16_t data;
} dac7678_cmd;
#pragma pack(0)

static void sync_send(uint8_t c, uint8_t a, uint16_t data) {
    static dac7678_cmd cmd;
    cmd.ca = (c << 4) | (a & 0xF);
    cmd.data = be16toh(data);
    `$I2cMasterInstanceName`_MasterClearStatus();
    `$I2cMasterInstanceName`_MasterWriteBuf(DAC7678_ADDR, (uint8_t*)&cmd, sizeof(cmd), `$I2cMasterInstanceName`_MODE_COMPLETE_XFER);
    uint8_t mstat;
    do {
        mstat = `$I2cMasterInstanceName`_MasterStatus();
    } while(!(mstat & `$I2cMasterInstanceName`_MSTAT_WR_CMPLT));
    if (mstat & (`$I2cMasterInstanceName`_MSTAT_ERR_XFER)) {
        stop("no");
    }
    // TODO (Landon) check the status bits.
}

void `$INSTANCE_NAME`_SetChannelValue(uint8_t channel_no, uint16_t value) {
    if (channel_no >= NUM_CHANNELS) {
        stop("bad dac7678 ch: %d", channel_no);
    }
    sync_send(0, channel_no, value); // Set input and update dac register n. 
}

void `$INSTANCE_NAME`_Start() {
    `$I2cMasterInstanceName`_Start();
    sync_send(0b0111, 0, 0); // SW reset
    sync_send(0b1000, 0xFF, 0xFFFF); // Enable internal vref
}
/* [] END OF FILE */

/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#include "stdint.h"
#ifndef CANOPEN_H
#define CANOPEN_H

// this is only the data field. The whole packet is longer, but accessing the rest takes special APIs
#define CAN_DATA_LENGTH 8
#define J1939_DATA_LENGTH CAN_DATA_LENGTH
#define CANOPEN_DATA_LENGTH CAN_DATA_LENGTH
#define CANOPEN_RECVBUF_LEN 16
#define CANOPEN_NUM_NODES 8
typedef struct {
    uint8_t nodeid;
    uint8_t func;
    uint8_t spec;
    uint16_t index;
    uint8_t subindex;
    uint16_t canid;
    uint8_t len;
    uint8_t data[4];
} canopen_sdo;

typedef enum {
    CANOPEN_SDO_DL_REQ = 1,
    CANOPEN_SDO_UL = 2, // req and resp are the same for this one
    CANOPEN_SDO_DL_RESP = 3,
    CANOPEN_SDO_ABORT = 4
} canopen_spec;


// These are from Section 7.3.5 of CIA-301 (CANOpen)
// CAN has an 11 bit identifier, CANOpen specifies uses for some of them. The ones it does it splits into functions and nodeids.
// 7 bits go to nodeid, 4 bits go to function.
// The functions are:
// 0 reserved (id == 0 is NMT)
#define CANOPEN_NMT_FUNC 0
// 3 reserved
// 11 SDO Tx
#define CANOPEN_SDO_TX_FUNC 11 // TX and RX are from the server's perspective. The server is the owner of the objdict. In this case the motor controller, so TX means data from the motor controller to the PC
// 12 SDO Rx
#define CANOPEN_SDO_RX_FUNC 12
// 13 reserved
// 14 NMT Error control (ionno what this does)
#define CANOPEN_NMT_ERROR_FUNC 14
// 15 Reserved

// function are the higher order bits
#define CANOPEN_FUNC(id) (id >> 7)
#define CANOPEN_NODE_ID(id) (id & 0x7F)
#define CANOPEN_ID_CONSTRUCT(node_id, func) (((node_id) & 0x7F) | (((func) & 0xF) << 7))
// command specifier bits (elaborated in section 7.2.4.3 of CIA-301)
// These cover the first byte of both 'SDO Download protocol' and 'SDO Upload protocol'
#define CANOPEN_SDO_SPEC_CS(spec) (((spec) & 0b11100000) >> 5)
#define CANOPEN_SDO_SPEC_XLOAD_T(spec) (((spec) & 0b00010000) >> 4)
#define CANOPEN_SDO_SPEC_XLOAD_N(spec) (((spec) & 0b00001100) >> 2)
#define CANOPEN_SDO_SPEC_XLOAD_E(spec) (((spec) & 0b00000010) >> 1)
#define CANOPEN_SDO_SPEC_XLOAD_S(spec) ((spec) & 0b00000001)

#define CANOPEN_SDO_SPEC_XLOAD_CONSTRUCT(cs, t, n, e, s) ((s & 0b00000001) | ((e << 1) & 0b00000010) | ((n << 2) & 0b00001100) | ((t << 4) & 0b00010000) | ((cs << 5) & 0b11100000))

#endif // CANOPEN_H

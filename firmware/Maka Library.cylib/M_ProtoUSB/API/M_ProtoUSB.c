/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
*/

#include "`$INSTANCE_NAME`.h"
#include "project.h"
#include "stop.h"
#include "stdlib.h"
#include "string.h"
#include "stdbool.h"
#include "leb128.h"
#include "cyapicallbacks.h"

#define USB_PACKET_SIZE 64

typedef enum { // I think this is a 'moore machine'
    STATE_BEGIN, // Reset state
    STATE_MAGIC, // Next byte is the magic byte
    STATE_LENGTH, // Next byte is a length byte (LEB128 encoded)
    STATE_PACKET // Next byte is packet data
} recvstate;

static int packets;
static int bytes;
#define MAX_PKTBUF_SIZE 512
static void process_recv(uint8_t *recvbuf, uint8_t recvbuf_size) {
    static recvstate state = STATE_BEGIN;
    static uint8_t pktbuf[MAX_PKTBUF_SIZE];
    static size_t pktbuf_size; // total size
    static size_t pktbuf_pos; // Position in pktbuf
    static uint8_t leb128_shift; 
    size_t recvbuf_pos = 0;
    packets++;
    bytes += recvbuf_size;
    while (recvbuf_pos < recvbuf_size) {
        switch(state) {
            case(STATE_BEGIN): {
                pktbuf_pos = 0;
                pktbuf_size = 0;
                leb128_shift = 0;
                state = STATE_MAGIC;
            }; break;
            case(STATE_MAGIC): {
                uint8_t magic = recvbuf[recvbuf_pos];
                if (magic != `$Magic`) {
                    stop("Bad magic %02x != %02x", magic, `$Magic`);
                }
                recvbuf_pos += 1;
                state = STATE_LENGTH;
            }; break;
            case(STATE_LENGTH): {
                pktbuf_size |= ((size_t)(recvbuf[recvbuf_pos] & 0x7F)) << leb128_shift;
                leb128_shift += 7;
                if (!(recvbuf[recvbuf_pos] & 0x80)) {
                    if (pktbuf_size > MAX_PKTBUF_SIZE) {
                        stop("big pktbuf: %d", pktbuf_size);
                    }
                    memset(pktbuf, 0, pktbuf_size);
                    state = STATE_PACKET;
                }
                recvbuf_pos += 1;
            }; break;
            case(STATE_PACKET): {
                size_t pktbuf_remaining = pktbuf_size - pktbuf_pos;
                size_t recvbuf_remaining = recvbuf_size - recvbuf_pos;
                if (pktbuf_remaining <= recvbuf_remaining) {
                    memcpy(pktbuf + pktbuf_pos, recvbuf + recvbuf_pos, pktbuf_remaining);
                    recvbuf_pos += pktbuf_remaining;
                    pktbuf_pos += pktbuf_remaining; // this is pointless
#ifdef `$INSTANCE_NAME`_RECEIVE_CALLBACK
                    `$INSTANCE_NAME`_Receive_Callback(pktbuf, pktbuf_size);
#endif
                    state = STATE_BEGIN;
                } else {
                    memcpy(pktbuf + pktbuf_pos, recvbuf + recvbuf_pos, recvbuf_remaining);
                    pktbuf_pos += recvbuf_remaining;
                    recvbuf_pos += recvbuf_remaining;
                }
            }; break;
            default:
                stop("Bad assembler state");
        }
    }
}

void `$INSTANCE_NAME`_Loop() {
    static uint8_t recvbuf[USB_PACKET_SIZE];
    if (0u != `$INSTANCE_NAME``[USB]`IsConfigurationChanged()) {
        if (0u != `$INSTANCE_NAME``[USB]`GetConfiguration()) {
            `$INSTANCE_NAME``[USB]`CDC_Init();
        }
    }
    if (`$INSTANCE_NAME``[USB]`GetConfiguration()) {
        while(`$INSTANCE_NAME``[USB]`DataIsReady()) {
            memset(recvbuf, 0, USB_PACKET_SIZE);
            size_t recvbuf_size = `$INSTANCE_NAME``[USB]`GetAll(recvbuf); // 64 is the max packet size
            if (recvbuf_size) { // zero length packets supposedly exist, and can be ignored
                process_recv(recvbuf, recvbuf_size);
            }
        }
    }
}

void `$INSTANCE_NAME`_Start() {
    `$INSTANCE_NAME``[USB]`Start(0, `$INSTANCE_NAME``[USB]`5V_OPERATION);
}
void `$INSTANCE_NAME`_Stop() {
    `$INSTANCE_NAME``[USB]`Stop();
}

bool `$INSTANCE_NAME`_Send(uint8_t* buf, size_t len) {
    // TODO (landon) This is janky and synchronous. There's a DMA facility for this, but it can't be used with the
    // simplified CDC API, so requires a more complicated setup and management.
    // Without that, in order to send longer than single packets requires buffer management, and currently this
    // cheap implementation works fine.
    static uint8_t outbuf[USB_PACKET_SIZE];
    if (len > (USB_PACKET_SIZE - 1)) {
        stop("Long out packet %d", len);
    }
    while (!`$INSTANCE_NAME``[USB]`CDCIsReady());
    outbuf[0] = `$Magic`;
    uint8_t size_size = leb128_encode(outbuf + 1, len); // right now this has to be 1 cause we only support 63 byte packets
    memcpy(outbuf + size_size + 1, buf, len);
    `$INSTANCE_NAME``[USB]`PutData(outbuf, len + size_size + 1);
    return true;
}

void `$INSTANCE_NAME`_Loop();
/* [] END OF FILE */

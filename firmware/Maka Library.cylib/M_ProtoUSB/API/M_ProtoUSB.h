/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/

#ifndef MAKA_`$INSTANCE_NAME`_H
#define MAKA_`$INSTANCE_NAME`_H
#include "stdbool.h"
#include "stddef.h"
#include "stdint.h"

void `$INSTANCE_NAME`_Start();
void `$INSTANCE_NAME`_Stop();
bool `$INSTANCE_NAME`_Send(uint8_t* buf, size_t len);
void `$INSTANCE_NAME`_Loop();

// *buf should be treated like it's on the stack
// Also `$INSTANCE_NAME`_RECEIVE_CALLBACK leads to `$INSTANCE_NAME`_Receive_Callback(uint8_t *buf, size_t size)

#endif // MAKA_`$INSTANCE_NAME`_H
/* [] END OF FILE */

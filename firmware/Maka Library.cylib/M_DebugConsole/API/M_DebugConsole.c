/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#include "stdarg.h"
#include "stdio.h"
#include "project.h"
#define DEBUGBUF_LEN 256
void `$INSTANCE_NAME`_Start() {
    `$INSTANCE_NAME``[UART]`Start();
}

void `$INSTANCE_NAME`_Printv(const char* fmt, va_list ap) {
    static char fmtbuf[DEBUGBUF_LEN];
    vsnprintf(fmtbuf, DEBUGBUF_LEN, fmt, ap);
    `$INSTANCE_NAME``[UART]`PutString(fmtbuf);
}

void `$INSTANCE_NAME`_Print(const char* fmt, ...) {
    va_list ap;
    va_start(ap, fmt);
    `$INSTANCE_NAME`_Printv(fmt, ap);
}
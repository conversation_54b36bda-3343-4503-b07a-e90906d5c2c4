/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/

// Serial debug terminal. Can be used with the PSoC bridge application or any linux console pointed at the serial device.

#ifndef `$INSTANCE_NAME`_H
#define `$INSTANCE_NAME`_H

#include "stdarg.h"
    
void `$INSTANCE_NAME`_Start();
void `$INSTANCE_NAME`_Printv(const char* fmt, va_list ap); // This is synchronous on the I2C bus, i.e. slow.
void `$INSTANCE_NAME`_Print(const char* fmt, ...);
#endif // `$INSTANCE_NAME`_H
/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#include "stdint.h"
#include "stdbool.h"
#include "project.h"
#include "stdlib.h"
#include "stop.h"

static int16_t setpoints[`$NUM_CHANNELS`]; // Input, for setting from interrupts (use SetChannelPosition)

// setpoint is in revolutions/2**17. (thusly, a signed int16 covers 1/2 circle)
// P, I, and D are those, those-seconds, and those/seconds respectively.
static struct {
    bool enabled;
    float p_gain; // We don't have an FPU, so these are slow, but fixed point PID math is kinda dicey and we don't have many of these so paying the cost is OK. Hopefully.
    float i_gain;
    float d_gain;
} settings[`$NUM_CHANNELS`];

void `$INSTANCE_NAME`_Start() {
    `$HydroControlInstanceName`_Start();
    `$INSTANCE_NAME`_Loop(0.0); // Bullshit run through the loop to initialize prev_pvs
}
static bool paused = false;
void `$INSTANCE_NAME`_Pause() {
    paused = true;
}
void `$INSTANCE_NAME`_Loop(float dt_s) { // Time since last loop in seconds
    float pvs[`$NUM_CHANNELS`]; // Process Variables (measured response)
    static float prev_pvs[`$NUM_CHANNELS`]; // PV last time. Used for calculating d term.
    static float is[`$NUM_CHANNELS`]; // Integral terms
    static int16_t cvs[`$NUM_CHANNELS`]; // Control Variables (outputs, velocity as understood by hydroControl (so int16)
    static uint16_t cur_setpoints[`$NUM_CHANNELS`]; 
    uint8_t saved_intstatus = CyEnterCriticalSection();
    memcpy(cur_setpoints, setpoints, sizeof(cur_setpoints)); // Copy all at once to be sure we're controlling a consistent, complete, desired robot state
    CyExitCriticalSection(saved_intstatus);
    for (uint8_t channel_no = 0; channel_no < `$NUM_CHANNELS`; channel_no++) {
        if (!settings[channel_no].enabled) {
            pvs[channel_no] = 0.0;
            continue;
        }
        prev_pvs[channel_no] = pvs[channel_no];
        pvs[channel_no] = (float)`$ADCInstanceName`_GetResult16(channel_no);
    }
    for (uint8_t channel_no = 0; channel_no < `$NUM_CHANNELS`; channel_no++) {
        if (!settings[channel_no].enabled) {
            continue;
        }
        float delta = cur_setpoints[channel_no] - pvs[channel_no];
        float p_term = delta * settings[channel_no].p_gain;
        float i_term = is[channel_no] + (delta * settings[channel_no].i_gain * dt_s);
        is[channel_no] = i_term;
        float d_term;
        if (dt_s != 0.0) { // can be 0 because apinning through this loop once on init is easier than building a separate init state. This could be made nicer by adding a classier initialization routine.
            d_term = (pvs[channel_no] - prev_pvs[channel_no]) * settings[channel_no].d_gain / dt_s;
        } else {
            d_term = 0;
        }
        float fcv = (p_term + i_term + d_term);
        if (fcv >= (float)INT16_MAX) {
            cvs[channel_no] = INT16_MAX;
        } else if (fcv <= (float)INT16_MIN) {
            cvs[channel_no] = INT16_MIN;
        } else {
            cvs[channel_no] =  (int16_t)(fcv);
        }
        if (!paused) {
            `$HydroControlInstanceName`_SetChannelVelocity(channel_no, cvs[channel_no]);
        }
    }
}

void `$INSTANCE_NAME`_Stop() {
    `$HydroControlInstanceName`_Stop();
}

void `$INSTANCE_NAME`_EnableChannel(uint8_t channel_no, float p_gain, float i_gain, float d_gain) {
    if (channel_no >= `$NUM_CHANNELS`) {
        stop("bad hedof en %d", channel_no);
    }
    settings[channel_no].enabled = true;
    settings[channel_no].p_gain = p_gain;
    settings[channel_no].i_gain = i_gain;
    settings[channel_no].d_gain = d_gain;
}

void `$INSTANCE_NAME`_SetChannelPosition(uint8_t channel_no, int16_t position) {
    if (channel_no >= `$NUM_CHANNELS` || !settings[channel_no].enabled) {
        stop("bad hedof set %d", channel_no);
    }
    paused = false;
    setpoints[channel_no] = position;
}
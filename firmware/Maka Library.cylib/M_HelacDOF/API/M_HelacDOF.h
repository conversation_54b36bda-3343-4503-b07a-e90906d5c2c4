/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#ifndef `$INSTANCE_NAME`_H
#define `$INSTANCE_NAME`_H

#define `$INSTANCE_NAME`_NUM_CHANNELS `$NUM_CHANNELS`
void `$INSTANCE_NAME`_Start();
void `$INSTANCE_NAME`_Loop(float dt_s);
void `$INSTANCE_NAME`_Stop();
void `$INSTANCE_NAME`_Pause(); // Stop movement until the next time SetChannelPosition is called
void `$INSTANCE_NAME`_SetChannelPosition(uint8_t channel_no, int16_t position);
// may be called multiple times (but not from interrupts) to change settings
void `$INSTANCE_NAME`_EnableChannel(uint8_t channel_no, float p_gain, float i_gain, float d_gain);
#endif // `$INSTANCE_NAME`_H

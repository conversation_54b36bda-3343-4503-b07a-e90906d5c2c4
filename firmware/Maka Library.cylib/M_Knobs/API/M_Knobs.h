/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#include "stdlib.h"
#ifndef `$INSTANCE_NAME`_H
#define `$INSTANCE_NAME`_H
typedef enum {
    `$INSTANCE_NAME`_NOTHING = 0,
    `$INSTANCE_NAME`_TURN_DOWN, // for what?
    `$INSTANCE_NAME`_TURN_UP, 
    `$INSTANCE_NAME`_PRESS,
    `$INSTANCE_NAME`_RELEASE
} `$INSTANCE_NAME`_event_what;

typedef enum {
    `$INSTANCE_NAME`_NEITHER = 0,
    `$INSTANCE_NAME`_KNOB_A,
    `$INSTANCE_NAME`_KNOB_B
} `$INSTANCE_NAME`_event_which;

typedef struct {
    `$INSTANCE_NAME`_event_which which;
    `$INSTANCE_NAME`_event_what what;
} `$INSTANCE_NAME`_event;

void `$INSTANCE_NAME`_Start();
void `$INSTANCE_NAME`_Init();
void `$INSTANCE_NAME`_Stop();
// This returns a struct on the stack
// Returns the next unprocessed event.
// Returns events of what NOTHING on knob NEITHER when there are no more.
`$INSTANCE_NAME`_event `$INSTANCE_NAME`_NextEvent();
// Returns the number of missed events since the last time you called it (or since start if not called before)
size_t `$INSTANCE_NAME`_MissedEvents();
#endif // `$INSTANCE_NAME`_H
/* [] END OF FILE */

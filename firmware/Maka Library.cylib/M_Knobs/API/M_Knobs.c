/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/

#include "project.h"
#include "stdint.h"
#include "stdbool.h"
#include "ringq.h"
#define KNOB_SW(kstate) ((kstate) & 1)
#define KNOB_DIR(kstate) ((kstate) & (1 << 1))
#define KNOB_CLK(kstate) ((kstate) & (1 << 2))
#define KNOB_N(n, instate) (((instate) >> (3 * n)) & 0b111)
static size_t missed_events;
static `$INSTANCE_NAME`_event eventq_buf[`$EventQueueDepth`];
static ringq eventq;

// TODO (landon) it would be nice if this was prettier. Someday it'd be cool to redo ringq for better static typing
// and this'll have to be redone then anyway, so for now it's ugly.
#define eventq_put(which, what) \
`$INSTANCE_NAME`_event e = {which, what}; \
if (RINGQ_FULL(eventq)) { \
    missed_events++; \
} else { \
    RINGQ_PUT(eventq, e); \
} 

CY_ISR(Knobs_Intr_Handler) {
    uint8_t saved_intstatus = CyEnterCriticalSection();
    `$INSTANCE_NAME``[Change]`ClearPending();
    uint16_t knobs_state = `$INSTANCE_NAME``[Inputs]`Read();
    uint16_t knobs_changed = `$INSTANCE_NAME``[Inputs]`ClearInterrupt();
    for (uint8_t n = 0; n < 2; n++) {
        `$INSTANCE_NAME`_event_which which = n ? `$INSTANCE_NAME`_KNOB_B : `$INSTANCE_NAME`_KNOB_A;
        if (KNOB_CLK(KNOB_N(n, knobs_changed & knobs_state))) { // we only want rising edges of clk
            if (KNOB_DIR(KNOB_N(n, knobs_state))) {
                eventq_put(which, `$INSTANCE_NAME`_TURN_DOWN);
            } else {
                eventq_put(which, `$INSTANCE_NAME`_TURN_UP);
            }
        } else if (KNOB_SW(KNOB_N(n, knobs_changed))) {
            if (KNOB_SW(KNOB_N(n, knobs_state))) {
                eventq_put(which, `$INSTANCE_NAME`_PRESS);
            } else {
                eventq_put(which, `$INSTANCE_NAME`_RELEASE);
            }
        } // we don't care about dir's edges
    }
    CyExitCriticalSection(saved_intstatus);
}

void `$INSTANCE_NAME`_Start() {
    `$INSTANCE_NAME``[Change]`StartEx(Knobs_Intr_Handler);
    RINGQ_INIT(eventq, eventq_buf, `$EventQueueDepth`, `$INSTANCE_NAME`_event);
}

`$INSTANCE_NAME`_event `$INSTANCE_NAME`_NextEvent() {
    uint8_t saved_intstatus = CyEnterCriticalSection();
    `$INSTANCE_NAME`_event r = {`$INSTANCE_NAME`_NEITHER, `$INSTANCE_NAME`_NOTHING};
    if (!RINGQ_EMPTY(eventq)) {
        r = RINGQ_GET(eventq, typeof(r));
    }
    CyExitCriticalSection(saved_intstatus);
    return r;
}

size_t `$INSTANCE_NAME`_MissedEvents() {
    uint8_t saved_intstatus = CyEnterCriticalSection();
    size_t r = missed_events;
    missed_events = 0;
    CyExitCriticalSection(saved_intstatus);
    return r;
}

/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#include "stdint.h"
#include "stdlib.h"
// XXX our LEB128 implementation doesn't support more than 32 bit values
// Decodes an LEB128 value from buf, returns number of bytes consumed, outputs into out
size_t leb128_decode(uint8_t* buf, size_t* out);
// Encodes an LEB128 value to buf, returns number of bytes written, takes from val
size_t leb128_encode(uint8_t* buf, size_t val);

/* [] END OF FILE */

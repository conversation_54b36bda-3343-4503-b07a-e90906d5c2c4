/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/

#include "project.h"
#include "stdlib.h"
#include "stdarg.h"
#include "stdio.h"
#include "stdbool.h"
#define PCF8574_ADDR 0x27

#define ROWS 4
#define COLS 20
// this should be COLS cols wide
#define EMPTYLINE "                    "

// commands
#define CMD_CLEARDISPLAY 0x01
#define CMD_RETURNHOME 0x02
#define CMD_SETCGRAMADDR 0x40
#define CMD_SETDDRAMADDR 0x80

// PCF8574 pin - HD44780 signal
// P0 - Rs
// P1 - R/W
// P2 - E
// P3 - Backlight on
// P4:7 - D4:7
#define RS (1 << 0)
#define RW (1 << 1)
#define EN (1 << 2)
#define BL (1 << 3)

// +1 for null byte at the end of the row
static char current[ROWS][COLS+1]; 
static char setpoints[ROWS][COLS+1];

static void write_and_pulse_en(uint8_t data) {
    `$I2cMasterInstanceName`_MasterClearStatus();
    data |= BL; // Why would you ever want the backlight off?
    data |= EN; // Data is latched in when EN drops
    `$I2cMasterInstanceName`_MasterWriteBuf(PCF8574_ADDR, &data, 1, `$I2cMasterInstanceName`_MODE_COMPLETE_XFER);
    while(!(`$I2cMasterInstanceName`_MasterStatus() & `$I2cMasterInstanceName`_MSTAT_WR_CMPLT));
    CyDelayUs(1);
    data &= ~EN;
    `$I2cMasterInstanceName`_MasterWriteBuf(PCF8574_ADDR, &data, 1, `$I2cMasterInstanceName`_MODE_COMPLETE_XFER);
    while(!(`$I2cMasterInstanceName`_MasterStatus() & `$I2cMasterInstanceName`_MSTAT_WR_CMPLT));
    CyDelayUs(50); // Spec'd settle time is 37us, I cargo culted this from the people who sell the thing.
    data = EN | BL;
    `$I2cMasterInstanceName`_MasterWriteBuf(PCF8574_ADDR, &data, 1, `$I2cMasterInstanceName`_MODE_COMPLETE_XFER);
    while(!(`$I2cMasterInstanceName`_MasterStatus() & `$I2cMasterInstanceName`_MSTAT_WR_CMPLT));
}

static void write_byte(uint8_t data, uint8_t mode) {
    write_and_pulse_en((data & 0xF0) | (mode & 0x0F));
    write_and_pulse_en((data << 4) | (mode & 0x0F));
}

static uint8_t cgram_chars[5][8] = {
    { // Don't use CGRAM address 0 cause it's null, which can't be printf'd
        0b00000000,
        0b00000000,
        0b00000000,
        0b00000000,
        0b00000000,
        0b00000000,
        0b00000000,
        0b00000000
    }, { // ):
        0b00000000,
        0b00001010,
        0b00001010,
        0b00000000,
        0b00001110,
        0b00010001,
        0b00000000,
        0b00000000
    },{ // (:
        0b00000000,
        0b00001010,
        0b00001010,
        0b00000000,
        0b00010001,
        0b00001110,
        0b00000000,
        0b00000000
    },{ // |:
        0b00000000,
        0b00001010,
        0b00001010,
        0b00000000,
        0b00011111,
        0b00000000,
        0b00000000,
        0b00000000
    },{ // D:<
        0b00000000,
        0b00010001,
        0b00001010,
        0b00000000,
        0b00001110,
        0b00011111,
        0b00000000,
        0b00000000
    }
};

static void load_cgram() {
    write_byte(CMD_SETCGRAMADDR, 0); // go to CGRAM address 0
    for (uint8_t cchar = 0; cchar < 5; cchar++) {
        for(uint8_t row = 0; row < 8; row++) {
            write_byte(cgram_chars[cchar][row], RS);
        }
    }
    write_byte(CMD_SETDDRAMADDR, 0); // Back to DDRAM
}

void `$INSTANCE_NAME`_Clear() {
    write_byte(CMD_CLEARDISPLAY, 0);
    CyDelayUs(2000);
}

#define FORTY_FIVE_HUNDRED 4500
#define CARGO_CULTED_RESET_CONSTANT (0x03 << 4)

void `$INSTANCE_NAME`_Start() {
    `$I2cMasterInstanceName`_Start();
    // This is 'initialization by instructions' as defined in the HD44780 datasheet.
    // According to the implementation (for arduino) that I copped this from, it
    // misses the first long message occasionally, so we try 3 times.
    
    // This is somewhat bizarre and fragile. Unless you have an HD44780 to test on and are doing so,
    // please leave it alone.

    CyDelay(50); // Wait 50msec from LCD poweron before sending stuff.
    write_and_pulse_en(CARGO_CULTED_RESET_CONSTANT);
    CyDelayUs(FORTY_FIVE_HUNDRED);
    write_and_pulse_en(CARGO_CULTED_RESET_CONSTANT);
    CyDelayUs(FORTY_FIVE_HUNDRED);
    write_and_pulse_en(CARGO_CULTED_RESET_CONSTANT);
    CyDelayUs(FORTY_FIVE_HUNDRED);
    write_and_pulse_en(CARGO_CULTED_RESET_CONSTANT);
    CyDelayUs(100);
    write_and_pulse_en(CARGO_CULTED_RESET_CONSTANT);
    CyDelayUs(150);
    write_and_pulse_en(0x02 << 4);
    write_byte(0b00101000, 0); // function set, 2 line mode, 5x8 font
    // Turn the display on (no cursor, no blinking cursor)
    write_byte(0b00001000, 0); // display off
    `$INSTANCE_NAME`_Clear();
    write_byte(0b00000110, 0); // Increment, no shift
    write_byte(CMD_RETURNHOME, 0);
    CyDelayUs(4000);
    load_cgram();
    write_byte(0b00001100, 0); // Display On
    memset(setpoints, ' ', sizeof(setpoints));
    memset(current, ' ', sizeof(current));
}

static void position(uint8_t row, uint8_t col) {
    if (col > COLS) {
        return;
    }
    if (row > ROWS) {
        return;
    }
    switch (row) {
        case 0:
            write_byte(0b010000000 | (0x80 + col), 0);
            break;
        case 1:
            write_byte(0b010000000 | (0xC0 + col), 0);
            break;
        case 2:
            write_byte(0b010000000 | (0x94 + col), 0);
            break;
        case 3:
            write_byte(0b010000000 | (0xD4 + col), 0);
            break;
        default:
            break;
    }
}

void `$INSTANCE_NAME`_vPrint(uint8_t row, uint8_t col, const char* fmt, va_list ap) {
    static char fmtbuf[COLS + 1];
    if (row >= ROWS || col >= COLS) {
        return;
    }
    size_t size = vsnprintf(fmtbuf, COLS - col + 1, fmt, ap);
    memcpy(setpoints[row] + col, fmtbuf, size);
}

void `$INSTANCE_NAME`_Print(uint8_t row, uint8_t col, const char* fmt, ...) {
    va_list ap;
    va_start(ap, fmt);
    `$INSTANCE_NAME`_vPrint(row, col, fmt, ap);
}

void `$INSTANCE_NAME`_Status(`$INSTANCE_NAME`_custom_char charno, const char* fmt, ...) {
    // TODO make this a memset
    `$INSTANCE_NAME`_Print(0, 0, EMPTYLINE);
    `$INSTANCE_NAME`_Print(0, 0, "%c", charno);
    va_list ap;
    va_start(ap, fmt);
    `$INSTANCE_NAME`_vPrint(0, 2, fmt, ap);
}

void `$INSTANCE_NAME`_Loop() {
    int dirty = memcmp(current, setpoints, sizeof(current));
    if (dirty) {
        memcpy(current, setpoints, sizeof(current));
        for(int row = 0; row < ROWS; row++) {
            position(row, 0);
            for(int col = 0; col < COLS; col++) {
                write_byte(current[row][col], RS);
            }
        }
    }
}
/* [] END OF FILE */


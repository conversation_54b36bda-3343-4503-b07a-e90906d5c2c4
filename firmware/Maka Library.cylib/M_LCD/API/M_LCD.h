/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#include "stdint.h"
#include "stdarg.h"

#ifndef `$INSTANCE_NAME`_H
#define `$INSTANCE_NAME`_H
// Interface for I2C character LCD interfaces. What these are is actually HD44780 LCD controllers
// hooked up to PCF8574 I2C I/O expanders. (the pinout is in m_lcd.c)
void `$INSTANCE_NAME`_Start();
// Does not repaint the screen unless it actually changes.
// Is slow af when it does cause synchronous I2C
void `$INSTANCE_NAME`_Loop();

// On the HD44780, CGR<PERSON> gets codepoints 0-7. 0 is also NUL, which has obvious implications.
// This is annoying, as it means we need an extra state to deal with whether or not a 0
// byte means "custom character 0", "Stop printing", or "Uninitialized (empty) space".
// We make custom character 0 empty space, which makes this easier.

typedef enum {
    `$INSTANCE_NAME`_CHAR_SAD = 1,
    `$INSTANCE_NAME`_CHAR_HAPPY = 2,
    `$INSTANCE_NAME`_CHAR_MEDIOCRE = 3,
    `$INSTANCE_NAME`_CHAR_ANGRY = 4
} `$INSTANCE_NAME`_custom_char;

void `$INSTANCE_NAME`_Clear();
void `$INSTANCE_NAME`_Print(uint8_t row, uint8_t col, const char* fmt, ...);
void `$INSTANCE_NAME`_vPrint(uint8_t row, uint8_t col, const char* fmt, va_list ap);
void `$INSTANCE_NAME`_Status(`$INSTANCE_NAME`_custom_char charno, const char* fmt, ...);

#endif /* `$INSTANCE_NAME`_H */

/* [] END OF FILE */


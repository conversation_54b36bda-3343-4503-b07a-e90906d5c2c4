/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/

#ifndef MAKA_`$INSTANCE_NAME`_H
#define MAKA_`$INSTANCE_NAME`_H
    
// Open-loop synchronous hydraulic control. This takes care of mapping
// DAC channels onto hydraulic PCVs and mapping signed velocities onto the
// two-solenoid setup hydraulics have.
// Associate with a DAC in the configurator, call EnableChannel a few times,
// and go. 
// All of these functions are synchronous and none are reentrant. 
// You probably don't want to use this directly, use M_HydroDOF or M_HelacDOF

void `$INSTANCE_NAME`_Start();
void `$INSTANCE_NAME`_Stop();
void `$INSTANCE_NAME`_SetChannelVelocity(uint8_t channel_no, int16_t velocity);
void `$INSTANCE_NAME`_EnableChannel(uint8_t channel_no, int8_t table_no, uint8_t dac_channel_a, uint8_t dac_channel_b);
void `$INSTANCE_NAME`_Loop();
#endif // MAKA_`$INSTANCE_NAME`_H
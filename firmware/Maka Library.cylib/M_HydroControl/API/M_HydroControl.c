/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#include "xfer.h"
#include "stop.h"
#include "stdbool.h"
#include "project.h"

static struct {
    uint8_t enabled;
    uint8_t dac_channel_a;
    uint8_t dac_channel_b;
    int8_t table_no;
} settings[`$NUM_CHANNELS`];

static int16_t setpoints[`$NUM_CHANNELS`];

void `$INSTANCE_NAME`_SetChannelVelocity(uint8_t channel_no, int16_t velocity) {
    if (channel_no >= `$NUM_CHANNELS` || !settings[channel_no].enabled) {
        stop("Bad hydof v %d", channel_no);
    }
    setpoints[channel_no] = velocity;
}

void `$INSTANCE_NAME`_SetChannelOutXfer(uint8_t channel_no, int8_t table_no) {
    settings[channel_no].table_no = table_no;
}

static void set_channel_velocity_sync(uint8_t channel_no, int16_t velocity) {
    // we shift velocity left one cause DACs are unsigned and vel is signed.
    if (velocity > 0) {
        uint16 v_unsigned = velocity;
        v_unsigned <<= 1;
        `$DACInstanceName`_SetChannelValue(settings[channel_no].dac_channel_a, uxfer16(settings[channel_no].table_no, v_unsigned));
        `$DACInstanceName`_SetChannelValue(settings[channel_no].dac_channel_b, 0);
    } else if (velocity == INT16_MIN) { // Because two's compliment, signed ints aren't symmetric, so this is special cause -INT16_MIN doesn't fit in an int16
        `$DACInstanceName`_SetChannelValue(settings[channel_no].dac_channel_a, 0);
        `$DACInstanceName`_SetChannelValue(settings[channel_no].dac_channel_b, uxfer16(settings[channel_no].table_no, UINT16_MAX));
    } else if (velocity < 0) {
        uint16_t v_unsigned = -velocity;
        v_unsigned <<= 1;
        `$DACInstanceName`_SetChannelValue(settings[channel_no].dac_channel_a, 0);
        `$DACInstanceName`_SetChannelValue(settings[channel_no].dac_channel_b, uxfer16(settings[channel_no].table_no, v_unsigned));
    } else {
        `$DACInstanceName`_SetChannelValue(settings[channel_no].dac_channel_a, 0);
        `$DACInstanceName`_SetChannelValue(settings[channel_no].dac_channel_b, 0);
    }
}

void `$INSTANCE_NAME`_EnableChannel(uint8_t channel_no, int8_t table_no, uint8_t dac_channel_a, uint8_t dac_channel_b) {
    if (channel_no >= `$NUM_CHANNELS`) {
        stop("bad hydro chan %d", channel_no);
    }
    settings[channel_no].enabled = true;
    settings[channel_no].dac_channel_a = dac_channel_a;
    settings[channel_no].dac_channel_b = dac_channel_b;
    settings[channel_no].table_no = table_no;
    `$DACInstanceName`_SetChannelValue(dac_channel_a, 0);
    `$DACInstanceName`_SetChannelValue(dac_channel_b, 0);
}

void `$INSTANCE_NAME`_Loop() {
    static int16_t cur_setpoints[`$NUM_CHANNELS`];
    for (uint8_t channel_no = 0; channel_no < `$NUM_CHANNELS`; channel_no++) {
        int16_t new_setpoint = setpoints[channel_no];
        if (!settings[channel_no].enabled) {
            continue;
        }
        if (new_setpoint != cur_setpoints[channel_no]) {
            set_channel_velocity_sync(channel_no, new_setpoint);
            cur_setpoints[channel_no] = new_setpoint;
        }
    }
}

void `$INSTANCE_NAME`_Start() {
    `$DACInstanceName`_Start();
}

void `$INSTANCE_NAME`_Stop() {
    `$DACInstanceName`_Stop();
}

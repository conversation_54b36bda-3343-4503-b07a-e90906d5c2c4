/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROP<PERSON>ETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/

// Synchronous DAC interface for a DS1803 Digipot or group thereof
// Lowest bit of channel is wiper no, rest of bits are A pin state
#ifndef `$INSTANCE_NAME`_H
#define `$INSTANCE_NAME`_H
void `$INSTANCE_NAME`_Start();
void `$INSTANCE_NAME`_Stop();
uint32 `$INSTANCE_NAME`_SetChannelValue(uint8_t channel, uint16_t val); // This is synchronous on the I2C bus, i.e. slow.

#define TRANSFER_CMPLT    (0x00u)
#define TRANSFER_ERROR    (0xFFu)

#endif // `$INSTANCE_NAME`_H

/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#include "stdint.h"
#include "project.h"
#include "utils.h"

#define MAX_RETRIES 1000

/* Empty method in case DebugConsoleName is unset */
static void _Print(const char* fmt, ...)
{
    UNUSED(fmt);
}

#pragma pack(1)
struct ds1803_command { // Goes on the i2c wire, so is packed
    uint8_t cmd;
    uint8_t value;
};

#pragma pack(0)
uint32 `$INSTANCE_NAME`_SetChannelValue(uint8_t channel, uint16 value) {
    uint32 status = TRANSFER_ERROR;
    uint32 retries = 0;
    struct ds1803_command command;
    uint8_t wiper = channel % 2;
    uint8_t addr = (channel >> 1) | 0x28; // Magic number from the datasheet (https://datasheets.maximintegrated.com/en/ds/DS1803.pdf)
    command.cmd = 0xA9 + wiper; // Magic number from the datasheet
    command.value = value >> 8;

    `$I2cMasterInstanceName`_MasterWriteBuf(addr, (uint8_t*)&command, sizeof(command), `$I2cMasterInstanceName`_MODE_COMPLETE_XFER);
    while(!(`$I2cMasterInstanceName`_MasterStatus() & `$I2cMasterInstanceName`_MSTAT_WR_CMPLT))
    {
        retries += 1;
        if (retries >= MAX_RETRIES)
        {
            `$DebugConsoleName`_Print("i2c failed to send after %d retries\r\n", retries);
            `$I2cMasterInstanceName`_Stop();
            CyDelay(1000);
            `$I2cMasterInstanceName`_Start();
            retries = 0;
        }
    }
    if (!(`$I2cMasterInstanceName`_MSTAT_ERR_XFER & `$I2cMasterInstanceName`_MasterStatus()))
    {
        uint8 xfer_size = `$I2cMasterInstanceName`_MasterGetWriteBufSize();
        if (xfer_size == sizeof(command))
        {
            status = TRANSFER_CMPLT;
        }
        else
        {
            `$DebugConsoleName`_Print("i2c xfer fail %d / %d\r\n", xfer_size, sizeof(command));
        }
    }
    `$I2cMasterInstanceName`_MasterClearStatus();
    return status;
}

void `$INSTANCE_NAME`_Start() {
    `$I2cMasterInstanceName`_Start(); 
    for (int channel_no = 0; channel_no < `$NUM_CHANNELS`; channel_no++) { 
        `$INSTANCE_NAME`_SetChannelValue(channel_no, 0);
    }
}

void `$INSTANCE_NAME`_Stop() {
    for (int channel_no = 0; channel_no < `$NUM_CHANNELS`; channel_no++) {
        `$INSTANCE_NAME`_SetChannelValue(channel_no, 0);
    }
}
/* [] END OF FILE */

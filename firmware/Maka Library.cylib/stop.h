/* ========================================
 *
 * Copyright Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF Maka ARS.
 *
 * ========================================
*/
#ifndef MAKA_STOP_H
#define MAKA_STOP_H

// THis is magic, you must implement it to use maka library.
// Maka library functions call this when invariants have been violated and they can no longer operate reliably
extern void stop(const char* fmt, ...);

#endif //MAKA_STOP_H
/* [] END OF FILE */

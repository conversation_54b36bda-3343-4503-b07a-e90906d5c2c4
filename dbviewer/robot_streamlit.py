# isort:skip_file
from lib.common.db.records.weed_accuracy_samples import WeedAccuracySamples
import sys
import json
import os
import pathlib
import typing
import cv2
import zipfile

import matplotlib.pyplot as plt
import pandas as pd
import streamlit as st
import numpy as np

sys.path.append(os.path.realpath(os.path.join(os.getcwd(), os.path.dirname(__file__), "..")))

from lib.common.db.carbon_db_lite import CarbonDBLite  # noqa: E402
from lib.common.db.records.laser_records import LaserRecord  # noqa: E402
from lib.common.db.carbon_robot_db import CarbonRobotDBLite  # noqa: E402
from lib.common.db.streamlit.table_viewer import DBTableViewer  # noqa: E402
from tools.camera.annotate_p2p_target_images import draw_annotation  # noqa: E402

# from prometheus_client import start_http_server  # noqa: E402


class LaserRecordDBViewer(DBTableViewer):
    def __init__(self, db: CarbonDBLite) -> None:
        super().__init__(db, LaserRecord.__tablename__)
        df = pd.read_sql_query(
            f"""
            SELECT * FROM {self._table_name}
            """,
            con=self._db.engine,
        )

        hours = []
        for _, item in df.iterrows():
            hours.append(item["millis"] / 3600000)
        df["hours"] = hours
        del df["millis"]

        self._placeholder.dataframe(df)


class WeedAccuracySamplesGraphDBViewer(DBTableViewer):
    def __init__(self, db: CarbonDBLite) -> None:
        super().__init__(db, WeedAccuracySamples.__tablename__)

        weeds = pd.read_sql_query(
            f"""
                SELECT DISTINCT scanner_id, timestamped_weed_id
                FROM {self._table_name}
                ORDER BY timestamped_weed_id DESC
                LIMIT 1000
            """,
            con=db.engine,
        )

        weed_keys = [f"scanner_{row['scanner_id']}_{row['timestamped_weed_id']}" for _, row in weeds.iterrows()]

        weed_key = st.selectbox("Select Weed Sample", weed_keys)

        weed_key_split = weed_key[8:].split("_")
        scanner_id = weed_key_split[0]
        timestamped_weed_id = weed_key_split[1]

        initial_offset = st.checkbox("Show Initial Offset")

        weed = pd.read_sql_query(
            f"""
                SELECT *
                FROM {self._table_name}
                WHERE scanner_id = {scanner_id} AND timestamped_weed_id = {timestamped_weed_id}
                ORDER BY sample_count ASC
                LIMIT 100
            """,
            con=db.engine,
        )

        st.dataframe(weed)

        if not initial_offset:
            weed = weed[weed.sample_count != 0]

        self.draw_weed_offset_plot(weed, "offset_x_px", "offset_y_px", "Pixel Offset")
        self.draw_weed_offset_plot(weed, "offset_pan_ticks", "offset_tilt_ticks", "Servo Ticks Offset")
        self.draw_weed_offset_plot(weed, "offset_x_mm", "offset_y_mm", "MM Offset")
        self.draw_weed_path_plot(weed, "pos_pan_ticks", "pos_tilt_ticks", "Servo Ticks Position", rescale=True)
        self.draw_weed_path_plot(weed, "pos_pan_ticks", "pos_tilt_ticks", "Servo Ticks Position Zoomed", rescale=False)

    def draw_weed_offset_plot(self, weed: pd.DataFrame, x_column: str, y_column: str, title: str) -> None:
        fig, ax = plt.subplots()

        x = weed[x_column].values
        y = weed[y_column].values

        u = np.diff(x)
        v = np.diff(y)
        pos_x = x[:-1] + u / 2
        pos_y = y[:-1] + v / 2
        norm = np.sqrt(u ** 2 + v ** 2)

        ax.set_title(title)
        ax.plot(weed[x_column], weed[y_column], marker="o")
        ax.quiver(pos_x, pos_y, u / norm, v / norm, angles="xy", zorder=5, pivot="mid", color="blue")

        yabs_max = abs(max([abs(_y) for _y in ax.get_ylim()]))
        xabs_max = abs(max([abs(_x) for _x in ax.get_xlim()]))
        ax.set_ylim(ymin=-yabs_max, ymax=yabs_max)
        ax.set_xlim(xmin=-xabs_max, xmax=xabs_max)

        for index, row in weed.iterrows():
            plt.text(row[x_column] + xabs_max / 20, row[y_column] + yabs_max / 20, str(index), color="purple")

        ax.spines["left"].set_position("zero")
        ax.spines["bottom"].set_position("zero")
        ax.grid(color="grey", linestyle="-", linewidth=0.5)

        # Eliminate upper and right axes
        ax.spines["right"].set_color("none")
        ax.spines["top"].set_color("none")

        # Show ticks in the left and lower axes only
        ax.xaxis.set_ticks_position("bottom")
        ax.yaxis.set_ticks_position("left")

        st.pyplot(fig)

    def draw_weed_path_plot(self, weed: pd.DataFrame, x_column: str, y_column: str, title: str, rescale: bool) -> None:
        fig, ax = plt.subplots()

        x = weed[x_column].values
        y = weed[y_column].values

        u = np.diff(x)
        v = np.diff(y)
        pos_x = x[:-1] + u / 2
        pos_y = y[:-1] + v / 2
        norm = np.sqrt(u ** 2 + v ** 2)

        ax.set_title(title)
        ax.plot(weed[x_column], weed[y_column], marker="o")
        ax.quiver(pos_x, pos_y, u / norm, v / norm, angles="xy", zorder=5, pivot="mid", color="blue")

        yabs_max = abs(max([abs(_y) for _y in ax.get_ylim()]))
        xabs_max = abs(max([abs(_x) for _x in ax.get_xlim()]))
        real_max = max(yabs_max, xabs_max)

        if rescale:
            ax.set_ylim(ymin=0, ymax=real_max)
            ax.set_xlim(xmin=0, xmax=real_max)

        delta_x = ax.get_xlim()[1] - ax.get_xlim()[0]
        delta_y = ax.get_ylim()[1] - ax.get_ylim()[0]

        for index, row in weed.iterrows():
            plt.text(row[x_column] + delta_x / 40, row[y_column] + delta_y / 40, str(index), color="purple")

        ax.grid(color="grey", linestyle="-", linewidth=0.5)

        st.pyplot(fig)


class BurstCaptureDownloader:
    def __init__(self, db: CarbonDBLite) -> None:
        MAKA_DATA_DIR = os.getenv("MAKA_DATA_DIR")
        assert MAKA_DATA_DIR is not None, "MAKA_DATA_DIR is not set."

        self.cache_dir = os.path.join(MAKA_DATA_DIR, "media/cache/burst_recordings")

        print(self.cache_dir)

        os.makedirs(self.cache_dir, exist_ok=True)

        st.title("Burst Capture Downloader")
        burst_capture_directories = sorted(self._get_burst_capture_directories())

        # TODO (ZACH): Make a download all button that only compresses things when the download button is clicked.
        # st.download_button(
        #    f"Download all {len(burst_capture_directories)} burst captures.",
        #    self._compress_all(),
        #    file_name="burst_records.zip",
        # )
        for burst_capture_dir in burst_capture_directories:
            zip_filepath = self._compress_one(burst_capture_dir)

            date, row, camera, timestamp = burst_capture_dir.split("/")[-4:]
            download_button_name = f"{date}-row-{row}-{camera}-{timestamp}"
            with open(zip_filepath, "rb") as f:
                st.download_button(download_button_name, f, file_name=os.path.basename(zip_filepath))

    def _get_burst_capture_directories(self) -> typing.List[str]:
        MAKA_DATA_DIR = os.getenv("MAKA_DATA_DIR")
        assert MAKA_DATA_DIR is not None, "MAKA_DATA_DIR is not set."
        data_dir = os.path.join(MAKA_DATA_DIR, "media/burst_recordings/full_burst_recordings")
        burst_capture_directories = []
        for date in os.listdir(data_dir):
            date_dir = os.path.join(data_dir, date)
            for row_index in os.listdir(date_dir):
                row_dir = os.path.join(date_dir, row_index)
                for target_camera in os.listdir(row_dir):
                    target_camera_dir = os.path.join(row_dir, target_camera)
                    for burst_capture in os.listdir(target_camera_dir):
                        burst_capture_dir = os.path.join(target_camera_dir, burst_capture)
                        burst_capture_directories.append(burst_capture_dir)
        return burst_capture_directories

    def _compress_all(self) -> str:
        data = "PLACEHOLDER"
        return data

    def _compress_one(self, burst_capture_dir: str) -> str:
        date, row, camera, timestamp = burst_capture_dir.split("/")[-4:]
        zip_filename = f"{date}_{row}_{camera}_{timestamp}"
        zip_filepath = os.path.join(self.cache_dir, zip_filename)
        with zipfile.ZipFile(zip_filepath + ".zip", "w", zipfile.ZIP_DEFLATED, allowZip64=True) as zf:
            for root, _, filenames in os.walk(burst_capture_dir):
                for filename in filenames:
                    filepath = os.path.join(root, filename)
                    zf.write(filepath, zip_filename + "/" + filename)
        return zip_filepath + ".zip"

    def _get_possible_target_cameras(self, data_dir: str,) -> typing.List[str]:
        possible_target_cameras = []
        for burst_name in os.listdir(data_dir):
            robot_id, row_id, camera_id, datetime_stamp = burst_name.split("_")
            if camera_id not in possible_target_cameras:
                possible_target_cameras.append(camera_id)
        return possible_target_cameras


class BurstCaptureExplorer:
    def __init__(self, db: CarbonDBLite) -> None:

        st.title("Burst Capture Explorer")
        MAKA_DATA_DIR = os.getenv("MAKA_DATA_DIR")
        assert MAKA_DATA_DIR is not None, "MAKA_DATA_DIR is not set."
        data_dir = os.path.join(MAKA_DATA_DIR, "media/burst_recordings/full_burst_recordings")
        assert len(os.listdir(data_dir)) > 0, "No saved burst recordings"

        min_images = st.sidebar.number_input("Minimum image count:", min_value=2, max_value=100)

        burst_capture_directories = [
            x for x in sorted(list(os.listdir(data_dir))) if self._check_burst(os.path.join(data_dir, x), min_images)
        ]

        possible_dates = self._get_possible_dates(burst_capture_directories)
        selected_date = st.sidebar.selectbox("Date selection:", possible_dates)

        possible_row_ids = self._get_possible_rows(burst_capture_directories, selected_date)
        selected_row_id = st.sidebar.selectbox("Row selection:", possible_row_ids)

        possible_camera_ids = self._get_possible_target_cameras(
            burst_capture_directories, selected_date, selected_row_id
        )
        selected_camera_id = st.sidebar.selectbox("Camera Selection:", possible_camera_ids)

        filtered_burst_capture_directories = []
        for burst_name in burst_capture_directories:
            robot_id, row_id, camera_id, datetime_stamp, height_mm = burst_name.split("_")
            date = datetime_stamp.split("T")[0]
            if date == selected_date and row_id == selected_row_id and camera_id == selected_camera_id:
                filtered_burst_capture_directories.append(burst_name)

        burst_capture_index = st.sidebar.slider(
            "Select burst capture", min_value=1, max_value=len(filtered_burst_capture_directories)
        )

        burst_name = filtered_burst_capture_directories[burst_capture_index - 1]
        burst_capture_dir = os.path.join(data_dir, burst_name)
        robot_id, row_id, camera_id, datetime_stamp, height_mm = burst_name.split("_")
        timestamp = datetime_stamp.split("T")[1]
        st.sidebar.write(f"Burst capture time: {timestamp}")

        predict_image_filepath = self._get_predict_image_filepath(burst_capture_dir)
        image_filepaths = sorted(self._get_possible_image_filepaths(burst_capture_dir))
        image_index = st.sidebar.slider("Select target image", min_value=1, max_value=len(image_filepaths))

        try:
            image_filepath = pathlib.Path(image_filepaths[image_index - 1])
            annotation_filepath = image_filepath.with_suffix(".p2p")
            metadata_filepath = image_filepath.parent / "meta.json"

            with open(metadata_filepath) as f:
                metadata = json.load(f)
                st.sidebar.json(metadata)

            with open(annotation_filepath) as f:
                annotation = json.load(f)
                st.sidebar.json(annotation)

            image = cv2.imread(str(image_filepath))
            draw_annotation(
                image,
                str(annotation_filepath),
                crosshair_pos=(metadata["target_crosshair"][0], metadata["target_crosshair"][1]),
                downsample=1,
            )

            st.write(os.path.basename(image_filepath))
            st.markdown("""---""")
            st.image(image, channels="BGR")
        except Exception as exception:
            st.write("Failed to draw target image.")
            print(exception)

        if predict_image_filepath is not None:

            st.markdown("""---""")
            st.write(os.path.basename(predict_image_filepath))
            image = cv2.imread(predict_image_filepath)
            st.image(image, channels="BGR")

        st.markdown("""---""")
        st.write("All files in burst record:")
        message = f"```\n{burst_capture_dir}\n"
        for filename in sorted(os.listdir(burst_capture_dir)):
            message += "\t" + filename + "\n"
        message += "```"
        st.markdown(message)

    def _get_burst_capture_directories(self, date: str, row_index: str, target_camera: str) -> typing.List[str]:
        MAKA_DATA_DIR = os.getenv("MAKA_DATA_DIR")
        assert MAKA_DATA_DIR is not None, "MAKA_DATA_DIR is not set."
        data_dir = os.path.join(MAKA_DATA_DIR, "media/burst_recordings/full_burst_recordings")
        burst_capture_directories = []
        for burst_capture in os.listdir(data_dir):
            burst_capture_dir = os.path.join(data_dir, burst_capture)
            if "meta.json" in os.listdir(burst_capture_dir):
                burst_capture_directories.append(burst_capture_dir)
        return burst_capture_directories

    def _get_possible_dates(self, burst_capture_directories: typing.List[str]) -> typing.List[str]:
        possible_dates = []
        for burst_name in burst_capture_directories:
            robot_id, row_id, camera_id, datetime_stamp, height_mm = burst_name.split("_")
            date = datetime_stamp.split("T")[0]
            if date not in possible_dates:
                possible_dates.append(date)
        return possible_dates

    def _get_possible_rows(self, burst_capture_directories: typing.List[str], selected_date: str) -> typing.List[str]:
        possible_rows = []
        for burst_name in burst_capture_directories:
            robot_id, row_id, camera_id, datetime_stamp, height_mm = burst_name.split("_")
            date = datetime_stamp.split("T")[0]
            if selected_date == date and row_id not in possible_rows:
                possible_rows.append(row_id)
        return possible_rows

    def _get_possible_target_cameras(
        self, burst_capture_directories: typing.List[str], selected_date: str, selected_row_id: str
    ) -> typing.List[str]:
        possible_camera_ids = []
        for burst_name in burst_capture_directories:
            robot_id, row_id, camera_id, datetime_stamp, height_mm = burst_name.split("_")
            date = datetime_stamp.split("T")[0]
            if date == selected_date and row_id == selected_row_id and camera_id not in possible_camera_ids:
                possible_camera_ids.append(camera_id)
        return possible_camera_ids

    def _get_possible_image_filepaths(self, burst_capture_dir: str) -> typing.List[str]:
        filepaths = []
        for filename in os.listdir(burst_capture_dir):
            filepath = os.path.join(burst_capture_dir, filename)
            if filename.endswith(".png") and "target" in filename:
                if os.path.splitext(filename)[0] + ".p2p" in os.listdir(burst_capture_dir):
                    filepaths.append(filepath)
        return filepaths

    def _get_predict_image_filepath(self, burst_capture_dir: str) -> typing.Optional[str]:
        for filename in os.listdir(burst_capture_dir):
            filepath = os.path.join(burst_capture_dir, filename)
            if filename.endswith(".png") and "predict" in filename:
                return filepath

        return None

    def _check_burst(self, burst_capture_dir: str, min_images: int = 2) -> bool:
        if len(os.path.basename(burst_capture_dir).split("_")) != 5:
            return False

        if not os.path.exists(os.path.join(burst_capture_dir, "meta.json")):
            return False

        if len(self._get_possible_image_filepaths(burst_capture_dir)) < min_images:
            return False

        return True


def get_db_hash(db: CarbonRobotDBLite) -> str:
    return db._filepath


@st.cache(hash_funcs={CarbonRobotDBLite: get_db_hash})  # type: ignore
def get_db() -> CarbonRobotDBLite:
    return CarbonRobotDBLite(load_async=False)


data_viewer_map = {
    "Weed Accuracy Samples": WeedAccuracySamplesGraphDBViewer,
    "Laser Records": LaserRecordDBViewer,
    "Burst Capture Downloader": BurstCaptureDownloader,
    "Burst Capture Explorer": BurstCaptureExplorer,
}


def draw_requested_viewer(requested_viewer: str, db: CarbonRobotDBLite) -> None:
    if requested_viewer in data_viewer_map:
        data_viewer_map[requested_viewer](db)


def main() -> None:

    # start_http_server(62105)
    db = get_db()

    st.sidebar.title("Carbon Robot Viewers")

    requested_viewer = st.sidebar.selectbox("Choose Viewer To Display", tuple(x for x in data_viewer_map))

    draw_requested_viewer(requested_viewer, db)


if __name__ == "__main__":
    main()

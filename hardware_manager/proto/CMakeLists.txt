CompileProto(hardware_manager_service.proto GENERATED_PATH GOPKG proto/hardware_manager LANGS python mypy grpc_python go go-grpc cpp grpc)

add_library(hardware_manager_proto SHARED ${GENERATED_PATH}/hardware_manager_service.grpc.pb.cc ${GENERATED_PATH}/hardware_manager_service.pb.cc)
add_dependencies(hardware_manager_proto generate_hardware_manager_proto_hardware_manager_service)
target_compile_options(hardware_manager_proto PRIVATE "-w")
target_link_libraries(hardware_manager_proto PUBLIC grpc++ protobuf)

syntax = "proto3";

package hardware_manager;
option go_package = "proto/hardware_manager";

message PingRequest {
  uint32 x = 1;
}

message PingResponse {
  uint32 x = 1;
}

message GetRotaryTicksRequest {
}

message GetRotaryTicksResponse {
  uint64 timestamp_ms = 1;
  int64 front_left_ticks = 2;
  int64 front_right_ticks = 3;
  int64 back_left_ticks = 4;
  int64 back_right_ticks = 5;

  bool front_left_enabled = 6;
  bool front_right_enabled = 7;
  bool back_left_enabled = 8;
  bool back_right_enabled = 9;
}

message GetNextDistanceRequest {
  uint64 timestamp_ms = 1;
}

message GetNextDistanceResponse {
  uint64 timestamp_ms = 1;
  double distance = 2;
}

message GetNextVelocityRequest {
  uint64 timestamp_ms = 1;
}

message GetNextVelocityResponse {
  uint64 timestamp_ms = 1;
  double mm_per_ms = 2;
  bool lifted = 3;
}

message SetJimboxSpeedRequest {
  double target_speed = 1;
  double actual_ground_speed = 2;
}

message SetJimboxSpeedResponse {
  double speed_setpoint = 1;
}

message SetCruiseEnabledRequest {
  bool enabled = 1;
}

message SetCruiseEnabledResponse {
  bool success = 1;
}

message GetCruiseStatusRequest {}

message GetCruiseStatusResponse {
  bool enabled = 1;
  bool installed = 2;
  double speed = 3;
  bool allow_enable = 4;
}

message SetImplementStateRequest {
  bool active = 1;
  bool error = 2;
  optional string error_message = 3;
}
message SetImplementStateResponse {
}

message SetSafeStateEnforcementRequest {
  bool enforced = 1;
}
message SetSafeStateEnforcementResponse {
}
message GetTractorSafetyStateRequest {
  int64 timestamp_ms = 1;
}
message GetTractorSafetyStateResponse {
  int64 timestamp_ms = 1;
  bool is_safe = 2;
  bool enforced = 3;
}
message GetTractorIFStateRequest {
}
message GetTractorIFStateResponse {
  bool expected = 1;
  bool connected = 2;
}

message GetSafetyStatusRequest {}

message GetSafetyStatusResponse {
  bool lifted = 1;
  bool estopped = 2;
  bool in_cab_estopped = 3;
  bool left_estopped = 4;
  bool right_estopped = 5;
  bool laser_key = 6;
  bool interlock = 7;
  bool water_protect = 8;
  bool reset_required = 9;
  bool center_estop = 10;
  bool power_button_estop = 11;
  bool left_lpsu_interlock = 12;
  bool right_lpsu_interlock = 13;
  bool debug_mode = 14;
}

message GeoLLA {
  double lat = 1;
  double lng = 2;
  double alt = 3;
  int64 timestamp_ms = 4;
}

message GeoECEF {
  double x = 1;
  double y = 2;
  double z = 3;
  int64 timestamp_ms = 4;
}

message GetGPSDataRequest {
  bool validate = 1;
}

message GetGPSDataResponse {
  GeoLLA lla = 1;
  GeoECEF ecef = 2;
}
message GetNextGPSDataRequest {
  int64 timestamp_ms = 1;
}

message GetNextGPSDataResponse {
  GeoLLA lla = 1;
}
message GetNextRawGPSDataRequest {
  int64 timestamp_ms = 1;
}
message ValueWithAccuracy {
    double value = 1;
    double accuracy = 2;
};

enum CarrierPhaseSoln {
    NONE = 0;
    FLOATING = 1;
    FIXED = 2;
};
enum  FixType {
  NO_FIX = 0;
  DEAD_RECKONING_ONLY = 1;
  FIX_2D = 2;
  FIX_3D = 3;
  GNSS_DR = 4;
  TIME_ONLY = 5;
}

// Relative position information from secondary gps on dual gps board
message DualGpsData {
    bool gnss_valid = 1;
    bool diff_corrections = 2;
    bool is_moving_base = 3;
    CarrierPhaseSoln carrier_phase = 4;

    // Timestamp of this fix (if valid)
    uint64 timestamp_ms = 5;

    // North component of relative position vector (mm)
    optional ValueWithAccuracy north = 6;
    // East component of relative position vector (mm)
    optional ValueWithAccuracy east = 7;
    // Down component of relative position vector (mm)
    optional ValueWithAccuracy down = 8;
    // Relative position vector, the distance between antennas (mm)
    optional ValueWithAccuracy length = 9;
    // Heading of travel (°)
    optional ValueWithAccuracy heading = 10;
};

message GetNextRawGPSDataResponse {
  bool have_fix = 1;
  double latitude = 2;
  double longitude = 3;
  int32 num_sats = 4;
  float hdop = 5;
  // Only valid if fix is present
  uint64 timestamp_ms = 6;
  int32 height_mm = 7;
  bool have_approx_fix = 8;
  FixType fix_type = 9;
  bool gnss_valid = 10;
  bool diff_corrections = 11;
  CarrierPhaseSoln carrier_phase = 12;

  optional DualGpsData dual = 13;
}

message GetGPSFixedPosRequest {}
message GetGPSFixedPosResponse {
  float x_mm = 1;
  float y_mm = 2;
}

message GetManagedBoardErrorsRequest{}
message GetManagedBoardErrorsResponse {
  repeated string board = 1;
  bool encoder_error_flag = 2;
  string encoder_error_msg = 3;
  bool gps_has_fix = 4;
}

message GetSupervisoryStatusRequest {}
message GetReaperSupervisoryStatusRequest {}

message ChillerAlarms {
  bool low_level_in_tank = 1;
  bool high_circulating_fluid_discharge_temp = 2;
  bool circulating_fluid_discharge_temp_rise = 3;
  bool circulating_fluid_discharge_temp_drop = 4;
  bool high_circulating_fluid_return_temp = 5;
  bool circulating_fluid_discharge_pressure_rise = 6;
  bool circulating_fluid_discharge_pressure_drop = 7;
  bool high_compressor_suction_temp = 8;
  bool low_compressor_suction_temp = 9;
  bool low_super_heat_temp = 10;
  bool high_compressor_discharge_pressure = 11;
  bool refrigerant_circut_pressure_high_drop = 12;
  bool refrigerant_circut_pressure_low_rise = 13;
  bool refrigerant_circut_pressure_low_drop = 14;
  bool compressor_running_failure = 15;
  bool communication_error = 16;
  bool memory_error = 17;
  bool dc_line_fuse_cut = 18;
  bool circulating_fluid_discharge_temp_sensor_failure = 19;
  bool circulating_fluid_return_temp_sensor_failure = 20;
  bool circulating_fluid_suction_temp_sensor_failure = 21;
  bool circulating_fluid_discharge_pressure_sensor_failure = 22;
  bool compressor_discharge_pressure_sensor_failure = 23;
  bool compressor_suction_pressure_sensor_failure = 24;
  bool pump_maintenance = 25;
  bool fan_maintenance = 26;
  bool compressor_maintenance = 27;
  bool contact_input_1_signal_detection = 28;
  bool contact_input_2_signal_detection = 29;
  bool compressor_discharge_temp_sensor_failure = 30;
  bool compressor_discharge_temp_rise = 31;
  bool dustproof_filter_maintenance = 32;
  bool power_stoppage = 33;
  bool compressor_waiting = 34;
  bool fan_failure = 35;
  bool compressor_over_current = 36;
  bool pump_over_current = 37;
  bool air_exhaust_fan_stoppage = 38;
  bool incorrect_phase_error = 39;
  bool phase_board_over_current = 40;
}

message GetSupervisoryStatusResponse {
  bool water_protect_status = 1;
  bool main_contactor_status_fb = 2;
  bool power_good = 3;
  bool power_bad = 4;
  bool power_very_bad = 5;
  bool lifted_status = 6;
  bool temp_humidity_status = 7;
  bool tractor_power = 8;

  double ac_frequency = 9;
  double ac_voltage_a_b = 10;
  double ac_voltage_b_c = 11;
  double ac_voltage_a_c = 12;
  double ac_voltage_a = 13;
  double ac_voltage_b = 14;
  double ac_voltage_c = 15;
  int64 phase_power_w_3 = 16;
  int64 phase_power_va_3 = 17;
  double power_factor = 18;
  double server_cabinet_temp = 19;
  double server_cabinet_humidity = 20;
  double battery_voltage_12v = 21;

  bool temp_humidity_bypass_status = 22 [ deprecated = true ]; // Deprecated
  bool temp_bypass_status = 23;
  bool humidity_bypass_status = 24;
  bool temp_status = 25;
  bool humidity_status = 26;
  repeated bool btl_disabled = 27;
  repeated bool server_disabled = 28;
  repeated bool scanners_disabled = 29;
  bool wheel_encoder_disabled = 30;
  bool strobe_disabled = 31;
  bool gps_disabled = 32;
  bool main_contactor_disabled = 33;
  bool air_conditioner_disabled = 34;
  bool chiller_disabled = 35;

  double chiller_temp = 36;
  double chiller_flow = 37;
  double chiller_pressure = 38;
  double chiller_conductivity = 39;
  double chiller_set_temp = 40;
  ChillerAlarms chiller_alarms = 41;
}

message SetServerDisableRequest {
  int64 row_id = 1;
  bool disable = 2;
}

message SetServerDisableResponse {
  bool success = 1;
}

message SetBTLDisableRequest {
  int64 row_id = 1;
  bool disable = 2;
}

message SetBTLDisableResponse {
  bool success = 1;
}

message SetScannersDisableRequest {
  int64 row_id = 1;
  bool disable = 2;
}

message SetScannersDisableResponse {
  bool success = 1;
}

message SetWheelEncoderBoardDisableRequest {
  bool disable = 1;
}

message SetWheelEncoderBoardDisableResponse {
  bool success = 1;
}

message SetWheelEncoderDisableRequest {
  bool disable = 1;
  bool front = 2;
  bool left = 3;
}

message SetWheelEncoderDisableResponse {
  bool success = 1;
}

message SetStrobeDisableRequest {
  bool disable = 1;
}

message SetStrobeDisableResponse {
  bool success = 1;
}

message SetGPSDisableRequest {
  bool disable = 1;
}

message SetGPSDisableResponse {
  bool success = 1;
}

message CommandComputerPowerCycleRequest {
}

message CommandComputerPowerCycleResponse {
  bool success = 1;
}

message SuicideSwitchRequest {
}

message SuicideSwitchResponse {
  bool success = 1;
}

message SetMainContactorDisableRequest {
  bool disable = 1;
}

message SetMainContactorDisableResponse {
  bool success = 1;
}

message SetAirConditionerDisableRequest {
  bool disable = 1;
}

message SetAirConditionerDisableResponse {
  bool success = 1;
}

message SetChillerDisableRequest {
  bool disable = 1;
}

message SetChillerDisableResponse {
  bool success = 1;
}

message SetTempBypassDisableRequest {
  bool disable = 1;
}

message SetTempBypassDisableResponse {
  bool success = 1;
}

message SetHumidityBypassDisableRequest {
  bool disable = 1;
}

message SetHumidityBypassDisableResponse {
  bool success = 1;
}

message GetAvailableUSBStorageRequest {}

message GetAvailableUSBStorageResponse {
  float used = 1;
  bool success = 2;
  bool usb_available = 3;
}

message GetReadyRequest {}
message GetReadyResponse {
  bool ready = 1;
}

message Get240vUptimeRequest{}
message Get240vUptimeResponse{
  int64 uptime_s = 1;
}

message GetDeltaTravelMMRequest{
  string id = 1; //uuid
}
message GetDeltaTravelMMResponse{
  double delta_mm = 1;
}

message GetRuntimeRequest {}
message GetRuntimeResponse {
  uint32 runtime_240v = 1;
}

message GetWheelEncoderResolutionRequest {}
message GetWheelEncoderResolutionResponse {
  uint32 resolution = 1;
}

message StrobeSettings {
  optional uint32 exposure_us = 1;
  optional uint32 period_us = 2;
  optional uint32 targets_per_predict_ratio = 3;
}
message SetStrobeSettingsResponse {
  bool success = 1;
}

message GetStrobeSettingsRequest {
}

message EnvironmentalSensorData {
    double temperature_c = 1;
    double humidity_rh = 2;
    double pressure_hpa = 3;
}

message CoolantSensorData {
    double temperature_c = 1;
    double pressure_kpa = 2;
}

enum NetworkLinkSpeed {
    UNKNOWN = 0;
    SPEED_10M_HALF = 1;
    SPEED_10M_FULL = 2;
    SPEED_100M_HALF = 3;
    SPEED_100M_FULL = 4;
    SPEED_1G_FULL = 5;
    SPEED_2G5_FULL = 6;
    SPEED_5G_FULL = 7;
    SPEED_10G_FULL = 8;
};

message NetworkPortState {
    bool link_up = 1;

    NetworkLinkSpeed actual_link_speed = 2;
    NetworkLinkSpeed expected_link_speed = 3;
};

message ReaperPcSensorData {
    double temperature_cpu_core_c = 1;
    double temperature_system_c = 2;
    optional double temperature_gpu_1_c = 3;
    optional double temperature_gpu_2_c = 4;

    double psu_12v = 5;
    double psu_5v = 6;
    double psu_3v3 = 7;

    // CPU load average
    double load = 8;
    // system uptime, in seconds
    uint32 uptime = 9;

    // Memory (RAM) utilization
    double ram_usage_percent = 10;
    // Disk space utilization
    double disk_usage_percent = 11;

    // Link state for scanner PCBs (A, B)
    repeated NetworkPortState scanner_link = 12;
    // Link state for target cam (A, B)
    repeated NetworkPortState target_cam_link = 13;
    // Link state for predict cam
    NetworkPortState predict_cam_link = 14;
    // IPMI port link state
    NetworkPortState ipmi_link = 15;
    // Global network uplink port link state
    NetworkPortState global_link = 16;
    // External (internet) uplink
    NetworkPortState ext_link = 17;
}

// Info about the BWT laser connected to Reaper scanner
message ReaperScannerLaserStatus {
    // Laser model number
    string model = 1;
    // Serial number reported by laser
    string sn = 2;
    // Rated laser power (W)
    uint32 rated_power = 3;

    // Internal temperature
    double temperature_c = 4;
    // Laser humidity
    double humidity = 5;
    // Current through laser diodes
    double laser_current_ma = 6;

    // Laser faults (if any)
    repeated string faults = 7;
}

// Information for each of the motors/controllers
message ReaperScannerMotorData {
    // Serial number of motor controller
    string controller_sn = 1;

    // Output/driver stage temperature
    double temperature_output_c = 2;

    // Motor supply voltage
    double motor_supply_v = 3;
    // Instantaneous motor current
    double motor_current_a = 4;

    // Current encoder position, ticks
    int64 encoder_position = 5;
};

// Sensor data readings for the Reaper scanners
message ReaperScannerSensorData {
    // Scanner assembly serial number
    string scanner_sn = 1;

    // Whether power to the scanner is on
    bool power_on = 2;
    // Scanner current consumption
    double current_a = 3;
    // Whether fuse to scanner is tripped
    bool fuse_tripped = 4;

    // Temperature of collimator (°C)
    double temperature_collimator_c = 5;
    // laser fiber connection temperature (°C)
    double temperature_fiber_c = 6;
    // Approximate laser power reading from photodiode
    double laser_power_w = 7;
    // Raw laser power reading in millivolts
    double laser_power_raw_mv = 16;

    // Whether laser is connected
    bool laser_connected = 8;
    // Additional laser status information
    optional ReaperScannerLaserStatus laser_status = 9;

    // If target cam is connected
    bool target_connected = 10;
    // Target camera's serial number
    optional string target_sn = 11;
    // Temperature of target camera
    optional double temperature_target_c = 12;

    // Individual motor controller info
    optional ReaperScannerMotorData motor_pan = 13;
    optional ReaperScannerMotorData motor_tilt = 14;

  // whether power to the target cam (controlled via scanner) is enabled
  bool target_cam_power_enabled = 15;
}

// All readings from stuff in the center enclosure
// Similar to GetSupervisoryStatusResponse
message ReaperCenterEnclosureData {
  bool water_protect_status = 1;
  bool main_contactor_status_fb = 2;
  bool power_good = 3;
  bool power_bad = 4;
  bool power_very_bad = 5;

  bool lifted_status = 6;
  bool tractor_power = 7;

  double ac_frequency = 8;
  double ac_voltage_a_b = 9;
  double ac_voltage_b_c = 10;
  double ac_voltage_a_c = 11;
  double ac_voltage_a = 12;
  double ac_voltage_b = 13;
  double ac_voltage_c = 14;
  int64 phase_power_w_3 = 15;
  int64 phase_power_va_3 = 16;
  double power_factor = 17;

  double server_cabinet_temp = 18;
  double server_cabinet_humidity = 19;

  // ISOBUS voltage
  double battery_voltage_12v = 20;

  bool wheel_encoder_disabled = 21;
  bool strobe_disabled = 22;
  bool gps_disabled = 23;
  bool main_contactor_disabled = 24;
  bool air_conditioner_disabled = 25;
  bool chiller_disabled = 26;

  double chiller_temp = 27;
  double chiller_flow = 28;
  double chiller_pressure = 29;
  double chiller_conductivity = 30;
  double chiller_set_temp = 31;
  double chiller_heat_transfer = 32;
  double chiller_fluid_delta_temp = 33;
  ChillerAlarms chiller_alarms = 34;
}

// Sensor readings from a single module
message ReaperModuleSensorData {
    // module ID from which this data was read
    int32 module_id = 1;
    // serial number of the ID from which this data was read
    optional string module_sn = 2;

    // Sensor on MCB (general enclosure area)
    EnvironmentalSensorData enviro_enclosure = 3;
    // Remote sensor in PC enclosure area
    EnvironmentalSensorData enviro_pc = 4;
    // Coolant inlet sensor
    CoolantSensorData coolant_inlet = 5;
    // Coolant outlet sensor
    CoolantSensorData coolant_outlet = 6;

    // Strobe board temperature
    double strobe_temperature_c = 7;
    // Strobe capacitor voltage
    double strobe_cap_voltage = 8;
    // Current (averaged) through the LEDs
    double strobe_current = 9;

    // Detailed information from sensors on the module PC
    optional ReaperPcSensorData pc = 10;
    // Info about each of the scanners and target cams
    optional ReaperScannerSensorData scanner_a = 11;
    optional ReaperScannerSensorData scanner_b = 12;

  bool pc_power_enabled = 13;
  bool lasers_power_enabled = 14;
  bool predict_cam_power_enabled = 15;

  // whether the relay for BTL power supply is on
  bool strobe_power_enabled = 16;
  // whether software is requesting strobe light triggering
  bool strobe_enabled = 17;
}

// Request center enclosure sensors
message GetReaperEnclosureSensorsRequest {
}
message GetReaperEnclosureSensorsResponse {
  ReaperCenterEnclosureData sensors = 1;
}

// Request data for one or more modules
message GetReaperModuleSensorsRequest {
  // Module IDs to return data for; empty list = all assigned modules
  repeated uint32 module_ids = 1;
};
message GetReaperModuleSensorsResponse {
  repeated ReaperModuleSensorData module_sensors = 1;
}

// Set the switched power to one or both scanners in a module
message SetReaperScannerPowerRequest {
    uint32 module_id = 1;

    optional bool scanner_a_power = 2;
    optional bool scanner_b_power = 3;
}
message SetReaperScannerPowerResponse {
    bool success = 1;
}

// Set switched target cam power (via scanner)
message SetReaperTargetPowerRequest {
  uint32 module_id = 1;

  optional bool target_a_power = 2;
  optional bool target_b_power = 3;
}
message SetReaperTargetPowerResponse { bool success = 1; }

// Set the switched power to a module's predict cam
message SetReaperPredictCamPowerRequest {
    uint32 module_id = 1;
    bool enabled = 2;
}
message SetReaperPredictCamPowerResponse {
    bool success = 1;
}

// Set the strobe configuration for one or all modules
message SetReaperStrobeConfigRequest {
    // Module ID to update settings for, otherwise all modules
    optional uint32 module_id = 1;
    StrobeSettings settings = 2;
}
message SetReaperStrobeConfigResponse {
    bool success = 1;
}
// Set whether strobe firing is enabled
message SetReaperStrobeEnableRequest {
    // Module IDs to set strobe state for, or empty for ALL known modules
    repeated uint32 module_ids = 1;

    bool enabled = 2;

    // if set, how long to disable strobes for, otherwise it's permanent
    optional uint32 duration_ms = 3;
}
message SetReaperStrobeEnableResponse {
    bool success = 1;
}

// Set module PC 240V input power
message SetReaperModulePcPowerRequest {
    uint32 module_id = 1;
    bool enabled = 2;
}
message SetReaperModulePcPowerResponse {
    bool success = 1;
}

// Set module lasers 240V input power
message SetReaperModuleLaserPowerRequest {
    uint32 module_id = 1;
    bool enabled = 2;
}
message SetReaperModuleLaserPowerResponse {
    bool success = 1;
}

// Set BTL (strobe board) 240V input power
message SetReaperModuleStrobePowerRequest {
    uint32 module_id = 1;
    bool enabled = 2;
}
message SetReaperModuleStrobePowerResponse {
    bool success = 1;
}

message IdentifyModuleRequest {
  string to_id_ip = 1;
  repeated string turn_off_ips = 2;
}

message IdentifyModuleResponse {
  bool success = 1;
}

service HardwareManagerService {
  rpc Ping(PingRequest) returns (PingResponse) {}
  rpc GetReady(GetReadyRequest) returns (GetReadyResponse) {}

  // Rotary
  rpc GetNextDistance(GetNextDistanceRequest) returns (GetNextDistanceResponse) {}
  rpc GetNextVelocity(GetNextVelocityRequest) returns (GetNextVelocityResponse) {}
  rpc GetRotaryTicks(GetRotaryTicksRequest) returns (GetRotaryTicksResponse) {}
  rpc GetDeltaTravelMM(GetDeltaTravelMMRequest) returns (GetDeltaTravelMMResponse) {}
  rpc GetWheelEncoderResolution(GetWheelEncoderResolutionRequest) returns (GetWheelEncoderResolutionResponse) {}

  // Safety
  rpc GetSafetyStatus(GetSafetyStatusRequest) returns (GetSafetyStatusResponse) {}

  // GPS
  rpc GetGPSData(GetGPSDataRequest) returns (GetGPSDataResponse) {}
  rpc GetNextGPSData(GetNextGPSDataRequest) returns (GetNextGPSDataResponse) {}
  rpc GetNextRawGPSData(GetNextRawGPSDataRequest) returns (GetNextRawGPSDataResponse) {}
  rpc GetGPSFixedPos(GetGPSFixedPosRequest) returns (GetGPSFixedPosResponse) {}

  // Strobe
  rpc SetStrobeSettings(StrobeSettings) returns (SetStrobeSettingsResponse) {}
  rpc GetStrobeSettings(GetStrobeSettingsRequest) returns (StrobeSettings) {}

  // Managed Boards
  rpc GetManagedBoardErrors(GetManagedBoardErrorsRequest) returns (GetManagedBoardErrorsResponse) {}

  // Supervisory PLC
  rpc GetSupervisoryStatus(GetSupervisoryStatusRequest) returns (GetSupervisoryStatusResponse) {}
  rpc GetReaperSupervisoryStatus(GetReaperSupervisoryStatusRequest) returns (ReaperCenterEnclosureData) {}

  rpc SetServerDisable(SetServerDisableRequest) returns (SetServerDisableResponse) {}
  rpc SetBTLDisable(SetBTLDisableRequest) returns (SetBTLDisableResponse) {}
  rpc SetScannersDisable(SetScannersDisableRequest) returns (SetScannersDisableResponse) {}
  rpc SetWheelEncoderBoardDisable(SetWheelEncoderBoardDisableRequest) returns (SetWheelEncoderBoardDisableResponse) {}
  rpc SetWheelEncoderDisable(SetWheelEncoderDisableRequest) returns (SetWheelEncoderDisableResponse) {}
  rpc SetGPSDisable(SetGPSDisableRequest) returns (SetGPSDisableResponse) {}
  rpc SuicideSwitch(SuicideSwitchRequest) returns (SuicideSwitchResponse) {}
  rpc CommandComputerPowerCycle(CommandComputerPowerCycleRequest) returns (CommandComputerPowerCycleResponse) {}
  rpc SetMainContactorDisable(SetMainContactorDisableRequest) returns (SetMainContactorDisableResponse) {}
  rpc SetStrobeDisable(SetStrobeDisableRequest) returns (SetStrobeDisableResponse) {}
  rpc SetAirConditionerDisable(SetAirConditionerDisableRequest) returns (SetAirConditionerDisableResponse) {}
  rpc SetChillerDisable(SetChillerDisableRequest) returns (SetChillerDisableResponse) {}
  rpc SetTempBypassDisable(SetTempBypassDisableRequest) returns (SetTempBypassDisableResponse) {}
  rpc SetHumidityBypassDisable(SetHumidityBypassDisableRequest) returns (SetHumidityBypassDisableResponse) {}
  rpc Get240vUptime(Get240vUptimeRequest) returns (Get240vUptimeResponse) {}
  rpc GetRuntime(GetRuntimeRequest) returns (GetRuntimeResponse) {}

  // USB
  rpc GetAvailableUSBStorage(GetAvailableUSBStorageRequest) returns (GetAvailableUSBStorageResponse) {}

  // Jimbox
  rpc SetJimboxSpeed(SetJimboxSpeedRequest) returns (SetJimboxSpeedResponse) {}
  rpc SetCruiseEnabled(SetCruiseEnabledRequest) returns (SetCruiseEnabledResponse) {}
  rpc GetCruiseStatus(GetCruiseStatusRequest) returns (GetCruiseStatusResponse) {}

  // Tractor
  rpc SetImplementStateOnTractor(SetImplementStateRequest) returns (SetImplementStateResponse) {}
  rpc SetSafeStateEnforcement(SetSafeStateEnforcementRequest) returns (SetSafeStateEnforcementResponse) {}
  rpc GetTractorSafetyState(GetTractorSafetyStateRequest) returns (GetTractorSafetyStateResponse) {}
  rpc GetTractorIFState(GetTractorIFStateRequest) returns (GetTractorIFStateResponse) {}

  // Hardware/board status
  rpc GetReaperEnclosureSensors(GetReaperEnclosureSensorsRequest) returns (GetReaperEnclosureSensorsResponse) {}
  rpc GetReaperModuleSensors(GetReaperModuleSensorsRequest) returns (GetReaperModuleSensorsResponse) {}

  rpc SetReaperScannerPower(SetReaperScannerPowerRequest) returns (SetReaperScannerPowerResponse) {}
  rpc SetReaperTargetPower(SetReaperTargetPowerRequest) returns (SetReaperTargetPowerResponse) {}
  rpc SetReaperPredictCamPower(SetReaperPredictCamPowerRequest) returns (SetReaperPredictCamPowerResponse) {}
  rpc SetReaperStrobeConfig(SetReaperStrobeConfigRequest) returns (SetReaperStrobeConfigResponse) {}
  rpc SetReaperStrobeEnable(SetReaperStrobeEnableRequest) returns (SetReaperStrobeEnableResponse) {}
  rpc SetReaperModulePcPower(SetReaperModulePcPowerRequest) returns (SetReaperModulePcPowerResponse) {}
  rpc SetReaperModuleLaserPower(SetReaperModuleLaserPowerRequest) returns (SetReaperModuleLaserPowerResponse) {}
  rpc SetReaperModuleStrobePower(SetReaperModuleStrobePowerRequest) returns (SetReaperModuleStrobePowerResponse) {}

  rpc IdentifyModule(IdentifyModuleRequest) returns (IdentifyModuleResponse) {}
}

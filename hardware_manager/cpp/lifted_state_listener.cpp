#include "hardware_manager/cpp/lifted_state_listener.h"
#include <config/client/cpp/config_subscriber.hpp>
#include <hardware_manager/cpp/grpc_client.h>
#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <lib/common/cpp/exceptions.h>

#include <chrono>
#include <condition_variable>
#include <mutex>

#include <spdlog/spdlog.h>

namespace hardware_manager {
LiftedStateListener::LiftedStateListener()
    : id_(0), is_lifted_(false), lifted_state_thread_(&LiftedStateListener::lifted_state_loop, this) {}
LiftedStateListener::~LiftedStateListener() { lifted_state_thread_.join(); }
uint32_t LiftedStateListener::add_callback(Callback callback) {
  uint32_t id = ++id_;
  std::unique_lock<std::mutex> lk(mut_);
  callbacks_.emplace(id, callback);
  return id;
}
void LiftedStateListener::unregister_callback(uint32_t callback) {
  std::unique_lock<std::mutex> lk(mut_);
  callbacks_.erase(callback);
}

void LiftedStateListener::lifted_state_loop() {
  std::string command_addr = carbon::config::make_robot_local_addr(HardwareManagerClient::DEFAULT_PORT);
  HardwareManagerClient hmc(command_addr);
  std::condition_variable cv;
  auto bse(lib::common::bot::BotStopHandler::get().create_scoped_event("lifted_state_listener",
                                                                       [&cv]() { cv.notify_all(); }));
  while (!bse.is_stopped()) {
    try {
      auto status = hmc.get_implement_status();
      bool lifted = status.lifted();
      if (lifted != is_lifted_) {
        spdlog::info("Detected lifted state change to {}.", lifted);
        std::unique_lock<std::mutex> lk(mut_);
        for (auto &cb : callbacks_) {
          cb.second(lifted);
        }
      }
      is_lifted_ = lifted;
    } catch (const maka_error &ex) {
      spdlog::warn("Failed to get lifted status.");
    }
    {
      std::unique_lock<std::mutex> lk(mut_);
      cv.wait_for(lk, std::chrono::seconds(5));
    }
  }
}

LiftedStateListener *LiftedStateListener::get() {
  static LiftedStateListener lsl;
  return &lsl;
}
} // namespace hardware_manager
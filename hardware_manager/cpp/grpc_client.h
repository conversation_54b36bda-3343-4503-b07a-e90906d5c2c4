#pragma once

#include "generated/hardware_manager/proto/hardware_manager_service.grpc.pb.h"
#include "generated/hardware_manager/proto/hardware_manager_service.pb.h"

#include <chrono>
#include <grpcpp/grpcpp.h>
#include <memory>
#include <mutex>
#include <optional>
#include <string>

namespace hardware_manager {

class HardwareManagerClient {
protected:
  std::string addr_;
  std::shared_ptr<grpc::Channel> channel_;
  std::shared_ptr<HardwareManagerService::Stub> stub_;
  uint32_t max_backoff_ms_;
  double backoff_multiplier_;
  std::mutex channel_setup_mutex_;

public:
  static const uint32_t DEFAULT_PORT = 61006;
  HardwareManagerClient(const std::string &addr = "127.0.0.1:61006", uint32_t max_backoff_ms = 30000,
                        double backoff_multiplier = 1.5);

  bool await_connection(uint64_t timeout_ms = 0);
  void reset();
  std::string get_addr() const;
  void ping();
  GetGPSDataResponse get_gps_data(bool validate = false);
  GetSafetyStatusResponse get_implement_status();
  GetNextVelocityResponse get_next_velocity();
  GetNextDistanceResponse get_next_distance(int64_t timestamp_ms);
  std::unique_ptr<GetNextRawGPSDataResponse>
  next_raw_gps(int64_t timestamp_ms, std::optional<std::chrono::milliseconds> timeout = std::nullopt);
  bool power_cycle_cam(uint32_t module_id, const std::string &cam_name,
                       std::optional<std::chrono::milliseconds> timeout = std::nullopt);

private:
  grpc::Status exec_grpc(std::function<grpc::Status()> func);
  void setup_grpc(bool reconnect_if_down = true);
  std::shared_ptr<HardwareManagerService::Stub> get_grpc_stub(bool reconnect_if_down = true);
  void reset_stub();
};

} // namespace hardware_manager

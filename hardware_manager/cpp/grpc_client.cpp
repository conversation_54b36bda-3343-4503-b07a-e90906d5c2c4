#include "grpc_client.h"

#include <chrono>
#include <fmt/format.h>
#include <functional>
#include <set>
#include <spdlog/spdlog.h>

#include "lib/common/cpp/exceptions.h"

namespace hardware_manager {

constexpr std::chrono::milliseconds kDefaultDeadlineMs(500);

HardwareManagerClient::HardwareManagerClient(const std::string &addr, uint32_t max_backoff_ms,
                                             double backoff_multiplier)
    : addr_(addr), channel_(nullptr), max_backoff_ms_(max_backoff_ms), backoff_multiplier_(backoff_multiplier) {}

void HardwareManagerClient::setup_grpc(bool reconnect_if_down) {
  // Not Thread Safe
  if (this->channel_ != nullptr && this->channel_->GetState(true) != GRPC_CHANNEL_READY) {
    this->reset_stub();
  }
  if (this->channel_ == nullptr) {
    if (!reconnect_if_down) {
      return;
    }
    this->channel_ = grpc::CreateChannel(this->addr_, grpc::InsecureChannelCredentials());
  }
  if (this->stub_ == nullptr) {
    this->stub_ = std::make_shared<HardwareManagerService::Stub>(this->channel_);
  }
}

std::shared_ptr<HardwareManagerService::Stub> HardwareManagerClient::get_grpc_stub(bool reconnect_if_down) {
  std::lock_guard<std::mutex> lock(this->channel_setup_mutex_);
  this->setup_grpc(reconnect_if_down);
  return this->stub_;
}

void HardwareManagerClient::reset() {
  std::lock_guard<std::mutex> lock(this->channel_setup_mutex_);
  return this->reset_stub();
}

void HardwareManagerClient::reset_stub() {
  // Not Thread Safe
  this->stub_ = nullptr;
  this->channel_ = nullptr;
}

grpc::Status HardwareManagerClient::exec_grpc(std::function<grpc::Status()> func) {
  try {
    return func();
  } catch (const std::exception &ex) { // TODO Better Exception Handling?
    this->reset_stub();
    throw;
  }
}

bool HardwareManagerClient::await_connection(uint64_t timeout_ms) {
  auto start = std::chrono::steady_clock::now();
  std::chrono::milliseconds chrono_timeout_ms{timeout_ms};
  uint32_t backoff_ms = 1000;
  std::chrono::duration<double> duration_s;
  while (true) {
    duration_s = std::chrono::steady_clock::now() - start;
    if (timeout_ms != 0 && duration_s > chrono_timeout_ms) {
      return false;
    }
    try {
      this->ping();
      return true;
    } catch (const std::exception &ex) {
    }
    backoff_ms = (uint32_t)(backoff_ms * this->backoff_multiplier_);
    backoff_ms = backoff_ms <= this->max_backoff_ms_ ? backoff_ms : this->max_backoff_ms_;

    if (timeout_ms != 0 &&
        (duration_s > chrono_timeout_ms || std::chrono::milliseconds(backoff_ms) > (chrono_timeout_ms - duration_s))) {
      return false;
    }
    spdlog::warn("Awaiting Connection to Hardware Manager with Backoff: {} ms", backoff_ms);
    std::this_thread::sleep_for(std::chrono::milliseconds(backoff_ms));
  }
}

void HardwareManagerClient::ping() {
  grpc::ClientContext context;
  context.set_deadline(std::chrono::system_clock::now() + kDefaultDeadlineMs);
  PingRequest req;
  PingResponse resp;
  req.set_x(42);
  auto stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(std::bind(&HardwareManagerService::Stub::Ping, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw maka_error(fmt::format("Invalid Ping Status Code: {}", status.error_code()));
  }

  if (resp.x() != 42) {
    throw maka_error(fmt::format("Invalid Pong Value: {}, Expected: {}", resp.x(), 42));
  }
}

std::string HardwareManagerClient::get_addr() const { return addr_; }

GetGPSDataResponse HardwareManagerClient::get_gps_data(bool validate) {
  grpc::ClientContext context;
  context.set_deadline(std::chrono::system_clock::now() + kDefaultDeadlineMs);
  GetGPSDataRequest req;
  req.set_validate(validate);
  GetGPSDataResponse resp;
  auto stub = this->get_grpc_stub();
  grpc::Status status =
      this->exec_grpc(std::bind(&HardwareManagerService::Stub::GetGPSData, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw maka_error(fmt::format("Invalid GetGPSData Status Code {}: {}", status.error_code(), status.error_message()),
                     false);
  }
  return resp;
}

GetSafetyStatusResponse HardwareManagerClient::get_implement_status() {
  grpc::ClientContext context;
  context.set_deadline(std::chrono::system_clock::now() + kDefaultDeadlineMs);
  GetSafetyStatusRequest req;
  GetSafetyStatusResponse resp;
  auto stub = this->get_grpc_stub();
  grpc::Status status =
      this->exec_grpc(std::bind(&HardwareManagerService::Stub::GetSafetyStatus, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw maka_error(
        fmt::format("Invalid GetSafetyStatus Status Code {}: {}", status.error_code(), status.error_message()), false);
  }
  return resp;
}

GetNextVelocityResponse HardwareManagerClient::get_next_velocity() {
  grpc::ClientContext context;
  context.set_deadline(std::chrono::system_clock::now() + kDefaultDeadlineMs);
  GetNextVelocityRequest req;
  GetNextVelocityResponse resp;
  auto stub = this->get_grpc_stub();
  grpc::Status status =
      this->exec_grpc(std::bind(&HardwareManagerService::Stub::GetNextVelocity, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw maka_error(
        fmt::format("Invalid GetNextVelocity Status Code {}: {}", status.error_code(), status.error_message()), false);
  }
  return resp;
}

GetNextDistanceResponse HardwareManagerClient::get_next_distance(int64_t timestamp_ms) {
  grpc::ClientContext context;
  context.set_deadline(std::chrono::system_clock::now() + kDefaultDeadlineMs);
  GetNextDistanceRequest req;
  req.set_timestamp_ms(timestamp_ms);
  GetNextDistanceResponse resp;
  auto stub = this->get_grpc_stub();
  grpc::Status status =
      this->exec_grpc(std::bind(&HardwareManagerService::Stub::GetNextDistance, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw maka_error(
        fmt::format("Invalid GetNextDistance Status Code {}: {}", status.error_code(), status.error_message()), false);
  }
  return resp;
}
std::unique_ptr<GetNextRawGPSDataResponse>
HardwareManagerClient::next_raw_gps(int64_t timestamp_ms, std::optional<std::chrono::milliseconds> timeout) {
  grpc::ClientContext context;
  context.set_deadline(std::chrono::system_clock::now() + timeout.value_or(kDefaultDeadlineMs));
  GetNextRawGPSDataRequest req;
  req.set_timestamp_ms(timestamp_ms);
  std::unique_ptr<GetNextRawGPSDataResponse> resp = std::make_unique<GetNextRawGPSDataResponse>();
  auto stub = this->get_grpc_stub();
  grpc::Status status =
      this->exec_grpc(std::bind(&HardwareManagerService::Stub::GetNextRawGPSData, stub, &context, req, resp.get()));

  if (status.error_code() != grpc::StatusCode::OK) {
    spdlog::warn("Invalid GetNextRawGPS Status Code {}: {}", status.error_code(), status.error_message());
    return nullptr;
  }
  return resp;
}
bool HardwareManagerClient::power_cycle_cam(uint32_t module_id, const std::string &cam_name,
                                            std::optional<std::chrono::milliseconds> timeout) {
  grpc::ClientContext context_off, context_on;
  auto stub = this->get_grpc_stub();
  context_off.set_deadline(std::chrono::system_clock::now() + timeout.value_or(kDefaultDeadlineMs));
  if (cam_name.rfind("predict", 0) == 0) {
    SetReaperPredictCamPowerRequest req;
    req.set_module_id(module_id);
    req.set_enabled(false);
    SetReaperPredictCamPowerResponse resp;
    grpc::Status status = this->exec_grpc(
        std::bind(&HardwareManagerService::Stub::SetReaperPredictCamPower, stub, &context_off, req, &resp));
    if (status.error_code() != grpc::StatusCode::OK) {
      spdlog::warn("failed to power off {} on module {}, Status Code {}: {}", cam_name, module_id, status.error_code(),
                   status.error_message());
      return false;
    }
    if (!resp.success()) {
      spdlog::warn("failed to power off {} on module {}", cam_name, module_id);
      return false;
    }
    context_on.set_deadline(std::chrono::system_clock::now() + timeout.value_or(kDefaultDeadlineMs));
    req.set_enabled(true);
    status = this->exec_grpc(
        std::bind(&HardwareManagerService::Stub::SetReaperPredictCamPower, stub, &context_on, req, &resp));
    if (status.error_code() != grpc::StatusCode::OK) {
      spdlog::warn("failed to power on {} on module {}, Status Code {}: {}", cam_name, module_id, status.error_code(),
                   status.error_message());
      return false;
    }
    if (!resp.success()) {
      spdlog::warn("failed to power on {} on module {}", cam_name, module_id);
      return false;
    }
    return true;
  } else if (cam_name.rfind("target", 0) == 0) {
    SetReaperTargetPowerRequest req;
    req.set_module_id(module_id);
    auto index = std::stoi(cam_name.substr(6)) % 2;
    if (index == 1) {
      req.set_target_a_power(false);
    } else {
      req.set_target_b_power(false);
    }
    SetReaperTargetPowerResponse resp;
    grpc::Status status =
        this->exec_grpc(std::bind(&HardwareManagerService::Stub::SetReaperTargetPower, stub, &context_off, req, &resp));
    if (status.error_code() != grpc::StatusCode::OK) {
      spdlog::warn("failed to power off {} on module {}, Status Code {}: {}", cam_name, module_id, status.error_code(),
                   status.error_message());
      return false;
    }
    if (!resp.success()) {
      spdlog::warn("failed to power off {} on module {}", cam_name, module_id);
      return false;
    }
    if (index == 1) {
      req.set_target_a_power(true);
    } else {
      req.set_target_b_power(true);
    }
    context_on.set_deadline(std::chrono::system_clock::now() + timeout.value_or(kDefaultDeadlineMs));

    status =
        this->exec_grpc(std::bind(&HardwareManagerService::Stub::SetReaperTargetPower, stub, &context_on, req, &resp));
    if (status.error_code() != grpc::StatusCode::OK) {
      spdlog::warn("failed to power on {} on module {}, Status Code {}: {}", cam_name, module_id, status.error_code(),
                   status.error_message());
      return false;
    }
    if (!resp.success()) {
      spdlog::warn("failed to power on {} on module {}", cam_name, module_id);
      return false;
    }
  } else {
    spdlog::warn("Unknown camera type {}", cam_name);
    return false;
  }
  return true;
}

} // namespace hardware_manager
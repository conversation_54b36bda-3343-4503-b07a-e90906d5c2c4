#include <atomic>
#include <functional>
#include <memory>
#include <mutex>
#include <thread>
#include <unordered_map>

namespace hardware_manager {
class LiftedStateListener {
public:
  ~LiftedStateListener();
  using Callback = std::function<void(bool)>;
  static LiftedStateListener *get();
  uint32_t add_callback(Callback callback);
  void unregister_callback(uint32_t callback);

private:
  LiftedStateListener();
  void lifted_state_loop();

  std::atomic<uint32_t> id_;
  std::mutex mut_;
  bool is_lifted_;
  std::unordered_map<uint32_t, Callback> callbacks_;

  std::thread lifted_state_thread_;
};
} // namespace hardware_manager
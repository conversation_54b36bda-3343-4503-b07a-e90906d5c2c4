from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Union, cast

import grpc

import generated.hardware_manager.proto.hardware_manager_service_pb2 as hardware_manager_pb
import generated.hardware_manager.proto.hardware_manager_service_pb2_grpc as hardware_manager_grpc
from lib.common.generation import is_reaper, is_slayer
from lib.common.gps.robot_pose import RelPos
from lib.common.logging import get_logger
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.units.duration import Duration
from lib.common.units.speed import ZERO_SPEED, Speed
from lib.drivers.nanopb.benjamin_gps_board.benjamin_gps_board_connector import (
    CarrierSolution,
    DualGpsData,
    FixFlags,
    FixType,
    Position,
)

LOG = get_logger(__name__)


@dataclass
class SafetyStatus:
    lifted: bool = False
    estopped: bool = False
    in_cab_estopped: bool = False
    left_estopped: bool = False
    right_estopped: bool = False
    laser_key: bool = False
    interlock: bool = False
    water_protect: bool = False
    reset_required: bool = False
    center_estop: bool = False
    power_button_estop: bool = False
    left_lpsu_interlock: bool = False
    right_lpsu_interlock: bool = False
    debug_mode: bool = False

    def __ior__(self, rhs: "SafetyStatus") -> "SafetyStatus":
        self.lifted |= rhs.lifted
        self.estopped |= rhs.estopped
        self.in_cab_estopped |= rhs.in_cab_estopped
        self.left_estopped |= rhs.left_estopped
        self.right_estopped |= rhs.right_estopped
        self.laser_key |= rhs.laser_key
        self.interlock |= rhs.interlock
        self.water_protect |= rhs.water_protect
        self.reset_required |= rhs.reset_required
        self.center_estop |= rhs.center_estop
        self.power_button_estop |= rhs.power_button_estop
        self.left_lpsu_interlock |= rhs.left_lpsu_interlock
        self.right_lpsu_interlock |= rhs.right_lpsu_interlock
        self.debug_mode |= rhs.debug_mode
        return self


@dataclass
class GPS_LLA:
    lat: float = 0
    lng: float = 0
    alt: float = 0
    timestamp_ms: int = 0


DEFAULT_PORT = 61006


class HardwareManagerClient:
    def __init__(self, hostname: str = "localhost", port: int = DEFAULT_PORT):
        self._hostname = hostname
        self._port = port
        self._channel = None
        self._stub: Optional[hardware_manager_grpc.HardwareManagerServiceStub] = None
        self._event_loop = get_event_loop_by_name()

    def _maybe_connect(self) -> None:
        if self._stub is None:
            self._channel = grpc.aio.insecure_channel(f"{self._hostname}:{self._port}")
            self._stub = hardware_manager_grpc.HardwareManagerServiceStub(self._channel)

    async def ping(self, x: int) -> int:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.PingRequest(x=x)
        response: hardware_manager_pb.PingResponse = await self._stub.Ping(req)
        return int(response.x)

    async def safety_status(self) -> SafetyStatus:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.GetSafetyStatusRequest()
        response: hardware_manager_pb.GetSafetyStatusResponse = await self._stub.GetSafetyStatus(req)
        return SafetyStatus(
            lifted=response.lifted,
            estopped=response.estopped,
            in_cab_estopped=response.in_cab_estopped,
            left_estopped=response.left_estopped,
            right_estopped=response.right_estopped,
            laser_key=response.laser_key,
            interlock=response.interlock,
            water_protect=response.water_protect,
            reset_required=response.reset_required,
            center_estop=response.center_estop,
            power_button_estop=response.power_button_estop,
            left_lpsu_interlock=response.left_lpsu_interlock,
            right_lpsu_interlock=response.right_lpsu_interlock,
            debug_mode=response.debug_mode,
        )

    async def error_boards(self) -> List[str]:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.GetManagedBoardErrorsRequest()
        response: hardware_manager_pb.GetManagedBoardErrorsResponse = await self._stub.GetManagedBoardErrors(req)

        return [b for b in response.board]

    async def error_encoders(self) -> Optional[str]:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.GetManagedBoardErrorsRequest()
        response: hardware_manager_pb.GetManagedBoardErrorsResponse = await self._stub.GetManagedBoardErrors(req)
        if response.encoder_error_flag:
            return response.encoder_error_msg
        return None

    async def get_gps(self) -> hardware_manager_pb.GetGPSDataResponse:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.GetGPSDataRequest()
        return cast(hardware_manager_pb.GetGPSDataResponse, await self._stub.GetGPSData(req))

    async def gps(self) -> Dict[str, Dict[str, Any]]:
        response: hardware_manager_pb.GetGPSDataResponse = await self.get_gps()

        resp_dict = {
            "lla": {
                "lat": response.lla.lat,
                "lng": response.lla.lng,
                "alt": response.lla.alt,
                "timestamp_ms": response.lla.timestamp_ms,
            },
            "ecef": {
                "x": response.ecef.x,
                "y": response.ecef.y,
                "z": response.ecef.z,
                "timestamp_ms": response.ecef.timestamp_ms,
            },
        }

        return resp_dict

    async def next_gps(self, timestamp_ms: int) -> GPS_LLA:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.GetNextGPSDataRequest(timestamp_ms=timestamp_ms)
        response: hardware_manager_pb.GetNextGPSDataResponse = await self._stub.GetNextGPSData(req)
        return GPS_LLA(
            lat=response.lla.lat, lng=response.lla.lng, alt=response.lla.alt, timestamp_ms=response.lla.timestamp_ms
        )

    async def next_raw_gps(self, timestamp_ms: int) -> Position:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.GetNextRawGPSDataRequest(timestamp_ms=timestamp_ms)
        response: hardware_manager_pb.GetNextRawGPSDataResponse = await self._stub.GetNextRawGPSData(req)
        dual: Optional[DualGpsData] = None

        if response.dual:
            dual = DualGpsData.FromMessage(response.dual)

        return Position(
            have_fix=response.have_fix,
            have_approx_fix=response.have_approx_fix,
            latitude=response.latitude,
            longitude=response.longitude,
            height_mm=response.height_mm,
            num_sats=response.num_sats,
            hdop=response.hdop,
            timestamp_ms=response.timestamp_ms,
            fix_type=FixType(response.fix_type),
            fix_flags=FixFlags(
                gnss_fix_ok=response.gnss_valid,
                diff_soln=response.diff_corrections,
                carr_soln=CarrierSolution(response.carrier_phase),
            ),
            dual_gps_data=dual,
        )

    async def get_gps_fixed_pos(self) -> RelPos:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.GetGPSFixedPosRequest()
        response: hardware_manager_pb.GetGPSFixedPosResponse = await self._stub.GetGPSFixedPos(req)
        return RelPos(x_mm=response.x_mm, y_mm=response.y_mm)

    async def _supervisory_status_slayer(self) -> Dict[str, Any]:
        assert self._stub is not None
        req = hardware_manager_pb.GetSupervisoryStatusRequest()
        response: hardware_manager_pb.GetSupervisoryStatusResponse = await self._stub.GetSupervisoryStatus(req)

        return {
            "water_protect_status": response.water_protect_status,
            "main_contactor_status_fb": response.main_contactor_status_fb,
            "power_good": response.power_good,
            "power_bad": response.power_bad,
            "power_very_bad": response.power_very_bad,
            "lifted_status": response.lifted_status,
            "temp_humidity_status": response.temp_humidity_status,
            "tractor_power": response.tractor_power,
            "ac_frequency": response.ac_frequency,
            "ac_voltage_a_b": response.ac_voltage_a_b,
            "ac_voltage_b_c": response.ac_voltage_b_c,
            "ac_voltage_a_c": response.ac_voltage_a_c,
            "ac_voltage_a": response.ac_voltage_a,
            "ac_voltage_b": response.ac_voltage_b,
            "ac_voltage_c": response.ac_voltage_c,
            "phase_power_w_3": response.phase_power_w_3,
            "phase_power_va_3": response.phase_power_va_3,
            "power_factor": response.power_factor,
            "server_cabinet_temp": response.server_cabinet_temp,
            "server_cabinet_humidity": response.server_cabinet_humidity,
            "battery_voltage_12v": response.battery_voltage_12v,
            "temp_humidity_bypass_status": response.temp_humidity_bypass_status,
            "temp_bypass_status": response.temp_bypass_status,
            "humidity_bypass_status": response.humidity_bypass_status,
            "temp_status": response.temp_status,
            "humidity_status": response.humidity_status,
            "btl_disabled": response.btl_disabled,
            "server_disabled": response.server_disabled,
            "scanners_disabled": response.scanners_disabled,
            "wheel_encoder_disabled": response.wheel_encoder_disabled,
            "gps_disabled": response.gps_disabled,
            "strobe_disabled": response.strobe_disabled,
            "main_contactor_disabled": response.main_contactor_disabled,
            "air_conditioner_disabled": response.air_conditioner_disabled,
            "chiller_disabled": response.chiller_disabled,
            "chiller_temp": response.chiller_temp,
            "chiller_flow": response.chiller_flow,
            "chiller_pressure": response.chiller_pressure,
            "chiller_conductivity": response.chiller_conductivity,
            "chiller_set_temp": response.chiller_set_temp,
            # TODO: refactor out common chiller alarm stuff
            "chiller_alarm_low_level_in_tank": response.chiller_alarms.low_level_in_tank,
            "chiller_alarm_high_circulating_fluid_discharge_temp": response.chiller_alarms.high_circulating_fluid_discharge_temp,
            "chiller_alarm_circulating_fluid_discharge_temp_rise": response.chiller_alarms.circulating_fluid_discharge_temp_rise,
            "chiller_alarm_circulating_fluid_discharge_temp_drop": response.chiller_alarms.circulating_fluid_discharge_temp_drop,
            "chiller_alarm_high_circulating_fluid_return_temp": response.chiller_alarms.high_circulating_fluid_return_temp,
            "chiller_alarm_circulating_fluid_discharge_pressure_rise": response.chiller_alarms.circulating_fluid_discharge_pressure_rise,
            "chiller_alarm_circulating_fluid_discharge_pressure_drop": response.chiller_alarms.circulating_fluid_discharge_pressure_drop,
            "chiller_alarm_high_compressor_suction_temp": response.chiller_alarms.high_compressor_suction_temp,
            "chiller_alarm_low_compressor_suction_temp": response.chiller_alarms.low_compressor_suction_temp,
            "chiller_alarm_low_super_heat_temp": response.chiller_alarms.low_super_heat_temp,
            "chiller_alarm_high_compressor_discharge_pressure": response.chiller_alarms.high_compressor_discharge_pressure,
            "chiller_alarm_refrigerant_circut_pressure_high_drop": response.chiller_alarms.refrigerant_circut_pressure_high_drop,
            "chiller_alarm_refrigerant_circut_pressure_low_rise": response.chiller_alarms.refrigerant_circut_pressure_low_rise,
            "chiller_alarm_refrigerant_circut_pressure_low_drop": response.chiller_alarms.refrigerant_circut_pressure_low_drop,
            "chiller_alarm_compressor_running_failure": response.chiller_alarms.compressor_running_failure,
            "chiller_alarm_communication_error": response.chiller_alarms.communication_error,
            "chiller_alarm_memory_error": response.chiller_alarms.memory_error,
            "chiller_alarm_dc_line_fuse_cut": response.chiller_alarms.dc_line_fuse_cut,
            "chiller_alarm_circulating_fluid_discharge_temp_sensor_failure": response.chiller_alarms.circulating_fluid_discharge_temp_sensor_failure,
            "chiller_alarm_circulating_fluid_return_temp_sensor_failure": response.chiller_alarms.circulating_fluid_return_temp_sensor_failure,
            "chiller_alarm_circulating_fluid_suction_temp_sensor_failure": response.chiller_alarms.circulating_fluid_suction_temp_sensor_failure,
            "chiller_alarm_circulating_fluid_discharge_pressure_sensor_failure": response.chiller_alarms.circulating_fluid_discharge_pressure_sensor_failure,
            "chiller_alarm_compressor_discharge_pressure_sensor_failure": response.chiller_alarms.compressor_discharge_pressure_sensor_failure,
            "chiller_alarm_compressor_suction_pressure_sensor_failure": response.chiller_alarms.compressor_suction_pressure_sensor_failure,
            "chiller_alarm_pump_maintenance": response.chiller_alarms.pump_maintenance,
            "chiller_alarm_fan_maintenance": response.chiller_alarms.fan_maintenance,
            "chiller_alarm_compressor_maintenance": response.chiller_alarms.compressor_maintenance,
            "chiller_alarm_contact_input_1_signal_detection": response.chiller_alarms.contact_input_1_signal_detection,
            "chiller_alarm_contact_input_2_signal_detection": response.chiller_alarms.contact_input_2_signal_detection,
            "chiller_alarm_compressor_discharge_temp_sensor_failure": response.chiller_alarms.compressor_discharge_temp_sensor_failure,
            "chiller_alarm_compressor_discharge_temp_rise": response.chiller_alarms.compressor_discharge_temp_rise,
            "chiller_alarm_dustproof_filter_maintenance": response.chiller_alarms.dustproof_filter_maintenance,
            "chiller_alarm_power_stoppage": response.chiller_alarms.power_stoppage,
            "chiller_alarm_compressor_waiting": response.chiller_alarms.compressor_waiting,
            "chiller_alarm_fan_failure": response.chiller_alarms.fan_failure,
            "chiller_alarm_compressor_over_current": response.chiller_alarms.compressor_over_current,
            "chiller_alarm_pump_over_current": response.chiller_alarms.pump_over_current,
            "chiller_alarm_air_exhaust_fan_stoppage": response.chiller_alarms.air_exhaust_fan_stoppage,
            "chiller_alarm_incorrect_phase_error": response.chiller_alarms.incorrect_phase_error,
            "chiller_alarm_phase_board_over_current": response.chiller_alarms.phase_board_over_current,
        }

    async def _supervisory_status_reaper(self) -> Dict[str, Any]:
        assert self._stub is not None
        req = hardware_manager_pb.GetReaperSupervisoryStatusRequest()
        response: hardware_manager_pb.ReaperCenterEnclosureData = await self._stub.GetReaperSupervisoryStatus(req)

        return {
            "water_protect_status": response.water_protect_status,
            "main_contactor_status_fb": response.main_contactor_status_fb,
            "power_good": response.power_good,
            "power_bad": response.power_bad,
            "power_very_bad": response.power_very_bad,
            "lifted_status": response.lifted_status,
            "temp_humidity_status": 0,  # response.temp_humidity_status,
            "tractor_power": response.tractor_power,
            "ac_frequency": response.ac_frequency,
            "ac_voltage_a_b": response.ac_voltage_a_b,
            "ac_voltage_b_c": response.ac_voltage_b_c,
            "ac_voltage_a_c": response.ac_voltage_a_c,
            "ac_voltage_a": response.ac_voltage_a,
            "ac_voltage_b": response.ac_voltage_b,
            "ac_voltage_c": response.ac_voltage_c,
            "phase_power_w_3": response.phase_power_w_3,
            "phase_power_va_3": response.phase_power_va_3,
            "power_factor": response.power_factor,
            "server_cabinet_temp": response.server_cabinet_temp,
            "server_cabinet_humidity": response.server_cabinet_humidity,
            "battery_voltage_12v": response.battery_voltage_12v,
            "temp_status": 0,  # response.temp_status,
            "humidity_status": 0,  # response.humidity_status,
            "wheel_encoder_disabled": response.wheel_encoder_disabled,
            "gps_disabled": response.gps_disabled,
            "main_contactor_disabled": response.main_contactor_disabled,
            "air_conditioner_disabled": response.air_conditioner_disabled,
            "chiller_disabled": response.chiller_disabled,
            "chiller_temp": response.chiller_temp,
            "chiller_flow": response.chiller_flow,
            "chiller_pressure": response.chiller_pressure,
            "chiller_conductivity": response.chiller_conductivity,
            "chiller_set_temp": response.chiller_set_temp,
            # TODO: refactor out common chiller alarm stuff
            "chiller_alarm_low_level_in_tank": response.chiller_alarms.low_level_in_tank,
            "chiller_alarm_high_circulating_fluid_discharge_temp": response.chiller_alarms.high_circulating_fluid_discharge_temp,
            "chiller_alarm_circulating_fluid_discharge_temp_rise": response.chiller_alarms.circulating_fluid_discharge_temp_rise,
            "chiller_alarm_circulating_fluid_discharge_temp_drop": response.chiller_alarms.circulating_fluid_discharge_temp_drop,
            "chiller_alarm_high_circulating_fluid_return_temp": response.chiller_alarms.high_circulating_fluid_return_temp,
            "chiller_alarm_circulating_fluid_discharge_pressure_rise": response.chiller_alarms.circulating_fluid_discharge_pressure_rise,
            "chiller_alarm_circulating_fluid_discharge_pressure_drop": response.chiller_alarms.circulating_fluid_discharge_pressure_drop,
            "chiller_alarm_high_compressor_suction_temp": response.chiller_alarms.high_compressor_suction_temp,
            "chiller_alarm_low_compressor_suction_temp": response.chiller_alarms.low_compressor_suction_temp,
            "chiller_alarm_low_super_heat_temp": response.chiller_alarms.low_super_heat_temp,
            "chiller_alarm_high_compressor_discharge_pressure": response.chiller_alarms.high_compressor_discharge_pressure,
            "chiller_alarm_refrigerant_circut_pressure_high_drop": response.chiller_alarms.refrigerant_circut_pressure_high_drop,
            "chiller_alarm_refrigerant_circut_pressure_low_rise": response.chiller_alarms.refrigerant_circut_pressure_low_rise,
            "chiller_alarm_refrigerant_circut_pressure_low_drop": response.chiller_alarms.refrigerant_circut_pressure_low_drop,
            "chiller_alarm_compressor_running_failure": response.chiller_alarms.compressor_running_failure,
            "chiller_alarm_communication_error": response.chiller_alarms.communication_error,
            "chiller_alarm_memory_error": response.chiller_alarms.memory_error,
            "chiller_alarm_dc_line_fuse_cut": response.chiller_alarms.dc_line_fuse_cut,
            "chiller_alarm_circulating_fluid_discharge_temp_sensor_failure": response.chiller_alarms.circulating_fluid_discharge_temp_sensor_failure,
            "chiller_alarm_circulating_fluid_return_temp_sensor_failure": response.chiller_alarms.circulating_fluid_return_temp_sensor_failure,
            "chiller_alarm_circulating_fluid_suction_temp_sensor_failure": response.chiller_alarms.circulating_fluid_suction_temp_sensor_failure,
            "chiller_alarm_circulating_fluid_discharge_pressure_sensor_failure": response.chiller_alarms.circulating_fluid_discharge_pressure_sensor_failure,
            "chiller_alarm_compressor_discharge_pressure_sensor_failure": response.chiller_alarms.compressor_discharge_pressure_sensor_failure,
            "chiller_alarm_compressor_suction_pressure_sensor_failure": response.chiller_alarms.compressor_suction_pressure_sensor_failure,
            "chiller_alarm_pump_maintenance": response.chiller_alarms.pump_maintenance,
            "chiller_alarm_fan_maintenance": response.chiller_alarms.fan_maintenance,
            "chiller_alarm_compressor_maintenance": response.chiller_alarms.compressor_maintenance,
            "chiller_alarm_contact_input_1_signal_detection": response.chiller_alarms.contact_input_1_signal_detection,
            "chiller_alarm_contact_input_2_signal_detection": response.chiller_alarms.contact_input_2_signal_detection,
            "chiller_alarm_compressor_discharge_temp_sensor_failure": response.chiller_alarms.compressor_discharge_temp_sensor_failure,
            "chiller_alarm_compressor_discharge_temp_rise": response.chiller_alarms.compressor_discharge_temp_rise,
            "chiller_alarm_dustproof_filter_maintenance": response.chiller_alarms.dustproof_filter_maintenance,
            "chiller_alarm_power_stoppage": response.chiller_alarms.power_stoppage,
            "chiller_alarm_compressor_waiting": response.chiller_alarms.compressor_waiting,
            "chiller_alarm_fan_failure": response.chiller_alarms.fan_failure,
            "chiller_alarm_compressor_over_current": response.chiller_alarms.compressor_over_current,
            "chiller_alarm_pump_over_current": response.chiller_alarms.pump_over_current,
            "chiller_alarm_air_exhaust_fan_stoppage": response.chiller_alarms.air_exhaust_fan_stoppage,
            "chiller_alarm_incorrect_phase_error": response.chiller_alarms.incorrect_phase_error,
            "chiller_alarm_phase_board_over_current": response.chiller_alarms.phase_board_over_current,
        }

    async def supervisory_status(self) -> Dict[str, Any]:
        self._maybe_connect()

        if is_reaper():
            return await self._supervisory_status_reaper()
        elif is_slayer():
            return await self._supervisory_status_slayer()
        else:
            raise Exception("Unsupported generation")

    async def get_reaper_enclosure_sensors(self) -> hardware_manager_pb.ReaperCenterEnclosureData:
        self._maybe_connect()
        assert self._stub is not None

        req = hardware_manager_pb.GetReaperEnclosureSensorsRequest()
        response: hardware_manager_pb.GetReaperEnclosureSensorsResponse = await self._stub.GetReaperEnclosureSensors(
            req
        )
        return response.sensors

    # TODO: return the python dataclass type?
    # async def get_reaper_module_sensors(self, module_ids: Optional[List[int]]) -> List[ModuleSensorData]:
    async def get_reaper_module_sensors(
        self, module_ids: Optional[List[int]]
    ) -> List[hardware_manager_pb.ReaperModuleSensorData]:
        self._maybe_connect()
        assert self._stub is not None

        req = hardware_manager_pb.GetReaperModuleSensorsRequest(module_ids=module_ids)
        response: hardware_manager_pb.GetReaperModuleSensorsResponse = await self._stub.GetReaperModuleSensors(req)

        return list(response.module_sensors)
        # return [ModuleSensorData.FromMessage(x) for x in response.module_sensors]

    async def set_reaper_predict_power(self, module_id: int, power: bool) -> None:
        self._maybe_connect()
        assert self._stub is not None

        req = hardware_manager_pb.SetReaperPredictCamPowerRequest(module_id=module_id, enabled=power)
        response: hardware_manager_pb.SetReaperPredictCamPowerResponse = await self._stub.SetReaperPredictCamPower(req)

        if not response.success:
            raise Exception(f"Failed to set predict cam power for module {module_id}")

    # TODO: reaper strobe config

    async def set_reaper_strobe_enable(
        self, module_ids: Optional[Union[int, List[int]]], enabled: bool, duration: Optional[Duration] = None
    ) -> None:
        """
        Set the state of module strobe lights

        module_ids may be either a single module's ID, a list of module IDs, or `None` to affect
        ALL modules.
        """
        if enabled and duration is not None:
            raise ValueError("Duration can only be specified for disabling strobes")

        self._maybe_connect()
        assert self._stub is not None

        req: hardware_manager_pb.SetReaperStrobeEnableRequest

        if module_ids is None:
            req = hardware_manager_pb.SetReaperStrobeEnableRequest(enabled=enabled)
        else:
            ids: List[int] = []

            if isinstance(module_ids, int):
                ids = [module_ids]
            else:
                ids = module_ids

            req = hardware_manager_pb.SetReaperStrobeEnableRequest(module_ids=ids, enabled=enabled)

        response: hardware_manager_pb.SetReaperStrobeEnableResponse = await self._stub.SetReaperStrobeEnable(req)

        if not response.success:
            raise Exception(f"Failed to set strobe enable for module {module_ids}")

    async def set_reaper_pc_power(self, module_id: int, power: bool) -> None:
        self._maybe_connect()
        assert self._stub is not None

        req = hardware_manager_pb.SetReaperModulePcPowerRequest(module_id=module_id, enabled=power)
        response: hardware_manager_pb.SetReaperModulePcPowerResponse = await self._stub.SetReaperModulePcPower(req)

        if not response.success:
            raise Exception(f"Failed to set PC power for module {module_id}")

    async def set_reaper_laser_power(self, module_id: int, power: bool) -> None:
        self._maybe_connect()
        assert self._stub is not None

        req = hardware_manager_pb.SetReaperModuleLaserPowerRequest(module_id=module_id, enabled=power)
        response: hardware_manager_pb.SetReaperModuleLaserPowerResponse = await self._stub.SetReaperModuleLaserPower(
            req
        )

        if not response.success:
            raise Exception(f"Failed to set laser power for module {module_id}")

    async def set_reaper_strobe_power(self, module_id: int, power: bool) -> None:
        self._maybe_connect()
        assert self._stub is not None

        req = hardware_manager_pb.SetReaperModuleStrobePowerRequest(module_id=module_id, enabled=power)
        response: hardware_manager_pb.SetReaperModuleStrobePowerResponse = await self._stub.SetReaperModuleStrobePower(
            req
        )

        if not response.success:
            raise Exception(f"Failed to set BTL supply power for module {module_id}")

    async def enable_row(self, row_id: int) -> bool:
        assert row_id in [1, 2, 3]
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetServerDisableRequest(row_id=row_id, disable=False)
        response: hardware_manager_pb.SetServerDisableResponse = await self._stub.SetServerDisable(req)
        return response.success

    async def disable_row(self, row_id: int) -> bool:
        assert row_id in [1, 2, 3]
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetServerDisableRequest(row_id=row_id, disable=True)
        response: hardware_manager_pb.SetServerDisableResponse = await self._stub.SetServerDisable(req)
        return response.success

    async def enable_btl(self, row_id: int) -> bool:
        assert row_id in [1, 2, 3]
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetBTLDisableRequest(row_id=row_id, disable=False)
        response: hardware_manager_pb.SetBTLDisableResponse = await self._stub.SetBTLDisable(req)
        return response.success

    async def disable_btl(self, row_id: int) -> bool:
        assert row_id in [1, 2, 3]
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetBTLDisableRequest(row_id=row_id, disable=True)
        response: hardware_manager_pb.SetBTLDisableResponse = await self._stub.SetBTLDisable(req)
        return response.success

    async def enable_scanners(self, row_id: int) -> bool:
        assert row_id in [1, 2, 3]
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetScannersDisableRequest(row_id=row_id, disable=False)
        response: hardware_manager_pb.SetScannersDisableResponse = await self._stub.SetScannersDisable(req)
        return response.success

    async def disable_scanners(self, row_id: int) -> bool:
        assert row_id in [1, 2, 3]
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetScannersDisableRequest(row_id=row_id, disable=True)
        response: hardware_manager_pb.SetScannersDisableResponse = await self._stub.SetScannersDisable(req)
        return response.success

    async def enable_wheel_encoder(self) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetWheelEncoderDisableRequest(disable=False)
        response: hardware_manager_pb.SetWheelEncoderDisableResponse = await self._stub.SetWheelEncoderDisable(req)
        return response.success

    async def disable_wheel_encoder(self) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetWheelEncoderDisableRequest(disable=True)
        response: hardware_manager_pb.SetWheelEncoderDisableResponse = await self._stub.SetWheelEncoderDisable(req)
        return response.success

    async def enable_gps(self) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetGPSDisableRequest(disable=False)
        response: hardware_manager_pb.SetGPSDisableResponse = await self._stub.SetGPSDisable(req)
        return response.success

    async def disable_gps(self) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetGPSDisableRequest(disable=True)
        response: hardware_manager_pb.SetGPSDisableResponse = await self._stub.SetGPSDisable(req)
        return response.success

    async def suicide_switch(self) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SuicideSwitchRequest()
        response: hardware_manager_pb.SuicideSwitchResponse = await self._stub.SuicideSwitch(req)
        return response.success

    async def command_computer_power_cycle(self) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.CommandComputerPowerCycleRequest()
        response: hardware_manager_pb.CommandComputerPowerCycleResponse = await self._stub.CommandComputerPowerCycle(
            req
        )
        return response.success

    async def enable_main_contactor_disabled(self) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetMainContactorDisableRequest(disable=True)
        response: hardware_manager_pb.SetMainContactorDisableResponse = await self._stub.SetMainContactorDisable(req)
        return response.success

    async def disable_main_contactor_disabled(self) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetMainContactorDisableRequest(disable=False)
        response: hardware_manager_pb.SetMainContactorDisableResponse = await self._stub.SetMainContactorDisable(req)
        return response.success

    async def enable_strobe(self) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetStrobeDisableRequest(disable=False)
        response: hardware_manager_pb.SetStrobeDisableResponse = await self._stub.SetStrobeDisable(req)
        return response.success

    async def disable_strobe(self) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetStrobeDisableRequest(disable=True)
        response: hardware_manager_pb.SetStrobeDisableResponse = await self._stub.SetStrobeDisable(req)
        return response.success

    async def enable_air_conditioner(self) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetAirConditionerDisableRequest(disable=False)
        response: hardware_manager_pb.SetAirConditionerDisableResponse = await self._stub.SetAirConditionerDisable(req)
        return response.success

    async def disable_air_conditioner(self) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetAirConditionerDisableRequest(disable=True)
        response: hardware_manager_pb.SetAirConditionerDisableResponse = await self._stub.SetAirConditionerDisable(req)
        return response.success

    async def enable_chiller(self) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetChillerDisableRequest(disable=False)
        response: hardware_manager_pb.SetChillerDisableResponse = await self._stub.SetChillerDisable(req)
        return response.success

    async def disable_chiller(self) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetChillerDisableRequest(disable=True)
        response: hardware_manager_pb.SetChillerDisableResponse = await self._stub.SetChillerDisable(req)
        return response.success

    async def enable_temp_bypass(self) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetTempBypassDisableRequest(disable=False)
        response: hardware_manager_pb.SetTempBypassDisableResponse = await self._stub.SetTempBypassDisable(req)
        return response.success

    async def disable_temp_bypass(self) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetTempBypassDisableRequest(disable=True)
        response: hardware_manager_pb.SetTempBypassDisableResponse = await self._stub.SetTempBypassDisable(req)
        return response.success

    async def enable_humidity_bypass(self) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetHumidityBypassDisableRequest(disable=False)
        response: hardware_manager_pb.SetHumidityBypassDisableResponse = await self._stub.SetHumidityBypassDisable(req)
        return response.success

    async def disable_humidity_bypass(self) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetHumidityBypassDisableRequest(disable=True)
        response: hardware_manager_pb.SetHumidityBypassDisableResponse = await self._stub.SetHumidityBypassDisable(req)
        return response.success

    async def get_240v_uptime(self) -> int:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.Get240vUptimeRequest()
        response: hardware_manager_pb.Get240vUptimeResponse = await self._stub.Get240vUptime(req)
        return response.uptime_s

    async def get_wheel_encoder_resolution(self) -> int:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.GetWheelEncoderResolutionRequest()
        response: hardware_manager_pb.GetWheelEncoderResolutionResponse = await self._stub.GetWheelEncoderResolution(
            req
        )
        return response.resolution

    async def get_strobe_settings(self) -> hardware_manager_pb.StrobeSettings:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.GetStrobeSettingsRequest()
        response: hardware_manager_pb.StrobeSettings = await self._stub.GetStrobeSettings(req)
        return response

    async def set_speed(self, target_speed: Speed, actual_speed: Speed = ZERO_SPEED()) -> Speed:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetJimboxSpeedRequest(
            target_speed=target_speed.mph, actual_ground_speed=actual_speed.mph
        )
        resp: hardware_manager_pb.SetJimboxSpeedResponse = await self._stub.SetJimboxSpeed(req)
        return Speed.from_mph(resp.speed_setpoint)

    async def set_cruise_enabled(self, enabled: bool) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetCruiseEnabledRequest(enabled=enabled)
        resp: hardware_manager_pb.SetCruiseEnabledResponse = await self._stub.SetCruiseEnabled(req)
        return resp.success

    async def get_cruise_status(self) -> hardware_manager_pb.GetCruiseStatusResponse:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.GetCruiseStatusRequest()
        resp: hardware_manager_pb.GetCruiseStatusResponse = await self._stub.GetCruiseStatus(req)
        return resp

    async def identify_module(self, to_id_ip: str, turn_off_ips: List[str]) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.IdentifyModuleRequest(to_id_ip=to_id_ip, turn_off_ips=turn_off_ips)
        resp: hardware_manager_pb.IdentifyModuleResponse = await self._stub.IdentifyModule(req)
        return resp.success

    async def set_implement_state_on_tractor(self, active: bool, error: bool, err_msg: Optional[str] = None) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetImplementStateRequest(active=active, error=error, error_message=err_msg)
        await self._stub.SetImplementStateOnTractor(req)

    async def set_safe_state_enforcement(self, enforced: bool) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.SetSafeStateEnforcementRequest(enforced=enforced)
        await self._stub.SetSafeStateEnforcement(req)

    async def get_tractor_safety_state(
        self, timestamp_ms: int = 0
    ) -> hardware_manager_pb.GetTractorSafetyStateResponse:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.GetTractorSafetyStateRequest(timestamp_ms=timestamp_ms)
        resp: hardware_manager_pb.GetTractorSafetyStateResponse = await self._stub.GetTractorSafetyState(req)
        return resp

    async def get_tractor_if_state(self) -> hardware_manager_pb.GetTractorIFStateResponse:
        self._maybe_connect()
        assert self._stub is not None
        req = hardware_manager_pb.GetTractorIFStateRequest()
        resp: hardware_manager_pb.GetTractorIFStateResponse = await self._stub.GetTractorIFState(req)
        return resp


def interactive(hostname: str, port: int) -> None:
    from lib.common.asyncio.repl import start

    client = HardwareManagerClient(hostname, port)
    imports = {
        "client": client,
    }
    start(imports)


def main() -> None:
    from argparse import ArgumentParser

    parser = ArgumentParser("Hardware manager GRPC client")
    parser.add_argument("--hostname", type=str, default="localhost", help="grpc service host")
    parser.add_argument("-p", "--port", type=int, default=DEFAULT_PORT, help="grpc service port")
    args = parser.parse_args()
    interactive(args.hostname, args.port)


if __name__ == "__main__":
    main()

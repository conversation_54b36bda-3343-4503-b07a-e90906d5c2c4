import asyncio
from typing import Awaitable, List, Optional, cast

from hardware_manager.python.boards.board import HardwareManagedBoard
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.devices.boards.safety_plc.safety_plc_device import SafetyPLCDevice, SafetyPLCStatus
from lib.common.devices.registry import DeviceRegistry
from lib.common.error import MakaException
from lib.common.logging import get_logger
from lib.common.time import maka_control_timestamp_ms

POLL_INTERVAL_MS = 1000

LOG = get_logger(__name__)


class SafetyBoardException(MakaException):
    pass


class SafetyBoard(HardwareManagedBoard):
    def __init__(self, device_registry: DeviceRegistry) -> None:
        super().__init__(device_registry, "safety_plc_1", SafetyPLCDevice, "safety")
        self._last_update = 0
        self._data = SafetyPLCStatus()
        self._lock: Optional[asyncio.Lock] = None

    async def start(self) -> List[Awaitable[None]]:
        self._lock = asyncio.Lock()
        return [asyncio.create_task(self._run_forever())]

    async def _get_status(self) -> SafetyPLCStatus:
        try:
            status = await cast(SafetyPLCDevice, self._board).get_safety_status()
            self._ok = True
        except Exception:
            self._ok = False
            LOG.exception("Exception occured getting safety status.")
            raise SafetyBoardException("Error getting status")
        return status

    async def get_status(self) -> SafetyPLCStatus:
        assert self._lock is not None
        now = maka_control_timestamp_ms()
        async with self._lock:
            if now - self._last_update > 500:
                self._data = await self._get_status()
                self._last_update = maka_control_timestamp_ms()
        return self._data

    async def _run_forever(self) -> None:
        assert self._lock is not None
        while not bot_stop_handler.stopped:
            now = maka_control_timestamp_ms()
            async with self._lock:
                if now - self._last_update > 10000:
                    try:
                        self._data = await self._get_status()
                        self._last_update = maka_control_timestamp_ms()
                    except Exception:
                        LOG.info("Safety PLC exception")
            await asyncio.sleep(10)

from abc import abstractmethod
from dataclasses import dataclass, field
from typing import Protocol

from lib.common.units.speed import ZERO_SPEED, Speed


@dataclass
class CCStatus:
    installed: bool = False
    allow_enabled: bool = False
    enabled: bool = False
    speed: Speed = field(default_factory=ZERO_SPEED)


class CruiseController(Protocol):
    @abstractmethod
    async def set_enabled(self, enabled: bool) -> bool:  # value
        pass

    @abstractmethod
    async def set_speed(self, target_speed: Speed, current_speed: Speed) -> Speed:
        pass

    @abstractmethod
    async def get_status(self) -> CCStatus:
        pass

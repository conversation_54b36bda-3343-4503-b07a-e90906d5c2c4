import asyncio
import csv
import logging
from datetime import datetime, timezone
from enum import IntEnum
from io import TextIOWrapper
from typing import Any, Callable, Dict, List, Optional, Tuple, TypeVar, Union

import navpy

import _csv
from config.client.cpp.config_client_python import ConfigTree
from hardware_manager.python.boards.gps_kalman import GPSLLAKalmanFilter
from lib.common.time.time import maka_control_timestamp_ms

LOG = logging.getLogger(__name__)


class GPSDataWriter:
    def __init__(self, output_file: Optional[str] = None) -> None:
        """
        Initialize the GPSDataWriter with the output CSV file.
        """
        self._output_file = (
            output_file if output_file is not None else f"/data/gps_data_{maka_control_timestamp_ms()}.csv"
        )
        self._initialized = False
        self._file: Optional[TextIOWrapper] = None
        self._writer: Optional[_csv._writer] = None
        LOG.info(f"GPSDataWriter initialized. Writing to {self._output_file}.")

    def add_point(self, timestamp: float, latitude: float, longitude: float, altitude: float) -> None:
        """
        Add a new GPS data point to the CSV file.
        """
        if self._file is None or self._writer is None:
            if self._file is not None:
                self._file.close()
            self._file = open(self._output_file, mode="w", newline="")
            self._writer = csv.writer(self._file)
            self._writer.writerow(["Timestamp", "Latitude", "Longitude", "Altitude"])  # Header
        self._writer.writerow([timestamp, latitude, longitude, altitude])
        self._file.flush()  # Ensure data is written to disk
        LOG.info(f"Added point: {timestamp}, {latitude:.6f}, {longitude:.6f}, {altitude:.2f}")

    def close(self) -> None:
        """
        Close the CSV file.
        """
        if self._file is not None:
            self._file.close()
        LOG.info(f"GPSDataWriter closed. File saved to {self._output_file}.")


def calculate_unix_timestamp(time: str, date: Optional[str]) -> int:
    if not date:
        date = datetime.now().strftime("%d%m%y")  # Format: DDMMYY
    if not time:
        return 0
    try:
        date_obj = datetime.strptime(date + time[:6], "%d%m%y%H%M%S")
        return int(date_obj.replace(tzinfo=timezone.utc).timestamp() * 1000)
    except ValueError:
        return 0


# Enum for Fix Quality
class FixQuality(IntEnum):
    INVALID = 0
    GPS_FIX = 1
    DGPS_FIX = 2
    PPS_FIX = 3
    RTK = 4
    FLOAT_RTK = 5
    ESTIMATED = 6
    MANUAL = 7
    SIMULATED = 8

    def bigger_than(f1: "FixQuality", f2: "FixQuality") -> bool:
        # Note that this does not make sense for all fix comparisons, as float_rtk is less precise than rtk, dead-reckoning etc.
        # Use with caution
        return int(f1) >= int(f2)


# Class to encapsulate the GGA message
class GGA:
    def __init__(
        self,
        time: str,
        latitude: Optional[float],
        longitude: Optional[float],
        fix_quality: FixQuality,
        num_satellites: int,
        altitude: float,
        date: Optional[str] = None,
    ):
        self.time = time
        self.latitude = latitude
        self.longitude = longitude
        self.fix_quality = fix_quality
        self.num_satellites = num_satellites
        self.altitude = altitude  # Altitude in meters
        self.ecef = self.calculate_ecef()  # ECEF coordinates (x, y, z)
        self.timestamp = calculate_unix_timestamp(time, date)

    def is_valid(self) -> bool:
        return (
            FixQuality.bigger_than(self.fix_quality, FixQuality.GPS_FIX)
            and self.longitude is not None
            and self.latitude is not None
        )

    def calculate_ecef(self) -> Optional[Tuple[float, float, float]]:
        if self.latitude is None or self.longitude is None:
            return None
        x, y, z = navpy.lla2ecef(self.latitude, self.longitude, self.altitude, latlon_unit="deg", alt_unit="m")
        return x, y, z

    def get_delta_from_current_time(self) -> Optional[int]:
        if self.timestamp is None:
            return None
        current_time_ms = int(datetime.now(timezone.utc).timestamp() * 1000)
        return current_time_ms - self.timestamp

    def __str__(self) -> str:
        delta = self.get_delta_from_current_time()
        delta_str = f"{delta} ms" if delta is not None else "N/A"
        ecef_str = f"ECEF: {self.ecef}" if self.ecef else "ECEF: N/A"
        return (
            f"GGA Message - Time: {self.time}, Latitude: {self.latitude}, "
            f"Longitude: {self.longitude}, Fix Quality: {self.fix_quality.name}, "
            f"Satellites: {self.num_satellites}, Altitude: {self.altitude}m, "
            f"Timestamp: {self.timestamp}, Delta: {delta_str}, {ecef_str}"
        )


class PeplinkGPSReceiver:
    def __init__(self, host: str = "0.0.0.0", port: int = 61018, filter_config: Optional[ConfigTree] = None) -> None:
        self._host = host
        self._port = port
        self._lock = asyncio.Lock()
        self._cond = asyncio.Condition(lock=self._lock)
        self._gga_data: Optional[GGA] = None
        self._gprmc_data: Optional[Dict[str, Any]] = None
        self._gpgsa_data: Optional[Dict[str, Any]] = None
        self._gpgsv_data: List[Dict[str, Any]] = []  # Current sequence of GPGSV messages
        self._gpvtg_data: Optional[Dict[str, Any]] = None
        self._last_date: Optional[str] = None
        self._last_timestamp_ms: int = 0
        self._filter_config = filter_config
        self._filter = GPSLLAKalmanFilter()
        self._filtering_enabled = False
        self._capturing_enabled = False
        self._gps_capture = GPSDataWriter()
        if self._filter_config is not None:
            self._filter_config.register_callback(self._update_filter_config)
            self._update_filter_config()
        # Mock starting point since PeplinkGPS is only used on MVS for now
        gga = self.parse_nmea("$GPGGA,000001.00,4736.3720,N,12219.9260,W,1,10,0.8,52.0,M,25.0,M,,*43")
        assert isinstance(gga, GGA)
        self._gga_data = gga
        self._last_timestamp_ms = 1

    T = TypeVar("T")

    def _update_filter_config(self) -> None:
        LOG.info("Updating Filter Config")
        if self._filter_config is None:
            return
        lat_long_r = self._filter_config.get_node("lat_long_r").get_float_value()
        alt_r = self._filter_config.get_node("altitude_r").get_float_value()
        lat_long_q = self._filter_config.get_node("lat_long_q").get_float_value()
        alt_q = self._filter_config.get_node("altitude_q").get_float_value()
        lat_long_q_vel = self._filter_config.get_node("lat_long_q_vel").get_float_value()
        alt_q_vel = self._filter_config.get_node("altitude_q_vel").get_float_value()
        enabled = self._filter_config.get_node("enabled").get_bool_value()
        capturing_enabled = self._filter_config.get_node("capture").get_bool_value()
        lat_long_exp = self._filter_config.get_node("lat_long_exp").get_float_value()
        motion_n = self._filter_config.get_node("motion_n").get_int_value()

        self._filtering_enabled = enabled
        self._capturing_enabled = capturing_enabled
        self._filter.set_measurement_noise(lat_long_r, alt_r)
        self._filter.set_process_noise(lat_long_q, alt_q, lat_long_q_vel, alt_q_vel)
        self._filter.set_n(motion_n)
        self._filter.set_lat_long_exp(lat_long_exp)

    async def wait_for_next(self, timestamp_ms: int, getter: Callable[[], T]) -> T:
        async with self._cond:
            await self._cond.wait_for(lambda: self._last_timestamp_ms > timestamp_ms and self._last_timestamp_ms != 0)
            return getter()

    async def get_next_gga(self, timestamp_ms: int = 0) -> Optional[GGA]:
        return await self.wait_for_next(timestamp_ms, lambda: self._gga_data)

    async def start_server(self) -> None:
        self._server = await asyncio.start_server(self.handle_client, self._host, self._port)
        LOG.info(f"GPS Receiver Server listening on {self._host}:{self._port}")

    async def handle_client(self, reader: asyncio.StreamReader, writer: asyncio.StreamWriter) -> None:
        client_address = writer.get_extra_info("peername")
        LOG.info(f"GPS Receiver Connected by {client_address}")

        while True:
            try:
                data = await reader.read(1024)
                if not data:
                    break

                messages = data.decode("utf-8").strip().splitlines()
                async with self._lock:
                    for message in messages:
                        if message.startswith("$"):
                            await self._update_data(message)
                        else:
                            LOG.warning("Non-NMEA message received:", message)
                    self._cond.notify_all()
            except Exception:
                LOG.exception("Exception in Peplink GPS Receiver, sleeping for 30 seconds")
                await asyncio.sleep(30)

        LOG.warning(f"Connection with {client_address} closed.")
        writer.close()
        await writer.wait_closed()

    async def _update_data(self, sentence: str) -> None:
        # Called under lock
        parsed_data = self.parse_nmea(sentence)
        if isinstance(parsed_data, GGA):
            self._gga_data = parsed_data
        elif isinstance(parsed_data, dict):
            data_type = parsed_data["type"]
            if data_type == "GPRMC":
                self._last_date = parsed_data["date"]
                self._last_timestamp_ms = calculate_unix_timestamp(parsed_data["time"], self._last_date)
                self._gprmc_data = parsed_data
            elif data_type == "GPGSA":
                self._gpgsa_data = parsed_data
            elif data_type == "GPGSV":
                self._handle_gpgsv(parsed_data)
            elif data_type == "GPVTG":
                self._gpvtg_data = parsed_data

    def _handle_gpgsv(self, parsed_data: Dict[str, Any]) -> None:
        # Called under lock
        if parsed_data["message_number"] == 1:
            self._gpgsv_data = []  # Start a new sequence
        self._gpgsv_data.append(parsed_data)

    def parse_nmea(self, sentence: str) -> Optional[Union[GGA, Dict[str, Any]]]:
        parts = sentence.strip().split(",")
        if parts[0] == "$GPGGA":
            lat = self.parse_latitude(parts[2], parts[3])
            long = self.parse_longitude(parts[4], parts[5])
            alt = self.parse_altitude(parts[9] + " " + parts[10])
            timestamp = calculate_unix_timestamp(parts[1], self._last_date)
            pos = (lat, long, alt)
            if lat is not None and long is not None and alt is not None and timestamp is not None:
                pos = (lat, long, alt)  # Redo just for mypy
                if self._capturing_enabled:
                    self._gps_capture.add_point(timestamp=timestamp, latitude=lat, longitude=long, altitude=alt)
                if self._filtering_enabled:
                    try:
                        pos = (lat, long, alt)  # Redo just for mypy
                        pos = self._filter.next_value(pos)
                    except Exception:
                        LOG.exception("Filter exception")
            return GGA(
                time=parts[1],
                latitude=pos[0],
                longitude=pos[1],
                fix_quality=FixQuality(int(parts[6])),
                num_satellites=int(parts[7]),
                altitude=pos[2],
                date=self._last_date,
            )
        elif parts[0] == "$GPRMC":
            return {
                "type": "GPRMC",
                "date": parts[9],
                "time": parts[1],
                "status": parts[2],
                "latitude": self.parse_latitude(parts[3], parts[4]),
                "longitude": self.parse_longitude(parts[5], parts[6]),
                "speed": parts[7],
                "course": parts[8],
            }
        elif parts[0] == "$GPGSA":
            return {
                "type": "GPGSA",
                "mode1": parts[1],
                "mode2": parts[2],
                "satellites_used": parts[3:15],
                "pdop": parts[15],
                "hdop": parts[16],
                "vdop": parts[17].split("*")[0],
            }
        elif parts[0] == "$GPGSV":
            num_messages = int(parts[1])
            message_number = int(parts[2])
            num_satellites = int(parts[3])
            satellites = []
            for i in range(4):
                base_idx = 4 + i * 4
                if base_idx + 3 < len(parts):
                    satellites.append(
                        {
                            "satellite_id": parts[base_idx],
                            "elevation": parts[base_idx + 1],
                            "azimuth": parts[base_idx + 2],
                            "snr": parts[base_idx + 3].split("*")[0]
                            if "*" in parts[base_idx + 3]
                            else parts[base_idx + 3],
                        }
                    )
            return {
                "type": "GPGSV",
                "num_messages": num_messages,
                "message_number": message_number,
                "num_satellites": num_satellites,
                "satellites": satellites,
            }
        elif parts[0] == "$GPVTG":
            return {
                "type": "GPVTG",
                "true_track": parts[1] + " " + parts[2] if parts[1] and parts[2] else None,
                "magnetic_track": parts[3] + " " + parts[4] if parts[3] and parts[4] else None,
                "speed_knots": parts[5] + " " + parts[6] if parts[5] and parts[6] else None,
                "speed_kmh": parts[7] + " " + parts[8].split("*")[0] if parts[7] and "*" in parts[8] else parts[7],
            }
        return None

    @staticmethod
    def parse_altitude(altitude: str) -> float:
        return float(altitude.split()[0]) if altitude else 0.0

    @staticmethod
    def parse_latitude(value: str, direction: str) -> Optional[float]:
        if value and direction:
            degrees = float(value[:2])
            minutes = float(value[2:]) / 60
            latitude = degrees + minutes
            if direction == "S":
                latitude = -latitude
            return latitude
        return None

    @staticmethod
    def parse_longitude(value: str, direction: str) -> Optional[float]:
        if value and direction:
            degrees = float(value[:3])
            minutes = float(value[3:]) / 60
            longitude = degrees + minutes
            if direction == "W":
                longitude = -longitude
            return longitude
        return None

    def print_latest_data(self) -> None:
        print("Latest GGA Data:", self._gga_data)
        print("Latest GPRMC Data:", self._gprmc_data)
        print("Latest GPGSA Data:", self._gpgsa_data)
        print("Latest GPGSV Data:", self._gpgsv_data)
        print("Latest GPVTG Data:", self._gpvtg_data)
        print("----- End of latest data -----")


async def main() -> None:
    gps_receiver = PeplinkGPSReceiver()
    await gps_receiver.start_server()

    while True:
        gps_receiver.print_latest_data()
        await asyncio.sleep(5)


if __name__ == "__main__":
    asyncio.run(main())

import asyncio
from dataclasses import dataclass
from time import time
from typing import Optional, Protocol

import lib.common.atim.implement_interface.laser_weeder as lw_if
import lib.common.atim.interface as atim_if
from config.client.cpp.config_client_python import ConfigTree
from hardware_manager.python.boards.cruise_if import <PERSON><PERSON><PERSON><PERSON>, CruiseController
from hardware_manager.python.boards.tractor_if import SafetyState
from lib.common.atim.client import AtimClient as WsAtimClient
from lib.common.logging import get_logger
from lib.common.messaging.message import msg_decoder
from lib.common.units.speed import ZERO_SPEED, Speed

LOG = get_logger(__name__)


BAORD_NAME = "atim"


class SystemController(Protocol):
    async def enable_strobe_while_lifted(self) -> bool:
        ...


class SpeedCorrector:
    def __init__(self, tree: ConfigTree) -> None:
        self._tree = tree
        self._multiplier = 1.0
        self._offset = ZERO_SPEED()
        self._cbid = self._tree.register_callback(self.__reload)
        self.__reload()

    def __reload(self) -> None:
        self._multiplier = self._tree.get_node("multiplier").get_float_value()
        self._offset.mph = self._tree.get_node("offset").get_float_value()

    def corrected(self, val: Speed) -> Speed:
        return (val * self._multiplier) + self._offset

    def uncorrected(self, val: Speed) -> Speed:
        return (val - self._offset) / self._multiplier


@dataclass
class _State:
    active: bool
    gear: atim_if.ATIMGear
    speed: Speed


class _SafetyStateWatcher:
    def __init__(self) -> None:
        self._cond = asyncio.Condition()
        self._state = SafetyState(is_safe=False, enforced=False, timestamp_ms=int(time() * 1000))

    @property
    def is_safe(self) -> bool:
        return self._state.is_safe

    @property
    def enforced(self) -> bool:
        return self._state.enforced

    async def set_is_safe(self, is_safe: bool) -> None:
        async with self._cond:
            if is_safe != self._state.is_safe:
                self._state.timestamp_ms = int(time() * 1000)
                self._state.is_safe = is_safe
                self._cond.notify_all()

    async def set_enforced(self, enforced: bool) -> None:
        async with self._cond:
            if enforced != self._state.enforced:
                self._state.timestamp_ms = int(time() * 1000)
                self._state.enforced = enforced
                self._cond.notify_all()

    async def get_next(self, timestamp_ms: int) -> SafetyState:
        async with self._cond:
            if self._state.timestamp_ms > timestamp_ms:
                return self._state
            await self._cond.wait_for(lambda: self._state.timestamp_ms > timestamp_ms)
            return self._state


def _allow_enable(state: _State) -> bool:
    return state.active and state.gear == atim_if.ATIMGear.FORWARD


class AtimClient(CruiseController):
    def __init__(self, tree: ConfigTree, ws_client: WsAtimClient, sys_controller: Optional[SystemController]) -> None:
        self._tree = tree
        self._speed_corrector = SpeedCorrector(tree.get_node("speed_correction"))
        self._ws = ws_client
        self._sys_ctl = sys_controller
        self._enabled = False
        self._version: Optional[atim_if.VersionInfo] = None
        self._vlock = asyncio.Lock()
        self._state = _State(active=False, gear=atim_if.ATIMGear.UNKNOWN, speed=ZERO_SPEED())
        self._safety_state = _SafetyStateWatcher()
        self._ws.register(atim_if.MessageType.ATIM_STATUS, self._on_atim_status)
        self._ws.register(atim_if.MessageType.SPEED_STATUS, self._on_speed_status)
        self._ws.register(atim_if.MessageType.GEAR_STATUS, self._on_gear_status)
        self._ws.register(lw_if.MessageType.SAFE_TO_OPERATE_STATE, self._on_safe_state)
        self._ws.on_connect(self._on_connect)

    async def _ainit(self) -> None:
        await self._ws.start()

    async def connected(self) -> bool:
        return await self._ws.connected()

    @staticmethod
    async def build(tree: ConfigTree, sys_controller: Optional[SystemController]) -> "AtimClient":
        override = tree.get_node("url_override").get_string_value()
        if override:
            LOG.info(f"Connecting to ATIM via override '{override}'")
            url = override
        else:
            url = atim_if.default_ws_url()
        ws = await WsAtimClient.build(url)
        c = AtimClient(tree, ws, sys_controller)
        await c._ainit()
        return c

    @msg_decoder(atim_if.ATIMStatus)
    async def _on_atim_status(self, atim_status: atim_if.ATIMStatus) -> None:
        self._state.active = atim_status.active

    @msg_decoder(atim_if.SpeedStatus)
    async def _on_speed_status(self, speed_status: atim_if.SpeedStatus) -> None:
        self._state.speed = self._speed_corrector.corrected(Speed.from_mph(speed_status.speed_mph))

    @msg_decoder(atim_if.GearStatus)
    async def _on_gear_status(self, gear_status: atim_if.GearStatus) -> None:
        self._state.gear = gear_status.gear

    @msg_decoder(lw_if.SafeOpState)
    async def _on_safe_state(self, safe_state: lw_if.SafeOpState) -> None:
        await self._safety_state.set_is_safe(safe_state.is_safe)

    async def _on_connect(self) -> None:
        await self._ws.set_interface()
        await self._get_version()
        await self._ws.set_implement_safety_policy(self._safety_state.enforced)

        if self._sys_ctl is not None and self._tree.get_node("enable_strobe_on_lift").get_bool_value():
            try:
                if await self._sys_ctl.enable_strobe_while_lifted():
                    LOG.info("strobe while lifted enabled.")
                else:
                    LOG.error("Failed to enable strobe while lifted.")
            except Exception:
                LOG.exception("Failed to enable strobe while lifted.")

    async def _get_version(self) -> atim_if.VersionInfo:
        async with self._vlock:
            if self._version is not None:
                return self._version
            if not await self._ws.connected():
                raise Exception("ATIM is not connected")
            self._version = await self._ws.get_version()
            return self._version

    async def _set_speed(self, speed: Speed) -> None:
        if not self._enabled:
            LOG.debug("Not sending speed update")
            return
        if not await self._ws.connected():
            raise Exception("No ATIM connection found")
        await self._ws.set_speed(self._speed_corrector.uncorrected(speed))

    async def set_enabled(self, enabled: bool) -> bool:
        if not await self._ws.connected():
            raise Exception("No ATIM connection found")
        if enabled:
            if _allow_enable(self._state):
                self._enabled = True
            else:
                LOG.info(f"ATIM not ready {self._state}")
        else:
            self._enabled = False
        return self._enabled

    async def set_speed(self, target_speed: Speed, _: Speed) -> Speed:
        if not await self._ws.connected():
            raise Exception("No ATIM connection found")
        try:
            await self._set_speed(target_speed)
            return self._state.speed
        except Exception:
            LOG.exception("Failed to set speed")
        return ZERO_SPEED()

    async def get_status(self) -> CCStatus:
        if not await self._ws.connected():
            return CCStatus()
        try:
            allow_enabled = _allow_enable(self._state)
            if not allow_enabled:
                self._enabled = False
            return CCStatus(
                installed=True, allow_enabled=allow_enabled, enabled=self._enabled, speed=self._state.speed,
            )
        except Exception:
            LOG.exception("Failed to get status")
        return CCStatus()

    async def set_implement_state(self, active: bool, error: bool, error_msg: Optional[str]) -> None:
        version = await self._get_version()
        if not await self._ws.connected():
            raise Exception("No ATIM connection found")
        if version < atim_if.VersionInfo(0, 0, 1):
            raise Exception(f"ATIM version {self._version} does not support implement state")
        await self._ws.set_implement_state(active, error, error_msg)

    async def set_safe_state_enforced(self, enforced: bool) -> None:
        await self._safety_state.set_enforced(enforced)
        await self._ws.set_implement_safety_policy(enforced)

    async def get_safety_state(self, timestamp_ms: int) -> SafetyState:
        return await self._safety_state.get_next(timestamp_ms)

from typing import <PERSON><PERSON>

from prometheus_client import Gauge

from lib.common.error import <PERSON>kaException
from lib.common.logging import get_logger
from lib.drivers.aleos.client import AleosClient

LOG = get_logger(__name__)


class ModemException(MakaException):
    pass


class ModemBoard:
    def __init__(self) -> None:
        self._connection_strength_lte_gauge = Gauge(
            name="connection_strength_lte", documentation="metric for connection strength"
        )
        self._client = AleosClient()

    async def get_connection_state_lte(self) -> Tuple[float, bool]:
        try:
            connection_strength_lte_resp = await self._client.get_param("system.cellular.lte.link.rsrp")
            connection_strength_lte = float(connection_strength_lte_resp.get("value", -150))
            connected_lte_resp = await self._client.get_param("system.cellular.link.state")
            connected_lte = int(connected_lte_resp.get("value", 0)) == 1
            self._connection_strength_lte_gauge.set(connection_strength_lte)
            return connection_strength_lte, connected_lte
        except Exception:
            raise ModemException("Couldn't get modem information.")

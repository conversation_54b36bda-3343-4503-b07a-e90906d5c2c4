import asyncio
from typing import Awaitable, List, cast

from hardware_manager.python.boards.board import HardwareManagedBoard
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.devices.boards.strobe_control.strobe_control_board_device import StrobeControlBoardDevice
from lib.common.devices.registry import DeviceRegistry
from lib.common.logging import get_logger
from lib.common.time.sleep import async_sleep_ms
from lib.common.time.time import maka_control_timestamp_ms

LOG = get_logger(__name__)

POLL_INTERVAL_MS = 500


class StrobeControlBoard(HardwareManagedBoard):
    def __init__(self, device_registry: DeviceRegistry) -> None:
        super().__init__(device_registry, "strobe_control_1", StrobeControlBoardDevice, "strobe")

    @property
    def device(self) -> StrobeControlBoardDevice:
        assert self._board is not None
        return cast(StrobeControlBoardDevice, self._board)

    async def start(self) -> List[Awaitable[None]]:
        return [asyncio.create_task(self._run_forever())]

    async def _await_board(self) -> None:
        self._ok = False
        LOG.error("Strobe board is unresponsive, reducing rate and attempting to re-establish communication.")
        while not bot_stop_handler.stopped:
            try:
                await cast(StrobeControlBoardDevice, self._board).ping()
                break
            except Exception:
                await asyncio.sleep(1)
        self._ok = True

    async def _run_forever(self) -> None:
        LOG.info("Strobe running forever")
        stop_blocker_event = bot_stop_handler.create_stop_blocker()
        assert self._board is not None
        try:
            last_iteration = maka_control_timestamp_ms()
            error_count = 0
            while not bot_stop_handler.stopped:
                if maka_control_timestamp_ms() - last_iteration < POLL_INTERVAL_MS:
                    await async_sleep_ms(50)
                    continue
                try:
                    await cast(StrobeControlBoardDevice, self._board).ping()
                    error_count = 0
                except Exception:
                    error_count += 1
                    if error_count > 5:
                        await self._await_board()
        finally:
            stop_blocker_event.set()

import asyncio
from typing import Optional

import lib.vt.python.vt as isobus
from config.client.cpp.config_client_python import ConfigTree
from hardware_manager.python.boards.cruise_if import <PERSON><PERSON>tatus, CruiseController
from hardware_manager.python.boards.safety_plc import SafetyBoard
from lib.common.logging import get_logger
from lib.common.units.speed import ZERO_SPEED, Speed

LOG = get_logger(__name__)

TIM_BOARD_NAME = "tim"
TECU_BOARD_NAME = "tecu"
CAN_ETHER_IP = "**********"
CAN_ETHER_PORT = 4488


class TIMBoxClient(CruiseController):
    def __init__(self, protocol: str, config: ConfigTree, safety_board: Optional[SafetyBoard]):
        isobus.initialize_vt("build/lib/vt")
        self._status = CCStatus()
        self._status.installed = True
        self._config = config
        self._safety_board = safety_board

        self._speed_control_scalar: float = 1.0

        # Get config subscriber and speed control scalar
        try:
            self._speed_control_tree = self._config.get_node("speed_control_scalar")
            self._speed_control_scalar = self._speed_control_tree.get_float_value()
            self._speed_control_tree.register_callback(self._on_speed_control_update)
        except Exception:
            LOG.exception("TIM Client failed to get config")

        # Protocol specific settings
        self._isobus_started = False
        if protocol == "tim":
            self._status.allow_enabled = False
        elif protocol == "tecu":
            self._status.allow_enabled = True
        else:
            raise ValueError("No protocol named %s" % protocol)

        self._status.enabled = False
        self._status.speed = ZERO_SPEED()
        self._protocol = protocol
        LOG.info("TIMBOX up")

    #
    # Public Methods
    #
    async def start(self) -> None:
        if not self._isobus_started:
            options: int = isobus.OPTIONS.DO_NOTHING
            if self._protocol == "tim":
                options |= isobus.OPTIONS.DO_TIM
            if self._protocol == "tecu":
                options |= isobus.OPTIONS.DO_TECU
            isobus.set_event_loop(asyncio.get_running_loop())
            isobus.set_isobus_name(
                isobus.SELF_ASSIGNED_ADDR,
                isobus.AGRICULTURAL_INDUSTRY_GROUP,
                0,  # DEVICE_CLASS INSTANCE
                0,  # DEVICE_CLASS
                0,  # FUNCTION
                0,  # FUNCTION_INSTANCE
                0,  # ECU_INSTANCE
                isobus.CARBON_MANUFACTURER_CODE,
                isobus.CARBON_IDENTIFICATION_NUMBER,
            )
            await isobus.start_isobus(
                0,
                CAN_ETHER_IP,
                CAN_ETHER_PORT,
                options,
                isobus.LOG_LEVEL.INFO,
                "build/lib/vt/certificates/timbox_v2",
                "-",
            )
            self._isobus_started = True

    #
    # Internal Methods
    #
    async def enable(self) -> None:
        self._status.enabled = True

    async def disable(self) -> None:
        if self._status.enabled:
            if self._protocol == "tim":
                await isobus.tim_stop_set_vehicle_speed()
            else:
                await isobus.tecu_stop_set_vehicle_speed()
            self._status.enabled = False

    async def set_target_speed(self, speed: Speed) -> None:
        if self._status.enabled:

            # Check if implement is lifted
            is_lifted = False
            try:
                if self._safety_board is not None:
                    status = await self._safety_board.get_status()
                    is_lifted = status.lift_prox
            except Exception as e:
                LOG.warning(f"TIM/TECU get lifted exception: {e}")

            if self._protocol == "tim":
                await isobus.tim_set_vehicle_speed(int(round(speed.mm_per_sec * self._speed_control_scalar, 0)))
            else:
                if is_lifted:
                    await isobus.tecu_stop_set_vehicle_speed()
                else:
                    await isobus.tecu_set_vehicle_speed(int(round(speed.mm_per_sec * self._speed_control_scalar, 0)))
            self._status.speed = speed

    #
    # Interface Methods
    #
    async def set_enabled(self, enabled: bool) -> bool:
        if enabled:
            await self.enable()
        else:
            await self.disable()

        return self._status.enabled

    async def set_speed(self, target_speed: Speed, current_speed: Speed) -> Speed:
        await self.set_target_speed(target_speed)
        return self._status.speed

    async def get_status(self) -> CCStatus:
        if self._protocol == "tim":
            assert isobus.get_tim_status is not None
            status = isobus.get_tim_status()
            if status == isobus.TIM_OK:
                self._status.allow_enabled = True
            else:
                self._status.allow_enabled = False
        else:
            self._status.allow_enabled = True
        return self._status

    def _on_speed_control_update(self) -> None:
        self._speed_control_scalar = self._speed_control_tree.get_float_value()
        LOG.info(f"Updated speed control scalar to {self._speed_control_scalar}")

import asyncio
from typing import Awaitable, List, Optional, Protocol, Set, Tuple, cast

import geohash
import navpy
from prometheus_client import Counter, Gauge

from config.client.cpp.config_client_python import get_global_config_subscriber
from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
from generated.core.controls.frame.model.geoposition_lat_lon_alt_message import GeopositionLatLonAltMessage
from hardware_manager.python.boards.board import HardwareManagedBoard
from lib.common.asyncio.cancellable_await import cancellable_await
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.collections.moving_average import MovingAverage
from lib.common.config.accessor import TypedConfigAccessor
from lib.common.devices.boards.gps.gps_board_device import GPSBoardDevice
from lib.common.devices.registry import DeviceRegistry
from lib.common.error import MakaException
from lib.common.gps.ntrip_client import <PERSON><PERSON><PERSON>_<PERSON>, R<PERSON><PERSON>_NTRIP_parser, SPARTN_NTRIP_parser
from lib.common.gps.robot_pose import ROBOT_POSE
from lib.common.gps.robot_pose import Position as PosePosition
from lib.common.gps.robot_pose import RelPos
from lib.common.logging import get_logger
from lib.common.time import maka_control_timestamp_ms
from lib.common.time.sleep import async_sleep_ms
from lib.common.units.angle import Angle
from lib.drivers.nanopb.benjamin_gps_board.benjamin_gps_board_connector import Position, PtpMode
from lib.rtc.data_provider.data_bus_client import DATA_BUS
from lib.rtc.messaging.gps import GPS_UPDATE_TYPE, GpsUpdate
from lib.rtc.messaging.message import Message
from tools.gps.usb_spartn_sender import send_data

LOG = get_logger(__name__)

METRICS_POLL_INTERVAL_MS = 15 * 1000


class GPSException(MakaException):
    pass


class LLAProvider(Protocol):
    @property
    def latitude(self) -> float:
        ...

    @property
    def longitude(self) -> float:
        ...

    @property
    def height_mm(self) -> int:
        ...

    @property
    def timestamp_ms(self) -> int:
        ...


class InvalidPositionError(RuntimeError):
    pass


class GPSBoard(HardwareManagedBoard):
    def __init__(self, device_registry: DeviceRegistry) -> None:
        super().__init__(device_registry, "gps_1", GPSBoardDevice, "GPS")
        pos_tree = get_global_config_subscriber().get_config_node("hardware_manager", "gps/fixed_pos")
        ROBOT_POSE.set_robot_pose(
            RelPos(x_mm=pos_tree.get_node("x").get_float_value(), y_mm=pos_tree.get_node("y").get_float_value())
        )
        self._gps_gap_count = Counter(
            name="gps_gap", documentation="Count of how many times we see a gap greater than 2x poll interval"
        )
        self._gps_timing = Gauge(name="gps_timing", documentation="average time between gps updates")
        self._geo_ecef_x = Gauge(name="robot_geo_ecef_x", documentation="robot ecef x position")
        self._geo_ecef_y = Gauge(name="robot_geo_ecef_y", documentation="robot ecef y position")
        self._geo_ecef_z = Gauge(name="robot_geo_ecef_z", documentation="robot ecef z position")
        self._heading_conf = get_global_config_subscriber().get_config_node(
            "hardware_manager", "gps/rtk/dual_gps_heading_offset"
        )
        self._heading_offset = Angle.from_degrees(self._heading_conf.get_float_value())
        self._heading_conf.register_callback(self.__reload_heading)
        self._position: Optional[Position] = None
        self._events: Set[asyncio.Event] = set()
        self.__lock: Optional[asyncio.Lock] = None
        self._loop: Optional[asyncio.AbstractEventLoop] = None
        self.__rtk_enabled = False

        self._position_ok = False
        interval_node = get_global_config_subscriber().get_config_node("hardware_manager", "gps/poll_interval")
        self._poll_interval_sec = TypedConfigAccessor(int, interval_node, self._ms_to_sec)

        self._broadcast_rtc = (
            get_global_config_subscriber()
            .get_config_node("hardware_manager", "gps/provide_controllable")
            .get_bool_value()
        )

        self._ptp_master_conf = get_global_config_subscriber().get_config_node("hardware_manager", "gps/is_ptp_master")
        self._ptp_master = self._ptp_master_conf.get_bool_value()
        self._ptp_master_conf.register_callback(self.__reload_ptp_config)

    async def start(self) -> List[Awaitable[None]]:
        self._loop = asyncio.get_event_loop()
        self.__lock = asyncio.Lock()

        try:
            await self.__set_heading_offset()
            await self.__set_ptp_config()
        except Exception:
            booted = await self.booted()
            if not booted:
                LOG.warning("Failed to set initial config, will try again on board boot")
            else:
                raise
        tasks: List[Awaitable[None]] = [
            asyncio.create_task(self._metrics()),
            asyncio.create_task(self._rtk_sender()),
            asyncio.create_task(self._get_position_task()),
        ]

        if self._broadcast_rtc:
            await DATA_BUS.start("gps", {})
            tasks.append(asyncio.create_task(self._rtc_broadcaster()))

        return tasks

    @property
    def _lock(self) -> asyncio.Lock:
        assert self.__lock is not None
        return self.__lock

    def pos_make_sense(self, position: LLAProvider) -> bool:
        if position.timestamp_ms == 0:
            return False
        if (abs(position.latitude) < 1) and (abs(position.longitude) < 1):
            return False
        return True

    def __reload_heading(self) -> None:
        self._heading_offset.degrees = self._heading_conf.get_float_value()
        if self._loop is not None:
            future = asyncio.run_coroutine_threadsafe(self.__set_heading_offset(), self._loop)
            return future.result()
        return None

    async def __set_heading_offset(self) -> None:
        assert self._board is not None, "No board instance"
        board = cast(GPSBoardDevice, self._board)

        if not board.supports_new_nanopb:
            return
        await board.set_heading_correction(self._heading_offset)

    def __reload_ptp_config(self) -> None:
        self._ptp_master = self._ptp_master_conf.get_bool_value()
        if self._loop is not None:
            future = asyncio.run_coroutine_threadsafe(self.__set_ptp_config(), self._loop)
            return future.result()
        return None

    async def __set_ptp_config(self) -> None:
        assert self._board is not None, "No board instance"
        board = cast(GPSBoardDevice, self._board)

        if not board.supports_new_nanopb:
            return

        LOG.info(f"GPS board is PTP master: {self._ptp_master}")
        await board.set_ptp_mode(PtpMode.MASTER if self._ptp_master else PtpMode.SLAVE)

    async def _await_board(self) -> None:
        self._position_ok = False
        self._ok = False  # this will be reset when board gets re-enabled
        LOG.error("GPS board is unresponsive, reducing rate and attempting to communicate.")
        while not bot_stop_handler.stopped:
            try:
                await cast(GPSBoardDevice, self._board).ping()
                await self.__set_heading_offset()
                break
            except Exception:
                LOG.exception("GPS Board not responding to ping")
                self._ok = False
                await asyncio.sleep(1)

    async def _get_position(self) -> Position:
        assert self._board is not None
        return await cast(GPSBoardDevice, self._board).position()

    @staticmethod
    def _ms_to_sec(val_ms: int) -> float:
        return val_ms / 1000.0

    async def _get_position_task(self) -> None:
        stop_blocker_event = bot_stop_handler.create_stop_blocker()
        stop_event = await bot_stop_handler.get_stop_event()
        avg_delta = MovingAverage(100)
        assert self._board is not None
        error_count = 0
        count = 0
        try:
            while not bot_stop_handler.stopped:
                try:
                    position = await self._get_position()

                    time_diff_ms = abs(maka_control_timestamp_ms() - position.timestamp_ms)
                    if time_diff_ms > 5000:
                        raise Exception(f"GPS Position too old: delta_t={time_diff_ms}, {self._position}")
                    if not self.pos_make_sense(position):
                        raise InvalidPositionError("Invalid position")
                    if self._position is not None:
                        delta = position.timestamp_ms - self._position.timestamp_ms
                        if delta == 0:
                            # got same object
                            continue
                        avg_delta.push(delta)
                        count = (count + 1) % avg_delta.window_size
                        if count == 0:
                            self._gps_timing.set(avg_delta.avg())
                        if delta > (
                            self._poll_interval_sec.value * 2000.0
                        ):  # for efficiency do the 2x interval as part of conversion to ms
                            self._gps_gap_count.inc()

                    self._position = position
                    error_count = 0
                    self._position_ok = position.have_approx_fix

                # invalid position = separate alarm, don't await the board
                except InvalidPositionError:
                    self._position_ok = False
                    await cancellable_await(asyncio.sleep(self._poll_interval_sec.value), stop_event)
                # other errors (maybe network related)
                except Exception:
                    LOG.exception("Failed to get GPS from board")
                    await cancellable_await(asyncio.sleep(self._poll_interval_sec.value), stop_event)
                    error_count += 1
                    if error_count > 5:
                        await self._await_board()
                else:
                    async with self._lock:
                        for event in self._events:
                            event.set()
                    await cancellable_await(asyncio.sleep(self._poll_interval_sec.value), stop_event)
        finally:
            stop_blocker_event.set()

    async def _rtc_broadcaster(self) -> None:
        event = asyncio.Event()
        async with self._lock:
            self._events.add(event)
        stop_event = await bot_stop_handler.get_stop_event()
        while not stop_event.is_set():
            try:
                await cancellable_await(event.wait(), stop_event)
                if stop_event.is_set():
                    return
                event.clear()
                pos = self._position
                if pos is None:
                    continue
                heading = -100000.0
                lat = pos.latitude
                lon = pos.longitude
                if pos.dual_gps_data is not None and pos.dual_gps_data.heading is not None:
                    heading = pos.dual_gps_data.heading.value
                    resp = ROBOT_POSE.get_fixed_point(
                        PosePosition(x=pos.longitude, y=pos.latitude), Angle.from_degrees(heading)
                    )
                    lat = resp.latitude
                    lon = resp.longitude
                update = GpsUpdate(
                    timestamp=pos.timestamp_ms,
                    latitude=lat,
                    longitude=lon,
                    altitude=pos.height_mm / 1000,
                    heading=heading,
                    gnssValid=pos.fix_flags.gnss_fix_ok,
                    diffSoln=pos.fix_flags.diff_soln,
                    fixType=pos.fix_type,
                    carrierPhaseSoln=pos.fix_flags.carr_soln,
                )
                msg = Message.build(GPS_UPDATE_TYPE, update)
                await DATA_BUS.broadcast_msg(msg)
            except Exception:
                LOG.exception("Unknown error waiting for next gps pos")

    async def _spartn_board_sender(
        self, change_event: asyncio.Event, reader: NTRIP_Reader, fail_counter: Counter
    ) -> None:
        assert self._board is not None
        board = cast(GPSBoardDevice, self._board)
        LOG.info("starting SPARTN data streaming")
        spartn_parser = SPARTN_NTRIP_parser(reader, board.get_latest_gga)
        while not change_event.is_set():
            try:
                async for spartn_msg in spartn_parser.parse():
                    await board.spartn(spartn_msg)
                    if change_event.is_set():
                        return
            except BaseException:
                LOG.exception("Error in SPARTN sender")
                fail_counter.inc()

    async def _rtcm_sender(self, change_event: asyncio.Event, reader: NTRIP_Reader, fail_counter: Counter) -> None:
        assert self._board is not None
        board = cast(GPSBoardDevice, self._board)
        LOG.info("starting RTCM data streaming")
        parser = RTCM_NTRIP_parser(reader, board.get_latest_gga)
        while not change_event.is_set():
            try:
                async for msg in parser.parse():
                    await board.rtcm(msg)
                    if change_event.is_set():
                        return
            except BaseException:
                LOG.exception("Error in RTCM sender")
                fail_counter.inc()

    async def _rtk_sender(self) -> None:
        gps_rtk_node = get_global_config_subscriber().get_config_node("hardware_manager", "gps/rtk")
        change_event = asyncio.Event()
        loop = asyncio.get_event_loop()

        def __change_cb() -> None:
            loop.call_soon_threadsafe(change_event.set)

        bot_stop_handler.add_callback(__change_cb)
        usb_port = gps_rtk_node.get_node("usb_port").get_string_value()
        mount_point = gps_rtk_node.get_node("mount_point").get_string_value()
        baudrate = gps_rtk_node.get_node("baudrate").get_int_value()
        uri = gps_rtk_node.get_node("uri").get_string_value()
        username = gps_rtk_node.get_node("username").get_string_value()
        password = gps_rtk_node.get_node("password").get_string_value()
        use_nanopb = gps_rtk_node.get_node("nanopb_support").get_bool_value()
        correction_type = gps_rtk_node.get_node("correction_type").get_string_value().upper()
        enabled_node = gps_rtk_node.get_node("enabled")
        rtk_state_guage = Gauge(name="rtk_state", documentation="Determine if we are connected to an NTRIP caster")
        ntrip_fail_count = Counter(
            name="ntrip_fail_count",
            documentation="Count of how many times the ntrip client fails and we need to reconnect",
        )

        def __connect_change(connected: bool) -> None:
            rtk_state_guage.set(1 if connected else 0)

        reader = NTRIP_Reader(
            uri=uri, mount=mount_point, username=username, passwd=password, connect_change=__connect_change
        )
        self.__rtk_enabled = enabled_node.get_bool_value()

        def __enable_cb() -> None:
            self.__rtk_enabled = enabled_node.get_bool_value()
            __change_cb()

        enabled_node.register_callback(__enable_cb)
        while not bot_stop_handler.stopped:
            if change_event.is_set():
                change_event.clear()
                continue
            if self.__rtk_enabled:
                LOG.info("starting RTK correction data streaming")
                if use_nanopb:
                    if correction_type == "SPARTN":
                        await self._spartn_board_sender(change_event, reader, ntrip_fail_count)
                    elif correction_type == "RTCM":
                        await self._rtcm_sender(change_event, reader, ntrip_fail_count)
                    else:
                        LOG.error(f"unsupported corrections type {correction_type}, no rtk will be used")
                        return
                else:
                    if correction_type != "SPARTN":
                        LOG.error(f"unsupported corrections type {correction_type} for USB, no rtk will be used")
                        return
                    await send_data(change_event, reader, usb_port, baudrate)
                LOG.info("stopped RTK correction data streaming")
            else:
                await change_event.wait()

    async def position_lla(self) -> GeopositionLatLonAltMessage:
        try:
            assert self._board is not None
            position = self._position
            if position is None or not position.have_approx_fix:
                raise GPSException("Invalid gps position")
            return GeopositionLatLonAltMessage(
                timestamp_ms=position.timestamp_ms,
                lat=position.latitude,
                lon=position.longitude,
                alt=position.height_mm / 1000.0,
            )
        except Exception:
            LOG.exception("Failed to get lla")
            return GeopositionLatLonAltMessage(timestamp_ms=0, lat=0, lon=0, alt=0)

    async def get_next_position_lla(self, last_ts: int) -> GeopositionLatLonAltMessage:
        def check() -> Optional[GeopositionLatLonAltMessage]:
            curr = self._position
            if curr is not None and curr.timestamp_ms > last_ts:
                return GeopositionLatLonAltMessage(
                    timestamp_ms=curr.timestamp_ms, lat=curr.latitude, lon=curr.longitude, alt=0
                )
            return None

        # fast-path check to avoid locking if possible
        result = check()
        if result is not None:
            return result

        event = asyncio.Event()
        async with self._lock:
            self._events.add(event)
        try:
            while True:
                try:
                    await asyncio.wait_for(event.wait(), timeout=1)
                except asyncio.TimeoutError:
                    pass
                event.clear()
                result = check()
                if result is not None:
                    return result
        finally:
            async with self._lock:
                self._events.discard(event)

    async def get_next_position(self, last_ts: int) -> Position:
        # fast-path check to avoid locking if possible
        if self._position is not None and self._position.timestamp_ms > last_ts:
            return self._position

        event = asyncio.Event()
        async with self._lock:
            self._events.add(event)
        try:
            while True:
                try:
                    await asyncio.wait_for(event.wait(), timeout=1)
                except asyncio.TimeoutError:
                    pass
                event.clear()
                if self._position is not None and self._position.timestamp_ms > last_ts:
                    return self._position
        finally:
            async with self._lock:
                self._events.discard(event)

    async def position_lla_and_ecef(self) -> Tuple[GeopositionLatLonAltMessage, GeopositionEcefMessage]:
        try:
            assert self._board is not None
            lla = await self.position_lla()
            if lla.timestamp_ms == 0:
                return lla, GeopositionEcefMessage(timestamp_ms=0, x=0, y=0, z=0)
            ecef = navpy.lla2ecef(lla.lat, lla.lon, lla.alt, "deg")
            return lla, GeopositionEcefMessage(timestamp_ms=lla.timestamp_ms, x=ecef[0], y=ecef[1], z=ecef[2])
        except GPSException:
            raise
        except Exception:
            LOG.exception("Failed to get ecef")
            return (
                GeopositionLatLonAltMessage(timestamp_ms=0, lat=0, lon=0, alt=0),
                GeopositionEcefMessage(timestamp_ms=0, x=0, y=0, z=0),
            )

    async def get_geohash(self) -> Tuple[int, str]:
        try:
            position = await self.position_lla()
            lat = position.lat
            lng = position.lon
            timestamp_ms = maka_control_timestamp_ms()

            geo_hash = geohash.encode(lat, lng, precision=9)
            return timestamp_ms, geo_hash
        except GPSException:
            raise
        except Exception:  # noqa
            raise GPSException("Couldn't get gps information.")

    async def _metrics(self) -> None:
        LOG.info("GPS running forever")
        stop_blocker_event = bot_stop_handler.create_stop_blocker()
        event = asyncio.Event()
        gps_fix_quality = Gauge(name="gps_fix_qualtity", documentation="quality of the gps fix", labelnames=["type"])
        async with self._lock:
            self._events.add(event)
        assert self._board is not None
        try:
            last_iteration = maka_control_timestamp_ms()
            while not bot_stop_handler.stopped:
                try:
                    await asyncio.wait_for(event.wait(), timeout=1)
                except asyncio.TimeoutError:
                    # want to timeout here so we can check for bot stop.
                    continue
                event.clear()
                if maka_control_timestamp_ms() - last_iteration < METRICS_POLL_INTERVAL_MS:
                    await async_sleep_ms(50)
                    continue
                try:
                    lla, ecef = await self.position_lla_and_ecef()
                    pos = self._position
                except GPSException:
                    LOG.warning("Could not fetch gps data, not updating metrics")
                    continue
                if lla.timestamp_ms != 0:
                    self._geo_ecef_x.set(ecef.x)
                    self._geo_ecef_y.set(ecef.y)
                    self._geo_ecef_z.set(ecef.z)
                if pos is not None:
                    gps_fix_quality.labels("fix_type").set(pos.fix_type.value)
                    gps_fix_quality.labels("carrier_solution").set(pos.fix_flags.carr_soln.value)
                    # TODO alarm if rtk is enabled and carrier solution is None
                last_iteration = maka_control_timestamp_ms()
        finally:
            stop_blocker_event.set()

    @property
    def ok(self) -> bool:
        return self._ok

    @property
    def has_fix(self) -> bool:
        return self._position_ok

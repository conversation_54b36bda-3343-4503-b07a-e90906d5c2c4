import abc
from typing import Awaitable, List, Optional, Type, TypeVar

import lib.common.logging
from lib.common.devices.device import Device, DeviceStatusCode
from lib.common.devices.registry import DeviceRegistry, DeviceRegistryException

LOG = lib.common.logging.get_logger(__name__)

T = TypeVar("T", bound=Device)


class HardwareManagedBoard:
    def __init__(self, device_registry: DeviceRegistry, device_id: str, device_type: Type[T], board_name: str) -> None:
        self._device_registry = device_registry
        self._board: Optional[T] = None
        self._device_id = device_id
        self._device_type = device_type
        self._board_name = board_name
        self._booted = False
        self._ok = False

    async def load_device(self) -> None:
        try:
            LOG.info(f"Booting board {self._device_id}")
            self._board = await self._device_registry.get_device(self._device_type, self._device_id)
            assert self._board is not None
            self._ok = await self.booted()
            LOG.info(f"Booted board {self._device_id}")
        except DeviceRegistryException:
            self._ok = False
            raise DeviceRegistryException(f"Error loading device {self._device_id}")

    async def enable_device(self) -> None:
        LOG.info(f"Enabling Device: {self._device_id}")
        try:
            self._board = await self._device_registry.get_device(self._device_type, self._device_id)
            assert self._board is not None
            await self._board.enable()
            self._ok = await self.booted() and await self.board_booted()
        except DeviceRegistryException:
            self._ok = False
            raise DeviceRegistryException(f"Error enabling {self._device_id}")

    async def disable_device(self) -> None:
        LOG.info(f"Disabling Device: {self._device_id}")
        try:
            self._board = await self._device_registry.get_device(self._device_type, self._device_id)
            assert self._board is not None
            await self._board.disable()
        except DeviceRegistryException:
            self._ok = False
            raise DeviceRegistryException(f"Error disabling {self._device_id}")

    @abc.abstractmethod
    async def start(self) -> List[Awaitable[None]]:
        pass

    async def booted(self) -> bool:
        return (await self.device_status()) in [DeviceStatusCode.OK, DeviceStatusCode.WARNING]

    async def board_booted(self) -> bool:
        return True

    @property
    def ok(self) -> bool:
        return self._ok

    @property
    def board_name(self) -> str:
        return self._board_name

    @property
    def device_id(self) -> str:
        return self._device_id

    async def device_status(self) -> DeviceStatusCode:
        assert self._board is not None
        return (await self._board.get_status()).code

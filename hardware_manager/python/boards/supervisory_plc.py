import asyncio
from typing import Any, Awaitable, Dict, List, cast

from prometheus_client import Gauge

from config.client.cpp.config_client_python import ConfigSubscriber
from hardware_manager.python.boards.board import HardwareManagedBoard
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.devices.boards.supervisory_plc.supervisory_plc_device import SupervisoryPLCDevice
from lib.common.devices.registry import DeviceRegistry
from lib.common.generation import is_slayer
from lib.common.logging import get_logger

LOG = get_logger(__name__)

METRICS_POLL_INTERVAL_MS = 15 * 1000


class SupervisoryBoard(HardwareManagedBoard):
    def __init__(self, device_registry: DeviceRegistry, config_subscriber: ConfigSubscriber) -> None:
        super().__init__(device_registry, "supervisory_plc_1", SupervisoryPLCDevice, "supervisory")

        self._config_subscriber = config_subscriber
        self._240v_uptime = Gauge(
            name="uptime_240v", documentation="Time elapsed since main contactor connected [seconds]"
        )
        self._12v_lifetime_hours = Gauge(name="lifetime_12v", documentation="Lifetime in hours the 12v system has run.")
        self._240v_lifetime_hours = Gauge(
            name="lifetime_240v", documentation="Lifetime in hours the 240v system has run."
        )
        self._chiller_lifetime_hours = Gauge(
            name="lifetime_chiller", documentation="Lifetime in hours the chiller has run."
        )
        self._ac_lifetime_hours = Gauge(
            name="lifetime_ac", documentation="Lifetime in hours the air conditioner has run."
        )
        self._btl_lifetime_hours = Gauge(
            name="lifetime_btl", documentation="Lifetime in hours the bed top lights have run."
        )
        self._main_contactor_lifetime_count = Gauge(
            name="lifetime_main_contactor", documentation="Lifetime cycle count for the main contactor"
        )
        self._battery_lifetime_count = Gauge(
            name="lifetime_battery", documentation="Lifetime cycle count for the battery contactor"
        )

    async def board_booted(self) -> bool:
        return await self._cast_board().ping()

    async def get_240v_uptime(self) -> int:
        try:
            status = await self._cast_board().get_240v_uptime()
            self._ok = True
        except Exception:
            LOG.warning("Error getting uptime", exc_info=True)
            self._ok = False
            raise
        return status

    async def get_240v_runtime(self) -> int:
        try:
            status = await self._cast_board().get_240v_lifetime_hours()
            self._ok = True
        except Exception:
            LOG.warning("Error getting runtime", exc_info=True)
            self._ok = False
            raise
        return status

    async def get_plc_output(self) -> Dict[str, Any]:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).get_plc_output()
            self._ok = True
        except Exception:
            LOG.warning("Error getting supervisory status", exc_info=True)
            self._ok = False
            raise
        return status

    async def enable_row(self, row_id: int) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).enable_row(row_id)
            if status:
                self._ok = True
            else:
                self._ok = False
        except Exception:
            LOG.warning("Failed to enable row", exc_info=True)
            self._ok = False
            raise
        return status

    async def disable_row(self, row_id: int) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).disable_row(row_id)
            if status:
                self._ok = True
            else:
                self._ok = False
        except Exception:
            LOG.warning("Failed to disable row", exc_info=True)
            self._ok = False
            raise
        return status

    async def enable_btl(self, row_id: int) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).enable_btl(row_id)
            if status:
                self._ok = True
            else:
                self._ok = False
        except Exception:
            LOG.warning("Failed to enable btl", exc_info=True)
            self._ok = False
            raise
        return status

    async def disable_btl(self, row_id: int) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).disable_btl(row_id)
            if status:
                self._ok = True
            else:
                self._ok = False
        except Exception:
            LOG.warning("Failed to disable btl", exc_info=True)
            self._ok = False
            raise
        return status

    async def enable_scanners(self, row_id: int) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).enable_scanners(row_id)
            if status:
                self._ok = True
            else:
                self._ok = False
        except Exception:
            LOG.warning("Failed to enable scanners", exc_info=True)
            self._ok = False
            raise
        return status

    async def disable_scanners(self, row_id: int) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).disable_scanners(row_id)
            if status:
                self._ok = True
            else:
                self._ok = False
        except Exception:
            LOG.warning("Failed to disable scanners", exc_info=True)
            self._ok = False
            raise
        return status

    async def enable_wheel_encoder(self) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).enable_wheel_encoder()
            if status:
                self._ok = True
            else:
                self._ok = False
        except Exception:
            LOG.warning("Failed to enable wheel_encoder", exc_info=True)
            self._ok = False
            raise
        return status

    async def disable_wheel_encoder(self) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).disable_wheel_encoder()
            if status:
                self._ok = True
            else:
                self._ok = False
        except Exception:
            LOG.warning("Failed to disable wheel_encoder", exc_info=True)
            self._ok = False
            raise
        return status

    async def enable_strobe(self) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).enable_strobe()
            if status:
                self._ok = True
            else:
                self._ok = False
        except Exception:
            LOG.warning("Failed to enable strobe", exc_info=True)
            self._ok = False
            raise
        return status

    async def disable_strobe(self) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).disable_strobe()
            if status:
                self._ok = True
            else:
                self._ok = False
        except Exception:
            LOG.warning("Failed to disable strobe", exc_info=True)
            self._ok = False
            raise
        return status

    async def enable_gps(self) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).enable_gps()
            if status:
                self._ok = True
            else:
                self._ok = False
        except Exception:
            LOG.warning("Failed to enable gps", exc_info=True)
            self._ok = False
            raise
        return status

    async def disable_gps(self) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).disable_gps()
            if status:
                self._ok = True
            else:
                self._ok = False
        except Exception:
            LOG.warning("Failed to disable gps", exc_info=True)
            self._ok = False
            raise
        return status

    async def command_computer_power_cycle(self) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).command_computer_power_cycle()
            if status:
                self._ok = True
            else:
                self._ok = False
        except Exception:
            LOG.warning("Failed to power cycle the command computer", exc_info=True)
            self._ok = False
            raise
        return status

    async def enable_main_contactor_disabled(self) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).enable_main_contactor_disabled()
            if status:
                self._ok = True
            else:
                self._ok = False
        except Exception:
            LOG.warning("Failed to enable the disabling of the main contactor", exc_info=True)
            self._ok = False
            raise
        return status

    async def disable_main_contactor_disabled(self) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).disable_main_contactor_disabled()
            if status:
                self._ok = True
            else:
                self._ok = False
        except Exception:
            LOG.warning("Failed to disable the disabling of the main contactor", exc_info=True)
            self._ok = False
            raise
        return status

    async def enable_air_conditioner(self) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).enable_air_conditioner()
            if status:
                self._ok = True
            else:
                self._ok = False
        except Exception:
            LOG.warning("Failed to enable air_conditioner", exc_info=True)
            self._ok = False
            raise
        return status

    async def disable_air_conditioner(self) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).disable_air_conditioner()
            if status:
                self._ok = True
            else:
                self._ok = False
        except Exception:
            LOG.warning("Failed to disable air_conditioner", exc_info=True)
            self._ok = False
            raise
        return status

    async def enable_chiller(self) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).enable_chiller()
            if status:
                self._ok = True
            else:
                self._ok = False
        except Exception:
            LOG.warning("Failed to enable chiller", exc_info=True)
            self._ok = False
            raise
        return status

    async def disable_chiller(self) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).disable_chiller()
            if status:
                self._ok = True
            else:
                self._ok = False
        except Exception:
            LOG.warning("Failed to disable chiller", exc_info=True)
            self._ok = False
            raise
        return status

    async def set_chiller_temp(self, temp: float) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).set_chiller_temp(temp)
            if status:
                self._ok = True
            else:
                self._ok = False
            LOG.info(f"Chiller temp set to {temp}C")
        except Exception:
            LOG.warning("Failed to set chiller temp", exc_info=True)
            self._ok = False
            raise
        return status

    async def enable_temp_bypass(self) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).enable_temp_bypass()
            if status:
                self._ok = True
            else:
                self._ok = False
            LOG.info("temp_bypass enabled")
        except Exception:
            LOG.warning("Failed to enable temp_bypass", exc_info=True)
            self._ok = False
            raise
        return status

    async def disable_temp_bypass(self) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).disable_temp_bypass()
            if status:
                self._ok = True
            else:
                self._ok = False
            LOG.info("temp_bypass disabled")
        except Exception:
            LOG.warning("Failed to disable temp_bypass", exc_info=True)
            self._ok = False
        return status

    async def enable_humidity_bypass(self) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).enable_humidity_bypass()
            if status:
                self._ok = True
            else:
                self._ok = False
            LOG.info("humidity_bypass enabled")
        except Exception:
            LOG.warning("Failed to enable humidity_bypass", exc_info=True)
            self._ok = False
        return status

    async def disable_humidity_bypass(self) -> bool:
        try:
            status = await cast(SupervisoryPLCDevice, self._board).disable_humidity_bypass()
            if status:
                self._ok = True
            else:
                self._ok = False
            LOG.info("humidity_bypass disabled")
        except Exception:
            LOG.warning("Failed to disable humidity_bypass", exc_info=True)
            self._ok = False
            raise
        return status

    async def enable_strobe_while_lifted(self) -> bool:
        if not is_slayer():
            # TODO determine what is needed for reaper
            LOG.warning("This robot generation does not support this feature.")
            return False
        try:
            status = await cast(SupervisoryPLCDevice, self._board).enable_strobe_while_lifted()
            if status:
                self._ok = True
            else:
                self._ok = False
            LOG.info(f"Strobe while lifted {status}")
        except Exception:
            LOG.warning("Failed to enable strobe while lifted", exc_info=True)
            self._ok = False
            raise
        return status

    async def temp_humidity_bypass(self) -> None:
        temp_node = self._config_subscriber.get_config_node("hardware_manager", "temp_bypass")

        if temp_node.get_bool_value():
            await self.enable_temp_bypass()
        else:
            await self.disable_temp_bypass()

        humidity_node = self._config_subscriber.get_config_node("hardware_manager", "humidity_bypass")

        if humidity_node.get_bool_value():
            await self.enable_humidity_bypass()
        else:
            await self.disable_humidity_bypass()

    async def chiller_temp_set(self) -> None:
        chiller_temp_node = self._config_subscriber.get_config_node("common", "expected_chiller_temp")
        await self.set_chiller_temp(chiller_temp_node.get_float_value())

    async def start(self) -> List[Awaitable[None]]:
        local_loop = asyncio.get_running_loop()

        # temp/humidity bypass is only on slayer
        if is_slayer():

            def callback() -> None:
                asyncio.run_coroutine_threadsafe(self.temp_humidity_bypass(), local_loop).result()

            humidity_bypass_node = self._config_subscriber.get_config_node("hardware_manager", "humidity_bypass")
            humidity_bypass_node.register_callback(callback)

            temp_bypass_node = self._config_subscriber.get_config_node("hardware_manager", "temp_bypass")
            temp_bypass_node.register_callback(callback)

            await self.temp_humidity_bypass()

        # chiller temp set via config for all generations
        def temp_callback() -> None:
            asyncio.run_coroutine_threadsafe(self.chiller_temp_set(), local_loop).result()

        chiller_temp_node = self._config_subscriber.get_config_node("common", "expected_chiller_temp")
        chiller_temp_node.register_callback(temp_callback)

        await self.chiller_temp_set()

        return [asyncio.create_task(self._metrics())]

    async def _metrics(self) -> None:
        assert self._board is not None
        with bot_stop_handler.scoped_bot_stop_blocker("supervisory_plc_metrics") as bot_stop:
            while not bot_stop.is_stopped():
                try:
                    self._240v_uptime.set(await self._cast_board().get_240v_uptime())
                    self._12v_lifetime_hours.set(await self._cast_board().get_12v_lifetime_hours())
                    self._240v_lifetime_hours.set(await self._cast_board().get_240v_lifetime_hours())
                    self._chiller_lifetime_hours.set(await self._cast_board().get_chiller_lifetime_hours())
                    self._ac_lifetime_hours.set(await self._cast_board().get_ac_lifetime_hours())
                    self._btl_lifetime_hours.set(await self._cast_board().get_btl_lifetime_hours())
                    self._main_contactor_lifetime_count.set(
                        await self._cast_board().get_main_contactor_lifetime_cycles()
                    )
                    self._battery_lifetime_count.set(await self._cast_board().get_battery_lifetime_cycles())
                except Exception as e:
                    LOG.warning(f"Failed to set plc metrics, exception = {e}")
                await bot_stop_handler.async_sleep_safe(METRICS_POLL_INTERVAL_MS)

    def _cast_board(self) -> SupervisoryPLCDevice:
        return cast(SupervisoryPLCDevice, self._board)

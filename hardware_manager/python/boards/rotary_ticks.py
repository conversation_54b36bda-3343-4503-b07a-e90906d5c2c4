import asyncio
from asyncio import Lock
from collections import deque
from typing import Awaitable, Deque, Dict, List, Optional, Tuple, cast

from config.client.cpp.config_client_python import get_global_config_subscriber
from hardware_manager.python.boards.board import HardwareManagedBoard
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.devices.boards.nofx.nofx_board_device import NoFXBoardDevice
from lib.common.devices.registry import DeviceRegistry
from lib.common.error import MakaException
from lib.common.logging import get_logger
from lib.common.time.sleep import async_sleep_ms
from lib.common.time.time import maka_control_timestamp_ms

LOG = get_logger(__name__)

POLL_INTERVAL_MS = 250


class RotaryTicksException(MakaException):
    pass


class TimestampedSnapshot:
    def __init__(self, *, fl: int, fr: int, bl: int, br: int, time_ms: int):
        self._time_ms = time_ms
        self._fl = fl
        self._fr = fr
        self._bl = bl
        self._br = br

    @property
    def time_ms(self) -> int:
        return self._time_ms

    @property
    def front_left(self) -> int:
        return self._fl

    @property
    def front_right(self) -> int:
        return self._fr

    @property
    def back_left(self) -> int:
        return self._bl

    @property
    def back_right(self) -> int:
        return self._br


class TimestampedSnapshots:
    def __init__(self, max_items: int = 120):
        self._snapshots: Deque[TimestampedSnapshot] = deque([])
        self._max_items = max_items
        self._full = False
        self._ticks_lock = Lock()

    def __len__(self) -> int:
        return len(self._snapshots)

    async def append(self, snapshot: TimestampedSnapshot) -> None:
        async with self._ticks_lock:
            if len(self._snapshots) >= self._max_items:
                self._full = True
                self._snapshots.popleft()
            self._snapshots.append(snapshot)

    async def is_full(self) -> bool:
        async with self._ticks_lock:
            return self._full

    async def get_first_snapshot(self) -> TimestampedSnapshot:
        async with self._ticks_lock:
            if len(self._snapshots) == 0:
                raise RotaryTicksException("No items in snapshot list.")

            return self._snapshots[0]

    async def get_last_snapshot(self) -> TimestampedSnapshot:
        async with self._ticks_lock:
            if len(self._snapshots) == 0:
                raise RotaryTicksException("No items in snapshot list.")

            return self._snapshots[-1]

    async def get_nth_snapshot(self, n: int) -> TimestampedSnapshot:
        async with self._ticks_lock:
            if len(self._snapshots) == 0:
                raise RotaryTicksException("No items in snapshot list.")

            return self._snapshots[n]

    async def get_snapshot(self, timestamp_ms: int) -> TimestampedSnapshot:
        async with self._ticks_lock:
            for snapshot in self._snapshots:
                if snapshot.time_ms == timestamp_ms:
                    return snapshot

            error_string = ""
            if len(self._snapshots) == 0:
                error_string = "No items in snapshot list."
            else:
                error_string = f"Snapshot with timestamp {timestamp_ms} not found."

            raise RotaryTicksException(error_string)

    @property
    def max_size(self) -> int:
        return self._max_items


class RotaryEncoderBoard(HardwareManagedBoard):
    def __init__(self, device_registry: DeviceRegistry, lock: asyncio.Lock) -> None:
        super().__init__(device_registry, "nofx_board_1", NoFXBoardDevice, "encoder")
        self._ticks = TimestampedSnapshots()
        self._wheel_encoder_types: Optional[Dict[str, str]] = None
        self._lock: asyncio.Lock = lock
        self._last_pos: Dict[str, float] = {}

    async def start(self) -> List[Awaitable[None]]:
        return [asyncio.create_task(self._run_forever())]

    async def _await_board(self) -> None:
        self._ok = False
        LOG.error("Rotary Tick board is unresponsive, reducing rate and attempting to communicate.")
        while not bot_stop_handler.stopped:
            try:
                await cast(NoFXBoardDevice, self._board).ping()
                break
            except Exception:
                await asyncio.sleep(1)
        self._ok = True

    async def _run_forever(self) -> None:
        LOG.info("Ticks running forever")
        stop_blocker_event = bot_stop_handler.create_stop_blocker()
        assert self._board is not None
        board = cast(NoFXBoardDevice, self._board)
        self._wheel_encoder_types = board.wheel_encoder_types
        try:
            last_iteration = maka_control_timestamp_ms()
            while not bot_stop_handler.stopped:
                if maka_control_timestamp_ms() - last_iteration < POLL_INTERVAL_MS:
                    await async_sleep_ms(50)
                    continue
                if board.error:
                    await self._await_board()
                try:
                    _ticks = await board.get_cur()
                    await self._ticks.append(
                        TimestampedSnapshot(
                            time_ms=_ticks.msec,
                            fl=_ticks.front_left,
                            fr=_ticks.front_right,
                            bl=_ticks.back_left,
                            br=_ticks.back_right,
                        )
                    )
                    last_iteration = maka_control_timestamp_ms()
                except Exception:
                    continue
        finally:
            stop_blocker_event.set()

    async def get_snapshot_at_timestamp(self, timestamp_ms: int) -> TimestampedSnapshot:
        return await self._ticks.get_snapshot(timestamp_ms)

    async def get_first_snapshot(self) -> TimestampedSnapshot:
        return await self._ticks.get_first_snapshot()

    async def get_last_snapshot(self) -> TimestampedSnapshot:
        return await self._ticks.get_last_snapshot()

    async def get_dist(self, before: int = -2, after: int = -1, diff: bool = False) -> Tuple[float, int, int]:
        assert self._board is not None
        assert self._wheel_encoder_types is not None
        if len(self._ticks) < 2:
            return 0, 0, 0

        after_snapshot = await self._ticks.get_nth_snapshot(after)
        before_snapshot = await self._ticks.get_nth_snapshot(before)
        after_tuple = (
            after_snapshot.front_left,
            after_snapshot.front_right,
            after_snapshot.back_left,
            after_snapshot.back_right,
        )
        if diff:
            before_tuple = (
                before_snapshot.front_left,
                before_snapshot.front_right,
                before_snapshot.back_left,
                before_snapshot.back_right,
            )
        else:
            before_tuple = (0, 0, 0, 0)
        avg_ticks = cast(NoFXBoardDevice, self._board).avg_dist(before_tuple, after_tuple)

        return avg_ticks, after_snapshot.time_ms, before_snapshot.time_ms

    async def get_cur_pos_mm(self) -> Tuple[int, float]:
        [timestamp_ms, pos] = await asyncio.get_event_loop().run_in_executor(None, self.board.get_cur_pos)
        return (timestamp_ms, pos * 1000.0)  # convert meters to mm

    async def get_delta_pos_mm(self, id: str) -> float:
        pos = await asyncio.get_event_loop().run_in_executor(None, self.board.total_meters)
        prev_pos = 0.0
        async with self._lock:
            if id in self._last_pos:
                prev_pos = self._last_pos[id]
            self._last_pos[id] = pos
        return (pos - prev_pos) * 1000.0

    async def get_velocity(self) -> Tuple[float, int]:
        smoothing_factor = (
            get_global_config_subscriber().get_config_node("hardware_manager", "velocity_smoothing").get_uint_value()
        )
        before = 0
        if smoothing_factor == 0:
            before = -2
        elif smoothing_factor == 100:
            before = 0
        elif await self._ticks.is_full():
            before = round((1 - (smoothing_factor / 100)) * self._ticks.max_size) - 1
            if before < 0:
                before = 0
            elif before > self._ticks.max_size - 1:
                before = -2

        distance_mm, after_time, before_time = await self.get_dist(before=before, after=-1, diff=True)
        delta_ms = after_time - before_time
        if delta_ms == 0:
            return 0, after_time

        return distance_mm / delta_ms, after_time

    @property
    def board(self) -> NoFXBoardDevice:
        assert self._board is not None
        return cast(NoFXBoardDevice, self._board)

    @property
    def encoder_error_msg(self) -> Optional[str]:
        return self.board.encoder_error_msg

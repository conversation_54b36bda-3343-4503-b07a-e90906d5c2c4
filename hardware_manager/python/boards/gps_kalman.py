import csv
import logging
from argparse import ArgumentParser
from collections import deque
from typing import Deque, List, Tuple

import matplotlib.pyplot as plt
import numpy as np
from filterpy.kalman import KalmanFilter

LOG = logging.getLogger(__name__)


class GPSLLAKalmanFilter:
    def __init__(self) -> None:
        self._initialized = False
        self._n = 300  # of GPS data point we look at to decide wether we are moving fast or not at 1 sample per second this is 5 minutes
        self._last_n_positions: Deque[Tuple[float, float, float]] = deque()
        self._kf = KalmanFilter(dim_x=6, dim_z=3)
        self._dt = 1  # time delta
        self._kf.F = np.array(
            [
                [1, self._dt, 0, 0, 0, 0],  # Latitude position
                [0, 1, 0, 0, 0, 0],  # Latitude velocity
                [0, 0, 1, self._dt, 0, 0],  # Longitude position
                [0, 0, 0, 1, 0, 0],  # Longitude velocity
                [0, 0, 0, 0, 1, self._dt],  # Altitude position
                [0, 0, 0, 0, 0, 1],  # Altitude velocity
            ]
        )
        self._kf.H = np.array(
            [
                [1, 0, 0, 0, 0, 0],  # Map latitude position
                [0, 0, 1, 0, 0, 0],  # Map longitude position
                [0, 0, 0, 0, 1, 0],  # Map altitude position
            ]
        )
        self._r_lat = 0.2
        self._r_long = 0.2
        self._r_alt = 1.0
        self._kf.R = np.array(
            [[self._r_lat, 0, 0], [0, self._r_long, 0], [0, 0, self._r_alt]]
        )  # Measurement noise covariance
        self._kf.Q = np.eye(6) * 0.0001  # Process noise covariance
        self._kf.P = np.eye(6) * 500
        self._lat_long_exp = 1.2

    def reset(self, pos: Tuple[float, float, float]) -> None:
        self._kf.x = np.array([pos[0], 0, pos[1], 0, pos[2], 0])  # Initial state

    def set_n(self, n: int) -> None:
        self._n = n

    def set_lat_long_exp(self, exp: float) -> None:
        self._lat_long_exp = exp

    def set_process_noise(self, q_lat_long: float, q_alt: float, q_lat_long_vel: float, q_alt_vel: float) -> None:
        self._kf.Q = np.diag([q_lat_long, q_lat_long_vel, q_lat_long, q_lat_long_vel, q_alt, q_alt_vel])

    def set_measurement_noise(self, r_lat_long: float, r_alt: float) -> None:
        self._r_lat = r_lat_long
        self._r_long = r_lat_long
        self._r_alt = r_alt
        self._kf.R = np.diag([r_lat_long, r_lat_long, r_alt])

    def next_value(self, pos: Tuple[float, float, float]) -> Tuple[float, float, float]:
        if not self._initialized:
            self._kf.x = np.array([pos[0], 0, pos[1], 0, pos[2], 0])  # Initial state
            self._initialized = True
        # Maintain last n position so we can lookback
        self._last_n_positions.append(pos)
        while len(self._last_n_positions) > self._n:
            self._last_n_positions.popleft()

        # If our lookback data is valid, we determine how much / how fast we are actually travelling in a particular direction
        if len(self._last_n_positions) >= 2:
            # We sum the relative motion in each dimension, that way if there is a lot of wiggle in 1 dimension we can tone it down
            # It will be interesting to see how such noise manifests in non-geo-aligned fields
            lat_sum = 0.0
            long_sum = 0.0
            alt_sum = 0.0
            for i, (lat, long, alt) in enumerate(self._last_n_positions):
                if i == 0:
                    continue
                lat_sum += self._last_n_positions[i - 1][0] - lat
                long_sum += self._last_n_positions[i - 1][1] - long
                alt_sum += self._last_n_positions[i - 1][2] - alt
            delta_lat = abs(lat_sum)
            delta_long = abs(long_sum)

            # At Low speed, we manually adjust the measurement covariance based on dimensional relative motion
            new_lat_r = (((180 / (delta_lat ** self._lat_long_exp)) / 10000000) if delta_lat > 0 else 1) * self._r_lat
            new_long_r = (
                ((180 / (delta_long ** self._lat_long_exp)) / 10000000) if delta_long > 0 else 1
            ) * self._r_long
            self._kf.R = np.diag([new_lat_r, new_long_r, self._r_alt])

        z = np.array(pos)  # Measurement
        self._kf.predict()
        self._kf.update(z)
        LOG.debug(f"Kalman Input: {pos} Output: {(self._kf.x[0], self._kf.x[2], self._kf.x[4])}")
        return self._kf.x[0], self._kf.x[2], self._kf.x[4]


def main() -> None:
    parser = ArgumentParser("GPS Kalman Testing Tool")
    parser.add_argument(
        "data", type=str, help="Path to CSV file to load data from, expected to be written by robot hardware manager"
    )
    args = parser.parse_args()

    # Read the CSV file
    with open(args.data, mode="r") as file:
        reader = csv.reader(file)
        _ = next(reader)  # Skip the header row
        loaded_lat: List[float] = []
        loaded_lon: List[float] = []
        loaded_alt: List[float] = []
        for row in reader:
            # Parse each row
            _, latitude, longitude, altitude = row
            loaded_lat.append(float(latitude))
            loaded_lon.append(float(longitude))
            loaded_alt.append(float(altitude))

    gps_lat_noisy = np.array(loaded_lat)
    gps_lon_noisy = np.array(loaded_lon)
    gps_alt_noisy = np.array(loaded_alt)

    # Initialize Kalman Filter
    kalman_filter = GPSLLAKalmanFilter()

    kalman_filter.set_measurement_noise(r_lat_long=2000, r_alt=1)
    kalman_filter.set_process_noise(q_lat_long=0.00001, q_alt=0.00001, q_lat_long_vel=0.00001, q_alt_vel=0.00001)

    # Apply Kalman filter to GPS data
    filtered_lat: List[float] = []
    filtered_lon: List[float] = []
    filtered_alt: List[float] = []

    for i, (lat, lon, alt) in enumerate(zip(gps_lat_noisy, gps_lon_noisy, gps_alt_noisy)):
        pos = (lat, lon, alt)  # Measurement
        filtered_pos = kalman_filter.next_value(pos)
        filtered_lat.append(filtered_pos[0])  # Smoothed latitude
        filtered_lon.append(filtered_pos[1])  # Smoothed longitude
        filtered_alt.append(filtered_pos[2])  # Smoothed altitude

    # Plot the raw vs filtered data
    _, axs = plt.subplots(3, 1, figsize=(10, 5))

    # Latitude vs Longitude
    for i, (x_val, y_val, x_val2, y_val2) in enumerate(zip(gps_lat_noisy, gps_lon_noisy, filtered_lat, filtered_lon)):
        i_start = 4300
        if i >= i_start and i <= i_start + 300:
            num = i - i_start
            axs[0].annotate(num + 1, (x_val, y_val), textcoords="offset points", xytext=(0, 10), ha="center")
            axs[0].annotate(num + 1, (x_val2, y_val2), textcoords="offset points", xytext=(0, 10), ha="center")
            axs[1].annotate(num + 1, (i, x_val), textcoords="offset points", xytext=(0, 10), ha="center")
            axs[1].annotate(num + 1, (i, x_val2), textcoords="offset points", xytext=(0, 10), ha="center")
            axs[2].annotate(num + 1, (i, y_val), textcoords="offset points", xytext=(0, 10), ha="center")
            axs[2].annotate(num + 1, (i, y_val2), textcoords="offset points", xytext=(0, 10), ha="center")
    axs[0].plot(gps_lat_noisy, gps_lon_noisy, "o-", label="Noisy GPS Data (Lat/Lon)", alpha=0.6)
    axs[0].plot(filtered_lat, filtered_lon, "s-", label="Filtered Data (Kalman Filter)", alpha=0.8)
    axs[0].set_title("Latitude vs Longitude")
    axs[0].set_xlabel("Latitude")
    axs[0].set_ylabel("Longitude")
    axs[0].axis("equal")
    axs[0].ticklabel_format(useOffset=False)
    axs[0].legend()
    axs[0].grid()

    # Latitude over Time
    axs[1].plot(range(len(gps_lat_noisy)), gps_lat_noisy, "o-", label="Noisy Latitude Data", alpha=0.6)
    axs[1].plot(range(len(gps_lat_noisy)), filtered_lat, "s-", label="Filtered Latitude", alpha=0.8)
    axs[1].set_title("Latitude over Time")
    axs[1].set_xlabel("Time Step")
    axs[1].set_ylabel("Latitude")
    axs[1].ticklabel_format(useOffset=False)
    axs[1].legend()
    axs[1].grid()

    # Longitude over Time
    axs[2].plot(range(len(gps_lat_noisy)), gps_lon_noisy, "o-", label="Noisy Longitude Data", alpha=0.6)
    axs[2].plot(range(len(gps_lat_noisy)), filtered_lon, "s-", label="Filtered Longitude", alpha=0.8)
    axs[2].set_title("Longitude over Time")
    axs[2].set_xlabel("Time Step")
    axs[2].set_ylabel("Longitude")
    axs[2].ticklabel_format(useOffset=False)
    axs[2].legend()
    axs[2].grid()

    plt.tight_layout()
    plt.show()


if __name__ == "__main__":
    main()

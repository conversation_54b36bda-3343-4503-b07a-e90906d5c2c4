from dataclasses import dataclass
from typing import Optional, Protocol

from hardware_manager.python.boards.cruise_if import CruiseController


@dataclass
class SafetyState:
    is_safe: bool
    enforced: bool
    timestamp_ms: int


class TractorIF(CruiseController, Protocol):
    async def set_implement_state(self, active: bool, error: bool, error_msg: Optional[str]) -> None:
        pass

    async def set_safe_state_enforced(self, enforced: bool) -> None:
        pass

    async def get_safety_state(self, timestamp_ms: int) -> SafetyState:
        pass

    async def connected(self) -> bool:
        pass

import asyncio
import os
import subprocess
from typing import Awaitable, List, Tuple

from config.client.cpp.config_client_python import Config<PERSON><PERSON>, ConfigSubscriber, get_computer_config_prefix
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.error import MakaException
from lib.common.logging import get_logger
from lib.common.time.sleep import async_sleep_ms
from lib.common.time.time import maka_control_timestamp_ms

LOG = get_logger(__name__)

USB_MOUNT_POINT = "/media/usb-device"
CHECK_POLL_INTERVAL_MS = 250


class USBException(MakaException):
    pass


class USBChecker:
    def __init__(self, config_subscriber: ConfigSubscriber, config_client: ConfigClient) -> None:
        self._config_subscriber = config_subscriber
        self._config_client = config_client

    async def start(self) -> List[Awaitable[None]]:
        return [
            asyncio.create_task(self._check_usb_availability()),
        ]

    def _is_usb_mounted(self) -> bool:
        try:
            return os.path.ismount(USB_MOUNT_POINT)
        except Exception:
            LOG.exception("Error checking if usb exists")
            return False

    async def _check_usb_availability(self) -> None:
        stop_blocker_event = bot_stop_handler.create_stop_blocker()
        error_count = 0
        try:
            last_iteration = maka_control_timestamp_ms()
            while not bot_stop_handler.stopped:
                use_offline_flow = self._config_subscriber.get_config_node(
                    "data_upload_manager", "use_offline_upload_flow"
                ).get_bool_value()
                if maka_control_timestamp_ms() - last_iteration < CHECK_POLL_INTERVAL_MS:
                    await async_sleep_ms(50)
                    continue
                try:
                    if self._is_usb_mounted() and os.path.exists(USB_MOUNT_POINT):
                        if not use_offline_flow:
                            self._config_client.set_bool_value(
                                f"{get_computer_config_prefix()}/data_upload_manager/use_offline_upload_flow", True
                            )
                    else:
                        if use_offline_flow:
                            self._config_client.set_bool_value(
                                f"{get_computer_config_prefix()}/data_upload_manager/use_offline_upload_flow", False
                            )
                except Exception:
                    error_count += 1
                    if error_count % 20 == 0:
                        LOG.warning("Error checking usb status", exc_info=True)
                last_iteration = maka_control_timestamp_ms()
        finally:
            stop_blocker_event.set()

    async def check_usb_device_storage(self) -> Tuple[float, bool]:
        if self._config_subscriber.get_config_node("data_upload_manager", "use_offline_upload_flow").get_bool_value():
            try:
                df = subprocess.Popen(["df", USB_MOUNT_POINT], stdout=subprocess.PIPE)
                output = (df.communicate()[0]).decode("utf-8")
                _, _, used, avail, _, _ = output.split("\n")[1].split()
                return float(used) / float(int(used) + int(avail)), True
            except Exception:
                raise USBException("Exception getting usb device storage")
        return 0, False

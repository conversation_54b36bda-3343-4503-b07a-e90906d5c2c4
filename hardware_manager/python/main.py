import asyncio
import sys
from typing import Awaitable, List, Optional

from prometheus_client import start_http_server

from config.client.cpp.config_client_python import (
    ConfigClient,
    get_computer_config_prefix,
    get_global_config_subscriber,
)
from hardware_manager.python.boards.atim import BAORD_NAME as ATIM_BOARD_NAME
from hardware_manager.python.boards.atim import AtimClient
from hardware_manager.python.boards.cruise_if import CruiseController
from hardware_manager.python.boards.gps import GPSBoard
from hardware_manager.python.boards.jimbox import BOARD_NAME as JIMBOX_BOARD_NAME
from hardware_manager.python.boards.jimbox import JimboxBoard
from hardware_manager.python.boards.peplink_gps import PeplinkGPSReceiver
from hardware_manager.python.boards.rotary_ticks import RotaryEncoderBoard
from hardware_manager.python.boards.safety_plc import SafetyBoard
from hardware_manager.python.boards.strobe import StrobeControlBoard
from hardware_manager.python.boards.supervisory_plc import SupervisoryBoard
from hardware_manager.python.boards.timbox import TECU_BOARD_NAME, TIM_BOARD_NAME, TIMBoxClient
from hardware_manager.python.boards.tractor_if import TractorIF
from hardware_manager.python.boards.usb import USBChecker
from hardware_manager.python.grpc import GrpcServer
from hardware_manager.python.manager import HardwareManager
from hardware_manager.python.reaper import McbConnectorRegistry, ReaperModuleSensorPoller
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.devices.registry import DeviceRegistryException
from lib.common.generation import is_reaper, is_rtc
from lib.common.logging import get_logger, init_log
from lib.common.tasks.manager import get_event_loop_by_name

LOG = get_logger(__name__)


# flake8: noqa: C901
async def main() -> None:
    config_subscriber = get_global_config_subscriber()

    config_subscriber.add_config_tree(
        "hardware_manager", f"{get_computer_config_prefix()}/hardware_manager", "services/hardware_manager.yaml"
    )
    if is_rtc():
        config_subscriber.add_config_tree("common", "common", "services/rtc_common.yaml")
    else:
        config_subscriber.add_config_tree("common", "common", "services/common.yaml")
        config_subscriber.add_config_tree(
            "data_upload_manager",
            f"{get_computer_config_prefix()}/data_upload_manager",
            "services/data_upload_manager.yaml",
        )
    config_subscriber.start()
    await asyncio.get_event_loop().run_in_executor(None, lambda: config_subscriber.wait_until_ready())

    config_client = ConfigClient("localhost:61001")

    config_jimbox_installed = (
        config_subscriber.get_config_node("hardware_manager", "jimbox").get_node("installed").get_bool_value()
    )
    config_use_peplink_gps = config_subscriber.get_config_node("hardware_manager", "use_peplink_gps").get_bool_value()
    config_peplink_kalman_filter = config_subscriber.get_config_node("hardware_manager", "peplink_kalman_filter")

    extra_skip_list: List[str] = []
    if config_use_peplink_gps:
        extra_skip_list.append("gps_1")

    LOG.info("Done starting the config service")
    hardware_manager = HardwareManager(config_subscriber)
    await hardware_manager.boot(extra_skip_list=extra_skip_list)
    device_list_ids = [d.device_id for d in await hardware_manager.device_list()]

    LOG.info("Starting Metrics server")
    start_http_server(62007)

    LOG.info("Booting boards")
    tasks: List[Awaitable[None]] = []

    # set up MCBs first since this can take a while
    reaper_mcb_registry: Optional[McbConnectorRegistry] = None
    if is_reaper():
        LOG.info("Initializing MCB registry")
        reaper_mcb_registry = McbConnectorRegistry()
        tasks.extend(await reaper_mcb_registry.start())

    # then initialize remaining boards/PLCs
    if "nofx_board_1" in device_list_ids:
        rotary_board = RotaryEncoderBoard(hardware_manager.registry, asyncio.Lock())
        hardware_manager.append_hw_managed_boards(rotary_board)
    if "strobe_control_1" in device_list_ids:
        strobe_board = StrobeControlBoard(hardware_manager.registry)
        hardware_manager.append_hw_managed_boards(strobe_board)

    peplink_gps: Optional[PeplinkGPSReceiver] = None
    if config_use_peplink_gps:
        peplink_gps = PeplinkGPSReceiver(filter_config=config_peplink_kalman_filter)
        await peplink_gps.start_server()
    elif "gps_1" in device_list_ids:
        gps_board = GPSBoard(hardware_manager.registry)
        hardware_manager.append_hw_managed_boards(gps_board)
    if "safety_plc_1" in device_list_ids:
        safety_board = SafetyBoard(hardware_manager.registry)
        hardware_manager.append_hw_managed_boards(safety_board)
    if "supervisory_plc_1" in device_list_ids:
        supervisory_board = SupervisoryBoard(hardware_manager.registry, config_subscriber)
        hardware_manager.append_hw_managed_boards(supervisory_board)
    if "jimbox_1" in device_list_ids and config_jimbox_installed:
        jimbox_board = JimboxBoard(hardware_manager.registry, config_subscriber)
        hardware_manager.append_hw_managed_boards(jimbox_board)

    for board in hardware_manager.hw_managed_boards:
        try:
            LOG.info(f"Booting {board.board_name} board")
            await board.load_device()
        except DeviceRegistryException:
            LOG.warning("Booting failed", exc_info=True)

    for board in hardware_manager.hw_managed_boards:
        LOG.info(f"Starting tasks for {board.board_name} board")
        tasks.extend(await board.start())

    usb_device = USBChecker(config_subscriber, config_client)
    tasks.extend(await usb_device.start())

    tasks.append(asyncio.create_task(hardware_manager.loop_enabling()))

    cruise_controller: Optional[CruiseController] = None
    tractor: Optional[TractorIF] = None
    cc_if = config_subscriber.get_config_node("hardware_manager", "cruise_control_if").get_string_value().lower()
    if cc_if == JIMBOX_BOARD_NAME:
        cruise_controller = hardware_manager.get_board_by_name(JIMBOX_BOARD_NAME, JimboxBoard)
    elif cc_if == ATIM_BOARD_NAME:
        sb = hardware_manager.get_board_by_name("supervisory", SupervisoryBoard)
        cruise_controller = await AtimClient.build(config_subscriber.get_config_node("hardware_manager", "atim"), sb)
        tractor = cruise_controller
    elif cc_if == TIM_BOARD_NAME:
        tim_safety_board: Optional[SafetyBoard] = hardware_manager.get_board_by_name("safety", SafetyBoard)
        tbc = TIMBoxClient("tim", config_subscriber.get_config_node("hardware_manager", "tim"), tim_safety_board)
        await tbc.start()
        cruise_controller = tbc
    elif cc_if == TECU_BOARD_NAME:
        tecu_safety_board: Optional[SafetyBoard] = hardware_manager.get_board_by_name("safety", SafetyBoard)
        tbc = TIMBoxClient("tecu", config_subscriber.get_config_node("hardware_manager", "tim"), tecu_safety_board)
        await tbc.start()
        cruise_controller = tbc

    reaper_sensor_poller: Optional[ReaperModuleSensorPoller] = None
    if is_reaper():
        assert reaper_mcb_registry

        LOG.info("Starting module sensor polling task")
        reaper_sensor_poller = ReaperModuleSensorPoller(reaper_mcb_registry)
        tasks.extend(await reaper_sensor_poller.start())

    server = GrpcServer(
        61006,
        peplink_gps,
        usb_device,
        cruise_controller,
        hardware_manager,
        config_subscriber,
        reaper_mcb_registry,
        reaper_sensor_poller,
        tractor,
    )
    LOG.info("server created")
    await server.start()

    await asyncio.gather(*tasks)


if __name__ == "__main__":
    init_log(level="INFO")
    loop = get_event_loop_by_name()
    asyncio.run_coroutine_threadsafe(main(), loop)
    bot_stop_handler.ready_for_termination_event.wait()
    sys.exit(bot_stop_handler.error_code)

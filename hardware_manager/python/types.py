import math
from dataclasses import dataclass
from enum import IntEnum, auto
from typing import List, Optional, Tuple

from typing_extensions import Self

import generated.hardware_manager.proto.hardware_manager_service_pb2 as hardware_manager_pb
from lib.common.units.current import Current
from lib.common.units.duration import Duration
from lib.common.units.temperature import Temperature
from lib.common.units.voltage import Voltage
from lib.drivers.nanopb.pulczar_board.pulczar_board_connector import ReaperScannerHwStatus, ServoStatus
from lib.drivers.nanopb.reaper_module_controller.reaper_module_controller_types import (
    PowerState,
    RelayState,
    ScannerState,
    SensorData,
    StrobeStatus,
)


class LinkSpeed(IntEnum):
    UNKNOWN = auto()
    SPEED_10M_HALF = auto()
    SPEED_10M_FULL = auto()
    SPEED_100M_HALF = auto()
    SPEED_100M_FULL = auto()
    SPEED_1G_FULL = auto()
    SPEED_2G5_FULL = auto()
    SPEED_5G_FULL = auto()
    SPEED_10G_FULL = auto()


@dataclass
class EthernetLinkState:
    link_up: bool
    link_speed_actual: LinkSpeed
    link_speed_expected: LinkSpeed

    @classmethod
    def FromMessage(cls, info: hardware_manager_pb.NetworkPortState) -> Self:
        LINK_SPEED_MAP = {
            hardware_manager_pb.NetworkLinkSpeed.UNKNOWN: LinkSpeed.UNKNOWN,
            hardware_manager_pb.NetworkLinkSpeed.SPEED_10M_HALF: LinkSpeed.SPEED_10M_HALF,
            hardware_manager_pb.NetworkLinkSpeed.SPEED_10M_FULL: LinkSpeed.SPEED_10M_FULL,
            hardware_manager_pb.NetworkLinkSpeed.SPEED_100M_HALF: LinkSpeed.SPEED_100M_HALF,
            hardware_manager_pb.NetworkLinkSpeed.SPEED_100M_FULL: LinkSpeed.SPEED_100M_FULL,
            hardware_manager_pb.NetworkLinkSpeed.SPEED_1G_FULL: LinkSpeed.SPEED_1G_FULL,
            hardware_manager_pb.NetworkLinkSpeed.SPEED_2G5_FULL: LinkSpeed.SPEED_2G5_FULL,
            hardware_manager_pb.NetworkLinkSpeed.SPEED_5G_FULL: LinkSpeed.SPEED_5G_FULL,
            hardware_manager_pb.NetworkLinkSpeed.SPEED_10G_FULL: LinkSpeed.SPEED_10G_FULL,
        }

        return cls(
            link_up=info.link_up,
            link_speed_actual=LINK_SPEED_MAP.get(info.actual_link_speed, LinkSpeed.UNKNOWN),
            link_speed_expected=LINK_SPEED_MAP.get(info.expected_link_speed, LinkSpeed.UNKNOWN),
        )

    def to_message(self) -> hardware_manager_pb.NetworkPortState:
        LINK_SPEED_MAP = {
            LinkSpeed.UNKNOWN: hardware_manager_pb.NetworkLinkSpeed.UNKNOWN,
            LinkSpeed.SPEED_10M_HALF: hardware_manager_pb.NetworkLinkSpeed.SPEED_10M_HALF,
            LinkSpeed.SPEED_10M_FULL: hardware_manager_pb.NetworkLinkSpeed.SPEED_10M_FULL,
            LinkSpeed.SPEED_100M_HALF: hardware_manager_pb.NetworkLinkSpeed.SPEED_100M_HALF,
            LinkSpeed.SPEED_100M_FULL: hardware_manager_pb.NetworkLinkSpeed.SPEED_100M_FULL,
            LinkSpeed.SPEED_1G_FULL: hardware_manager_pb.NetworkLinkSpeed.SPEED_1G_FULL,
            LinkSpeed.SPEED_2G5_FULL: hardware_manager_pb.NetworkLinkSpeed.SPEED_2G5_FULL,
            LinkSpeed.SPEED_5G_FULL: hardware_manager_pb.NetworkLinkSpeed.SPEED_5G_FULL,
            LinkSpeed.SPEED_10G_FULL: hardware_manager_pb.NetworkLinkSpeed.SPEED_10G_FULL,
        }

        return hardware_manager_pb.NetworkPortState(
            link_up=self.link_up,
            actual_link_speed=LINK_SPEED_MAP[self.link_speed_actual],
            expected_link_speed=LINK_SPEED_MAP[self.link_speed_expected],
        )


@dataclass
class ModulePcSensorData:
    """
    Sensor information provided by the module PC via heartbeats
    """

    cpu_core_temp: Temperature
    system_temp: Temperature
    gpu_1_temp: Optional[Temperature]
    gpu_2_temp: Optional[Temperature]

    psu_12v: Voltage
    psu_5v: Voltage
    psu_3v3: Voltage

    uptime: Duration
    cpu_load: float
    ram_usage: float
    disk_usage: float

    scanner_link: Tuple[EthernetLinkState, EthernetLinkState]
    target_link: Tuple[EthernetLinkState, EthernetLinkState]
    predict_link: EthernetLinkState
    ipmi_link: EthernetLinkState
    ext_link: EthernetLinkState
    global_link: EthernetLinkState

    @classmethod
    def FromMessage(cls, packet: hardware_manager_pb.ReaperPcSensorData) -> Self:
        return cls(
            cpu_core_temp=Temperature.from_c(packet.temperature_cpu_core_c),
            system_temp=Temperature.from_c(packet.temperature_system_c),
            gpu_1_temp=Temperature.from_c(packet.temperature_gpu_1_c) if packet.temperature_gpu_1_c else None,
            gpu_2_temp=Temperature.from_c(packet.temperature_gpu_2_c) if packet.temperature_gpu_2_c else None,
            psu_12v=Voltage.from_volts(packet.psu_12v),
            psu_5v=Voltage.from_volts(packet.psu_5v),
            psu_3v3=Voltage.from_volts(packet.psu_3v3),
            uptime=Duration.from_seconds(packet.uptime),
            cpu_load=packet.load,
            ram_usage=packet.ram_usage_percent,
            disk_usage=packet.disk_usage_percent,
            scanner_link=(
                EthernetLinkState.FromMessage(packet.scanner_link[0]),
                EthernetLinkState.FromMessage(packet.scanner_link[1]),
            ),
            target_link=(
                EthernetLinkState.FromMessage(packet.target_cam_link[0]),
                EthernetLinkState.FromMessage(packet.target_cam_link[1]),
            ),
            predict_link=EthernetLinkState.FromMessage(packet.predict_cam_link),
            ipmi_link=EthernetLinkState.FromMessage(packet.ipmi_link),
            global_link=EthernetLinkState.FromMessage(packet.global_link),
            ext_link=EthernetLinkState.FromMessage(packet.ext_link),
        )

    def to_message(self) -> hardware_manager_pb.ReaperPcSensorData:
        """
        Convert to the proto message type used for RPC
        """
        return hardware_manager_pb.ReaperPcSensorData(
            temperature_cpu_core_c=self.cpu_core_temp.deg_c,
            temperature_system_c=self.system_temp.deg_c,
            temperature_gpu_1_c=self.gpu_1_temp.deg_c if self.gpu_1_temp else None,
            temperature_gpu_2_c=self.gpu_2_temp.deg_c if self.gpu_2_temp else None,
            psu_12v=self.psu_12v.volts,
            psu_5v=self.psu_5v.volts,
            psu_3v3=self.psu_3v3.volts,
            load=self.cpu_load,
            uptime=int(self.uptime.seconds),
            ram_usage_percent=self.ram_usage,
            disk_usage_percent=self.disk_usage,
            scanner_link=[link.to_message() for link in self.scanner_link],
            target_cam_link=[link.to_message() for link in self.target_link],
            predict_cam_link=self.predict_link.to_message(),
            ipmi_link=self.ipmi_link.to_message(),
            global_link=self.global_link.to_message(),
            ext_link=self.ext_link.to_message(),
        )


@dataclass
class ModuleLaserData:
    """
    Information reporetd by a scanner from the attached laser
    """

    temp: Temperature
    humidity: float
    laser_current: Current

    model: str
    serial: str
    rated_power: int

    faults: List[int]

    @classmethod
    def FromMessage(cls, info: ReaperScannerHwStatus) -> Self:
        assert info.bwt_connected
        assert info.bwt_inventory and info.bwt_status

        if not info.bwt_status.temperature or not info.bwt_status.humidity or not info.bwt_status.current:
            raise ValueError("Missing required BWT status data fields in ReaperScannerHwStatus: {info}")

        return cls(
            temp=Temperature.from_c(info.bwt_status.temperature[0]),
            humidity=info.bwt_status.humidity[0],
            laser_current=Current.from_milliamps(info.bwt_status.current[0]),
            model=info.bwt_inventory.model,
            serial=info.bwt_inventory.serial,
            rated_power=info.bwt_inventory.ratedPower,
            faults=info.bwt_status.faults or [],
        )

    def to_message(self) -> hardware_manager_pb.ReaperScannerLaserStatus:
        # TODO: better/actual conversion of faults
        faults = [str(fault) for fault in self.faults]

        return hardware_manager_pb.ReaperScannerLaserStatus(
            model=self.model,
            sn=self.serial,
            rated_power=self.rated_power,
            temperature_c=self.temp.deg_c,
            humidity=self.humidity,
            laser_current_ma=self.laser_current.milliamps,
            faults=faults,
        )


@dataclass
class ModuleScannerMotorData:
    """
    Sensor data for a single motor controller/servo
    """

    controller_sn: int

    output_temp: Temperature

    motor_vin: Voltage
    motor_current: Current

    encoder_pos: int

    @classmethod
    def FromMessage(cls, info: ServoStatus) -> Self:
        return cls(
            controller_sn=info.controller_sn,
            output_temp=info.output_stage_temp,
            motor_vin=info.motor_supply,
            motor_current=info.motor_current,
            encoder_pos=info.encoder_ticks,
        )

    def to_message(self) -> hardware_manager_pb.ReaperScannerMotorData:
        return hardware_manager_pb.ReaperScannerMotorData(
            controller_sn=str(self.controller_sn),
            temperature_output_c=self.output_temp.deg_c,
            motor_supply_v=self.motor_vin.volts,
            motor_current_a=self.motor_current.amps,
            encoder_position=self.encoder_pos,
        )


@dataclass
class ModuleScannerSensorData:
    """
    Sensor data as polled from the scanner
    """

    target_cam_power: bool

    collimator_temp: Temperature
    fiber_temp: Temperature

    laser_power_w: float
    laser_power_raw_mv: float

    laser_connected: bool
    laser_status: Optional[ModuleLaserData] = None

    servo_pan: Optional[ModuleScannerMotorData] = None
    servo_tilt: Optional[ModuleScannerMotorData] = None

    # TODO: target cam data


@dataclass
class ModuleScannerData:
    """
    Accumulated information for a single scanner

    This consists of both information from the MCB, as well as data polled directly from the scanner
    itself.
    """

    mcb_status: ScannerState

    # whether this scanner is connected/reachable from command
    connected: bool = False
    # additional sensor data reported by the scanner
    sensors: Optional[ModuleScannerSensorData] = None

    def to_message(self) -> hardware_manager_pb.ReaperScannerSensorData:
        if self.connected:
            assert self.sensors

            return hardware_manager_pb.ReaperScannerSensorData(
                power_on=self.mcb_status.power,
                current_a=self.mcb_status.current.amps,
                fuse_tripped=self.mcb_status.fuseBlown,
                temperature_collimator_c=self.sensors.collimator_temp.deg_c,
                temperature_fiber_c=self.sensors.fiber_temp.deg_c,
                laser_power_w=self.sensors.laser_power_w,
                laser_power_raw_mv=self.sensors.laser_power_raw_mv,
                laser_connected=self.sensors.laser_connected,
                laser_status=self.sensors.laser_status.to_message() if self.sensors.laser_status else None,
                # TODO: target cam data
                motor_pan=self.sensors.servo_pan.to_message() if self.sensors.servo_pan else None,
                motor_tilt=self.sensors.servo_tilt.to_message() if self.sensors.servo_tilt else None,
                target_cam_power_enabled=(self.sensors.target_cam_power if self.sensors else False),
            )

        else:
            return hardware_manager_pb.ReaperScannerSensorData(
                power_on=self.mcb_status.power,
                current_a=self.mcb_status.current.amps,
                fuse_tripped=self.mcb_status.fuseBlown,
                temperature_collimator_c=math.nan,
                temperature_fiber_c=math.nan,
                laser_power_w=math.nan,
                laser_power_raw_mv=math.nan,
                laser_connected=False,
                target_connected=False,
            )


@dataclass
class ModuleSensorData:
    module_id: int
    sensors: SensorData
    strobe: StrobeStatus
    relays: RelayState
    power: PowerState
    scanner: List[ModuleScannerData]

    pc_sensors: Optional[ModulePcSensorData] = None
    module_sn: Optional[str] = None

    # TODO: predict cam sensors

    def copy_mcb_data(self, data: Self) -> None:
        """
        Update this instance with MCB sensor data from the provided instance
        """
        assert self.module_id == data.module_id

        self.sensors = data.sensors
        self.strobe = data.strobe
        self.relays = data.relays
        self.power = data.power

        def _update_scanner(inData: ModuleScannerData, newData: ModuleScannerData) -> ModuleScannerData:
            data = inData
            data.mcb_status = newData.mcb_status
            return data

        self.scanner = [_update_scanner(old, new) for old, new in zip(self.scanner, data.scanner)]

    def to_message(self) -> hardware_manager_pb.ReaperModuleSensorData:
        def _convert_mcb_enviro(enviro: SensorData.EnviroData) -> hardware_manager_pb.EnvironmentalSensorData:
            return hardware_manager_pb.EnvironmentalSensorData(
                temperature_c=enviro.temp.deg_c, humidity_rh=enviro.humidity, pressure_hpa=enviro.pressure.hpa
            )

        def _convert_mcb_coolant(coolant: SensorData.PressureData) -> hardware_manager_pb.CoolantSensorData:
            return hardware_manager_pb.CoolantSensorData(
                temperature_c=coolant.temperature.deg_c, pressure_kpa=coolant.pressure.kpa
            )

        return hardware_manager_pb.ReaperModuleSensorData(
            module_id=self.module_id,
            module_sn=self.module_sn,
            enviro_enclosure=_convert_mcb_enviro(self.sensors.environmental[0]),
            enviro_pc=_convert_mcb_enviro(self.sensors.environmental[1])
            if len(self.sensors.environmental) == 2
            else hardware_manager_pb.EnvironmentalSensorData(),
            coolant_inlet=_convert_mcb_coolant(self.sensors.pressure[0]),
            coolant_outlet=_convert_mcb_coolant(self.sensors.pressure[1]),
            strobe_temperature_c=self.strobe.temperature.deg_c,
            strobe_cap_voltage=self.strobe.voltage.volts,
            strobe_current=self.strobe.current.amps,
            pc=self.pc_sensors.to_message() if self.pc_sensors else None,
            scanner_a=self.scanner[0].to_message(),
            scanner_b=self.scanner[1].to_message(),
            pc_power_enabled=(self.relays.pc is True),
            lasers_power_enabled=(self.relays.laser is True),
            predict_cam_power_enabled=(self.power.predictCam is True),
            strobe_power_enabled=(self.relays.btl is True) and (self.power.strobeBoard is True),
            strobe_enabled=self.strobe.enabled,
        )

    def __repr__(self) -> str:
        return f"(module id = {self.module_id}: sensors = {self.sensors}, strobe status = {self.strobe}, scanner status = {self.scanner}, PC sensors = {self.pc_sensors})"

import asyncio
import re
from ipaddress import IPv4Address
from typing import Any, Awaitable, Dict, List, Optional, cast

from hardware_manager.python.types import (
    ModuleLaserData,
    ModuleScannerData,
    ModuleScannerMotorData,
    ModuleScannerSensorData,
    ModuleSensorData,
)
from lib.common.asyncio.interruptible_sleep import InterruptibleSleep
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.devices.boards.reaper_mcb.reaper_mcb_device import ReaperMcbDevice
from lib.common.devices.device import DeviceStatusCode
from lib.common.logging import get_logger, init_log
from lib.common.module_server.client import ModuleServerClient
from lib.common.robot_definition.pybind.robot_definition_python import RobotDefinition
from lib.common.units.duration import Duration
from lib.drivers.errors.error import RetryableMakaDeviceException
from lib.drivers.nanopb.pulczar_board.pulczar_board_connector import PulczarBoardConnector
from lib.drivers.nanopb.reaper_module_controller.reaper_module_controller_connector import (
    ReaperModuleConnector,
    get_board,
)
from lib.drivers.psoc_ethernet.psoc_m_ethernet import PsocMEthernetConnector

LOG = get_logger(__name__)


class ModulePollingException(RuntimeError):
    """
    Indicates something went wrong polling a module
    """


# XXX: should it be renamed as ModuleConnectorRegistry as it holds scanner/module_server connectors too
class McbConnectorRegistry:
    """
    Holds connectors for all MCBs

    Automagically handles robot definition changes and identifying the modules. The module
    connectors can then be retrieved by module id.
    """

    def __init__(self) -> None:
        # Lock for both of these silly little lists
        self._connectors_lock: asyncio.Lock = asyncio.Lock()
        # Connectors for modules with an id
        self._devices: Dict[int, ReaperMcbDevice] = dict()
        # Scanner board connectors for all modules with an id
        self._scanner_connectors: Dict[int, List[PulczarBoardConnector]] = dict()
        # Module server RPC clients (used to poll sensors)
        self._module_server_clients: Dict[int, ModuleServerClient] = dict()

        # this event signals that we have finished initial board boot
        self._boot_complete_event: asyncio.Event = asyncio.Event()

        # Lock to allow only one identify operation at a time
        self._identify_lock: asyncio.Lock = asyncio.Lock()
        self._identify_in_progress: bool = False

    @property
    def boot_complete_event(self) -> asyncio.Event:
        return self._boot_complete_event

    async def start(self) -> List[Awaitable[None]]:
        """
        Set up the registry by creating connectors for all MCBs from their known IPs.
        """
        await self._update_connectors()

        tasks: List[Awaitable[None]] = [asyncio.create_task(self._boot_loop(), name="MCB device booter")]

        return tasks

    async def _update_connectors(self) -> None:
        """
        Recreate all connectors based on the IPs retrieved from the IP service
        """
        pc_ip_map = RobotDefinition.get().get_all_computer_addresses()
        mcb_ip_map = RobotDefinition.get().get_all_mcb_addresses()
        LOG.debug(f"Got MCB IPs: {mcb_ip_map}")

        async with self._connectors_lock:
            self._devices.clear()
            self._scanner_connectors.clear()
            self._module_server_clients.clear()

            for module_name, mcb_address in mcb_ip_map.items():
                match = re.match(r"^module(\d+)", module_name)
                assert match
                module_id_str = str(match.group(1))
                assert module_id_str.isnumeric()
                module_id = int(module_id_str)
                pc_ip = pc_ip_map[module_name]

                # creat board and connectors; we'll boot them all in parallel later
                device = ReaperMcbDevice(
                    address=IPv4Address(mcb_address), module_id=module_id, pc_ip=IPv4Address(pc_ip)
                )
                LOG.debug(f"Created MCB connector for `{module_name}` at {mcb_address}: {device}")

                self._devices[module_id] = device
                await self._create_aux_connectors(module_id, pc_ip)

    async def _create_aux_connectors(self, module_id: int, pc_ip: str) -> None:
        """
        Create connectors for auxiliary devices we poll for status on a module; this is the two
        scanners and the module_server client.
        """
        PULCZAR_PORTS = [
            # maps to scanner A (*********) on module
            42431,
            # maps to scanner B (*********) on module
            42432,
        ]

        pulczar_connectors: List[PulczarBoardConnector] = []

        for port in PULCZAR_PORTS:
            connector = PsocMEthernetConnector(pc_ip, port, asyncio.get_event_loop())
            await connector.open()

            board = PulczarBoardConnector(connector)
            pulczar_connectors.append(board)

        LOG.debug(f"Created scanner connectors for module {module_id}: {pulczar_connectors}")
        self._scanner_connectors[module_id] = pulczar_connectors

        # create module server client
        client = ModuleServerClient(pc_ip)
        LOG.debug(f"Created module_server RPC client for module {module_id} (at {pc_ip}): {client}")

        self._module_server_clients[module_id] = client

    async def _boot_loop(self) -> None:
        await self._boot_initial()
        self._boot_complete_event.set()

        while not bot_stop_handler.stopped:
            await asyncio.sleep(30)

            devices = (await self.all_with_id()).values()
            for board in devices:
                try:
                    status = await board.get_status()

                    if status.code not in [DeviceStatusCode.OK, DeviceStatusCode.WARNING, DeviceStatusCode.BOOTING]:
                        LOG.info(f"Attempting to recover MCB {board.device_id} (status is {status.code})")
                        await board.disable()
                        await board.enable()
                except asyncio.CancelledError:
                    raise
                except BaseException:
                    LOG.warning(f"Failed to recover MCB ({board.device_id})", exc_info=True)

    async def _boot_initial(self) -> None:
        """
        Asynchronously (in parallel) boot all MCBs. This should be done first by the boot task
        to handle firmware updates.
        """

        async def _booter(module_id: int, device: ReaperMcbDevice) -> bool:
            try:
                LOG.info(f"Enabling MCB device for module {module_id} ({device.device_id})")
                await device.enable()
                LOG.info(f"Successfully enabled MCB device module {module_id} ({device.device_id})")

                return True
            except BaseException:
                LOG.warning(f"Failed to boot MCB for module {module_id}, will retry later", exc_info=True)
                return False

        async with self._connectors_lock:
            devices = sorted(self._devices.items())

        LOG.info(f"Booting MCB devices (total {len(devices)})")
        result = await asyncio.gather(*[_booter(module_id, device) for module_id, device in devices])
        success_map = dict(zip([id for id, _ in devices], result))
        LOG.info(f"Finished booting MCB devices: {success_map}")

    async def get(self, module_id: int) -> Optional[ReaperMcbDevice]:
        """
        Get MCB connector for a module with the given ID, if it is known to the system
        """
        async with self._connectors_lock:
            return self._devices.get(module_id)

    async def get_scanners(self, module_id: int) -> Optional[List[PulczarBoardConnector]]:
        """
        Get all scanner board connectors for the given module. These connectors use the passthrough
        ports on the module PC to communicate with the scanners directly via command.

        Retruned list is in the A, B order.
        """
        async with self._connectors_lock:
            return self._scanner_connectors.get(module_id)

    async def get_module_server_client(self, module_id: int) -> Optional[ModuleServerClient]:
        """
        Get the module server client for the specified module.
        """
        async with self._connectors_lock:
            return self._module_server_clients.get(module_id)

    async def all_with_id(self) -> Dict[int, ReaperMcbDevice]:
        """
        Get MCB connectors for all modules which have an assigned ID
        """
        async with self._connectors_lock:
            return self._devices

    async def identify_module_with_strobe(self, to_id: str, to_turn_off: List[str]) -> None:
        """
        Flash the lights for a given DHCP MCB IP and a list of DHCP IPs to turn off.

        Raises an exception an identify operation is already in progress.
        """

        await self._identify_lock.acquire()
        if self._identify_in_progress:
            self._identify_lock.release()
            raise RuntimeError("An identify operation is already in progress")

        to_id_connector: ReaperModuleConnector = await get_board(to_id)
        to_turn_off_connectors: List[ReaperModuleConnector] = [await get_board(x) for x in to_turn_off]

        # ensure to_id has its strobe on
        try:
            await to_id_connector.set_strobe_enabled(True)
        except BaseException:
            LOG.exception(f"Failed to turn on strobe for module {to_id}")
            self._identify_lock.release()
            raise

        # turn off the strobes for the other modules
        identify_time_s = 5
        off_duration = Duration.from_seconds(identify_time_s)
        try:
            await asyncio.gather(*[connector.strobe_disable_for(off_duration) for connector in to_turn_off_connectors])
        except BaseException:
            LOG.exception(f"Failed to turn off strobes for modules {to_turn_off}")
            self._identify_lock.release()
            raise

        self._identify_in_progress = True
        self._identify_lock.release()

        # create a task to set in progress to false after the duration
        async def set_done() -> None:
            await asyncio.sleep(identify_time_s)
            async with self._identify_lock:
                self._identify_in_progress = False

        # run the task to set in progress to false after the duration
        asyncio.create_task(set_done())


class ReaperModuleSensorPoller:
    """
    Periodically retrieves sensor data from modules.

    This combines information from the MCB, as well as data from sensors of the PC.
    """

    def __init__(self, registry: McbConnectorRegistry) -> None:
        self._mcb_registry = registry
        self._sensor_poll_task: Optional[asyncio.Task[Any]] = None

        self._sensors: Dict[int, ModuleSensorData] = dict()
        self._sensors_lock: asyncio.Lock = asyncio.Lock()

        # this event signals that sensors have been polled
        self._sensor_poll_event: asyncio.Event = asyncio.Event()

        # interruptible sleep handler for polling
        self._sleeper: InterruptibleSleep = InterruptibleSleep()

    @property
    def sensor_poll_event(self) -> asyncio.Event:
        """
        Event signalled once we've polled data from all modules
        """
        return self._sensor_poll_event

    async def start(self) -> List[Awaitable[None]]:
        """
        Start polling for module sensor data. Returns all created tasks so that they can be awaited
        later.
        """
        tasks: List[Awaitable[None]] = [asyncio.create_task(self._mcb_poll_loop(), name="Module sensor poll worker")]
        return tasks

    async def get_module_sensors(self, moduleId: int) -> Optional[ModuleSensorData]:
        """
        Return the most recent sensor data read from the given module, if available.
        """
        async with self._sensors_lock:
            return self._sensors.get(moduleId)

    async def get_all_module_sensors(self) -> Dict[int, ModuleSensorData]:
        """
        Get sensor data for all modules
        """
        async with self._sensors_lock:
            return self._sensors

    async def update_module(self, module_id: int, fast: bool = False) -> None:
        """
        Fetch sensor data for a single module outside of the polling loop, in response to an external
        change in state.
        """
        device = await self._mcb_registry.get(module_id)
        assert device

        # fetch data
        data = await self._get_module_sensors(module_id, device, fast)
        LOG.debug(f"Got sensor data for module {module_id}: {data}")

        # if this was a fast update, merge new module sensor data with old PC/scanner data
        if fast:
            async with self._sensors_lock:
                old_data = self._sensors.get(module_id)

                # copy over the MCB-specific sensors that have just been updated
                if old_data:
                    old_data.copy_mcb_data(data)
                    self._sensors[module_id] = old_data
                # no sensor data at all so store it as is
                else:
                    self._sensors[module_id] = data

        # otherwise, this was a full sensor data set so overwrite it
        else:
            async with self._sensors_lock:
                self._sensors[module_id] = data

    async def update_all(self) -> None:
        """
        Trigger the main poll loop to expire immediately, which will cause all MCB, PC and scanner
        state to be updated asynchronously in the background.
        """
        _ = await self._sleeper.cancel_all()

    async def _mcb_poll_loop(self) -> None:
        """
        Poll each of the MCBs for sensor data.
        """
        await self._mcb_registry.boot_complete_event.wait()
        LOG.info("Started module sensor poll loop")

        while not bot_stop_handler.stopped:
            # TODO: should this be a configurable interval?
            await self._sleeper.sleep(10)

            try:
                devices = await self._mcb_registry.all_with_id()
                LOG.debug(f"Got MCB devices: {devices}")

                # poll sensor data for each
                results = await asyncio.gather(
                    *[self._get_module_sensors(module_id, device) for module_id, device in devices.items()],
                    return_exceptions=True,
                )
                LOG.debug(f"Raw sensor data: {results}")

                async with self._sensors_lock:
                    for result in results:
                        if isinstance(result, Exception):
                            # TODO: get module id
                            LOG.error(f"Failed to read sensor data from module: {result}")
                        else:
                            sensors: ModuleSensorData = cast(ModuleSensorData, result)
                            self._sensors[sensors.module_id] = sensors
            except asyncio.CancelledError:
                raise
            except BaseException:
                LOG.exception("Error getting MCB sensor data")
            else:
                self._sensor_poll_event.set()

    async def _get_module_sensors(
        self, module_id: int, mcb: ReaperMcbDevice, mcb_only: bool = False
    ) -> ModuleSensorData:
        """
        Retrieve all sensor data for the given module id. Queries sensors on the MCB, scanners, and
        fetches the most recent heartbeat sensor packet.
        """
        # MCB should have been booted
        device_status = await mcb.get_status()
        if device_status.code not in [DeviceStatusCode.OK, DeviceStatusCode.WARNING]:
            raise ModulePollingException(f"Invalid device state for {module_id}: {device_status.code}")

        # query MCB first; any errors abort this entire process (caller gets exception)
        mcb_status = await mcb.get_board_status()
        data = ModuleSensorData(
            module_id=module_id,
            sensors=mcb_status.sensors,
            strobe=mcb_status.strobe,
            relays=mcb_status.relays,
            power=mcb_status.power,
            scanner=[ModuleScannerData(x) for x in mcb_status.scanners],
        )

        # get additional data; failures here are ignored, and data we have is returned as-is
        if not mcb_only:
            try:
                # get data from module_server (PC sensors)
                server_connector = await self._mcb_registry.get_module_server_client(module_id)
                if server_connector:
                    try:
                        # retrieve PC sensor data
                        pc_sensors = await server_connector.GetModuleSensors()
                        data.pc_sensors = pc_sensors
                    except BaseException:
                        LOG.warning(f"Failed to get module sensors from module {module_id}", exc_info=True)

                # get all scanners on this module, and poll them for status
                connectors = await self._mcb_registry.get_scanners(module_id)
                if connectors:
                    for connector, info in zip(connectors, data.scanner):
                        try:
                            # request succeeded so scanner is connected
                            status = await connector.get_hwstatus_reaper()
                            info.connected = True

                            # then convert the sensors
                            info.sensors = ModuleScannerSensorData(
                                target_cam_power=status.target_cam_power,
                                collimator_temp=status.laser_temp_collimator,
                                fiber_temp=status.laser_temp_fiber,
                                laser_power_w=status.laser_power_w,
                                laser_power_raw_mv=status.laser_power_raw.millivolts,
                                laser_connected=status.bwt_connected,
                                laser_status=ModuleLaserData.FromMessage(status) if status.bwt_connected else None,
                                servo_pan=ModuleScannerMotorData.FromMessage(status.servo_pan)
                                if status.servo_pan and status.servo_pan.connected
                                else None,
                                servo_tilt=ModuleScannerMotorData.FromMessage(status.servo_tilt)
                                if status.servo_tilt and status.servo_tilt.connected
                                else None,
                            )

                        # handle communication failures with scanner
                        except RetryableMakaDeviceException:
                            info.connected = False
                            LOG.exception(
                                f"Failed to communicate with scanner {connector.identifier} on module {module_id}"
                            )

            # soak up any other exceptions (from getting PC sensors or other scanner issues)
            except BaseException:
                LOG.exception(f"Failed to get additional sensor data for module {module_id}")

        return data


# When run as standalone, sit and print the captured data
async def main() -> None:
    registry = McbConnectorRegistry()
    await registry.start()

    poller = ReaperModuleSensorPoller(registry)

    LOG.info("Starting worker…")
    await poller.start()

    while True:
        LOG.info(f"Sensor data: {poller.get_all_module_sensors()}")
        await asyncio.sleep(10)


if __name__ == "__main__":
    init_log(level="DEBUG")

    loop = asyncio.get_event_loop()
    loop.run_until_complete(main())

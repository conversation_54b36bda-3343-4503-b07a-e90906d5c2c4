import asyncio
from typing import TYPE_CHECKING, Any, Dict, List, Optional, cast

import grpc

import generated.hardware_manager.proto.hardware_manager_service_pb2 as hardware_manager_pb
import generated.hardware_manager.proto.hardware_manager_service_pb2_grpc as hardware_manager_grpc
import lib.common.devices.boards.supervisory_plc.supervisory_messages as supervisory_messages
import lib.common.logging
from config.client.cpp.config_client_python import ConfigSubscriber
from hardware_manager.python.boards.cruise_if import CruiseController
from hardware_manager.python.boards.gps import GPSBoard, GPSException
from hardware_manager.python.boards.peplink_gps import FixQuality, PeplinkGPSReceiver
from hardware_manager.python.boards.rotary_ticks import RotaryEncoderBoard, RotaryTicksException
from hardware_manager.python.boards.safety_plc import SafetyBoard, SafetyBoardException
from hardware_manager.python.boards.strobe import StrobeControlBoard
from hardware_manager.python.boards.supervisory_plc import SupervisoryBoard
from hardware_manager.python.boards.tractor_if import TractorIF
from hardware_manager.python.boards.usb import USBChecker
from hardware_manager.python.manager import HardwareManager
from hardware_manager.python.reaper import McbConnectorRegistry, ReaperModuleSensorPoller
from hardware_manager.python.types import ModuleSensorData
from lib.common.devices.boards.reaper_mcb.reaper_mcb_device import ReaperMcbDevice
from lib.common.devices.device import DeviceStatusCode
from lib.common.generation import is_bud, is_reaper
from lib.common.gps.robot_pose import ROBOT_POSE
from lib.common.time.sleep import async_sleep_ms
from lib.common.units.duration import Duration
from lib.common.units.speed import Speed
from lib.drivers.nanopb.pulczar_board.pulczar_board_connector import PulczarBoardConnector
from lib.drivers.nanopb.reaper_module_controller.reaper_module_controller_types import (
    PowerState,
    RelayState,
    StrobeConfig,
)

LOG = lib.common.logging.get_logger(__name__)


if TYPE_CHECKING:
    FixTypeType = hardware_manager_pb.FixTypeValue
    CarrierPhaseSolnType = hardware_manager_pb.CarrierPhaseSolnValue
else:
    FixTypeType = int
    CarrierPhaseSolnType = int


class GrpcServicer(hardware_manager_grpc.HardwareManagerServiceServicer):
    def __init__(
        self,
        peplink_gps: Optional[PeplinkGPSReceiver],
        usb_checker: USBChecker,
        cruise_controller: Optional[CruiseController],
        hardware_manager: HardwareManager,
        config_subscriber: ConfigSubscriber,
        reaper_mcb_registry: Optional[McbConnectorRegistry] = None,
        reaper_sensor_poller: Optional[ReaperModuleSensorPoller] = None,
        tractor: Optional[TractorIF] = None,
    ):
        self._peplink_gps = peplink_gps
        self._usb_checker = usb_checker
        self._cruise_controller = cruise_controller
        self._hardware_manager = hardware_manager
        self._config_subscriber = config_subscriber
        self._reaper_mcb_registry = reaper_mcb_registry
        self._reaper_sensor_poller = reaper_sensor_poller
        self._tractor = tractor

        if is_reaper():
            assert self._reaper_mcb_registry and self._reaper_sensor_poller

        self._rotary_board = self._hardware_manager.get_board_by_name("encoder", RotaryEncoderBoard)
        self._safety_board = self._hardware_manager.get_board_by_name("safety", SafetyBoard)
        self._gps_board = self._hardware_manager.get_board_by_name("GPS", GPSBoard)
        self._supervisory_board = self._hardware_manager.get_board_by_name("supervisory", SupervisoryBoard)
        self._strobe_board = self._hardware_manager.get_board_by_name("strobe", StrobeControlBoard)

    async def Ping(
        self, request: hardware_manager_pb.PingRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.PingResponse:
        LOG.info(f"Got A Ping: {request.x}")
        context.set_code(grpc.StatusCode.OK)
        return hardware_manager_pb.PingResponse(x=request.x)

    async def GetDeltaTravelMM(
        self, request: hardware_manager_pb.GetDeltaTravelMMRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.GetDeltaTravelMMResponse:
        try:
            assert self._rotary_board is not None and await self._rotary_board.booted()
            delta_mm = await self._rotary_board.get_delta_pos_mm(request.id)
            context.set_code(grpc.StatusCode.OK)

            return hardware_manager_pb.GetDeltaTravelMMResponse(delta_mm=delta_mm)
        except (RotaryTicksException, AssertionError):
            LOG.exception("Exception getting rotary ticks")
            context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
            context.set_details("Exception getting rotary ticks")
            return hardware_manager_pb.GetDeltaTravelMMResponse(delta_mm=0.0)

    async def GetNextDistance(
        self, request: hardware_manager_pb.GetNextDistanceRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.GetNextDistanceResponse:
        try:
            assert self._rotary_board is not None and await self._rotary_board.booted()
            while (await self._rotary_board.get_last_snapshot()).time_ms <= request.timestamp_ms:
                await async_sleep_ms(20)
            timestamp_ms, distance = await self._rotary_board.get_cur_pos_mm()
            context.set_code(grpc.StatusCode.OK)

            return hardware_manager_pb.GetNextDistanceResponse(timestamp_ms=timestamp_ms, distance=distance,)
        except (RotaryTicksException, AssertionError):
            LOG.exception("Exception getting rotary ticks")
            context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
            context.set_details("Exception getting rotary ticks")
            return hardware_manager_pb.GetNextDistanceResponse(timestamp_ms=0, distance=0)

    async def GetNextVelocity(
        self, request: hardware_manager_pb.GetNextVelocityRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.GetNextVelocityResponse:
        try:
            assert self._rotary_board is not None and await self._rotary_board.booted()
            while (await self._rotary_board.get_last_snapshot()).time_ms <= request.timestamp_ms:
                await async_sleep_ms(20)
            mm_per_ms, timestamp_ms = await self._rotary_board.get_velocity()

            if self._safety_board is None or not await self._safety_board.booted() or not self._safety_board.ok:
                lifted = False
            else:
                status = await self._safety_board.get_status()
                lifted = status.lift_prox

            return hardware_manager_pb.GetNextVelocityResponse(
                timestamp_ms=timestamp_ms, mm_per_ms=mm_per_ms, lifted=lifted
            )
        except (RotaryTicksException, AssertionError):
            context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
            timestamp_ms = 0
            mm_per_ms = 0
            lifted = False
        except SafetyBoardException:
            context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
            lifted = False

        return hardware_manager_pb.GetNextVelocityResponse(
            timestamp_ms=timestamp_ms, mm_per_ms=mm_per_ms, lifted=lifted
        )

    async def GetRotaryTicks(
        self, request: hardware_manager_pb.GetRotaryTicksRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.GetRotaryTicksResponse:
        try:
            assert self._rotary_board is not None and await self._rotary_board.booted()
            snapshot = await self._rotary_board.get_last_snapshot()

            return hardware_manager_pb.GetRotaryTicksResponse(
                timestamp_ms=snapshot.time_ms,
                front_left_ticks=snapshot.front_left,
                front_right_ticks=snapshot.front_right,
                back_left_ticks=snapshot.back_left,
                back_right_ticks=snapshot.back_right,
                front_left_enabled=self._config_subscriber.get_config_node(
                    "common", "wheel_encoders/front_left/enabled"
                ).get_bool_value(),
                front_right_enabled=self._config_subscriber.get_config_node(
                    "common", "wheel_encoders/front_right/enabled"
                ).get_bool_value(),
                back_left_enabled=self._config_subscriber.get_config_node(
                    "common", "wheel_encoders/back_left/enabled"
                ).get_bool_value(),
                back_right_enabled=self._config_subscriber.get_config_node(
                    "common", "wheel_encoders/back_right/enabled"
                ).get_bool_value(),
            )
        except RotaryTicksException:
            context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
            return hardware_manager_pb.GetRotaryTicksResponse(
                timestamp_ms=0, front_left_ticks=0, front_right_ticks=0, back_left_ticks=0, back_right_ticks=0
            )

    async def GetWheelEncoderResolution(
        self, request: hardware_manager_pb.GetWheelEncoderResolutionRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.GetWheelEncoderResolutionResponse:
        try:
            assert self._rotary_board is not None and await self._rotary_board.booted()
            return hardware_manager_pb.GetWheelEncoderResolutionResponse(
                resolution=self._rotary_board.board.rotary_resolution
            )
        except BaseException:
            LOG.exception("Error getting wheel encoder resolution")
            context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
            return hardware_manager_pb.GetWheelEncoderResolutionResponse(resolution=0)

    async def GetSafetyStatus(
        self, request: hardware_manager_pb.GetSafetyStatusRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.GetSafetyStatusResponse:

        try:
            assert self._safety_board is not None and await self._safety_board.booted(), "Safety PLC unavailable"
            status = await self._safety_board.get_status()
            context.set_code(grpc.StatusCode.OK)
            return hardware_manager_pb.GetSafetyStatusResponse(
                lifted=status.lift_prox,
                estopped=status.overall_e_stop,
                in_cab_estopped=status.in_cab_e_stop,
                left_estopped=status.left_e_stop,
                right_estopped=status.right_e_stop,
                laser_key=status.laser_key,
                interlock=status.interlock,
                water_protect=status.water_protect,
                reset_required=status.reset_required,
                center_estop=status.center_e_stop,
                power_button_estop=status.power_button_e_stop,
                left_lpsu_interlock=status.left_lpsu_door_interlock,
                right_lpsu_interlock=status.right_lpsu_door_interlock,
                debug_mode=status.debug_mode,
            )
        except BaseException:
            LOG.exception("Exception getting safety PLC status")
            context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
            context.set_details("Exception getting safety PLC status")
            return hardware_manager_pb.GetSafetyStatusResponse()

    async def GetManagedBoardErrors(
        self, request: hardware_manager_pb.GetManagedBoardErrorsRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.GetManagedBoardErrorsResponse:
        response = hardware_manager_pb.GetManagedBoardErrorsResponse(encoder_error_flag=False, encoder_error_msg="")
        for board in self._hardware_manager.hw_managed_boards:
            if not board.ok:
                response.board.append(board.board_name)
        if self._rotary_board is not None:
            enc_msg = self._rotary_board.encoder_error_msg
            if enc_msg is not None:
                response.encoder_error_flag = True
                response.encoder_error_msg = enc_msg

        if self._peplink_gps is not None:
            # TODO: get fix status from Peplink, we only use on MVS right now
            response.gps_has_fix = True
        elif self._gps_board is not None:
            response.gps_has_fix = self._gps_board.has_fix

        context.set_code(grpc.StatusCode.OK)
        return response

    async def GetGPSData(
        self, request: hardware_manager_pb.GetGPSDataRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.GetGPSDataResponse:
        geo_lla = hardware_manager_pb.GeoLLA(lat=0, lng=0, alt=0, timestamp_ms=0)
        geo_ecef = hardware_manager_pb.GeoECEF(timestamp_ms=0)
        try:
            if self._peplink_gps is None:
                assert self._gps_board is not None
                assert await self._gps_board.booted()
                geo_lla_msg, geo_ecef_msg = await self._gps_board.position_lla_and_ecef()

                position_fixed = geo_lla_msg.timestamp_ms > 0

                context.set_code(grpc.StatusCode.OK)
                if position_fixed:
                    geo_lla.lat = geo_lla_msg.lat
                    geo_lla.lng = geo_lla_msg.lon
                    geo_lla.alt = geo_lla_msg.alt
                    geo_lla.timestamp_ms = geo_lla_msg.timestamp_ms

                    geo_ecef.x = geo_ecef_msg.x
                    geo_ecef.y = geo_ecef_msg.y
                    geo_ecef.z = geo_ecef_msg.z
                    geo_ecef.timestamp_ms = geo_ecef_msg.timestamp_ms
                else:
                    msg = "GPS position is not fixed"
                    LOG.warning(msg)
                    if request.validate:
                        context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                        context.set_details(msg)
            else:  # This is using the peplink GPS
                gga = await self._peplink_gps.get_next_gga()
                assert gga is not None

                if gga.is_valid():
                    geo_lla.lat = cast(float, gga.latitude)
                    geo_lla.lng = cast(float, gga.longitude)
                    geo_lla.alt = gga.altitude
                    geo_lla.timestamp_ms = gga.timestamp

                    assert gga.ecef is not None
                    geo_ecef.x = gga.ecef[0]
                    geo_ecef.y = gga.ecef[1]
                    geo_ecef.z = gga.ecef[2]
                    geo_ecef.timestamp_ms = gga.timestamp
                else:
                    msg = "GPS position is not fixed"
                    LOG.warning(msg)
                    if request.validate:
                        context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                        context.set_details(msg)

        except GPSException:
            LOG.exception("GPS Error")
            if request.validate:
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details("GPS Error")
        return hardware_manager_pb.GetGPSDataResponse(lla=geo_lla, ecef=geo_ecef)

    async def GetNextGPSData(
        self, request: hardware_manager_pb.GetNextGPSDataRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.GetNextGPSDataResponse:
        try:
            if self._peplink_gps is None:
                assert self._gps_board is not None
                assert await self._gps_board.booted()
                geo_lla_msg = await self._gps_board.get_next_position_lla(request.timestamp_ms)

                geo_lla = hardware_manager_pb.GeoLLA()

                position_fixed = geo_lla_msg.timestamp_ms > 0

                if position_fixed:
                    geo_lla.lat = geo_lla_msg.lat
                    geo_lla.lng = geo_lla_msg.lon
                    geo_lla.alt = geo_lla_msg.alt
                    geo_lla.timestamp_ms = geo_lla_msg.timestamp_ms

                else:
                    LOG.warning("GPS position is not fixed")
            else:
                gga = await self._peplink_gps.get_next_gga(request.timestamp_ms)
                assert gga is not None

                if gga.is_valid():
                    geo_lla.lat = cast(float, gga.latitude)
                    geo_lla.lng = cast(float, gga.longitude)
                    geo_lla.alt = gga.altitude
                    geo_lla.timestamp_ms = gga.timestamp
                else:
                    LOG.warning("GPS position is not fixed")

            context.set_code(grpc.StatusCode.OK)
            return hardware_manager_pb.GetNextGPSDataResponse(lla=geo_lla)

        except GPSException:
            LOG.exception("GPS Error")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details("GPS Error")
            return hardware_manager_pb.GetNextGPSDataResponse()

    async def GetNextRawGPSData(
        self, request: hardware_manager_pb.GetNextRawGPSDataRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.GetNextRawGPSDataResponse:
        try:
            resp = hardware_manager_pb.GetNextRawGPSDataResponse()
            if self._peplink_gps is None:
                assert self._gps_board is not None
                assert await self._gps_board.booted()
                pos = await self._gps_board.get_next_position(request.timestamp_ms)

                context.set_code(grpc.StatusCode.OK)
                resp = hardware_manager_pb.GetNextRawGPSDataResponse(
                    have_fix=pos.have_fix,
                    have_approx_fix=pos.have_approx_fix,
                    latitude=pos.latitude,
                    longitude=pos.longitude,
                    height_mm=pos.height_mm,
                    num_sats=pos.num_sats,
                    hdop=pos.hdop,
                    timestamp_ms=pos.timestamp_ms,
                    fix_type=cast(FixTypeType, pos.fix_type.value),
                    gnss_valid=pos.fix_flags.gnss_fix_ok,
                    diff_corrections=pos.fix_flags.diff_soln,
                    carrier_phase=cast(CarrierPhaseSolnType, pos.fix_flags.carr_soln.value),
                    dual=None,
                )
                if pos.dual_gps_data is not None:
                    resp.dual.SetInParent()
                    resp.dual.gnss_valid = pos.dual_gps_data.gnss_valid
                    resp.dual.diff_corrections = pos.dual_gps_data.diff_corrections
                    resp.dual.is_moving_base = pos.dual_gps_data.is_moving_base
                    resp.dual.carrier_phase = cast(CarrierPhaseSolnType, pos.dual_gps_data.carrier_solution.value)

                    if pos.dual_gps_data.north is not None:
                        resp.dual.north.SetInParent()
                        resp.dual.north.value = pos.dual_gps_data.north.value
                        resp.dual.north.accuracy = pos.dual_gps_data.north.accuracy
                    if pos.dual_gps_data.east is not None:
                        resp.dual.east.SetInParent()
                        resp.dual.east.value = pos.dual_gps_data.east.value
                        resp.dual.east.accuracy = pos.dual_gps_data.east.accuracy
                    if pos.dual_gps_data.down is not None:
                        resp.dual.down.SetInParent()
                        resp.dual.down.value = pos.dual_gps_data.down.value
                        resp.dual.down.accuracy = pos.dual_gps_data.down.accuracy
                    if pos.dual_gps_data.length is not None:
                        resp.dual.length.SetInParent()
                        resp.dual.length.value = pos.dual_gps_data.length.value
                        resp.dual.length.accuracy = pos.dual_gps_data.length.accuracy
                    if pos.dual_gps_data.heading is not None:
                        resp.dual.heading.SetInParent()
                        resp.dual.heading.value = pos.dual_gps_data.heading.value
                        resp.dual.heading.accuracy = pos.dual_gps_data.heading.accuracy
                return resp
            else:
                gga = await self._peplink_gps.get_next_gga(request.timestamp_ms)
                assert gga is not None
                fix_type = hardware_manager_pb.NO_FIX
                if FixQuality.bigger_than(gga.fix_quality, FixQuality.DGPS_FIX):
                    fix_type = hardware_manager_pb.FIX_3D
                elif FixQuality.bigger_than(gga.fix_quality, FixQuality.GPS_FIX):
                    fix_type = hardware_manager_pb.FIX_2D
                context.set_code(grpc.StatusCode.OK)
                resp = hardware_manager_pb.GetNextRawGPSDataResponse(
                    have_fix=FixQuality.bigger_than(gga.fix_quality, FixQuality.DGPS_FIX),
                    have_approx_fix=FixQuality.bigger_than(gga.fix_quality, FixQuality.GPS_FIX),
                    latitude=cast(float, gga.latitude),
                    longitude=cast(float, gga.longitude),
                    height_mm=int(gga.altitude * 1000),
                    num_sats=gga.num_satellites,
                    hdop=None,
                    timestamp_ms=gga.timestamp,
                    fix_type=fix_type,
                    gnss_valid=FixQuality.bigger_than(gga.fix_quality, FixQuality.GPS_FIX),
                    diff_corrections=False,
                    carrier_phase=None,
                    dual=None,
                )
            return resp
        except GPSException:
            LOG.exception("GPS Error")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details("GPS Error")
            return hardware_manager_pb.GetNextRawGPSDataResponse()

    async def GetGPSFixedPos(
        self, _: hardware_manager_pb.GetGPSFixedPosRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.GetGPSFixedPosResponse:
        try:
            pos = ROBOT_POSE.fixed_point
            return hardware_manager_pb.GetGPSFixedPosResponse(x_mm=pos.x_mm, y_mm=pos.y_mm)
        except Exception:
            LOG.exception("Exception getting gps fixed pos")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details("Exception getting gps fixed pos")
            return hardware_manager_pb.GetGPSFixedPosResponse(x_mm=0.0, y_mm=0.0)

    async def Get240vUptime(
        self, request: hardware_manager_pb.Get240vUptimeRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.Get240vUptimeResponse:
        try:
            assert self._supervisory_board is not None and (
                await self._supervisory_board.booted()
            ), "Supervisory PLC unavailable"
            if is_bud():
                uptime = 0
            else:
                uptime = await self._supervisory_board.get_240v_uptime()
            return hardware_manager_pb.Get240vUptimeResponse(uptime_s=uptime)
        except BaseException:
            LOG.exception("Exception getting 240V uptime")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details("Exception getting 240V uptime")
            return hardware_manager_pb.Get240vUptimeResponse(uptime_s=0)

    async def GetRuntime(
        self, request: hardware_manager_pb.GetRuntimeRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.GetRuntimeResponse:
        if is_bud():
            return hardware_manager_pb.GetRuntimeResponse()
        try:
            assert self._supervisory_board is not None and (
                await self._supervisory_board.booted()
            ), "Supervisory PLC unavailable"
            return hardware_manager_pb.GetRuntimeResponse(runtime_240v=await self._supervisory_board.get_240v_runtime())
        except BaseException:
            LOG.exception("Exception getting machine run time")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details("Exception getting machine run time")
            return hardware_manager_pb.GetRuntimeResponse()

    def _populate_chiller_alarms(self, plcInfo: Dict[str, Any]) -> hardware_manager_pb.ChillerAlarms:
        """
        Decode the PLC status bits for chiller status and convert to the message type used in
        RPC calls.
        """
        chillerAlarm1 = int(plcInfo[supervisory_messages.CHILLER_ALARM1])
        chillerAlarm2 = int(plcInfo[supervisory_messages.CHILLER_ALARM2])
        chillerAlarm3 = int(plcInfo[supervisory_messages.CHILLER_ALARM3])
        chillerAlarm4 = int(plcInfo[supervisory_messages.CHILLER_ALARM4])
        return hardware_manager_pb.ChillerAlarms(
            low_level_in_tank=chillerAlarm1 >> 0 & 1 == 1,
            high_circulating_fluid_discharge_temp=chillerAlarm1 >> 1 & 1 == 1,
            circulating_fluid_discharge_temp_rise=chillerAlarm1 >> 2 & 1 == 1,
            circulating_fluid_discharge_temp_drop=chillerAlarm1 >> 3 & 1 == 1,
            high_circulating_fluid_return_temp=chillerAlarm1 >> 4 & 1 == 1,
            circulating_fluid_discharge_pressure_rise=chillerAlarm1 >> 7 & 1 == 1,
            circulating_fluid_discharge_pressure_drop=chillerAlarm1 >> 8 & 1 == 1,
            high_compressor_suction_temp=chillerAlarm1 >> 9 & 1 == 1,
            low_compressor_suction_temp=chillerAlarm1 >> 10 & 1 == 1,
            low_super_heat_temp=chillerAlarm1 >> 11 & 1 == 1,
            high_compressor_discharge_pressure=chillerAlarm1 >> 12 & 1 == 1,
            refrigerant_circut_pressure_high_drop=chillerAlarm1 >> 14 & 1 == 1,
            refrigerant_circut_pressure_low_rise=chillerAlarm1 >> 15 & 1 == 1,
            refrigerant_circut_pressure_low_drop=chillerAlarm2 >> 0 & 1 == 1,
            compressor_running_failure=chillerAlarm2 >> 1 & 1 == 1,
            communication_error=chillerAlarm2 >> 2 & 1 == 1,
            memory_error=chillerAlarm2 >> 3 & 1 == 1,
            dc_line_fuse_cut=chillerAlarm2 >> 4 & 1 == 1,
            circulating_fluid_discharge_temp_sensor_failure=chillerAlarm2 >> 5 & 1 == 1,
            circulating_fluid_return_temp_sensor_failure=chillerAlarm2 >> 6 & 1 == 1,
            circulating_fluid_suction_temp_sensor_failure=chillerAlarm2 >> 7 & 1 == 1,
            circulating_fluid_discharge_pressure_sensor_failure=chillerAlarm2 >> 8 & 1 == 1,
            compressor_discharge_pressure_sensor_failure=chillerAlarm2 >> 9 & 1 == 1,
            compressor_suction_pressure_sensor_failure=chillerAlarm2 >> 10 & 1 == 1,
            pump_maintenance=chillerAlarm2 >> 11 & 1 == 1,
            fan_maintenance=chillerAlarm2 >> 12 & 1 == 1,
            compressor_maintenance=chillerAlarm2 >> 13 & 1 == 1,
            contact_input_1_signal_detection=chillerAlarm2 >> 14 & 1 == 1,
            contact_input_2_signal_detection=chillerAlarm2 >> 15 & 1 == 1,
            compressor_discharge_temp_sensor_failure=chillerAlarm3 >> 4 & 1 == 1,
            compressor_discharge_temp_rise=chillerAlarm3 >> 5 & 1 == 1,
            dustproof_filter_maintenance=chillerAlarm3 >> 7 & 1 == 1,
            power_stoppage=chillerAlarm3 >> 8 & 1 == 1,
            compressor_waiting=chillerAlarm3 >> 9 & 1 == 1,
            fan_failure=chillerAlarm3 >> 10 & 1 == 1,
            compressor_over_current=chillerAlarm3 >> 12 & 1 == 1,
            pump_over_current=chillerAlarm3 >> 14 & 1 == 1,
            air_exhaust_fan_stoppage=chillerAlarm4 >> 0 & 1 == 1,
            incorrect_phase_error=chillerAlarm4 >> 1 & 1 == 1,
            phase_board_over_current=chillerAlarm4 >> 2 & 1 == 1,
        )

    async def GetSupervisoryStatus(
        self, request: hardware_manager_pb.GetSupervisoryStatusRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.GetSupervisoryStatusResponse:
        try:
            assert self._supervisory_board is not None and (
                await self._supervisory_board.booted()
            ), "Supervisory PLC unavailable"
            out = await self._supervisory_board.get_plc_output()
            context.set_code(grpc.StatusCode.OK)

            chiller_alarms = self._populate_chiller_alarms(out)
            return hardware_manager_pb.GetSupervisoryStatusResponse(
                water_protect_status=out[supervisory_messages.WATER_PROTECT_STATUS] == "1",
                main_contactor_status_fb=out[supervisory_messages.MAIN_CONTACTOR_STATUS_FB] == "1",
                power_good=out[supervisory_messages.POWER_GOOD] == "1",
                power_bad=out[supervisory_messages.POWER_BAD] == "1",
                power_very_bad=out[supervisory_messages.POWER_VERY_BAD] == "1",
                lifted_status=out[supervisory_messages.LIFTED_STATUS] == "1",
                temp_humidity_status=out[supervisory_messages.TEMP_HUMIDITY_STATUS] == "1",
                tractor_power=out[supervisory_messages.TRACTOR_POWER] == "1",
                ac_frequency=float(out[supervisory_messages.AC_FREQUENCY]) / 10,
                ac_voltage_a_b=float(out[supervisory_messages.AC_VOLTAGE_A_B]) / 10,
                ac_voltage_b_c=float(out[supervisory_messages.AC_VOLTAGE_B_C]) / 10,
                ac_voltage_a_c=float(out[supervisory_messages.AC_VOLTAGE_A_C]) / 10,
                ac_voltage_a=float(out[supervisory_messages.AC_VOLTAGE_A]) / 10,
                ac_voltage_b=float(out[supervisory_messages.AC_VOLTAGE_B]) / 10,
                ac_voltage_c=float(out[supervisory_messages.AC_VOLTAGE_C]) / 10,
                phase_power_w_3=int(out[supervisory_messages.PHASE_POWER_W_3]),
                phase_power_va_3=int(out[supervisory_messages.PHASE_POWER_VA_3]),
                power_factor=float(out[supervisory_messages.POWER_FACTOR]) / 100,
                server_cabinet_temp=float(out[supervisory_messages.SERVER_CABINET_TEMP]) / 100,
                server_cabinet_humidity=float(out[supervisory_messages.SERVER_CABINET_HUMIDITY]) / 100,
                battery_voltage_12v=float(out[supervisory_messages.BATTERY_VOLTAGE_12V]) / 1000,
                temp_bypass_status=out[supervisory_messages.TEMP_BYPASS_STATUS] == "1",
                humidity_bypass_status=out[supervisory_messages.HUMIDITY_BYPASS_STATUS] == "1",
                temp_status=out[supervisory_messages.TEMP_STATUS] == "1",
                humidity_status=out[supervisory_messages.HUMIDITY_STATUS] == "1",
                btl_disabled=[
                    out[supervisory_messages.ROW_1_BTL_DISABLE] == "1",
                    out[supervisory_messages.ROW_2_BTL_DISABLE] == "1",
                    out[supervisory_messages.ROW_3_BTL_DISABLE] == "1",
                ],
                server_disabled=[
                    out[supervisory_messages.ROW_1_SERVER_DISABLE] == "1",
                    out[supervisory_messages.ROW_2_SERVER_DISABLE] == "1",
                    out[supervisory_messages.ROW_3_SERVER_DISABLE] == "1",
                ],
                scanners_disabled=[
                    out[supervisory_messages.ROW_1_SCANNERS_DISABLE] == "1",
                    out[supervisory_messages.ROW_2_SCANNERS_DISABLE] == "1",
                    out[supervisory_messages.ROW_3_SCANNERS_DISABLE] == "1",
                ],
                wheel_encoder_disabled=out[supervisory_messages.WHEEL_ENCODER_DISABLE] == "1",
                gps_disabled=out[supervisory_messages.GPS_DISABLE] == "1",
                strobe_disabled=out[supervisory_messages.STROBE_DISABLE] == "1",
                main_contactor_disabled=out[supervisory_messages.MAIN_CONTACTOR_DISABLED] == "1",
                air_conditioner_disabled=out[supervisory_messages.AIR_CONDITIONER_DISABLE] == "1",
                chiller_disabled=out[supervisory_messages.CHILLER_DISABLE] == "1",
                chiller_temp=float(out[supervisory_messages.CHILLER_TEMP]) / 10,
                chiller_flow=float(out[supervisory_messages.CHILLER_FLOW]) / 10,
                chiller_pressure=float(out[supervisory_messages.CHILLER_PRESSURE]) / 10,
                chiller_conductivity=float(out[supervisory_messages.CHILLER_CONDUCTIVITY]) / 10,
                chiller_set_temp=float(out[supervisory_messages.CHILLER_SET_TEMP]) / 10,
                chiller_alarms=chiller_alarms,
            )
        except BaseException:
            LOG.exception("Exception getting plc status")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details("Exception getting plc status")
            return hardware_manager_pb.GetSupervisoryStatusResponse()

    async def GetReaperSupervisoryStatus(
        self, request: hardware_manager_pb.GetReaperSupervisoryStatusRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.ReaperCenterEnclosureData:
        try:
            assert self._supervisory_board is not None and (
                await self._supervisory_board.booted()
            ), "Supervisory PLC unavailable"
            out = await self._supervisory_board.get_plc_output()
            context.set_code(grpc.StatusCode.OK)

            return self._convert_reaper_center_enclosure(out)
        except BaseException:
            LOG.exception("Exception getting plc status")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details("Exception getting plc status")
            return hardware_manager_pb.ReaperCenterEnclosureData()

    def _convert_reaper_center_enclosure(self, out: Dict[str, int]) -> hardware_manager_pb.ReaperCenterEnclosureData:
        # XXX: a lot of this is copy/pasted from GetSupervisoryStatus; perhaps there is a way we
        # can share a lot of this parsing code with it?
        chiller_alarms = self._populate_chiller_alarms(out)
        return hardware_manager_pb.ReaperCenterEnclosureData(
            water_protect_status=out[supervisory_messages.WATER_PROTECT_STATUS] == "1",
            main_contactor_status_fb=out[supervisory_messages.MAIN_CONTACTOR_STATUS_FB] == "1",
            power_good=out[supervisory_messages.POWER_GOOD] == "1",
            power_bad=out[supervisory_messages.POWER_BAD] == "1",
            power_very_bad=out[supervisory_messages.POWER_VERY_BAD] == "1",
            lifted_status=out[supervisory_messages.LIFTED_STATUS] == "1",
            # always applicable as command is powered via ISOBUS
            tractor_power=True,
            ac_frequency=float(out[supervisory_messages.AC_FREQUENCY]) / 10,
            ac_voltage_a_b=float(out[supervisory_messages.AC_VOLTAGE_A_B]) / 10,
            ac_voltage_b_c=float(out[supervisory_messages.AC_VOLTAGE_B_C]) / 10,
            ac_voltage_a_c=float(out[supervisory_messages.AC_VOLTAGE_A_C]) / 10,
            ac_voltage_a=float(out[supervisory_messages.AC_VOLTAGE_A]) / 10,
            ac_voltage_b=float(out[supervisory_messages.AC_VOLTAGE_B]) / 10,
            ac_voltage_c=float(out[supervisory_messages.AC_VOLTAGE_C]) / 10,
            phase_power_w_3=int(out[supervisory_messages.PHASE_POWER_W_3]),
            phase_power_va_3=int(out[supervisory_messages.PHASE_POWER_VA_3]),
            power_factor=float(out[supervisory_messages.POWER_FACTOR]) / 100,
            server_cabinet_temp=float(out[supervisory_messages.SERVER_CABINET_TEMP]) / 100,
            server_cabinet_humidity=float(out[supervisory_messages.SERVER_CABINET_HUMIDITY]) / 100,
            battery_voltage_12v=float(out[supervisory_messages.BATTERY_VOLTAGE_12V]) / 1000,
            wheel_encoder_disabled=out[supervisory_messages.WHEEL_ENCODER_DISABLE] == "1",
            gps_disabled=out[supervisory_messages.GPS_DISABLE] == "1",
            main_contactor_disabled=out[supervisory_messages.MAIN_CONTACTOR_DISABLED] == "1",
            air_conditioner_disabled=out[supervisory_messages.AIR_CONDITIONER_DISABLE] == "1",
            chiller_disabled=out[supervisory_messages.CHILLER_DISABLE] == "1",
            chiller_temp=float(out[supervisory_messages.CHILLER_TEMP]) / 10,
            chiller_flow=float(out[supervisory_messages.CHILLER_FLOW]) / 10,
            chiller_pressure=float(out[supervisory_messages.CHILLER_PRESSURE]) / 10,
            chiller_conductivity=float(out[supervisory_messages.CHILLER_CONDUCTIVITY]) / 10,
            chiller_set_temp=float(out[supervisory_messages.CHILLER_SET_TEMP]) / 10,
            chiller_heat_transfer=float(out[supervisory_messages.CHILLER_HEAT_TRANSFER]) / 10,
            chiller_fluid_delta_temp=float(out[supervisory_messages.CHILLER_FLUID_DELTA_TEMP]) / 10,
            chiller_alarms=chiller_alarms,
        )

    async def GetReaperEnclosureSensors(
        self, request: hardware_manager_pb.GetReaperEnclosureSensorsRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.GetReaperEnclosureSensorsResponse:
        try:
            assert self._supervisory_board is not None and (
                await self._supervisory_board.booted()
            ), "Supervisory PLC unavailable"
            out = await self._supervisory_board.get_plc_output()
            center_sensors = self._convert_reaper_center_enclosure(out)

            context.set_code(grpc.StatusCode.OK)
            return hardware_manager_pb.GetReaperEnclosureSensorsResponse(sensors=center_sensors)

        except BaseException:
            LOG.exception("Exception getting center enclosure status")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details("Exception getting center enclosure status")
            return hardware_manager_pb.GetReaperEnclosureSensorsResponse()

    async def GetReaperModuleSensors(
        self, request: hardware_manager_pb.GetReaperModuleSensorsRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.GetReaperModuleSensorsResponse:
        async def _get_data_for_id(id: int) -> ModuleSensorData:
            """
            Helper to get a module's sensor data or raise an exception if not found
            """
            assert self._reaper_sensor_poller
            data = await self._reaper_sensor_poller.get_module_sensors(id)
            if not data:
                raise RuntimeError(f"Failed to get sensor data for module {id}")
            return data

        try:
            assert self._reaper_sensor_poller

            raw_data: List[ModuleSensorData]

            if len(request.module_ids) == 0:
                raw_data = list((await self._reaper_sensor_poller.get_all_module_sensors()).values())
            else:
                raw_data = list([await _get_data_for_id(id) for id in request.module_ids])

            context.set_code(grpc.StatusCode.OK)
            return hardware_manager_pb.GetReaperModuleSensorsResponse(module_sensors=[x.to_message() for x in raw_data])
        except BaseException:
            LOG.exception("Exception getting module sensors")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details("Exception getting module sensors")
            return hardware_manager_pb.GetReaperModuleSensorsResponse()

    async def SetServerDisable(
        self, request: hardware_manager_pb.SetServerDisableRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetServerDisableResponse:
        try:
            assert self._supervisory_board is not None and await self._supervisory_board.booted()
            if request.disable:
                status = await self._supervisory_board.disable_row(request.row_id)
            else:
                status = await self._supervisory_board.enable_row(request.row_id)
            if status:
                context.set_code(grpc.StatusCode.OK)
            else:
                context.set_code(grpc.StatusCode.UNKNOWN)
            return hardware_manager_pb.SetServerDisableResponse(success=status)
        except BaseException:
            LOG.exception(f"Exception setting row {request.row_id}")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting row {request.row_id}")
            return hardware_manager_pb.SetServerDisableResponse(success=False)

    async def SetBTLDisable(
        self, request: hardware_manager_pb.SetBTLDisableRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetBTLDisableResponse:
        try:
            assert self._supervisory_board is not None and await self._supervisory_board.booted()
            if request.disable:
                status = await self._supervisory_board.disable_btl(request.row_id)
            else:
                status = await self._supervisory_board.enable_btl(request.row_id)
            if status:
                context.set_code(grpc.StatusCode.OK)
            else:
                context.set_code(grpc.StatusCode.UNKNOWN)
            return hardware_manager_pb.SetBTLDisableResponse(success=status)
        except BaseException:
            LOG.exception(f"Exception setting row {request.row_id}")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting row {request.row_id}")
            return hardware_manager_pb.SetBTLDisableResponse(success=False)

    async def SetScannersDisable(
        self, request: hardware_manager_pb.SetScannersDisableRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetScannersDisableResponse:
        try:
            assert self._supervisory_board is not None and await self._supervisory_board.booted()
            if request.disable:
                status = await self._supervisory_board.disable_scanners(request.row_id)
            else:
                status = await self._supervisory_board.enable_scanners(request.row_id)
            if status:
                context.set_code(grpc.StatusCode.OK)
            else:
                context.set_code(grpc.StatusCode.UNKNOWN)
            return hardware_manager_pb.SetScannersDisableResponse(success=status)
        except BaseException:
            LOG.exception(f"Exception setting row {request.row_id}")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting row {request.row_id}")
            return hardware_manager_pb.SetScannersDisableResponse(success=False)

    async def SetWheelEncoderBoardDisable(
        self, request: hardware_manager_pb.SetWheelEncoderBoardDisableRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetWheelEncoderBoardDisableResponse:
        try:
            assert self._supervisory_board is not None and await self._supervisory_board.booted()
            if request.disable:
                status = await self._supervisory_board.disable_wheel_encoder()
            else:
                status = await self._supervisory_board.enable_wheel_encoder()
            if status:
                context.set_code(grpc.StatusCode.OK)
            else:
                context.set_code(grpc.StatusCode.UNKNOWN)
            return hardware_manager_pb.SetWheelEncoderBoardDisableResponse(success=status)
        except BaseException:
            LOG.exception(f"Exception setting wheel encoder disable={request.disable}")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting wheel encoder disable={request.disable}")
            return hardware_manager_pb.SetWheelEncoderBoardDisableResponse(success=False)

    async def SetWheelEncoderDisable(
        self, request: hardware_manager_pb.SetWheelEncoderDisableRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetWheelEncoderDisableResponse:
        try:
            assert self._supervisory_board is not None and await self._supervisory_board.booted()
            if request.left and request.front:
                self._config_subscriber.get_client().set_bool_value(
                    "common/wheel_encoders/front_left/enabled", not request.disable
                )
            elif request.left and not request.front:
                self._config_subscriber.get_client().set_bool_value(
                    "common/wheel_encoders/back_left/enabled", not request.disable
                )
            elif not request.left and request.front:
                self._config_subscriber.get_client().set_bool_value(
                    "common/wheel_encoders/front_right/enabled", not request.disable
                )
            elif not request.left and not request.front:
                self._config_subscriber.get_client().set_bool_value(
                    "common/wheel_encoders/back_right/enabled", not request.disable
                )
            return hardware_manager_pb.SetWheelEncoderDisableResponse(success=True)
        except BaseException:
            LOG.exception(f"Exception setting wheel encoder disable={request.disable}")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting wheel encoder disable={request.disable}")
            return hardware_manager_pb.SetWheelEncoderDisableResponse(success=False)

    async def SetStrobeDisable(
        self, request: hardware_manager_pb.SetStrobeDisableRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetStrobeDisableResponse:
        try:
            assert self._supervisory_board is not None and await self._supervisory_board.booted()
            if request.disable:
                status = await self._supervisory_board.disable_strobe()
            else:
                status = await self._supervisory_board.enable_strobe()
            if status:
                context.set_code(grpc.StatusCode.OK)
            else:
                context.set_code(grpc.StatusCode.UNKNOWN)
            return hardware_manager_pb.SetStrobeDisableResponse(success=status)
        except BaseException:
            LOG.exception(f"Exception setting strobe disable={request.disable}")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting strobe disable={request.disable}")
            return hardware_manager_pb.SetStrobeDisableResponse(success=False)

    async def SetGPSDisable(
        self, request: hardware_manager_pb.SetGPSDisableRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetGPSDisableResponse:
        try:
            assert self._supervisory_board is not None and await self._supervisory_board.booted()
            if request.disable:
                status = await self._supervisory_board.disable_gps()
            else:
                status = await self._supervisory_board.enable_gps()
            if status:
                context.set_code(grpc.StatusCode.OK)
            else:
                context.set_code(grpc.StatusCode.UNKNOWN)
            return hardware_manager_pb.SetGPSDisableResponse(success=status)
        except BaseException:
            LOG.exception(f"Exception setting gps disable={request.disable}")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting gps disable={request.disable}")
            return hardware_manager_pb.SetGPSDisableResponse(success=False)

    async def SetAirConditionerDisable(
        self, request: hardware_manager_pb.SetAirConditionerDisableRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetAirConditionerDisableResponse:
        try:
            assert self._supervisory_board is not None and await self._supervisory_board.booted()
            if request.disable:
                status = await self._supervisory_board.disable_air_conditioner()
            else:
                status = await self._supervisory_board.enable_air_conditioner()
            if status:
                context.set_code(grpc.StatusCode.OK)
            else:
                context.set_code(grpc.StatusCode.UNKNOWN)
            return hardware_manager_pb.SetAirConditionerDisableResponse(success=status)
        except BaseException:
            LOG.exception(f"Exception setting air conditioner disable={request.disable}")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting air conditioner disable={request.disable}")
            return hardware_manager_pb.SetAirConditionerDisableResponse(success=False)

    async def SetChillerDisable(
        self, request: hardware_manager_pb.SetChillerDisableRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetChillerDisableResponse:
        try:
            assert self._supervisory_board is not None and await self._supervisory_board.booted()
            if request.disable:
                status = await self._supervisory_board.disable_chiller()
            else:
                status = await self._supervisory_board.enable_chiller()
            if status:
                context.set_code(grpc.StatusCode.OK)
            else:
                context.set_code(grpc.StatusCode.UNKNOWN)
            return hardware_manager_pb.SetChillerDisableResponse(success=status)
        except BaseException:
            LOG.exception(f"Exception setting chiller disable={request.disable}")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting chiller disable={request.disable}")
            return hardware_manager_pb.SetChillerDisableResponse(success=False)

    async def CommandComputerPowerCycle(
        self, request: hardware_manager_pb.CommandComputerPowerCycleRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.CommandComputerPowerCycleResponse:
        try:
            assert self._supervisory_board is not None and await self._supervisory_board.booted()
            status = await self._supervisory_board.command_computer_power_cycle()
            if status:
                context.set_code(grpc.StatusCode.OK)
            else:
                context.set_code(grpc.StatusCode.UNKNOWN)
            return hardware_manager_pb.CommandComputerPowerCycleResponse(success=status)
        except BaseException:
            LOG.exception("Exception power cycling the command computer")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details("Exception power cycling the command computer")
            return hardware_manager_pb.CommandComputerPowerCycleResponse(success=False)

    async def SetMainContactorDisable(
        self, request: hardware_manager_pb.SetMainContactorDisableRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetMainContactorDisableResponse:
        try:
            assert self._supervisory_board is not None and await self._supervisory_board.booted()
            if request.disable:
                status = await self._supervisory_board.enable_main_contactor_disabled()
            else:
                status = await self._supervisory_board.disable_main_contactor_disabled()
            if status:
                context.set_code(grpc.StatusCode.OK)
            else:
                context.set_code(grpc.StatusCode.UNKNOWN)
            return hardware_manager_pb.SetMainContactorDisableResponse(success=status)
        except BaseException:
            LOG.exception(f"Exception setting main contactor disabled disable={request.disable}")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting main contactor disabled disable={request.disable}")
            return hardware_manager_pb.SetMainContactorDisableResponse(success=False)

    async def SetTempBypassDisable(
        self, request: hardware_manager_pb.SetTempBypassDisableRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetTempBypassDisableResponse:
        try:
            assert self._supervisory_board is not None and await self._supervisory_board.booted()
            if request.disable:
                status = await self._supervisory_board.disable_temp_bypass()
            else:
                status = await self._supervisory_board.enable_temp_bypass()
            if status:
                context.set_code(grpc.StatusCode.OK)
            else:
                context.set_code(grpc.StatusCode.UNKNOWN)
            return hardware_manager_pb.SetTempBypassDisableResponse(success=status)
        except BaseException:
            LOG.exception(f"Exception setting temp disable={request.disable}")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting temp disable={request.disable}")
            return hardware_manager_pb.SetTempBypassDisableResponse(success=False)

    async def SetHumidityBypassDisable(
        self, request: hardware_manager_pb.SetHumidityBypassDisableRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetHumidityBypassDisableResponse:
        try:
            assert self._supervisory_board is not None and await self._supervisory_board.booted()
            if request.disable:
                status = await self._supervisory_board.disable_humidity_bypass()
            else:
                status = await self._supervisory_board.enable_humidity_bypass()
            if status:
                context.set_code(grpc.StatusCode.OK)
            else:
                context.set_code(grpc.StatusCode.UNKNOWN)
            return hardware_manager_pb.SetHumidityBypassDisableResponse(success=status)
        except BaseException:
            LOG.exception(f"Exception setting humidity disable={request.disable}")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting humidity disable={request.disable}")
            return hardware_manager_pb.SetHumidityBypassDisableResponse(success=False)

    async def SetJimboxSpeed(
        self, request: hardware_manager_pb.SetJimboxSpeedRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetJimboxSpeedResponse:
        try:
            assert self._cruise_controller is not None
            speed = await self._cruise_controller.set_speed(
                Speed.from_mph(request.target_speed), Speed.from_mph(request.actual_ground_speed)
            )
            return hardware_manager_pb.SetJimboxSpeedResponse(speed_setpoint=speed.mph)
        except BaseException:
            LOG.exception(f"Exception setting jimbox speed={request.target_speed}")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting jimbox speed={request.target_speed}")
            return hardware_manager_pb.SetJimboxSpeedResponse(speed_setpoint=0.0)

    async def SetCruiseEnabled(
        self, request: hardware_manager_pb.SetCruiseEnabledRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetCruiseEnabledResponse:
        LOG.info(f"Setting cruise control enabled: {request.enabled}")
        try:
            if self._cruise_controller is None:
                return hardware_manager_pb.SetCruiseEnabledResponse(success=False)
            enabled = await self._cruise_controller.set_enabled(request.enabled)
            return hardware_manager_pb.SetCruiseEnabledResponse(success=(enabled == request.enabled))
        except BaseException:
            LOG.exception("Exception enabling cruise control")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details("Exception enabling cruise control")
            return hardware_manager_pb.SetCruiseEnabledResponse(success=False)

    async def GetCruiseStatus(
        self, _: hardware_manager_pb.GetCruiseStatusRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.GetCruiseStatusResponse:
        try:
            if self._cruise_controller is None:
                return hardware_manager_pb.GetCruiseStatusResponse(enabled=False, installed=False, speed=0.0)
            status = await self._cruise_controller.get_status()
            return hardware_manager_pb.GetCruiseStatusResponse(
                enabled=status.enabled,
                installed=status.installed,
                speed=status.speed.mph,
                allow_enable=status.allow_enabled,
            )
        except BaseException:
            LOG.exception("Exception getting cruise control status")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details("Exception getting cruise control status")
            return hardware_manager_pb.GetCruiseStatusResponse(enabled=False, installed=False, speed=0.0)

    async def SetImplementStateOnTractor(
        self, req: hardware_manager_pb.SetImplementStateRequest, _: grpc.ServicerContext
    ) -> hardware_manager_pb.SetImplementStateResponse:
        if self._tractor is not None:
            err_msg = req.error_message if req.HasField("error_message") else None
            await self._tractor.set_implement_state(req.active, req.error, err_msg)
        return hardware_manager_pb.SetImplementStateResponse()

    async def SetSafeStateEnforcement(
        self, req: hardware_manager_pb.SetSafeStateEnforcementRequest, ctx: grpc.ServicerContext
    ) -> hardware_manager_pb.SetSafeStateEnforcementResponse:
        if self._tractor is None:
            ctx.set_code(grpc.StatusCode.FAILED_PRECONDITION)
        else:
            await self._tractor.set_safe_state_enforced(req.enforced)
        return hardware_manager_pb.SetSafeStateEnforcementResponse()

    async def GetTractorSafetyState(
        self, req: hardware_manager_pb.GetTractorSafetyStateRequest, ctx: grpc.ServicerContext
    ) -> hardware_manager_pb.GetTractorSafetyStateResponse:
        if self._tractor is None:
            ctx.set_code(grpc.StatusCode.FAILED_PRECONDITION)
            return hardware_manager_pb.GetTractorSafetyStateResponse()
        state = await self._tractor.get_safety_state(req.timestamp_ms)
        return hardware_manager_pb.GetTractorSafetyStateResponse(
            timestamp_ms=state.timestamp_ms, is_safe=state.is_safe, enforced=state.enforced
        )

    async def GetTractorIFState(
        self, _: hardware_manager_pb.GetTractorIFStateRequest, __: grpc.ServicerContext
    ) -> hardware_manager_pb.GetTractorIFStateResponse:
        if self._tractor is None:
            return hardware_manager_pb.GetTractorIFStateResponse(expected=False, connected=False)
        return hardware_manager_pb.GetTractorIFStateResponse(expected=True, connected=await self._tractor.connected())

    async def GetAvailableUSBStorage(
        self, request: hardware_manager_pb.GetAvailableUSBStorageRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.GetAvailableUSBStorageResponse:
        try:
            used, usb_available = await self._usb_checker.check_usb_device_storage()
            return hardware_manager_pb.GetAvailableUSBStorageResponse(
                used=used, success=True, usb_available=usb_available
            )
        except BaseException:
            LOG.exception("Exception getting available usb space")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details("Exception getting available usb space")
            return hardware_manager_pb.GetAvailableUSBStorageResponse(used=0, success=False, usb_available=False)

    async def GetReady(
        self, request: hardware_manager_pb.GetReadyRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.GetReadyResponse:
        device_statuses = await self._hardware_manager.get_device_statuses()
        ready = all(
            [device_statuses[x].code == DeviceStatusCode.OK for x in await self._hardware_manager.get_device_statuses()]
        )
        return hardware_manager_pb.GetReadyResponse(ready=ready)

    async def SetStrobeSettings(
        self, request: hardware_manager_pb.StrobeSettings, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetStrobeSettingsResponse:
        if self._strobe_board is None:
            return hardware_manager_pb.SetStrobeSettingsResponse(success=False)
        exposure_us = request.exposure_us if request.HasField("exposure_us") else self._strobe_board.device.exposure_us
        period_us = request.period_us if request.HasField("period_us") else self._strobe_board.device.period_us
        targets_per_predict_ratio = (
            request.targets_per_predict_ratio
            if request.HasField("targets_per_predict_ratio")
            else self._strobe_board.device.targets_per_predict_ratio
        )
        if (
            self._strobe_board.device.exposure_us != exposure_us
            or self._strobe_board.device.period_us != period_us
            or self._strobe_board.device.targets_per_predict_ratio != targets_per_predict_ratio
        ):
            await self._strobe_board.device.set_strobe_settings(exposure_us, period_us, targets_per_predict_ratio)
        return hardware_manager_pb.SetStrobeSettingsResponse(success=True)

    async def GetStrobeSettings(
        self, request: hardware_manager_pb.GetStrobeSettingsRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.StrobeSettings:
        if self._strobe_board is None:
            return hardware_manager_pb.StrobeSettings()
        return hardware_manager_pb.StrobeSettings(
            exposure_us=self._strobe_board.device.exposure_us,
            period_us=self._strobe_board.device.period_us,
            targets_per_predict_ratio=self._strobe_board.device.targets_per_predict_ratio,
        )

    async def SetReaperScannerPower(
        self, request: hardware_manager_pb.SetReaperScannerPowerRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetReaperScannerPowerResponse:
        if not is_reaper():
            context.set_code(grpc.StatusCode.UNIMPLEMENTED)
            return hardware_manager_pb.SetReaperScannerPowerResponse(success=False)

        try:
            assert self._reaper_mcb_registry
            assert self._reaper_sensor_poller
            connector = await self._reaper_mcb_registry.get(request.module_id)
            assert connector

            await connector.board.set_scanner_power(
                request.scanner_a_power if request.HasField("scanner_a_power") else None,
                request.scanner_b_power if request.HasField("scanner_b_power") else None,
            )

            try:
                await self._reaper_sensor_poller.update_module(request.module_id, True)
            except BaseException:
                LOG.exception(f"Failed to update sensor data for module {request.module_id}")

            return hardware_manager_pb.SetReaperScannerPowerResponse(success=True)
        except BaseException:
            LOG.exception(f"Exception setting module {request.module_id} scanner power")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting module {request.module_id} scanner power")
            return hardware_manager_pb.SetReaperScannerPowerResponse(success=False)

    async def SetReaperTargetPower(
        self, request: hardware_manager_pb.SetReaperTargetPowerRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetReaperTargetPowerResponse:
        if not is_reaper():
            context.set_code(grpc.StatusCode.UNIMPLEMENTED)
            return hardware_manager_pb.SetReaperTargetPowerResponse(success=False)

        try:
            assert self._reaper_mcb_registry
            assert self._reaper_sensor_poller
            scanners = await self._reaper_mcb_registry.get_scanners(request.module_id)
            assert scanners

            if scanners[0] and request.HasField("target_a_power"):
                await scanners[0].set_power(PulczarBoardConnector.PowerState(targetCam=request.target_a_power))

            if scanners[1] and request.HasField("target_b_power"):
                await scanners[1].set_power(PulczarBoardConnector.PowerState(targetCam=request.target_b_power))

            try:
                await self._reaper_sensor_poller.update_module(request.module_id, True)
            except BaseException:
                LOG.exception(f"Failed to update sensor data for module {request.module_id}")

            return hardware_manager_pb.SetReaperTargetPowerResponse(success=True)
        except BaseException:
            LOG.exception(f"Exception setting module {request.module_id} target power")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting module {request.module_id} target power")
            return hardware_manager_pb.SetReaperTargetPowerResponse(success=False)

    async def SetReaperPredictCamPower(
        self, request: hardware_manager_pb.SetReaperPredictCamPowerRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetReaperPredictCamPowerResponse:
        if not is_reaper():
            context.set_code(grpc.StatusCode.UNIMPLEMENTED)
            return hardware_manager_pb.SetReaperPredictCamPowerResponse(success=False)

        try:
            assert self._reaper_mcb_registry
            assert self._reaper_sensor_poller
            connector = await self._reaper_mcb_registry.get(request.module_id)
            assert connector

            await connector.board.set_power(PowerState(predictCam=request.enabled))

            try:
                await self._reaper_sensor_poller.update_module(request.module_id, True)
            except BaseException:
                LOG.exception(f"Failed to update sensor data for module {request.module_id}")

            return hardware_manager_pb.SetReaperPredictCamPowerResponse(success=True)
        except BaseException:
            LOG.exception(f"Exception setting module {request.module_id} predict cam power")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting module {request.module_id} predict cam power")
            return hardware_manager_pb.SetReaperPredictCamPowerResponse(success=False)

    async def SetReaperStrobeConfig(
        self, request: hardware_manager_pb.SetReaperStrobeConfigRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetReaperStrobeConfigResponse:
        if not is_reaper():
            context.set_code(grpc.StatusCode.UNIMPLEMENTED)
            return hardware_manager_pb.SetReaperStrobeConfigResponse(success=False)

        try:
            assert self._reaper_mcb_registry

            # convert strobe config type
            assert (
                request.settings.period_us
                and request.settings.exposure_us
                and request.settings.targets_per_predict_ratio
            )
            config = StrobeConfig(
                period=Duration.from_microseconds(request.settings.period_us),
                exposureDuration=Duration.from_microseconds(request.settings.exposure_us),
                targetPredictRatio=request.settings.targets_per_predict_ratio,
            )

            # get all modules or a single one
            device: List[ReaperMcbDevice] = []

            if request.module_id:
                connector = await self._reaper_mcb_registry.get(request.module_id)
                assert connector
                device.append(connector)
            else:
                device = list((await self._reaper_mcb_registry.all_with_id()).values())

            # then apply the strobe configs
            await asyncio.gather(*[device.board.set_strobe_config(config) for device in device])

            return hardware_manager_pb.SetReaperStrobeConfigResponse(success=True)
        except BaseException:
            LOG.exception(f"Exception setting module {request.module_id} strobe config")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting module {request.module_id} strobe config")
            return hardware_manager_pb.SetReaperStrobeConfigResponse(success=False)

    async def SetReaperStrobeEnable(
        self, request: hardware_manager_pb.SetReaperStrobeEnableRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetReaperStrobeEnableResponse:
        if not is_reaper():
            context.set_code(grpc.StatusCode.UNIMPLEMENTED)
            return hardware_manager_pb.SetReaperStrobeEnableResponse(success=False)

        try:
            assert self._reaper_mcb_registry
            assert self._reaper_sensor_poller

            # convert list of module IDs
            devices: List[ReaperMcbDevice] = []

            async def _get_device(id: int) -> ReaperMcbDevice:
                assert self._reaper_mcb_registry
                device = await self._reaper_mcb_registry.get(id)
                if device is None:
                    raise RuntimeError(f"Failed to get device for module id {id}")
                return device

            if request.module_ids:
                devices = [await _get_device(id) for id in request.module_ids]
            else:
                devices = list((await self._reaper_mcb_registry.all_with_id()).values())

            if request.duration_ms:
                if request.enabled:
                    raise NotImplementedError("Only timed strobe disable is supported")

                await asyncio.gather(
                    *[
                        device.board.strobe_disable_for(Duration.from_milliseconds(request.duration_ms))
                        for device in devices
                    ]
                )
            else:
                await asyncio.gather(*[device.board.set_strobe_enabled(request.enabled) for device in devices])

            # if module IDs specified, update those modules' data
            if request.module_ids:
                try:
                    await asyncio.gather(
                        *[self._reaper_sensor_poller.update_module(module_id, True) for module_id in request.module_ids]
                    )
                except BaseException:
                    LOG.exception(f"Failed to update sensor data for modules {request.module_ids}")
            # otherwise, request updating async in background
            else:
                await self._reaper_sensor_poller.update_all()

            return hardware_manager_pb.SetReaperStrobeEnableResponse(success=True)
        except BaseException:
            LOG.exception(f"Exception setting module {request.module_ids} strobe config")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting module {request.module_ids} strobe config")
            return hardware_manager_pb.SetReaperStrobeEnableResponse(success=False)

    async def SetReaperModulePcPower(
        self, request: hardware_manager_pb.SetReaperModulePcPowerRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetReaperModulePcPowerResponse:
        if not is_reaper():
            context.set_code(grpc.StatusCode.UNIMPLEMENTED)
            return hardware_manager_pb.SetReaperModulePcPowerResponse(success=False)

        try:
            assert self._reaper_mcb_registry
            assert self._reaper_sensor_poller
            device = await self._reaper_mcb_registry.get(request.module_id)
            assert device

            await device.board.set_relays(RelayState(pc=request.enabled))

            try:
                await self._reaper_sensor_poller.update_module(request.module_id, True)
            except BaseException:
                LOG.exception(f"Failed to update sensor data for module {request.module_id}")

            return hardware_manager_pb.SetReaperModulePcPowerResponse(success=True)
        except BaseException:
            LOG.exception(f"Exception setting module {request.module_id} PC power")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting module {request.module_id} PC power")
            return hardware_manager_pb.SetReaperModulePcPowerResponse(success=False)

    async def SetReaperModuleLaserPower(
        self, request: hardware_manager_pb.SetReaperModuleLaserPowerRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetReaperModuleLaserPowerResponse:
        if not is_reaper():
            context.set_code(grpc.StatusCode.UNIMPLEMENTED)
            return hardware_manager_pb.SetReaperModuleLaserPowerResponse(success=False)

        try:
            assert self._reaper_mcb_registry
            assert self._reaper_sensor_poller
            device = await self._reaper_mcb_registry.get(request.module_id)
            assert device

            await device.board.set_relays(RelayState(laser=request.enabled))

            try:
                await self._reaper_sensor_poller.update_module(request.module_id, True)
            except BaseException:
                LOG.exception(f"Failed to update sensor data for module {request.module_id}")

            return hardware_manager_pb.SetReaperModuleLaserPowerResponse(success=True)
        except BaseException:
            LOG.exception(f"Exception setting module {request.module_id} laser power")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting module {request.module_id} laser power")
            return hardware_manager_pb.SetReaperModuleLaserPowerResponse(success=False)

    async def SetReaperModuleStrobePower(
        self, request: hardware_manager_pb.SetReaperModuleStrobePowerRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.SetReaperModuleStrobePowerResponse:
        if not is_reaper():
            context.set_code(grpc.StatusCode.UNIMPLEMENTED)
            return hardware_manager_pb.SetReaperModuleStrobePowerResponse(success=False)

        try:
            assert self._reaper_mcb_registry
            assert self._reaper_sensor_poller
            device = await self._reaper_mcb_registry.get(request.module_id)
            assert device

            await device.board.set_relays(RelayState(btl=request.enabled))

            try:
                await self._reaper_sensor_poller.update_module(request.module_id, True)
            except BaseException:
                LOG.exception(f"Failed to update sensor data for module {request.module_id}")

            return hardware_manager_pb.SetReaperModuleStrobePowerResponse(success=True)
        except BaseException:
            LOG.exception(f"Exception setting module {request.module_id} strobe power")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception setting module {request.module_id} strobe power")
            return hardware_manager_pb.SetReaperModuleStrobePowerResponse(success=False)

    async def IdentifyModule(
        self, request: hardware_manager_pb.IdentifyModuleRequest, context: grpc.ServicerContext
    ) -> hardware_manager_pb.IdentifyModuleResponse:
        if not is_reaper():
            context.set_code(grpc.StatusCode.UNIMPLEMENTED)
            return hardware_manager_pb.IdentifyModuleResponse(success=False)

        try:
            assert self._reaper_mcb_registry
            await self._reaper_mcb_registry.identify_module_with_strobe(request.to_id_ip, list(request.turn_off_ips))
            return hardware_manager_pb.IdentifyModuleResponse(success=True)
        except BaseException:
            LOG.exception(f"Exception identifying module {request.to_id_ip}")
            context.set_code(grpc.StatusCode.UNKNOWN)
            context.set_details(f"Exception identifying module {request.to_id_ip}")
            return hardware_manager_pb.IdentifyModuleResponse(success=False)


class GrpcServer:
    def __init__(
        self,
        port: int,
        peplink_gps: Optional[PeplinkGPSReceiver],
        usb_device: USBChecker,
        cruise_controller: Optional[CruiseController],
        hardware_manager: HardwareManager,
        config_subscriber: ConfigSubscriber,
        reaper_mcb_registry: Optional[McbConnectorRegistry] = None,
        reaper_sensor_poller: Optional[ReaperModuleSensorPoller] = None,
        tractor: Optional[TractorIF] = None,
    ):
        self._port = port
        self._server = grpc.aio.server()
        self._servicer = GrpcServicer(
            peplink_gps,
            usb_device,
            cruise_controller,
            hardware_manager,
            config_subscriber,
            reaper_mcb_registry,
            reaper_sensor_poller,
            tractor,
        )
        self._loggingServicer = lib.common.logging.LoggingGRPCServicer(self._server, "hardware_manager.log")
        hardware_manager_grpc.add_HardwareManagerServiceServicer_to_server(self._servicer, self._server)
        self._server.add_insecure_port(f"[::]:{self._port}")

    async def start(self) -> None:
        await self._server.start()
        LOG.info(f"Started Hardware Manager at 0.0.0.0:{self._port}")

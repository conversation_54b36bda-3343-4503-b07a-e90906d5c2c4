import asyncio
from typing import Dict, Iterable, List, Optional, Type, TypeVar

from config.client.cpp.config_client_python import ConfigSubscriber
from hardware_manager.python.boards.board import HardwareManagedBoard
from lib.common.devices.device import Device, DeviceStatus, DeviceStatusCode
from lib.common.devices.device_configuration import get_devices_for_service, get_skip_list
from lib.common.devices.registry import DeviceRegistry
from lib.common.logging import get_logger

LOG = get_logger(__name__)


T = TypeVar("T", bound=HardwareManagedBoard)


class HardwareManager:
    def __init__(self, config_subscriber: ConfigSubscriber) -> None:
        self._registry = DeviceRegistry()
        self._config_subscriber = config_subscriber
        self._hw_managed_boards: Dict[str, HardwareManagedBoard] = {}

    @property
    def registry(self) -> DeviceRegistry:
        return self._registry

    async def device_list(self) -> List[Device]:
        return await self.registry.get_device_list()

    @property
    def hw_managed_boards(self) -> Iterable[HardwareManagedBoard]:
        return self._hw_managed_boards.values()

    def append_hw_managed_boards(self, board: HardwareManagedBoard) -> None:
        self._hw_managed_boards[board.board_name] = board

    def get_board_by_name(self, board_name: str, board_type: Type[T]) -> Optional[T]:
        board = self._hw_managed_boards.get(board_name, None)
        if board is not None and isinstance(board, board_type):
            return board
        return None

    async def boot(self, extra_skip_list: Optional[List[str]] = None) -> None:
        device_conf = self._config_subscriber.get_config_node("hardware_manager", "device_overrides")
        common_conf = self._config_subscriber.get_config_node("common", "")
        devices_for_service_iterable = get_devices_for_service("hardware_manager", device_conf, common_conf)

        skip_list = get_skip_list(device_conf)
        if extra_skip_list is not None:
            for esl in extra_skip_list:
                skip_list.add(esl)
        config_jimbox_installed = (
            self._config_subscriber.get_config_node("hardware_manager", "jimbox").get_node("installed").get_bool_value()
        )
        if not config_jimbox_installed:
            skip_list.add("jimbox_1")

        try:
            await self._registry.boot_devices_from_iterable(
                devices_for_service_iterable, skip_list=skip_list,
            )
        except Exception:
            LOG.exception("Hardware manager had issues booting")

    async def get_device_statuses(self) -> Dict[str, DeviceStatus]:
        return await self._registry.get_status()

    async def loop_enabling(self) -> None:
        while True:
            await asyncio.sleep(10)
            for board in self.hw_managed_boards:
                try:
                    status = await board.device_status()
                    if status not in [DeviceStatusCode.OK, DeviceStatusCode.BOOTING] or not board.ok:
                        if status != DeviceStatusCode.NEW:
                            await board.disable_device()
                        await board.enable_device()
                except Exception:
                    LOG.exception(f"Error trying to enable board {board.board_name}")
